if ENV['AWS_REGION'].present?
  Aws.config.update({
                      region: ENV.fetch('AWS_REGION', nil),
                      credentials: Aws::Credentials.new(ENV.fetch('AWS_ACCESS_KEY_ID', nil), ENV.fetch('AWS_SECRET_ACCESS_KEY', nil))
                    })

  AWS_TRANSCRIBE_CLIENT = Aws::TranscribeService::Client.new
  AWS_COMPREHEND_CLIENT = Aws::Comprehend::Client.new

  recoknition_credentials = Aws::Credentials.new(ENV.fetch('AWS_REKOGNITION_ACCESS_KEY_ID', nil), ENV.fetch('AWS_REKOGNITION_SECRET_ACCESS_KEY', nil))
  AWS_REKOGNITION_CLIENT = Aws::Rekognition::Client.new credentials: recoknition_credentials

  # This new credential is used for OpenAI audio transcription only.
  # We'll use this bucket later to save and manage all audio recordings.
  AWS_RECORDING_CLIENT = Aws::S3::Client.new({
                                               region: ENV.fetch('AWS_REGION', nil),
                                               credentials: Aws::Credentials.new(
                                                 ENV.fetch('AWS_RECORDING_ACCESS_KEY_ID', nil),
                                                 ENV.fetch('AWS_RECORDING_SECRET_ACCESS_KEY', nil)
                                               )
                                             })

  AWS_COMPREHEND_LANGUAGES = {
    'ms' => ['id', 'ms'],
    'id' => ['ms', 'id'],
    'pt' => ['pt', 'ptbr'],
    'zh' => ['zhcn'],
    'zh-TW' => ['zhyue']
  }

  AWS_TRANSCRIPTION_LANG = {
    ar: { aws_language_code: 'ar-AE' },
    da: { aws_language_code: 'da-DK' },
    de: { aws_language_code: 'de-DE' },
    en: { aws_language_code: 'en-US' },
    es: { aws_language_code: 'es-ES', permissive_error: ['pt'] },
    fr: { aws_language_code: 'fr-FR' },
    he: { aws_language_code: 'he-IL' },
    hi: { aws_language_code: 'hi-IN' },
    it: { aws_language_code: 'it-IT' },
    ja: { aws_language_code: 'ja-JP' },
    ko: { aws_language_code: 'ko-KR' },
    ms: { aws_language_code: 'ms-MY' },
    nl: { aws_language_code: 'nl-NL' },
    ptbr: { aws_language_code: 'pt-BR', permissive_error: ['es'] },
    pt: { aws_language_code: 'pt-PT', permissive_error: ['es'] },
    ru: { aws_language_code: 'ru-RU' },
    tr: { aws_language_code: 'tr-TR' },
    zhcn: { aws_language_code: 'zh-CN' },
    th: { aws_language_code: 'th-TH' },
    sq: { aws_language_code: 'sq-SQ' }
  }

  identity_credentials = Aws::Credentials.new(ENV.fetch('AWS_IDENTITY_ACCESS_KEY_ID', nil), ENV.fetch('AWS_IDENTITY_SECRET_ACCESS_KEY', nil))
  Rails.application.config.s3 = {
    bucket: Aws::S3::Resource.new.bucket(ENV.fetch('S3_BUCKET_NAME', nil)),
    identity_bucket: Aws::S3::Resource.new(credentials: identity_credentials).bucket(ENV.fetch('AWS_IDENTITY_BUCKET_NAME', nil))
  }
end
