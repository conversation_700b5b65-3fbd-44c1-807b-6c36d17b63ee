# ----------------------------------------
# :section: Languages and Tests
# ----------------------------------------
# LanguageList gem has a COMMON Language list but it is too long (71 languages). So we create a short one
# Not used anymore
SHORT_LANGUAGES_LIST = [LanguageList::LanguageInfo.find('English'),
                        LanguageList::LanguageInfo.find('French'),
                        LanguageList::LanguageInfo.find('German'),
                        LanguageList::LanguageInfo.find('Dutch'),
                        LanguageList::LanguageInfo.find('Spanish'),
                        LanguageList::LanguageInfo.find('Italian'),
                        LanguageList::LanguageInfo.find('German'),
                        LanguageList::LanguageInfo.find('Japanese'),
                        LanguageList::LanguageInfo.find('Arabic'),
                        LanguageList::LanguageInfo.find('Chinese')].freeze

# This list is absolutely not official, we allow the possibility to the user to add a specific one
# Not used anymore
LANGUAGE_TESTS_LIST = [
  { name: 'TOEIC', info: 'A score between 0 and 990' },
  { name: 'TOEFL', info: 'A score between 0 and 120' },
  { name: 'CECRL', info: 'A grade between A1 and C2' }
].freeze

LANGUAGE_SKILLS = %w[Listening Reading Writing Speaking].freeze

# ----------------------------------------
# :section: Scoring config
# ----------------------------------------
# Default Score
SCORE_DEFAULT_RATING = 1200
SCORE_DEFAULT_RATING_DEVIATION = 350
SCORE_DEFAULT_VOLATILITY = 0.06

# Scoring strategy
SCORING_BATCH_SIZE = 10

# Variable used to transform a Pipplet score into a percentage.
MAX_SCORE_LIMIT = 1800

CECRL_LEVELS_RANGE_MIN = 0
CECRL_LEVELS_RANGE_MAX = 100
CECRL_LEVELS_RANGE = { 'A1-' => 0..6, 'A1' => 7..11, 'A1+' => 12..17, 'A2-' => 18..22, 'A2' => 23..28,
                       'A2+' => 29..33, 'B1-' => 34..39, 'B1' => 40..44, 'B1+' => 45..50, 'B2-' => 51..56,
                       'B2' => 57..61, 'B2+' => 62..67, 'C1-' => 68..72, 'C1' => 73..78, 'C1+' => 79..83,
                       'C2-' => 84..89, 'C2' => 90..94, 'C2+' => 95..100 }.freeze

# To keep the old behaviour, this will/must be removed.
CECRL_LEVELS_VALUES = {}.tap do |data|
  data[:cecrl], data[:threshold] = CECRL_LEVELS_RANGE.map { |level, range| [level, range.min] }.transpose
end.freeze

JSON_ENTRY_LABELS = {
  entry: [
    %w[entry.122374946 entry.1581966851 entry.888723862 entry.1912596366 entry.1223975733 entry.1786950336 entry.1222525773 entry.1554372908 entry.128448655],
    %w[entry.1203467641 entry.781207062 entry.1518212951 entry.836554058 entry.1729087157 entry.143868909 entry.1622426097 entry.158142824 entry.1982074748],
    %w[entry.888723862 entry.1912596366 entry.1786950336 entry.1222525773 entry.781207062 entry.1518212951 entry.836554058 entry.143868909 entry.1622426097],
    %w[entry.122374946 entry.1581966851 entry.1223975733],
    %w[entry.128448655 entry.1518212951],
    %w[entry.888723862 entry.1786950336 entry.1203467641 entry.143868909],
    %w[entry.1222525773 entry.836554058 entry.1622426097],
    %w[entry.1554372908 entry.1729087157 entry.158142824],
    %w[entry.122374946 entry.1581966851 entry.888723862 entry.1912596366 entry.1223975733 entry.1786950336 entry.1222525773 entry.1554372908 entry.128448655 entry.1203467641 entry.781207062 entry.1518212951 entry.836554058 entry.1729087157 entry.143868909 entry.1622426097 entry.158142824 entry.1982074748]
  ],
  label: %w[
    spoken written academic
    pronunciation spokenFluency grammar
    vocabulary coherence overall
  ]
}.freeze

TOEIC_EQ = {
  score: [
    5.555556, 6.349206, 7.142857, 7.936508, 8.730159, 9.523810, 10.317460, 11.111111,
    11.904762, 12.698413, 13.492063, 14.285714, 15.079365, 15.873016, 16.666667, 17.46,
    18.25, 19.05, 19.84, 20.63, 21.43, 22.222222, 22.49, 22.75,
    23.02, 23.28, 23.54, 23.81, 24.07, 24.34, 24.60, 24.87,
    25.13, 25.40, 25.66, 25.93, 26.19, 26.46, 26.72, 26.98,
    27.25, 27.51, 27.777778, 28.03, 28.28, 28.54, 28.79, 29.04,
    29.29, 29.55, 29.80, 30.05, 30.30, 30.56, 30.81, 31.06,
    31.31, 31.57, 31.82, 32.07, 32.32, 32.58, 32.83, 33.08,
    33.333333, 33.59, 33.84, 34.09, 34.34, 34.60, 34.85, 35.10,
    35.35, 35.61, 35.86, 36.11, 36.36, 36.62, 36.87, 37.12,
    37.37, 37.63, 37.88, 38.13, 38.38, 38.64, 38.888889, 39.26,
    39.63, 40.00, 40.37, 40.74, 41.11, 41.48, 41.85, 42.22,
    42.59, 42.96, 43.33, 43.70, 44.07, 44.444444, 44.79, 45.14,
    45.49, 45.83, 46.18, 46.53, 46.88, 47.22, 47.57, 47.92,
    48.26, 48.61, 48.96, 49.31, 49.65, 50.000000, 50.35, 50.69,
    51.04, 51.39, 51.74, 52.08, 52.43, 52.78, 53.13, 53.47,
    53.82, 54.17, 54.51, 54.86, 55.21, 55.555556, 56.11, 56.67,
    57.22, 57.78, 58.33, 58.89, 59.44, 60.00, 60.56, 61.111111,
    61.62, 62.12, 62.63, 63.13, 63.64, 64.14, 64.65, 65.15,
    65.66, 66.16, 66.666667, 67.17, 67.68, 68.18, 68.69, 69.19,
    69.70, 70.20, 70.71, 71.21, 71.72, 72.222222, 72.84, 73.46,
    74.07, 74.69, 75.31, 75.93, 76.54, 77.16, 77.78, 100.00
  ],
  eq: [
    120, 125, 130, 135, 140, 145, 150, 155, 160, 165, 170, 175,
    180, 185, 190, 195, 200, 205, 210, 215, 220, 225, 230, 235,
    240, 245, 250, 255, 260, 265, 270, 275, 280, 285, 290, 295,
    300, 305, 310, 315, 320, 325, 330, 335, 340, 345, 350, 355,
    360, 365, 370, 375, 380, 385, 390, 395, 400, 405, 410, 415,
    420, 425, 430, 435, 440, 445, 450, 455, 460, 465, 470, 475,
    480, 485, 490, 495, 500, 505, 510, 515, 520, 525, 530, 535,
    540, 545, 550, 555, 560, 565, 570, 575, 580, 585, 590, 595,
    600, 605, 610, 615, 620, 625, 630, 635, 640, 645, 650, 655,
    660, 665, 670, 675, 680, 685, 690, 695, 700, 705, 710, 715,
    720, 725, 730, 735, 740, 745, 750, 755, 760, 765, 770, 775,
    780, 785, 790, 795, 800, 805, 810, 815, 820, 825, 830, 835,
    840, 845, 850, 855, 860, 865, 870, 875, 880, 885, 890, 895,
    900, 905, 910, 915, 920, 925, 930, 935, 940, 945, 950, 955,
    960, 965, 970, 975, 980, 985, 990, 990
  ]
}.freeze

# Mapping until 27/02/2019
# CECRL_LEVELS_VALUES = {
#  :cecrl     => [
#    'A1-','A1','A1+',
#    'A2-','A2','A2+',
#    'B1-','B1','B1+',
#    'B2-','B2','B2+',
#    'C1-','C1','C1+',
#    'C2-','C2','C2+'],
#  :threshold => [
#    0,5.56,11.11,
#    16.67,22.22,27.78,
#    33.33,38.89,44.44,
#    50.00,55.56,61.11,
#    66.67,72.22,77.78,
#    83.33,88.89,94.44
#  ]
# }

# Mapping until end of 2018
# CECRL_LEVELS_VALUES = {
#     :cecrl =>       ["A1", "A2", "B1", "B2", "C1", "C2"],
#     :down_limit =>  [0,   500, 800, 1100, 1400, 1700],
#     :average =>     [250, 650, 950, 1250, 1700, 2200],
#     :deviation =>   [100, 200,  350,  350,  350,  350],
#     :human_down_limit => [0, 16.67, 33.33, 50.00, 66.67, 83.33]
# }

# Nominal mapping updated by Matt: max fixed as 1800 so nobody is C2 which is normal (test max score is C1 as we are not legitimate to assess above that)
# CECRL_LEVELS_VALUES = {
#    :cecrl =>       ["A1", "A2", "B1", "B2", "C1", "C2"],
#    :down_limit =>  [0,   400, 800, 1200, 1600, 2000],
#    :average =>     [200, 600, 1000, 1400, 1800, 2200],
#    :deviation =>   [100, 200,  350,  350,  350,  350],
# }

# Mapping of Pipplet rating vs CECRL levels (OLD)
# CECRL_LEVELS_VALUES = {
#    :cecrl =>       ["A1", "A2", "B1", "B2", "C1", "C2"],
#   :down_limit =>  [0,   600, 1000, 1500, 2000, 2500],
#    :average =>     [300, 800, 1250, 1750, 2250, 2750],
#    :deviation =>   [100, 200,  350,  350,  350,  350],
# }

# ----------------------------------------
# :section: Question Import
# ----------------------------------------
IMPORT_QUESTIONS_IMAGES_PATH = '/Users/<USER>/pippletV2/images/'.freeze
IMPORT_QUESTIONS_IMAGES_URL = 'https://s3-eu-west-1.amazonaws.com/pippletdotcom/questions_images'.freeze

# ----------------------------------------
# :section: Email
# ----------------------------------------
# How many hours do we wait between each refresh of the remote email file.
EMAIL_CACHE_LIFETIME = 2
EMAIL_CACHE_KEY = 'email/last_update'.freeze
EMAIL_REMOTE_FILE = 'https://s3-eu-west-1.amazonaws.com/pipplet-production-irland/authorised_emails.csv'.freeze unless Object.const_defined?(:EMAIL_REMOTE_FILE)

# ----------------------------------------
# :section: Google Drive
# ----------------------------------------
if Rails.env.production?
  # https://docs.google.com/spreadsheets/d/13XP5a1BOAj6Flzgbg1UkCqm7nIR-CdX00BbaMLHfmGg/edit#gid=1948416432
  GOOGLE_DRIVE_HOOK_URL = 'https://script.google.com/a/macros/pipplet.com/s/AKfycbzVZHuN2Oou9xuiPsfkRL_SwgKofK-XOK-d0Am98KF2IiWN4Q/exec'.freeze
  GOOGLE_ASSESSMENT_FOR_URL = '1FAIpQLSfIV4uVQFNpKCmcpNz5QMweNi5mKnH7D_OY-uwURBZmuH0cjw'.freeze
  GOOGLE_TECHNICAL_ISSUE_URL = 'https://script.google.com/macros/s/AKfycbzepvnvGSOpYeDSwUktDdvcokWR_Y5PUBvKy4PDxwARxozdU3dWIyaXyiwMhxz7p7VNuw/exec'.freeze
  GOOGLE_SHEET_EXAMINERS_FOR_URL = '160yuAUasrM-Vg5auchw9h7uTQ1EK7MXcvOxT5uRDJ-A'.freeze
else
  GOOGLE_DRIVE_HOOK_URL = ''.freeze
  GOOGLE_ASSESSMENT_FOR_URL = '1FAIpQLSfVcpkM-4pyg9-0LVb7ffX-5q8u9RyByRN3ByZ4RzM7I9P0Jw'.freeze
  GOOGLE_TECHNICAL_ISSUE_URL = 'https://script.google.com/macros/s/AKfycbwmWWvLc4AVOn_COQZOVlpd3CY27ow5_4ry9hNf9LdNv02Kpx14u4X8VVGymQ8OmTrp/exec'.freeze
  GOOGLE_SHEET_EXAMINERS_FOR_URL = '1o6N7nEH4BgsHJx2qk99hyQU4N4RXZTeS6jSZq_qXKgI'.freeze
end

# ----------------------------------------
# :section: Demo environment / Special config for showcase app
# ----------------------------------------
# How many hours do we wait to reset the database.
DEMO_RESET_LIFETIME = 24
DEMO_CACHE_KEY = 'demo/refresh_timestamp'.freeze

# Deprecated: Boolean, if you want to turn the dev environment like demo.
# DEV_BEHAVES_LIKE_DEMO = false

# ----------------------------------------
# :section: API
# ----------------------------------------
# How many hours is the authentication_token valid?
API_AUTHENTIFICATION_TOKEN_LIFETIME = 24
# Default value for pagination
API_DEFAULT_OBJECTS_PER_PAGE = 100

SOURCE_CLIENT_TYPES = {
  pipplet_clients_account: 'PippletClientsAccount',
  greenhouse: 'Greenhouse',
  language_test_api: 'LanguageTestAPI',
  smartrecruiters: 'SmartRecruiters',
  talentsoft: 'Talentsoft',
  teamtailor: 'Teamtailor',
  lever: 'Lever',
  workable: 'Workable',
  jobylon: 'Jobylon',
  jobvite: 'Jobvite',
  idp: 'IdP'
}.freeze

# ----------------------------------------
# :section: Pipplet Clients - DEFAULT
# ----------------------------------------
APIPippletClients = Struct.new(:name, :url, :token)
prod = { name: :production, url: 'https://dashboard.pipplet.com', token: '2663dc995f94156b45ee2e58068c0414' }
preprod = { name: :preprod, url: ENV.fetch('PCLIENT_PREPROD_URL', 'https://pipplet-clients-preprod.herokuapp.com'), token: '2663dc995f94156b45ee2e58068c0414' }
staging = { name: :staging, url: 'https://pipplet-clients-staging.herokuapp.com', token: '2663dc995f94156b45ee2e58068c0414' }
dev = { name: :development, url: 'http://pipplet-clients_rails_client_1:4000', token: '2663dc995f94156b45ee2e58068c0414' }
test = { name: :test, url: 'http://sample_testing_url', token: 'sample_token' }

PIPPLET_CLIENTS = [
  APIPippletClients.new(*prod.values_at(*APIPippletClients.members)),
  APIPippletClients.new(*preprod.values_at(*APIPippletClients.members)),
  APIPippletClients.new(*staging.values_at(*APIPippletClients.members)),
  APIPippletClients.new(*dev.values_at(*APIPippletClients.members)),
  APIPippletClients.new(*test.values_at(*APIPippletClients.members))
].freeze

# ----------------------------------------
# :section: General config
# ----------------------------------------
# First language in the list is default!
# If you add a new language please do a migration for the user group. Add a new examiner key for the new language.
AVAILABLE_TEST_LANGUAGES = %i[en fr de es] + %i[it nl ru ar pt ptbr el sv zhcn ja ko th vi tr he pl ms hi da no lb ro fi cs eu hu uk hr sr sl sq bg sk et lv lt zhyue].sort
# Identify languages that need to be displayed from left to right
RIGHT_TO_LEFT_LANGUAGES = %i[ar he].freeze

LINGUIST_IDS_FOR_DEFAULT_AUDITION_TEST = %w[AUD-05 AUD-05a AUD-06 AUD-07 AUD-08 AUD-09 AUD-10].freeze

DELAY_IN_DAYS_BETWEEN_EACH_REMINDER = 2
MAX_NUMBER_OF_REMINDERS_PER_TEST_INSTANCE = 8
NO_REMINDERS_CLIENTS = ['api:2068-dgac---dsac', 'api:729-demo-account'].freeze
CLIENTS_IN_URGENT_MODE = [].freeze
CLIENTS_WITH_EXPIRABLE_TESTINSTANCES = [{ client_name: 'Lavazza', cancel_after_days: 5 }].freeze

MAGIC_RATIO_TO_GUESS_RECORDING_DURATION = 6600

USER_GROUP_LEVEL_ALERTING_THRESHOLD = 10

DEFAULT_CERTIFICATE_SETTINGS = {
  'orientation' => 'portrait',
  'custom' => 'portrait'
}.freeze

SYNTHESIS_GRADES = %w[spoken written academic].freeze
DETAILED_GRADES = %w[pronunciation spokenFluency grammar vocabulary coherence].freeze

CLIENTS_INCREASE_LEVELS = {
  'AFCI' => '1',
  'OKYAKUSAMA FORMATION' => '1'
}.freeze

AI_THRESHOLD_GRADE_DIFF = 3

TEST_PROFILE_FOR_FORMATION = ['formation-positionnement', 'Formation-3-sets', 'Flex', 'formation_positionnement_18min', 'Low_level', 'Low_level Simple', 'Low_level_INTL Detailed', 'Low_level_INTL Simple', 'IDF_Low level Simple', 'IDF_Low level Detailed', 'Air_France_Jp'].freeze

GTM_ID = 'GTM-MZJGSMB'.freeze

INVOICE_AMOUNT_COMPARISON_PERCENTAGE_DIFFERENCE = 2

# AI Config
OPEN_AI_API_TOKEN = ENV.fetch('OPEN_AI_API_TOKEN', nil)

AI_EXAMINER_VERSION = ENV.fetch('AI_EXAMINER_VERSION', '1')

API_ATS_NAMES = %w[SmartRecruiterAPI TeamtailorAPI GreenhouseAPI].freeze

TALENT_AI_BINARY_B2_SCORE_CUT = 51

API_AVAILABLE_PROFILES_ID = {
  'default' => -1,
  'flex' => 84,
  'screening' => 74,
  'placement' => 79,
  'talent' => 28,
  'placement-breakthrough' => 139,
  'placement-breakthrough-non-latin' => 142
}.freeze

API_AVAILABLE_PROFILES_ID_V2 = {
  'default' => -1,
  'talent' => 74,
  'talent+' => 28,
  'flex' => 79,
  'flex+' => 72,
  'talent_ai' => 248,
  'placement-breakthrough' => 139,
  'placement-breakthrough-non-latin' => 142
}.freeze

KEYBOARD_CHECK_LANGUAGE = {
  'ja' => 'こんにちは',
  'zhcn' => '谢谢',
  'ar' => 'السلام عليكم',
  'TH' => 'สวัสดี',
  'el' => 'Γεια',
  'he' => 'שלום',
  'hi' => 'नमस्कार',
  'ko' => '안녕하세요',
  'uk' => 'Привіт',
  'ru' => 'Привет',
  'bg' => 'Здрасти',
  'zhyue' => '多謝'
}.freeze

DEFAULT_TOTAL_AUDIO_DURATION = 20

PCLIENT_TEST_DEMO_SLUGS = {
  practice: 191,
  practiceflex: 192,
  standard_recruitment: 191,
  training: 192,
  customer_service_written: 194,
  customer_service: 206,
  audit_consulting: 82
}.freeze

RANGE_FROM_SCORE = {
  'A1-' => %w[A1- A1+],
  'A1' => %w[A1- A1+],
  'A1+' => %w[A1 A2-],
  'A2-' => %w[A1+ A2],
  'A2' => %w[A2- A2+],
  'A2+' => %w[A2 B1-],
  'B1-' => %w[A2+ B1],
  'B1' => %w[B1- B1+],
  'B1+' => %w[B1 B2-],
  'B2-' => %w[B1+ B2],
  'B2' => %w[B2- B2+],
  'B2+' => %w[B2 C1-],
  'C1-' => %w[B2+ C2+],
  'C1' => %w[C1- C2+],
  'C1+' => %w[C1 C2+],
  'C2-' => %w[C1+ C2+],
  'C2' => %w[C2- C2+],
  'C2+' => %w[C2 C2+]
}.freeze

EXAMINER_FEES_VALUES = {
  'simple' => 3.0,
  'detailed' => 10.0,
  'written' => 3.0,
  'spoken' => 3.0
}.freeze

EXAMINER_FEES_DEFAULT_CURRENCY = 'EUR'.freeze

# Bypass check_caracters_match_with_language_validation see https://pipplet-squad.monday.com/boards/**********/views/********/pulses/**********
DISABLE_CHECK_CHARACTERS = {
  zhcn: [97, 139]
}.freeze

TRANSFERWISE_API_URL = 'https://api.transferwise.com'.freeze
TRANSFERWISE_API_SANDBOX_URL = 'https://api.sandbox.transferwise.tech'.freeze

# rubocop:disable Lint/UnusedBlockArgument
WISE_CONFIG = {
  'GBP' => {
    type: 'sort_code',
    details: ->(account_number:, sort_code:, branch_code:) { { accountNumber: account_number, sortCode: sort_code } }
  },
  'HKD' => {
    type: 'hongkong',
    details: ->(account_number:, sort_code:, branch_code:) { { accountNumber: "#{branch_code}#{account_number}", bankCode: sort_code } }
  },
  'USD' => {
    type: 'aba',
    details: ->(account_number:, sort_code:, branch_code:) { { accountNumber: account_number, abartn: sort_code } }
  },
  'PHP' => {
    type: 'philippines',
    details: ->(account_number:, sort_code:, branch_code:) { { accountNumber: account_number } }
  },
  'CAD' => {
    type: 'canadian',
    details: ->(account_number:, sort_code:, branch_code:) { { accountNumber: account_number, institutionNumber: sort_code, transitNumber: branch_code } }
  },
  'INR' => {
    type: 'indian',
    details: ->(account_number:, sort_code:, branch_code:) { { accountNumber: account_number, ifscCode: sort_code } }
  },
  'DEFAULT' => {
    type: 'iban',
    details: ->(account_number:, sort_code:, branch_code:) { { iban: account_number } }
  }
}
# rubocop:enable Lint/UnusedBlockArgument

DESCRIBE_IMAGE_CHALLENGES = [101, 107, 113, 119, 130, 131, 132, 136, 137, 138, 145, 146, 147, 151, 152, 172, 175, 190, 204, 207, 210, 213, 216, 219, 222, 225, 229, 231, 232, 242, 243, 246, 247, 250, 251, 263, 266, 270, 273, 277, 280, 284, 285, 287, 288, 290, 291, 293, 296, 299, 302, 305, 308, 314, 317, 321, 324, 328, 331, 346, 349, 353, 356, 360, 363, 366, 368, 370, 397, 436, 439, 442, 446, 449, 452, 456, 460, 464, 500, 504, 513, 514, 515, 518, 521, 524, 526, 528, 531, 82, 95].freeze
