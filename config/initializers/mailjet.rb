# kindly generated by appropriated Rails generator
Mailjet.configure do |config|
  config.api_key = '394c184ead2304597d7bcd47f3a8111d'
  config.secret_key = '53e0fa70ffe15b550b55f0f0608217e6'
  config.default_from = '<EMAIL>'
  # Mailjet API v3.1 is at the moment limited to Send API.
  # We’ve not set the version to it directly since there is no other endpoint in that version.
  # We recommend you create a dedicated instance of the wrapper set with it to send your emails.
  # If you're only using the gem to send emails, then you can safely set it to this version.
  # Otherwise, you can remove the dedicated line into config/initializers/mailjet.rb.
  config.api_version = 'v3.1'
end

Rails.application.config.mailjet = {
  demo_certificate_file_names: %w[simple_report_sample_certificate detailed_sample_certificate written_sample_certificate ai_sample_certificate],
  demo_certificate_file_names_by_service: {
    'talent_ai' => 'ai_sample_certificate',
    'talent' => 'simple_report_sample_certificate',
    'talent+' => 'detailed_sample_certificate'
  },
  demo_certificate_file_names_by_assessment_type: {
    'simple' => 'simple_report_sample_certificate',
    'detailed' => 'detailed_sample_certificate',
    'written' => 'written_sample_certificate'
  },
  demo_template_ids: {
    talent_ai: { 'en' => 4_702_768,
                 'fr' => 4_702_770 },
    default: { 'en' => 463_577,
               'fr' => 463_603 }
  },
  redo_template_ids: {
    'fr' => 4_784_062,
    'en' => 4_784_139
  }
}
