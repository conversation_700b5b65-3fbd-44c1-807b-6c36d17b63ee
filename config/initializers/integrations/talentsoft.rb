Rails.application.config.talentsoft = {
  catalog: {
    'ptbr' => 'Brazilian Portuguese',
    'da' => 'Danish',
    'nl' => 'Dutch',
    'en' => 'English',
    'fr' => 'French',
    'de' => 'German',
    'he' => 'Hebrew',
    'hi' => 'Hindi',
    'it' => 'Italian',
    'ja' => 'Japanese',
    'ko' => 'Korean',
    'lb' => 'Luxembourgish',
    'zhcn' => 'Mandarin Chinese (simplified)',
    'el' => 'Modern Greek',
    'ar' => 'Modern Standard Arabic',
    'no' => 'Norwegian',
    'pl' => 'Polish',
    'pt' => 'Portuguese',
    'ru' => 'Russian',
    'es' => 'Spanish',
    'sv' => 'Swedish',
    'th' => 'Thai',
    'tr' => 'Turkish',
    'vi' => 'Vietnamese'
  },
  test_profile_ids: {
    default: -1,
    talent: ENV.fetch('TALENTSOFT_TALENT_TP_ID', '74').to_i,
    screening: ENV.fetch('TALENTSOFT_SCREENING_TP_ID', '74').to_i,
    talent_plus: ENV.fetch('TALENTSOFT_SCREENING_TP_ID', '28').to_i,
    talent_ai: ENV.fetch('TALENTSOFT_TALENT_AI_TP_ID', '200').to_i,
  }.with_indifferent_access
}.freeze
