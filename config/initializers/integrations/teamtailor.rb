# TODO : rely only on env variables
Rails.application.config.teamtailor = {
  api_key: ENV['TEAMTAILOR_API_KEY'] || 'I_eIjGv31DshlfsLCBPxmbwJZ0VT53yEM9NME9QB',
  partner_id: ENV['TEAMTAILOR_PARTNER_ID'] || 549,
  base_url: ENV['TEAMTAILOR_BASE_URL'] || 'https://api.teamtailor.com/partner/v1',
  api_version: ENV['TEAMTAILOR_API_VERSION'] || 20_180_828,
  pipplet_sales_url: ENV['PIPPLET_SALES_URL'] || 'https://pipplet.com',
  test_profile_ids: [
    74,
    145,
    143,
    28,
    65,
    188
  ],
  catalog: {
    'ptbr' => 'Brazilian Portuguese',
    'da' => 'Danish',
    'nl' => 'Dutch',
    'en' => 'English',
    'fr' => 'French',
    'de' => 'German',
    'he' => 'Hebrew',
    'hi' => 'Hindi',
    'it' => 'Italian',
    'ja' => 'Japanese',
    'ko' => 'Korean',
    'lb' => 'Luxembourgish',
    'zhcn' => 'Mandarin Chinese (simplified)',
    'el' => 'Modern Greek',
    'ar' => 'Modern Standard Arabic',
    'no' => 'Norwegian',
    'pl' => 'Polish',
    'pt' => 'Portuguese',
    'ru' => 'Russian',
    'es' => 'Spanish',
    'sv' => 'Swedish',
    'th' => 'Thai',
    'tr' => 'Turkish',
    'vi' => 'Vietnamese'
  }
}
