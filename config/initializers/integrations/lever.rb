Rails.application.config.lever = {
  base_url: ENV['LEVER_BASE_URL'] || (Rails.env.production? ? 'https://api.lever.co/v1' : 'https://api.sandbox.lever.co/v1'),
  auth_url: ENV['LEVER_AUTH_URL'] || (Rails.env.production? ? 'https://auth.lever.co' : 'https://sandbox-lever.auth0.com'),
  client_id: ENV['LEVER_CLIENT_ID'] || 'sdvn0SwCGlnguA9qS9rCZh23S25AASMB',
  client_secret: ENV['LEVER_CLIENT_SECRET'] || 'vkteV4tRa4t3n4MVICkaElndO0i3H3wq9B2wiwsswdToYSH6dFTKFmE_yR4Z6MgM',
  archive_reason_text: ENV['LEVER_ARCHIVE_REASON_TEXT'] || 'Withdrew',
  webhook: {
    trigger_events: ['candidateStageChange'],
    trigger_stages: []
  },
  test_profile_ids: [
    74,
    145,
    143,
    28,
    65,
    188
  ],
  catalog: {
    'en' => 'English',
    'fr' => 'French',
    'es' => 'Spanish',
    'de' => 'German'
  }
}
