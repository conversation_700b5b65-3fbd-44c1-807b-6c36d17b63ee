files:
  defaults.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=0&single=true&output=csv"
  pages.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=1837177108&single=true&output=csv"
  models_and_validations: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=1667612324&single=true&output=csv"
  languages: "https://docs.google.com/spreadsheets/d/13xGzgFPis686YoV-6nG8zgqYb0S7n_WRITLMBsbIlRo/pub?gid=0&single=true&output=csv"
  devise: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=286622460&single=true&output=csv"
  branded_impactup.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=711831654&single=true&output=csv"
  branded_pippletdemo.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=492116155&single=true&output=csv"
  branded_pippletdemocandidates.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=603112536&single=true&output=csv"
  branded_flex.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=1212894127&single=true&output=csv"
  branded_renault.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=699362261&single=true&output=csv"
  branded_certificate.yml: "https://docs.google.com/spreadsheets/d/e/2PACX-1vSOo8R8I0uLQ-UUbcIIKMkybnd_2LRSdkDEJspMjBM-0zXAcLY5jtnM4UKgv7TK4XjZ9NsIz3pvrPoi/pub?gid=128275671&single=true&output=csv"
  branded_alpha_lto.yml: "https://docs.google.com/spreadsheets/d/1MeoTciVM76l_clrQ6czLunkEtC-ihjk84EVX0PgzxB8/pub?gid=3474665&single=true&output=csv"
  mailjet.yml: "https://docs.google.com/spreadsheets/d/e/2PACX-1vQVtLRTnUKkJtucclLp9RrvzYViPms_w0D-qXifsTMkVH_UDRHus_j0ifRyLPhjYQecLPXtxrOOHDXU/pub?gid=0&single=true&output=csv"
