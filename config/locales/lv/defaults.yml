---
lv:
  general:
    close: Close
    leaving_test:
      title: Careful!
      body: You are in the middle of the test, are you sure you want to leave? The
        question displayed will be considered incomplete.
      button_stay: Go back and finish the test
      button_leave: Leave the test
    unfocus_test_title: Stay with us!
    unfocus_test_1: You are not allowed to switch windows during the test. Are you
      sure you want to leave this page?
    unfocus_test_2: Plagiarism will be detected and reported to the organization that
      asked you to take the test.
    answer_questions: Answer the questions
    get_started: Get started
    continue: Continue
    no_javascript: Your browser doesn't support JavaScript, or it has been disabled.
      Please use a JavaScript enabled browser to take the test.
    'yes': 'Yes'
    'no': 'No'
    to: to
  cookie_consent:
    message: This website uses cookies to offer the best user experience. We do not
      store any personal data.
    dismiss: I agree
    learn_more: More info
  footer:
    termsofuse: Terms & Conditions
    contact: Contact
    legal: Legal
  home:
    title: Welcome to Pipplet!
    intro: 'Pipplet is an online platform that provides language testing and evaluation.
      Our test will record your answers through questions and role-play scenarios.

      '
    intro_written: Pipplet is an online platform that provides language testing and
      evaluation. Our test will record your written answers through questions and
      role-play scenarios.
    schools: Pipplet is currently available to students of Sorbonne-Universités, Paris-6,
      ISIT and UTC. Please do not hesitate to contact your foreign languages professors
      to get more information about the validity of the test in your university.
    beta: 'The test is still in its first version: please do not hesitate to give
      us feedback.'
    enjoy: You should have received an invitation to take the test. Look at your emails
      or contact <NAME_EMAIL>.
    contact_1: 'If you have any questions or concerns, feel free to contact us at '
    contact_2: <EMAIL>.
  waiting_confirmation:
    waiting_confirmation_title: We have sent you an email!
    waiting_confirmation_content: In order to complete your registration and start
      using our service, please click the link in the email we sent you.
  menu:
    my_account: My account
    continue: Continue
    start_answering: Answer questions
    signin: Sign In
    signup: Register
    signout: Sign out
  welcome:
    title: Welcome to Pipplet!
    intro: Pipplet assesses people’s ability to understand and be understood by others.
    concept: While taking the test, you will have to talk to and listen to other test
      takers.
    concept2: Your ability to be understood by them, and in return to understand their
      recordings, is what will determine your results of the Pipplet test.
    contact: Please feel free to contact us should you have any remark or question.
    beforetest: Before you take the test
    BT1: Make sure you are in a quiet place, where you can speak and listen without
      interruption.
    BT2: The test must be taken in one attempt; please set aside 45 minutes in which
      you will not be disturbed.
    BT3: If possible deactivate the notifications on your computer (email, etc.).
    BT4: Take it easy! Breaks are possible during the test to relax and take a coffee.
      Just exit at the end of a question using the [ Save and Pause ] button. You
      will be able to resume the test later.
    first_time: As this is your first time answering questions, we would like to ask
      you some information. This will help us improve the test.
    new_questions: You have answered all available questions. Please come back later
      to answer more.
    demo_info: Also, please do not hesitate to contact us if you have any questions
      or remarks about the test.
  language_information:
    title: It's your first time here, tell us a bit about yourself!
    other_tests: Have you ever taken an English proficiency test?
    other_tests_results: Please give us the results you got.
    mother_tongue: What is your mother tongue? You can select more than one.
    language_tests_intro: As this is your first time answering questions, we would
      like to ask you some information to help us improve our service.
    test_label: 'English test taken:'
    test_placeholder: Test name
    score_label: 'Score obtained:'
    score_placeholder: Score result
    level_estimation: On a scale of 1 to 10, how would you evaluate your level in
      English?
    level_label: My level is
    alerts:
      test_taken: Please tell us if you have already taken an English Test
      test_results: Please indicate a test result if you have taken one.
      mother_tongue: Please select at least one mother tongue.
    other: Other
  end_test:
    thank_you: Thank you for using Pipplet!
    all_questions: You have answered all the questions.
    updated: Your score will be updated when other candidates take the test.
    notification: You will receive a notification email when it is ready.
    go_back: You can go back to answer questions at any time.
    new_questions: There are no more questions for you to answer. Please come back
      later.
    showcase: You have only seen a few of Pipplet possibilities. Enter your email
      and we will organise a full presentation of our service.
    next: Keep answering questions
    profile: Go to my profile
    next_step: You have answered all the questions. Your answers have been recorded
      and will be assessed.
    survey_1: How did we do?
    survey_2: Share your feedback in our quick 2 minute survey. Your responses are
      anonymous.
    go_to_survey: Take the survey
    technical_report_copy: We are always working to improve the Pipplet experience.
      However you might have encountered a technical issue during your test. If that
      is the case, please let us know below and we will get back to you shortly.
    go_to_technical_report: Report an issue
    audition_survey_url: "#"
    technical_report_url: https://docs.google.com/forms/d/e/1FAIpQLSehLnGKghQOy8pVOGTPUV1RSgJ661AjG7HW8zEkC1QB9G92vQ/viewform?usp=pp_url
    survey_url: https://docs.google.com/forms/d/e/1FAIpQLScBjcSEBi6D4B4X41be4jZAEcL2ylQyVWh9Bd_NYHGMURhKSQ/viewform
    next_test_title: You have completed your language proficiency test.
    next_test_copy: You now have another test to take in %{next_test_language}. Please
      click on the button below when you are ready.
    next_test_button: Start the %{next_test_language} test
    back: Go to home
    quit: Quit
    certification_title: Coming soon for Pipplet FLEX
    certification_text: You will have the possibility to take the Pipplet FLEX certification
      at the end of professional training in the same format, to validate acquired
      competencies. The Pipplet FLEX certification is acclaimed by recruiters in France
      and around the world.
    certification_link: Learn more.
    redirection_step: You have answered all the questions. Your spoken and written
      answers have been recorded and will be assessed by our native-speaking examiners.
      You can now carry on with the next steps asked by %{client}.
  no_more_questions:
    new_user_sorry: We are sorry, we do not have any questions in stock for you to
      start the test.
    sorry: We are sorry, we ran out of questions for you.
    contact: This should not happen, we have been notified and we will get in touch
      with you. If you have any remarks please send us an <NAME_EMAIL>
    back_home: Back to home
  account:
    my_score: My score
    my_account: My account
    beta_message: The score evaluation is not available in the beta version.
    between: Between
    questions_answered: questions answered
    start_answering: Upgrade my score by answering more questions
    no_more_questions: There are no more questions for you to answer, please come
      back later to improve your score.
  score_information:
    title: Your score
    completed: Thanks for completing the test!
    your_score_is: Your score is
    needmore: You need to answer more questions in order to receive your score!
    cecrl: This represents a CECRL level of
    more: You need to answer more questions in order to receive your score!
    computing: Thanks for completing the test! Your score is being computed. We will
      send you an email when it is ready!
    question: Question
    over: answered over
  test_onboarding:
    step:
      tcs: "◯"
      notice: Instructions
      microphone: Microphone
      audio_test: Audio Test
      hardware_check: Hardware check
      tutorial: Tutorial
      start: Test
      succeed_tips: Tips
    keyboard_check_title: Your %{test_langue} test requires special characters.
    keyboard_check_subtitle: Let's check your keyboard.
    keyboard_check_content: 'To do so, please type “%{word}” in the textbox below
      using your keyboard. '
    keyboard_check_placeholder: Type here
    keyboard_check_valid: Valid ✓
    keyboard_check_not_valid: Invalid ⚠
    keyboard_install_keyboard: Do you need help installing a keyboard?
  audio:
    download_failed:
      body: 'The audio files cannot be loaded with your current connection. Try connecting
        to a different network. If none is available try sharing a connection from
        your mobile phone. '
      link: https://help.pipplet.com/en-us/article/how-can-i-take-the-test-if-the-audio-files-wont-load-14umk8r/
    load:
      acceptmic: Please allow for the use of the microphone to take the test.
    record: Click the microphone to start recording your answer. Try to talk for at
      least 20 seconds.
    recording: |-
      Recording : <span class='recorder_duration'>00 / 20 sec.</span>
      <br>Click again to stop the recording.
    recording_in_progress: Click again to stop the recording.
    low_volume: We couldn't hear you very well. Could you try again and talk closer
      to your microphone?
    saving: Saving your recording...
    too_short: You haven't said much! Please try again and talk more. Talk for at
      least 15 seconds.
    secondes: sec.
    submit_while_too_short: Your answer is too short
    recorded: Your answer has been saved! Thank you. If you want to redo your answer,
      click again.
    authorise_microphone_title: Please allow access to your microphone
    please_authorise_mic_2: The Pipplet test needs to use your microphone to record
      your answers.
    please_authorise_mic_step_1: Click "Display the microphone authorization" below.
    please_authorise_mic_step_2: Check the box "Remember this decision" if asked.
    please_authorise_mic_step_3: Click on "Allow".
    authorise_microphone: Continue
    show_authorise_microphone: Display the microphone authorization
    please_authorise_mic_firefox_image_mac: i18n_images/audio.authorise_mic_firefox_mac_en.png
    please_authorise_mic_chrome_image_mac: i18n_images/audio.authorise_mic_chrome_mac_en.png
    please_authorise_mic_firefox_image_win: i18n_images/audio.authorise_mic_firefox_windows_en.png
    please_authorise_mic_chrome_image_win: i18n_images/audio.authorise_mic_chrome_windows_en.png
    mic_was_not_authorised: Oops, it seems that you have blocked access to your microphone.
    unblock_mic_text_chrome: To allow the use of your microphone, please click on
      the icon just left of the address bar.
    unblock_mic_text_chrome_2: In the dialog, select "Allow" for the microphone.
    unblock_mic_image_chrome: i18n_images/audio.unblock_mic_image_chrome_en.png
    unblock_mic_text_firefox: To unblock your microphone, click on the little icon
      just left of the address bar.
    unblock_mic_text_firefox_2: In the "Permissions" section, click on the "x".
    unblock_mic_image_firefox: i18n_images/audio.unblock_mic_image_firefox_en.png
    mic_was_authorised: Great! Your microphone has been allowed. You are now all set
      to answer the questions.
    no_microphone_detected_title: No microphone detected
    no_microphone_detected_body_1: We could not access your microphone. Make sure
      that it's configured correctly in your operating system's settings.
    no_microphone_detected_try_again: Try again !
    not_supported: Unfortunately, your browser does not support the audio features
      we use.
    not_supported_general_navigation: Please access Pipplet from the latest versions
      of Chrome or Firefox.
    not_supported_modal:
      title: Non compatible browser
      text_part1: We are sorry, your browser does not support the required audio and
        video functionalities to take our test.
      text_part2: We recommend that you use the latest versions of
      or: or
      text_part3: in order to execute Pipplet. Please do not hesitate to alert us
        about any error message that you may see.
    refused_modal:
      title: Access to your microphone has been refused.
      text_part1: You have declined the usage of your microphone to Pipplet, the test
        cannot continue.
      text_part2: If you have not done it on purpose you can reactivate it by
      list_element1: Looking for a microphone or a camera icon in the navigation bar
        at the top of your browser.
      list_element2: Clicking on it, then saying that you want to allow the microphone
        to be used for this website.
      list_element3: Reloading the page.
      text_part3: Please do not hesitate to contact us if you're having any difficulties.
    no_audio_device_available_or_allowed_title: We couldn't access your audio device
      properly.
    no_audio_device_available_or_allowed_1: Your microphone is not available or its
      access has been refused.
    no_audio_device_available_or_allowed_2: Go through our microphone authorization
      process again
    upload_to_s3_failed_title: We couldn't save your audio recording to our servers.
    upload_to_s3_failed_1: 'Please ensure that you have a stable internet connection
      and that your computer or network administrator is not blocking requests to
      the following domains: pipplet.com, amazonaws.com and cloudfront.net. If you
      are unable to fix this issue, please contact us.'
    upload_to_s3_failed_2: Go through our microphone test process again
    start_audio_test: Continue
    mic_was_not_authorised_subtitle: Don't worry, we can fix this!
    mic_ok: I can hear myself properly
  camera:
    camera_required_title: Your test requires identity verification
    camera_required_copy: To verify your identity you will need to activate your webcam.
      You will see a pop-up in the top left corner of your browser asking you to authorize
      the webcam, please click 'allow' to continue to the test.
    camera_required_check: Can you see yourself? Please ensure that you are facing
      the camera.
    camera_required_denied_title: Camera not authorized. Please check your media permissions
      settings.
    camera_required_denied_copy: Click on the padlock icon on the left of the address
      bar (see image below).
    camera_permission: Click on the camera or lock icon next to your address bar to
      authorize access to your camera and click on continue.
  timeout:
    title: Timeout
    content_1: Your time to answer this question has run out!
    content_2: No worries, your answer has been recorded as is. You can now go to
      the next question when you are ready.
    next: Next question
    finish: Save & Pause
  question:
    please_correct_errors: 'Please correct the following issues with your answer:'
    answer_not_provided: The answer is not provided
    answer_buttons:
      submit: Submit
      recording: Recording...
      loading: Loading...
      finish: Save & Pause
      next: Next question
    elements:
      player:
        report: Report this recording
    skipped_by_candidate: The candidate found this question too difficult and chose
      to skip it.
    info: The candidate took <b>%{time_min_used}</b> min to answer out of <b>%{time_min_gave}</b>
      min allowed.
    info_timeout: |-
      The candidate took <b> %{time_min_used} </b> min to answer over <b> %{time_min_gave} </b> min allowed.<br>
      The answer was automatically submitted.
  questions:
    elements:
      placeholder: Please type your answer here.
      textcount: 'Number of characters: '
  report:
    title: Please let us know what is wrong with the question.
    audio_quality: The sound quality is too bad.
    no_sound: There is no sound.
    offensive: It is offensive.
    no_help: It does not help to answer the question.
    information_missing: Some information is missing to answer the question.
    irrelevant: The content is irrelevant.
    no_english: It is not in English.
  audio_test:
    title: Key Tips Before Taking the Test
    time: Allow 45min, the test must be taken in one shot.
    notifications: If possible deactivate the notifications on your machine (email,
      etc.).
    relax: Take it easy! Breaks are possible during the test to relax and take a coffee.
      Just click the "Save & Pause" button at the end of a question.
    check: Check your microphone and speakers
    quietplace: Make sure that you are in a quiet place, where you can speak and listen
      without being disturbed.
    headset: 'Use a headset or headphones if you have them. Otherwise, check that
      the microphone and speakers of your device are enabled. '
    loud: Make sure you speak loudly, clearly and into the microphone.
    no_back_button: Do not use the back or forward buttons on your browser during
      the test. It is not possible to go back to a question that has already been
      displayed.
    adapted_keyboard: Make sure that you will be able to type in the language of the
      test by checking that you have the correct keyboard installed on your device.
      <a href="https://help.pipplet.com/en-us/article/how-to-take-the-pipplet-test-in-non-latin-alphabet-languages-2ljukm/"
      target="_blank"> Learn more.</a>
    start_test: Yes, continue to the test
    click_button: 'Please click on the microphone and say: "Is the recording loud
      and clear?".'
    click_button_2: Click on the microphone again to complete the recording.
    click_play: Click on the Play button to listen to your recording
    check_voice: Can you hear your voice loud and clear?
    audio_issue: Something not working? Click here.
    browser_window: Your browser must be currently asking you to allow microphone
      access (look for a tiny window on the top left corner).
    please_allow: Please allow access so you can proceed to the test.
    modal_button: Try again
    test_failed:
      title: Configure your microphone
      b1: Use a headset microphone if you can.
      p1: 'Using a headset microphone usually provides a better recording. You can
        use a mobile phone headset on some computers, or a bluetooth headset. '
      b2: Make sure that the microphone is connected and enabled.
      p2: Check the wiring of your microphone if it is not integrated. Check in the
        audio parameters of your computer that the microphone is enabled and the input
        volume at the maximum.
      b3: Speak loud and clear
      p3: Try to speak closer from your computer microphone, and as loud and clear
        as you can.
    test_failed_alert:
      p: It looks like your microphone is not working! Please make sure it is connected,
        enabled and input level is set to the maximum. Please also try restarting
        your browser.
      link: Need help?
    no_sound:
      title: I can't hear any sound
      b1: Are your speakers on?
      p1: Please make sure you switched your speakers on. Play some music to make
        sure they work!
      b3: Still not working?
      p3: 'Please contact us and give us your account name, the problem you encountered,
        and the browser you were using. We''ll look into it quickly:'
    bad_sound:
      title: I can hear my voice but it is not clear
      b1: If the sound quality is bad
      p1: Are you in a quiet environment? The microphone can pick up background noises
        (fans, etc.) that will reduce the quality of your recording. If you have a
        headset, please use it. It will probably improve the sound quality of your
        recording, as your own computer's cooling fan might be the issue.
      b2: If the volume is too low
      p20: Make sure the volume of your computer is set to the maximum.
      p21: Make sure your microphone input level is set to the maximum.
      p22: Try to speak closer to your computer microphone.
      p23: Speak loud and clear.
      b3: Still not working?
      p3: 'Please contact us and give us your account name, the problem you encountered,
        and the browser you were using. We''ll look into it quickly:'
    launch_test:
      title: Are you ready?
      text_part1: The Pipplet test requires you to be in a quiet environment without
        being disturbed.
      text_part2: If you are not sure that you will be in those conditions for the
        next 45 minutes, you should stop here and come back later.
      text_part2_audition: If you are not sure that you will be in those conditions
        for the next 20 minutes, you should stop here and come back later.
      copy1-1: The Pipplet test requires you to be in a quiet environment without
        being disturbed. If you are not sure that you will be in those conditions
        for the next
      copy1-2: The Pipplet test requires you to be in a quiet environment without
        being disturbed. If you are not sure that you will be in those conditions
        for the next
      copy1-3: Remember, your answers will be assessed according to the criteria set
        by the Common European Framework of Reference for Languages (CEFR).
      copy2-1: The test contains
      copy2-2: questions. Each question is timed independently.
      copy3: 'They will be presented in the following order:'
      list1: Question
      list2: spoken answer
      list3: written answer
      list4: minutes
      start_test: Start the test
      cancel: I will come back later
  feedback:
    feedback: Feedback
    type: Type of feedback
    general: General feedback
    specific: Question specific feedback
    content: Your feedback
    thank_you: Thank you for your message.
    button_message: Send feedback
    message: Do you have any feedback?
    send: Send
    title: Send us some feedback
  identity:
    test:
      title: We need to verify your identity before you can take the test.
      text: You will be taken to a secure website to verify your chosen identity document.
        Please be aware that any fraud attempts will be reported to the concerned
        parties.
      verification: Identity Verification
      status:
        unverified: unverified
        verified: verified
      verified:
        text_1: Thank you for completing the identity verification process.
        text_2: You can now continue to the test.
    title: Identity Verification
    sentence_1: We need to verify your identity before you start the test. A new tab
      will open, you only need this tab if you encounter an issue.
    sentence_2: This will take approximately 5 minutes to complete.
    button: Verify my identity
    during_test:
      title: Identity Verification
      s1: Did you encounter an issue?
      s2: You can report it below. Otherwise, please complete the process on the new
        tab.
      button: Report an issue and proceed with the test
    no_trouble:
      title: Thank you
      text_1: Thank you for completing the identity verification process.
      text_2: Please continue on the new tab.
  identified:
    button: Continue
  tutorial:
    question_position: Instructions will appear here
    title: Please follow this tutorial before you start the test.
    take_tour: Take the tour
    skip: Skip this tutorial
    play_button: There is either a speaker button or a microphone button on each question.
      Click on the speaker to listen to an audio recording. Click on the microphone,
      speak and click again to record your answer. You can record multiple times but
      only the last one will be saved.
    record_button: To record your spoken answers to questions, click on the microphone
      to start recording. Click it again to stop recording. You can record multiple
      times, but only the last one will be saved.
    textarea: "Write your answer in the textbox. \nCopy and paste has been disabled
      to prevent cheating."
    question_area: Question material will be displayed in this area.
    timer: There is a timer for each question. Once the timer stops, your answers
      are saved in their current state. Keep an eye on it! We recommend that you set
      30 seconds aside to proofread your written answers.
    report: 'If there is an issue with the recording (it is empty, insulting or not
      in English), you can click here to notify us. Please be careful, though: if
      the recording is correct and you have reported it, the question will be counted
      as failed.'
    next: After you have answered a question, click here to submit your answer and
      go to the next question.
    finish: If you want to have a break, click here and your question will be submitted
      before you pause the test.
    ok: Next
    welcome_title: Welcome to the tutorial
    welcome: Follow these steps to familiarize yourself with the test interface
    start: Continue
    progress: The progress bar will show you how many questions you have completed.
    camera: Authorize camera access
    skip_button: You can skip the question. If you do, we will consider during your
      assessment that it was too difficult for you.
    no_prev: Do not use the back or forward buttons on your browser during the test.
      It is not possible to return to a previous question.
  tab_change:
    ok: Ok
  help:
    title: Help
    modern_browser: Pipplet uses modern web components that are currently only supported
      by the latest versions of Chrome and Firefox.
    download_advice: If you do not have such a browser please consider downloading
      one.
    audio_check: Be aware that Pipplet uses audio recording and playback. Please be
      sure that your microphone is correctly configured and that the volume of speakers
      is up.
    not_working_1: If something is not working correctly, for example you can't hear
      any sound, you couldn't be recorded on the audio test page, etc., please
    contact_us: contact us
    not_working_2: and give us your account name, the problem you encountered, and
      the browser that you used, and we'll look into it.
  notification:
    validation_errors: Your answer is incomplete. Please complete it before submitting.
  voice_recordings:
    instruction: Instruction
    details: Details
    answer: Answer
    audio_error: Your browser does not support this feature, please use Chrome or
      Firefox
    missing: The recording is missing, there must have been an error during the test
  cecrl:
    title: Level's decription
    introduction: The Pipplet scores are ranging from 0 to 100. They place all test
      takers on a single scale and give you a precise information on each test taker's
      relative position. They are correlated with the Common European Framework of
      Reference for Languages levels below, which in turn give you insights on the
      test taker practical abilities.
    level_group_name:
      column_title: Level group name
      a: Basic User
      b: Independent User
      c: Proficient User
    level:
      column_title: Level
    level_name:
      column_title: Level name
      a1-: Beginner
      a1: Beginner
      a1+: Beginner
      a2-: Elementary
      a2: Elementary
      a2+: Elementary
      b1-: Intermediate
      b1: Intermediate
      b1+: Intermediate
      b2-: Upper Intermediate
      b2: Upper Intermediate
      b2+: Upper Intermediate
      c1-: Advanced
      c1: Advanced
      c1+: Advanced
      c2-: Mastery
      c2: Mastery
      c2+: Mastery
      ai_binary_fail: Fail
      ai_binary_pass: Pass
      ai_binary_grade_pass: Work Ready
      ai_binary_grade_fail: Not Work Ready
    description:
      column_title: Description
      no_answer: "%{user_first_name} was not able to answer."
      a1: |-
        Can understand and use familiar everyday expressions and very basic phrases aimed at the satisfaction of needs of a concrete type.
        Can introduce him/herself and others and can ask and answer questions about personal details such as where he/she lives, people he/she knows and things he/she has.
        Can interact in a simple way provided the other person talks slowly and clearly and is prepared to help.
      a2: |-
        Can understand sentences and frequently used expressions related to areas of most immediate relevance (e.g. very basic personal and family information, shopping, local geography, employment).
        Can communicate in simple and routine tasks requiring a simple and direct exchange of information on familiar and routine matters.
        Can describe in simple terms aspects of his/her background, immediate environment and matters in areas of immediate need.
      b1: |-
        Can understand the main points of clear standard input on familiar matters regularly encountered in work, school, leisure, etc.
        Can deal with most situations likely to arise while travelling in an area where the language is spoken.
        Can produce simple connected text on topics that are familiar or of personal interest.
        Can describe experiences and events, dreams, hopes and ambitions and briefly give reasons and explanations for opinions and plans.
      b2: |-
        Can understand the main ideas of complex text on both concrete and abstract topics, including technical discussions in his/her field of specialisation.
        Can interact with a degree of fluency and spontaneity that makes regular interaction with native speakers quite possible without strain for either party.
        Can produce clear, detailed text on a wide range of subjects and explain a viewpoint on a topical issue giving the advantages and disadvantages of various options.
      c1: |-
        Can understand a wide range of demanding, longer texts, and recognise implicit meaning.
        Can express ideas fluently and spontaneously without much obvious searching for expressions.
        Can use language flexibly and effectively for social, academic and professional purposes.
        Can produce clear, well-structured, detailed text on complex subjects, showing controlled use of organisational patterns, connectors and cohesive devices.
      c2: |-
        Can understand with ease virtually everything heard or read.
        Can summarise information from different spoken and written sources, reconstructing arguments and accounts in a coherent presentation.
        Can express him/herself spontaneously, very fluently and precisely, differentiating finer shades of meaning even in the most complex situations.
  certificate:
    filename: Assessment report of %{full_name} in %{lang} - %{date}
    title: Your assessment order results
    date: 'Date:'
    head: Professional Language Proficiency Assessment
    head-flex: Pipplet FLEX Pro Language Certification
    head-flex-detailed: Pipplet FLEX Pro+ Language Certification
    authenticity: Verify the authenticity of this certificate with a simple request
      to <NAME_EMAIL>. Verification code %{uuid}
    copyright: Copyright © 2023 by Pipplet. All rights reserved. Unauthorized use
      or reproduction is strictly prohibited. Pipplet is a registered trademark of
      Pipplet.<br>ETS and the ETS Logo are registered trademarks of ETS, used under
      license in France by Pipplet.
    name: 'Name of the candidate: '
    language: 'Language:'
    registration_number: 'Registration number:'
    description:
      title: What can you expect at this level?
    synthesis:
      simple: Language Proficiency Levels
      detailed: Overall Skills
      flex: Overall Skills
      academic:
        score: 'Language Fundamentals:'
      spoken:
        score: 'Speaking & Listening:'
        abstract: 'Speaking & Listening:'
      written:
        score: 'Writing & Reading: '
        abstract: 'Reading & Writing:'
      how_pipplet_ai_work:
        description_1: "\nTalent AI is a powerful automated evaluation tool, combining
          machine learning technology with our expertise in language assessment with
          human examiners. Talent AI provides you with a comprehensive report that
          includes a predicted CEFR grade. Talent AI analyzes the test taker's spoken
          and written productions according to the CEFR framework and provides fast
          and objective assessments, allowing you to make informed decisions quickly
          and efficiently. "
    detail:
      name: Detailed Skills
      pronunciation:
        score: 'Phonological Control:'
      spokenFluency:
        score: 'Spoken Fluency:'
      grammar:
        score: 'Grammar:'
      vocabulary:
        score: 'Vocabulary:'
      coherence:
        score: 'Coherence & Cohesion:'
    written:
      title:
        score: 'Level of Written Communication:'
      description:
        score: What does this grade mean?
    spoken:
      title:
        score: 'Level of Spoken Communication:'
    strengh:
      name: "%{user_name}'s Strengths"
      title: 'did particularly well with:'
    recommendations:
      name: "%{user_name}'s Weaknesses"
      title: 'could improve his/her communication in %{language} by:'
    report: Download report
    level: 'Level '
    score:
      title: 'Score: '
      copy1: 'This score corresponds to the '
      copy2: " level on the European CEFR Scale"
      copy3: ". You will find below the description of the skills of such a candidate."
    na:
      title: No certificate available
      copy1: 'We do not have any assessment with this reference. Please '
      copy2: contact us
      copy3: " for more information."
    reading:
      level:
        a1-: Can understand very short, simple texts a single phrase at a time, picking
          up familiar names, words and basic phrases and rereading as required.
        a1: Can understand very short, simple texts a single phrase at a time, picking
          up familiar names, words and basic phrases and rereading as required.
        a1+: Can understand very short, simple texts a single phrase at a time, picking
          up familiar names, words and basic phrases and rereading as required.
        a2-: Can understand short, simple texts containing the highest frequency vocabulary,
          including a proportion of shared international vocabulary items.
        a2: Can understand short, simple texts on familiar matters of a concrete type
          which consist of high frequency everyday or job-related language.
        a2+: Can understand short, simple texts on familiar matters of a concrete
          type which consist of high frequency everyday or job-related language.
        b1-: Can read straightforward factual texts on subjects related to his/her
          field and interests with a satisfactory level of comprehension.
        b1: Can read straightforward factual texts on subjects related to his/her
          field and interests with a satisfactory level of comprehension.
        b1+: Can read straightforward factual texts on subjects related to his/her
          field and interests with a satisfactory level of comprehension.
        b2-: Can read with a large degree of independence, adapting style and speed
          of reading to different texts and purposes. Has a broad active reading vocabulary,
          but may experience some difficulty with low-frequency idioms.
        b2: Can read with a large degree of independence, adapting style and speed
          of reading to different texts and purposes. Has a broad active reading vocabulary,
          but may experience some difficulty with low-frequency idioms.
        b2+: Can read with a large degree of independence, adapting style and speed
          of reading to different texts and purposes. Has a broad active reading vocabulary,
          but may experience some difficulty with low-frequency idioms.
        c1-: Can understand in detail lengthy, complex texts, whether or not they
          relate to his/her own area of speciality, provided he/she can reread difficult
          sections.
        c1: Can understand in detail lengthy, complex texts, whether or not they relate
          to his/her own area of speciality, provided he/she can reread difficult
          sections.
        c1+: Can understand in detail lengthy, complex texts, whether or not they
          relate to his/her own area of speciality, provided he/she can reread difficult
          sections.
        c2-: Can understand virtually all forms of the written language, appreciating
          subtle distinctions of style and implicit as well as explicit meaning.
        c2: Can understand virtually all forms of the written language, appreciating
          subtle distinctions of style and implicit as well as explicit meaning.
        c2+: Can understand virtually all forms of the written language, appreciating
          subtle distinctions of style and implicit as well as explicit meaning.
    listening:
      level:
        a1-: |-
          Can follow speech that is very slow and carefully articulated, with long pauses for him/her to assimilate
          meaning. Can recognise concrete information (e.g. places and times) on familiar topics encountered in everyday life,
          provided it is delivered in slow and clear speech.
        a1: |-
          Can follow speech that is very slow and carefully articulated, with long pauses for him/her to assimilate
          meaning. Can recognise concrete information (e.g. places and times) on familiar topics encountered in everyday life,
          provided it is delivered in slow and clear speech.
        a1+: |-
          Can follow speech that is very slow and carefully articulated, with long pauses for him/her to assimilate
          meaning. Can recognise concrete information (e.g. places and times) on familiar topics encountered in everyday life,
          provided it is delivered in slow and clear speech.
        a2-: |-
          Can understand phrases and expressions related to areas of most immediate priority (e.g. very basic personal
          and family information, shopping, local geography, employment), provided speech is clearly and slowly
          articulated.
        a2: "Can understand enough to be able to meet needs of a concrete type provided
          speech is clearly and slowly\rarticulated."
        a2+: "Can understand enough to be able to meet needs of a concrete type provided
          speech is clearly and slowly\rarticulated."
        b1-: "Can understand the main points of clear standard speech on familiar
          matters regularly encountered in work,\rschool, leisure etc., including
          short narratives."
        b1: "Can understand straightforward factual information about common everyday
          or job related topics, identifying\rboth general messages and specific details,
          provided speech is clearly articulated in a generally familiar\r accent."
        b1+: "Can understand straightforward factual information about common everyday
          or job related topics, identifying\rboth general messages and specific details,
          provided speech is clearly articulated in a generally familiar\r accent."
        b2-: Can understand the main ideas of complex speech, including technical
          discussions in his/her field of specialisation. Can follow extended speech
          and complex lines of argument provided the topic is reasonably familiar,
          and well structured.
        b2: Can understand standard spoken language normally encountered in personal,
          social, academic or vocational life. Only extreme background noise, inadequate
          discourse structure and/or idiomatic usage influence the ability to understand.
        b2+: Can understand standard spoken language normally encountered in personal,
          social, academic or vocational life. Only extreme background noise, inadequate
          discourse structure and/or idiomatic usage influence the ability to understand.
        c1-: Can understand enough to follow extended speech on abstract and complex
          topics beyond his/her own field. Can recognise a wide range of idiomatic
          expressions and colloquialisms, appreciating register shifts. Can follow
          extended speech even when it is not clearly structured.
        c1: Can understand enough to follow extended speech on abstract and complex
          topics beyond his/her own field. Can recognise a wide range of idiomatic
          expressions and colloquialisms, appreciating register shifts. Can follow
          extended speech even when it is not clearly structured.
        c1+: Can understand enough to follow extended speech on abstract and complex
          topics beyond his/her own field. Can recognise a wide range of idiomatic
          expressions and colloquialisms, appreciating register shifts. Can follow
          extended speech even when it is not clearly structured.
        c2-: Can understand with ease virtually any kind of spoken language, whether
          live or broadcast, delivered at fast natural speed.
        c2: Can understand with ease virtually any kind of spoken language, whether
          live or broadcast, delivered at fast natural speed.
        c2+: Can understand with ease virtually any kind of spoken language, whether
          live or broadcast, delivered at fast natural speed.
    writing:
      level:
        a1-: Can write simple isolated phrases and sentences.
        a1: Can give information in writing about matters of personal relevance (e.g.
          likes and dislikes, family, pets) using simple words and basic expressions.
        a1+: Can give information in writing about matters of personal relevance (e.g.
          likes and dislikes, family, pets) using simple words and basic expressions.
        a2-: Can write a series of simple phrases and sentences linked with simple
          connectors like ‘and,’ ‘but’ and ‘because’.
        a2: Can write a series of simple phrases and sentences linked with simple
          connectors like ‘and,’ ‘but’ and ‘because’.
        a2+: Can write a series of simple phrases and sentences linked with simple
          connectors like ‘and,’ ‘but’ and ‘because’.
        b1-: Can write straightforward connected texts on a range of familiar subjects
          within his/her field of interest, by linking a series of shorter discrete
          elements into a linear sequence.
        b1: Can write straightforward connected texts on a range of familiar subjects
          within his/her field of interest, by linking a series of shorter discrete
          elements into a linear sequence.
        b1+: Can write straightforward connected texts on a range of familiar subjects
          within his/her field of interest, by linking a series of shorter discrete
          elements into a linear sequence.
        b2-: Can write clear, detailed texts on a variety of subjects related to his/her
          field of interest, synthesising and evaluating information and arguments
          from a number of sources.
        b2: Can write clear, detailed texts on a variety of subjects related to his/her
          field of interest, synthesising and evaluating information and arguments
          from a number of sources.
        b2+: Can write clear, detailed texts on a variety of subjects related to his/her
          field of interest, synthesising and evaluating information and arguments
          from a number of sources.
        c1-: Can write clear, well-structured texts of complex subjects, underlining
          the relevant salient issues, expanding and supporting points of view at
          some length with subsidiary points, reasons and relevant examples, and rounding
          off with an appropriate conclusion.
        c1: Can write clear, well-structured texts of complex subjects, underlining
          the relevant salient issues, expanding and supporting points of view at
          some length with subsidiary points, reasons and relevant examples, and rounding
          off with an appropriate conclusion.
        c1+: Can write clear, well-structured texts of complex subjects, underlining
          the relevant salient issues, expanding and supporting points of view at
          some length with subsidiary points, reasons and relevant examples, and rounding
          off with an appropriate conclusion.
        c2-: Can write clear, smoothly flowing, complex texts in an appropriate and
          effective style and a logical structure which helps the reader to find significant
          points.
        c2: Can write clear, smoothly flowing, complex texts in an appropriate and
          effective style and a logical structure which helps the reader to find significant
          points.
        c2+: Can write clear, smoothly flowing, complex texts in an appropriate and
          effective style and a logical structure which helps the reader to find significant
          points.
    speaking:
      level:
        a1-: Can produce simple mainly isolated phrases about people and places.
        a1: Can produce simple mainly isolated phrases about people and places.
        a1+: Can produce simple mainly isolated phrases about people and places.
        a2-: Can give a simple description or presentation of people, living or working
          conditions, daily routines. likes/dislikes etc. as a short series of simple
          phrases and sentences linked into a list.
        a2: Can give a simple description or presentation of people, living or working
          conditions, daily routines. likes/dislikes etc. as a short series of simple
          phrases and sentences linked into a list.
        a2+: Can give a simple description or presentation of people, living or working
          conditions, daily routines. likes/dislikes etc. as a short series of simple
          phrases and sentences linked into a list.
        b1-: Can reasonably fluently sustain a straightforward description of one
          of a variety of subjects within his/her field of interest, presenting it
          as a linear sequence of points.
        b1: Can reasonably fluently sustain a straightforward description of one of
          a variety of subjects within his/her field of interest, presenting it as
          a linear sequence of points.
        b1+: Can reasonably fluently sustain a straightforward description of one
          of a variety of subjects within his/her field of interest, presenting it
          as a linear sequence of points.
        b2-: Can give clear, detailed descriptions and presentations on a wide range
          of subjects related to his/her field of interest, expanding and supporting
          ideas with subsidiary points and relevant examples.
        b2: Can give clear, systematically developed descriptions and presentations,
          with appropriate highlighting of significant points, and relevant supporting
          details.
        b2+: Can give clear, systematically developed descriptions and presentations,
          with appropriate highlighting of significant points, and relevant supporting
          details.
        c1-: Can give clear, detailed presentations on complex subjects, integrating
          sub themes, developing particular points and rounding off with an appropriate
          conclusion.
        c1: Can give clear, detailed presentations on complex subjects, integrating
          sub themes, developing particular points and rounding off with an appropriate
          conclusion.
        c1+: Can give clear, detailed presentations on complex subjects, integrating
          sub themes, developing particular points and rounding off with an appropriate
          conclusion.
        c2-: Can produce clear, smoothly flowing well-structured speech with an effective
          logical structure which helps the recipient to notice and remember significant
          points.
        c2: Can produce clear, smoothly flowing well-structured speech with an effective
          logical structure which helps the recipient to notice and remember significant
          points.
        c2+: Can produce clear, smoothly flowing well-structured speech with an effective
          logical structure which helps the recipient to notice and remember significant
          points.
    recommandation:
      _01: improving his/her pronunciation
      _02: improving his/her grammar skills
      _03: improving his/her spelling
      _04: improving his/her use of verb tenses
      _05: trying to master more connective words to link sentences
      _06: speaking more slowly so the need for pause is reduced
      _07: reading in the language to improve his/her comprehension
      _08: listening content in the language to improve his/her listening skills
      _09: improving his/her writing skills
      _10: improving his/her speaking skills
      _11: reading or listening content in the language to learn more vocabulary
      _12: writing shorter sentences
      _13: improving his/her use of language registers
      _14: using more idiomatic expressions
      _15: everything was perfect!
      _16: improving his/her punctuation
      _17: improving his/her grammar fundamentals
      _18: working on using more complex grammar
      _19: avoiding overly complicated sentences that may lead to grammar mistakes
      _20: working on using the right prepositions
      _21: working on his/her subject-verb agreements
      _22: working on using the right articles
      _23: working on his/her word ordering
      _24: working on his/her use of infinitive verb forms
      _25: working on his/her subject-complement agreements
      _27: working on his/her use of conjunctions
      _28: working on using the right possessive pronouns
      _29: proofreading
      _30: working on his/her use of contractions
      _32: improving his/her use of complex verb tenses
      _33: practicing conversation to get used to speaking spontaneously
      _34: practising on where the stress should be in each word
      _35: improving on long vowel sounds
      _36: avoiding adding schwa sounds
      _37: improving on diphthong pronunciation
      _38: improving on "th" sounds
      _39: improving on using reflexive pronouns
      _40: improving on using quantifiers
      _41: improving on completing a secondary clause
      _42: improving on forming questions
      _43: improving on using determiners
      _44: improving on using conditional clauses
      _45: improving on using irregular nouns
      _46: improving on using irregular plurals
      _47: improving on using verb phrases
      _50: avoiding using vocabulary from another language
      _51: using idiomatic expressions correctly
      _52: "%{user_first_name} could not answer the questions."
      _101: Spelling
      _102: Language registers
      _103: Industry-specific vocabulary
      _104: Clarity of response
      _105: Positive attitude
      _106: Nothing
    strenght:
      _01: pronunciation and intonation
      _05: speaking with a natural rhythm
      _14: grammar fundamentals
      _15: complex grammar
      _04: control over tenses
      _03: spelling
      _07: vocabulary
      _08: using connective words and transitional phrases to link sentences
      _09: language registers
      _10: using idiomatic expressions
      _12: reading comprehension
      _13: listening comprehension
      _16: "%{user_first_name} is a complete beginner."
      _11: consistency
      _06: writing
      _02: grammatical control
      _101: Spelling
      _102: Language registers
      _103: Industry-specific vocabulary
      _104: Clarity of response
      _105: Positive attitude
      _106: Nothing
    public_page:
      title: Proficiency level of %{user_first_name} %{user_last_name} in %{language}
      intro: The candidate has taken the test on %{date}. The candidate answered to
        situation-based questions that allowed us to assess oral and written skills
        in professional context.
      explanations:
        title: About this score
        url: https://help.pipplet.com/en-us/article/cefr-levels-17nw2dz/
        link: Find all about CEFR scores
      contact_us:
        title: Contact us
        copy: Do you want to know more about Pipplet, contact us
      share: Share my score
      strengths:
        title: Strengths
      weaknessess:
        title: Weaknesses
      contact:
        title: About Pipplet
        copy: Pipplet is a language assessment service, used by recruiters and trainers
          all over the world.
        learn_more: Learn more
        url: https://www.pipplet.com/en-gb
    partial: Partial
    cheating:
      default:
        title: Test Security Violation
        body: This test does not meet our test security requirements and so has been
          invalidated.
      cheating-facematch-failed:
        title: Impersonation Detected
        body: The identity verification photo and the photos taken during the test
          display significant differences.
  date:
    formats:
      default: "%B %-d, %Y"
  invalid_test:
    body_1: 'We do not have a test with this reference. '
    body_2: Please contact the person who provided you with this reference for more
      information.
  demo_certificate:
    subject: Thank you for taking the Pipplet demo test!
    thankyou: You did it! You just completed a demo of the Pipplet Test. We hope that
      you enjoyed it as much as our many happy customers.
    body_1: The goal of this demo was to help you get acclimated to the Pipplet interface
      while helping you understand how simple, yet effective our tests are in evaluating
      your potential candidates' language competencies.
    body_2: Our examiners won't be evaluating your productions for this test. We have
      however attached a sample certificate to this email to show you how you may
      receive candidates' test results.
    body_2_ai: Your productions will not be evaluated by our AI model. We have however
      attached a sample evaluation report to this email to show you how your candidates’
      results will be presented to you.
    body_3: NA
    forwardit: What next?
    contactus_1: '&nbsp; Are you a professional looking for language assessments for
      your company?<br/><br/>&nbsp; Contact us <a href="mailto:<EMAIL>">here</a>,
      or <a href="https://calendly.com/caubin">book a meeting with us</a>.<br/><br/>'''
    contactus_2: '&nbsp; Would you like to know more about how we assess skills? Find
      out more <a href="https://www.pipplet.com/features/quality-control">here</a>.'
    filename: Pipplet_Report_Sample
    simple_report_sample_certificate: simple_report_sample_certificate
    detailed_sample_certificate: detailed_sample_certificate
    written_sample_certificate: written_sample_certificate
    ai_sample_certificate: ai_sample_certificate
    goodbye: Best,
    signature: The Pipplet Team
  direct:
    title_1: Launch a test session
    title_training: Start a practice test
    invalid_test_type: The requested test does not exist
    select_language: 'Please select the language(s) to assess below:'
    go_languages_button: Launch a test session
    title_2: Ready?
    enter_name: 'Please enter your name below to start the test:'
    start_tests: Start test(s)
  survey:
    title: Tell us what you think.
    intro: We would like to ask you 5 questions to improve Pipplet.
    q1:
      title: 'Would you say that the test was :'
      enjoyable: Enjoyable
      annoying: Annoying
      quick: Quick
      slow: Slow
    q2:
      title: Did you enjoy taking the test?
      lowest: It was awful
      highest: It was awesome
    q3:
      title: How relevant would you say the questions were to reveal your actual level
        in the language?
      lowest: Not relevant at all
      highest: Absolutely relevant
    q4:
      title: 'If you have taken other language tests, would you say Pipplet is :'
      much_better: Much better
      better: Better
      worse: Worse
      much_worse: Much worse
    q6:
      title: |-
        What would you improve?
        Please choose a category and tell us about it.
      choose: Choose a category
      surprise: I was surprised by the format of the test and felt unprepared
      enoughtime: I didn't have enough time
      stressful: The test is too stressful
      toodifficult: The test is too difficult
      dontlike: I don't like the questions
      unclear: The instructions were unclear
      notallskills: Not all of my skills were tested
      noreflect: My responses don't reflect my actual ability
      unappropriate: The test is not appropriate for my level
      notadapted: The test is not adapted to my job
      specific: I had a language-specific issue (keyboard, alphabet, difficulty)
      newfeature: I want to suggest a new feature
      mistake: I found a mistake in the test
      teki: I had a technical issue
    q7:
      title: Which other language tests have you taken?
    q5:
      title: Would you like to give us some details? Go for it!
    thankyou: Thank you for your feedback!
    submit_intro: "Thank you for your comment, it will help improve the Pipplet experience.
      Would you mind if we shared your comment to promote Pipplet? \n<br>\n<br>\nIt
      could look like that :"
    submit_with_share: Yes, feel free to share it!
    submit_no_share: No, please do not share it
    submit: Submit
    thankyou_shareyes: Thank you, we will keep you informed when we use your comment!
    thankyou_shareno: Thank you for your comment, we will keep it private!
  buy:
    choose_language: Choose the language which you want to be tested in
    total_exvat: 'Total (Ex VAT):'
    total_incvat: 'Total (Inc VAT):'
    vat: 'VAT :'
    currency: "€"
    infos_title: Account Information
    order_description: Pipplet Test
    go-button: Start your test
    order_by_card: Pay €66
    success_top: |-
      <p>Thank you for your order!</p>
      <p>You can start your test immediatly.</p>
    success_bottom: We have also emailed you a connection link to your test so you
      can take it later if needed.
    missing_terms: Please accept the terms and conditions
    missing_elements: Please fill all information
    accepts_terms: I accept the <a href="/pages/terms-of-sales" target="_blank">terms
      and conditions</a>.
    title: Welcome to Pipplet!
    introduction: Demonstrate your language skills by taking our language certification
      test!  <br/> During the test you will be presented with scenario-based questions
      and prompts, all in a business-oriented context. Our certified assessors will
      evaluate your skills and you will then receive your results and certificate
      within twenty-four hours. The certificate is a great addition to your CV or
      professional portfolio. <br/></br/> Any questions? <a href="https://help.pipplet.com/en-us/">Take
      a look at our Helpdesk!</a>
  token:
    introduction: Please enter the token that has been given to you in order to start
      your test. If you don't know your token please contact your test administrator.
    submit: Start Test
    placeholder: 'Enter Token here (ex : Xxxxx)'
    error: This token does not exist. Please check again.
  update_success: Updated successfuly
  update_fail: Update failed
  evaluation:
    link_form: Assessment form
  production:
    result: Answer provided by the candidate
    question: Question asked
  test:
    bad_connection:
      title: Your connection is too slow
      body: A network error has been detected. You will be contacted by our support
        team shortly.
      cancel: Ok
    no_connection:
      title: Unable to connect
      body: A network error has been detected. Please contact us via email at <a href="mailto:<EMAIL>"><EMAIL></a>
        for more information
      cancel: Ok
    intro: 'You have been assigned the tests below:'
    test_mode: Sample test, it will not be assessed.
    completed: This test has been completed
    cancelled: This test has been canceled by %{clientname}
    flex: FLEX Certification
    button: Take the test
    finished_title: 'You have completed your %{lang} test

      '
    cancelled_title: 'Your %{lang} proficiency test has been canceled

      '
    finished_desc: You have completed your %{lang} language proficiency test. Thank
      you. You can now close this tab.
    cancelled_desc: Your %{lang} proficiency test have been canceled. Please contact
      your test administrator for more information.
    tips:
      succeed:
        plan: 'Plan your oral responses before you record them. This will help make
          sure your answers are fluid and well-structured.

          '
        detail: Detail your responses as much as possible. The more developed your
          responses, the more likely you are to obtain an advanced level.
        structure: Structure your written responses well. The coherence of your answers
          will be taken into consideration as part of the evaluation.
        re_read: Check each answer thoroughly before moving on to the next - you won't
          be able to go back. Keep in mind each question is timed. We recommend saving
          the last 30 seconds to proofread.
        title: Key Tips for Success
  user:
    continue: Continue
  btn:
    assessment_exemple: Assessment exemple
  skip_question:
    button: Is this question too difficult?
    modal:
      title: 'Would you like to skip this question?       '
      copy: If you skip this question we would consider during your assessment that
        it was too difficult for you.
      cancel: Go back
      confirm: Try again
  skip_short_question:
    modal:
      title: Your answer is too short
      copy: Your answer is too short and this may affect your final grade. Would you
        like to modify it or submit it as is?
      cancel: Go back
      confirm: Modify
  test_profile:
    '74':
      description: Standard recruitment test
    '17':
      description: Standard recruitment test
    '65':
      description: Recruitment test for managers
    '82':
      description: Recruitment test for managers
    '93':
      description: Recruitment test for managers
    '66':
      description: Recruitment test for customer service agents
    '85':
      description: Standard recruitment test
    '91':
      description: Recruitment test for technical support agents
    '90':
      description: Recruitment test for technical support agents
    '89':
      description: Recruitment test for technical support agents
    '88':
      description: Recruitment test for technical support agents
    '99':
      description: Recruitment test for technical support agents
    '100':
      description: Recruitment test for technical support agents
    '72':
      description: Standard end of training test
    '79':
      description: Standard start of training test
    '84':
      description: Standard end of training test
    '97':
      description: Training test for beginners
    '92':
      description: Standard recruitment test
    '95':
      description: Standard recruitment test
    '94':
      description: Japanese Romaji test for Civil aviation personnel
    '96':
      description: Audit test for airport staff
  talentsoft:
    language_test_name: "%{testlanguage} %{testname} Language Test"
  test-card:
    from: for
  teamtailor:
    webhook_config:
      test_profile:
        label: Profile
        placeholder: Select profile
        select_label: Profiles
      type:
        label: Test language
        placeholder: Select test langugage
        select_label: Languages
    partner_results:
      summary:
        completed: Test completed!
        completed_no_grade: test completed but no grade was assigned
        sending: Waiting for the candidate to take the test
      attachments:
        description:
          test_url: test link
          pdf_report_url: 'PDF report '
    activation_data:
      infobox:
        content:
          link: Click
          text: to get in touch with the sales team
      email:
        label: Your email for billing
      first_name:
        label: Your first name
      last_name:
        label: Your last name
    errors:
      invalid_partner_config: Invalid Partner configuration
      invalid_partner_config_with_message: 'Partner configuration error : %{message}'
      invalid_request: Invalid Request
      test_not_found: Test not found
      partner_event_not_found: Partner event not found
  mailjet:
    languages:
      zhyue:
        non_latin_characters_warning: Please respond to the test using traditional
          Chinese characters. Responses using simplified Chinese characters will not
          be taken into account during evaluation and will result in a null score
          for the written section.
  lever:
    errors:
      api_user_not_found: Api User not found
      invalid_webhook_signature: Invalid webhook signature
      invalid_request: Invalid request
      invalid_order: Invalid order
      refresh_token: 'Impossible to refresh token for api_user : %{api_user_name}'
      expired_token: 'Expired token for api_user : %{api_user_name}, a new oauth connection
        is needed'
      save_token: 'Error saving the Token & refresh token created for api_user : %{api_user_name}'
      request_unsuccessful: 'Request was unsuccessful for api_user : %{api_user_name},
        with code: %{error_code}'
      get_opportunity_reasons: 'Error getting opportunity reasons for api_user : %{api_user_name}'
      get_opportunity: 'Error getting opportunity for api_user : %{api_user_name}'
      create_opportunity: 'Error creating opportunity for api_user : %{api_user_name}'
      get_user: 'Error getting the admin user for api_user : %{api_user_name} with
        email: %{admin_email}'
      no_admin_email: No admin email provided
      create_opportunity_note: 'Error creating an opportunity note for api_user :
        %{api_user_name}'
      order_rejected: 'Order rejected, New order : %{api_order_id} from %{api_user_name}
        has been rejected'
    tokens_created: 'Token & refresh token created for api_user: %{api_user_name}'
    token_refreshed: 'Token refreshed : old refresh token : ''%{old_refresh_token}'',
      new refresh token : ''%{new_refresh_token}'''
    order_accepted: 'Order accepted, New order : %{api_order_id} from %{api_user_name}
      has been accepted'
    code_added: 'Code added : %{code}'
    opportunity_note:
      sending: 'Pipplet : Test pending'
      completed_no_grade: 'Pipplet : Test completed (no grade)'
      completed: 'Pipplet : Test completed'
      test_url: 'Test url '
      overall_score_rating: Score rating
      overall_score_cecrl: Score cecrl
      overall_score_date: Score date
      minutes_duration: "%{duration} Minutes"
      error_message: 'Pipplet : Error while creating test : %{error}'
      oauth_login_required: 'Pipplet : Integration disconnected - Please contact support'
    config_opportunity:
      name: 'Pipplet : Candidate for tags (do not delete!)'
      tag_prefix: Pipplet
  reminders:
    sms: 'Pipplet: %{client_name} from %{client_company} would like you to take a
      language test in %{ti_language}. Please check your e-mails for the test invitation.'
  onboarding:
    problem: There seems to be an issue
    instructions-title:
      concentration: Concentration
      audio: Audio Quality
      keyboard: Keyboard
      speak: Speak Clearly
    tips:
      plan: Plan
      elaborate: Elaborate
      structure: Structure
      review: Review
    test_writting_speaking: Oral and written test
    test_writting: Written test
    test_speaking: Oral test
  accepts_marketing_contacts: I agree to be contacted by Pipplet or the ETS Group
    for recommendations about my language skills.
  practice_pack:
    boost:
      title: Boost your chances with our Practice Pack!
      description: Do you want to get your best possible result?<br>Find out more
        about the <strong>Pipplet Practice Pack</strong> and unlock <strong>3 practice
        tests</strong>.
      cta: Learn more about the Practice Pack
  grants_ai_consent: I consent to the transfer of my test answers to an artificial
    intelligence (AI) tool for the purpose of assessing my oral and written linguistic
    skills. I understand that the AI will be used to evaluate my language proficiency
    and provide feedback on my performance. I understand that the AI will be used
    in accordance with the European General Data Protection Regulation (GDPR) and
    all applicable privacy laws.
  terms_of_use_privacy_policy_message: 'By clicking submit, you hereby acknowledge
    that you have read and accepted the <a href="https://www.pipplet.com/terms-and-conditions"
    target="_blank">Terms and conditions</a> and <a href="https://www.pipplet.com/en-gb/Privacy-policy"
    target="_blank">Privacy Policy</a> of Pipplet.

    '
  secure_browser:
    title: Take Your Test in the Secure Browser
    subtitle: To ensure the integrity of the language test, you must take it using
      the <strong>Secure Browser</strong>.
    step_1: |-
      <strong>Step 1: Install Secure Browser</strong>
      <br>
      If you haven’t already installed it, please download it using the link below:
    step_2: |-
      <strong>Step 2: Open the Test in Secure Browser</strong>
      <br>
      Once installed, click the link below to launch the <strong>Secure Browser</strong>.
    required: Please use a Secure Browser compatible computer (Mac or Windows)
    download_macos: Download for MacOS
    download_windows: Download for Windows
    launch_application: Launch the Secure Browser
