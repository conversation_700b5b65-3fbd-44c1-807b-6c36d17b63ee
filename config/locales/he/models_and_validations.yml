---
he:
  activerecord:
    attributes:
      user:
        understands_cgu: Terms & Conditions
        first_name: First name
        last_name: Last name
        new_password: Choose a password
        new_password_placeholder: Leave blank unless you want to change it
        new_password_confirmation: Confirm your password
        new_password_confirmation_placeholder: Leave blank unless you want to change
          it
        terms_of_use_message: '<p style=''text-align: justify;''>To continue you must
          accept our <a href=''/pages/terms-of-use'' target="_blank">terms and conditions.</a></p>'
        confidentiality: '<p style="font-style: italic;font-size:0.8em; text-align:
          justify;">The information collected is subject to computer processing intended
          to implement the services offered by the company Pipplet, a simplified joint-stock
          company, registered at the Paris Trade and Companies Register under number
          813 493 673, the registered office of which is located at 10 rue de Penthièvre
          - 75008 Paris. In accordance with the law on “Information Technology and
          Liberties” of 6 January 1978 as amended and with the GDPR, you have a right
          to access, rectify and delete information regarding you, as well as a portability
          right for data that you provided to us, that you can exercise by contacting
          the company Pipplet, 10 rue de Penthièvre - 75008 Paris –<EMAIL>.
          You can also object to the processing of data concerning you on legitimate
          grounds.<br/><br/>For more information, please take note of the terms of
          processing of your data : <a href="https://www.pipplet.com/en-gb/Privacy-policy#Test-takers"
          target="_blank"><b>Click here to read our Charter on the protection of personal
          data</b></a></p>'
        code_of_registration_code: Registration Code
        email: Email
        phone_number: Phone number
    errors:
      models:
        user:
          attributes:
            understands_cgu:
              blank: must be accepted
            confirmation_token:
              invalid: Your confirmation token is invalid
          no_email_or_no_token: Your email is not authorized and you didn\'t provide
            a valid registration code. Try again or contact us for preview access.
  question_validation:
    cannot_be_empty: Please answer this question before submitting your answer.
    cannot_be_nil: This element must exist.
    answer_cannot_contain_word: Your answer cannot contain the word
    text_too_short: The text that you entered is too short.
  errors:
    messages:
      something_wrong: Oh no! Something went wrong
      accepted: must be accepted
      blank: can't be blank
      present: must be blank
      confirmation: doesn't match %{attribute}
      empty: can't be empty
      equal_to: must be equal to %{count}
      even: must be even
      exclusion: is reserved
      greater_than: must be greater than %{count}
      greater_than_or_equal_to: must be greater than or equal to %{count}
      inclusion: is not included in the list
      invalid: is invalid
      less_than: must be less than %{count}
      less_than_or_equal_to: must be less than or equal to %{count}
      model_invalid: '%{errors}"'
      not_a_number: is not a number
      not_an_integer: must be an integer
      odd: must be odd
      required: must exist
      taken: has already been taken
      too_long:
        one: is too long (maximum is %{count} characters)
      too_short:
        one: is too short (minimum is 1 character)
        other: is too short (minimum is %{count} characters)
      wrong_length:
        one: is the wrong length (should be 1 character)
        other: is the wrong length (should be %{count} characters)
      other_than: must be other than %{count}
    template:
      body: 'There were problems with the following fields: '
      header:
        one: 'Please correct the following issue: '
        other: 'Please correct the following issues: '
