---
de:
  activerecord:
    attributes:
      user:
        understands_cgu: "#N/A"
        first_name: Vorname
        last_name: Nachname
        new_password: <PERSON><PERSON>hl<PERSON> Sie ein Passwort
        new_password_placeholder: <PERSON><PERSON> leer, wenn Sie es nicht ändern
          möchten.
        new_password_confirmation: Passwort bestätigen
        new_password_confirmation_placeholder: Leave blank unless you want to change
          it
        terms_of_use_message: '<p style=''text-align: justify;''>Um fortzufahren,
          akzeptieren Si<PERSON> bitte unsere <a href=''/pages/terms-of-use'' target="_blank">Nutzungsbedingungen.</a></p>'
        confidentiality: '<p style="font-style: italic;font-size:0.8em;  text-align:
          justify;">Information werden zum Zwecke der Auswertung durch die Firma Pipplet
          gesammelt, einer vereinfachten Aktiengesellschaft (Société par Action Simplifiée),
          registriert unter der Nummer 813 493 673 im Pariser Handelsregister, 10
          rue de Penthièvre - 75008 Paris. In Übereinstimmung mit dem französischen
          Gesetz "Informationstechnologie und -rechte", in Kraft seit dem 6. Januar
          1978 und ergänzt von der europäischen Datenschutz-Grundverordnung, haben
          Sie das Recht, auf Informationen über Sie zuzugreifen, sie korrigieren und
          löschen zu lassen sowie Daten, die Sie uns zur Verfügung gestellt haben,
          übertragen zu lassen. Sie können Ihre Rechte ausüben, indem Sie die Firma
          Pipplet kontaktieren: 10 rue de Penthièvre - 75008 Paris - <EMAIL>.
          Sie können außerdem der Verarbeitung Ihrer Daten aus triftigen Gründen widersprechen.<br/><br/>Für
          mehr Informationen nehmen Sie bitte unsere Hinweise zur Datenverarbeitung
          zur Kenntnis: <a href="https://www.pipplet.com/en-gb/Privacy-policy#Test-takers"
          target="_blank"><b>Für Datenschutzrichtlinie hier klicken.</b></a></p>'
        code_of_registration_code: Registration Code
        email: E-Mail
        phone_number: Telefonnummer
    errors:
      models:
        user:
          attributes:
            understands_cgu:
              blank: must be accepted
            confirmation_token:
              invalid: Your confirmation token is invalid
          no_email_or_no_token: Your email is not authorized and you didn\'t provide
            a valid registration code. Try again or contact us for preview access.
  question_validation:
    cannot_be_empty: Bitte beantworten Sie die Frage, bevor Sie Ihre Antwort einreichen.
    cannot_be_nil: This element must exist.
    answer_cannot_contain_word: Your answer cannot contain the word
    text_too_short: Der eingegebene Text ist zu kurz.
  errors:
    messages:
      something_wrong: Oh no! Something went wrong
      accepted: must be accepted
      blank: can't be blank
      present: must be blank
      confirmation: doesn't match %{attribute}
      empty: can't be empty
      equal_to: must be equal to %{count}
      even: must be even
      exclusion: is reserved
      greater_than: must be greater than %{count}
      greater_than_or_equal_to: must be greater than or equal to %{count}
      inclusion: is not included in the list
      invalid: is invalid
      less_than: must be less than %{count}
      less_than_or_equal_to: must be less than or equal to %{count}
      model_invalid: '%{errors}"'
      not_a_number: is not a number
      not_an_integer: must be an integer
      odd: must be odd
      required: must exist
      taken: has already been taken
      too_long:
        one: is too long (maximum is %{count} characters)
      too_short:
        one: is too short (minimum is 1 character)
        other: is too short (minimum is %{count} characters)
      wrong_length:
        one: is the wrong length (should be 1 character)
        other: is the wrong length (should be %{count} characters)
      other_than: must be other than %{count}
    template:
      body: 'There were problems with the following fields: '
      header:
        one: 'Please correct the following issue: '
        other: 'Please correct the following issues: '
