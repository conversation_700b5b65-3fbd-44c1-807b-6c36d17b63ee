---
de:
  general:
    close: "#N/A"
    leaving_test:
      title: Careful!
      body: Sie sind mitten im Test, sind Si<PERSON> sicher, dass Sie verlassen wollen? Die
        angezeigte Frage gilt als nicht beantwortet.
      button_stay: Go back and finish the test
      button_leave: Leave the test
    unfocus_test_title: Bleiben Sie bei uns!
    unfocus_test_1: Während des Tests dürfen Sie das Fenster nicht wechseln. Möchten
      Sie diese Seite wirklich verlassen?
    unfocus_test_2: Die Verwendung eines anderen Fensters wird als Betrug gewertet.
      Machen Sie bitte mit dem Test weiter, bis er beendet ist.
    unfocus_test_4: Ich verwende eine virtuelle Tastatur
    answer_questions: Answer the questions
    get_started: Los geht's
    continue: Weiter
    no_javascript: Ihr Browser unterstützt kein JavaScript oder es wurde deaktiviert.
      Bitte benutzen Sie einen JavaScript-fähigen Browser, um den Test durchzuführen.
    'yes': Ja
    'no': Nein
    to: to
  cookie_consent:
    message: Diese <PERSON>r<PERSON>, um bestmögliche Nutzerfreundlichkeit
      zu bieten. Wir speichern keine personenbezogenen Daten.
    dismiss: Ich stimme zu
    learn_more: Mehr Infos
  footer:
    termsofuse: Nutzungsbedingungen
    contact: Kontakt
    legal: Rechtliches
  home:
    title: Wilkommen bei Pipplet!
    intro: 'Pipplet ist eine Online-Plattform, die Sprachtests und -bewertungen anbietet.
      In unserem Test aus Fragen und Rollenspielszenarien werden deine Antworten aufgezeichnet.
      Die gesammelten Informationen unterliegen einer Computerbearbeitung, die darauf
      abzielt, die von Pipplet angebotenen Dienste umzusetzen.

      '
    intro_written: Pipplet ist eine Online-Plattform, die Sprachtests und -einstufungen
      durchführt. Der Test präsentiert Ihnen Fragen und Rollenspiele und zeichnet
      Ihre schriftlichen Antworten auf.
    schools: Pipplet is currently available to students of Sorbonne-Universités, Paris-6,
      ISIT and UTC. Please do not hesitate to contact your foreign languages professors
      to get more information about the validity of the test in your university.
    beta: 'The test is still in its first version: please do not hesitate to give
      us feedback.'
    enjoy: Sie sollte eine Einladung zum Test erhalten haben. Überprüfen Sie Ihre
      E-Mails oder kontaktieren Sie <NAME_EMAIL>.
    contact_1: 'If you have any questions or concerns, feel free to contact us at '
    contact_2: <EMAIL>.
  waiting_confirmation:
    waiting_confirmation_title: We have sent you an email!
    waiting_confirmation_content: In order to complete your registration and start
      using our service, please click the link in the email we sent you.
  menu:
    my_account: Mein Konto
    continue: Weiter
    start_answering: Answer questions
    signin: Anmelden
    signup: Registrieren
    signout: Abmelden
  welcome:
    title: Wilkommen bei Pipplet!
    intro: Pipplet assesses people’s ability to understand and be understood by others.
    concept: While taking the test, you will have to talk to and listen to other test
      takers.
    concept2: Your ability to be understood by them, and in return to understand their
      recordings, is what will determine your results of the Pipplet test.
    contact: Please feel free to contact us should you have any remark or question.
    beforetest: Before you take the test
    BT1: Make sure you are in a quiet place, where you can speak and listen without
      interruption.
    BT2: The test must be taken in one attempt; please set aside 45 minutes in which
      you will not be disturbed.
    BT3: If possible deactivate the notifications on your computer (email, etc.).
    BT4: Take it easy! Breaks are possible during the test to relax and take a coffee.
      Just exit at the end of a question using the [ Save and Pause ] button. You
      will be able to resume the test later.
    first_time: As this is your first time answering questions, we would like to ask
      you some information. This will help us improve the test.
    new_questions: You have answered all available questions. Please come back later
      to answer more.
    demo_info: Also, please do not hesitate to contact us if you have any questions
      or remarks about the test.
  language_information:
    title: It's your first time here, tell us a bit about yourself!
    other_tests: Have you ever taken an English proficiency test?
    other_tests_results: Please give us the results you got.
    mother_tongue: What is your mother tongue? You can select more than one.
    language_tests_intro: As this is your first time answering questions, we would
      like to ask you some information to help us improve our service.
    test_label: 'English test taken:'
    test_placeholder: Test name
    score_label: 'Score obtained:'
    score_placeholder: Score result
    level_estimation: On a scale of 1 to 10, how would you evaluate your level in
      English?
    level_label: My level is
    alerts:
      test_taken: Please tell us if you have already taken an English Test
      test_results: Please indicate a test result if you have taken one.
      mother_tongue: Please select at least one mother tongue.
    other: Other
  end_test:
    thank_you: Vielen Dank, dass Sie Pipplet nutzen!
    all_questions: Sie haben alle Fragen beantwortet.
    updated: Your score will be updated when other candidates take the test.
    notification: You will receive a notification email when it is ready.
    go_back: You can go back to answer questions at any time.
    new_questions: There are no more questions for you to answer. Please come back
      later.
    showcase: You have only seen a few of Pipplet possibilities. Enter your email
      and we will organise a full presentation of our service.
    next: Keep answering questions
    profile: Go to my profile
    next_step: Sie haben alle Fragen beantwortet. Ihre Antworten wurden aufgezeichnet
      und werden von unseren muttersprachlichen Prüfern bewertet.
    survey_1: Wie war's?
    survey_2: Bitte nehmen Sie an dieser kurzen, ca. zweiminütigen Umfrage teil. Ihr
      Feedback bleibt anonym.
    go_to_survey: Zur Umfrage
    technical_report_copy: Wir arbeiten immer daran, die Pipplet Erfahrung zu verbessern.
      Allerdings könnten ein technisches Problem während des Tests auftreten. Wenn
      das der Fall ist, verwenden Sie bitte das unten stehende Formular und wir werden
      in Kürze auf Sie zurückkommen.
    go_to_technical_report: Melde Probleme
    audition_survey_url: "#"
    technical_report_url: https://docs.google.com/forms/d/e/1FAIpQLSehLnGKghQOy8pVOGTPUV1RSgJ661AjG7HW8zEkC1QB9G92vQ/viewform?usp=pp_url
    survey_url: https://docs.google.com/forms/d/e/1FAIpQLScBjcSEBi6D4B4X41be4jZAEcL2ylQyVWh9Bd_NYHGMURhKSQ/viewform
    next_test_title: Sie haben Ihren Sprachleistungstest absolviert.
    next_test_copy: 'Sie haben jetzt noch einen Test auf %{next_test_language} zu
      machen. Bitte klicken Sie unten, wenn Sie dafür bereit sind. '
    next_test_button: Beginnen Sie mit dem Test.
    back: Go to home
    quit: Quit
    certification_title: Coming soon for Pipplet FLEX
    certification_text: You will have the possibility to take the Pipplet FLEX certification
      at the end of professional training in the same format, to validate acquired
      competencies. The Pipplet FLEX certification is acclaimed by recruiters in France
      and around the world.
    certification_link: Learn more.
    redirection_step: Sie haben alle Fragen beantwortet. Ihre mündlichen und schriftlichen
      Antworten wurden gespeichert und werden in Kürze von unseren muttersprachlichen
      Prüfern bewertet. Sie können jetzt mit den von %{client} verlangten Schritten
      weitermachen.
  no_more_questions:
    new_user_sorry: We are sorry, we do not have any questions in stock for you to
      start the test.
    sorry: We are sorry, we ran out of questions for you.
    contact: This should not happen, we have been notified and we will get in touch
      with you. If you have any remarks please send us an <NAME_EMAIL>
    back_home: Back to home
  account:
    my_score: My score
    my_account: My account
    beta_message: The score evaluation is not available in the beta version.
    between: Between
    questions_answered: questions answered
    start_answering: Upgrade my score by answering more questions
    no_more_questions: There are no more questions for you to answer, please come
      back later to improve your score.
  score_information:
    title: Your score
    completed: Thanks for completing the test!
    your_score_is: Your score is
    needmore: You need to answer more questions in order to receive your score!
    cecrl: This represents a CECRL level of
    more: You need to answer more questions in order to receive your score!
    computing: Thanks for completing the test! Your score is being computed. We will
      send you an email when it is ready!
    question: Frage
    over: answered over
  test_onboarding:
    step:
      tcs: "◯"
      notice: Anweisungen
      microphone: Mikrofon
      audio_test: Audio-Test
      hardware_check: Hardware überprüfen
      tutorial: Lernprogramm
      start: Test
      succeed_tips: Tipps
    keyboard_check_title: 'Ihr %{test_langue} Test erfordert Sonderzeichen. '
    keyboard_check_subtitle: Lassen Sie uns Ihre Tastatur überprüfen.
    keyboard_check_content: Geben Sie dazu “%{word}” mit Hilfe Ihrer Tastatur in das
      Textfeld unten ein.
    keyboard_check_placeholder: Geben Sie es hier ein.
    keyboard_check_valid: Gültig ✓
    keyboard_check_not_valid: Ungültig ⚠
    keyboard_install_keyboard: Benötigen Sie Hilfe bei der Installation einer Tastatur?
  audio:
    download_failed:
      body: Die Audiodateien können mit Ihrer aktuellen Verbindung nicht geladen werden.
        Versuchen Sie, eine Verbindung zu einem anderen Netzwerk herzustellen. Wenn
        keine verfügbar ist, versuchen Sie, sich mit dem Internetzugang Ihres Mobiltelefons
        zu verbinden.
      link: https://help.pipplet.com/en-us/article/how-can-i-take-the-test-if-the-audio-files-wont-load-14umk8r/
    load:
      acceptmic: Please allow for the use of the microphone to take the test.
    record: Klicken Sie auf das Mikrofon, um die Antwort aufzunehmen. Sprechen Sie
      mindestens 20 Sekunden lang.
    recording: |-
      Aufnahme: <span class='recorder_duration'>00 / 20 Sek.</span>
      <br>Klicken Sie erneut, wenn Sie mit der Aufnahme fertig sind.
    recording_in_progress: Klicken Sie erneut, wenn Sie die fertig sind!
    low_volume: Wir konnten Sie nicht gut hören. Würden Sie es bitte noch einmal versuchen
      und näher am Mikrofon sprechen?
    saving: Ihre Aufnahme wird gespeichert...
    too_short: 'Sie haben nicht viel gesagt! Bitte versuchen Sie es noch einmal und
      sprechen Sie etwas mehr. Nehmen Sie sich für mindestens 15 Sekunden auf. '
    secondes: Sek.
    submit_while_too_short: Die Antwort ist zu kurz
    recorded: Danke, Ihre Antwort ist gespeichert. Wenn Sie Ihre Antwort überarbeiten
      möchten, klicken Sie noch einmal.
    authorise_microphone_title: Erlauben Sie bitte den Zugriff auf Ihr Mikrofon
    please_authorise_mic_2: Der Pipplet-Test benötigt den Zugriff auf Ihr Mikrofon,
      um Ihre Antworten aufzuzeichnen.
    please_authorise_mic_step_1: Klicken Sie bitte auf "Anzeige des Dialogfelds zur
      Authorisierung des Mikrofons".
    please_authorise_mic_step_2: Prüfe die Checkbox "Entscheidung merken", ob gefragt.
    please_authorise_mic_step_3: Klicken Sie bitte auf "Zulassen".
    authorise_microphone: Weiter
    show_authorise_microphone: Anzeige des Dialogfelds zur Authorisierung des Mikrofons
    please_authorise_mic_firefox_image_mac: i18n_images/audio.authorise_mic_firefox_mac_en.png
    please_authorise_mic_chrome_image_mac: i18n_images/audio.authorise_mic_chrome_mac_en.png
    please_authorise_mic_firefox_image_win: i18n_images/audio.authorise_mic_firefox_windows_en.png
    please_authorise_mic_chrome_image_win: i18n_images/audio.authorise_mic_chrome_windows_en.png
    mic_was_not_authorised: Sie haben den Zugriff auf Ihr Mikrofon nicht aktiviert.
    unblock_mic_text_chrome: 'Um die Verwendung Ihres Mikrofons zu erlauben, klicken
      Sie bitte auf das Symbol links neben der Adressleiste. '
    unblock_mic_text_chrome_2: Wählen Sie im Dialogfenster "Einstellungen" aus und
      setzen Sie die Einstellung für das Mikrofon auf "Immer auf dieser Seite zulassen".
    unblock_mic_image_chrome: i18n_images/audio.unblock_mic_image_chrome_en.png
    unblock_mic_text_firefox: Um die Verwendung Ihres Mikrofons zu erlauben, klicken
      Sie bitte auf das kleine links neben der Adressleiste.
    unblock_mic_text_firefox_2: Klicken Sie im Bereich "Berechtigungen" unter "Verwendung
      des Mikrofons" die kreuz.
    unblock_mic_image_firefox: i18n_images/audio.unblock_mic_image_firefox_en.png
    mic_was_authorised: Großartig! Dein Mikrofon wurde zugelassen. Nun ist alles bereit,
      dass du die Fragen beantworten kannst.
    no_microphone_detected_title: Kein Mikrofon erkannt
    no_microphone_detected_body_1: Wir können nicht auf Ihr Mikrofon zugreifen. Stellen
      Sie sicher, dass es in den Einstellungen Ihres Betriebssystems richtig konfiguriert
      ist.
    no_microphone_detected_try_again: Versuchen Sie es noch einmal!
    not_supported: Leider unterstützt dein Browser nicht die von uns verwendeten Audiofunktionen.
    not_supported_general_navigation: Please access Pipplet from the latest versions
      of Chrome or Firefox.
    not_supported_modal:
      title: Non compatible browser
      text_part1: We are sorry, your browser does not support the required audio and
        video functionalities to take our test.
      text_part2: We recommend that you use the latest versions of
      or: or
      text_part3: in order to execute Pipplet. Please do not hesitate to alert us
        about any error message that you may see.
    refused_modal:
      title: Access to your microphone has been refused.
      text_part1: You have declined the usage of your microphone to Pipplet, the test
        cannot continue.
      text_part2: If you have not done it on purpose you can reactivate it by
      list_element1: Looking for a microphone or a camera icon in the navigation bar
        at the top of your browser.
      list_element2: Clicking on it, then saying that you want to allow the microphone
        to be used for this website.
      list_element3: Reloading the page.
      text_part3: Please do not hesitate to contact us if you're having any difficulties.
    no_audio_device_available_or_allowed_title: Wir konnten nicht auf Ihr Audiogerät
      zugreifen.
    no_audio_device_available_or_allowed_1: Ihr Mikrofon ist nicht verfügbar oder
      der Zugriff wurde abgelehnt.
    no_audio_device_available_or_allowed_2: Führen Sie die Mikrofonautorisierung erneut
      durch
    upload_to_s3_failed_title: Wir konnten Ihre Audioaufnahme nicht auf unseren Servern
      speichern.
    upload_to_s3_failed_1: 'Stellen Sie bitte sicher, dass Sie über eine stabile Internetverbindung
      verfügen und Ihr Computer- oder Netzwerkadministrator keine Anfragen an die
      folgenden Domains blockiert: pipplet.com, amazonaws.com und cloudfront.net.
      Falls Sie das Problem nicht beheben können, wenden Sie sich bitte an uns.'
    upload_to_s3_failed_2: Führen Sie den Mikrofontest erneut durch
    start_audio_test: Weiter
    mic_was_not_authorised_subtitle: Keine Sorge, wir können das beheben!
    mic_ok: Ich kann mich gut hören.
  camera:
    camera_required_title: 'Ihr Test erfordert eine kurze Identitätsüberprüfung. '
    camera_required_copy: 'Um Ihre Identität zu bestätigen, bitten wir Sie, Ihre Webcam
      zu aktivieren. Dafür wird in der linken oberen Ecke ein Pop-Up erscheinen, das
      Sie um Erlaubnis bittet, die Webcam zu verwenden. Bitte klicken Sie auf "Erlauben",
      um zum Test fortzufahren. '
    camera_required_check: 'Können Sie sich selber sehen? Bitte stellen Sie sicher,
      dass Ihr Gesicht der Kamera zugewandt ist. '
    camera_required_denied_title: Camera not authorized. Please check your media permissions
      settings.
    camera_required_denied_copy: Klicken Sie auf das Vorhängeschloss-Symbol links
      neben der Adressleiste (siehe Abbildung unten).
    camera_permission: Click on the camera or lock icon next to your address bar to
      authorize access to your camera and click on continue.
  timeout:
    title: Zeitüberschreitung
    content_1: Die Zeit für diese Frage ist abgelaufen!
    content_2: Keine Sorge, Ihre Antwort wurde so aufgezeichnet, wie sie war. Sie
      können nun zur nächsten Frage übergehen, wenn Sie soweit sind.
    next: Nächste Frage
    finish: Speichern & Anhalten
  question:
    please_correct_errors: 'Please correct the following issues with your answer:'
    answer_not_provided: The answer is not provided
    answer_buttons:
      submit: Abschicken
      recording: Aufnahme läuft...
      loading: Laden...
      finish: Speichern & Anhalten
      next: Nächste Frage
    elements:
      player:
        report: Report this recording
    skipped_by_candidate: The candidate found this question too difficult and chose
      to skip it.
    info: The candidate took <b>%{time_min_used}</b> min to answer out of <b>%{time_min_gave}</b>
      min allowed.
    info_timeout: |-
      The candidate took <b> %{time_min_used} </b> min to answer over <b> %{time_min_gave} </b> min allowed.<br>
      The answer was automatically submitted.
  questions:
    elements:
      placeholder: Bitte geben Sie hier Ihre Antwort ein.
      textcount: 'Anzahl der Zeichen: '
  report:
    title: Please let us know what is wrong with the question.
    audio_quality: Die Tonqualität ist zu schlecht.
    no_sound: There is no sound.
    offensive: It is offensive.
    no_help: It does not help to answer the question.
    information_missing: Some information is missing to answer the question.
    irrelevant: The content is irrelevant.
    no_english: It is not in English.
  audio_test:
    title: wichtige Tipps vor dem Test
    time: Planen Sie 45 Minuten für den gesamten Test ein, er muss in einem Mal abgelegt
      werden.
    notifications: If possible deactivate the notifications on your machine (email,
      etc.).
    relax: Locker bleiben! Sie können während des Tests Pausen machen, um kurz durchzuatmen
      oder einen Schluck Kaffee zu trinken. Klicken Sie einfach auf "Speichern und
      pausieren" am Ende einer Aufgabe.
    check: Überprüfen Sie Ihr Mikrofon und Ihre Lautsprecher
    quietplace: Bitte achten Sie darauf, dass Sie sich an einem ruhigen Ort befinden,
      wo Sie ungestört sprechen und zuhören können.
    quietplace_no_speaking: Stellen Sie sicher, dass Sie sich an einem ruhigen Ort
      befinden, an dem Sie sich konzentrieren können.
    headset: Verwenden Sie ein Headset oder Kopfhörer, wenn Sie welche haben. Überprüfen
      Sie andernfalls, ob das Mikrofon und die Lautsprecher Ihres Geräts aktiviert
      sind.
    loud: Denken Sie daran, laut, deutlich und direkt in das Mikrofon zu sprechen.
    no_back_button: Benutzen Sie während des Tests nicht die Vorwärts- oder Rückwärts-Schaltflächen
      Ihres Browsers. Es ist nicht möglich, auf eine eingereichte Frage zurückzukommen.
    adapted_keyboard: Stellen Sie sicher, dass Sie die Testsprache eingeben können,
      indem Sie überprüfen, ob auf Ihrem Gerät die richtige Tastatur installiert ist.
      <a href="https://help.pipplet.com/en-us/article/how-to-take-the-pipplet-test-in-non-latin-alphabet-languages-2ljukm/"
      target="_blank"> Weitere Informationen.</a>
    start_test: Ja, weiter zum Test
    click_button: Bitte klicken Sie auf das Mikrofon und sagen Sie "Hallo! Ist die
      Aufnahme klar?".
    click_button_2: Klicken Sie erneut auf das Mikrofon, um die Aufnahme abzuschließen.
    click_play: Klicken Sie nun auf den Lautsprecher, um die Aufnahme anzuhören.
    check_voice: Können Sie Ihre Stimme laut und deutlich hören?
    audio_issue: Something not working? Click here.
    browser_window: Your browser must be currently asking you to allow microphone
      access (look for a tiny window on the top left corner).
    please_allow: Please allow access so you can proceed to the test.
    modal_button: Versuchen Sie es erneut auf der Testseite
    test_failed:
      title: Konfigurieren Sie Ihr Mikrofon
      b1: Verwenden Sie wenn möglich ein Headset-Mikrofon.
      p1: Die Verwendung eines Headset-Mikrofons sorgt in der Regel für eine bessere
        Aufnahme. Sie können ein Handy-Headset auf einigen Computern oder ein Bluetooth-Headset
        verwenden.
      b2: Vergewissern Sie sich, dass das Mikrofon angeschlossen und aktiviert ist.
      p2: Überprüfen Sie die Verkabelung Ihres Mikrofons, falls es nicht integriert
        ist. Überprüfen Sie in den Audio-Einstellungen Ihres Computers, ob das Mikrofon
        eingeschaltet ist und die Eingangslautstärke auf Maximum eingestellt ist.
      b3: Sprechen Sie laut und deutlich
      p3: Versuchen Sie, näher am Computermikrofon und so laut und deutlich wie möglich
        zu sprechen.
    test_failed_alert:
      p: Es scheint, als ob Ihr Mikrofon nicht funktioniert! Bitte stellen Sie sicher,
        dass es angeschlossen, aktiviert und der Eingangspegel auf das Maximum eingestellt
        ist. Bitte versuchen Sie ebenfalls, Ihren Browser neu zu starten.
      link: Benötigen Sie Hilfe?
    no_sound:
      title: Ich kann die Aufnahme nicht hören.
      b1: Sind Ihre Lautsprecher eingeschaltet?
      p1: Bitte vergewissern Sie sich, dass die Lautsprecher eingeschaltet sind. Spielen
        Sie Musik ab, um sicherzugehen, dass sie funktionieren!
      b3: Funktioniert es immer noch nicht?
      p3: 'Bitte kontaktieren Sie uns und teilen Sie uns Ihren Benutzernamen, das
        auftretende Problem und den verwendeten Browser mit, und wir werden uns der
        Sache schnell annehmen:'
    bad_sound:
      title: Ich kann meine Stimme hören, aber sie ist nicht klar
      b1: Wenn die Klangqualität schlecht ist
      p1: Befinden Sie sich in einer ruhigen Umgebung? Umgebungsgeräusche, Lüftungen
        usw. verursachen Nebengeräusche und verschlechtern die Qualität Ihrer Aufnahme.
        Wenn Sie ein Headset haben, verwenden Sie es bitte. Es wird wahrscheinlich
        die Klangqualität verbessern, da der Lüfter Ihres eigenen Computers das Problem
        sein könnte.
      b2: Wenn die Lautstärke zu niedrig ist
      p20: Stellen Sie sicher, dass die Lautstärke Ihres Computers auf das Maximum
        eingestellt ist.
      p21: Stellen Sie sicher, dass die Lautstärke Ihres Mikrofoneingangs auf das
        Maximum eingestellt ist.
      p22: Versuchen Sie, näher am Computermikrofon zu sprechen.
      p23: Sprechen Sie so laut und deutlich wie möglich.
      b3: Funktioniert es immer noch nicht?
      p3: 'Bitte kontaktieren Sie uns und teilen Sie uns Ihren Benutzernamen, das
        auftretende Problem und den verwendeten Browser mit, und wir werden uns der
        Sache schnell annehmen:'
    launch_test:
      title: Sind Sie bereit?
      text_part1: Der Pipplet-Test setzt voraus, dass Sie sich in einer ruhigen Umgebung
        befinden, ohne gestört zu werden.
      text_part2: If you are not sure that you will be in those conditions for the
        next 45 minutes, you should stop here and come back later.
      text_part2_audition: Wenn Sie diese Bedingungen in den nächsten 20 Minuten nicht
        sicherstellen können, sollten Sie hier anhalten und später weitermachen.
      copy1-1: Der Pipplet-Test setzt voraus, dass Sie sich in einer ruhigen Umgebung
        befinden, ohne gestört zu werden. Wenn Sie diese Bedingungen in den nächsten
      copy1-2: Minuten nicht sicherstellen können, sollten Sie hier anhalten und später
        weitermachen.
      copy1-3: Ihre Antworten werden von qualifizierten Prüfern, die Muttersprachler
        sind, analysiert und bewertet, unter Einhaltung der Kriterien des Gemeinsamen
        Europäischen Referenzrahmens für Sprachen (GeRS).
      copy2-1: Der Test enthält
      copy2-2: Fragen. Jede Frage hat ein eigenes Zeitlimit.
      copy3: 'Sie werden in der folgenden Reihenfolge präsentiert:'
      list1: Frage
      list2: gesprochene Antwort
      list3: schriftliche Antwort
      list4: Minuten
      start_test: Test beginnen
      cancel: Ich komme später wieder.
  feedback:
    feedback: Feedback
    type: Feedback eingeben
    general: Allgemeines Feedback
    specific: Fragenspezifisches Feedback
    content: Ihr Feedback
    thank_you: Vielen Dank für Ihre Nachricht.
    button_message: Feedback senden
    message: Do you have any feedback?
    send: Senden
    title: Senden Sie uns Ihr Feedback
  identity:
    test:
      title: Wir müssen Ihre Identität verifizieren, bevor Sie den Test ablegen können.
      text: Sie werden auf eine sichere Website weitergeleitet, um Ihr gewähltes Identitätsdokument
        zu verifizieren. Bitte beachten Sie, dass Versuche des Betrugs den zuständigen
        Stellen gemeldet werden.
      verification: Identity Verification
      status:
        unverified: unverified
        verified: verified
      verified:
        text_1: 'Vielen Dank, dass Sie Ihre Identität verifiziert haben. '
        text_2: Sie können jetzt.
    title: 'Identitätsverifizierung '
    sentence_1: 'Bevor Sie den Test beginnen können, müssen wir kurz Ihre Identität
      überprüfen. Dafür wird sich ein neuer Tab öffnen, den Sie aber nur benötigen,
      falls es ein Problem gibt. '
    sentence_2: 'Dies wird ungefähr 5 Minuten in Anspruch nehmen. '
    button: Meine Identität verifizieren
    during_test:
      title: 'Identitätsverifizierung '
      s1: 'Gibt es ein Problem? '
      s2: 'Sie können es unten melden. Ansonsten schließen Sie den Prozess bitte im
        neuen Tab ab. '
      button: Ein Problem melden und mit dem Test fortfahren
    no_trouble:
      title: 'Vielen Dank '
      text_1: 'Vielen Dank, dass Sie Ihre Identität verifiziert haben. '
      text_2: Please continue on the new tab.
    help: |-
      <p>
      Leider ist die Überprüfung Ihrer Identität nicht erfolgreich gewesen. Gerne möchten wir Ihnen einige Tipps geben, wie Sie das Problem lösen können:
      <br><br>
      <strong>Nutzen Sie Ihr Mobiltelefon</strong><br>
      Versuchen Sie, die Identifizierung mit Ihrem Mobiltelefon abzuschließen.
      <br><br>
      <strong>Starten Sie Ihr Gerät neu</strong><br>
      Manchmal genügt es, das Gerät einfach neu zu starten.
      <br><br>
      <strong>Löschen Sie den Cache</strong><br>
      Leeren Sie den Cache Ihres Browsers, um Probleme mit veralteten Daten zu vermeiden.
      <br><br>
      <strong>Prüfen Sie Ihre Sicherheitseinstellungen</strong><br>
      Stellen Sie sicher, dass die Sicherheitseinstellungen Ihres Browsers die Identitätsprüfung nicht blockieren. Deaktivieren Sie vorübergehend Werbeblocker und hohe Sicherheitseinstellungen.<br>
      Sollten Sie weiterhin Probleme mit der Überprüfung haben, kontaktieren Sie uns <NAME_EMAIL>.
      <br><br>
      Wir helfen Ihnen gerne weiter,
      <br><br>
      Ihr Support-Team
      </p>
  identified:
    button: Weiter
  tutorial:
    question_position: Hier werden Anweisungen angezeigt.
    title: Bitte gehen Sie dieses Tutorial durch, bevor Sie den Test beginnen.
    take_tour: Take the tour
    skip: Skip this tutorial
    play_button: There is either a speaker button or a microphone button on each question.
      Click on the speaker to listen to an audio recording. Click on the microphone,
      speak and click again to record your answer. You can record multiple times but
      only the last one will be saved.
    record_button: Um Ihre gesprochenen Antworten auf Fragen aufzunehmen, klicken
      Sie auf das Mikrofon, um die Aufnahme zu starten, sprechen Sie und klicken Sie
      erneut, um die Aufnahme zu stoppen. Sie können mehrere Aufnahmen durchführen,
      doch nur die letzte wird gespeichert.
    textarea: |-
      Schreiben Sie Ihre Antwort in das Textfeld.
      Kopieren und Einfügen wurde deaktiviert, um Betrug zu verhindern.
    question_area: In diesem Bereich werden die Fragen angezeigt.
    timer: Für jede Frage gibt es einen Timer. Sobald der Timer abgelaufen ist, wird
      Ihre Antwort so wie sie ist gespeichert. Behalten Sie den Timer daher im Auge!
      Wir empfehlen, sich am Ende jeder schriftlichen Aufgabe 30 Sekunden Zeit zum
      Korrekturlesen zu lassen.
    report: 'If there is an issue with the recording (it is empty, insulting or not
      in English), you can click here to notify us. Please be careful, though: if
      the recording is correct and you have reported it, the question will be counted
      as failed.'
    next: Nachdem Sie eine Frage beantwortet haben, klicken Sie hier, um Ihre Antwort
      zu bestätigen und zur nächsten Frage zu gelangen.
    finish: Wenn Sie eine Pause einlegen möchten, klicken Sie hier und Ihre Frage
      wird gestellt, bevor Sie den Test pausieren.
    ok: Nächste/s
    welcome_title: Willkommen beim Lernprogramm!
    welcome: Befolgen Sie diese Schritte, um sich mit der Testoberfläche vertraut
      zu machen!
    start: Weiter
    progress: Hier sehen Sie, wie viele Fragen Sie bereits beantwortet haben.
    camera: Kamerazugriff autorisieren
    skip_button: Sie können die Frage überspringen. Wenn Sie sie überspringen, müssen
      wir bei der Bewertung annehmen, dass sie zu schwierig für Sie war.
    no_prev: Verwenden Sie während des Tests nicht die Zurück- oder Vorwärts-Schaltflächen
      Ihres Browsers. Es ist nicht möglich, zu einer vorherigen Frage zurückzukehren.
  tab_change:
    ok: Okay
  help:
    title: Help
    modern_browser: Pipplet uses modern web components that are currently only supported
      by the latest versions of Chrome and Firefox.
    download_advice: If you do not have such a browser please consider downloading
      one.
    audio_check: Be aware that Pipplet uses audio recording and playback. Please be
      sure that your microphone is correctly configured and that the volume of speakers
      is up.
    not_working_1: If something is not working correctly, for example you can't hear
      any sound, you couldn't be recorded on the audio test page, etc., please
    contact_us: contact us
    not_working_2: and give us your account name, the problem you encountered, and
      the browser that you used, and we'll look into it.
  notification:
    validation_errors: Ihre Antwort ist unvollständig. Geben Sie bitte eine vollständige
      Antwort ein, bevor Sie sie absenden.
  voice_recordings:
    instruction: Instruction
    details: Details
    answer: Answer
    audio_error: Dein Browser unterstützt diese Funktion nicht, bitte verwende Chrome
      oder Firefox
    missing: The recording is missing, there must have been an error during the test
  cecrl:
    title: Level's decription
    introduction: The Pipplet scores are ranging from 0 to 100. They place all test
      takers on a single scale and give you a precise information on each test taker's
      relative position. They are correlated with the Common European Framework of
      Reference for Languages levels below, which in turn give you insights on the
      test taker practical abilities.
    level_group_name:
      column_title: Level group name
      a: Basic User
      b: Independent User
      c: Proficient User
    level:
      column_title: Level
    level_name:
      column_title: Level name
      a1-: Beginner
      a1: Beginner
      a1+: Beginner
      a2-: Elementary
      a2: Elementary
      a2+: Elementary
      b1-: Intermediate
      b1: Intermediate
      b1+: Intermediate
      b2-: Upper Intermediate
      b2: Upper Intermediate
      b2+: Upper Intermediate
      c1-: Advanced
      c1: Advanced
      c1+: Advanced
      c2-: Mastery
      c2: Mastery
      c2+: Mastery
      ai_binary_fail: Fail
      ai_binary_pass: Pass
      ai_binary_grade_pass: Work Ready
      ai_binary_grade_fail: Not Work Ready
    description:
      column_title: Description
      no_answer: "%{user_first_name} was not able to answer."
      a1: |-
        Can understand and use familiar everyday expressions and very basic phrases aimed at the satisfaction of needs of a concrete type.
        Can introduce him/herself and others and can ask and answer questions about personal details such as where he/she lives, people he/she knows and things he/she has.
        Can interact in a simple way provided the other person talks slowly and clearly and is prepared to help.
      a2: |-
        Can understand sentences and frequently used expressions related to areas of most immediate relevance (e.g. very basic personal and family information, shopping, local geography, employment).
        Can communicate in simple and routine tasks requiring a simple and direct exchange of information on familiar and routine matters.
        Can describe in simple terms aspects of his/her background, immediate environment and matters in areas of immediate need.
      b1: |-
        Can understand the main points of clear standard input on familiar matters regularly encountered in work, school, leisure, etc.
        Can deal with most situations likely to arise while travelling in an area where the language is spoken.
        Can produce simple connected text on topics that are familiar or of personal interest.
        Can describe experiences and events, dreams, hopes and ambitions and briefly give reasons and explanations for opinions and plans.
      b2: |-
        Can understand the main ideas of complex text on both concrete and abstract topics, including technical discussions in his/her field of specialisation.
        Can interact with a degree of fluency and spontaneity that makes regular interaction with native speakers quite possible without strain for either party.
        Can produce clear, detailed text on a wide range of subjects and explain a viewpoint on a topical issue giving the advantages and disadvantages of various options.
      c1: |-
        Can understand a wide range of demanding, longer texts, and recognise implicit meaning.
        Can express ideas fluently and spontaneously without much obvious searching for expressions.
        Can use language flexibly and effectively for social, academic and professional purposes.
        Can produce clear, well-structured, detailed text on complex subjects, showing controlled use of organisational patterns, connectors and cohesive devices.
      c2: |-
        Can understand with ease virtually everything heard or read.
        Can summarise information from different spoken and written sources, reconstructing arguments and accounts in a coherent presentation.
        Can express him/herself spontaneously, very fluently and precisely, differentiating finer shades of meaning even in the most complex situations.
  certificate:
    filename: Assessment report of %{full_name} in %{lang} - %{date}
    title: Your assessment order results
    date: 'Date:'
    head: Professional Language Proficiency Assessment
    head-flex: Pipplet FLEX Pro Language Certification
    head-flex-detailed: Pipplet FLEX Pro+ Language Certification
    authenticity: Verify the authenticity of this certificate with a simple request
      to <NAME_EMAIL>. Verification code %{uuid}
    copyright: Copyright © 2023 by Pipplet. All rights reserved. Unauthorized use
      or reproduction is strictly prohibited. Pipplet is a registered trademark of
      Pipplet.<br>ETS and the ETS Logo are registered trademarks of ETS, used under
      license in France by Pipplet.
    name: 'Name of the candidate: '
    language: 'Language:'
    registration_number: 'Registration number:'
    description:
      title: What can you expect at this level?
    synthesis:
      simple: Language Proficiency Levels
      detailed: Overall Skills
      flex: Overall Skills
      academic:
        score: 'Language Fundamentals:'
      spoken:
        score: 'Speaking & Listening:'
        abstract: 'Speaking & Listening:'
      written:
        score: 'Writing & Reading: '
        abstract: 'Reading & Writing abilities :'
      how_pipplet_ai_work:
        description_1: "\nTalent AI is a powerful automated evaluation tool, combining
          machine learning technology with our expertise in language assessment with
          human examiners. Talent AI provides you with a comprehensive report that
          includes a predicted CEFR grade. Talent AI analyzes the test taker's spoken
          and written productions according to the CEFR framework and provides fast
          and objective assessments, allowing you to make informed decisions quickly
          and efficiently. "
    detail:
      name: Detailed Skills
      pronunciation:
        score: 'Phonological Control :'
      spokenFluency:
        score: 'Spoken Fluency:'
      grammar:
        score: 'Grammar:'
      vocabulary:
        score: 'Vocabulary:'
      coherence:
        score: 'Coherence & Cohesion:'
    written:
      title:
        score: 'Level of Written Communication:'
      description:
        score: What does this grade mean?
    spoken:
      title:
        score: 'Level of Spoken Communication:'
    strengh:
      name: "%{user_name}'s Strengths"
      title: 'did particularly well with:'
    recommendations:
      name: Recommendations from Pipplet's Experts
      title: 'could improve his/her communication in %{language} by:'
    report: Download report
    level: 'Level '
    score:
      title: 'Score: '
      copy1: 'This score corresponds to the '
      copy2: " level on the European CEFR Scale"
      copy3: ". You will find below the description of the skills of such a candidate."
    na:
      title: No certificate available
      copy1: 'We do not have any assessment with this reference. Please '
      copy2: contact us
      copy3: " for more information."
    recommandation:
      _01: improving his/her pronunciation
      _02: improving his/her grammar skills
      _03: improving his/her spelling
      _04: improving his/her use of verb tenses
      _05: trying to master more connective words to link sentences
      _06: speaking more slowly so the need for pause is reduced
      _07: reading in the language to improve his/her comprehension
      _08: listening content in the language to improve his/her listening skills
      _09: improving his/her writing skills
      _10: improving his/her speaking skills
      _11: reading or listening content in the language to learn more vocabulary
      _12: writing shorter sentences
      _13: improving his/her use of language registers
      _14: using more idiomatic expressions
      _15: everything was perfect!
      _16: improving his/her punctuation
      _17: improving his/her grammar fundamentals
      _18: working on using more complex grammar
      _19: avoiding overly complicated sentences that may lead to grammar mistakes
      _20: working on using the right prepositions
      _21: working on his/her subject-verb agreements
      _22: working on using the right articles
      _23: working on his/her word ordering
      _24: working on his/her use of infinitive verb forms
      _25: working on his/her subject-complement agreements
      _27: working on his/her use of conjunctions
      _28: working on using the right possessive pronouns
      _29: proofreading
      _30: working on his/her use of contractions
      _32: improving his/her use of complex verb tenses
      _33: practicing conversation to get used to speaking spontaneously
      _34: practising on where the stress should be in each word
      _35: improving on long vowel sounds
      _36: avoiding adding schwa sounds
      _37: improving on diphthong pronunciation
      _38: improving on "th" sounds
      _39: improving on using reflexive pronouns
      _40: improving on using quantifiers
      _41: improving on completing a secondary clause
      _42: improving on forming questions
      _43: improving on using determiners
      _44: improving on using conditional clauses
      _45: improving on using irregular nouns
      _46: improving on using irregular plurals
      _47: improving on using verb phrases
      _50: avoiding using vocabulary from another language
      _51: using idiomatic expressions correctly
      _52: "%{user_first_name} could not answer the questions."
      _101: Spelling
      _102: Language registers
      _103: Industry-specific vocabulary
      _104: Clarity of response
      _105: Positive attitude
      _106: Nothing
    strenght:
      _01: pronunciation and intonation
      _05: speaking with a natural rhythm
      _14: grammar fundamentals
      _15: complex grammar
      _04: control over tenses
      _03: spelling
      _07: vocabulary
      _08: using connective words and transitional phrases to link sentences
      _09: language registers
      _10: using idiomatic expressions
      _12: reading comprehension
      _13: listening comprehension
      _16: "%{user_first_name} is a complete beginner."
      _11: consistency
      _06: writing
      _02: grammatical control
      _101: Spelling
      _102: Language registers
      _103: Industry-specific vocabulary
      _104: Clarity of response
      _105: Positive attitude
      _106: Nothing
    partial: Partial
    cheating:
      default:
        title: Test Security Violation
        body: This test does not meet our test security requirements and so has been
          invalidated.
      cheating-facematch-failed:
        title: Impersonation Detected
        body: The identity verification photo and the photos taken during the test
          display significant differences.
  date:
    formats:
      default: "%B %-d, %Y"
  invalid_test:
    body_1: 'We do not have a test with this reference. '
    body_2: Please contact the person who provided you with this reference for more
      information.
  demo_certificate:
    subject: Thank you for taking the Pipplet demo test!
    thankyou: You did it! You just completed a demo of the Pipplet Test. We hope that
      you enjoyed it as much as our many happy customers.
    body_1: The goal of this demo was to help you get acclimated to the Pipplet interface
      while helping you understand how simple, yet effective our tests are in evaluating
      your potential candidates' language competencies.
    body_2: Our examiners won't be evaluating your productions for this test. We have
      however attached a sample certificate to this email to show you how you may
      receive candidates' test results.
    body_2_ai: Your productions will not be evaluated by our AI model. We have however
      attached a sample evaluation report to this email to show you how your candidates’
      results will be presented to you.
    body_3: NA
    forwardit: What next?
    contactus_1: '&nbsp; Are you a professional looking for language assessments for
      your company?<br/><br/>&nbsp; Contact us <a href="mailto:<EMAIL>">here</a>,
      or <a href="https://calendly.com/caubin">book a meeting with us</a>.<br/><br/>'''
    contactus_2: '&nbsp; Would you like to know more about how we assess skills? Find
      out more <a href="https://www.pipplet.com/features/quality-control">here</a>.'
    filename: Pipplet_Report_Sample
    simple_report_sample_certificate: simple_report_sample_certificate
    detailed_sample_certificate: detailed_sample_certificate
    written_sample_certificate: written_sample_certificate
    ai_sample_certificate: ai_sample_certificate
    goodbye: Best,
    signature: The Pipplet Team
  direct:
    title_1: Test
    title_training: Übungstest beginnen
    invalid_test_type: The requested test does not exist
    select_language: 'Bitte wählen Sie die Testsprache(n) aus:'
    go_languages_button: Test
    title_2: Bereit?
    enter_name: Bitte geben Sie Ihren Namen ein, um den Test zu beginnen?
    start_tests: Test(s) beginnen
  survey:
    title: Lassen Sie uns wissen, was Sie denken.
    intro: Wir möchten dir gern 5 Fragen stellen, um Pipplet zu verbessern.
    q1:
      title: 'War der Test Ihrer Meinung nach:'
      enjoyable: Angenehm
      annoying: Nervig
      quick: Schnell
      slow: Langsam
    q2:
      title: Hat Ihnen der Test Spaß gemacht?
      lowest: Es war furchtbar
      highest: Es war fantastisch
    q3:
      title: Wie relevant waren Ihrer Meinung nach die Fragen, um Ihr tatsächliches
        Sprachniveau herauszufinden?
      lowest: Überhaupt nicht relevant
      highest: Absolut relevant
    q4:
      title: 'Falls Sie weitere Sprachtests gemacht haben, ist Pipplet Ihrer Meinung
        nach:'
      much_better: Viel besser
      better: Besser
      worse: Schlechter
      much_worse: Viel schlechter
    q6:
      title: Würden Sie etwas verbessern wollen? Wählen Sie eine Kategorie aus und
        sagen Sie uns, was.
      choose: Wählen Sie eine Kategorie
      surprise: Ich war vom Testformat überrascht und fühlte mich nicht ausreichend
        vorbereitet
      enoughtime: Ich hatte nicht genug Zeit
      stressful: Der Test ist zu stressig
      toodifficult: Der Test ist zu schwer
      dontlike: Ich mochte die Fragen nicht
      unclear: Die Anweisungen waren unklar
      notallskills: Es wurden nicht alle meine Fähigkeiten getestet
      noreflect: Meine Antworten haben meine tatsächlichen Fähigkeiten nicht widergespiegelt
      unappropriate: Der Test passt nicht zu meinem Sprachniveau
      notadapted: Der Test hat nichts mit meiner Arbeit zu tun
      specific: Ich hatte ein sprachspezifisches Problem (Tastatur, Alphabet, Schwierigkeit)
      newfeature: Ich möchte ein neues/anderes Element vorschlagen
      mistake: Ich habe einen Fehler gefunden
      teki: Ich hatte ein technisches Problem
    q7:
      title: Welche anderen Sprachtests haben Sie schon abgelegt?
    q5:
      title: Möchten Sie uns ein paar Details geben? Nur zu!
    thankyou: Vielen Dank für Ihr Feedback!
    submit_intro: "Vielen Dank für Ihren Kommentar, er hilft uns bei der Verbesserung
      der Pipplet-Nutzererfahrung. Dürfen wir Ihr Feedback teilen, um für Pipplet
      zu werben? \n<br>\n<br>\nEs könnte so aussehen:"
    submit_with_share: Ja, mein Feedback darf öffentlich geteilt werden!
    submit_no_share: Nein, bitte nicht öffentlich teilen.
    submit: Abschicken
    thankyou_shareyes: Vielen Dank, wir werden Sie auf dem Laufenden halten, wenn
      wir Ihr Feedback umsetzen!
    thankyou_shareno: Vielen Dank für Ihren Hinweis, wir werden ihn nicht veröffentlichen!
  buy:
    choose_language: Vielen Dank für deinen Kommentar, wir werden ihn nicht öffentlich
      verwenden!
    total_exvat: 'Total (Ex VAT):'
    total_incvat: 'Total (Inc VAT):'
    vat: 'VAT :'
    currency: "€"
    infos_title: Account Information
    order_description: Pipplet Test
    go-button: Start your test
    order_by_card: Pay €66
    success_top: |-
      <p>Thank you for your order!</p>
      <p>You can start your test immediatly.</p>
    success_bottom: We have also emailed you a connection link to your test so you
      can take it later if needed.
    missing_terms: Please accept the terms and conditions
    missing_elements: Please fill all information
    accepts_terms: I accept the <a href="/pages/terms-of-sales" target="_blank">terms
      and conditions</a>.
    title: Wilkommen bei Pipplet!
    introduction: Demonstrate your language skills by taking our language certification
      test!  <br/> During the test you will be presented with scenario-based questions
      and prompts, all in a business-oriented context. Our certified assessors will
      evaluate your skills and you will then receive your results and certificate
      within twenty-four hours. The certificate is a great addition to your CV or
      professional portfolio. <br/></br/> Any questions? <a href="https://help.pipplet.com/en-us/">Take
      a look at our Helpdesk!</a>
  token:
    introduction: Please enter the token that has been given to you in order to start
      your test. If you don't know your token please contact your test administrator.
    submit: Test machen
    placeholder: 'Token hier eingeben (z. B.: Xxxxx)'
    error: Dieses Token existiert nicht. Bitte versuchen Sie es noch einmal.
  update_success: Updated successfuly
  update_fail: Update failed
  evaluation:
    link_form: Assessment form
  production:
    result: Answer provided by the candidate
    question: Question asked
  test:
    bad_connection:
      title: Ihre Netzwerkverbindung ist zu langsam
      body: Ein Netzwerkfehler ist aufgetreten. Sie werden in Kürze von unserem Support
        Team kontaktiert.
      cancel: Okay
    no_connection:
      title: Fehler beim Herstellen der Verbindung
      body: Ein Netzwerkfehler ist aufgetreten. Bitte kontaktieren Sie uns per E-Mail
        unter  <a href="mailto:<EMAIL>"><EMAIL></a> für weitere
        Informationen.
      cancel: Okay
    intro: 'Folgende Tests wurden Ihnen zugewiesen:'
    test_mode: Stichprobentest, dieser wird nicht bewertet.
    completed: Dieser Test wurde abgeschlossen
    cancelled: Dieser Test wurde von %{clientname} annulliert
    flex: FLEX Certification
    button: Test machen
    finished_title: Sie haben Ihren %{lang} Test abgeschlossen
    cancelled_title: Ihr %{lang} Eignungstest wurde abgebrochen
    finished_desc: Sie haben Ihren %{lang} Spracheignungstest abgeschlossen. Vielen
      Dank. Sie können diesen Tab jetzt schließen.
    cancelled_desc: Ihr %{lang} Eignungstest wurde abgebrochen. Bitte kontaktieren
      Sie Ihren Test-Administrator, um weitere Informationen zu erhalten.
    tips:
      succeed:
        plan: Planen Sie die mündlichen Antworten, bevor Sie sie aufnehmen. Das hilft
          Ihnen dabei, gut strukturierte Antworten zu geben und flüssig zu sprechen.
        detail: Führen Sie Ihre Antworten so detailliert wie möglich aus. Je ausführlicher
          Ihre Antworten sind, desto eher erreichen Sie ein höheres Niveau.
        structure: Strukturieren Sie Ihre Antworten gut. Der logische Zusammenhang
          Ihrer Antwort wird als Teil der Bewertung berücksichtigt.
        re_read: Überprüfen Sie jede Antwort gründlich, bevor Sie mit der nächsten
          fortfahren. Denken Sie daran, dass jede Frage zeitlich limitiert ist. Wir
          empfehlen, die letzten 30 Sekunden zum Korrekturlesen aufzusparen.
        title: wichtige Tipps für den Erfolg
      security_guidelines:
        title: Richtlinien zur Testsicherheit
        description: |-
          Bitte befolgen Sie diese Richtlinien, um die Gültigkeit des Tests sicherzustellen.<br>
          <strong>Ein Nichteinhalten kann dazu führen, dass Ihr Test markiert oder für ungültig erklärt wird.</strong>
        block_1:
          title: Privates Testumfeld
          description: Die Anwesenheit oder Einmischung anderer Personen ist nicht
            gestattet.
        block_2:
          title: Im Testfenster bleiben
          description: Bleiben Sie im Testfenster. Wechseln Sie nicht zwischen Tabs
            und Apps.
        block_3:
          title: Eigene Arbeiten einreichen
          description: Die Verwendung vorgefertigter Antworten, externer Inhalte oder
            Hilfe durch KI-Tools ist nicht gestattet.
        block_4:
          title: Nur genehmigte Tools
          description: Browser-Erweiterungen, die Text erstellen, korrigieren oder
            übersetzen, sind nicht zulässig.
    extensions_check:
      title: 'Überprüfung von Browser-Erweiterungen

        '
      disclamer: Die Verwendung von Browser-Erweiterungen, die Ihren Text verändern
        könnten, wie Rechtschreibprüfungen, Übersetzungstools oder andere KI-Tools,
        sind nicht gestattet.
      detected: Wir haben eine Installation verbotener Erweiterungen in Ihrem Browser
        festgestellt.
      disable: Wir bitten Sie, diese zu deaktivieren, da diese Verwendung zur Ungültigkeit
        Ihrer Testergebnisse führen könnte.
  user:
    continue: Weiter
  btn:
    assessment_exemple: Assessment exemple
  skip_question:
    button: Ist diese Frage zu schwer?
    modal:
      title: 'Möchten Sie diese Frage überspringen?       '
      copy: 'Wenn Sie diese Frage überspringen, müssen wir bei der Bewertung annehmen,
        dass sie zu schwierig für Sie war.

        '
      cancel: Frage überspringen
      confirm: Versuchen Sie es erneut
  skip_short_question:
    modal:
      title: Die Antwort ist zu kurz
      copy: Deine Antwort ist zu kurz, was sich auf deine Endnote auswirken könnte.
        Möchtest du sie ändern oder so abschicken?
      cancel: Abschicken und weitermachen
      confirm: Ändern
  test_profile:
    '74':
      description: Standard recruitment test
    '17':
      description: Standard recruitment test
    '65':
      description: Recruitment test for managers
    '82':
      description: Recruitment test for managers
    '93':
      description: Recruitment test for managers
    '66':
      description: Recruitment test for customer service agents
    '85':
      description: Standard recruitment test
    '91':
      description: Recruitment test for technical support agents
    '90':
      description: Recruitment test for technical support agents
    '89':
      description: Recruitment test for technical support agents
    '88':
      description: Recruitment test for technical support agents
    '99':
      description: Recruitment test for technical support agents
    '100':
      description: Recruitment test for technical support agents
    '72':
      description: Standard end of training test
    '79':
      description: Standard start of training test
    '84':
      description: Standard end of training test
    '97':
      description: Training test for beginners
    '92':
      description: Standard recruitment test
    '95':
      description: Standard recruitment test
    '94':
      description: Japanese Romaji test for Civil aviation personnel
    '96':
      description: Audit test for airport staff
  talentsoft:
    language_test_name: "%{testlanguage} %{testname} Language Test"
  test-card:
    from: für
  teamtailor:
    webhook_config:
      test_profile:
        label: Profile
        placeholder: Select profile
        select_label: Profiles
      type:
        label: Test language
        placeholder: Select test langugage
        select_label: Languages
    partner_results:
      summary:
        completed: Test completed!
        completed_no_grade: test completed but no grade was assigned
        sending: Waiting for the candidate to take the test
      attachments:
        description:
          test_url: test link
          pdf_report_url: 'PDF report '
    activation_data:
      infobox:
        content:
          link: Click
          text: to get in touch with the sales team
      email:
        label: Your email for billing
      first_name:
        label: Your first name
      last_name:
        label: Your last name
    errors:
      invalid_partner_config: Invalid Partner configuration
      invalid_partner_config_with_message: 'Partner configuration error : %{message}'
      invalid_request: Invalid Request
      test_not_found: Test not found
      partner_event_not_found: Partner event not found
  mailjet:
    languages:
      zhyue:
        non_latin_characters_warning: Please respond to the test using traditional
          Chinese characters. Responses using simplified Chinese characters will not
          be taken into account during evaluation and will result in a null score
          for the written section.
  lever:
    errors:
      api_user_not_found: Api User not found
      invalid_webhook_signature: Invalid webhook signature
      invalid_request: Invalid request
      invalid_order: Invalid order
      refresh_token: 'Impossible to refresh token for api_user : %{api_user_name}'
      expired_token: 'Expired token for api_user : %{api_user_name}, a new oauth connection
        is needed'
      save_token: 'Error saving the Token & refresh token created for api_user : %{api_user_name}'
      request_unsuccessful: 'Request was unsuccessful for api_user : %{api_user_name},
        with code: %{error_code}'
      get_opportunity_reasons: 'Error getting opportunity reasons for api_user : %{api_user_name}'
      get_opportunity: 'Error getting opportunity for api_user : %{api_user_name}'
      create_opportunity: 'Error creating opportunity for api_user : %{api_user_name}'
      get_user: 'Error getting the admin user for api_user : %{api_user_name} with
        email: %{admin_email}'
      no_admin_email: No admin email provided
      create_opportunity_note: 'Error creating an opportunity note for api_user :
        %{api_user_name}'
      order_rejected: 'Order rejected, New order : %{api_order_id} from %{api_user_name}
        has been rejected'
    tokens_created: 'Token & refresh token created for api_user: %{api_user_name}'
    token_refreshed: 'Token refreshed : old refresh token : ''%{old_refresh_token}'',
      new refresh token : ''%{new_refresh_token}'''
    order_accepted: 'Order accepted, New order : %{api_order_id} from %{api_user_name}
      has been accepted'
    code_added: 'Code added : %{code}'
    opportunity_note:
      sending: 'Pipplet : Test pending'
      completed_no_grade: 'Pipplet : Test completed (no grade)'
      completed: 'Pipplet : Test completed'
      test_url: 'Test url '
      overall_score_rating: Score rating
      overall_score_cecrl: Score cecrl
      overall_score_date: Score date
      minutes_duration: "%{duration} Minutes"
      error_message: 'Pipplet : Error while creating test : %{error}'
      oauth_login_required: 'Pipplet : Integration disconnected - Please contact support'
    config_opportunity:
      name: 'Pipplet : Candidate for tags (do not delete!)'
      tag_prefix: Pipplet
  reminders:
    sms: 'Pipplet: %{client_name} from %{client_company} would like you to take a
      language test in %{ti_language}. Please check your e-mails for the test invitation.'
  onboarding:
    problem: Es scheint ein Problem zu geben.
    instructions-title:
      concentration: Konzentration
      audio: Audio Qualität
      keyboard: Tastatur
      speak: Sprechen Sie deutlich
    tips:
      plan: Plan
      elaborate: Durchdacht
      structure: Struktur
      review: Review
    test_writting_speaking: Mündlicher und schriftlicher Test
    test_writting: Schriftlicher Test
    test_speaking: Mündlicher Test
  accepts_marketing_contacts: Ich bin damit einverstanden, von Pipplet oder der ETS-Gruppe
    kontaktiert zu werden, um Empfehlungen zu meinen Sprachkenntnissen zu erhalten.
  practice_pack:
    boost:
      title: Erhöhen Sie Ihre Chancen mit unserem Practice Pack!
      description: Möchten Sie Ihr bestmögliches Ergebnis erzielen?<br>Erfahren Sie
        mehr über das <strong>Pipplet-Practice Pack</strong> und schalten Sie <strong>3
        Übungstests frei</strong>.
      cta: Erfahren Sie mehr über Practice Pack
  grants_ai_consent: Ich stimme der Übertragung meiner Testantworten an ein KI-Tool
    (Künstliche Intelligenz) zu, um meine mündlichen und schriftlichen Sprachfähigkeiten
    zu bewerten. Ich verstehe, dass die KI zur Bewertung meiner Sprachkompetenz und
    zur Rückmeldung über meine Leistung verwendet wird. Ich verstehe, dass die KI
    in Übereinstimmung mit der Europäischen Datenschutz-Grundverordnung (DSGVO) und
    allen anwendbaren Datenschutzgesetzen verwendet wird.
  terms_of_use_privacy_policy_message: Durch Klicken auf Absenden bestätigst du, dass
    du die <a href="https://www.pipplet.com/terms-and-conditions" target="_blank">Allgemeinen
    Geschäftsbedingungen</a> und die <a href="https://www.pipplet.com/en-gb/Privacy-policy"
    target="_blank">Datenschutzrichtlinie</a> von Pipplet gelesen und akzeptiert hast.
  test_instances:
    tags:
      categories:
        security_violation: Test Security Violation
        unusual_behaviour: Unusual Behavior
      facematch-failed: The identity verification photo and the photos taken during
        the test display significant differences.
      namematch-failed: The name on the ID card does not match the one used to invite
        the test taker.
      cheating-multi-voices: Multiple voices can be heard on the audio recordings.
      cheating-plagiarism: Some test content has been copied from another source.
      spoken-written-difference: There is an unusual difference between the spoken
        and written skills.
      ai-content: Some test content may have been generated using an AI tool.
      inconsistent-level: The level of the responses is not consistent throughout
        the test.
  secure_browser:
    title: Take Your Test in the Secure Browser
    subtitle: To ensure the integrity of the language test, you must take it using
      the <strong>Secure Browser</strong>.
    step_1: |-
      <strong>Step 1: Install Secure Browser</strong>
      <br>
      If you haven’t already installed it, please download it using the link below:
    step_2: |-
      <strong>Step 2: Open the Test in Secure Browser</strong>
      <br>
      Once installed, click the link below to launch the <strong>Secure Browser</strong>.
    required: Please use a Secure Browser compatible computer (Mac or Windows)
    download_macos: Download for MacOS
    download_windows: Download for Windows
    launch_application: Launch the Secure Browser
