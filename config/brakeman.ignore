{"ignored_warnings": [{"warning_type": "Dangerous Eval", "warning_code": 13, "fingerprint": "1d1a2563d5c1fc9f6a657ba4cb7fd9af1a7d05c53f7b8a009fc9c2a7fff96b42", "check_name": "Evaluation", "message": "User input in eval", "file": "app/controllers/admin/question_elements_controller.rb", "line": 26, "link": "https://brakemanscanner.org/docs/warning_types/dangerous_eval/", "code": "eval(params[:content])", "render_path": null, "location": {"type": "method", "class": "Admin::QuestionElementsController", "method": "update"}, "user_input": "params[:content]", "confidence": "High", "note": "We should change this into a JSON object instead of a Hash object"}], "updated": "2022-07-18 13:37:58 +0000", "brakeman_version": "5.2.3"}