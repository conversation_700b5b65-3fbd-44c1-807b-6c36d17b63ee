module Workable::Api::Core
  def self.execute(method:, path:, api_user:, payload: {}, headers: {})
    subdomain                       = api_user&.api_oauth_manager&.login_name
    base_url                        = format(Rails.application.config.workable[:subdomain_url_template], subdomain: subdomain)
    url                             = "#{base_url}/#{path}"
    headers['Authorization']        = "Bearer #{Rails.application.config.workable[:partner_token]}"
    headers['X-WORKABLE-CLIENT-ID'] = Rails.application.config.workable[:client_id]

    RestClient::Request.execute(method: method,
                                url: url,
                                payload: payload,
                                headers: headers,
                                content_type: 'application/x-www-form-urlencoded')
  end

  module Assessments
    def self.update(api_order)
      payload = Integrations::Workable::TestInstanceSerializer.new(api_order.test_instance).as_json
      Workable::Api::Core.execute(method: :put, path: "assessments/#{api_order.order_information['assessment_id']}", payload: payload, api_user: api_order.api_user)
    end
  end
end
