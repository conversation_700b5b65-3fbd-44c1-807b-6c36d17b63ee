# testing in console:
# $ rails c
# > PippletClientsApi.get_campaign_details(1234)
# > PippletClientsApi.create_user(TestInstance.last.id, 2243)

Netrc.configure do |config|
  config[:allow_permissive_netrc_file] = true
end

module Pi<PERSON>let<PERSON>lients<PERSON><PERSON>
  def self.credentials
    key = Rails.env.to_sym
    PIPPLET_CLIENTS.detect { |pc| pc.name == key }
  end

  def self.api_url
    PippletClientsApi.credentials.url
  end

  def self.api_token
    PippletClientsApi.credentials.token
  end

  def self.headers
    {
      'Authorization' => "Token token=#{PippletClientsApi.api_token}",
      'Content-Type' => 'application/json',
      'Accept' => 'application/json'
    }
  end

  # Pipplet clients user creation
  # -----------------------------
  # Purpose: When a user is created on p-test
  #
  # Called by
  # - [asynchronous] CreatePippletClientsUserJob, triggered by an after_create callback on users
  #
  # Request
  # - URI: POST /api/v1/users
  # - params:
  #     * user hash: uuid, email, first_name, last_name, phone_number
  #     * p-clients campaign_id
  #
  #
  # Expected remote behaviour
  # - Create a user in p-clients, with the same uuid
  # - Add this user to the campaign_id provided in params
  # - Automatically generate the group of a user
  #
  # Response
  # - [200] if creation is a success: the group of the user
  # - [422] if creation has errors: the group of the user
  #
  def self.create_user(test_instance_id, campaign_id = nil)
    test_instance = TestInstance.find_by(id: test_instance_id)
    return if test_instance.nil?

    user = test_instance.user

    begin
      cpf_number = test_instance.api_order.order_information.dig('params', 'test_instance', 'cpf_id')
    rescue StandardError
      cpf_number = nil
    end

    request_hash = {
      user: {
        pipplet_uuid: user.uuid,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        campaign_id: campaign_id,
        account_id: test_instance.client_id,
        language: test_instance.test_language,
        test_profile_id: test_instance.test_profile_id,
        registration_url: user.account_registration_url,
        test_instance_uuid: test_instance.uuid,
        test_instance_id: test_instance.id,
        cpf_number: cpf_number,
        remote_client_rule_name: test_instance&.api_user&.remote_clients&.enabled&.first&.rule_name
      }
    }

    begin
      response = HTTParty.post(
        URI("#{PippletClientsApi.api_url}/api/v1/users"),
        headers: self.headers,
        body: request_hash.to_json
      )
    rescue Timeout::Error => e
      Alert.api('[PippletClientsApi::create_user] Timeout while creating user', { errors: e.message, request_hash: }.to_s, handled: true)
      return nil
    end

    # We must update the user group, as returned by the API
    if response && response.code == 200 && !response.body.nil?
      json_response = JSON.parse(response.body)

      if json_response && json_response['user']
        # Update TI api_order
        if (campaign = json_response.dig('user', 'campaign')).present? && (api_order = test_instance.api_order).present?
          params = api_order.order_information&.dig('params') || {}
          params['pipplet_clients_campaign'] = { id: campaign['id'] }
          params['pipplet_clients_service'] = { service_name: campaign.dig('service', 'name'), service_id: campaign.dig('service', 'id'), service_is_cpf: 'false', handled: true }
          params['pipplet_clients_sub_service'] = { sub_service_name: campaign.dig('sub_service', 'name'), sub_service_id: campaign.dig('sub_service', 'id') }
          api_order.order_information['params'] = params
          api_order.save
        end
        # Update user group!
        if (group = json_response.dig('user', 'group')).present?
          user.update_group_from_api(group)
          return user
        else
          Alert.api('[PippletClientsApi::create_user] Something went wrong and we couldn\'t update the group of the user created on Pipplet-clients', response.body, handled: true)
        end
      else
        Alert.api('[PippletClientsApi::create_user] Something went wrong and we couldn\'t get the user created on Pipplet-clients', response.body, handled: true)
      end
    else
      Alert.api('[PippletClientsApi::create_user] User creation failed on p-clients', response, handled: true)
    end

    nil
  rescue RestClient::ExceptionWithResponse => e
    error_details = if e.response.headers[:content_type] =~ /html/
                      { errors: e.response.body, request_hash: request_hash }
                    else
                      { errors: JSON.parse(e.response.body), request_hash: request_hash }
                    end

    # Trigger Alert
    Alert.api('[PippletClientsApi::create_user] Error while creating user', error_details.to_s, handled: true)
    nil
  end

  def self.show_user(uuids)
    # TODO: Move to HTTParty
    response = RestClient::Request.execute(verify_ssl: false, method: :post, url: "#{PippletClientsApi.api_url}/api/v1/users/list_by_uuids", payload: { user: { uuids: uuids } }, headers: { authorization: "Token token=#{PippletClientsApi.api_token}", accept: :json, content_type: 'application/json' })
    JSON.parse(response.body)['content']
  end

  # Update API User remote p-clients data
  # -----------------------------
  # Purpose: For a given (campaign_id), get the details of p-clients data based on the campaign
  #
  # Called by
  # - [synchronous] Triggered by a click on in the admin interface). This call in Admin::ApiUser#show, call a method in the ApiUser model which triggers the APICall
  # - [asynchronous] TestInstance.gdrive_export_to_worksheet, from DataExportToGoogleDriveJob
  #
  # Request
  # - URI: GET /api/v1/campaigns/:campaign_id
  # - params:
  #     * p-clients campaign_id (in URL)
  #
  #
  # Expected remote behaviour
  # - Serialize campaign information in JSON
  #
  # Response
  # - [200] if campaign is found: all serialised campaign info
  # - [404] if campaign is not found: an 404 error.
  #
  def self.get_campaign_details(campaign_id)
    return if campaign_id.nil?

    url    = "#{PippletClientsApi.api_url}/api/v1/campaigns/#{campaign_id}"
    params = { authorization: "Token token=#{PippletClientsApi.api_token}", accept: :json, content_type: 'application/json' }

    # API call
    # TODO: Move to HTTParty
    response = RestClient.get(url, params)

    if response && response.code == 200 && response.body
      json_response = JSON.parse(response.body)

      if json_response['campaign']
        return json_response['campaign']
      else
        msg = '[PippletClientsApi::get_campaign_details] no response, or invalid response'
        Alert.api(msg, response.body)
      end

    else
      msg = '[PippletClientsApi::get_campaign_details] no response, or invalid response'
      Alert.api(msg, response)
    end

    nil
  rescue RestClient::ExceptionWithResponse => e
    error_details = if e.response.headers[:content_type] =~ /html/
                      { errors: e.response.body, url: url, params: params }
                    else
                      { errors: JSON.parse(e.response.body), url: url, params: params }
                    end

    # Trigger Alert
    Alert.api("[PippletClientsApi::get_campaign_details] Error while syncing campaign info for #{campaign_id}",
              error_details.to_s,
              skip_appsignal: true)
    nil
  end

  def self.request_user_sync(test_instance_uuid:)
    # Error is rescued as the GetUpdatedTestInstancesJob will run within 10 minutes to sync
    HTTParty.post(
      URI("#{PippletClientsApi.api_url}/api/v1/users/#{test_instance_uuid}/sync"),
      headers: {
        authorization: "Bearer #{PippletClientsApi.api_token}",
        accept: 'json',
        content_type: 'application/json'
      }
    )
  rescue Timeout::Error => e
    Alert.system("Certificate could not be synchronized for TestInstance 'uuid'=#{test_instance_uuid}", e.message)
  end

  def self.update_test_profile(test_profile_id:, attributes: {})
    HTTParty.put(
      URI("#{PippletClientsApi.api_url}/api/v1/test_profiles/#{test_profile_id}"),
      body: attributes.to_json,
      headers: {
        'Content-Type' => 'application/json',
        'Authorization' => "Bearer #{PippletClientsApi.api_token}"
      }
    )
  end

  def self.update_user(test_instance_uuid:, attributes: {})
    HTTParty.patch(
      URI("#{PippletClientsApi.api_url}/api/v1/users/#{test_instance_uuid}"),
      body: attributes,
      headers: {
        authorization: "Bearer #{PippletClientsApi.api_token}",
        accept: 'json',
        content_type: 'application/json'
      }
    )
  end

  def self.account(account_id)
    HTTParty.get(
      URI("#{PippletClientsApi.api_url}/api/v1/accounts/#{account_id}"),
      headers: {
        authorization: "Bearer #{PippletClientsApi.api_token}",
        accept: 'json',
        content_type: 'application/json'
      }
    )
  end

  def self.services(account_id, test_profile_ids: nil)
    query = test_profile_ids ? { test_profile_ids: } : {}
    HTTParty.get(
      URI("#{PippletClientsApi.api_url}/api/v1/accounts/#{account_id}/services"),
      query:,
      headers: {
        authorization: "Bearer #{PippletClientsApi.api_token}",
        accept: 'json',
        content_type: 'application/json'
      }
    )
  end

  # TODO : replace get_campaign_details calls by this & remove get_campaign_details
  def self.campaign(campaign_id)
    HTTParty.get(
      URI("#{PippletClientsApi.api_url}/api/v1/campaigns/#{campaign_id}"),
      headers: {
        authorization: "Bearer #{PippletClientsApi.api_token}",
        accept: 'json',
        content_type: 'application/json'
      }
    )
  end
end
