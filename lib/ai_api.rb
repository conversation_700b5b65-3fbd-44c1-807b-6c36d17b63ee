class AiApi
  class DemodulizedMessageError < StandardError
    def initialize(msg = nil)
      @msg = msg
      super
    end

    def to_s
      @msg || self.class.to_s.demodulize
    end
  end

  class RetriableError < DemodulizedMessageError; end
  class NonRetriableError < DemodulizedMessageError; end
  class NoGradeFoundError < DemodulizedMessageError; end
  class HTTPCodeError < StandardError; end

  def self.new(section:, evaluation:, language: :en)
    config = evaluation.examiner.ai_config&.deep_symbolize_keys&.[](section) || Rails.application.config.ai[language][:talent_ai][section]
    type = config[:type]
    const_get(type.classify).new(model: config[:model], language:, section:, evaluation:)
  end
end
