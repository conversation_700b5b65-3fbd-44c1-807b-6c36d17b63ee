class Alv::Api::Core
  def self.execute(api_order:, status:)
    test_instance = api_order.test_instance
    begin
      data_serialized = Alv::TestInstanceSerializer.new(test_instance, status:)
    rescue StandardError => e
      Alert.integrations('ALV Serializer errors', e.message, integration_name: 'alv', meta: { test_instance_id: test_instance&.id })
      return
    end

    response = HTTParty.post(ENV.fetch('PGPHANDLER_LAMBDA_URL', nil), headers: { 'Content-Type' => 'application/json' }, body: { plainText: data_serialized.to_json }.to_json)
    encrypted_data = JSON.parse(response.body)

    if response.code != 200
      Alert.integrations('ALV Lambda errors', integration_name: 'alv', meta: encrypted_data.merge(test_instance_id: test_instance&.id))
      return
    end

    correlation_id = SecureRandom.uuid
    headers = {
      'x-api-key' => ENV.fetch('ALV_X_API_KEY', nil),
      'CorrelationId' => correlation_id,
      'x-functions-key' => ENV.fetch('ALV_X_FUNCTIONS_KEY', nil),
      'partner-key' => 'TS'
    }

    api_order.order_information[:correlation_id] = correlation_id
    api_order.save

    response = HTTParty.post("#{ENV.fetch('ALV_API_URL', nil)}/ALVAssessmentProgress",
                             body: encrypted_data['body'],
                             headers:)

    alv_api_response = JSON.parse(response.body)
    api_order.order_information[:alv_api_response] = alv_api_response
    api_order.save

    if response.code != 200
      Alert.integrations('ALVAssessmentProgress errors', integration_name: 'alv', meta: alv_api_response.merge(test_instance_id: test_instance.id))
    end
  end

  module Candidate
    def self.update(api_order:, status:)
      Alv::Api::Core.execute(api_order:, status:)
    end
  end
end
