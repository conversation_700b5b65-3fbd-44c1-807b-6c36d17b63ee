class AiApi::EtsTartine::Spoken < AiApi::EtsTartine::TestType
  PATHS_PER_LANGUAGE = {
    en: '/score_speech_responses',
    es: '/score_spanish_speech_responses',
    de: '/score_german_speech_responses'
  }

  class << self
    def path(language)
      PATHS_PER_LANGUAGE[language.to_sym]
    end

    def request_params(productions)
      {
        base64s: productions,
        audio_file_format: 'ogg'
      }
    end

    def prepare_productions(productions)
      productions.filter_map do |production|
        if production.has_recording? && production.recording&.url && production.recording.data
          response = HTTParty.get(production.recording.url)
          base64 = Base64.encode64(response.body).delete("\n")
          base64.presence
        end
      end
    end
  end
end
