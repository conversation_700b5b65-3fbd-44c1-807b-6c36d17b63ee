class AiApi::EtsTartine::Written < AiApi::EtsTartine::TestType
  PATHS_PER_LANGUAGE = {
    en: '/score_text_responses',
    es: '/score_spanish_text_responses',
    de: '/score_german_text_responses'
  }

  class << self
    def path(language)
      PATHS_PER_LANGUAGE[language.to_sym]
    end

    def request_params(productions)
      { texts: productions }
    end

    def prepare_productions(productions)
      productions.filter_map do |production|
        if production.has_text_production? && production.text_production.present?
          production.text_production
        end
      end
    end
  end
end
