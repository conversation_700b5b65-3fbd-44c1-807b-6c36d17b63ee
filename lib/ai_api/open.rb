class AiApi::Open
  include HasAiApi
  # defines:
  # @section
  # @language
  # @model
  # @evaluation

  define_type :open

  OPEN_AI_API_URL = Rails.application.config.open_ai[:base_url].freeze
  OPEN_AI_TRANSCRIPTIONS_API_URL = Rails.application.config.open_ai[:transcriptions_url].freeze

  private

  def question_text(productions_count)
    <<~HEREDOC.chomp
      Please evaluate the following #{productions_count} #{I18n.t("languages.#{@language}", locale: :en)} texts by the same author and determine the author's overall proficiency level according to the Common European Framework of Reference for Languages (CEFR).
      Use this CEFR scale, that consists of 3 sub-levels for each major level, progressing from the most basic to the most advanced:
      A1-, A1, A1+, A2-, A2, A2+, B1-, B1, B1+, B2-, B2, B2+, C1-, C1, C1+, C2-, C2, C2+
    HEREDOC
  end

  def extract_response(data)
    return nil if data.blank?

    data['choices'][0]['message']['content']
  end

  def build_prompt(productions_count:, productions_text:)
    <<~HEREDOC.chomp
      #{question_text(productions_count)}
      #{productions_text}
      Reply in the most concise way possible giving only one CEFR level. If you cannot tell from the data or the data is in other language, reply 'not enough data'.
    HEREDOC
  end

  protected

  def uri
    @uri ||= URI(OPEN_AI_API_URL)
  end

  def api_token
    @api_token ||= OPEN_AI_API_TOKEN
  end

  def request_data(params)
    @request_data ||= {
      model: @model,
      messages: [{ role: 'user', content: params[:text].to_s }]
    }
  end

  def headers
    @headers ||= {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{api_token}"
    }
  end

  def extract_grade(response)
    response.match(/(A1[-+]?|A2[-+]?|B1[-+]?|B2[-+]?|C1[-+]?|C2[-+]?)/)&.[](0)
  end

  def prediction_cecrl_score(data, no_result_error = AiApi::NoGradeFoundError)
    # We need to figure out what Open AI is sending us if it's not this format
    begin
      response = extract_response(data)
      AiService.add_ai_event_log(evaluation: @evaluation,
                                 section: @section,
                                 criticity: :info,
                                 message: "Response: '#{response}'",
                                 action: :continuing)
      value = extract_grade(response)
    rescue Exception
      raise AiApi::RetriableError, 'ParseError'
      # Open Ai sometime sends a weird successful null error
      # https://community.openai.com/t/openai-api-error-the-server-had-an-error-while-processing-your-request-sorry-about-that/53263
    end
    value || (raise no_result_error)
  end

  public

  def score_and_response(productions:)
    productions_text = ''
    productions.each do |production|
      if production.has_recording?
        next if production.production_metadatum&.production_transcription.blank?

        productions_text += "\n[#{production.production_metadatum&.production_transcription.to_s.strip}]"
      elsif production.has_text_production?
        next if production.text_production.blank?

        productions_text += "\n[#{production.text_production}]"
      end
    end

    # TODO: this could be improved by extracting texts first and formatting after
    raise AiApi::NonRetriableError, 'NotEnoughContent' if productions_text.blank?

    prompt = build_prompt(productions_count: productions.count, productions_text:)
    if @evaluation.public_send(:"ai_#{@section}_attempts") == 1
      AiService.add_ai_event_log(evaluation: @evaluation,
                                 section: @section,
                                 criticity: :info,
                                 message: prompt,
                                 action: :continuing)
    end
    resp = request({ text: prompt })
    cecrl_score = prediction_cecrl_score(resp)
    score = CECRL_LEVELS_RANGE[cecrl_score].last if cecrl_score
    [score, resp]
  end

  def audio_transcription(url:, filename:)
    file_path = Rails.root.join('tmp', File.basename(filename))

    # Download the file from URL and save it to the tmp folder
    unless File.exist?(file_path)
      uri = URI(url)
      response = Net::HTTP.get_response(uri)
      File.binwrite(file_path, response.body)
    end

    payload = {
      file: File.open(file_path),
      prompt: 'Transcribe exactly what you hear',
      model: @model
    }

    headers = {
      'Content-Type' => 'multipart/form-data',
      'Authorization' => "Bearer #{api_token}"
    }
    response = RestClient.post(OPEN_AI_TRANSCRIPTIONS_API_URL, payload, headers)
    JSON.parse(response.body)
  end
end
