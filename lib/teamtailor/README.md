Requirements :

- Create an ApiUser named 'teamtailor_api' : 

```
ApiUser.create(name: 'teamtailor_api', default_test_profile_id: test_profile_id)
```

- Post the initial config to the API :
```
rake teamtailor::set_activation_config
```

- Set the default tests and profiles to be used :

```
# config/initializers/teamtailor.rb

  teamtailor: {
    test_profile_ids: [
      28,
      72
    ],
    catalog: {
      'ptbr' => 'Brazilian Portuguese'
    }
```

- OPTIONAL : Set the proper environment variables, in initializers/teamtailor

```
    ENV['TEAMTAILOR_API_KEY']
    ENV['TEAMTAILOR_PARTNER_ID']
    ENV['TEAMTAILOR_BASE_URL'] 
    ENV['TEAMTAILOR_API_VERSION'] 
    ENV['PIPPLET_SALES_URL'] 
```

Test with :

```
rails test test/teamtaillor
```
