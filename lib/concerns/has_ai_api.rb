module HasAiApi
  extend ActiveSupport::Concern

  included do
    class << self
      attr_accessor :type

      TYPES = %i[open ets_tartine].freeze

      def define_type(type)
        raise ArgumentError if self.type

        self.type = type if type.in?(TYPES)
      end
    end
  end

  def initialize(language:, model:, section:, evaluation: nil)
    @section = section
    @language = language
    @model = model
    @evaluation = evaluation
  end

  protected

  def request(params)
    body = request_data(params)
    resp = HTTParty.post(
      uri,
      body: body.to_json,
      headers:,
      timeout: 300 # seconds
    )
    raise AiApi::HTTPCodeError, resp.code unless resp.code.in? 200..299

    JSON.parse(resp.body)
  end

  def uri
    raise NotImplementedError
  end

  def api_token
    raise NotImplementedError
  end

  def request_data(params)
    raise NotImplementedError
  end

  def headers
    raise NotImplementedError
  end
end
