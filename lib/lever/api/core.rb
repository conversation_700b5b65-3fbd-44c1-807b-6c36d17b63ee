module Lever::Api::<PERSON>
  def self.execute(method:, url:, api_user:, payload: {}, headers: {})
    retried = false
    access_token = api_user&.api_oauth_manager&.access_token
    headers['Content-Type']  = 'application/json'
    headers['Authorization'] = "Bearer #{access_token}" if access_token

    begin
      RestClient::Request.execute(method: method,
                                  url: url,
                                  payload: payload.to_json,
                                  headers: headers)
    rescue RestClient::ExceptionWithResponse => e
      parsed_response = begin
        JSON.parse(e.response) if e.response
      rescue JSON::ParserError
        nil
      end

      code = e.response&.code
      if code == 401
        if parsed_response&.[]('code') == 'InvalidCredentialsError' && !retried
          begin
            Lever::Api.refresh_token(api_user)
          rescue StandardError
            error_message = I18n.t('lever.errors.refresh_token', api_user_name: api_user.name)
            EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
            raise LeverTokenError, error_message if retried
          end
          retried = true
          retry
        end
      elsif code == 403 && parsed_response&.[]('error') == 'invalid_grant'
        raise LeverSetupError, I18n.t('lever.errors.expired_token', api_user_name: api_user.name)
      else
        error_message = I18n.t('lever.errors.request_unsuccessful', api_user_name: api_user.name, error_code: code&.to_s)
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
        raise LeverError, error_message
      end
    end
  end

  module ArchiveReasons
    def self.all(api_user)
      url = "#{Rails.application.config.lever[:base_url]}/archive_reasons"
      error_message = I18n.t('lever.errors.get_opportunity_reasons', api_user_name: api_user.name)
      begin
        response = Lever::Api::Core.execute(method: :get, url: url, api_user: api_user)
      rescue LeverError
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
        raise LeverSetupError, error_message
      end
      if response&.body
        parsed_response = JSON.parse(response.body)
        parsed_response&.[]('data')
      else
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
        raise LeverSetupError, error_message
      end
    end
  end

  module Opportunities
    def self.get(api_user, opportunity_id)
      return unless opportunity_id

      url = "#{Rails.application.config.lever[:base_url]}/opportunities/#{opportunity_id}"
      begin
        response = Lever::Api::Core.execute(method: :get, url: url, api_user: api_user)
      rescue LeverError
        error_message = I18n.t('lever.errors.get_opportunity', api_user_name: api_user.name)
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
        raise LeverError, error_message
      end
      Integrations::Lever::Opportunity.new(JSON.parse(response.body)&.[]('data')) if response&.body
    end

    def self.create(api_user, payload)
      url = "#{Rails.application.config.lever[:base_url]}/opportunities/?perform_as=#{payload.perform_as}"
      begin
        Lever::Api::Core.execute(method: :post, url: url, payload: payload.serializable_hash, api_user: api_user)
      rescue LeverError
        error_message = I18n.t('lever.errors.create_opportunity', api_user_name: api_user.name)
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
        raise LeverError, error_message
      end
    end

    def self.add_tag(api_user, opportunity_id, payload)
      url = "#{Rails.application.config.lever[:base_url]}/opportunities/#{opportunity_id}/addTags"
      Lever::Api::Core.execute(method: :post, url: url, payload: payload, api_user: api_user)
    end

    def self.remove_tags(api_user, opportunity_id, payload)
      url = "#{Rails.application.config.lever[:base_url]}/opportunities/#{opportunity_id}/removeTags"
      Lever::Api::Core.execute(method: :post, url: url, payload: payload, api_user: api_user)
    end

    module Notes
      def self.create(api_user, opportunity_id, payload)
        url = "#{Rails.application.config.lever[:base_url]}/opportunities/#{opportunity_id}/notes"
        Lever::Api::Core.execute(method: :post, url: url, payload: payload, api_user: api_user)
      end
    end
  end

  module Users
    def self.get(api_user)
      email = api_user&.metadata_1&.[]('lever_admin_email')
      error_message = I18n.t('lever.errors.get_user', api_user_name: api_user.name, admin_email: email || I18n.t('lever.errors.no_admin_email'))

      raise LeverSetupError, error_message unless email

      lever_user = Integrations::Lever::User.new(email: email)
      url = "#{Rails.application.config.lever[:base_url]}/users/?email=#{email.to_param}"
      begin
        response = Lever::Api::Core.execute(method: :get, url: url, api_user: api_user)
        raise LeverError unless response&.body

        parsed_response = JSON.parse(response.body)
        lever_user.id = parsed_response&.[]('data')&.[](0)&.[]('id')
        return nil unless lever_user.valid?

        lever_user
      rescue LeverError
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :danger)
        raise LeverSetupError, error_message
      end
    end
  end

  module Tokens
    def self.refresh(api_user)
      refresh_token_data = Integrations::Lever::RefreshToken.new(api_user)
      url = "#{Rails.application.config.lever[:auth_url]}/oauth/token"
      Lever::Api::Core.execute(method: :post, url: url, payload: refresh_token_data.serializable_hash, api_user: api_user)
    end

    def self.create(api_user)
      code = api_user&.api_oauth_manager&.login_name
      url = "#{Rails.application.config.lever[:auth_url]}/oauth/token"
      Lever::Api::Core.execute(method: :post, url: url, api_user: api_user,
                               payload: {
                                 code: code,
                                 grant_type: 'authorization_code',
                                 client_id: Rails.application.config.lever[:client_id],
                                 client_secret: Rails.application.config.lever[:client_secret],
                                 redirect_uri: Rails.application.routes.url_helpers.api_lever_callback_url
                               })
    end
  end
end
