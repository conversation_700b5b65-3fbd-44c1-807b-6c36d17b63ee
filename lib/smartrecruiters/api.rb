module Smartrecruiters
  class Api
    def self.partner_token
      response = Core::Tokens.create(api_user: nil)
      JSON.parse(response.body)['access_token']
    end

    def self.customer_token(api_user:)
      response = Core::Tokens.create(api_user: api_user)
      JSON.parse(response.body)['access_token']
    end

    def self.update_partner_configuration
      Core::Partner::Configuation.update
    end

    def self.create_integration(api_user)
      response = Core::Integrations::Companies.create(company_id: api_user.metadata_1['company_id'], client_id: api_user.name, client_secret: api_user.passphrase)
      parsed_response = JSON.parse(response.body)
      api_user.metadata_1['smartrecruiters_client_id']     = parsed_response['clientId']
      api_user.metadata_1['smartrecruiters_client_secret'] = parsed_response['clientSecret']
      api_user.authentication_token_expires_at = Time.now
      api_user.save
    end

    def self.publish_update(api_order)
      return unless api_order&.test_instance

      Core::Orders::Results.update(api_order: api_order)
    end
  end
end
