namespace :create do
  desc "Create Currencies"
  task :currencies => :environment do
    download = open('https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml')
    hash = Hash.from_xml(download)

    hash.first.last["Cube"]["Cube"]["Cube"].each do |currency|
      c = Currency.find_by_code(currency["currency"])
      if c.nil?
        Currency.new(
          code: currency["currency"],
          eur_exchange_rate: currency["rate"]
        ).save!
      end
    end
  end
end
