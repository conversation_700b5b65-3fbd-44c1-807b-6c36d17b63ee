namespace :bootstrap do
  desc 'Change pippletmail.com emails to pipplet.com'
  task fix_old_emails: :environment do
    [
      [DirectUser, 'pipplet_clients_client_email'],
      [User],
      [TestInstance, 'client_contact_email'],
      [Examiner]
    ].each do |model, email = 'email'|
      puts "#{model.name} : #{ApplicationRecord.connection.execute(
        "update #{model.table_name} set #{email} = replace(#{email}, '@pippletmail.com', '@pipplet.com')"
      ).inspect}"
    end
  end
end
