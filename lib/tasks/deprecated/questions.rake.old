### Import/Export Script

# Import
# rake questions:load
# Uses IMPORT_FILE_NAME as input for the question (same format as merged Data tab)
# Also uses 'elementstypes.csv' that is an export of the element types tab.

# Export
# rake questions:export
# Creates a csv file from the database

#########################

Dir.glob('lib//*.rb').each { |r| load r}
require 'csv'
# require 'regexp_parser'

# Fichier à charger
IMPORTFILENAME = "questions.csv"

MAX_LENGTH_FOR_WORDS = 20

namespace :questions do
  desc "create question"
  task :create, [:linguist_id] => :environment do |t, args|
    if args[:linguist_id].nil?
      puts "no argument"
    else
      puts args[:linguist_id]
    end

    ruby "db/questions/templates/"+args[:linguist_id]
  end


  desc "test arguments"
  task :arguments, [:filename] => :environment do |t, args|

  end


  desc "Review text productions"
  task :review_text_productions => :environment do
    Challenge.all.each do |challenge|

      next if challenge.questions.where(:type => "Questions::QTextFromTextArea").empty?

      challenge.productions.each do |production|
        reception = production.reception
        next if reception.nil?
        question = production.questions.where(:type => "Questions::QTextArea").first
        next if question.nil?
        question_label = question.label
        answer = production.questions.where(:type => "Questions::QTextArea").first.question_elements.for(reception).first.dqd.data['answer']
        puts "===="
        puts "QuestionID : #{question.id}, ProductionId : #{production.id}, ReceptionID: #{reception.id}"
        puts "Question: "+question_label.to_s
        puts "Answer was : "+answer.to_s+"//End of answer"
        puts "Report production ? (y/n)"
        input = STDIN.gets.strip
        production.reported! if input == 'y'
      end
    end
  end


  desc "Set scores for questions elements"
  task :set_scores_for_qe => :environment do
    Question.all.each do |q|
      if !q.cecrl_prod.nil? || CECRL_LEVELS_VALUES[:cecrl].include?(q.cecrl_prod)
        index = CECRL_LEVELS_VALUES[:cecrl].index(q.cecrl_prod)
        q.question_elements.each do |qe|
          qe.scores.for(:main_production).update_attribute(:rating, CECRL_LEVELS_VALUES[:average][index])
          qe.scores.for(:production).update_attribute(:rating, CECRL_LEVELS_VALUES[:average][index])
          qe.scores.for(:average).update_attribute(:rating, CECRL_LEVELS_VALUES[:average][index])
        end
      end
      if !q.cecrl_recep.nil? || CECRL_LEVELS_VALUES[:cecrl].include?(q.cecrl_recep)
        index = CECRL_LEVELS_VALUES[:cecrl].index(q.cecrl_recep)
        q.question_elements.each do |qe|
          qe.scores.for(:main_reception).update_attribute(:rating, CECRL_LEVELS_VALUES[:average][index])
          qe.scores.for(:reception).update_attribute(:rating, CECRL_LEVELS_VALUES[:average][index])
          qe.scores.for(:average).update_attribute(:rating, CECRL_LEVELS_VALUES[:average][index])
        end
      end
    end
  end

  desc "Load questions"
  task :empty => :environment do
    Challenge.delete_all
    Question.delete_all
    QuestionElement.delete_all
    Result.delete_all
  end

  desc "Updates for challenges"
  task :update_challenges => :environment do

    update_hash = {}
    # Load Element types definition
    row_count = 0
    CSV.foreach("lib/tasks/updates.csv") do |row|
      if row_count == 0
        row_count += 1
        next
      end
      update_hash["#{row[0]}"] = {
        :new_recording_duration => row[2],
        :new_production_duration => row[3],
        :new_reception_duration => row[4],
        :unpublish => row[5],
        :question_to_remove => row[6]
        }
      row_count += 1
    end

    update_hash.each do |id, value|
        print "Trying to update challenge #{id}...."
        c = Challenge.find_by_linguist_id(id)
        puts "..challenge found"

        # Update recording duration
        print "Recording duration update - "

        if !value[:new_recording_duration].nil? && !value[:new_recording_duration].empty?
          begin
            new_value = value[:new_recording_duration].to_i
            print "update to #{new_value}.."
            recorder = c.questions.where(:type => "Questions::QMicRecorder").first.question_elements.where(type: 'QuestionElements::QEMicRecorder').first
            print "recorder found.."
            recorder.update_max_duration = new_value
            print "new value is #{c.questions.where(:type => 'Questions::QMicRecorder').first.max_duration}"
            puts " - DONE"
          rescue
            print "..problem"
            puts "- ERROR"
          end
        else
          puts "no update asked - SKIP."
        end


        # Update challenge production duration
        print "Challenge production duration update - "

        if !value[:new_production_duration].nil? && !value[:new_production_duration].empty?
          begin
            # New value in miliseconds
            new_value = value[:new_production_duration].to_i*60*1000
            print "update to #{new_value}.."
            c.update_attribute(:production_duration, new_value)
            print "New value is #{c.production_duration}"
            puts " - DONE"
          rescue
            print "..problem"
            puts "- ERROR"
          end
        else
          puts "no update asked - SKIP."
        end


        # Update challenge reception duration
        print "Challenge reception duration update - "

        if !value[:new_reception_duration].nil? && !value[:new_reception_duration].empty?
          begin
            # New value in miliseconds
            new_value = value[:new_reception_duration].to_i*60*1000
            print "update to #{new_value}.."
            c.update_attribute(:reception_duration, new_value)
            print "New value is #{c.reception_duration}"
            puts " - DONE"
          rescue
            print "..problem"
            puts "- ERROR"
          end
        else
          puts "no update asked - SKIP."
        end


        # Update challenge publication status
        print "Challenge publication status - "

        if !value[:unpublish].nil? && !value[:unpublish].empty? && value[:unpublish] == "X"
          begin
            print "unpublishing challenge..."
            c.unpublished!
            print "new status is #{c.status}"
            puts " - DONE"
          rescue
            print "..problem"
            puts "- ERROR"
          end
        else
          puts "no update asked - SKIP."
        end

        puts "End of modifications for challenge #{c.id}"
        puts "--"
    end
  end

  desc "Update Durations"
  task :update_durations => :environment do
    update_hash = {
      'BH-21' => 60,
      'BH-22' => 60,
      'BH-31' => 60,
      'BH-32' => 60,
      'BH-23' => 90,
      'BH-30' => 90,
      'BH-24' => 40,
      'BH-25' => 50,
      'BH-26' => 50
    }
    update_hash.each do |id, value|
      begin
        print "Trying to update challenge #{id} to #{value} sec..."
        c = Challenge.find_by_linguist_id(id)
        c.questions.where(:type => "Questions::QMicRecorder").first.question_elements.where(type: 'QuestionElements::QEMicRecorder').first.update_max_duration = value
        print "..done. New value is #{c.questions.where(:type => 'Questions::QMicRecorder').first.max_duration}"
        puts "-- COMPLETED"
      rescue
        print "..problem"
        puts "-- TO BE FIXED"
      end
    end
  end

  desc "Load questions"
  task :load, [:filename] => :environment do |t, args|

    elements_types = {}

    if args[:filename].nil?
      question_file = IMPORTFILENAME
    else
      question_file = args[:filename]
    end



    # Load Element types definition
    CSV.foreach("lib/tasks/elementstypes.csv") do |row|
      row[3] == "x" ? validation = true : validation = false
      elements_types["#{row[0]}"] = {:validation => validation, :regex => "#{row[4]}", :subgroup_type => "#{row[5]}", :implementationQ => "#{row[6]}", :implementationQE => "#{row[6]}"}
    end

    # Create layouts taable
    layouts_table = {}
    l1col  = Layout.find_by_filename('one-column')
    raise 'Please run rake bootstrap:layouts first!' if l1col.nil?
    l1col_z1 = l1col.layout_zones.with_name(:instructions)
    l1col_z2 = l1col.layout_zones.with_name(:main)
    layouts_table['one-column'] = {
      :layout => l1col,
      :instructions => l1col_z1,
      :main => l1col_z2
    }


    l2col  = Layout.find_by_filename('two-columns')
    raise 'Please run rake bootstrap:layouts first!' if l2col.nil?
    l2col_z1 = l2col.layout_zones.with_name(:instructions)
    l2col_z2 = l2col.layout_zones.with_name(:main)
    l2col_z3 = l2col.layout_zones.with_name(:side)
    layouts_table['two-columns'] = {
      :layout => l2col,
      :instructions => l2col_z1,
      :main => l2col_z2,
      :side => l2col_z3,
    }

    # TODO : Change layout depending of challenge (or table that indicates actual layout to use
    current_layout = 'two-columns'

    # Load Ids (next time IDs will be directly in the questions.csv file)
    #ids_table = {}
    #CSV.foreach("lib/tasks/IDs.csv") do |row|
    #  ids_table["#{row[0]}"] = row[0].to_i
    # end

    # Create default image
    f = open(IMPORT_QUESTIONS_IMAGES_URL+"/default.png", "r")
    image = Image.create(:data => f)
    image.name = "default.png"
    image.save



    # Initiate missing_element_types list for quick detection
    missing_elements_types = []
    bad_elements_types = []
    not_implemented_class = []
    not_listed_in_script_class = []
    current_challenge_stringid = ""
    current_challenge_id = nil
    current_challenge = nil
    row_counter = 0
    current_question_index = 0

    CSV.foreach("lib/tasks/"+question_file) do |row|
      row_counter = row_counter+1
      next if row[0].nil?
      next if row[0] == "Author"

      ## Temporary specific to ignore test challenges (the two first ones)
      next if row[26].to_i <= 2

      ## ======
      ## CHALLENGE MANAGEMENT
      ## Get the challenge id, check if it the same as the previous line, if not check if we have to create the challenge or it already exist
      ##

      # Test if new challenge or continuing previous one
      if row[26].to_i == current_challenge_id
        # Do nothing
        puts "- continue challenge id #{current_challenge_id}"
        current_question_index = current_question_index+1
      else
        current_challenge_id = row[26].to_i
        puts "Challenge change"
        # If we are parsing a new challenge, test if it already exists in database, otherwise create it
        if Challenge.exists?(current_challenge_id)
          puts "- reusing existing Challenge id #{current_challenge_id}"
          current_challenge = Challenge.find(current_challenge_id)

          # Destroy all questions for this challenge
          current_challenge.questions.each do |q|
            q.question_elements.each do |qe|
              qe.destroy
            end
            q.destroy
          end
        else
          ## TODO : Set production/reception duration in Excel
          production_duration = 180000
          reception_duration = 180000


          ## TODO Add layout exception for written questions
          puts "- creating challenge, id #{current_challenge_id}"
          current_challenge = Challenge.create({
            production_layout_id: layouts_table[current_layout][:layout].id,
            reception_layout_id: layouts_table[current_layout][:layout].id,
            linguist_id:row[25],
            production_duration: production_duration,
            reception_duration: reception_duration,
            id: current_challenge_id})

        end
        current_question_index = 0
      end

      ## END OF CHALLENGE MANAGEMENT
      ## ======


      ## ======
      ## QUESTION MANAGEMENT
      ## Check the question type, the implementatino class and if we have them in the system.
      ## If not add them to a list that will be shown at the end  and skip line

      # Check if element type exists (cf GoogleSheet that makes correspondance), otherwise add it to missing_elements_types list
      if !elements_types.has_key?("#{row[2]}")
        missing_elements_types << "#{row[2]}" if !missing_elements_types.include?("#{row[2]}")
        puts "   skip Question of class #{row[2]}"
        next
      else
        current_element_type = elements_types["#{row[2]}"]
        # Test if class exists, otherwise add it to the list
        if !current_element_type[:implementationQ].empty?
          begin
            current_element_type[:implementationQ].constantize
            print  "   create Question of class #{current_element_type[:implementationQ]} ... "
            question_class = current_element_type[:implementationQ]
          rescue
            puts "   skip Question of class #{current_element_type[:implementationQ]} (question type is not implemented)"
            not_implemented_class << "#{current_element_type[:implementationQ]}" if !not_implemented_class.include?("#{current_element_type[:implementationQ]}")
            next
          end
        else
          bad_elements_types << "#{row[2]}" if !bad_elements_types.include?("#{row[2]}")
          puts "   skip Question of class #{row[2]} (no implementation name)"
          next
        end
      end





      # At this point we have the class of the Q, we can create it





      # Extract generic information whatever class we are dealing with
      # Test what for is this question
      q_for = row[5] == "Production" ? :production : :reception
      # Get order
      q_order = row[7].nil? ? current_question_index : row[7].to_i
      # Attributes
      q_attributes = row[3]

      # Test if q_attibutes is empty, this can happen for certain QTypes (ex : AudioProduction)
      if q_attributes.nil?
        q_elements = []
      else
        # Extract elements from attributes, depending of type of groups (standard are just comma separated, complex are between brackets)
        if current_element_type[:subgroup_type] == "brackets"
          q_elements = parse_brackets_attributes(q_attributes)
        else
          q_elements = parse_standard_attributes(q_attributes)
        end
      end




      # Specific test for text/word
      # If class is Text but we only have short answer, consider it as a Word
      text_classes = [
        "Questions::QRadioTextsRecorder",
        "Questions::QRadioLinkedTextsDisplayer",
        "Questions::QRadioLinkedTextsRecorder",
        "Questions::QRadioSecretLinkedTextsDisplayer",
      ]
      if text_classes.include?(question_class)
        if q_elements.length > 1
          has_only_words = true
          q_elements.each_with_index do |qe, index|
            next if index==0
            if q_elements[index].length > MAX_LENGTH_FOR_WORDS
              has_only_words = false
              break
            end
          end
        else
          has_only_words = false
        end

        question_class.gsub!('Texts', 'Words') if has_only_words

      end
      # End of specific test for text/word




      # Switch on class

      case question_class

        ## === Question class category
        ## General Elements

        # QInstruction
        when "Questions::QInstruction"
          ## Construct Question
          instruction = Questions::QInstruction.create({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:instructions].id ,
            order: 0,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for)
          })

          QuestionElements::QEText.create({
            question_id: instruction.id,
            order: 1,
            scorable: false,
            content: {:text => q_elements[1], :style => 'h2'}
          })
          i = 2
          until q_elements[i].nil? || q_elements[i].empty?
            QuestionElements::QEText.create({
              question_id: instruction.id,
              order: i,
              scorable: false,
              content: {:text => q_elements[i], :style => 'h2'}
            })
            i+=1
          end

          print "QInstruction done"
        # End of QInstruction

        # QImage
        when "Questions::QImage"
          ## Only one argument except label for this one

          ## Construct Question
          Questions::QImage.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: "",
            question_for: Question.question_fors.fetch(q_for),
            question_elements: [{name: q_elements[0], credit: q_elements[1]}]
          })
          print "QImage done"
        # End of QImage

        # QHtml
        when "Questions::QHtml"
          ## Open file and put code as content
          begin
            f = File.open("lib/tasks/html/#{q_elements[0]}")
            code = f.read.html_safe
          rescue
            code = "<p>This is an error</p>"
          end

          ## Construct Question
          Questions::QHtml.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: "This is an html element",
            question_for: Question.question_fors.fetch(q_for),
            question_elements: [{code: code}]
          })
          print "QHtml done"
        # End of QHtml

        ## End of General Elements

        ## === Question class category
        ## Microphone elements

        # QMicRecorder
        when "Questions::QMicRecorder"
          ## Only one argument except label for this one

          ## Construct Question
          Questions::QMicRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:side].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: [max_duration: q_elements[1]]
          })
          print "QMicRecorder done"
        # End of QMicRecorder

        # QAudioProduction
        when "Questions::QAudioProduction"
          ## Construct Question
          Questions::QAudioProduction.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:side].id ,
            order: 0,
            label: "Audio production",
            question_for: Question.question_fors.fetch(q_for),
            question_elements: [{value: "An audio production"}]
          })
          print "QAudioProduction done"
        # End of QAudioProduction

        ## End of Microphone elements

        ## === Question class category
        ## Text Elements (not radio)


        # QText
        when "Questions::QText"
          ## Only one argument except label for this one

          ## Construct Question
          Questions::QText.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: "This is a text",
            question_for: Question.question_fors.fetch(q_for),
            question_elements: [{text: q_elements[0], style: q_elements[1]}]
          })
          print "QText done"
        # End of QText

        # QTextArea
        when "Questions::QTextArea"
          ## Extract arguments
          structured_elements = []
          forbidden_words = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            forbidden_words << qe
          end
          structured_elements = [{
            placeholder: 'Type here...',
            forbidden_words: forbidden_words
          }]

          ## Construct Question
          Questions::QTextArea.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QTextArea done"
        # End of QTextArea

        # QTextProduction
        when "Questions::QTextProduction"
          ## Construct Question

          Questions::QTextFromTextArea.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: 0,
            label: "Text production",
            question_for: Question.question_fors.fetch(q_for)
          })
          print "QTextProduction done"
        # End of QTextProduction

        # QTextFieldRecorder
        when "Questions::QTextFieldRecorder"
          ## Extract arguments
          structured_elements = []
          valid_answers = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            valid_answers << qe
          end
          structured_elements = [{
            placeholder: 'Type here...',
            valid_answers: valid_answers
          }]

          ## Construct Question
          Questions::QTextFieldRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QTextFieldRecorder done"
        # End of QTextFieldRecorder

        ## End of Text Elements (not radio)

        ## === Question class category
        ## RadioElements Displayers (Words, Texts, Images and Html)

        # QRadioLinkedWordsDisplayer
        when "Questions::QRadioLinkedWordsDisplayer"
          ## Structure elements
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {word: qe}
          end

          ## Construct Question
          Questions::QRadioLinkedWordsDisplayer.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioLinkedWordsDisplayer done"
        # End of QRadioLinkedWordsDisplayer

        # QRadioSecretLinkedWordsDisplayer
        when "Questions::QRadioSecretLinkedWordsDisplayer"
          ## Structure elements
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {word: qe}
          end

          ## Construct Question
          Questions::QRadioSecretLinkedWordsDisplayer.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioSecretLinkedWordsDisplayer done"
        # End of QRadioSecretLinkedWordsDisplayer

        # QRadioLinkedTextsDisplayer
        when "Questions::QRadioLinkedTextsDisplayer"
          ## Structure elements
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {text: qe}
          end

          ## Construct Question
          Questions::QRadioLinkedTextsDisplayer.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioLinkedTextsDisplayer done"
        # End of QRadioLinkedTextsDisplayer

        # QRadioSecretLinkedTextsDisplayer
        when "Questions::QRadioSecretLinkedTextsDisplayer"
          ## Structure elements
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {text: qe}
          end

          ## Construct Question
          Questions::QRadioSecretLinkedTextsDisplayer.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioSecretLinkedTextsDisplayer done"
        # End of QRadioSecretLinkedTextsDisplayer

        # QRadioLinkedImagesDisplayer
        when "Questions::QRadioLinkedImagesDisplayer"
          ## Structure elements
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {image_name: qe}
          end

          ## Construct Question
          Questions::QRadioLinkedImagesDisplayer.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioLinkedImagesDisplayer done"
        # End of QRadioLinkedImagesDisplayer

        # QRadioSecretLinkedImagesDisplayer
        when "Questions::QRadioSecretLinkedImagesDisplayer"
          ## Structure elements
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {image_name: qe}
          end

          ## Construct Question
          Questions::QRadioSecretLinkedImagesDisplayer.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioSecretLinkedImagesDisplayer done"
        # End of QRadioSecretLinkedImagesDisplayer

        ## End of RadioElements Displayers (Words, Texts, Images and Html)

        ## === Question class category
        ## RadioElements Recorders (Words, Texts, Images and Html)

        # QRadioWordsRecorder
        when "Questions::QRadioWordsRecorder"
          ## Extract arguments
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {
              word: qe,
              static_valid_answer: index==1 ? 'true' : 'false'
              }
          end

          ## Construct Question
          Questions::QRadioWordsRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioWordsRecorder done"
        # End of QRadioWordsRecorder


        # QRadioLinkedWordsRecorder
        when "Questions::QRadioLinkedWordsRecorder"
          ## Extract arguments
          ## No arguments as they are taken from corresponding displayer

          ## Construct Question
          Questions::QRadioLinkedWordsRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for)
          })
          print "QRadioLinkedWordsRecorder done"
        # End of QRadioLinkedWordsRecorder

        # QRadioTextsRecorder
        when "Questions::QRadioTextsRecorder"
          ## Extract arguments
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {
              text: qe,
              static_valid_answer: index==1 ? 'true' : 'false'
              }
          end

          ## Construct Question
          Questions::QRadioTextsRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioTextsRecorder done"
        # End of QRadioTextsRecorder

        # QRadioImagesRecorder
        when "Questions::QRadioImagesRecorder"
          ## Extract arguments
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {
              image_name: qe,
              static_valid_answer: index==1 ? 'true' : 'false'
              }
          end

          ## Construct Question
          Questions::QRadioImagesRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QRadioImagesRecorder done"
        # End of QRadioImagesRecorder

        # QRadioLinkedImagesRecorder
        when "Questions::QRadioLinkedImagesRecorder"
          ## Extract arguments
          ## No arguments as they are taken from corresponding displayer

          ## Construct Question
          Questions::QRadioLinkedImagesRecorder.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for)
          })
          print "QRadioLinkedImagesRecorder done"
        # End of QRadioLinkedImagesRecorder

        ## End of RadioElements Recorders (Words, Texts, Images and Html)

        ## === Question class category
        ## Specific elements, can have complex behaviour

        # QCheckbox
        when "Questions::QCheckbox"
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {
              value: qe[0],
              answer: qe[1].to_i
              }
          end


          ## Construct Question
          Questions::QCheckbox.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QCheckbox done"
        # End of QCheckbox

        # QDateTime
        when "Questions::QDateTime"
          ## Only one argument except label for this one

          case q_elements[2]
            when "DateTime"
              value = q_elements[1].split(" ")
              structured_elements = [{:static_valid_answer => value[0], :mode => "date"}, {:value => value[1], :mode => "time"}]
            when "Date", "Time", "Day-Month"
              structured_elements = [{:static_valid_answer => q_elements[1], :mode => q_elements[2]}]
            else
              raise "Bad DateTimePicker mode"
          end


          ## Construct Question
          Questions::QDateTime.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QDateTime done"
        # End of QDateTime

        # QDropDown
        when "Questions::QDropDown"
          ## Extract arguments
          structured_elements = []
          q_elements.each_with_index do |qe, index|
            next if index == 0
            structured_elements << {
              element: qe,
              static_valid_answer: index==1 ? 'true' : 'false'
              }
          end
          ## Construct Question
          Questions::QDropDown.build({
            challenge_id: current_challenge.id,
            layout_zone_id: layouts_table[current_layout][:main].id ,
            order: q_order,
            label: q_elements[0],
            question_for: Question.question_fors.fetch(q_for),
            question_elements: structured_elements
          })
          print "QDropDown done"
        # End of QDropDown

        ## End of Specific elements, can have complex behaviour


# Normally those does not exist anymore !!
#        # QRadioWordsDisplayer
#        when "QRadioWordsDisplayer"
#          ## Only one argument except label for this one
#
#          ## Construct Question
#          QRadioWordsDisplayer.build({
#            challenge_id: current_challenge.id,
#            layout_zone_id: 1,
#            order: q_order,
#            label: q_elements[0],
#            question_for: Question.question_fors.fetch(q_for),
#            question_elements: [{name: q_elements[0], credit: q_elements[1]}]
#          })
#          puts "QRadioWordsDisplayer done"
#        # End of QRadioWordsDisplayer
#
#        # QRadioTextsDisplayer
#        when "QRadioTextsDisplayer"
#          ## Only one argument except label for this one
#
#          ## Construct Question
#          QRadioTextsDisplayer.build({
#            challenge_id: current_challenge.id,
#            layout_zone_id: 1,
#            order: q_order,
#            label: q_elements[0],
#            question_for: Question.question_fors.fetch(q_for),
#            question_elements: [{name: q_elements[0], credit: q_elements[1]}]
#          })
#          puts "QRadioTextsDisplayer done"
#        # End of QRadioTextsDisplayer


        # TEMPLATE
        #when TEMPLATE
          ## Extract arguments
          #structured_elements = []
          #q_elements.each do |qe|
            # structured_elements << ## Someting
          #end

          ## Construct Question
          #TEMPLATE.build({
          #  challenge_id: current_challenge.id,
          #  layout_zone_id: 1,
          #  order: q_order,
          #  label: q_elements[0],
          #  question_for: Question.question_fors.fetch(q_for),
          #  question_elements: structured_elements
          #})
        # End of TEMPLATE

        else
          not_listed_in_script_class << question_class if !not_listed_in_script_class.include?(question_class)
          next
      end

      ## TAGS
      created_question = Question.last
      if !created_question.nil? && current_element_type[:validation]
        print " - Set tags..."
        tags = Tag.create([{:name => "Domain", :value => row[8]}, {:name => "cecrl_prod", :value => row[9]}, {:name => "cecrl_recep", :value => row[10]}])
        created_question.tags = tags
        created_question.save
        puts "tags done"
      end


      ## END OF QUESTION MANAGEMENT
      ## ======

    end




    ## ======
    ## STATISTICS


    ## Statistics and error display
    puts "Import done - #{row_counter} lines"
    puts "========="
    puts "Statistics and information"
    puts "Number of missing element types (not in the Element Types list) : #{missing_elements_types.length}"
    puts "Element types missing :"
    missing_elements_types.each do |met|
      puts "- #{met}"
    end
    puts "========="
    puts "Number of bad element types (does not have enough info) : #{bad_elements_types.length}"
    puts "Bad element types :"
    bad_elements_types.each do |bet|
      puts "- #{bet}"
    end
    puts "========="
    puts "Number of not implemented class (not created in code) : #{not_implemented_class.length}"
    puts "Not implemented class :"
    not_implemented_class.each do |nic|
      puts "- #{nic}"
    end
    puts "========="
    puts "Number of not listed in script class (not added to the case statement) : #{not_listed_in_script_class.length}"
    puts "Not listed in script class :"
    not_listed_in_script_class.each do |nlis|
      puts "- #{nlis}"
    end
    puts "========="
    ## STATISTICS
    ## ======

  end # end questions:load



  task :export => :environment do
    filename = "Questions_Export_#{Time.now.to_i}.csv"
    CSV.open( filename, 'w' ) do |writer|
      Challenge.find_each do |challenge|
        challenge.questions.each do |question|
          if question.is_a?(Questions::QInstruction)
            writer << [challenge.id, question.id, question.class.name, question.label, ""]
          else
            question.question_elements.each do |question_element|
              writer << [challenge.id, question.id, question_element.class.name, question.label, question_element.content]
            end
          end
        end
      end
    end
  end




  ## ======
  ## HELPER FUNCTIONS



  # Method to parse attributes like those : 'a1','a2', ..
  # Returns an array like : ["a1", "a2", ..]
  def parse_standard_attributes(attributes)

    # Reg is a bit complicated as we have to detect all strings that do not containt ',' element to dissociate all arguments
    # This regex works but returns an array that contains the argument and then the last letter of it, so I remove the second part
    attributes_array = []

    # First test if attributes start with an empty argument
    attributes_array << nil if attributes.match(/^\s*,/)

    # Then browse all arguments
    #attributes_array_temp = attributes.scan(/\'(((?!\'\s*,\s*\').)*)\'/)
    #attributes_array_temp = attributes.scan(/\'([\w\W]*?)\'/)
    attributes_array_temp = attributes.scan(/\'(((?!\'\s*,\s*\')[\w\W])*)\'/)
    attributes_array_temp.each do |a|
      attributes_array << a.first.to_s
    end
    return attributes_array
  end

  # Method to parse attributes like those : {'a1', 1},{'a2', 2}..
  # Returns an array like : [["a work of fiction", "1"], ["a social commentary", "1"], ["a biography of Gatsby", "0"]]
  def parse_brackets_attributes(attributes)
    attributes_array = []

    # First get the label (either empty or set)
    if attributes.match(/^\s*,/)
      attributes_array << nil
    else
      attributes_array << attributes.scan(/\'(((?!\'\s*,\s*{).)*)\'/)[0][0]
    end

    attributes.scan(/\'([^{}]*)\',\s*\'(\w)\'/).each do |attribute|
      attributes_array << attribute
    end
    return attributes_array
  end

  ## ======
  ## HELPER FUNCTIONS



end
