namespace :average_grades_for_test_instance do
  desc "create average grades for TestInstance"
  task :create => :environment do
    failed = []

    TestInstance.where(status: :graded).find_each do |test_instance|
      if (eval_ids = test_instance.evaluations.with_certificate.completed.with_trusted_examiner.with_real_examiner.pluck(:id) ).count > 1
        
        ## Get all available labels
        assessment_types = test_instance.evaluations.with_certificate.completed.with_trusted_examiner.with_real_examiner.pluck(:assessment_type_id)
        labels = []
        assessment_types.each do |a_t|
          labels |= AssessmentType.find(a_t).json_calculation_rules.keys
        end
        
        labels.each do |label|
          begin
            test_instance.average_grades << AverageGrade.create_with_eval_ids(eval_ids: eval_ids, label: label, ti: test_instance)
          rescue
             failed << [test_instance.id, label]
          end
        end
      end
    end
    puts "failed: #{failed}"
  end
end
