namespace :data_migration do
  desc '2024.07.08 - Copy AI Responses from TIs to Evaluations'
  task :up20240708 => :environment do
    TestInstance.where("ai_written_response IS NOT NULL OR ai_spoken_response IS NOT NULL").find_in_batches do |test_instances|
      test_instances.each do |test_instance|
        evaluations = test_instance.evaluations.joins(:examiner).where(examiner: { is_ai: true }).to_a
        evaluations.each do |evaluation|
          evaluation.update(
            ai_relatedness_written_response: test_instance.ai_relatedness_written_response,
            ai_relatedness_spoken_response: test_instance.ai_relatedness_spoken_response,
            ai_written_response: test_instance.ai_written_response,
            ai_spoken_response: test_instance.ai_spoken_response
          )
        end
      end
    end
  end
end
