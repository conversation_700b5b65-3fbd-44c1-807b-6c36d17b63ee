namespace :data_migration do
  desc '2024.06.11 - [up] Add missing AssessmentQuestion for Written AssessmentType'
  task :up20240611 => :environment do
    assessment_type = AssessmentType.find_by(name: 'written')
    puts('Assessment Type not found').then { return } unless assessment_type

    assessment_questions = assessment_type.json_data.dig('questions', 1, 'assessmentQuestions')

    if assessment_questions.detect { |aq| aq['assessment_question_id'] == 34 }
      puts 'AssessmentQuestion 34 already exists, skipping'
    else
      assessment_type.json_data['questions'][1]['assessmentQuestions'].insert(0, { assessment_question_id: 34, key: 'strenghts', google_entry: '' })
      assessment_type.save!
    end

    if assessment_questions.detect { |aq| aq['assessment_question_id'] == 35 }
      puts 'AssessmentQuestion 35 already exists, skipping'
    else
      assessment_type.json_data['questions'][1]['assessmentQuestions'].insert(1, { assessment_question_id: 35, key: 'recomandation', google_entry: '' })
      assessment_type.save!
    end
  end
end
