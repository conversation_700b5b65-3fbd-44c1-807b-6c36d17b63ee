require 'test_helper'

class JobviteApiControllerTest < ActionDispatch::IntegrationTest
  include ActiveJob::TestHelper
  include IntegrationTestHelper

  def setup
    FactoryBot.create(:examiner, :requestable)
    @test_language = AVAILABLE_TEST_LANGUAGES.sample.to_s
    test_profile_id = Rails.application.config.jobvite[:test_profile_ids].sample
    @test_profile = TestProfile.find_by(id: test_profile_id) || FactoryBot.create(:test_profile, id: test_profile_id)
    @candidate_id = Faker::Internet.uuid
    @application_id = Faker::Internet.uuid
    @api_user = FactoryBot.create(:api_user,
                                  default_test_profile_id: @test_profile.id,
                                  metadata: {
                                    jobvite:
                                      {
                                        api_token: Faker::Internet.uuid,
                                        secret: Faker::Internet.uuid
                                      }
                                  })

    @valid_params = FactoryBot.build(:jobvite_test_instance_request_data, id: @candidate_id, application_id: @application_id)
    @valid_headers = build_valid_headers(params: @valid_params, api_user: @api_user)
    @candidate_show_url = "#{Rails.application.config.jobvite[:base_url]}/candidate?candidateId=#{@candidate_id}&format=json"
    stub_request(:get, @candidate_show_url).to_return(status: 200, body: FactoryBot.build(:jobvite_candidate, test_profile_name: @test_profile.name, language: @test_language).to_json)
  end

  def build_valid_headers(params:, api_user:)
    { 'X-Jobvite-Event-Signature' => build_signature(params:, api_user:) }
  end

  def build_signature(params:, api_user:)
    Base64.encode64(OpenSSL::HMAC.digest(OpenSSL::Digest.new('sha256'), api_user.passphrase, params.to_json)).delete("\n")
  end

  test '#create should include a valid api_user param' do
    post api_jobvite_language_tests_url(api_user_name: Faker::Company.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 404, status
  end

  test '#create api_user should be active' do
    api_user = FactoryBot.create(:api_user, status: 0)
    post api_jobvite_language_tests_url(api_user_name: api_user),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 404, status
  end

  test '#create api_user should have a passphrase' do
    api_user = FactoryBot.create(:api_user, passphrase: nil)
    post api_jobvite_language_tests_url(api_user_name: api_user),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 404, status
  end

  test '#create should validate the webhook signature' do
    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: { 'X-Jobvite-Event-Signature' => SecureRandom.hex },
         as: :json

    assert_equal 401, status
  end

  test '#create not should trigger for the wrong event_types and trigger_types' do
    invalid_event_type_params = FactoryBot.build(:jobvite_test_instance_request_data, id: @candidate_id, event_type: 'invalid-event-type')
    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: invalid_event_type_params,
         headers: build_valid_headers(params: invalid_event_type_params, api_user: @api_user),
         as: :json

    assert_equal 204, status
  end

  test '#create should use language/test_profile from custom fields' do
    test_profile_id = Rails.application.config.jobvite[:test_profile_ids].sample
    test_profile = TestProfile.find_by(id: test_profile_id) || FactoryBot.create(:test_profile, id: test_profile_id)

    test_language = AVAILABLE_TEST_LANGUAGES.sample.to_s
    candidate = FactoryBot.build(:jobvite_candidate, test_profile_name: test_profile.name, language: test_language)

    stub_request(:get, @candidate_show_url).to_return(status: 200, body: candidate.to_json)

    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 201, status

    test_instance = TestInstance.last

    assert_equal test_profile.id, test_instance.test_profile_id
    assert_equal test_language, test_instance.test_language
  end

  test '#create should only allow language/test_profile from config' do
    test_language = AVAILABLE_TEST_LANGUAGES.sample.to_s
    candidate = FactoryBot.build(:jobvite_candidate, test_profile_name: FactoryBot.create(:test_profile).name, language: test_language)

    stub_request(:get, @candidate_show_url).to_return(status: 200, body: candidate.to_json)

    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 400, status

    parsed_response = JSON.parse(response.body)

    assert_equal({ 'error' => 'Test profile is not included in the list' }, parsed_response)
  end

  test '#create should fallback to default language/test_profile if custom fields are missing or invalid' do
    candidate = FactoryBot.build(:jobvite_candidate, application: {})

    stub_request(:get, @candidate_show_url).to_return(status: 200, body: candidate.to_json)

    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 201, status

    test_instance = TestInstance.last

    assert_equal @api_user.default_test_profile_id, test_instance.test_profile_id
    assert_equal @api_user.default_test_language, test_instance.test_language
  end

  test '#create should return a 201 upon success' do
    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 201, status
  end

  test '#create should store api_order' do
    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    associated_api_order = ApiOrder.last
    refute_nil associated_api_order
    refute_nil associated_api_order.source
    assert_equal 'jobvite', associated_api_order.source
    assert_equal @api_user.name, associated_api_order.api_user.name
    refute_nil associated_api_order.order_information
    assert_equal({ "candidate_id" => @candidate_id, "application_id" => @application_id }, associated_api_order.order_information)
  end

  test '#create should create/update a user and create a TestInstance' do
    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    associated_api_order = ApiOrder.last
    assert_equal 201, status
    user = associated_api_order.user
    test_instance = associated_api_order.test_instance

    refute_nil user
    refute_nil test_instance

    assert_equal @api_user.default_test_profile_id, test_instance.test_profile_id
    assert_equal user.id, test_instance.user_id
    assert_equal user.first_name, test_instance.user.first_name
    assert_equal user.email, test_instance.user.email
    assert_equal "api:#{@api_user.name}", test_instance.user.group

    assert_equal 'Jobvite', test_instance.client_type
    assert_equal @api_user.name.capitalize, test_instance.client_name
  end

  test '#create should validate TestInstanceRequest params' do
    email = Faker::Internet.unique.email(domain: 'invalid.com')
    ValidatesEmailFormatOf.expects(:validate_email_format).with(email).returns(['does not appear to be a valid email address']).once
    stub_request(:get, @candidate_show_url).to_return(status: 200, body: FactoryBot.build(:jobvite_candidate, email: email, first_name: '', last_name: '').to_json)

    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 400, status
    assert_equal({ 'error' => 'Candidate email is invalid, Candidate first Name is blank, and Candidate last Name is blank' }, JSON.parse(response.body))
    assert_integration_alert_sent(:jobvite, title: 'Invalid params while creating order', message: 'Candidate email is invalid, Candidate first Name is blank, and Candidate last Name is blank')
  end

  test '#create should catch errors if Jobvite::Api.show fails' do
    stub_request(:get, @candidate_show_url).to_return(status: 403, body: {}.to_json)

    post api_jobvite_language_tests_url(api_user_name: @api_user.name),
         params: @valid_params,
         headers: @valid_headers,
         as: :json

    assert_equal 400, status
    assert_equal({ 'error' => 'Network error while retrieving Candidate' }, JSON.parse(response.body))
    assert_integration_alert_sent(:jobvite, title: 'Invalid params while creating order', message: 'Network error while retrieving Candidate')
  end
end
