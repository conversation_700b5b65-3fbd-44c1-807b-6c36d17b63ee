require 'test_helper'

class TestInstanceValidationDetailsJobTest < ActiveJob::TestCase
  include AlertsHelper

  test '#perform creates an Alert upon Error' do
    message = Faker::Company.buzzword
    test_instance = FactoryBot.create(:test_instance)
    passed = [true, false].sample
    TestInstance.any_instance.stubs(:test_instance_validation).raises(StandardError.new(message))
    TestInstanceValidationDetailsJob.perform_now(test_instance.id, passed)

    assert_alert(title: "[#{TestInstanceValidationDetailsJob}]",
                 category: :job,
                 message:,
                 meta: {
                   test_instance_id: test_instance.id,
                   passed:
                 })
  end
end
