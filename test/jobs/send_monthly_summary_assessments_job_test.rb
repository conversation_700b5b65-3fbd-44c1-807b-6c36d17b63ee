require 'test_helper'

class SendMonthlySummaryAssessmentsJobTest < ActiveJob::TestCase
  include AlertsHelper

  test '#perform creates an Alert upon Error' do
    examiner = FactoryBot.create(:examiner)
    message = Faker::Company.buzzword
    AlertMailer.stubs(:monthly_summary_assessments).raises(StandardError.new(message))
    SendMonthlySummaryAssessmentsJob.perform_now(examiner)

    assert_alert(title: "[#{SendMonthlySummaryAssessmentsJob}]",
                 category: :job,
                 message:, meta: {
                   examiner_id: examiner.id
                 })
  end
end
