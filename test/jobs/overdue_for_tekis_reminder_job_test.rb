require 'test_helper'

class OverdueForTekisReminderJobTest < ActiveJob::TestCase
  include AlertsHelper

  test '#perform creates an Alert upon Error' do
    message = Faker::Company.buzzword
    TestInstance.stubs(:not_completed).raises(StandardError.new(message))
    OverdueForTekisReminderJob.perform_now

    assert_alert(title: "[#{OverdueForTekisReminderJob}]", category: :job, message:)
  end
end
