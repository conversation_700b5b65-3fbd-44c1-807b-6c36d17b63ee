require 'test_helper'

class CreateProductionMetadataJobTest < ActiveJob::TestCase
  setup do
    ProductionMetadatum.any_instance.stubs(:service).returns(stub(transcription: ''))
    @production = FactoryBot.create(:production, :spoken)
    @production_metadatum = @production.production_metadatum
    @production_metadatum.update!(production_transcription: '')
  end

  test 'does not raise an exception on last retry' do
    ProductionMetadatum.any_instance.stubs(:updated_transcription?).raises(StandardError)

    assert_nothing_raised do
      assert_performed_jobs 3, only: CreateProductionMetadataJob do
        CreateProductionMetadataJob.perform_later(@production_metadatum.id)
      end
    end
    assert_event_log(@production.test_instance,
                     content: "[CreateProductionMetadataJob] All retries failed for Production 'id'=#{@production.id} error: StandardError")
  end

  test 'retry logic if the transcription failed' do
    assert_enqueued_with(job: CreateProductionMetadataJob, args: [@production_metadatum.id]) do
      CreateProductionMetadataJob.perform_later(@production_metadatum.id)
    end
  end

  test 'Add to EventLog if transcription failed' do
    assert_enqueued_with(job: CreateProductionMetadataJob, args: [@production_metadatum.id]) do
      assert_difference('EventLog.count', 1) do
        CreateProductionMetadataJob.perform_now(@production_metadatum.id)
      end
    end
  end

  test 'Retry logic if transcription failed first' do
    ProductionMetadatum.any_instance.unstub(:service)
    ProductionMetadatum.any_instance.stubs(:updated_transcription?)
                       .returns(false)
                       .then.returns(false)
                       .then.returns(true)

    assert_performed_jobs 3, only: CreateProductionMetadataJob do
      CreateProductionMetadataJob.perform_later(@production_metadatum.id)
    end
  end

  test 'Retry logic if transcription failed : retry 3 times to finally raise' do
    ProductionMetadatum.any_instance.unstub(:service)
    ProductionMetadatum.any_instance.stubs(:updated_transcription?)
                       .returns(false)
                       .then.returns(false)
                       .then.returns(false)
    assert_nothing_raised do
      assert_performed_jobs 3, only: CreateProductionMetadataJob do
        CreateProductionMetadataJob.perform_later(@production_metadatum.id)
      end
    end
  end
end
