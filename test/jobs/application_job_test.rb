require 'test_helper'

class ApplicationJobTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  ActiveJob::Base.queue_adapter = :test

  class TestJob < ApplicationJob
    def perform(evaluation); end
  end

  test 'perform should execute the job & not create an Error if serialized object exists' do
    alerts_count = Alert.count
    evaluation = FactoryBot.create(:evaluation)
    perform_enqueued_jobs do
      TestJob.perform_later(evaluation)
    end

    assert_equal alerts_count, Alert.count
  end
end
