require 'test_helper'

class FactorialApiControllerTest < ActionDispatch::IntegrationTest
  include Integration<PERSON>estHel<PERSON>

  def setup
    api_key = SecureRandom.hex
    passphrase = SecureRandom.hex
    challenge = SecureRandom.hex

    @api_user = FactoryBot.create(:api_user, name: 'factorial',
                                             passphrase:,
                                             metadata: {
                                               factorial: {
                                                 api_key: api_key,
                                                 challenge: challenge
                                               }
                                             })

    @valid_headers = { 'x-factorial-wh-challenge' => challenge }
    @valid_factorial_headers = { authorization: "Bearer #{passphrase}" }
    @ats_candidate_id = Faker::Number.unique.number(digits: 7)
    @ats_job_posting_id = Faker::Number.unique.number(digits: 7)
    @applications_url = "#{Rails.application.config.factorial[:base_url]}/api/v1/ats/applications"
    @valid_create_params = FactoryBot.build(:factorial_test_instance_request_data,
                                            ats_candidate_id: @ats_candidate_id,
                                            ats_job_posting_id: @ats_job_posting_id)

    factorial_application = FactoryBot.build(:factorial_application,
                                             ats_candidate_id: @ats_candidate_id,
                                             ats_job_posting_id: @ats_job_posting_id)

    stub_request(:get, @applications_url).to_return(body: [factorial_application].to_json)
  end

  test '#webhook without ats_candidate_id should return an error' do
    invalid_create_params = FactoryBot.build(:factorial_test_instance_request_data, ats_candidate_id: nil)
    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: invalid_create_params, headers: @valid_headers, as: :json
    assert_response :bad_request
  end

  test '#webhook with ats_candidate_id should return an error if candidate is not found' do
    invalid_create_params = FactoryBot.build(:factorial_test_instance_request_data,
                                             ats_job_posting_id: @ats_job_posting_id,
                                             ats_candidate_id: Faker::Number.unique.number(digits: 7))

    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: invalid_create_params, headers: @valid_headers, as: :json
    parsed_response = JSON.parse(response.body)
    assert_equal({ 'error' => 'Error while retrieving Application' }, parsed_response)

    assert_equal 400, status
  end

  test '#create requires a valid api_user_id and api key' do
    post api_factorial_language_tests_url(api_user_id: SecureRandom.hex), params: @valid_create_params, headers: @valid_headers, as: :json

    assert_equal 401, status

    invalid_headers = { 'x-factorial-wh-challenge' => SecureRandom.hex }
    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: @valid_create_params, headers: invalid_headers, as: :json

    assert_equal 401, status

    inactive_challenge = SecureRandom.hex
    FactoryBot.create(:api_user, status: 0, metadata: { challenge: inactive_challenge }) # initialized
    invalid_headers = { 'x-factorial-wh-challenge' => inactive_challenge }

    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: @valid_create_params, headers: invalid_headers, as: :json

    assert_equal 401, status
  end

  test '#create should return an error if application is missing parameters' do
    wrong_phase_factorial_application = FactoryBot.build(:factorial_application,
                                                         ats_candidate_id: @ats_candidate_id,
                                                         ats_job_posting_id: @ats_job_posting_id,
                                                         first_name: nil,
                                                         last_name: nil,
                                                         email: nil)

    stub_request(:get, @applications_url).to_return(body: [wrong_phase_factorial_application].to_json)

    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: @valid_create_params, headers: @valid_headers, as: :json
    parsed_response = JSON.parse(response.body)

    assert_equal 400, status
    assert_equal({ 'error' => 'Candidate email is invalid, Candidate first Name is blank, Candidate last Name is blank, and Application is invalid' }, parsed_response)
  end

  test '#create should return an error if application is not in the right phase' do
    wrong_phase_factorial_application = FactoryBot.build(:factorial_application,
                                                         ats_candidate_id: @ats_candidate_id,
                                                         ats_job_posting_id: @ats_job_posting_id,
                                                         ats_application_phase_name: Faker::Company.bs)

    stub_request(:get, @applications_url).to_return(body: [wrong_phase_factorial_application].to_json)

    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: @valid_create_params, headers: @valid_headers, as: :json
    parsed_response = JSON.parse(response.body)

    assert_equal 400, status
    assert_equal({ 'error' => 'Application is invalid' }, parsed_response)
  end

  test '#create should create an api_order, test_instance and user' do
    counts = [@api_user.test_instances.count, @api_user.api_orders.count]

    post api_factorial_language_tests_url(api_user_id: @api_user.id), params: @valid_create_params, headers: @valid_headers, as: :json
    assert_equal 201, status
    assert_in_delta(counts[0], @api_user.test_instances.count, 1)
    assert_in_delta(counts[1], @api_user.api_orders.count, 1)

    assert_empty(response.parsed_body.symbolize_keys)
  end

  test '#create should catch errors if application retrieval fails' do
    stub_request(:get, @applications_url)
      .to_return(status: 403, body: {}.to_json)

    post api_factorial_language_tests_url(api_user_id: @api_user.id),
         params: @valid_create_params,
         headers: @valid_headers,
         as: :json

    assert_equal 400, status
    assert_equal({ 'error' => 'Error while retrieving Application' }, JSON.parse(response.body))
    assert_integration_alert_sent(:factorial, title: 'Invalid params while creating order', message: 'Error while retrieving Application')
  end
end
