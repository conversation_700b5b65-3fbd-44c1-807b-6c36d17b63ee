require 'test_helper'

class Integrations::Factorial::ApplicationTest < ActiveSupport::TestCase
  def setup
    @application_data = FactoryBot.build(:factorial_application)
  end

  test 'load up' do
    application = Integrations::Factorial::Application.new(@application_data)

    assert application.valid?
  end

  test 'when the candidate is not valid it is not valid' do
    application_data = FactoryBot.build(:factorial_application, first_name: nil)
    application = Integrations::Factorial::Application.new(application_data)

    refute application.valid?
  end

  test 'when the phase name is Language Assessment it says so' do
    application_data = FactoryBot.build(:factorial_application, ats_application_phase_name: 'Language Assessment')
    application = Integrations::Factorial::Application.new(application_data)

    assert application.assessment_needed?
  end

  test 'when the phase name is not Language Assessment it says so' do
    application_data = FactoryBot.build(:factorial_application, ats_application_phase_name: Faker::Company.bs)

    application = Integrations::Factorial::Application.new(application_data)

    refute application.assessment_needed?
  end
end
