require 'test_helper'

class LeverApiControllerTest < ActionDispatch::IntegrationTest
  include LeverTestHelper
  include ActiveJob::TestHelper

  def setup
    @test_profile  = FactoryBot.create(:test_profile)
    @test_profile2 = FactoryBot.create(:test_profile)
    @test_profile3 = FactoryBot.create(:test_profile)

    set_lever_test_config(test_profile_ids: [@test_profile2.id, @test_profile3.id])

    # Randomly generated variables

    @timestamp          = Time.now.to_time.to_i
    @opportunity_uuid   = Faker::Internet.uuid
    @lever_user_id      = Faker::Internet.uuid
    @token              = SecureRandom.hex
    @refresh_token      = SecureRandom.hex
    @admin_email        = Faker::Internet.unique.email
    @sample_opportunity = Integrations::Lever::Opportunity.new('name' => Faker::Name.name, 'emails' => [Faker::Internet.unique.email(domain: 'pipplet.com')])

    # Model data

    @tags                  = Lever::Api.build_tag_list
    @api_user              = FactoryBot.create(:api_user, :with_api_oauth_manager, access_token: SecureRandom.hex, access_token_url: SecureRandom.hex, name: "#{Faker::Company.name.underscore}_api", default_test_profile_id: @test_profile.id, default_test_language: 'en', 'metadata_1' => { lever_admin_email: @admin_email })
    @unconfigured_api_user = FactoryBot.create(:api_user, name: "#{Faker::Company.name.underscore}_api", default_test_profile_id: @test_profile.id, default_test_language: 'en', 'metadata_1' => { lever_admin_email: @admin_email })
    @valid_webhook_data = FactoryBot.build(:lever_test_instance_request_data,
                                           token: @token,
                                           signature: get_valid_signature(@api_user, @token, @timestamp),
                                           triggered_at: @timestamp,
                                           opportunity_id: @opportunity_uuid)

    @lever_token           = Integrations::Lever::Token.new({ 'access_token' => @token, 'refresh_token' => @refresh_token })
    @opportunity_with_tags = Integrations::Lever::Opportunity.new('name' => Faker::Name.name, 'emails' => [Faker::Internet.unique.email(domain: 'pipplet.com')], 'tags' => [Faker::Marketing.buzzwords, @tags.first, Faker::Marketing.buzzwords])

    # URLS & Stubs

    define_api_urls(opportunity_uuid: @opportunity_uuid, lever_user_id: @lever_user_id)
    define_default_stubs(admin_email: @admin_email,
                         sample_opportunity: @sample_opportunity,
                         lever_user_id: @lever_user_id,
                         new_token: @token,
                         new_refresh_token: @refresh_token)
  end

  test 'the webhook endpoint should return an error if a valid signature is not sent' do
    post api_lever_url(api_user_name: @api_user.name), params: FactoryBot.build(:lever_test_instance_request_data,
                                                                                to_stage_id: "#{Rails.application.config.lever[:webhook][:trigger_stages][0]}invalid",
                                                                                token: @token,
                                                                                signature: SecureRandom.hex,
                                                                                triggered_at: @timestamp,
                                                                                opportunity_id: @opportunity_uuid)

    assert_equal 401, status
    assert_equal I18n.t('lever.errors.invalid_webhook_signature'), JSON.parse(response.body)['error']
  end

  test 'the webhook endpoint should return an error if no passphrase is stored on the ApiUser' do
    api_user     = FactoryBot.create(:api_user, :with_api_oauth_manager, passphrase: nil, access_token: SecureRandom.hex, access_token_url: SecureRandom.hex, name: "#{Faker::Company.name.underscore}_api", default_test_profile_id: @test_profile.id, default_test_language: 'en', 'metadata_1' => { lever_admin_email: @admin_email })
    webhook_data = FactoryBot.build(:lever_test_instance_request_data,
                                    token: @token,
                                    signature: nil,
                                    triggered_at: @timestamp,
                                    opportunity_id: @opportunity_uuid)

    post api_lever_url(api_user_name: api_user.name), params: webhook_data

    assert_equal 404, status
    assert_equal I18n.t('lever.errors.api_user_not_found'), JSON.parse(response.body)['error']
  end

  test 'the webhook endpoint should receive a valid api_user' do
    post api_lever_url(api_user_name: "#{Faker::Company.name.underscore}_api_invalid")
    assert_equal 404, status
    assert_equal I18n.t('lever.errors.api_user_not_found'), JSON.parse(response.body)['error']
  end

  test 'the webhook endpoint should use config to trigger if no api_user stage is defined' do
    post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data
    assert_equal 201, status
    refute_nil JSON.parse(response.body)['partner_interview_id']
  end

  test 'the webhook endpoint should only be triggered for specific opportunity stages' do
    post api_lever_url(api_user_name: @api_user.name), params: FactoryBot.build(:lever_test_instance_request_data,
                                                                                to_stage_id: "#{Rails.application.config.lever[:webhook][:trigger_stages][0]}invalid",
                                                                                token: @token,
                                                                                signature: get_valid_signature(@api_user, @token, @timestamp),
                                                                                triggered_at: @timestamp,
                                                                                opportunity_id: @opportunity_uuid)
    assert_equal 204, status
  end

  test 'the webhook endpoint should trigger at set api_user stage, and don\'t trigger webhook if event doesn\'t exist' do
    @api_user.metadata_1 = { lever_trigger_stage: 'different_stage' }
    @api_user.save
    post api_lever_url(api_user_name: @api_user.name), params: FactoryBot.build(:lever_test_instance_request_data,
                                                                                to_stage_id: "some_stage",
                                                                                token: @token,
                                                                                signature: get_valid_signature(@api_user, @token, @timestamp),
                                                                                triggered_at: @timestamp,
                                                                                opportunity_id: @opportunity_uuid)

    assert_equal 204, status
  end

  test 'the webhook endpoint should trigger at set api_user stage' do
    @api_user.metadata_1 = { lever_trigger_stage: 'some_user_stage' }
    @api_user.save
    post api_lever_url(api_user_name: @api_user.name), params: FactoryBot.build(:lever_test_instance_request_data,
                                                                                to_stage_id: "some_user_stage",
                                                                                token: @token,
                                                                                signature: get_valid_signature(@api_user, @token, @timestamp),
                                                                                triggered_at: @timestamp,
                                                                                opportunity_id: @opportunity_uuid)

    assert_equal 201, status
    refute_nil JSON.parse(response.body)['partner_interview_id']
  end

  test 'the webhook endpoint should not trigger if lever_trigger_only_on_tag is set + no tag is on the opportunity' do
    @api_user.metadata_1 = { lever_trigger_only_on_tag: true }
    @api_user.save
    post api_lever_url(api_user_name: @api_user.name), params: FactoryBot.build(:lever_test_instance_request_data,
                                                                                to_stage_id: "some_user_stage",
                                                                                token: @token,
                                                                                signature: get_valid_signature(@api_user, @token, @timestamp),
                                                                                triggered_at: @timestamp,
                                                                                opportunity_id: @opportunity_uuid)

    assert_equal 204, status
  end

  test 'the webhook endpoint should store api_order' do
    post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data

    associated_api_order = ApiOrder.last
    refute_nil associated_api_order
    refute_nil associated_api_order.source
    assert_equal 'lever', associated_api_order.source
    assert_equal @api_user.name, associated_api_order.api_user.name
    refute_nil associated_api_order.order_information
    assert_equal @valid_webhook_data['data']['opportunityId'], associated_api_order.order_information['opportunity_id']
  end

  test 'the webhook endpoint should create/update a user and create a TestInstance' do
    post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data

    associated_api_order = ApiOrder.last
    user = associated_api_order.user
    test_instance = associated_api_order.test_instance

    refute_nil user
    refute_nil test_instance

    assert_equal @test_profile.id, test_instance.test_profile_id
    assert_equal user.id, test_instance.user_id
    assert_equal user.first_name, test_instance.user.first_name
    assert_equal user.email, test_instance.user.email
    assert_equal "api:#{@api_user.name}", test_instance.user.group

    assert_equal 'Lever', test_instance.client_type
    assert_equal @api_user.name.capitalize, test_instance.client_name
  end

  test 'the webhook should post a note message on error' do
    sample_opportunity = Integrations::Lever::Opportunity.new('name' => '', 'emails' => [])
    stub_request(:get, @opportunity_url)
      .to_return(status: 200, body: sample_opportunity.serializable_hash.to_json)

    assert_performed_jobs 1, only: Integrations::Lever::ErrorJob do
      post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data
    end
  end

  test 'the callback should save the oauth code of the api_user in the login name field & save the tokens' do
    code = SecureRandom.hex
    get api_lever_callback_url(code: code, state: @unconfigured_api_user.name)
    @unconfigured_api_user.reload
    assert_equal code, @unconfigured_api_user.api_oauth_manager.login_name
    assert_equal @token, @unconfigured_api_user.api_oauth_manager.access_token
    assert_equal @refresh_token, @unconfigured_api_user.api_oauth_manager.access_token_url
    assert_redirected_to root_url
    assert_equal I18n.t('devise.confirmations.confirmed'), flash[:success]
  end

  test 'the callback should log an error and display a message to the user if token recovery fails' do
    stub_request(:post, "#{Rails.application.config.lever[:auth_url]}/oauth/token")
      .to_return(status: 403, body: { error: 'invalid_grant', error_description: 'Invalid authorization code' }.to_json)

    code = SecureRandom.hex
    get api_lever_callback_url(code: code, state: @unconfigured_api_user.name)
    @unconfigured_api_user.reload
    assert_equal code, @unconfigured_api_user.api_oauth_manager.login_name
    assert_nil @unconfigured_api_user.api_oauth_manager.access_token
    assert_nil @unconfigured_api_user.api_oauth_manager.access_token_url
    assert_redirected_to root_url
    assert_equal I18n.t('lever.errors.expired_token', api_user_name: @unconfigured_api_user.name), flash[:error]
  end

  test 'the callback should log an error and display a message to the user if opportunity creation fails' do
    stub_request(:post, @opportunities_url).to_return(status: 500, body: {}.to_json)
    get api_lever_callback_url(code: SecureRandom.hex, state: @api_user.name)
    assert_redirected_to root_url
    assert_equal I18n.t('lever.errors.create_opportunity', api_user_name: @api_user.name), flash[:error]
  end

  test 'the callback should log an error and display a message to the user if getting archive_reasons fails' do
    stub_request(:get, @archive_reasons_url).to_return(status: 500, body: {}.to_json)
    get api_lever_callback_url(code: SecureRandom.hex, state: @api_user.name)
    assert_redirected_to root_url
    assert_equal I18n.t('lever.errors.get_opportunity_reasons', api_user_name: @api_user.name), flash[:error]
  end

  test 'the callback should set-up the account, and return an error if the lever_user cannot be recovered' do
    stub_request(:get, "#{@users_url}?email=#{@admin_email}").to_return(status: 403, body: { data: [] }.to_json)
    get api_lever_callback_url(code: SecureRandom.hex, state: @api_user.name)
    assert_redirected_to root_url

    assert_equal I18n.t('lever.errors.get_user', api_user_name: @api_user.name, admin_email: @admin_email), flash[:error]
  end

  test 'the callback should set-up the account, and return an error if the api_user cannot be recovered (invalid state)' do
    code = SecureRandom.hex
    state = SecureRandom.hex
    get api_lever_callback_url(code: code, state: state)

    assert_equal "Error saving code for Api_user : #{state}, code :  #{code}", flash[:error]
  end

  test 'webhook should select the right test_profile/language if they are mentioned in the webhook tags' do
    stub_request(:get, "#{Rails.application.config.lever[:base_url]}/opportunities/#{@opportunity_uuid}")
      .to_return(status: 200, body: @opportunity_with_tags.serializable_hash.to_json)
    post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data

    associated_api_order = ApiOrder.last
    test_instance = associated_api_order.test_instance

    assert_equal @test_profile2.id, test_instance.test_profile_id
    assert_equal 'ptbr', test_instance.test_language
  end

  test 'webhook should select the default test_profile/language if none are mentioned in the webhook tags' do
    stub_request(:get, "#{Rails.application.config.lever[:base_url]}/opportunities/#{@opportunity_uuid}")
      .to_return(status: 200, body: @sample_opportunity.serializable_hash.to_json)

    post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data

    associated_api_order = ApiOrder.last
    test_instance = associated_api_order.test_instance

    refute_nil test_instance
    refute_nil test_instance.test_profile_id
    assert_equal @api_user.default_test_profile_id, test_instance.test_profile_id
    assert_equal @api_user.default_test_language, test_instance.test_language
  end

  test 'webhook should select the first test_profile/language in tags list' do
    Rails.application.config.lever[:test_profile_ids] = [@test_profile2.id, @test_profile3.id]

    @opportunity_with_multi_valid_tag = Integrations::Lever::Opportunity.new('name' => Faker::Name.name,
                                                                             'emails' => [Faker::Internet.unique.email(domain: 'pipplet.com')],
                                                                             'tags' => [Faker::Marketing.buzzwords, @tags.last, @tags.second, @tags.first])
    stub_request(:get, "#{Rails.application.config.lever[:base_url]}/opportunities/#{@opportunity_uuid}")
      .to_return(status: 200, body: @opportunity_with_multi_valid_tag.serializable_hash.to_json)

    post api_lever_url(api_user_name: @api_user.name), params: @valid_webhook_data

    associated_api_order = ApiOrder.last
    test_instance = associated_api_order.test_instance
    refute_nil test_instance
    refute_nil test_instance.test_profile_id
    assert_equal @test_profile3.id, test_instance.test_profile_id
    assert_equal 'da', test_instance.test_language
  end
end
