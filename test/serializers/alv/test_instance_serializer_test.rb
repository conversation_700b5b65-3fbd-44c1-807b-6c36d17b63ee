require 'test_helper'

class Alv::TestInstanceSerializerTest < ActiveSupport::TestCase
  setup do
    @test_instance = FactoryBot.create(:test_instance, :graded)
    @delivered_evaluation = @test_instance.get_delivered_eval
    @user_id = SecureRandom.uuid
    FactoryBot.create(:api_order, test_instance: @test_instance, order_information: { 'userId' => ["samlp|Learn-Vantage|#{@user_id}"] })
    @serializer = Alv::TestInstanceSerializer.new(@test_instance, status: "Passed")
  end

  test 'serializer must have the correct values' do
    assert_equal "Passed", @serializer.instance_variable_get(:@status)
    assert_equal "Passed", @serializer.items.first[:assessmentStatusUId]
    assert_equal @test_instance.begin_date.iso8601, @serializer.items.first[:attendedDateTime]
    assert_equal @test_instance.count_of_questions_to_answer, @serializer.items.first[:countOfQuestions]
    assert_equal @delivered_evaluation&.assessed_at&.iso8601, @serializer.items.first[:assessmentDate]
    assert_equal '', @serializer.items.first[:userAssessmentProgressId]
    assert_equal 1, @serializer.items.first[:attempt]
    assert_equal @test_instance.user.id, @serializer.items.first[:candidateId]
    assert_equal '43485889-aacd-4eb4-a8f6-ad06ada93a86', @serializer.items.first[:assessmentId]
    assert_equal 1, @serializer.items.first[:noOfAttempts]
    assert_equal @user_id, @serializer.items.first[:userUid]
  end

  test 'serializer must display the assessment scores' do
    assessment_scores = @serializer.items.first[:assessmentScore]
    assert_kind_of Hash, assessment_scores
    assert_kind_of Array, assessment_scores['skillProfile']
    refute_empty assessment_scores['skillProfile']

    assessment_scores['skillProfile'].each do |skill_profile|
      assert_empty skill_profile[:skills].first[:name]
      assert_empty skill_profile[:skills].first[:description]
      assert_empty skill_profile[:skills].first[:overallScore]
      assert_empty skill_profile[:skills].first[:overallPerformance]
      assert_includes ['Speaking & Listening', 'Writing & Reading'], skill_profile[:category]
      assert_includes @delivered_evaluation.certificate.grades.map(&:score), skill_profile[:score]
    end
  end
end
