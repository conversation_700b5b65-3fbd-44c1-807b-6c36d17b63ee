require 'test_helper'

module Api
  module V3
    class TestInstanceFullSerializerTest < ActiveSupport::TestCase
      def setup
        @client_stub = Aws::S3::Client.new(stub_responses: true)
        Rails.application.config.stubs(:s3).returns(
          identity_bucket: Aws::S3::Resource.new(client: @client_stub).bucket('')
        )
      end

      test 'serializes identity_photos_urls if expose_identity_photos_in_api? is set to true in client_config' do
        @client_stub.stub_responses(:list_objects_v2, { contents: Array.new(30, { key: 'image.jpeg' }) })
        client_config = FactoryBot.create(:client_config, expose_identity_photos_in_api: true)
        test_instance = FactoryBot.create(:test_instance, :graded, client_config:)
        assert_equal 10, Api::V3::LanguageTestSerializer.new(test_instance).as_json[:identity_photos_urls].length
      end

      test 'does not serialize identity_photos_urls if expose_identity_photos_in_api is set to false in client_config' do
        @client_stub.stub_responses(:list_objects_v2, { contents: Array.new(10, { key: 'image.jpeg' }) })
        client_config = FactoryBot.create(:client_config, expose_identity_photos_in_api: false)
        test_instance = FactoryBot.create(:test_instance, :graded, client_config:)
        refute Api::V3::LanguageTestSerializer.new(test_instance).as_json.key? :dashboard_url
      end

      test 'does not serialize identity_photos_urls if test_instance has no client_config' do
        test_instance = FactoryBot.create(:test_instance, :graded)
        refute Api::V3::LanguageTestSerializer.new(test_instance).as_json.key? :identity_photos_urls
      end

      test 'serializes alerts only if the TI is graded' do
        tags = Tag.where(name: Tag::EVALUATION[:security_violation]).sample(2)
        test_instance = FactoryBot.create(:test_instance, :sent_for_evaluation, tags:)
        assert_empty Api::V3::LanguageTestSerializer.new(test_instance).as_json[:alerts]
      end

      test 'serializes TSVs' do
        tags = Tag.where(name: Tag::EVALUATION[:security_violation]).sample(2)
        test_instance = FactoryBot.create(:test_instance, :graded, tags:)
        assert_equal({ 'test_security_violation' => tags.sort.map { |tag| I18n.t("test_instances.tags.#{tag.name}") } }, Api::V3::LanguageTestSerializer.new(test_instance).as_json[:alerts])
      end

      test 'serializes UBs' do
        tags = Tag.where(name: Tag::EVALUATION[:unusual_behaviour]).sample(2)
        test_instance = FactoryBot.create(:test_instance, :graded, tags:)
        assert_equal({ 'unusual_behaviour' => tags.sort.map { |tag| I18n.t("test_instances.tags.#{tag.name}") } }, Api::V3::LanguageTestSerializer.new(test_instance).as_json[:alerts])
      end

      test 'does not serialize UBs if TSVs' do
        unusual_behaviour_tags = Tag.where(name: Tag::EVALUATION[:unusual_behaviour]).sample(2)
        security_violation_tags = Tag.where(name: Tag::EVALUATION[:security_violation]).sample(2)
        test_instance = FactoryBot.create(:test_instance, :graded, tags: unusual_behaviour_tags + security_violation_tags)
        assert_equal({ 'test_security_violation' => security_violation_tags.sort.map { |tag| I18n.t("test_instances.tags.#{tag.name}") } }, Api::V3::LanguageTestSerializer.new(test_instance).as_json[:alerts])
      end

      test 'serializes dashboard_url if expose_dashboard_url_in_api is set to true in client_config' do
        client_config = FactoryBot.create(:client_config, expose_dashboard_url_in_api: true)
        test_instance = FactoryBot.create(:test_instance, :graded, client_config:)
        assert_equal "#{PippletClientsApi.api_url}/audition_users/#{test_instance.uuid}", Api::V3::LanguageTestSerializer.new(test_instance).as_json[:dashboard_url]
      end

      test 'does not serialize dashboard_url if test_instance has no client_config' do
        test_instance = FactoryBot.create(:test_instance, :graded)
        refute Api::V3::LanguageTestSerializer.new(test_instance).as_json.key? :dashboard_url
      end
    end
  end
end
