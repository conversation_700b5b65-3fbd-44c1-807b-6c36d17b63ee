FactoryBot.define do
  factory :workable_test_instance_request_data, class: Hash do
    skip_create

    transient do
      test_profile_id { Rails.application.config.workable[:test_profile_ids].sample }
      language { AVAILABLE_TEST_LANGUAGES.sample }
      assessment_id { Faker::Number.number }
      api_user { nil }
    end

    test_id { "#{test_profile_id}-#{language}" }
    job_shortcode { Faker::Internet.uuid }
    callback_url  { "#{Faker::Internet.url}/#{assessment_id}" }
    candidate { build(:workable_candidate) }

    initialize_with { attributes.with_indifferent_access }
  end
end
