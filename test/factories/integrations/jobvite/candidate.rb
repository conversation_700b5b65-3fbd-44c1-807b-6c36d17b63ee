FactoryBot.define do
  factory :jobvite_candidate, class: Hash do
    skip_create

    transient do
      test_profile_name { TestProfile.where(id: Rails.application.config.jobvite[:test_profile_ids]).pluck(:name).sample }
      language { AVAILABLE_TEST_LANGUAGES.sample }
    end

    email { Faker::Internet.unique.email(domain: 'pipplet.com') }
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    application do
      {
        job: {
          'customField' => [
            {
              'fieldCode' => Rails.application.config.jobvite[:custom_fields][:input][:test_language],
              'value' => language.to_s
            },
            {
              'fieldCode' => Rails.application.config.jobvite[:custom_fields][:input][:test_profile],
              'value' => test_profile_name
            }
          ]
        }
      }
    end

    initialize_with { { 'candidates' => [attributes.transform_keys { |key| key.to_s.camelize(:lower) }.with_indifferent_access] } }
  end
end
