FactoryBot.define do
  factory :lever_test_instance_request_data, class: Hash do
    skip_create

    transient do
      to_stage_id { Rails.application.config.lever[:webhook][:trigger_stages].sample }
      opportunity_id { Faker::Internet.uuid }
      triggered_at { nil }
    end

    event { 'candidateStageChange' }
    token { SecureRandom.hex }
    signature { SecureRandom.hex }
    triggeredAt { triggered_at }
    data do
      {
        'toStageId' => to_stage_id,
        'opportunityId' => opportunity_id
      }
    end

    initialize_with { attributes.with_indifferent_access }
  end
end
