FactoryBot.define do
  factory :teamtailor_test_instance_request_data, class: Hash do
    skip_create

    transient do
      type { 'en' }
      candidate_id { rand(100) }
      first_name { Faker::Name.first_name }
      last_name { Faker::Name.last_name }
      email { Faker::Internet.unique.email(domain: 'pipplet.com') }
      job_id { rand(100) }
      test_profile { 28 }
    end

    initialize_with do
      { 'partner-event':
          {
            'webhook-data': {
              type: type,
              profile: test_profile
            },
            candidate: {
              id: candidate_id,
              'first-name': first_name,
              'last-name': last_name,
              email: email,
              job: {
                id: job_id
              }
            }
          } }
    end
  end
end
