# == Schema Information
#
# Table name: test_profiles
#
#  id                              :integer          not null, primary key
#  additional_linguist_ids         :string
#  additional_translated_languages :string
#  api_exposed                     :boolean          default(FALSE)
#  certification_type              :integer          default("detailed")
#  client_type                     :integer
#  deleted_at                      :datetime
#  examiner_description            :text
#  fallback_set                    :string
#  force_multilingual              :boolean          default(FALSE)
#  internal_description            :text
#  level                           :integer
#  linguist_ids                    :string
#  max_questions_to_answer         :integer
#  name                            :string           not null
#  next_questionable_profile       :integer
#  show_skip_button                :boolean          default(FALSE)
#  slug                            :string
#  status                          :integer          default("initialized"), not null
#  talent_ai                       :boolean          default(FALSE)
#  test_languages                  :string           default([]), is an Array
#  test_taker_type                 :integer          default("candidate")
#  test_type                       :integer          default("writting_and_speaking")
#  translated_languages            :string
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  test_instance_validation_id     :bigint
#
# Foreign Keys
#
#  fk_rails_...  (test_instance_validation_id => test_instance_validations.id)
#
FactoryBot.define do
  factory :test_profile do
    trait :spoken_and_written

    transient do
      id { nil }
      number_of_spoken_challenges { 4 }
      number_of_written_challenges { 3 }
      challenges do
        create_list(:challenge, number_of_written_challenges, :written) +
          create_list(:challenge, number_of_spoken_challenges, :spoken)
      end
      fallback_challenges { [] }
    end

    name { "#{test_type}-#{certification_type}-#{Faker::Number.unique.number}" }
    status { :active }
    test_languages { AVAILABLE_TEST_LANGUAGES.map(&:to_s) }
    translated_languages { AVAILABLE_TEST_LANGUAGES.join(';') }
    additional_translated_languages { AVAILABLE_TEST_LANGUAGES.join(';') }
    linguist_ids { challenges.pluck(:linguist_id).join(';') }
    fallback_set { fallback_challenges.pluck(:linguist_id).join(';') }
    api_exposed { false }
    certification_type { :simple }
    internal_description { Faker::Lorem.sentence }
    max_questions_to_answer { number_of_written_challenges + number_of_spoken_challenges }
    level { TestProfile.levels.keys.sample }
    client_type { TestProfile.client_types.keys.sample }
    talent_ai { false }

    # TODO: Typo, used for certificate templating, but should use certification_type
    test_type { :writting_and_speaking }

    # TODO: Remove columns
    test_taker_type { :candidate }
    next_questionable_profile { :fixed_audition }

    association :test_instance_validation, :spoken_and_written

    after :create do |test_profile, evaluator|
      test_profile.update(id: evaluator.id) if evaluator.id
    end

    trait(:talent_ai) do
      name { "#{test_type}-#{certification_type}-#{Faker::Number.unique.number}-AI" }
      talent_ai { true }
      test_languages { AI_ENABLED_LANGUAGES }
    end

    trait(:single_spoken_and_written) do
      number_of_spoken_challenges  { 1 }
      number_of_written_challenges { 1 }
      certification_type           { :written }
      test_type                    { :writting }
      test_instance_validation     { nil }
    end

    trait(:written) do
      number_of_spoken_challenges  { 0 }
      number_of_written_challenges { 5 }
      certification_type           { :written }
      test_type                    { :writting }
      association :test_instance_validation, :written
    end

    trait(:spoken) do
      number_of_spoken_challenges  { 5 }
      number_of_written_challenges { 0 }
      certification_type           { :spoken }
      test_type                    { :speaking }
      association :test_instance_validation, :spoken
    end
  end
end
