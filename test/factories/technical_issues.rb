# == Schema Information
#
# Table name: technical_issues
#
#  id               :integer          not null, primary key
#  status           :string
#  created_at       :datetime
#  updated_at       :datetime
#  test_instance_id :integer
#
# Indexes
#
#  index_technical_issues_on_test_instance_id  (test_instance_id)
#
FactoryBot.define do
  factory :technical_issue do
    association :test_instance

    trait(:opened) do
      status { "opened" }
    end

    trait(:ongoing) do
      status { "ongoing" }
    end

    trait(:pending) do
      status { "pending" }
    end

    trait(:closed) do
      status { "closed" }
    end

    trait(:abandonned) do
      status { "abandonned" }
    end

    trait(:ignored) do
      status { "ignored" }
    end
  end
end
