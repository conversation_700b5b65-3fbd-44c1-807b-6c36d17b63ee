# == Schema Information
#
# Table name: next_questionables
#
#  id                       :integer          not null, primary key
#  challenge_average_rating :float
#  questionable_user_rating :float
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  challenge_id             :integer
#  production_id            :integer
#  questionable_user_id     :integer
#  reception_id             :integer
#  test_instance_id         :integer
#  user_id                  :integer
#
# Indexes
#
#  index_next_questionables_on_challenge_id      (challenge_id)
#  index_next_questionables_on_production_id     (production_id)
#  index_next_questionables_on_test_instance_id  (test_instance_id)
#
FactoryBot.define do
  factory :next_questionable do
    test_instance { create(:test_instance) }
    production { create(:production, test_instance:) }
    challenge { Challenge.find(test_instance.test_profile.challenge_id_random_list.first) }
    reception { create(:reception, test_instance:, challenge:) }
    user { test_instance.user }
  end
end
