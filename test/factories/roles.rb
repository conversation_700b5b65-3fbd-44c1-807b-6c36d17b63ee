# == Schema Information
#
# Table name: roles
#
#  id            :integer          not null, primary key
#  name          :string
#  resource_type :string
#  created_at    :datetime
#  updated_at    :datetime
#  resource_id   :integer
#
# Indexes
#
#  index_roles_on_name_and_resource_type_and_resource_id  (name,resource_type,resource_id)
#
FactoryBot.define do
  factory :role do
    trait(:admin) { name { "admin" } }
    trait(:examiner) { name { "examiner" } }
  end
end
