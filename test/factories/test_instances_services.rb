# { user: %i[email first_name last_name phone_number locale] },
# { test_instance: %i[test_language test_mode redirection_url notification_url skip_email check_identity profile cpf_id] },
# { requestor: %i[email first_name last_name phone_number locale company_name] },
# { order: [:reference] }

# Barebone version of the factory - improvements welcome
FactoryBot.define do
  factory :test_instance_service, class: Api::TestInstancesService do
    skip_create

    transient do
      test_mode { true }
      skip_email { false }
      check_identity { false }
      test_profile_id { FactoryBot.create(:test_profile, :written).id }
      request { OpenStruct.new(raw_post: nil) }
      current_user { FactoryBot.create(:api_user) }
      test_language { AVAILABLE_TEST_LANGUAGES.map(&:to_s).sample }
      user_locale { 'en' }
    end

    requestor_params do
      {
        email: Faker::Internet.email(domain: 'pipplet.com'),
        first_name: Faker::Name.first_name,
        last_name: Faker::Name.last_name,
        phone_number: nil,
        locale: 'en',
        company_name: Faker::Company.name
      }
    end

    test_instance_params do
      {
        test_language:,
        test_mode:,
        redirection_url: nil,
        notification_url: nil,
        skip_email:,
        check_identity:,
        profile: nil,
        cpf_id: nil
      }
    end

    user_params do
      {
        email: Faker::Internet.email(domain: 'pipplet.com'),
        first_name: Faker::Name.first_name,
        last_name: Faker::Name.last_name,
        phone_number: nil,
        locale: user_locale
      }
    end

    initialize_with do
      params = {
        requestor: requestor_params,
        test_instance: test_instance_params,
        user: user_params
      }
      new(params:, current_user:, request:, test_profile_id:)
    end
  end
end
