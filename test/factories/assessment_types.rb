# == Schema Information
#
# Table name: assessment_types
#
#  id                     :integer          not null, primary key
#  assessment_time        :integer
#  json_calculation_rules :json
#  json_data              :json
#  name                   :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
FactoryBot.define do
  factory :assessment_type do
    json_data              { { 'questions' => [{ 'questionablesToDisplay' => [0, 1, 2, 3], 'assessmentQuestions' => [{ 'assessment_question_id' => 6, 'key' => 'overall_spoken_fluency', 'google_entry' => 'entry.128448655' }] }, { 'questionablesToDisplay' => [4, 5, 6], 'assessmentQuestions' => [{ 'assessment_question_id' => 9, 'key' => 'overall_writing', 'google_entry' => 'entry.1982074748' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 20, 'key' => 'assessment_robust_explanation', 'google_entry' => 'entry.704205072' }, { 'assessment_question_id' => 21, 'key' => 'assessment_cheating_explanation', 'google_entry' => 'entry.444996064' }, { 'assessment_question_id' => 18, 'key' => 'assessment_improvment', 'google_entry' => 'entry.1480324755' }, { 'assessment_question_id' => 14, 'key' => 'skills-description', 'google_entry' => 'entry.667541887' }] }] } }
    json_calculation_rules { { 'overall' => [{ 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }, { 'weight' => 1.0, 'key' => 'overall_writing' }], 'spoken' => [{ 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }], 'written' => [{ 'weight' => 1.0, 'key' => 'overall_writing' }] } }
    name                   { 'simple' }
    assessment_time        { 7 }

    trait(:with_assessment_questions) do
      transient do
        include_overall { true }
        types { %w[spoken written] }
        keys { types.map { |t| "#{t}_#{Time.now.to_i}" } }
        assessment_questions { create_list(:assessment_question, keys.length + (include_overall ? 1 : 0), :rating) }
        weights { [1.0, 1.0] }
      end

      name { 'simple' }
      json_calculation_rules { Hash[types.map.with_index { |type, index| [type, [{ weight: weights[index], key: keys[index] }]] }] } # rubocop:disable Style/HashConversion
      json_data do
        { questions: assessment_questions.map.with_index { |question, index| { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ assessment_question_id: question.id, key: keys[index] || Faker::Lorem.word }] } } }
      end

      after(:build) do |assessment_type, evaluator|
        if evaluator.include_overall
          assessment_type.json_calculation_rules[:overall] = assessment_type.json_calculation_rules.map { |_k, v| v[0] }
        end
      end
    end

    # Simple assessment type
    trait(:simple) do
      json_data              { { 'questions' => [{ 'questionablesToDisplay' => [0, 1, 2, 3], 'assessmentQuestions' => [{ 'assessment_question_id' => 6, 'key' => 'overall_spoken_fluency', 'google_entry' => 'entry.128448655' }] }, { 'questionablesToDisplay' => [4, 5, 6], 'assessmentQuestions' => [{ 'assessment_question_id' => 9, 'key' => 'overall_writing', 'google_entry' => 'entry.1982074748' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 20, 'key' => 'asssessment_robust_explanation', 'google_entry' => 'entry.704205072' }, { 'assessment_question_id' => 21, 'key' => 'assessment_cheating_explanation', 'google_entry' => 'entry.444996064' }, { 'assessment_question_id' => 18, 'key' => 'assessment_improvment', 'google_entry' => 'entry.1480324755' }, { 'assessment_question_id' => 14, 'key' => 'skills-description', 'google_entry' => 'entry.667541887' }] }] } }
      json_calculation_rules { { 'overall' => [{ 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }, { 'weight' => 1.0, 'key' => 'overall_writing' }], 'spoken' => [{ 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }], 'written' => [{ 'weight' => 1.0, 'key' => 'overall_writing' }] } }
      name                   { 'simple' }
      assessment_time        { 7 }
    end

    # Detailed assessment type
    trait(:detailed) do
      json_data              { { 'questions' => [{ 'questionablesToDisplay' => [0], 'assessmentQuestions' => [{ 'assessment_question_id' => 1, 'key' => 'phonological_control_1', 'google_entry' => 'entry.122374946' }] }, { 'questionablesToDisplay' => [1, 2], 'assessmentQuestions' => [{ 'assessment_question_id' => 1, 'key' => 'phonological_control_2_3', 'google_entry' => 'entry.1581966851' }, { 'assessment_question_id' => 2, 'key' => 'grammatical_accuracy_2_3', 'google_entry' => 'entry.888723862' }] }, { 'questionablesToDisplay' => [3], 'assessmentQuestions' => [{ 'assessment_question_id' => 3, 'key' => 'listening_4', 'google_entry' => 'entry.1912596366' }, { 'assessment_question_id' => 1, 'key' => 'phonological_control_4', 'google_entry' => 'entry.1223975733' }, { 'assessment_question_id' => 2, 'key' => 'grammatical_accuracy_4', 'google_entry' => 'entry.1786950336' }, { 'assessment_question_id' => 4, 'key' => 'vocabulary_range_4', 'google_entry' => 'entry.1222525773' }, { 'assessment_question_id' => 5, 'key' => 'coherence_cohesion_4', 'google_entry' => 'entry.1554372908' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 6, 'key' => 'overall_spoken_fluency', 'google_entry' => 'entry.128448655' }] }, { 'questionablesToDisplay' => [4], 'assessmentQuestions' => [{ 'assessment_question_id' => 7, 'key' => 'grammatical_accuracy_5', 'google_entry' => 'entry.1203467641' }] }, { 'questionablesToDisplay' => [5], 'assessmentQuestions' => [{ 'assessment_question_id' => 8, 'key' => 'reading_comprehension_6', 'google_entry' => 'entry.781207062' }, { 'assessment_question_id' => 15, 'key' => 'grammatical_accuracy_6', 'google_entry' => 'entry.1518212951' }, { 'assessment_question_id' => 4, 'key' => 'vocabulary_range_6', 'google_entry' => 'entry.836554058' }, { 'assessment_question_id' => 19, 'key' => 'coherence_cohesion_6', 'google_entry' => 'entry.1729087157' }] }, { 'questionablesToDisplay' => [6], 'assessmentQuestions' => [{ 'assessment_question_id' => 15, 'key' => 'grammatical_accuracy_7', 'google_entry' => 'entry.143868909' }, { 'assessment_question_id' => 4, 'key' => 'vocabulary_range_7', 'google_entry' => 'entry.1622426097' }, { 'assessment_question_id' => 19, 'key' => 'coherence_cohesion_7', 'google_entry' => 'entry.158142824' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 9, 'key' => 'overall_writing', 'google_entry' => 'entry.1982074748' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 10, 'key' => 'strenghts', 'google_entry' => 'entry.1635287695' }, { 'assessment_question_id' => 11, 'key' => 'other-strenghts', 'google_entry' => 'entry.1414892458' }, { 'assessment_question_id' => 12, 'key' => 'recomandation', 'google_entry' => 'entry.1012064561' }, { 'assessment_question_id' => 13, 'key' => 'other-recomandation', 'google_entry' => 'entry.1967490645' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 14, 'key' => 'skills-description', 'google_entry' => 'entry.667541887' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 20, 'key' => 'asssessment_robust_explanation', 'google_entry' => 'entry.704205072' }, { 'assessment_question_id' => 21, 'key' => 'assessment_cheating_explanation', 'google_entry' => 'entry.444996064' }, { 'assessment_question_id' => 18, 'key' => 'assessment_improvment', 'google_entry' => 'entry.1480324755' }] }] } }
      json_calculation_rules { { 'overall' => [{ 'weight' => 0.3333333333333333, 'key' => 'phonological_control_1' }, { 'weight' => 0.3333333333333333, 'key' => 'phonological_control_2_3' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_2_3' }, { 'weight' => 0.3333333333333333, 'key' => 'phonological_control_4' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_4' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_4' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_4' }, { 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }, { 'weight' => 0.2, 'key' => 'listening_4' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_5' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_6' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_6' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_6' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_7' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_7' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_7' }, { 'weight' => 1.0, 'key' => 'overall_writing' }, { 'weight' => 0.2, 'key' => 'reading_comprehension_6' }], 'spoken' => [{ 'weight' => 0.3333333333333333, 'key' => 'phonological_control_1' }, { 'weight' => 0.3333333333333333, 'key' => 'phonological_control_2_3' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_2_3' }, { 'weight' => 0.3333333333333333, 'key' => 'phonological_control_4' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_4' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_4' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_4' }, { 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }, { 'weight' => 0.2, 'key' => 'listening_4' }], 'written' => [{ 'weight' => 0.2, 'key' => 'grammatical_accuracy_5' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_6' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_6' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_6' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_7' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_7' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_7' }, { 'weight' => 1.0, 'key' => 'overall_writing' }, { 'weight' => 0.2, 'key' => 'reading_comprehension_6' }], 'academic' => [{ 'weight' => 0.2, 'key' => 'grammatical_accuracy_2_3' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_4' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_4' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_6' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_6' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_7' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_7' }, { 'weight' => 0.2, 'key' => 'listening_4' }, { 'weight' => 0.2, 'key' => 'reading_comprehension_6' }], 'pronunciation' => [{ 'weight' => 0.3333333333333333, 'key' => 'phonological_control_1' }, { 'weight' => 0.3333333333333333, 'key' => 'phonological_control_2_3' }, { 'weight' => 0.3333333333333333, 'key' => 'phonological_control_4' }], 'spokenFluency' => [{ 'weight' => 1.0, 'key' => 'overall_spoken_fluency' }], 'grammar' => [{ 'weight' => 0.2, 'key' => 'grammatical_accuracy_2_3' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_4' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_5' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_6' }, { 'weight' => 0.2, 'key' => 'grammatical_accuracy_7' }], 'vocabulary' => [{ 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_4' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_6' }, { 'weight' => 0.3333333333333333, 'key' => 'vocabulary_range_7' }], 'coherence' => [{ 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_4' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_6' }, { 'weight' => 0.3333333333333333, 'key' => 'coherence_cohesion_7' }] } }
      name                   { 'detailed' }
      assessment_time        { 15 }
    end

    # Detailed assessment type
    trait(:written) do
      json_data              { { 'questions' => [{ 'questionablesToDisplay' => [0, 1, 2, 3, 4], 'assessmentQuestions' => [{ 'assessment_question_id' => 9, 'key' => 'overall_writing' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 34, 'key' => 'strenghts' }, { 'assessment_question_id' => 35, 'key' => 'recomandation' }, { 'assessment_question_id' => 14, 'key' => 'skills-description' }, { 'assessment_question_id' => 20, 'key' => 'asssessment_robust_explanation' }, { 'assessment_question_id' => 21, 'key' => 'assessment_cheating_explanation' }, { 'assessment_question_id' => 18, 'key' => 'assessment_improvment' }] }] } }
      json_calculation_rules { { 'overall' => [{ 'weight' => 1.0, 'key' => 'overall_writing' }], 'written' => [{ 'weight' => 1.0, 'key' => 'overall_writing' }] } }
      name                   { 'written' }
      assessment_time        { 7 }
    end

    # Detailed assessment type
    trait(:spoken) do
      json_data              { { 'questions' => [{ 'questionablesToDisplay' => [0, 1, 2, 3], 'assessmentQuestions' => [{ 'assessment_question_id' => 9, 'key' => 'overall_writing' }] }, { 'questionablesToDisplay' => [], 'assessmentQuestions' => [{ 'assessment_question_id' => 34, 'key' => 'strenghts' }, { 'assessment_question_id' => 35, 'key' => 'recomandation' }, { 'assessment_question_id' => 14, 'key' => 'skills-description' }, { 'assessment_question_id' => 20, 'key' => 'asssessment_robust_explanation' }, { 'assessment_question_id' => 21, 'key' => 'assessment_cheating_explanation' }, { 'assessment_question_id' => 18, 'key' => 'assessment_improvment' }] }] } }
      json_calculation_rules { { 'overall' => [{ 'weight' => 1.0, 'key' => 'overall_writing' }], 'written' => [{ 'weight' => 1.0, 'key' => 'overall_writing' }] } }
      name                   { 'spoken' }
      assessment_time        { 7 }
    end
  end
end
