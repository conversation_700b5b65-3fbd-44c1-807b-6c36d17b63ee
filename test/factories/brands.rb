# == Schema Information
#
# Table name: brands
#
#  id                                     :integer          not null, primary key
#  additional_css                         :text
#  application_branded                    :boolean          default(FALSE)
#  certificate_branded                    :boolean          default(FALSE)
#  certificate_logo_content_type          :string
#  certificate_logo_file_name             :string
#  certificate_logo_file_size             :bigint
#  certificate_logo_updated_at            :datetime
#  client_name                            :string
#  custom_certificate                     :string
#  custom_style_attributes                :jsonb
#  generated_css                          :text
#  group_name                             :string
#  header_banner_certificate_content_type :string
#  header_banner_certificate_file_name    :string
#  header_banner_certificate_file_size    :bigint
#  header_banner_certificate_updated_at   :datetime
#  hide_end_of_test_forms                 :boolean          default(FALSE)
#  is_default                             :boolean
#  logo_content_type                      :string
#  logo_file                              :string
#  logo_file_name                         :string
#  logo_file_size                         :integer
#  logo_updated_at                        :datetime
#  name                                   :string
#  show_toeic_score_on_certificate        :boolean
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#
FactoryBot.define do
  factory :brand do
    name  { "#{Faker::Color.color_name} #{Faker::Number.number}" }
    group_name { "Api::#{self.name}" }
    client_name { Faker::Company.name }
    hide_end_of_test_forms { false }

    trait :application_branded do
      application_branded { true }
    end
  end
end
