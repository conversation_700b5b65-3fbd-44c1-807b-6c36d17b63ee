# == Schema Information
#
# Table name: invoice_requests
#
#  id                              :integer          not null, primary key
#  bank_account_currency           :string           default("EUR")
#  detailed_evals_counter          :integer
#  examiner_invoice_reference      :string           default("")
#  invoice_currency                :string           default("EUR")
#  invoice_pdf_content_type        :string
#  invoice_pdf_file_name           :string
#  invoice_pdf_file_size           :bigint
#  invoice_pdf_updated_at          :datetime
#  invoice_received_at             :datetime
#  json_examiner_fees              :json
#  month                           :string
#  paid_at                         :datetime
#  request_sent_at                 :datetime
#  requested_amount                :float
#  requested_amount_evaluations    :float
#  requested_amount_other_services :float
#  simple_evals_counter            :integer
#  spoken_evals_counter            :integer          default(0)
#  status                          :string
#  total_in_fee_currency           :float
#  validated_at                    :datetime
#  validated_auto_at               :datetime
#  vat_amount                      :float            default(0.0)
#  written_evals_counter           :integer          default(0)
#  created_at                      :datetime
#  examiner_id                     :integer
#
FactoryBot.define do
  factory :invoice_request do
    status { :created }
    invoice_currency { 'EUR' }
    association :examiner
    month { Time.zone.today.strftime('%Y-%m') }

    trait :with_evaluations do
      after :create do |invoice_request|
        FactoryBot.create(:evaluation, :delivered, invoice_request:)
        FactoryBot.create(:evaluation, :ct_written, invoice_request:)
        FactoryBot.create(:evaluation, :ct_spoken, invoice_request:)
        FactoryBot.create(:evaluation, :ct_detailed, invoice_request:)
      end
    end
    trait :with_assessed_evaluations do
      after :create do |invoice_request|
        create_list(:evaluation, 6, :assessed, invoice_request:, examiner: invoice_request.examiner)
      end
    end
  end
end
