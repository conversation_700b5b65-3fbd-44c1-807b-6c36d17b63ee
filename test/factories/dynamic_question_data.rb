# == Schema Information
#
# Table name: dynamic_question_data
#
#  id                  :integer          not null, primary key
#  anonymized_at       :datetime
#  data                :text             default({})
#  dqd_able_type       :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  dqd_able_id         :integer
#  question_element_id :integer
#  user_id             :integer
#
# Indexes
#
#  index_dynamic_question_data_on_dqd_able_id  (dqd_able_id)
#
FactoryBot.define do
  factory :dynamic_question_datum do
    association :user
    association :question_element
    dqd_able_type { 'Reception' }
    data { {} }
    dqd_able_id { FactoryBot.create(:reception).id }

    transient do
      recording { create(:recording) }
    end

    trait :with_text_answer do
      data { { answer: Faker::Lorem.paragraph_by_chars(number: 100) } }
    end

    trait :with_audio_answer do
      data { { answer: Faker::Internet.url, recording_id: recording&.id } }
    end
  end
end
