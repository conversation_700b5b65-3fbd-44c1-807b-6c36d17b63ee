# == Schema Information
#
# Table name: receptions
#
#  id               :integer          not null, primary key
#  answered_at      :datetime
#  deleted_at       :datetime
#  displayed_at     :datetime
#  status           :integer          default("unready")
#  created_at       :datetime
#  updated_at       :datetime
#  challenge_id     :integer
#  production_id    :integer
#  test_instance_id :integer
#  user_id          :integer
#
# Indexes
#
#  index_receptions_on_challenge_id  (challenge_id)
#  index_receptions_on_status        (status)
#  index_receptions_on_user_id       (user_id)
#  receptiond_production_index       (status,test_instance_id) WHERE (deleted_at IS NULL)
#  receptions_production_index       (production_id) WHERE (deleted_at IS NULL)
#
FactoryBot.define do
  factory :reception do
    association :challenge
    association :reception_user, factory: :user
    association :test_instance

    status { :completed }

    transient do
      section { :written }
      answer { section == :spoken ? Faker::Internet.url : Faker::Lorem.paragraph_by_chars(number: 100) }
    end

    after(:create) do |reception, evaluator|
      type = evaluator.section == :spoken ? 'Questions::QMicRecorder' : 'Questions::QTextArea'
      create(:dynamic_question_datum,
             dqd_able_type: 'Reception',
             dqd_able_id: reception.id,
             user: reception.reception_user,
             question_element: reception.challenge.questions.where(type:).first.question_elements.first,
             data: { answer: evaluator.answer })
    end
  end
end
