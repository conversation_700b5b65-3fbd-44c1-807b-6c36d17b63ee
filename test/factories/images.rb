# == Schema Information
#
# Table name: images
#
#  id                :integer          not null, primary key
#  attachable_type   :string
#  caption           :text
#  data_content_type :string
#  data_file_name    :string
#  data_file_size    :integer
#  data_updated_at   :datetime
#  name              :string
#  position          :integer          default(0)
#  created_at        :datetime
#  updated_at        :datetime
#  attachable_id     :integer
#
FactoryBot.define do
  factory :image do
    name { Faker::Lorem.word }
    data_file_name { "#{Faker::Lorem.word}.jpg" }
    data_content_type { 'image/jpeg' }
    data_file_size { 1024 }
    data_updated_at { Time.now }
  end
end
