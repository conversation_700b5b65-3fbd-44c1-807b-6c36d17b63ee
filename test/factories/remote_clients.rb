# == Schema Information
#
# Table name: remote_clients
#
#  id                          :integer          not null, primary key
#  client_name                 :string
#  client_type                 :string
#  deleted_at                  :datetime
#  rule_name                   :string
#  status                      :integer          default("disabled")
#  test_language               :string
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  api_user_id                 :integer
#  pipplet_clients_account_id  :integer
#  pipplet_clients_campaign_id :integer
#
FactoryBot.define do
  factory :remote_client do
    association :api_user

    client_name { Faker::Company.name }
    client_type { SOURCE_CLIENT_TYPES.values.sample }
    pipplet_clients_account_id { nil }
    pipplet_clients_campaign_id { nil }
    rule_name { "#{Time.now.to_i}#{client_name&.parameterize || Faker::Company.name}" }
    status { :enabled }
    test_language { AVAILABLE_TEST_LANGUAGES.sample }
  end
end
