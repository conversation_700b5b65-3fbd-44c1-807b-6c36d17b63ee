require 'test_helper'

class TestInstancesServiceTest < ActiveSupport::TestCase
  # VERSION 1
  #
  # # A valid test profile name should match
  # test '#test_profile_id should return the proper ID based on API_AVAILABLE_PROFILES_ID (V1)' do
  #   profile_key = (API_AVAILABLE_PROFILES_ID.keys - ['talent_ai']).sample
  #   test_profile_id = API_AVAILABLE_PROFILES_ID[profile_key]
  #   TestProfile.stubs(:exists?).returns(true)
  #   test_instance_service = FactoryBot.build(:test_instance_service, version: 1, current_user: @api_user, test_profile_id:)
  #   assert_equal test_profile_id, test_instance_service.send(:test_profile_id)
  # end
  #
  # # VERSION 2
  #
  # test '#test_profile_id should return the proper ID based on API_AVAILABLE_PROFILES_ID (V2)' do
  #   profile_key = (API_AVAILABLE_PROFILES_ID_V2.keys - ['talent_ai']).sample
  #   TestProfile.stubs(:exists?).returns(true)
  #   test_instance_service = FactoryBot.build(:test_instance_service, version: 2, current_user: @api_user, profile: profile_key)
  #   assert_equal API_AVAILABLE_PROFILES_ID_V2[profile_key], test_instance_service.send(:test_profile_id)
  # end
  #
  # # An empty test profile name should match the default test profile
  # test 'test empty test profile' do
  #   test_instance_service = FactoryBot.build(:test_instance_service, current_user: @api_user, profile: nil)
  #   @api_user.update_column(:default_test_profile_id, nil)
  #   assert_equal API_AVAILABLE_PROFILES_ID_V2['talent'], test_instance_service.send(:test_profile_id)
  # end
  #
  # # An empty test profile name should match the default test profile present inside the ApiUser default_test_profile_id field
  # test 'test empty test profile with default test profile' do
  #   test_instance_service = FactoryBot.build(:test_instance_service, current_user: @api_user, profile: nil)
  #   @api_user.update_column(:default_test_profile_id, TestProfile.first.id)
  #   assert_equal TestProfile.first.id, test_instance_service.send(:test_profile_id)
  # end
  #
  # # An invalid test profile name should match the default test profile
  # test 'test invalid test profile' do
  #   test_instance_service = FactoryBot.build(:test_instance_service, current_user: @api_user, profile: 'Wrong Test Profile')
  #   @api_user.update_column(:default_test_profile_id, nil)
  #   assert_equal API_AVAILABLE_PROFILES_ID_V2['talent'], test_instance_service.send(:test_profile_id)
  # end
  #
  # # An invalid test profile name should match the default test profile present inside the ApiUser default_test_profile_id field
  # test 'test invalid test profile with default test profile' do
  #   test_instance_service = FactoryBot.build(:test_instance_service, current_user: @api_user, profile: 'Wrong Test Profile')
  #   @api_user.update_column(:default_test_profile_id, TestProfile.first.id)
  #   assert_equal TestProfile.first.id, @api_user.default_test_profile_id, test_instance_service.send(:test_profile_id)
  # end

  test '#call with valid test_language creates TI and User' do
    test_instance_service = FactoryBot.build(:test_instance_service)
    assert_difference(%w[TestInstance.count User.count], 1) do
      test_instance_service.call
      refute test_instance_service.send(:errors?)
    end
  end

  test '#call with invalid test_language errors and does not create TI and User' do
    test_profile = FactoryBot.create(:test_profile, api_exposed: true, test_languages: ['es'])
    test_profile.reload
    test_instance_service = FactoryBot.build(:test_instance_service, test_language: 'en', test_profile_id: test_profile.id)
    test_instance_service.expects(:create_test_instance).never
    assert_no_difference(%w[TestInstance.count User.count], 1) do
      test_instance_service.call
      assert_equal ['wrong test language code for profile'], test_instance_service.test_instance_errors[1]['errors']['test_language']
    end
  end

  test '#call profile with valid test_language creates a TI and a User' do
    test_profile = FactoryBot.create(:test_profile, api_exposed: true, test_languages: ['en'])
    test_profile.reload
    test_instance_service = FactoryBot.build(:test_instance_service, test_language: 'en', test_profile_id: test_profile.id)
    assert_difference(%w[TestInstance.count User.count], 1) do
      test_instance_service.call
      refute test_instance_service.send(:errors?)
    end
  end

  test '#call with check_identity=false and api_user.force_identity_check = false should set skip_identity_check? to true' do
    current_user = FactoryBot.create(:api_user, force_identity_check: false)
    FactoryBot.build(:test_instance_service, current_user:, check_identity: false).call
    assert TestInstance.last.skip_identity_check?
  end

  test '#call with check_identity=true and api_user.force_identity_check = false should set skip_identity_check? to false' do
    current_user = FactoryBot.create(:api_user, force_identity_check: false)
    FactoryBot.build(:test_instance_service, current_user:, check_identity: true).call
    refute TestInstance.last.skip_identity_check?
  end

  test '#call with check_identity=false and api_user.force_identity_check = true should set skip_identity_check? to false' do
    current_user = FactoryBot.create(:api_user, force_identity_check: true)
    FactoryBot.build(:test_instance_service, current_user:, check_identity: false).call
    refute TestInstance.last.skip_identity_check?
  end

  test '#call with check_identity=true and api_user.force_identity_check = true should set skip_identity_check? to false' do
    current_user = FactoryBot.create(:api_user, force_identity_check: true)
    FactoryBot.build(:test_instance_service, current_user:, check_identity: true).call
    refute TestInstance.last.skip_identity_check?
  end

  test '#call with check_identity=nil and api_user.force_identity_check = true should set skip_identity_check? to false' do
    current_user = FactoryBot.create(:api_user, force_identity_check: true)
    FactoryBot.build(:test_instance_service, current_user:, check_identity: nil).call
    refute TestInstance.last.skip_identity_check?
  end

  test '#call with check_identity=nil and api_user.force_identity_check = false should set skip_identity_check? to true' do
    current_user = FactoryBot.create(:api_user, force_identity_check: false)
    FactoryBot.build(:test_instance_service, current_user:, check_identity: nil).call
    assert TestInstance.last.skip_identity_check?
  end

  test '#call allows nil language_name' do
    api_user = FactoryBot.create(:api_user, default_locale: nil)
    test_instance_service = FactoryBot.build(:test_instance_service, user_locale: nil, current_user: api_user)
    test_instance_service.call
    assert_nil User.last.language_name
    assert Language.default, User.last.locale
  end

  test '#call defaults to api_user#default_locale' do
    locale = Language.available.sample.to_s
    api_user = FactoryBot.create(:api_user, default_locale: locale)
    test_instance_service = FactoryBot.build(:test_instance_service, user_locale: nil, current_user: api_user)
    test_instance_service.call
    assert_equal locale, User.last.language_name
    assert locale, User.last.locale
  end

  test '#call defines a valid locale' do
    user_locale = Language.available.sample.to_s
    default_locale = (Language.available - [user_locale.to_sym]).sample.to_s
    api_user = FactoryBot.create(:api_user, default_locale:)
    test_instance_service = FactoryBot.build(:test_instance_service, user_locale:, current_user: api_user)
    test_instance_service.call
    assert_equal user_locale, User.last.language_name
    refute_equal default_locale, User.last.language_name
    assert user_locale, User.last.locale
  end

  test '#call does not define an invalid locale (falls back to default_locale)' do
    user_unavailable_locale = (I18n.available_locales - Language.available).sample.to_s
    default_locale = (Language.available - [user_unavailable_locale.to_sym]).sample.to_s
    api_user = FactoryBot.create(:api_user, default_locale:)
    test_instance_service = FactoryBot.build(:test_instance_service, user_locale: user_unavailable_locale, current_user: api_user)
    test_instance_service.call
    assert_equal default_locale, User.last.language_name
    refute_equal user_unavailable_locale, User.last.language_name
    assert default_locale, User.last.locale
  end

  test '#call falls back to default_test_language if test language is nil' do
    default_test_language = AVAILABLE_TEST_LANGUAGES.map(&:to_s).sample
    test_profile = FactoryBot.create(:test_profile, api_exposed: true, test_languages: [default_test_language])
    test_profile.reload
    api_user = FactoryBot.create(:api_user, default_test_language:)

    assert_difference('TestInstance.count', 1) do
      test_instance_service = FactoryBot.build(:test_instance_service, test_language: nil,
                                                                       current_user: api_user,
                                                                       test_profile_id: test_profile.id)
      test_instance_service.call
      assert_equal default_test_language, TestInstance.last.test_language
    end
  end

  test '#call returns an error if no test_language and no default_test_language)' do
    api_user = FactoryBot.create(:api_user, default_test_language: nil)

    assert_no_difference('TestInstance.count') do
      test_instance_service = FactoryBot.build(:test_instance_service, test_language: nil,
                                                                       current_user: api_user)
      test_instance_service.call
      assert_equal ['wrong test language code for profile'], test_instance_service.test_instance_errors[1]['errors']['test_language']
    end
  end
end
