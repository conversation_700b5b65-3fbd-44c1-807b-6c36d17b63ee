require 'test_helper'

class EvaluationsManagerServiceTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper
  include AITestHelper
  include AITestHelper::OpenAi
  include FilesTestHelper
  include AwsHelper
  include EvaluationsTestHelper

  def setup
    stubs_ai_config
    stubs_ai_api_response
    stubs_s3_buckets
  end

  #####################
  # Callbacks related #
  #####################

  test 'update_reference_evaluation (evaluation.manage_evaluations_before_assign) triggers only on reference_evaluation' do
    test_instance = FactoryBot.create(:test_instance, :sent_for_evaluation)
    reference_evaluation = test_instance.reference_evaluation
    Evaluation::EVALUATION_GOAL.each do |goal|
      test_instance.add_and_assign_evaluation(evaluation_goal: goal)
      assert_equal reference_evaluation, test_instance.reference_evaluation
    end
  end

  test 'update_reference_evaluation (evaluation.manage_evaluations_before_assign) Standard' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.standards.take
  end

  test 'update_reference_evaluation (evaluation.manage_evaluations_before_assign) Client_grade_check' do
    client_config = FactoryBot.create(:client_config, systematic_peer_review: 1, systematic_peer_review_counter: 10)
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed, client_config:, test_language: non_ai_language)

    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    assert_equal test_instance.reference_evaluation, test_instance.evaluations.client_grade_checks.take
    client_config.reload
    assert_equal 9, client_config.systematic_peer_review_counter
  end

  test 'update_reference_evaluation (evaluation.manage_evaluations_before_assign) Multiple_evaluations' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed)

    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.multiple_evaluations.take
  end

  test 'assign_reference_evaluation (test_instance.manage_evaluations_after_sent_for_evaluation) non talent_ai' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)

    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    assert_equal 1, test_instance.evaluations.count
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.standards.take
    assert test_instance.reference_evaluation.examiner.real?
  end

  test 'assign_reference_evaluation (test_instance.manage_evaluations_after_sent_for_evaluation) non talent_ai (from CC)' do
    ai_language = AI_ENABLED_LANGUAGES.sample.to_s
    test_instance = FactoryBot.create(:test_instance,
                                      :completed,
                                      test_language: ai_language,
                                      client_config: FactoryBot.create(:client_config,
                                                                       disable_ai_assessments: true,
                                                                       systematic_peer_review: 0))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal 1, test_instance.evaluations.count
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.standards.take
    assert test_instance.reference_evaluation.examiner.real?
  end

  test 'create_initial_peer_reviews (test_instance.manage_evaluations_after_sent_for_evaluation) no qc_ai' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    assert_equal 1, test_instance.evaluations.count
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.standards.take
    assert test_instance.reference_evaluation.examiner.real?
  end

  test 'create_initial_peer_reviews (test_instance.manage_evaluations_after_sent_for_evaluation) no qc_ai (from CC)' do
    ai_language = AVAILABLE_TEST_LANGUAGES.sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: ai_language,
                                      client_config: FactoryBot.create(:client_config,
                                                                       disable_ai_assessments: true,
                                                                       systematic_peer_review: 0))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    assert_equal 1, test_instance.evaluations.count
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.standards.take
    assert test_instance.reference_evaluation.examiner.real?
  end

  test 'create_initial_peer_reviews (test_instance.manage_evaluations_after_sent_for_evaluation) regrade no qc_ai' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :graded, test_language: non_ai_language)
    assert test_instance.graded?
    test_instance.regrade!
    assert test_instance.evaluations.reload.last.peer_review?
    # TODO: out of scope
    assert test_instance.sent_for_evaluation?
  end

  test 'create_initial_peer_reviews (test_instance.manage_evaluations_after_sent_for_evaluation) regrade no qc_ai (from CC)' do
    ai_language = AVAILABLE_TEST_LANGUAGES.sample.to_s

    test_instance = FactoryBot.create(:test_instance,
                                      :graded,
                                      test_language: ai_language,
                                      client_config: FactoryBot.create(:client_config,
                                                                       disable_ai_assessments: true))

    assert test_instance.graded?
    test_instance.regrade!
    assert test_instance.evaluations.reload.last.peer_review?
    # TODO: out of scope
    assert test_instance.sent_for_evaluation?
  end

  test 'create_initial_peer_reviews (test_instance.manage_evaluations_after_sent_for_evaluation) does not trigger for regrade + test_mode' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :graded, test_language: non_ai_language, test_mode: true)
    assert test_instance.graded?
    test_instance.regrade!
    assert_empty test_instance.evaluations.assigned
  end

  test 'create_initial_peer_reviews (test_instance.manage_evaluations_after_sent_for_evaluation) Multiple_evaluations' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed)

    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal 2, test_instance.evaluations.count
    assert_equal 1, test_instance.evaluations.peer_reviews.assigned.count
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=regrade evaluation=peer_review' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :graded, test_language: non_ai_language)
    test_instance.regrade!
    peer_review_evaluation = test_instance.evaluations.peer_reviews.last
    peer_review_evaluation = save_grade_and_certificate(evaluation: peer_review_evaluation)

    Evaluation.any_instance.expects(:handle_regraded).once
    peer_review_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=regrade evaluation!=peer_review' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :graded, test_language: non_ai_language)
    test_instance.regrade!
    evaluation = test_instance.add_and_assign_evaluation(evaluation_goal: (Evaluation.evaluation_goals - [:peer_review]).sample)

    Evaluation.any_instance.expects(:handle_regraded).never
    evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=regrade evaluation=peer_review examiner=ai' do
    test_instance = FactoryBot.create(:test_instance, :graded)
    test_instance.regrade!

    evaluation = test_instance.add_and_force_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review],
      examiner: Examiner.find_or_create_ai_examiner(lang: test_instance.test_language, ai_workflow: :qc)
    )

    Evaluation.any_instance.expects(:handle_regraded).never
    evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=client_grade_check evaluation=peer_review no ai' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_ai_language,
                                      client_config: FactoryBot.create(:client_config))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    peer_review_evaluation = test_instance.evaluations.peer_reviews.last
    peer_review_evaluation = save_grade_and_certificate(evaluation: peer_review_evaluation)

    TestInstance.any_instance.expects(:compute_client_check_eval).once
    peer_review_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=client_grade_check evaluation=peer_review no qc_ai from CC' do
    non_ai_language = AI_ENABLED_LANGUAGES.sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_ai_language,
                                      client_config: FactoryBot.create(:client_config,
                                                                       disable_ai_assessments: true))

    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    peer_review_evaluation = test_instance.evaluations.peer_reviews.last
    peer_review_evaluation = save_grade_and_certificate(evaluation: peer_review_evaluation)

    TestInstance.any_instance.expects(:compute_client_check_eval).once
    peer_review_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=client_grade_check evaluation=client_grade_check' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_ai_language,
                                      client_config: FactoryBot.create(:client_config))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    client_grade_check_evaluation = test_instance.evaluations.client_grade_checks.last
    client_grade_check_evaluation = save_grade_and_certificate(evaluation: client_grade_check_evaluation)
    TestInstance.any_instance.expects(:compute_client_check_eval).once
    client_grade_check_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=client_grade_check evaluation=standard no ai' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_ai_language,
                                      client_config: FactoryBot.create(:client_config))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    TestInstance.any_instance.expects(:compute_client_check_eval).never

    evaluation = test_instance.add_and_assign_evaluation(evaluation_goal: Evaluation::EVALUATION_GOAL[:standard])
    evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=standard evaluation=standard lang!=en' do
    non_en_language = (AVAILABLE_TEST_LANGUAGES - [:en]).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_en_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    reference_evaluation = test_instance.reference_evaluation

    EvaluationsManagerService.any_instance.expects(:create_holding_peer_review).never
    reference_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=standard evaluation=peer_review lang!=en' do
    non_en_language = (AVAILABLE_TEST_LANGUAGES - [:en]).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_en_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )

    EvaluationsManagerService.any_instance.expects(:create_holding_peer_review).never
    peer_review_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=standard evaluation=holding_peer_review no ai' do
    non_en_language = (AVAILABLE_TEST_LANGUAGES - [:en]).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_en_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    holding_peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:holding_peer_review]
    )

    EvaluationsManagerService.any_instance.expects(:create_and_assess_examiner_average).never
    holding_peer_review_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=multiple_evaluations evaluation=peer_review standard assessed' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    reference_evaluation = test_instance.reference_evaluation
    reference_evaluation = save_grade_and_certificate(evaluation: reference_evaluation)

    EvaluationsManagerService.any_instance.expects(:create_multiple_evaluations_client_grade_check).never
    reference_evaluation.assess!('assessed_at')

    peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )

    EvaluationsManagerService.any_instance.expects(:create_multiple_evaluations_client_grade_check).with(peer_review: peer_review_evaluation).once
    peer_review_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=multiple_evaluations evaluation=peer_review standard assigned' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    Examiner.delete_all
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )

    reference_evaluation = test_instance.reference_evaluation

    EvaluationsManagerService.any_instance.expects(:create_multiple_evaluations_client_grade_check).never
    reference_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=multiple_evaluations evaluation=standard peer_review assessed' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    FactoryBot.create_list(:examiner, 2, :requestable, language: non_ai_language)
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )

    EvaluationsManagerService.any_instance.expects(:create_multiple_evaluations_client_grade_check).never
    peer_review_evaluation = save_grade_and_certificate(evaluation: peer_review_evaluation)
    peer_review_evaluation.assess!('assessed_at')

    reference_evaluation = test_instance.reference_evaluation

    EvaluationsManagerService.any_instance.expects(:create_multiple_evaluations_client_grade_check).with(peer_review: peer_review_evaluation).once
    reference_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=multiple_evaluations evaluation=standard peer_review assigned' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )

    reference_evaluation = test_instance.reference_evaluation

    EvaluationsManagerService.any_instance.expects(:create_multiple_evaluations_client_grade_check).with(peer_review: peer_review_evaluation).never
    reference_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=standard ai_workflow=talent_ai' do
    stubs_ai_api_response
    test_instance = FactoryBot.create(:test_instance, :validations_passed_talent_ai)
    TestInstance.any_instance.expects(:manage_duplicates).never
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert test_instance.reference_evaluation.delivered?
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=standard ai_workflow!=talent_ai' do
    stubs_ai_api_response
    test_instance = FactoryBot.create(:test_instance, :validations_passed)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    TestInstance.any_instance.expects(:manage_duplicates).once
    reference_evaluation = test_instance.reference_evaluation
    reference_evaluation = save_grade_and_certificate(evaluation: reference_evaluation)
    reference_evaluation.assess!('assessed_at')
  end

  test 'on_evaluation_assessed (evaluation.manage_evaluations_after_assess) workflow=standard ai_workflow!=talent_ai (from CC)' do
    stubs_ai_api_response
    test_instance = FactoryBot.create(:test_instance,
                                      :validations_passed_talent_ai,
                                      client_config: FactoryBot.create(:client_config,
                                                                       disable_ai_assessments: true,
                                                                       systematic_peer_review: 0))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    TestInstance.any_instance.expects(:manage_duplicates).once
    reference_evaluation = test_instance.reference_evaluation
    reference_evaluation = save_grade_and_certificate(evaluation: reference_evaluation)
    reference_evaluation.assess!('assessed_at')
  end

  test 'create_client_grade_check_peer_reviews (evaluation.manage_evaluations_after_assign)' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_ai_language,
                                      client_config: FactoryBot.create(:client_config))
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal 2, test_instance.evaluations.count
    assert_equal 1, test_instance.evaluations.peer_reviews.assigned.count
  end

  test 'create_client_grade_check_peer_reviews (evaluation.manage_evaluations_after_assign) no examiner with enough assessment time' do
    Examiner.includes(:evaluations).destroy_all
    Examiner.delete_all

    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    evals_count = rand(1..5)
    # Examiner for Client_grade_check
    available_examiner = FactoryBot.create(:examiner, :requestable, language: non_ai_language)
    # Examiners who match usable_for but have insufficient max_assessment_time
    no_assessment_time_examiners = FactoryBot.create_list(:examiner, evals_count,
                                                          :requestable,
                                                          language: non_ai_language,
                                                          max_assessment_time: 1)

    assert_equal ([available_examiner.id] + no_assessment_time_examiners.pluck(:id)).sort, Examiner.usable_for(non_ai_language).pluck(:id).sort

    client_config = FactoryBot.create(:client_config, systematic_peer_review: evals_count)
    test_instance = FactoryBot.create(:test_instance, :completed,
                                      test_language: non_ai_language,
                                      client_config:)

    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    # reference evaluation + systematic_peer_review * peer_reviews
    assert_equal evals_count + 1, test_instance.evaluations.count
    # systematic_peer_review * peer_reviews
    assert_equal evals_count, test_instance.evaluations.peer_reviews.assigned.count
    # all assigned evaluations should be assigned to the admin_linguistic_examiner as no real examiner available
    assert_equal ["linguistics+adminexaminer#{test_instance.test_language}@pipplet.com"], test_instance.evaluations
                                                                                                       .includes(examiner: :user)
                                                                                                       .peer_reviews
                                                                                                       .assigned
                                                                                                       .map(&:examiner)
                                                                                                       .map(&:user)
                                                                                                       .pluck(:email)
                                                                                                       .uniq
  end

  test 'create_peer_review_interval_peer_reviews (evaluation.manage_evaluations_after_assign) peer_review_interval=0' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    examiner = FactoryBot.create(:examiner, :requestable, peer_review_interval: 0)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)

    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal 1, test_instance.evaluations.count
    assert_equal test_instance.reference_evaluation, test_instance.evaluations.take
  end

  test 'create_peer_review_interval_peer_reviews (evaluation.manage_evaluations_after_assign) peer_review_interval>0' do
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    evals_count = rand(3..10)
    FactoryBot.create_list(:examiner, evals_count - 3, :requestable, language: non_ai_language) # 3 created in seeds.rb
    examiner = FactoryBot.create(:examiner, :requestable, peer_review_interval: 1, peer_review_number: evals_count)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)

    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    assert_equal evals_count + 1, test_instance.evaluations.count
    assert_equal evals_count, test_instance.evaluations.peer_reviews.count
    assert_equal 0, examiner.number_of_eval_since_last_peer_review
  end

  test 'after_assess should not send a certificate if the test_instance is talent_ai and certificate validations fail (i.e. if a VIP user is set)' do
    stubs_ai_api_response
    user = FactoryBot.create(:user, :is_vip)
    test_instance = FactoryBot.create(:test_instance, :validations_passed_talent_ai, user:, test_language: 'en')
    Evaluation.any_instance.expects(:deliver_certificate).never
    perform_enqueued_jobs do
      test_instance.send_for_evaluation!
    end
  end

  test 'after_assess should send a certificate if the test_instance is talent_ai and certificate validations succeed' do
    stubs_ai_api_response
    test_instance = FactoryBot.create(:test_instance, :validations_passed_talent_ai, test_language: 'en')
    Evaluation.any_instance.expects(:deliver_certificate).once

    perform_enqueued_jobs do
      test_instance.send_for_evaluation!
    end
  end

  test 'assess! on a standard evaluation should not create a Quality_control_review if standard grade is C1 or above' do
    stub_generate_certificate_process
    stubs_ai_api_response(cecrl_grade: 'A1')

    default_evaluation = FactoryBot.create(:evaluation)
    default_evaluation.assign!('assigned_at')
    test_instance = FactoryBot.create(:test_instance, :validations_passed, test_language: 'en')
    ai_evaluation = test_instance.add_and_force_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review],
      examiner: Examiner.find_or_create_ai_examiner(lang: :en)
    )
    test_instance.update(evaluations: [default_evaluation, ai_evaluation])

    perform_enqueued_jobs do
      AiServiceEvalJob.perform_now(section: :spoken, evaluation_id: ai_evaluation.id)
      AiServiceEvalJob.perform_now(section: :written, evaluation_id: ai_evaluation.id)
    end

    certificate = FactoryBot.create(:certificate)
    certificate.grades << %i[spoken written overall].map { |skill| FactoryBot.create(:grade, label: skill, score: 90, cecrl_score: 'C1') }
    default_evaluation.update(certificate: certificate, json_assessment: {
                                'overall_spoken_fluency' => Faker::Lorem.paragraph,
                                'overall_writing' => 'C1',
                                'skills-description' => Faker::Lorem.paragraph
                              })

    perform_enqueued_jobs do
      # redo what is in the controller examiners/evaluations_controller.rb#submit_assessment - should be moved to assess! !!!
      default_evaluation.assess!('assessed_at')
      GenerateEvaluationCertificateJob.perform_later(default_evaluation.id) if default_evaluation.can_be_delivered?
    end

    test_instance.reload
    assert_equal 2, test_instance.evaluations.count
    default_evaluation     = test_instance.evaluations.standards.take
    peer_review_evaluation = test_instance.evaluations.peer_reviews.take

    assert default_evaluation.delivered?
    assert peer_review_evaluation.assessed?
    assert test_instance.graded?
  end

  ###################
  # Private methods #
  ###################

  test '#create_and_assess_examiner_average' do
    test_instance = FactoryBot.create(:test_instance, :sent_for_evaluation)
    certificate = FactoryBot.create(:certificate, :with_grades)
    standard_evaluation = test_instance.reference_evaluation
    standard_evaluation.update(certificate:)
    peer_review_evaluation = FactoryBot.create(:evaluation, :peer_review, test_instance:, certificate: FactoryBot.create(:certificate, :with_grades))
    EvaluationsManagerService.new(test_instance:).send(:create_and_assess_examiner_average, evaluation: peer_review_evaluation)
    admin_evaluation = Evaluation.find_by(test_instance_id: test_instance.id, examiner_id: Examiner.pipplet_admin_examiner(test_instance.test_language).id)
    assert_equal peer_review_evaluation.json_assessment, admin_evaluation.json_assessment
    %i[spoken written overall].each do |grade|
      assert_equal ((peer_review_evaluation.certificate.grades.send(grade).last.score + standard_evaluation.certificate.grades.send(grade).last.score) / 2), admin_evaluation.certificate.grades.send(grade).last.score
    end
  end

  test '#create_and_assess_examiner_average must create a certificate for an evaluation, with same grades as the peer_review one' do
    test_instance = FactoryBot.create(:test_instance, :sent_for_evaluation)
    stubs_ai_config
    standard_evaluation = test_instance.reference_evaluation
    standard_evaluation.update(certificate: FactoryBot.create(:certificate, :with_grades))
    peer_review_evaluation = FactoryBot.create(:evaluation, :peer_review, test_instance:, certificate: FactoryBot.create(:certificate, :with_grades))
    EvaluationsManagerService.new(test_instance:).send(:create_and_assess_examiner_average, evaluation: peer_review_evaluation)

    admin_evaluation = Evaluation.last
    assert_equal peer_review_evaluation.json_assessment, admin_evaluation.json_assessment
    assert_empty admin_evaluation.certificate.grades.map(&:label) - peer_review_evaluation.certificate.grades.map(&:label)
  end

  test 'Full Multiple_evaluations workflow score diff < 4' do
    client_stub = Aws::S3::Client.new(stub_responses: true)
    Rails.application.config.stubs(:s3).returns(
      bucket: Aws::S3::Resource.new(client: client_stub)
    )
    images = Array.new(rand(11..600)) { |i| { key: "image#{i}.pdf" } }
    client_stub.stub_responses(:list_objects_v2, { contents: images })

    Tag.find_or_create_by(name: 'multiple-evaluations')
    reference_score = CECRL_LEVELS_RANGE.keys.drop(4).sample # A2 >> C2+
    peer_review_score = CECRL_LEVELS_RANGE.keys[CECRL_LEVELS_RANGE.keys.find_index(reference_score) - rand(0..3)]
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )

    peer_review_evaluation = save_grade_and_certificate(evaluation: peer_review_evaluation,
                                                        score: ApplicationController.helpers.cecrl_score_to_max_score(peer_review_score))
    peer_review_evaluation.assess!('assessed_at')
    reference_evaluation = test_instance.reference_evaluation
    reference_evaluation = save_grade_and_certificate(evaluation: reference_evaluation,
                                                      score: ApplicationController.helpers.cecrl_score_to_max_score(reference_score))
    perform_enqueued_jobs { reference_evaluation.assess!('assessed_at') }

    test_instance.reload
    assert_equal 4, test_instance.evaluations.count
    examiner_average_evaluation = test_instance.evaluations.examiner_averages.take
    assert examiner_average_evaluation
    assert examiner_average_evaluation.delivered?
  end

  test 'Full Multiple_evaluations workflow score diff >= 4' do
    Tag.find_or_create_by(name: 'multiple-evaluations')
    reference_score = CECRL_LEVELS_RANGE.keys.drop(10).sample # B2 >> C2+
    peer_review_score = CECRL_LEVELS_RANGE.keys[CECRL_LEVELS_RANGE.keys.find_index(reference_score) - rand(4..10)]
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    FactoryBot.create(:examiner, :active, language: non_ai_language)
    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }

    peer_review_evaluation = test_instance.add_and_assign_evaluation(
      evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review]
    )
    peer_review_evaluation = save_grade_and_certificate(evaluation: peer_review_evaluation, score: ApplicationController.helpers.cecrl_score_to_max_score(peer_review_score))
    peer_review_evaluation.assess!('assessed_at')
    reference_evaluation = test_instance.reference_evaluation
    reference_evaluation = save_grade_and_certificate(evaluation: reference_evaluation, score: ApplicationController.helpers.cecrl_score_to_max_score(reference_score))
    perform_enqueued_jobs { reference_evaluation.assess!('assessed_at') }

    test_instance.reload
    assert_equal 3, test_instance.evaluations.count
    assert_empty test_instance.evaluations.examiner_averages
  end
end
