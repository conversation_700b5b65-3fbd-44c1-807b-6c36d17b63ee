require 'test_helper'

class AiService::FillJsonAssessmentTest < ActiveSupport::TestCase
  def setup
    @evaluation = FactoryBot.create(:evaluation, test_instance: FactoryBot.create(:test_instance, :sent_for_evaluation))
    @written_score = rand(0..100)
    @spoken_score = rand(0..100)
  end

  test '#fill_json_assessment, error' do
    error_message = Faker::Company.bs
    AiService.fill_json_assessment(section: :spoken, evaluation: @evaluation, error_message:)
    AiService.fill_json_assessment(section: :written, evaluation: @evaluation, error_message:)

    expected_assessment = {
      'overall_spoken_fluency' => 'No oral answers were provided',
      'overall_writing' => 'No written answers were provided',
      'assessment_robust_explanation' => 'other',
      'assessment_robust_explanation-Comment' => <<-HEREDOC.squish
                                                          An error occurred from :
                                                          spoken: #{@spoken_type}AI
                                                          Message : #{error_message} |
                                                          An error occurred from :
                                                          written: #{@written_type}AI
                                                          Message : #{error_message}
      HEREDOC
    }

    assert_equal expected_assessment, @evaluation.json_assessment
  end

  test '#fill_json_assessment simple assessment, both scored' do
    AiService.fill_json_assessment(section: :spoken, evaluation: @evaluation, score: @spoken_score)
    AiService.fill_json_assessment(section: :written, evaluation: @evaluation, score: @written_score)

    expected_assessment = @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: :written, score: @written_score).merge(
      @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: :spoken, score: @spoken_score)
    )

    assert_equal expected_assessment, @evaluation.json_assessment
  end

  test '#fill_json_assessment, both nil (sections not answered)' do
    AiService.fill_json_assessment(section: :spoken, evaluation: @evaluation, score: nil)
    AiService.fill_json_assessment(section: :written, evaluation: @evaluation, score: nil)
    expected_assessment = {
      'overall_spoken_fluency' => 'No oral answers were provided',
      'overall_writing' => 'No written answers were provided'
    }

    assert_equal expected_assessment, @evaluation.json_assessment
  end

  test '#fill_json_assessment, spoken nil (section not answered)' do
    AiService.fill_json_assessment(section: :spoken, evaluation: @evaluation, score: nil)
    AiService.fill_json_assessment(section: :written, evaluation: @evaluation, score: @written_score)

    expected_assessment = { 'overall_spoken_fluency' => 'No oral answers were provided' }.merge(
      @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: :written, score: @written_score)
    )

    assert_equal expected_assessment, @evaluation.json_assessment
  end

  test '#fill_json_assessment, written nil (section not answered)' do
    AiService.fill_json_assessment(section: :spoken, evaluation: @evaluation, score: @spoken_score)
    AiService.fill_json_assessment(section: :written, evaluation: @evaluation, score: nil)
    expected_assessment = { 'overall_writing' => 'No written answers were provided' }.merge(
      @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: :spoken, score: @spoken_score)
    )

    assert_equal expected_assessment, @evaluation.json_assessment
  end

  test '#fill_json_assessment, both 0' do
    AiService.fill_json_assessment(section: :spoken, evaluation: @evaluation, score: 0)
    AiService.fill_json_assessment(section: :written, evaluation: @evaluation, score: 0)
    expected_assessment = @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: :written, score: 0).merge(
      @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: :spoken, score: 0)
    )

    assert_equal expected_assessment, @evaluation.json_assessment
  end
end
