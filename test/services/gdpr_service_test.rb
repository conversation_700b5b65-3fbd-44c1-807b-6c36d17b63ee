require 'test_helper'

class GdprServiceTest < ActiveSupport::TestCase
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def setup
    stubs_s3_buckets

    @anonymized_user = FactoryBot.create(:user)
    @anonymized_user.update_columns(created_at: DateTime.current - 2.days)

    @anonymized_test_instance = FactoryBot.create(:test_instance)
    @anonymized_test_instance.update_columns(created_at: DateTime.current - 2.days)
  end

  test 'anonymize_users anonymizes old users without testInstances' do
    GdprService.anonymize_users(last_created_at: DateTime.current - 1.day)
    assert_empty Alert.all
    assert @anonymized_user.reload.anonymized?
  end

  test 'anonymize_users does not anonymize recent users' do
    GdprService.anonymize_users(last_created_at: DateTime.current - 3.days)
    assert_empty Alert.all
    refute @anonymized_user.reload.anonymized?
  end

  test 'anonymize_users does not anonymize users updated recently but with old TestInstances only' do
    anonymized_user = FactoryBot.create(:user)
    FactoryBot.create(:test_instance, user: anonymized_user).update_columns(created_at: DateTime.current - 2.days)
    FactoryBot.create(:test_instance, user: anonymized_user).update_columns(created_at: DateTime.current - 3.days)

    GdprService.anonymize_users(last_created_at: DateTime.current - 1.day)

    assert_empty Alert.all
    refute anonymized_user.reload.anonymized?
  end

  test 'anonymize_users does not anonymize old users with recent TestInstances' do
    anonymized_user = FactoryBot.create(:user)
    FactoryBot.create(:test_instance, user: anonymized_user).update_columns(created_at: DateTime.current)
    FactoryBot.create(:test_instance, user: anonymized_user).update_columns(created_at: DateTime.current - 2.days)
    FactoryBot.create(:test_instance, user: anonymized_user).update_columns(created_at: DateTime.current - 3.days)

    GdprService.anonymize_users(last_created_at: DateTime.current - 1.day)

    assert_empty Alert.all
    refute anonymized_user.reload.anonymized?
  end

  test 'anonymize_test_instances anonymizes old TestInstances' do
    GdprService.anonymize_test_instances(last_created_at: DateTime.current - 1.day)
    assert_empty Alert.all
    assert @anonymized_test_instance.reload.anonymized?
  end

  test 'anonymize_test_instances does not anonymize recent TestInstances' do
    GdprService.anonymize_test_instances(last_created_at: DateTime.current - 3.days)
    assert_empty Alert.all
    refute @anonymized_test_instance.reload.anonymized?
  end

  test 'anonymize_test_instances_and_users' do
    TestInstance.delete_all
    User.without_role(:examiner).without_role(:admin).delete_all

    recent_ti = FactoryBot.create(:test_instance, :graded)
    recent_ti2 = FactoryBot.create(:test_instance, :graded)

    old_ti = FactoryBot.create(:test_instance, :graded)
    old_ti.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    old_ti2 = FactoryBot.create(:test_instance, :graded)
    old_ti2.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    recent_user_without_tis = FactoryBot.create(:user, first_name: 'recent')

    old_user_without_tis = FactoryBot.create(:user, first_name: 'old_user_without_tis')
    old_user_without_tis.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    old_user_with_old_tis = FactoryBot.create(:user, first_name: 'old_user_with_old_tis')
    old_ti3 = FactoryBot.create(:test_instance, :graded, user: old_user_with_old_tis)
    old_ti3.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)
    old_ti4 = FactoryBot.create(:test_instance, :graded, user: old_user_with_old_tis)
    old_ti4.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)
    old_user_with_old_tis.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    old_user_with_old_and_recent_tis = FactoryBot.create(:user, first_name: 'old_user_with_old_and_recent_tis')
    old_ti5 = FactoryBot.create(:test_instance, :graded, user: old_user_with_old_and_recent_tis)
    old_ti5.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)
    recent_ti3 = FactoryBot.create(:test_instance, :graded, user: old_user_with_old_and_recent_tis)
    old_user_with_old_and_recent_tis.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    old_user_with_recent_tis = FactoryBot.create(:user, first_name: 'old_user_with_recent_tis')
    recent_ti4 = FactoryBot.create(:test_instance, :graded, user: old_user_with_recent_tis)
    recent_ti5 = FactoryBot.create(:test_instance, :graded, user: old_user_with_recent_tis)
    old_user_with_recent_tis.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    recent_user_with_old_tis = FactoryBot.create(:user, first_name: 'recent_user_with_old_tis')
    old_ti6 = FactoryBot.create(:test_instance, :graded, user: recent_user_with_old_tis)
    old_ti6.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)
    old_ti7 = FactoryBot.create(:test_instance, :graded, user: recent_user_with_old_tis)
    old_ti7.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)

    recent_user_with_old_and_recent_tis = FactoryBot.create(:user, first_name: 'recent_user_with_old_and_recent_tis')
    old_ti8 = FactoryBot.create(:test_instance, :graded, user: recent_user_with_old_and_recent_tis)
    old_ti8.update_columns(created_at: 2.years.ago, updated_at: 2.years.ago)
    recent_ti6 = FactoryBot.create(:test_instance, :graded, user: recent_user_with_old_and_recent_tis)

    recent_user_with_recent_tis = FactoryBot.create(:user, first_name: 'recent_user_with_recent_tis')
    recent_ti7 = FactoryBot.create(:test_instance, :graded, user: recent_user_with_recent_tis)
    recent_ti8 = FactoryBot.create(:test_instance, :graded, user: recent_user_with_recent_tis)

    GdprService.anonymize_test_instances_and_users

    assert old_ti.reload.anonymized?
    assert old_ti2.reload.anonymized?
    assert old_ti3.reload.anonymized?
    assert old_ti4.reload.anonymized?
    assert old_ti5.reload.anonymized?
    assert old_ti6.reload.anonymized?
    assert old_ti7.reload.anonymized?
    assert old_ti8.reload.anonymized?

    refute recent_ti.reload.anonymized?
    refute recent_ti2.reload.anonymized?
    refute recent_ti3.reload.anonymized?
    refute recent_ti4.reload.anonymized?
    refute recent_ti5.reload.anonymized?
    refute recent_ti6.reload.anonymized?
    refute recent_ti7.reload.anonymized?
    refute recent_ti8.reload.anonymized?

    refute recent_user_without_tis.reload.anonymized?
    refute old_user_with_old_and_recent_tis.reload.anonymized?
    refute old_user_with_recent_tis.reload.anonymized?
    refute recent_user_with_old_tis.reload.anonymized?
    refute recent_user_with_old_and_recent_tis.reload.anonymized?
    refute recent_user_with_recent_tis.reload.anonymized?

    assert old_user_without_tis.reload.anonymized?
    assert old_user_with_old_tis.reload.anonymized?
  end

  test '.force_anonymize_user_and_test_instances' do
    user = FactoryBot.create(:user)
    test_instance = FactoryBot.create(:test_instance, user:)
    test_instance2 = FactoryBot.create(:test_instance, user:)
    assert GdprService.force_anonymize_user_and_test_instances(test_instance)
    assert user.reload.anonymized?
    assert test_instance.reload.anonymized?
    assert test_instance2.reload.anonymized?
  end

  test '.force_anonymize_user_and_test_instances errors' do
    user = FactoryBot.create(:user)
    test_instance = FactoryBot.create(:test_instance, user:)
    test_instance2 = FactoryBot.create(:test_instance, user:)
    TestInstance.any_instance.stubs(:anonymize!).raises(StandardError)
    refute GdprService.force_anonymize_user_and_test_instances(test_instance)
    refute user.reload.anonymized?
    refute test_instance.reload.anonymized?
    refute test_instance2.reload.anonymized?
    assert_equal ["StandardError (While Anonymizing User 'id'=#{user.id}) from TestInstance 'id'=#{test_instance.id}"], test_instance.errors.full_messages
  end
end
