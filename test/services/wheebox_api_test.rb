require 'test_helper'

class WheeboxApiTest < ActiveSupport::TestCase
  def setup
    # Use Faker to generate test URLs and credentials
    @base_url = Faker::Internet.url
    @user_name = Faker::Internet.username
    @password = Faker::Internet.password

    # Stub the Rails configuration using Mocha BEFORE creating the WheeboxApi object
    Rails.application.config.stubs(:wheebox_proctoring).returns({
                                                                  base_url: @base_url,
                                                                  user_name: @user_name,
                                                                  password: @password
                                                                })

    # NOW create the WheeboxApi object after the configuration is stubbed
    @wheebox_api = WheeboxApi.new
  end

  # ----------------------------------------
  # get_token tests
  # ----------------------------------------

  test 'get_token returns valid token response on success' do
    token_response_body = {
      'token' => 'valid_token_123',
      'expires' => '2024-12-31T23:59:59Z'
    }.to_json

    stub_request(:post, "#{@base_url}/getToken")
      .with(
        body: { userName: @user_name, password: @password }.to_json,
        headers: { 'Content-Type' => 'application/json', 'Accept' => 'application/json' }
      )
      .to_return(status: 200, body: token_response_body, headers: {})

    token_response = @wheebox_api.get_token

    assert_equal 'valid_token_123', token_response.token
    assert_equal '2024-12-31T23:59:59Z', token_response.expires
  end

  test 'get_token raises WheeboxError on HTTP error status' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 401, body: 'Unauthorized', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.get_token
    end

    assert_equal 'Failed to get token (HTTP 401)', error.message
  end

  test 'get_token raises WheeboxError on invalid JSON response' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: 'invalid json', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.get_token
    end

    assert_equal 'Invalid response format from Wheebox API when getting token.', error.message
  end

  test 'get_token raises WheeboxError on network timeout' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_timeout

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.get_token
    end

    assert_match(/Network error when requesting token/, error.message)
  end

  test 'get_token raises WheeboxError on connection refused' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_raise(Errno::ECONNREFUSED)

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.get_token
    end

    assert_match(/Network error when requesting token/, error.message)
  end

  # ----------------------------------------
  # register_candidate tests
  # ----------------------------------------

  test 'register_candidate returns valid registration data on success' do
    # Mock get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock register_candidate call
    registration_response_body = {
      'attemptId' => 'attempt_12345',
      'studentId' => 'student_67890',
      'eventId' => 'pipplet',
      'status' => 'registered'
    }.to_json

    stub_request(:post, "#{@base_url}/getRegistration")
      .with(
        body: {
          student_unique_id: 'user_uuid_123',
          event_id: 'pipplet',
          attemptnumber: 1,
          student_name: 'John',
          emailid: '<EMAIL>',
          is100msEnbale: true
        }.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json',
          'Authorization' => 'valid_token_123'
        }
      )
      .to_return(status: 200, body: registration_response_body, headers: {})

    result = @wheebox_api.register_candidate(
      'user_uuid_123',
      'pipplet',
      'John',
      1,
      '<EMAIL>'
    )

    assert_equal 'attempt_12345', result[:data]['attemptId']
    assert_equal 'valid_token_123', result[:token]
  end

  test 'register_candidate raises WheeboxError when get_token fails' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 401, body: 'Unauthorized', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.register_candidate('user_123', 'event_123', 'John', 1, '<EMAIL>')
    end

    assert_equal 'Failed to get token (HTTP 401)', error.message
  end

  test 'register_candidate raises WheeboxError on HTTP error status' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock failed registration call
    stub_request(:post, "#{@base_url}/getRegistration")
      .to_return(status: 403, body: 'Forbidden', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.register_candidate('user_123', 'event_123', 'John', 1, '<EMAIL>')
    end

    assert_equal 'Failed to register candidate (HTTP 403)', error.message
  end

  test 'register_candidate raises WheeboxError on invalid JSON response' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock registration call with invalid JSON
    stub_request(:post, "#{@base_url}/getRegistration")
      .to_return(status: 200, body: 'invalid json', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.register_candidate('user_123', 'event_123', 'John', 1, '<EMAIL>')
    end

    assert_equal 'Invalid response format from Wheebox API when registering candidate.', error.message
  end

  test 'register_candidate raises WheeboxError on network timeout' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock registration call timeout
    stub_request(:post, "#{@base_url}/getRegistration")
      .to_timeout

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.register_candidate('user_123', 'event_123', 'John', 1, '<EMAIL>')
    end

    assert_match(/Network error when registering candidate/, error.message)
  end

  # ----------------------------------------
  # end_test tests
  # ----------------------------------------

  test 'end_test returns valid response on success' do
    # Mock get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock end_test call
    end_test_response_body = {
      'status' => 'success',
      'message' => 'Test ended successfully',
      'attemptId' => 'attempt_12345'
    }.to_json

    stub_request(:post, "#{@base_url}/endTestRpass")
      .with(
        body: {
          code: '1549000',
          student_unique_id: 'attempt_12345'
        }.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json',
          'Authorization' => 'valid_token_123'
        }
      )
      .to_return(status: 200, body: end_test_response_body, headers: {})

    result = @wheebox_api.end_test('attempt_12345')

    assert_equal 'success', result['status']
    assert_equal 'Test ended successfully', result['message']
    assert_equal 'attempt_12345', result['attemptId']
  end

  test 'end_test raises WheeboxError when get_token fails' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 500, body: 'Internal Server Error', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.end_test('attempt_12345')
    end

    assert_equal 'Failed to get token (HTTP 500)', error.message
  end

  test 'end_test raises WheeboxError on HTTP error status' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock failed end_test call
    stub_request(:post, "#{@base_url}/endTestRpass")
      .to_return(status: 404, body: 'Not Found', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.end_test('attempt_12345')
    end

    assert_equal 'Failed to end test (HTTP 404)', error.message
  end

  test 'end_test raises WheeboxError on invalid JSON response' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock end_test call with invalid JSON
    stub_request(:post, "#{@base_url}/endTestRpass")
      .to_return(status: 200, body: 'invalid json', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.end_test('attempt_12345')
    end

    assert_equal 'Invalid response format from Wheebox API when ending test.', error.message
  end

  # ----------------------------------------
  # generate_page_url tests
  # ----------------------------------------

  test 'generate_page_url returns valid response on success' do
    # Mock get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock generate_page_url call
    page_url_response_body = {
      'attemptId' => 'attempt_12345',
      'attemptUrl' => 'https://wheebox.com/face-training/attempt_12345',
      'assessmentId' => 'assessment_67890'
    }.to_json

    stub_request(:post, 'https://wgc.wheebox.com/WheeboxRestService/generatepageURL')
      .with(
        body: {
          student_unique_id: 'user_uuid_123',
          custom_logo: '',
          custom_title: '',
          colortheam: '#1f1f19',
          return_url: 'https://example.com/callback',
          event_id: 'pipplet',
          param: '',
          pagetype: 'train',
          autoapproval: true,
          attemptnumber: 1,
          captureimage: 'both',
          dob: '2000-01-01',
          father_name: 'Doe',
          fullname: 'Doe, John'
        }.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json',
          'Authorization' => 'valid_token_123'
        }
      )
      .to_return(status: 200, body: page_url_response_body, headers: {})

    result = @wheebox_api.generate_page_url(
      'user_uuid_123',
      'https://example.com/callback',
      'pipplet',
      'John',
      'Doe'
    )

    assert_equal 'attempt_12345', result[:attempt_id]
    assert_equal 'https://wheebox.com/face-training/attempt_12345', result[:attempt_url]
    assert_equal 'assessment_67890', result[:assessment_id]
    assert_equal 'valid_token_123', result[:token]
  end

  test 'generate_page_url raises WheeboxError when get_token fails' do
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 401, body: 'Unauthorized', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.generate_page_url('user_123', 'https://example.com/callback', 'event_123', 'John', 'Doe')
    end

    assert_equal 'Failed to get token (HTTP 401)', error.message
  end

  test 'generate_page_url raises WheeboxError on HTTP error status' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock failed generate_page_url call
    stub_request(:post, 'https://wgc.wheebox.com/WheeboxRestService/generatepageURL')
      .to_return(status: 400, body: 'Bad Request', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.generate_page_url('user_123', 'https://example.com/callback', 'event_123', 'John', 'Doe')
    end

    assert_equal 'Failed to generate page URL (HTTP 400)', error.message
  end

  test 'generate_page_url raises WheeboxError on invalid JSON response' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock generate_page_url call with invalid JSON
    stub_request(:post, 'https://wgc.wheebox.com/WheeboxRestService/generatepageURL')
      .to_return(status: 200, body: 'invalid json', headers: {})

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.generate_page_url('user_123', 'https://example.com/callback', 'event_123', 'John', 'Doe')
    end

    assert_equal 'Invalid response format from Wheebox API when generating page URL.', error.message
  end

  test 'generate_page_url raises WheeboxError on network error' do
    # Mock successful get_token call
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Mock generate_page_url call with socket error
    stub_request(:post, 'https://wgc.wheebox.com/WheeboxRestService/generatepageURL')
      .to_raise(SocketError)

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.generate_page_url('user_123', 'https://example.com/callback', 'event_123', 'John', 'Doe')
    end

    assert_match(/Network error when generating page URL/, error.message)
  end

  # ----------------------------------------
  # Edge cases and error handling
  # ----------------------------------------

  test 'all methods handle HTTParty::Error correctly' do
    # Mock get_token to raise HTTParty::Error
    stub_request(:post, "#{@base_url}/getToken")
      .to_raise(HTTParty::Error.new('HTTParty error'))

    # Test get_token
    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.get_token
    end
    assert_match(/API communication error when getting token/, error.message)

    # For other methods, we need to mock successful get_token first
    token_response_body = { 'token' => 'valid_token_123', 'expires' => '2024-12-31T23:59:59Z' }.to_json
    stub_request(:post, "#{@base_url}/getToken")
      .to_return(status: 200, body: token_response_body, headers: {})

    # Test register_candidate
    stub_request(:post, "#{@base_url}/getRegistration")
      .to_raise(HTTParty::Error.new('HTTParty error'))

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.register_candidate('user_123', 'event_123', 'John', 1, '<EMAIL>')
    end
    assert_match(/API communication error when registering candidate/, error.message)

    # Test end_test
    stub_request(:post, "#{@base_url}/endTestRpass")
      .to_raise(HTTParty::Error.new('HTTParty error'))

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.end_test('attempt_12345')
    end
    assert_match(/API communication error when ending test/, error.message)

    # Test generate_page_url
    stub_request(:post, 'https://wgc.wheebox.com/WheeboxRestService/generatepageURL')
      .to_raise(HTTParty::Error.new('HTTParty error'))

    error = assert_raises(WheeboxApi::WheeboxError) do
      @wheebox_api.generate_page_url('user_123', 'https://example.com/callback', 'event_123', 'John', 'Doe')
    end
    assert_match(/API communication error when generating page URL/, error.message)
  end

  test 'TokenResponse struct works correctly' do
    token_response = WheeboxApi::TokenResponse.new('test_token', '2024-12-31')
    assert_equal 'test_token', token_response.token
    assert_equal '2024-12-31', token_response.expires
  end

  test 'WheeboxError is a StandardError' do
    assert_kind_of StandardError, WheeboxApi::WheeboxError.new('test error')
  end
end
