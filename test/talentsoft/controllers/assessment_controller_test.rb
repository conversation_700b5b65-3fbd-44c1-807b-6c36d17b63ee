require 'test_helper'
require 'securerandom'

class AssessmentControllerTest < ActionDispatch::IntegrationTest
  include ActiveJob::TestHelper
  include AITestHelper::OpenAi
  include AwsHelper

  def setup
    @test_language = Rails.application.config.talentsoft[:catalog].keys.sample.to_s

    @token = SecureRandom.hex
    @valid_header = { Authorization: "Bearer #{@token}" }
    @token_expiration_date = DateTime.now + 1.day

    @non_ai_test_profile = TestProfile.find(Rails.application.config.talentsoft[:test_profile_ids]['talent'])
    @ai_test_profile = TestProfile.find(Rails.application.config.talentsoft[:test_profile_ids]['talent_ai'])
    @non_ai_campaign, @non_ai_campaign2 = TalentsoftApi.campaigns.filter { |c| c['type'] == 'talent' }.sample(2)
    @ai_campaign = TalentsoftApi.campaigns.filter { |c| c['type'] == 'talent_ai' }.sample
    @candidate             = build_candidate
    @candidate2            = build_candidate
    @candidate3            = build_candidate
    @invalid_candidate     = build_candidate.tap { |candidate| candidate['email'] = '' }

    @api_user = FactoryBot.create(:api_user,
                                  authentication_token: @token,
                                  authentication_token_expires_at: @token_expiration_date,
                                  name: "#{Time.now.to_i}_api",
                                  default_test_profile_id: @non_ai_test_profile.id,
                                  default_test_language: 'en')
  end

  def build_candidate
    {
      applicant_id: SecureRandom.hex,
      lastname: Faker::Name.last_name,
      firstname: Faker::Name.first_name,
      email: Faker::Internet.unique.email(domain: 'pipplet.com')
    }.with_indifferent_access
  end

  def grade_test_instance!(test_instance:)
    grade = FactoryBot.build(:grade)
    certificate = FactoryBot.create(:certificate, grades: [grade])
    evaluation = FactoryBot.build(:evaluation, certificate: certificate)

    test_instance.begin_date = Time.zone.now - rand(60).minutes - rand(60).seconds
    test_instance.end_date = Time.zone.now
    test_instance.mark_as_graded(evaluation)
  end

  test '#campaigns should return the proper data' do
    get campaigns_api_talentsoft_assessment_index_url, headers: { Authorization: "Bearer #{@token}" }
    assert_equal 200, status
    assert_equal({ 'campaign_id' => 'eu-talent-en',
                   'language' => 'eu',
                   'local' => 'en',
                   'name' => 'Basque Talent Language Test',
                   'type' => 'talent' }, JSON.parse(response.body)['data'].first)
  end

  test '#campaigns should filter on languages if the :language param is provided' do
    get campaigns_api_talentsoft_assessment_index_url(language: @test_language), headers: @valid_header
    assert_equal [@test_language], JSON.parse(response.body)['data'].pluck('local').uniq
  end

  test '#campaigns should filter on languages if the :language param is localised' do
    get campaigns_api_talentsoft_assessment_index_url(language: "#{@test_language}-xx"), headers: @valid_header
    assert_equal [@test_language], JSON.parse(response.body)['data'].pluck('local').uniq
  end

  test '#campaigns should filter on the :filter param' do
    filter = 'Polish Talent Language Test'
    get campaigns_api_talentsoft_assessment_index_url(keyword: filter), headers: @valid_header

    assert_equal [filter], JSON.parse(response.body)['data'].pluck('name').uniq
  end

  test '#campaigns should return an error on no results for filter' do
    filter = Faker::Marketing.buzzwords
    get campaigns_api_talentsoft_assessment_index_url(keyword: filter), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'fail', 'message' => 'Keyword fail : nothing corresponding to keyword' }, JSON.parse(response.body))
  end

  test '#campaignsCreationLink should always return an error' do
    get campaigns_creationLink_api_talentsoft_assessment_index_url, headers: @valid_header

    assert_equal 401, status
    assert_equal({ 'code' => 401, 'status' => 'error', 'message' => 'You cannot create a new campaign. See existing campaigns at endpoint GET assessment_campaigns.' }, JSON.parse(response.body))
  end

  test '#test_post should return an error if :campaign_id is missing' do
    post test_api_talentsoft_assessment_index_url, headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'IW03 : The campaign does not exist.' }, JSON.parse(response.body))
  end

  test '#test_post should return an error if applicants are not in the invitation list' do
    post test_api_talentsoft_assessment_index_url(campaign_id: Faker::Marketing.buzzwords), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'MA04 : Candidate not found.' }, JSON.parse(response.body))
  end

  test '#test_post should return an error :campaign_id is not found' do
    post test_api_talentsoft_assessment_index_url(campaign_id: Faker::Marketing.buzzwords, invitations: [@candidate]), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'IW03 : The campaign does not exist.' }, JSON.parse(response.body))
  end

  test '#test_post should create 1 User with proper data for each candidate' do
    params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3] }
    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    users = User.last(3)

    assert_equal @candidate[:email],        users[0].email
    assert_equal @candidate[:firstname],    users[0].first_name
    assert_equal @candidate[:lastname],     users[0].last_name
    assert_equal "api:#{@api_user.name}", users[0].group

    assert_equal @candidate2[:email],       users[1].email
    assert_equal @candidate2[:firstname],   users[1].first_name
    assert_equal @candidate2[:lastname],    users[1].last_name
    assert_equal "api:#{@api_user.name}", users[1].group

    assert_equal @candidate3[:email],       users[2].email
    assert_equal @candidate3[:firstname],   users[2].first_name
    assert_equal @candidate3[:lastname],    users[2].last_name
    assert_equal "api:#{@api_user.name}", users[2].group
  end

  test '#test_post should create 1 TestInstance for each candidate' do
    params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3] }
    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    test_instances = TestInstance.last(3)

    assert_equal [@non_ai_test_profile.id],      test_instances.pluck(:test_profile_id).uniq
    assert_equal [@non_ai_campaign['language']], test_instances.map(&:test_language_sym).uniq
    assert_equal ['Talentsoft'],                 test_instances.pluck(:client_type).uniq
    assert_equal [@api_user.name],               test_instances.pluck(:client_name).uniq
    assert_equal ['Sir or Madam'],               test_instances.pluck(:client_contact_first_name).uniq
    assert_equal [' '],                          test_instances.pluck(:client_contact_last_name).uniq
  end

  test '#test_post should create 1 ApiOrder for each candidate' do
    params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3] }
    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    api_orders = ApiOrder.last(3)

    assert_equal @api_user.id,               api_orders[0].api_user_id
    assert_equal @api_user.id,               api_orders[1].api_user_id
    assert_equal @api_user.id,               api_orders[2].api_user_id
    assert_equal @candidate[:applicant_id],  api_orders[0].order_reference
    assert_equal @candidate2[:applicant_id], api_orders[1].order_reference
    assert_equal @candidate3[:applicant_id], api_orders[2].order_reference
    assert_equal 'received',                 api_orders[0].status
    assert_equal 'talentsoft',               api_orders[0].source
    assert_equal params,                     api_orders[0].order_information.symbolize_keys
    assert_equal params,                     api_orders[1].order_information.symbolize_keys
    assert_equal params,                     api_orders[2].order_information.symbolize_keys
  end

  test '#test_post should return the proper data for each candidate' do
    params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3] }
    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    test_instances = TestInstance.last(3)

    assert_equal 200, status
    parsed_data = JSON.parse(response.body)['data']

    assert_equal @candidate[:applicant_id],   parsed_data[0]['applicant_id']
    assert_equal 'success',                   parsed_data[0]['status']
    assert_equal test_instances[0].test_url,  parsed_data[0]['interview_invitation_link']

    assert_equal @candidate2[:applicant_id],  parsed_data[1]['applicant_id']
    assert_equal 'success',                   parsed_data[1]['status']
    assert_equal test_instances[1].test_url,  parsed_data[1]['interview_invitation_link']

    assert_equal @candidate3[:applicant_id],  parsed_data[2]['applicant_id']
    assert_equal 'success',                   parsed_data[2]['status']
    assert_equal test_instances[2].test_url,  parsed_data[2]['interview_invitation_link']
  end

  # TODO : 1 test even if graded ?
  test '#test_post should only create 1 TestInstance/ApiOrder/ApiUser per candidate on the same campaign, even if graded' do
    params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate] }
    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    user_length =          User.all.length
    api_order_length =     ApiOrder.all.length
    test_instance_length = TestInstance.all.length
    test_instance =        TestInstance.last

    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header
    assert_equal 200,                  status
    assert_equal user_length,          User.all.length
    assert_equal api_order_length,     ApiOrder.all.length
    assert_equal test_instance_length, TestInstance.all.length
    parsed_response = JSON.parse(response.body)

    assert_equal @candidate[:applicant_id],                                   parsed_response['data'][0]['applicant_id']
    assert_equal 'IW01 : The candidate has already a test on this campaign.', parsed_response['data'][0]['error_message']
    assert_equal 'error',                                                     parsed_response['data'][0]['status']

    grade_test_instance!(test_instance: test_instance)

    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header
    parsed_response = JSON.parse(response.body)
    assert_equal @candidate[:applicant_id],                                   parsed_response['data'][0]['applicant_id']
    assert_equal 'IW01 : The candidate has already a test on this campaign.', parsed_response['data'][0]['error_message']
    assert_equal 'error',                                                     parsed_response['data'][0]['status']

    params = { campaign_id: @non_ai_campaign2['campaign_id'], invitations: [@candidate] }
    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    assert_equal user_length,              User.all.length
    assert_equal api_order_length + 1,     ApiOrder.all.length
    assert_equal test_instance_length + 1, TestInstance.all.length
  end

  test '#test_post should return an error if candidate has email || first_name || last_name || applicant_id missing' do
    params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [{ applicant_id: SecureRandom.hex, email: Faker::Internet.unique.email(domain: 'pipplet.com') }] }

    post test_api_talentsoft_assessment_index_url(params), headers: @valid_header

    parsed_response = JSON.parse(response.body)

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'MA04 : Candidate not found.' }, parsed_response)
  end

  test '#test_post can create talent_ai test_instances' do
    stubs_ai_config
    stubs_ai_api_response(cecrl_grade: 'C2')

    perform_enqueued_jobs do
      post test_api_talentsoft_assessment_index_url(campaign_id: @ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header
    end

    assert TestInstance.last.talent_ai?
  end

  # TODO: this test should be generalized in test_instance#create_with_user
  test '#test_post creates a test with proper service & sub_service when a RemoteClient exists for the ApiUser' do
    sub_service_name = Faker::Internet.uuid
    service_name = Faker::Internet.uuid
    stubs_ai_config
    stubs_ai_api_response(cecrl_grade: 'C2')
    stub_request(:post, %r{\A#{PippletClientsApi.api_url}/.*}).to_return(status: 200, body: { user:
                                                                                                {
                                                                                                  campaign: {
                                                                                                    id: SecureRandom.rand(6),
                                                                                                    name: 'Campaign',
                                                                                                    service: { id: SecureRandom.rand(6), name: service_name },
                                                                                                    sub_service: { id: SecureRandom.rand(6), name: sub_service_name }
                                                                                                  }
                                                                                                } }.to_json, headers: {})

    FactoryBot.create(:remote_client, api_user_id: @api_user.id)

    post test_api_talentsoft_assessment_index_url(campaign_id: @ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    perform_enqueued_jobs

    assert_equal service_name, ApiOrder.last.service_name
    assert_equal sub_service_name, ApiOrder.last.sub_service_name
  end

  test '#test_post should create 1 talent_ai TestInstance for each candidate (no RemoteClient)' do
    params = { campaign_id: @ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3] }
    perform_enqueued_jobs do
      post test_api_talentsoft_assessment_index_url(params), headers: @valid_header
    end
    assert_equal 3, TestInstance.last(3).pluck(&:talent_ai?).count
    assert_equal [@ai_test_profile.id], TestInstance.last(3).pluck(:test_profile_id).uniq
  end

  test '#test_post Ai end-to-end scenario test' do
    stubs_ai_config
    stubs_ai_api_response(cecrl_grade: 'C2')

    perform_enqueued_jobs do
      post test_api_talentsoft_assessment_index_url(campaign_id: @ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header
    end

    test_instance = TestInstance.last
    test_instance.update(productions:
                           FactoryBot.create_list(:production, 4, :spoken, recording_duration: DEFAULT_TOTAL_AUDIO_DURATION) +
                           FactoryBot.create_list(:production, 3, :written, answer: Faker::Lorem.paragraph_by_chars(number: 100)))

    perform_enqueued_jobs do
      test_instance.send_for_evaluation!
    end

    assert test_instance.evaluations.standards.take.delivered?
    assert test_instance.reload.graded?

    get test_api_talentsoft_assessment_index_url(campaign_id: @ai_campaign['campaign_id'], 'applicants' => @candidate[:applicant_id]), headers: @valid_header

    assert_response :success
    parsed_response = JSON.parse(response.body)

    assert_equal([{ 'name' => 'Spoken', 'value' => 94 }, { 'name' => 'Written', 'value' => 94 }, { 'name' => 'Overall', 'value' => 94 }], parsed_response['data'][0]['scores'])
  end

  # TODO : This raises an exception but should pass
  # test 'test_post should return an error if candidate has invalid parameters' do
  #   params = { campaign_id: @non_ai_campaign['campaign_id'], invitations: [@invalid_candidate] }
  #
  #   post test_api_talentsoft_assessment_index_url(params), headers: @valid_header
  #
  # end

  test '#test_get should return an error if :campaign_id is missing' do
    get test_api_talentsoft_assessment_index_url, headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'IW03 : The campaign does not exist.' }, JSON.parse(response.body))
  end

  test '#test_get should return an error if :campaign_id is invalid' do
    get test_api_talentsoft_assessment_index_url(campaign_id: SecureRandom.hex), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'IW03 : The campaign does not exist.' }, JSON.parse(response.body))
  end

  test '#test_get should return an error if :applicants is missing' do
    get test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id']), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'MA04 : Candidate not found.' }, JSON.parse(response.body))
  end

  test '#test_get should return an error if :applicants is empty' do
    get test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], applicants: []), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400, 'status' => 'error', 'message' => 'MA04 : Candidate not found.' }, JSON.parse(response.body))
  end

  # TODO: 200 + 400??
  test 'test_get should return an error if applicant does not have a TestInstance' do
    get test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], 'applicants' => @candidate[:applicant_id]), headers: @valid_header

    assert_equal 200, status

    parsed_response = JSON.parse(response.body)
    assert_equal 400, parsed_response['code']
    assert_equal 'error', parsed_response['status']
    assert_equal(
      { 'campaign_id' => @non_ai_campaign['campaign_id'],
        'applicant_id' => @candidate[:applicant_id],
        'message' => 'IW02 : Candidate not found.' },
      parsed_response['data'][0]
    )
  end

  test 'test_get should return the proper data for a single candidate' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    test_instance = TestInstance.last

    get test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], 'applicants' => @candidate[:applicant_id]), headers: @valid_header

    assert_equal 200, status
    parsed_data = JSON.parse(response.body)['data'][0]

    assert_equal @candidate[:applicant_id], parsed_data['applicant_id']
    assert_equal 'waiting',                 parsed_data['status']
    assert_equal test_instance.test_url,    parsed_data['assessment_invitation_link']
  end

  test 'test_get should return the proper data for multiple candidate for a pending TestInstance' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3]), headers: @valid_header

    test_instances = TestInstance.last(3)

    get test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], 'applicants' => [@candidate[:applicant_id], @candidate2[:applicant_id], @candidate3[:applicant_id]]), headers: @valid_header

    assert_equal 200, status
    parsed_data = JSON.parse(response.body)['data']

    assert_equal 'waiting',                   parsed_data[0]['status']
    assert_equal @candidate[:applicant_id],   parsed_data[0]['applicant_id']
    assert_equal test_instances[0].test_url,  parsed_data[0]['assessment_invitation_link']
    assert_equal @candidate2[:applicant_id],  parsed_data[1]['applicant_id']
    assert_equal test_instances[1].test_url,  parsed_data[1]['assessment_invitation_link']
    assert_equal @candidate3[:applicant_id],  parsed_data[2]['applicant_id']
    assert_equal test_instances[2].test_url,  parsed_data[2]['assessment_invitation_link']
  end

  test 'test_get should return the proper data for multiple candidate for a graded TestInstance' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate, @candidate2, @candidate3]), headers: @valid_header

    test_instances = TestInstance.last(3)
    test_instances.each { |test_instance| grade_test_instance!(test_instance: test_instance) }

    get test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], 'applicants' => [@candidate[:applicant_id], @candidate2[:applicant_id], @candidate3[:applicant_id]]), headers: @valid_header

    assert_equal 200, status
    parsed_data = JSON.parse(response.body)['data']

    assert_equal 'completed',                          parsed_data[0]['status']
    assert_equal @candidate[:applicant_id],            parsed_data[0]['applicant_id']
    assert_equal test_instances[0].test_url,           parsed_data[0]['assessment_invitation_link']
    assert_equal test_instances[0].result_rating || 0, parsed_data[0]['global_score']
    assert_equal @candidate2[:applicant_id],           parsed_data[1]['applicant_id']
    assert_equal test_instances[1].test_url,           parsed_data[1]['assessment_invitation_link']
    assert_equal test_instances[1].result_rating || 0, parsed_data[1]['global_score']
    assert_equal @candidate3[:applicant_id],           parsed_data[2]['applicant_id']
    assert_equal test_instances[2].test_url,           parsed_data[2]['assessment_invitation_link']
    assert_equal test_instances[2].result_rating || 0, parsed_data[2]['global_score']
  end

  test 'test_delete should return an error if applicant & stored email do not match' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    delete test_api_talentsoft_assessment_index_url(applicant_id: @candidate[:applicant_id], email: Faker::Internet.unique.email(domain: 'pipplet.com')), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400,
                   'status' => 'error',
                   'message' => 'MA04 : Candidate not found. Emails do not match',
                   'details' => [] }, JSON.parse(response.body))
  end

  test 'test_delete should return an error if any of the campaigns do not exist' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    delete test_api_talentsoft_assessment_index_url(campaigns: [@non_ai_campaign['campaign_id'], @non_ai_campaign2['campaign_id'], SecureRandom.hex], applicant_id: @candidate[:applicant_id], email: @candidate[:email]), headers: @valid_header

    assert_equal 400, status
    assert_equal({ 'code' => 400,
                   'status' => 'error',
                   'message' => 'IW03 : The campaign does not exist.' }, JSON.parse(response.body))
  end

  # 200 or 400 ?
  test 'test_delete should return an error if the test instance does not exist' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    TestInstance.last.destroy!

    delete test_api_talentsoft_assessment_index_url(campaigns: [@non_ai_campaign['campaign_id']], applicant_id: @candidate[:applicant_id], email: @candidate[:email]), headers: @valid_header

    assert_equal 200, status

    assert_equal({ 'code' => 400,
                   'status' => 'error',
                   'message' => '',
                   'details' => [{ 'campaign_id' => @non_ai_campaign['campaign_id'],
                                   'applicant_id' => @candidate[:applicant_id],
                                   'code' => 400,
                                   'status' => 'error',
                                   'message' => 'IW02 : Candidate not found.' }] },
                 JSON.parse(response.body))
  end

  # TODO : this needs to be for unfinished TestInstances
  test 'test_delete should return an error if the test instance is finished' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    delete test_api_talentsoft_assessment_index_url(campaigns: [@non_ai_campaign['campaign_id']], applicant_id: @candidate[:applicant_id], email: @candidate[:email]), headers: @valid_header

    assert_equal 200, status

    assert_equal({ 'code' => 200,
                   'status' => 'success',
                   'message' => '',
                   'details' => [{ 'campaign_id' => @non_ai_campaign['campaign_id'],
                                   'applicant_id' => @candidate[:applicant_id],
                                   'code' => 200,
                                   'status' => 'success',
                                   'message' => '' }] }, JSON.parse(response.body))
  end

  # TODO : this needs to be for finished TestInstances also 200 or 400 ?
  test 'test_delete should return an error if the test instance is unfinished' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    grade_test_instance!(test_instance: TestInstance.last)

    delete test_api_talentsoft_assessment_index_url(campaigns: [@non_ai_campaign['campaign_id']], applicant_id: @candidate[:applicant_id], email: @candidate[:email]), headers: @valid_header

    assert_equal 200, status

    assert_equal({ 'code' => 400,
                   'status' => 'error',
                   'message' => '',
                   'details' => [{ 'campaign_id' => @non_ai_campaign['campaign_id'],
                                   'applicant_id' => @candidate[:applicant_id],
                                   'code' => 400,
                                   'status' => 'error',
                                   'message' => 'Test not finished yet, cannot delete it' }] },
                 JSON.parse(response.body))
  end

  test 'test_delete should delete TestInstance data' do
    post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header

    test_instance_length = TestInstance.all.length

    delete test_api_talentsoft_assessment_index_url(campaigns: [@non_ai_campaign['campaign_id']], applicant_id: @candidate[:applicant_id], email: @candidate[:email]), headers: @valid_header

    assert_equal test_instance_length - 1, TestInstance.all.length
  end

  # TODO : the whole point of the deletion feature is GDPR, user personal data should be deleted
  # test 'test_delete should delete User data' do
  #   post test_api_talentsoft_assessment_index_url(campaign_id: @non_ai_campaign['campaign_id'], invitations: [@candidate]), headers: @valid_header
  #
  #   user_length = User.all.length
  #
  #   delete test_api_talentsoft_assessment_index_url(campaigns:[@non_ai_campaign['campaign_id']], applicant_id: @candidate[:applicant_id], email: @candidate[:email]), headers: @valid_header
  #
  #   assert_equal user_length - 1, User.all.length
  # end
end
