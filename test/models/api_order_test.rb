# == Schema Information
#
# Table name: api_orders
#
#  id                :integer          not null, primary key
#  anonymized_at     :datetime
#  order_errors      :string
#  order_information :string           default({})
#  order_reference   :string
#  source            :integer
#  status            :integer
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  api_user_id       :integer
#  test_instance_id  :integer
#  user_id           :integer
#
# Indexes
#
#  api_orders_ti_index                  (test_instance_id)
#  index_api_orders_on_order_reference  (order_reference)
#
require 'test_helper'

class ApiOrderTest < ActiveSupport::TestCase
  test 'api_order should initialize order_information with an empty hash' do
    api_order = ApiOrder.new
    assert_kind_of Hash, api_order.order_information
    assert_empty api_order.order_information
  end

  test '#service_name should return nil if order_information is a string' do
    api_order = FactoryBot.create(:api_order)
    api_order.stubs(:order_information).returns('')

    assert_nil api_order.service_name
  end

  test '#sub_service_name should return nil if order_information is a string' do
    api_order = FactoryBot.create(:api_order)
    api_order.stubs(:order_information).returns('')

    assert_nil api_order.sub_service_name
  end

  test '#service_name should return nil if any of the following : order_information > params > pipplet_clients_service > service_name does not exist' do
    api_order = FactoryBot.create(:api_order, order_information: nil)
    assert_nil api_order.service_name
    api_order.update(order_information: {})
    assert_nil api_order.service_name
    api_order.update(order_information: { params: {} })
    assert_nil api_order.service_name
    api_order.update(order_information: { params: { pipplet_clients_service: {} } })
    assert_nil api_order.service_name
    api_order.update(order_information: { params: { pipplet_clients_service: { service_name: nil } } })
    assert_nil api_order.service_name
  end

  test '#sub_service_name should return nil if any of the following : order_information > params > pipplet_clients_service > sub_service_name does not exist' do
    api_order = FactoryBot.create(:api_order, order_information: nil)
    assert_nil api_order.sub_service_name
    api_order.update(order_information: {})
    assert_nil api_order.sub_service_name
    api_order.update(order_information: { params: {} })
    assert_nil api_order.sub_service_name
    api_order.update(order_information: { params: { pipplet_clients_service: {} } })
    assert_nil api_order.sub_service_name
    api_order.update(order_information: { params: { pipplet_clients_service: { sub_service_name: nil } } })
    assert_nil api_order.sub_service_name
  end
end
