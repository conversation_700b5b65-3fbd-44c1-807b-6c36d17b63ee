# == Schema Information
#
# Table name: test_instances
#
#  id                                  :integer          not null, primary key
#  ai_spoken_response                  :text
#  ai_written_response                 :text
#  begin_date                          :datetime
#  cached_count_of_questions_answered  :integer
#  cached_count_of_questions_to_answer :integer
#  certificate_sent_to_candidate       :boolean
#  client_contact_email                :string
#  client_contact_first_name           :string
#  client_contact_last_name            :string
#  client_contact_locale               :string
#  client_name                         :string
#  client_type                         :string
#  count_of_reminders_sent             :integer          default(0)
#  count_of_sms_reminders              :integer          default(0)
#  count_of_tekis_reminders            :integer          default(0)
#  cut_max_limit                       :integer
#  cut_min_limit                       :integer
#  deleted_at                          :datetime
#  duplicate                           :string
#  end_date                            :datetime
#  graded_at                           :datetime
#  had_check_failed                    :boolean          default(FALSE)
#  last_reminder_sent_at               :datetime
#  last_sms_reminder_sent_at           :datetime
#  last_tekis_email_send_at            :datetime
#  monitored                           :boolean          default(FALSE)
#  redirection_url                     :string
#  regrade_requested_at                :datetime
#  report_content_type                 :string
#  report_file_name                    :string
#  report_file_size                    :integer
#  report_updated_at                   :datetime
#  result_rating                       :integer
#  review_required                     :boolean          default(FALSE)
#  review_required_modified_at         :datetime
#  send_reminders                      :boolean          default(TRUE)
#  sent_for_evaluation_date            :datetime
#  skills_tested                       :string           default([]), is an Array
#  skip_audio_test                     :boolean          default(FALSE)
#  skip_browser_check                  :boolean          default(FALSE)
#  skip_email                          :boolean          default(FALSE)
#  skip_identity_check                 :boolean          default(TRUE)
#  skip_tutorial                       :boolean          default(FALSE)
#  sms_reminders                       :boolean          default(FALSE)
#  source_type                         :string
#  status                              :string           default("initialized"), not null
#  test_language                       :string
#  test_mode                           :boolean          default(TRUE)
#  time_multiplier                     :float            default(1.0)
#  uuid                                :string           not null
#  created_at                          :datetime         not null
#  updated_at                          :datetime         not null
#  api_user_id                         :integer
#  client_id                           :integer
#  delivered_evaluation_id             :integer
#  direct_user_id                      :integer
#  evaluation_id                       :integer
#  examiner_id                         :integer
#  pipplet_clients_campaign_id         :integer
#  question_set_id                     :integer
#  test_profile_id                     :integer
#  user_id                             :integer
#
# Indexes
#
#  index_test_instances_on_api_user_id            (api_user_id)
#  index_test_instances_on_status                 (status)
#  index_test_instances_on_test_language          (test_language)
#  index_test_instances_on_test_mode              (test_mode)
#  index_test_instances_on_test_profile_id        (test_profile_id)
#  index_test_instances_on_user_id                (user_id)
#  test_instances_composite_index_deleted_graded  (graded_at) WHERE (deleted_at IS NULL)
#
require 'test_helper'

class TestInstance::TestInstanceTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper
  include FilesTestHelper
  include EvaluationsTestHelper
  include AwsHelper

  def setup
    stubs_s3_buckets
    @test_instance = FactoryBot.create(:test_instance, :graded)
    @non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    @completed_test_instance = FactoryBot.create(:test_instance, :completed, test_language: @non_ai_language, test_mode: 'false')
  end

  test 'client_contact_email should validate' do
    assert_email_validations_without_mx(@test_instance, :client_contact_email)
  end

  def test_browser_details_log
    log = { browser: 'Netscape' }.to_json
    EventLog.add(@test_instance, :browser_details, log, :info)
    last_log = @test_instance.browser_details_log.last
    assert_equal log, last_log.content
  end

  def test_keyboard_check
    test_instance = build(:test_instance)
    KEYBOARD_CHECK_LANGUAGE.each_key do |key|
      test_instance.test_language = key
      assert test_instance.keyboard_check?
    end

    test_instance.test_language = 'en'
    refute test_instance.keyboard_check?
  end

  def test_is_section_full_skipped
    test_instance = FactoryBot.create(:test_instance, :with_empty_productions, productions_status: :skipped)
    assert test_instance.is_section_full_skipped?('audio')
    assert test_instance.is_section_full_skipped?('written')

    test_instance.productions.exclude_ignored.audio.each { |production| production.update(status: :completed) }
    refute test_instance.is_section_full_skipped?('audio')
    assert test_instance.is_section_full_skipped?('written')

    test_instance.productions.exclude_ignored.audio.each { |production| production.update(status: :skipped) }
    test_instance.productions.exclude_ignored.written.each { |production| production.update(status: :completed) }
    assert test_instance.is_section_full_skipped?('audio')
    refute test_instance.is_section_full_skipped?('written')

    test_instance.productions.exclude_ignored.audio.each { |production| production.update(status: :completed) }
    test_instance.productions.exclude_ignored.written.each { |production| production.update(status: :completed) }
    refute test_instance.is_section_full_skipped?('audio')
    refute test_instance.is_section_full_skipped?('written')
  end

  test '.is_full_skipped_or_being_answered?' do
    test_instance = FactoryBot.create(:test_instance, :with_empty_productions, productions_status: :skipped)
    assert test_instance.is_full_skipped_or_being_answered?

    test_instance.productions.exclude_ignored.first.update(status: :completed)
    refute test_instance.is_full_skipped_or_being_answered?

    spoken_productions = create_list(:production, 3, :spoken, :with_empty_reception, status: :being_answered) # missing 1 spoken production
    written_productions = create_list(:production, 3, :written, :with_empty_reception, status: :being_answered)

    test_instance = FactoryBot.create(:test_instance, productions: written_productions + spoken_productions)
    assert test_instance.is_full_skipped_or_being_answered?

    test_instance = FactoryBot.create(:test_instance, :completed, productions_status: :skipped)
    assert test_instance.is_full_skipped_or_being_answered?

    test_instance = FactoryBot.create(:test_instance, :completed, productions_status: :being_answered)
    assert test_instance.is_full_skipped_or_being_answered?
  end

  def test_valid_check_audio_and_text_size # rubocop:disable Minitest/MultipleAssertions
    # Productions Audio and Text
    test_instance = FactoryBot.create(:test_instance, :completed)
    # No audio, no text => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(0, 0)

    assert_event_log(test_instance, content: 'The total audio length is less than or equal to the minimum required.')
    assert_event_log(test_instance, content: 'The total number of characters is less than or equal to the minimum required.')

    # No audio and text is equal to the minimum size => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(0, test_instance.minimum_total_written_duration)
    assert_event_log(test_instance, content: 'The total audio length is less than or equal to the minimum required.')

    # No text and audio is equal to the minimum size => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION, 0)
    assert_event_log(test_instance, content: 'The total number of characters is less than or equal to the minimum required.')

    # Audio and text are equal to the minimum size => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION, test_instance.minimum_total_written_duration)

    assert_event_log(test_instance, content: 'The total audio length is less than or equal to the minimum required.')
    assert_event_log(test_instance, content: 'The total number of characters is less than or equal to the minimum required.')

    # Audio and text are greater than the minimum size => OK
    EventLog.destroy_all
    assert test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION + 1, test_instance.minimum_total_written_duration + 1)

    assert_event_log(test_instance, content: 'The total audio length is greater than the minimum required.')
    assert_event_log(test_instance, content: 'The total number of characters is greater than the minimum required.')

    # Audio is equal to default and text is greater than the minimum size => OK
    EventLog.destroy_all
    assert test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION, test_instance.minimum_total_written_duration + 1)
    assert_event_log(test_instance, content: 'The total audio length is less than or equal to the minimum required.')
    assert_event_log(test_instance, content: 'The total number of characters is greater than the minimum required.')

    # Text is equal to default and audio is greater than the minimum size => OK
    EventLog.destroy_all
    assert test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION + 1, test_instance.minimum_total_written_duration)
    assert_event_log(test_instance, content: 'The total audio length is greater than the minimum required.')
    assert_event_log(test_instance, content: 'The total number of characters is less than or equal to the minimum required.')

    test_instance.productions.destroy_all
    # Text is equal to default and audio is greater than the minimum size => OK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(0, 0)

    assert_event_log(test_instance, content: 'The total audio length is equal to zero.')
    assert_event_log(test_instance, content: 'The total number of characters is equal to zero.')

    # Productions Audio only
    test_instance = FactoryBot.create(:test_instance, :with_spoken_productions)
    # No audio, no text => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(0, 0)

    assert_event_log(test_instance, content: 'The total audio length is less than or equal to the minimum required.')
    assert EventLog.count.eql?(1)
    # Audio is equal to the minimum size => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION, 0)
    assert_event_log(test_instance, content: 'The total audio length is less than or equal to the minimum required.')
    assert EventLog.count.eql?(1)
    # Audio is greater than the minimum size => OK
    EventLog.destroy_all
    assert test_instance.valid_audio_and_text_size?(DEFAULT_TOTAL_AUDIO_DURATION + 1, 0)
    assert_event_log(test_instance, content: 'The total audio length is greater than the minimum required.')
    assert EventLog.count.eql?(1)

    # Productions Text only
    test_instance = FactoryBot.create(:test_instance, :with_written_productions)
    # No audio, no text => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(0, 0)

    assert_event_log(test_instance, content: 'The total number of characters is less than or equal to the minimum required.')
    assert EventLog.count.eql?(1)
    # Text is equal to the minimum size => NOK
    EventLog.destroy_all
    refute test_instance.valid_audio_and_text_size?(0, test_instance.minimum_total_written_duration)
    assert_event_log(test_instance, content: 'The total number of characters is less than or equal to the minimum required.')
    assert EventLog.count.eql?(1)
    # Text is greater than the minimum size => OK
    EventLog.destroy_all
    assert test_instance.valid_audio_and_text_size?(0, test_instance.minimum_total_written_duration + 1)
    assert_event_log(test_instance, content: 'The total number of characters is greater than the minimum required.')
    assert EventLog.count.eql?(1)
  end

  test 'questions_passed_validations?' do
    aws_response = Struct.new(:languages).new([Struct.new(:language_code, :score).new('en', 0.8), Struct.new(:language_code, :score).new('fr', 0.2)])
    AwsComprehendService.any_instance.stubs(:call).returns(aws_response)

    skipped_test_instance = FactoryBot.create(:test_instance, :with_empty_productions, productions_status: :skipped)
    assert skipped_test_instance.questions_passed_validations?

    test_instance = FactoryBot.create(:test_instance, :with_written_productions)

    assert test_instance.reload.questions_passed_validations?

    test_instance.update(test_language: 'fr')
    refute test_instance.reload.questions_passed_validations?

    # production 0 is setup
    test_instance.productions[1..].each do |production|
      assert_event_log(production, content: 'Language Failed :', category: 'validation')
    end

    Production.any_instance.stubs(:recording_duration).returns(45)
    test_instance = FactoryBot.create(:test_instance, :with_spoken_productions)

    assert test_instance.reload.questions_passed_validations?

    # TODO : this should pass
    # test_instance.update(test_language: 'fr')
    # refute test_instance.reload.questions_passed_validations?
    #
    # # production 0 is setup
    # test_instance.productions[1..-1].each do |production|
    #   refute_nil EventLog.find_by(loggable_id: production.id, category: 'validation', content: 'Language Failed :')
    # end
  end

  def test_assessment_type
    assessment_type = FactoryBot.create(:assessment_type, :simple)
    @completed_test_instance.test_profile.update(certification_type: assessment_type.name)
    assert_equal @completed_test_instance.assessment_type, assessment_type.name
  end

  def test_ordered_completed_productions_for
    audio_test_instance = FactoryBot.create(:test_instance, :with_spoken_productions)
    written_test_instance = FactoryBot.create(:test_instance, :with_written_productions)

    assert_empty written_test_instance.ordered_completed_productions_for(:audio)
    assert_equal audio_test_instance.productions.pluck(:id).sort, audio_test_instance.ordered_completed_productions_for(:audio).pluck(:id).sort
    assert_empty audio_test_instance.ordered_completed_productions_for(:written)
    assert_equal written_test_instance.productions.pluck(:id).sort, written_test_instance.ordered_completed_productions_for(:written).pluck(:id).sort
  end

  test 'find_trial_tis should only retrieve TIs created after November 2021' do
    examiner = FactoryBot.create(:examiner, :pre_trial_period)
    average_grade = FactoryBot.create(:average_grade, :with_test_instance)
    test_instance = average_grade.average_gradable
    FactoryBot.create(:evaluation, :delivered, examiner_id: test_instance.examiner.id, test_instance_id: test_instance.id)

    # Test one day before
    test_instance.update(created_at: '2021-11-30')
    refute_includes TestInstance.find_trial_tis(test_instance.test_language, 1, average_grade.cecrl_score, test_instance.test_profile.certification_type, examiner.id), test_instance

    # Test on the minimum date (0 because of UTC)
    test_instance.update(created_at: '2021-12-01')
    refute_includes TestInstance.find_trial_tis(test_instance.test_language, 1, average_grade.cecrl_score, test_instance.test_profile.certification_type, examiner.id), test_instance

    # Test one day after
    test_instance.update(created_at: '2021-12-02')
    assert_includes TestInstance.find_trial_tis(test_instance.test_language, 1, average_grade.cecrl_score, test_instance.test_profile.certification_type, examiner.id), test_instance
  end

  test 'test refresh_skills_tested' do
    test_instance = FactoryBot.create(:test_instance, :completed)

    expected_skills_tested = %w[reading speaking writing]
    assert_equal expected_skills_tested, test_instance.skills_tested
  end

  def test_evaluations_should_be_cancelled_when_cancelling_ti
    ti = FactoryBot.create(:test_instance)
    refute_nil ti.evaluations
    ti.evaluations << create(:evaluation, test_instance: ti)
    ti.cancel!
    assert ti.evaluations.all?(&:canceled?)
  end

  test "test speaking_only? method must return true" do
    test_instance = FactoryBot.create(:test_instance, :with_spoken_productions)
    test_instance.update_column(:skills_tested, %w[reading speaking]) # TODO: Hack WIP
    assert test_instance.speaking_only?
    refute test_instance.writing_only?
  end

  test "test writing_only method must return true" do
    test_instance = FactoryBot.create(:test_instance, :with_written_productions)
    test_instance.update_column(:skills_tested, %w[reading writing]) # TODO: Hack WIP
    refute test_instance.speaking_only?
    assert test_instance.writing_only?
  end

  test 'find_trial_tis should not retrieve TIs with evaluation which has a security violation / unusual behaviour tag' do
    examiner = FactoryBot.create(:examiner, :pre_trial_period)
    average_grade = FactoryBot.create(:average_grade, :with_test_instance)
    test_instance = average_grade.average_gradable
    test_instance.evaluations.destroy_all
    test_instance.update(created_at: '2021-12-02')
    evaluation = FactoryBot.create(:evaluation, :delivered, examiner_id: test_instance.examiner.id, test_instance_id: test_instance.id)
    assert_includes TestInstance.find_trial_tis(test_instance.test_language, 1, average_grade.cecrl_score, test_instance.test_profile.certification_type, examiner.id), test_instance

    tag = Tag.find_by_name(Tag::EVALUATION[:security_violation].sample)
    evaluation.add_tag_by_name(tag.name)
    refute_includes TestInstance.find_trial_tis(test_instance.test_language, 1, average_grade.cecrl_score, test_instance.test_profile.certification_type, examiner.id), test_instance
  end

  test 'test_cancelled_callback should send the send_test_canceled email' do
    test_instance = FactoryBot.create(:test_instance)
    assert_enqueued_with(job: MailjetSenderJob) do
      test_instance.cancel!
    end
  end

  test 'test_cancelled_callback should NOT send the send_test_canceled email if skip_cancel_notification is set' do
    test_instance = FactoryBot.create(:test_instance)
    test_instance.skip_cancel_notification = true
    test_instance.cancel!
    assert_empty(ActiveJob::Base.queue_adapter.enqueued_jobs.select { |enqueued_job| enqueued_job[:job] == MailjetSenderJob })
  end

  test 'test_cancelled_callback should only cancel cancellable evaluations' do
    test_instance = FactoryBot.create(:test_instance)
    test_instance.evaluations.delete_all
    cancellable_evaluation = FactoryBot.create(:evaluation, test_instance:, status: Evaluation::CANCELLABLE_STATUSES.sample)
    non_cancellable_evaluation = FactoryBot.create(:evaluation, test_instance:, status: Evaluation::NON_CANCELLABLE_STATUSES.sample)

    FactoryBot.create(:evaluation, :assigned, test_instance:)
    test_instance.cancel!
    assert cancellable_evaluation.reload.canceled?
    refute non_cancellable_evaluation.reload.canceled?
  end

  test 'complete! callback (not testable because of the 5mn wait, using main called method : validate_questions!) should always run validations for talent_ai' do
    aws_response = Struct.new(:languages).new([Struct.new(:language_code, :score).new('en', 0.8), Struct.new(:language_code, :score).new('fr', 0.2)])
    AwsComprehendService.any_instance.stubs(:call).returns(aws_response)

    TestInstance.any_instance.stubs(:create_productions_metadata).returns(nil)
    talent_ai_test_instance = FactoryBot.create(:test_instance, :completed_talent_ai)

    talent_ai_test_instance.validate_questions!
    talent_ai_test_instance.reload
    refute talent_ai_test_instance.test_instance_validation_detail.passed?
    assert talent_ai_test_instance.validations_failed?
  end

  test '#check_duplicate allows names with dashes and quotes' do
    seed = Faker::Number.number
    user1 = FactoryBot.create(:user, first_name: "o ---- 'Réil-ly#{seed}Ærøskøbing    ", last_name: "O'REIL-ly#{seed}")
    user2 = FactoryBot.create(:user, first_name: "O'Reil-ly#{seed}    Ærøskøbing", last_name: "O'  -- Reil-ly#{seed}    ")

    test_instance = FactoryBot.create(:test_instance, :graded, user: user1)
    FactoryBot.create(:test_instance, :graded, user: user2)
    assert test_instance.check_duplicate.any?
  end

  test '#check_duplicate without duplicates (last name)' do
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name

    user1 = FactoryBot.create(:user, first_name: first_name, last_name: last_name)
    user2 = FactoryBot.create(:user, first_name: first_name, last_name: last_name + Faker::Number.number.to_s)

    test_instance = FactoryBot.create(:test_instance, :graded, user: user1)
    FactoryBot.create(:test_instance, :graded, user: user2)
    assert_empty test_instance.check_duplicate
  end

  test '#check_duplicate without duplicates (first name)' do
    first_name = Faker::Name.first_name
    last_name = Faker::Name.last_name

    user1 = FactoryBot.create(:user, first_name: first_name, last_name: last_name)
    user2 = FactoryBot.create(:user, first_name: first_name + Faker::Number.number.to_s, last_name: last_name)

    test_instance = FactoryBot.create(:test_instance, :graded, user: user1)
    FactoryBot.create(:test_instance, :graded, user: user2)
    assert_empty test_instance.check_duplicate
  end

  test 'practice_pack_mention? result' do
    test_instance = FactoryBot.create(:test_instance, :graded)
    assert test_instance.practice_pack_mention?
    FactoryBot.create(:client_config, :no_practice_pack_mention, client_name: test_instance.client_name)
    refute test_instance.reload.practice_pack_mention?
  end

  test '#assign_to_all_examiners' do
    test_instance = FactoryBot.create(:test_instance, :completed)
    test_instance.assign_to_all_examiners
    assert_equal Examiner.real.available.ti_bulk_assign.where(language: test_instance.test_language).count, test_instance.evaluations.size
  end

  test 'create_with_user 2 tests for the same candidate should cancel the first test' do
    api_user = FactoryBot.create(:api_user)
    user = FactoryBot.create(:user)
    api_order = FactoryBot.create(:api_order, api_user: api_user)
    test_instance_request = FactoryBot.build(:test_instance_request, user: user, api_order: api_order, test_mode: true)

    _user, ti = TestInstance.create_with_user(test_instance_request)
    _user2, ti2 = TestInstance.create_with_user(test_instance_request)

    ti.reload

    assert ti.cancelled?
    assert ti2.ready?
  end

  test 'create_with_user does not validate the test_profile language' do
    test_profile = FactoryBot.create(:test_profile)
    language = (AVAILABLE_TEST_LANGUAGES - test_profile.translated_languages_list).sample
    test_instance_request = FactoryBot.build(:test_instance_request, test_profile:, test_language: language)

    refute_includes test_profile.translated_languages_list, language
    assert_nothing_raised do
      ti, user = TestInstance.create_with_user(test_instance_request)
      assert ti
      assert user
    end
  end

  test 'create_with_user with an invalid test_profile language' do
    test_language = 'xx'
    test_instance_request = FactoryBot.build(:test_instance_request, test_language:)
    assert_nothing_raised do
      ti, user = TestInstance.create_with_user(test_instance_request)
      assert_equal test_language, ti.test_language
      assert user
    end
  end

  test 'Client_grade_check duplicates the tags of the peer_review (taken to create the average grade) on the admin evaluation (candidate for delivery)' do
    cheating_assessment = :two_voices_recording
    cheating_tag = 'cheating-multi-voices'
    default_score = rand(1..30)

    stub_generate_certificate_process
    client_config = FactoryBot.create(:client_config)
    test_instance = FactoryBot.create(:test_instance, :completed, client_config:, test_language: @non_ai_language)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    default_evaluation = test_instance.evaluations.client_grade_checks.take
    default_evaluation = EvaluationsTestHelper.save_grade_and_certificate(evaluation: default_evaluation, score: default_score)

    perform_enqueued_jobs do
      default_evaluation.assess!('assessed_at')
    end

    peer_review_evaluation = test_instance.evaluations.peer_reviews.take
    json_assessment = { assessment_cheating_explanation: cheating_assessment.to_s }
    peer_review_evaluation = EvaluationsTestHelper.save_grade_and_certificate(evaluation: peer_review_evaluation,
                                                                              score: default_score,
                                                                              json_assessment: json_assessment)

    perform_enqueued_jobs do
      peer_review_evaluation.assess!('assessed_at')
      # why are we trying to generate a certificate for peer reviews ? (Examiner::EvaluationsController#submit_assessment)
      GenerateEvaluationCertificateJob.perform_later(peer_review_evaluation.id)
    end

    peer_review_evaluation.reload
    refute peer_review_evaluation.checks_passed
    assert_equal '["Evaluation goal is Peer_review", "Block with the tag: cheating-multi-voices"]', peer_review_evaluation.check_details
    assert_equal [[cheating_tag, nil]], peer_review_evaluation.tags.pluck(:name, :value)

    standard_average_evaluation = test_instance.evaluations.standards.take

    assert standard_average_evaluation.assessed?
    assert_equal [[cheating_tag, nil]], standard_average_evaluation.tags.pluck(:name, :value)
    assert_equal '["Block with the tag: cheating-multi-voices"]', standard_average_evaluation.check_details
    assert_equal cheating_assessment.to_s, standard_average_evaluation.json_assessment['assessment_cheating_explanation']
  end

  test '#redo_questionable should not re-assign an unavailable examiner' do
    test_instance = FactoryBot.create(:test_instance, :completed)
    examiner = FactoryBot.create(:examiner, :requestable, language: :en)
    evaluation = test_instance.evaluations.standards.take
    evaluation.update!(examiner:, status: :assessed)

    assert_equal 1, test_instance.evaluations.count

    examiner.update(next_unavailable_start_date: DateTime.now - 5.hours, next_unavailable_end_date: DateTime.now + 5.days)

    assert_includes Examiner.not_available.pluck(:id), examiner.id
    test_instance.redo_questionable('production', test_instance.productions.first.id)
    test_instance.send_for_evaluation!
    evaluation = test_instance.evaluations.standards.find_by(status: :assigned)
    refute_equal examiner, evaluation.examiner
  end

  test 'status changes should be logged in EventLog' do
    test_instance = FactoryBot.create(:test_instance)

    test_instance.complete!
    assert_event_log(test_instance, content: 'Status changed to completed (event: complete!). Callbacks will now trigger')

    test_instance.send_for_evaluation!
    assert_event_log(test_instance, content: 'Status changed to sent_for_evaluation (event: send_for_evaluation!). Callbacks will now trigger')

    test_instance.grade!
    assert_event_log(test_instance, content: 'Status changed to graded (event: grade!). Callbacks will now trigger')
  end

  test '#create_teki should perform jobs only when not in progress or when forced' do
    test_instance = FactoryBot.build(:test_instance)
    test_params = { test_instance_id: test_instance.id, status: 'Incomplete', from: 'validation', teki_category: nil }

    assert_no_enqueued_jobs do
      test_instance.expects(:has_in_progress_tekis?).returns(true)
      test_instance.create_teki
    end

    assert_enqueued_with(job: CreateTechnicalIssueJob, args: [test_params]) do
      test_instance.create_teki(force: true)
    end

    assert_enqueued_with(job: CreateTechnicalIssueJob, args: [test_params]) do
      test_instance.expects(:has_in_progress_tekis?).returns(false)
      test_instance.create_teki
    end
  end

  test '#collect_evaluation_tags!' do
    stub_generate_certificate_process
    ti_tag_name = Tag::TEST_INSTANCE[:deliver_no_answers_evaluation].sample
    excluded_tag_name = 'wrong-language'
    included_sv_tag_name = Tag::EVALUATION[:collected_on_test_instance].sample
    test_instance = FactoryBot.create(:test_instance, :sent_for_evaluation, tags: [Tag.find_by_name(ti_tag_name)])
    evaluation = test_instance.evaluations.standards.take
    evaluation.assign!
    evaluation.update!(tags: [
                         Tag.find_by_name(included_sv_tag_name),
                         Tag.find_by_name(excluded_tag_name)
                       ])
    evaluation = EvaluationsTestHelper.save_grade_and_certificate(evaluation:)
    perform_enqueued_jobs { evaluation.assess!('assessed_at') }
    assert_equal [ti_tag_name], test_instance.reload.tags.pluck(:name).sort

    evaluation.deliver!('delivered_at')

    assert_equal [ti_tag_name, included_sv_tag_name].sort, test_instance.reload.tags.pluck(:name).sort
  end

  test '#strong_identity_check? must return true if the account has this feature' do
    test_instance = FactoryBot.create(:test_instance)
    FactoryBot.create(:client_config, :strong_identity_check, client_name: test_instance.client_name)

    assert test_instance.strong_identity_check?
  end

  test "#strong_identity_check? must return false if the account doesn't have this feature" do
    test_instance = FactoryBot.create(:test_instance)
    FactoryBot.create(:client_config, client_name: test_instance.client_name)

    refute test_instance.strong_identity_check?
  end

  test '.overdue_for_tekis_reminder must return the TI only if last_tekis_email_send_at was between 1.month.ago and 3.days.ago' do
    technical_issue = FactoryBot.create(:technical_issue, :ongoing)
    test_instance = technical_issue.test_instance
    test_instance.update(count_of_tekis_reminders: TechnicalIssue::MAX_TEKI_REMINDERS + 1, last_tekis_email_send_at: (1.month.ago - 1.day))

    assert TestInstance.overdue_for_tekis_reminder.count.zero?

    test_instance.update(last_tekis_email_send_at: (1.month.ago + 1.day))

    refute TestInstance.overdue_for_tekis_reminder.count.zero?

    test_instance.update(last_tekis_email_send_at: 3.days.ago)

    refute TestInstance.overdue_for_tekis_reminder.count.zero?

    test_instance.update(last_tekis_email_send_at: 2.days.ago)

    assert TestInstance.overdue_for_tekis_reminder.count.zero?
  end

  test 'full_reset must reset the count_of_tekis_reminders column to zero' do
    test_instance = FactoryBot.create(:test_instance, :completed, count_of_tekis_reminders: 4)
    assert_equal 4, test_instance.count_of_tekis_reminders
    test_instance.full_reset
    assert_equal 0, test_instance.count_of_tekis_reminders
  end

  test '.client_config should match on lowered name' do
    client_config = FactoryBot.create(:client_config, client_name: 'CLIENT NAME')
    test_instance = FactoryBot.create(:test_instance, client_name: 'client name')
    test_instance.reload

    assert_equal client_config, test_instance.client_config
  end

  test '.test_instances exclude nil and empty values' do
    test_instance = FactoryBot.create(:test_instance, client_name: nil)
    FactoryBot.create(:client_config, client_name: nil)
    test_instance.reload

    assert_nil test_instance.client_config

    test_instance2 = FactoryBot.create(:test_instance, client_name: '')
    FactoryBot.create(:client_config, client_name: '')
    test_instance.reload

    assert_nil test_instance2.client_config
  end

  test '.get_certificate_email_destination' do
    email = Faker::Internet.email(domain: 'pipplet.com')
    assert_nil FactoryBot.create(:test_instance, client_contact_email: nil).get_certificate_email_destination
    assert_equal email, FactoryBot.create(:test_instance, client_contact_email: email).get_certificate_email_destination
    assert_equal email, FactoryBot.create(:test_instance,
                                          client_contact_email: email,
                                          client_config: FactoryBot.create(:client_config, surcharged_certificate_email: '')).get_certificate_email_destination
    assert_equal email, FactoryBot.create(:test_instance,
                                          client_contact_email: nil,
                                          client_config: FactoryBot.create(:client_config, surcharged_certificate_email: email)).get_certificate_email_destination
  end

  test 'test_completed_callback : closes all tekis' do
    test_instance = FactoryBot.create(:test_instance)
    technical_issue = FactoryBot.create(:technical_issue, :ongoing, test_instance:)

    test_instance.complete!

    assert technical_issue.reload.closed?
    assert_equal 1, test_instance.technical_issues.closed.count, TechnicalIssue.count
  end

  test 'test_completed_callback : fails validations if a teki has been opened between test_completed_callback and ValidateTestInstanceJob' do
    Production.any_instance.stubs(:recording_duration).returns(45)
    aws_response = Struct.new(:languages).new([Struct.new(:language_code, :score).new('en', 0.8), Struct.new(:language_code, :score).new('fr', 0.2)])
    AwsComprehendService.any_instance.stubs(:call).returns(aws_response)
    ProductionMetadatum.any_instance.stubs(:updated_transcription?).returns(true)
    ProductionMetadatum.any_instance.stubs(:transcription).returns(Faker::Lorem.paragraph)
    test_instance = FactoryBot.create(:test_instance, :completed)
    test_instance.complete!
    FactoryBot.create(:technical_issue, :ongoing, test_instance:)
    perform_enqueued_jobs

    assert test_instance.reload.validations_failed?
  end

  test 'Full client_grade_check workflow : non AI high grade diff' do
    default_score = rand(80..100)
    peer_review_score = rand(0..50)

    stub_generate_certificate_process
    client_config = FactoryBot.create(:client_config)
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language, client_config:)
    perform_enqueued_jobs { test_instance.send_for_evaluation! }
    test_instance.reload
    assert_equal 2, test_instance.evaluations.count

    default_evaluation = test_instance.evaluations.client_grade_checks.take
    peer_review_evaluation = test_instance.evaluations.peer_reviews.take

    default_evaluation = EvaluationsTestHelper.save_grade_and_certificate(evaluation: default_evaluation, score: default_score)

    perform_enqueued_jobs do
      default_evaluation.assess!('assessed_at')
      CheckEvaluationForCertificateIssueJob.perform_later(default_evaluation.id)
    end

    assert default_evaluation.assessed?
    refute_nil default_evaluation.certificate

    peer_review_evaluation = EvaluationsTestHelper.save_grade_and_certificate(evaluation: peer_review_evaluation, score: peer_review_score)

    perform_enqueued_jobs do
      peer_review_evaluation.assess!('assessed_at')
      GenerateEvaluationCertificateJob.perform_later(peer_review_evaluation.id)
    end

    assert_equal 3, test_instance.evaluations.count

    standard_average_evaluation = test_instance.evaluations.standards.take

    assert standard_average_evaluation.assessed?
    grades = standard_average_evaluation.certificate.grades

    average_score = (default_score + peer_review_score) / 2
    average_cecrl_score = ApplicationController.helpers.get_cecrl_score(average_score)
    expected_score = CECRL_LEVELS_RANGE[average_cecrl_score].max

    assert_equal expected_score, grades.spoken.last.score
    assert_equal expected_score, grades.written.last.score
    assert_equal expected_score, grades.overall.last.score
  end

  test 'Redos workflow with multiple_evaluations' do
    AITestHelper::OpenAi.stubs_ai_config
    AITestHelper::OpenAi.stubs_ai_api_response(cecrl_grade: 'C2')
    stubs_comprehend(language: :en, score: 0.9482744932174683)

    test_instance = FactoryBot.create(:test_instance, :completed)
    TestInstance.any_instance.stubs(:need_strong_id_check?).returns(true)
    perform_enqueued_jobs do
      test_instance.fail_validations!
    end

    assert_equal [%w[Standard created]], test_instance.evaluations.pluck(:evaluation_goal, :status)

    assert_performed_jobs 1, only: CreateTechnicalIssueJob

    examiner = FactoryBot.create(:examiner, :active)
    UserGroup.any_instance.stubs(:get_examiner).returns(examiner)

    perform_enqueued_jobs do
      test_instance.pass_validations!
    end

    evaluation_multiple_evaluations = test_instance.evaluations.multiple_evaluations.take
    assert evaluation_multiple_evaluations
    assert evaluation_multiple_evaluations.assigned?

    trial_examiner = FactoryBot.create(:examiner, :on_trial_period)
    UserGroup.any_instance.stubs(:get_examiner).returns(trial_examiner)

    perform_enqueued_jobs do
      test_instance.redo_all(:written)
    end

    assert_equal([
                   %w[Multiple_evaluations canceled],
                   %w[Peer_review canceled],
                   %w[Standard canceled],
                   %w[Standard canceled],
                   %w[Standard created]
                 ], test_instance.evaluations.pluck(:evaluation_goal, :status))
    UserGroup.any_instance.unstub(:get_examiner)
    perform_enqueued_jobs do
      test_instance.pass_validations!
    end

    assert_equal([
                   %w[Multiple_evaluations canceled],
                   %w[Peer_review canceled],
                   %w[Standard canceled],
                   %w[Standard canceled],
                   %w[Standard assigned],
                   %w[Trial assigned],
                   %w[Peer_review assessed]
                 ], test_instance.evaluations.pluck(:evaluation_goal, :status))
  end

  test 'add_and_assign_evaluation sets Examiner to Admin if no examiner available' do
    Examiner.includes(:evaluations).destroy_all
    Examiner.delete_all
    examiner, _examiner2 = FactoryBot.create_list(:examiner, 2, :requestable)
    no_assessment_time_examiners = FactoryBot.create_list(:examiner, 3, max_assessment_time: 0, next_unavailable_start_date: DateTime.now + 5.hours, next_unavailable_end_date: DateTime.now + 6.hours)

    evaluation = FactoryBot.create(:evaluation, examiner:)
    no_assessment_time_examiners.each do |ex|
      refute ex.can_assign_evaluation?(evaluation)[:result]
    end

    evaluation.test_instance.add_and_assign_evaluation(evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review])

    peer_review_evaluation = evaluation.test_instance.evaluations.last
    refute_equal evaluation.examiner, peer_review_evaluation.examiner

    refute_equal 0, peer_review_evaluation.examiner.max_assessment_time
    assert peer_review_evaluation.examiner.can_assign_evaluation?(peer_review_evaluation)[:result]

    evaluation.test_instance.add_and_assign_evaluation(evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review])
    evaluation.reload

    peer_review_evaluation2 = evaluation.test_instance.evaluations.last
    assert_equal Examiner.find_admin_linguistic_examiner(evaluation.test_instance.test_language), peer_review_evaluation2.examiner
  end

  test 'cancel! should succeed on TI in test_mode from any status' do
    all_statuses = TestInstance.aasm.states.map(&:name)
    all_statuses.each do |status|
      assert FactoryBot.create(:test_instance, status:, test_mode: true).may_cancel?
    end
  end

  test 'cancel! should fail on TI not in test_mode from any non-cancellable statuses' do
    non_cancellable_statuses = TestInstance::NON_CANCELLABLE_STATUSES
    non_cancellable_statuses.each do |status|
      refute FactoryBot.create(:test_instance, status:, test_mode: false).may_cancel?
    end
  end

  test 'cancel! should succeed on TI not in test_mode from any cancellable statuses' do
    cancellable_statuses = TestInstance::CANCELLABLE_STATUSES
    cancellable_statuses.each do |status|
      assert FactoryBot.create(:test_instance, status:, test_mode: false).may_cancel?
    end
  end

  test 'identity_photos_urls should return all photos if less than 10 photos' do
    client_stub = Aws::S3::Client.new(stub_responses: true)
    Rails.application.config.stubs(:s3).returns(
      identity_bucket: Aws::S3::Resource.new(client: client_stub).bucket('')
    )
    images = Array.new(8) { |i| { key: "image#{i}.jpeg" } }
    client_stub.stub_responses(:list_objects_v2, { contents: images })

    urls = Rails.application.config.s3[:identity_bucket].objects(prefix: "#{Rails.env}/ti#{@test_instance.id}").to_a.map { |photo| photo.presigned_url(:get) }
    assert_equal urls, @test_instance.identity_photos_urls
  end

  test 'identity_photos_urls always samples 10 photos if more than 10 photos' do
    client_stub = Aws::S3::Client.new(stub_responses: true)
    Rails.application.config.stubs(:s3).returns(
      identity_bucket: Aws::S3::Resource.new(client: client_stub).bucket('')
    )
    images = Array.new(rand(11..600)) { |i| { key: "image#{i}.jpeg" } }
    client_stub.stub_responses(:list_objects_v2, { contents: images })

    assert_equal 10, @test_instance.identity_photos_urls.count
  end

  test 'identity_photos_urls samples 10 photos if more than 10 photos' do
    client_stub = Aws::S3::Client.new(stub_responses: true)
    Rails.application.config.stubs(:s3).returns(
      identity_bucket: Aws::S3::Resource.new(client: client_stub).bucket('')
    )
    base_url = Faker::Internet.url
    class StubbedAwsObject # rubocop:disable Lint/ConstantDefinitionInBlock
      def initialize(url)
        @url = url
      end

      def presigned_url(_param = nil)
        @url
      end
    end

    objects = Array.new(40) { |i| StubbedAwsObject.new("#{base_url}#{i}") }
    Rails.application.config.s3[:identity_bucket].stubs(:objects).returns(objects)
    urls = objects.map(&:presigned_url)
    expected_urls = [urls[0], urls[4], urls[8], urls[12], urls[16], urls[20], urls[24], urls[28], urls[32], urls[36]]
    assert_equal expected_urls, @test_instance.identity_photos_urls
  end

  test 'inactive client_configs are ignored' do
    client_config = FactoryBot.create(:client_config, active: false)
    test_instance = FactoryBot.create(:test_instance, client_name: client_config.client_name)
    assert_nil test_instance.client_config
  end

  test 'inactive client_configs are associated' do
    client_config = FactoryBot.create(:client_config, active: true)
    test_instance = FactoryBot.create(:test_instance, client_name: client_config.client_name)
    assert_equal client_config, test_instance.client_config
  end

  test 'inactive client_configs does not trigger client_grade_check' do
    client_config = FactoryBot.create(:client_config, active: false)
    non_ai_language = (AVAILABLE_TEST_LANGUAGES - AI_ENABLED_LANGUAGES.map(&:to_sym)).sample.to_s
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: non_ai_language, client_config:)
    assert_difference('test_instance.evaluations.count', 0) do
      perform_enqueued_jobs { test_instance.send_for_evaluation! }
      refute test_instance.evaluations.client_grade_checks.any?
      assert test_instance.evaluations.standards.any?
    end
  end

  test '#mark_as_graded always updates graded_at' do
    previous_graded_at = @test_instance.graded_at
    @test_instance.mark_as_graded(@test_instance.evaluations.first)
    assert_operator previous_graded_at, :<, @test_instance.graded_at
  end
end
