# == Schema Information
#
# Table name: productions
#
#  id               :integer          not null, primary key
#  answered_at      :datetime
#  deleted_at       :datetime
#  displayed_at     :datetime
#  status           :integer          default("unready")
#  super            :boolean          default(FALSE)
#  created_at       :datetime
#  updated_at       :datetime
#  challenge_id     :integer
#  test_instance_id :integer
#  user_id          :integer
#
# Indexes
#
#  index_productions_on_challenge_id      (challenge_id)
#  index_productions_on_status            (status)
#  index_productions_on_test_instance_id  (test_instance_id)
#  index_productions_on_user_id           (user_id)
#
require 'test_helper'

class ProductionTest < ActiveSupport::TestCase
  def setup
    @test_instance = FactoryBot.create(:test_instance, :completed)
    @production = @test_instance.productions.last
    @dynamic_question_datum = @production.reception.dynamic_question_data.first
    @response = Struct.new(:languages).new([Struct.new(:language_code, :score).new('en', 0.8), Struct.new(:language_code, :score).new('fr', 0.2)])
  end

  test '#check_caracters_match_with_language returns nil' do
    @dynamic_question_datum.update_attribute(:data, {})
    assert_nil @production.check_caracters_match_with_language

    @dynamic_question_datum.update_attribute(:data, nil)
    assert_nil @production.check_caracters_match_with_language
  end

  test '#check_caracters_match_with_language returns false' do
    @dynamic_question_datum.update_attribute(:data, { 'answer' => Faker::Lorem.paragraph_by_chars(number: 100) })
    @test_instance.update_attribute(:test_language, 'la')
    AwsComprehendService.any_instance.stubs(:call).returns(@response)
    refute @production.check_caracters_match_with_language
  end

  test '#check_caracters_match_with_language returns true' do
    @dynamic_question_datum.update_attribute(:data, { 'answer' => Faker::Books::Lovecraft.paragraph_by_chars(characters: 100) })
    AwsComprehendService.any_instance.stubs(:call).returns(@response)
    assert @production.check_caracters_match_with_language
  end

  test '#too_short? written' do
    assert FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: 24)).too_short?
    refute FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: 25)).too_short?
  end

  test '#too_short? spoken' do
    assert FactoryBot.create(:production, :spoken, recording_duration: Production::MINIMUM_AUDIO_LENGTH_PER_PRODUCTION - 1).too_short?
    refute FactoryBot.create(:production, :spoken, recording_duration: Production::MINIMUM_AUDIO_LENGTH_PER_PRODUCTION + 1).too_short?
  end

  test '#cant_be_assessed? skipped' do
    assert FactoryBot.create(:production, :spoken, status: :skipped).cant_be_assessed?
    assert FactoryBot.create(:production, :written, status: :skipped).cant_be_assessed?
  end

  test '#cant_be_assessed? being_answered' do
    assert FactoryBot.create(:production, :spoken, status: :being_answered).cant_be_assessed?
    assert FactoryBot.create(:production, :written, status: :being_answered).cant_be_assessed?
  end

  test '#cant_be_assessed? language_failed' do
    assert FactoryBot.create(:production, :spoken, status: :language_failed).cant_be_assessed?
    assert FactoryBot.create(:production, :written, status: :language_failed).cant_be_assessed?
  end

  test '#cant_be_assessed? timeout' do
    assert FactoryBot.create(:production, :spoken, status: :timeout, recording_duration: Production::PRODUCTION_CONSIDERED_EMPTY_THRESHOLD - 1).cant_be_assessed?
    refute FactoryBot.create(:production, :spoken, status: :timeout, recording_duration: Production::PRODUCTION_CONSIDERED_EMPTY_THRESHOLD + 1).cant_be_assessed?

    Production.any_instance.stubs(:text_minimum_skip_length).returns(2)
    assert FactoryBot.create(:production, :written, status: :timeout, answer: Faker::Lorem.paragraph_by_chars(number: 1)).cant_be_assessed?
    refute FactoryBot.create(:production, :written, status: :timeout, answer: Faker::Lorem.paragraph_by_chars(number: 3)).cant_be_assessed?
  end

  test '#cant_be_assessed? valid status' do
    refute FactoryBot.create(:production, :spoken, status: :completed).cant_be_assessed?
  end

  test '.total_recording_duration' do
    recording_duration_per_production = rand(1..10)
    p1 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p2 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p3 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p4 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p5 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production, status: :language_failed)
    p6 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production, status: :to_be_redone)

    assert_equal recording_duration_per_production * 4, Production.total_recording_duration(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id, p5.id, p6.id]))
  end

  test '.total_text_length' do
    length = rand(1..10)
    p1 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p2 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p3 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p4 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p5 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length), status: :to_be_redone)

    assert_equal length * 4, Production.total_text_length(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id, p5.id]))
  end

  test '.valid_total_recording_duration? valid' do
    recording_duration_per_production = (DEFAULT_TOTAL_AUDIO_DURATION / 4.0) + 1
    p1 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p2 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p3 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p4 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p5 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production, status: :language_failed)
    p6 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production, status: :to_be_redone)

    assert Production.valid_total_recording_duration?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id, p5.id, p6.id]))
  end

  test '.valid_total_recording_duration? invalid' do
    recording_duration_per_production = (DEFAULT_TOTAL_AUDIO_DURATION / 4.0) - 1

    p1 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p2 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p3 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p4 =  FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p5 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production, status: :language_failed)
    p6 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production, status: :to_be_redone)

    refute Production.valid_total_recording_duration?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id, p5.id, p6.id]))
  end

  test '.valid_total_text_length? valid' do
    total_length = rand(20..50)
    TestInstance.any_instance.stubs(:minimum_total_written_duration).returns(total_length)
    length = (total_length / 4.0) + 1
    p1 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p2 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p3 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p4 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p5 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length), status: :to_be_redone)

    assert Production.valid_total_text_length?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id, p5.id]))
  end

  test '.valid_total_text_length? invalid' do
    total_length = rand(20..50)
    TestInstance.any_instance.stubs(:minimum_total_written_duration).returns(total_length)

    length = (total_length / 4.0) - 1

    p1 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p2 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p3 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p4 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length))
    p5 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: length), status: :to_be_redone)

    refute Production.valid_total_text_length?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id, p5.id]))
  end

  test '.valid_total_text_length? no production' do
    refute Production.valid_total_text_length?(productions: [])
  end

  test '.valid_total_recording_duration? no production' do
    refute Production.valid_total_recording_duration?(productions: [])
  end

  test '.valid_total_duration? no production' do
    refute Production.valid_total_duration?(productions: [])
  end

  test '.valid_total_duration? invalid written' do
    total_length = rand(20..50)
    TestInstance.any_instance.stubs(:minimum_total_written_duration).returns(total_length)
    recording_duration_per_production = (DEFAULT_TOTAL_AUDIO_DURATION / 2.0) + 1
    text_length_per_production = (total_length / 2.0) - 1
    p1 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: text_length_per_production))
    p2 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: text_length_per_production))
    p3 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p4 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)

    assert Production.valid_total_recording_duration?(productions: Production.where(id: [p3.id, p4.id]))
    refute Production.valid_total_duration?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id]))
  end

  test '.valid_total_duration? invalid spoken' do
    total_length = rand(20..50)
    TestInstance.any_instance.stubs(:minimum_total_written_duration).returns(total_length)
    recording_duration_per_production = (DEFAULT_TOTAL_AUDIO_DURATION / 2.0) - 1
    text_length_per_production = (total_length / 2.0) + 1
    p1 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: text_length_per_production))
    p2 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: text_length_per_production))
    p3 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p4 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)

    assert Production.valid_total_text_length?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id]))
    refute Production.valid_total_duration?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id]))
  end

  test '.valid_total_duration? both valid' do
    total_length = rand(20..50)
    TestInstance.any_instance.stubs(:minimum_total_written_duration).returns(total_length)
    recording_duration_per_production = (DEFAULT_TOTAL_AUDIO_DURATION / 2.0) + 1

    p1 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: (total_length / 2) + 1))
    p2 = FactoryBot.create(:production, :written, answer: Faker::Lorem.paragraph_by_chars(number: (total_length / 2) + 1))
    p3 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)
    p4 = FactoryBot.create(:production, :spoken, recording_duration: recording_duration_per_production)

    assert Production.valid_total_duration?(productions: Production.where(id: [p1.id, p2.id, p3.id, p4.id]))
  end
end
