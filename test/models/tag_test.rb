# == Schema Information
#
# Table name: tags
#
#  id    :integer          not null, primary key
#  name  :string
#  value :string
#
require 'test_helper'

class TagTest < ActiveSupport::TestCase
  test '#get_all_as_hash' do
    Tag.delete_all
    tag1 = FactoryBot.create(:tag, name: 'tag-1')
    tag2 = FactoryBot.create(:tag, name: 'tag-2')
    expected_result = { tag1.name => tag1.id, tag2.name => tag2.id }
    assert_equal expected_result, Tag.get_all_as_hash
  end
end
