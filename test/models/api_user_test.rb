# == Schema Information
#
# Table name: api_users
#
#  id                                     :integer          not null, primary key
#  access_control_allow_methods           :string
#  access_control_allow_origin            :string
#  api_ti_creation_requires_rc            :boolean          default(FALSE)
#  authentication_token                   :string
#  authentication_token_expires_at        :datetime
#  days_before_test_instance_cancellation :integer
#  default_client_name                    :string
#  default_locale                         :string
#  default_sanitized_client_name          :text
#  default_test_language                  :string
#  deleted_at                             :datetime
#  force_api_user_brand                   :boolean          default(FALSE)
#  force_identity_check                   :boolean          default(FALSE)
#  force_test_mode                        :boolean          default(TRUE)
#  metadata                               :jsonb            not null
#  metadata_1                             :string           default({})
#  name                                   :string           not null
#  passphrase                             :string           not null
#  source                                 :integer
#  status                                 :integer          default("initialized"), not null
#  type                                   :string
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  default_test_profile_id                :integer
#  identity_provider_id                   :bigint
#
# Indexes
#
#  index_api_users_on_identity_provider_id  (identity_provider_id)
#
require 'test_helper'

class ApiUserTest < ActiveSupport::TestCase
  def setup
    @api_user = FactoryBot.create(:api_user)
  end

  test 'metadata_1 should be initialized with an empty hash' do
    api_user = ApiUser.new
    assert_empty(api_user.metadata_1)
  end

  test '#create should generate_passphrase & generate_api_manager (before_create)' do
    api_user = FactoryBot.create(:api_user)
    assert_kind_of ApiOauthManager, api_user.api_oauth_manager
    assert_kind_of String, api_user.passphrase
    assert_equal SecureRandom.urlsafe_base64(32).length, api_user.passphrase.length
  end

  test '.authenticate: should return false if no ApiUser matches name, passphrase and status' do
    unsaved_api_user = FactoryBot.build(:api_user)
    refute ApiUser.authenticate(unsaved_api_user.name, unsaved_api_user.passphrase)

    inactive_api_user = FactoryBot.create(:api_user, status: ApiUser.statuses.dup.slice!(:active).keys.sample)
    refute ApiUser.authenticate(inactive_api_user.name, inactive_api_user.passphrase)
  end

  test '.authenticate: should generate authentication token if not present' do
    api_user = FactoryBot.create(:api_user, authentication_token: nil, authentication_token_expires_at: nil)
    authenticated_user = ApiUser.authenticate(api_user.name, api_user.passphrase)
    refute_nil authenticated_user.authentication_token
    refute_nil authenticated_user.authentication_token_expires_at
  end

  test '.authenticate: should generate authentication token if expired' do
    api_user = FactoryBot.create(:api_user, authentication_token: SecureRandom.urlsafe_base64(32), authentication_token_expires_at: 1.day.ago)
    authenticated_user = ApiUser.authenticate(api_user.name, api_user.passphrase)
    refute_nil authenticated_user.authentication_token
    refute_nil authenticated_user.authentication_token_expires_at
  end

  test 'generate_passphrase does not process ApiUsers with passphrase!=nil unless forced: true' do
    existing_passphrase = SecureRandom.urlsafe_base64(32)
    api_user = FactoryBot.create(:api_user, passphrase: existing_passphrase)
    api_user.generate_passphrase
    assert_equal api_user.passphrase, existing_passphrase
    api_user.generate_passphrase(force: true)
    refute_equal api_user.passphrase, existing_passphrase
  end

  test 'generate_passphrase does not generate the same passphrase twice' do
    existing_passphrase = SecureRandom.urlsafe_base64(32)
    new_passphrase = SecureRandom.urlsafe_base64(32)
    FactoryBot.create(:api_user, passphrase: existing_passphrase)
    api_user = FactoryBot.build(:api_user, passphrase: nil)
    SecureRandom.stubs(:urlsafe_base64).returns(existing_passphrase)
                .then.returns(existing_passphrase)
                .then.returns(new_passphrase)

    api_user.generate_passphrase
    refute_equal existing_passphrase, api_user.passphrase
  end

  test '#refresh_passphrase!' do
    api_user = FactoryBot.build(:api_user)
    api_user.generate_passphrase
    old_passphrase = api_user.passphrase
    api_user.refresh_passphrase!
    refute_equal old_passphrase, api_user.passphrase
  end

  test '#test_instances_to_cancel should only include test_instances status=in_progress' do
    api_user = FactoryBot.create(:api_user, days_before_test_instance_cancellation: 1)

    to_cancel_test_instance = FactoryBot.create(:test_instance, status: :in_progress, api_user:)
    to_cancel_test_instance.update_columns(updated_at: 2.days.ago)
    not_to_cancel_test_instance = FactoryBot.create(:test_instance, status: (TestInstance.aasm.states.map(&:name) - [:in_progress]).sample, api_user:)
    not_to_cancel_test_instance.update_columns(updated_at: 2.days.ago)

    assert_equal [to_cancel_test_instance.id], api_user.test_instances_to_cancel.pluck(:id)
  end

  test '#test_instances_to_cancel should only include last updated before days_before_test_instance_cancellation' do
    api_user = FactoryBot.create(:api_user, days_before_test_instance_cancellation: 7)

    to_cancel_test_instance1 = FactoryBot.create(:test_instance, status: :in_progress, api_user:)
    to_cancel_test_instance1.update_columns(updated_at: 8.days.ago)
    to_cancel_test_instance2 = FactoryBot.create(:test_instance, status: :in_progress, api_user:)
    to_cancel_test_instance2.update_columns(updated_at: 9.days.ago)
    to_cancel_test_instance3 = FactoryBot.create(:test_instance, status: :in_progress, api_user:)
    to_cancel_test_instance3.update_columns(updated_at: 7.days.ago - 1.minute)

    not_to_cancel_test_instance = FactoryBot.create(:test_instance, status: :in_progress, api_user:)
    not_to_cancel_test_instance.update_columns(updated_at: 7.days.ago + 1.minute)
    not_to_cancel_test_instance2 = FactoryBot.create(:test_instance, status: :in_progress, api_user:)
    not_to_cancel_test_instance2.update_columns(updated_at: 6.days.ago)

    assert_equal [to_cancel_test_instance1.id, to_cancel_test_instance2.id, to_cancel_test_instance3.id].sort, api_user.test_instances_to_cancel.pluck(:id).sort
  end
end
