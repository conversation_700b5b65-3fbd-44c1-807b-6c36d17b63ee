# == Schema Information
#
# Table name: examiner_other_services
#
#  id                 :bigint           not null, primary key
#  amount             :float
#  description        :string
#  service_date          :date
#  task_type          :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  examiner_id        :bigint
#  invoice_request_id :bigint
#
# Indexes
#
#  index_examiner_other_services_on_examiner_id         (examiner_id)
#  index_examiner_other_services_on_invoice_request_id  (invoice_request_id)
#
# Foreign Keys
#
#  fk_rails_...  (examiner_id => examiners.id)
#  fk_rails_...  (invoice_request_id => invoice_requests.id)
#
require "test_helper"

describe ExaminerOtherService do
  # it "does a thing" do
  #   value(1+1).must_equal 2
  # end
end
