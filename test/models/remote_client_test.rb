# == Schema Information
#
# Table name: remote_clients
#
#  id                          :integer          not null, primary key
#  client_name                 :string
#  client_type                 :string
#  deleted_at                  :datetime
#  rule_name                   :string
#  status                      :integer          default("disabled")
#  test_language               :string
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  api_user_id                 :integer
#  pipplet_clients_account_id  :integer
#  pipplet_clients_campaign_id :integer
#
require 'test_helper'

class RemoteClientTest < ActiveSupport::TestCase
  test 'validates some element of rule' do
    rc = RemoteClient.build(rule_name: Faker::Company.name, client_name: nil, client_type: nil, api_user: nil)
    refute rc.valid?
    assert_equal ['Provide some information to build a rule.'], rc.errors.full_messages
  end
end
