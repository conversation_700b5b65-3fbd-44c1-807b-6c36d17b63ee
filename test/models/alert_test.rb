# == Schema Information
#
# Table name: alerts
#
#  id            :integer          not null, primary key
#  category      :integer          default("system")
#  email_sent_at :datetime
#  message       :text
#  meta          :text             default({})
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#
require 'test_helper'

class AlertTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper
  include AppsignalHelper

  def setup
    Rails.application.config.raise_on_alert_creation = false
  end

  def teardown
    Rails.application.config.raise_on_alert_creation = false
  end

  test 'all error class methods should accept tags' do
    user = FactoryBot.create(:user)
    handled = %w[true false].sample

    expect_appsignal_error tags: { handled: }

    Alert.system(handled:, skip_appsignal: false)

    expect_appsignal_error tags: { handled: }
    Alert.process(handled:)

    expect_appsignal_error tags: { handled: }
    Alert.api(handled:)

    expect_appsignal_error tags: { handled: }
    Alert.request_errors(handled:)

    expect_appsignal_error tags: { handled: }
    Alert.javascript_error(user, '', '', handled:)
  end

  test 'linguistics should not call appsignal' do
    refute_appsignal_error

    Alert.linguistics
  end

  test 'user_activity should not call appsignal' do
    user = FactoryBot.create(:user)

    refute_appsignal_error

    Alert.user_activity(user)
  end

  test 'test_instance_activity should not call appsignal' do
    test_instance = FactoryBot.create(:test_instance)

    refute_appsignal_error

    Alert.test_instance_activity(test_instance)
  end

  test 'meta should be initialized with an empty hash' do
    alert = Alert.new
    assert_kind_of Hash, alert.meta
    assert_empty alert.meta
  end

  test 'create_with_meta should raise if config.raise_on_alert_creation' do
    Rails.application.config.raise_on_alert_creation = true
    title = Faker::Lorem.word
    message = Faker::Lorem.sentence

    error = assert_raises do # rubocop:disable Minitest/UnspecifiedException
      Alert.system(title, message)
    end

    assert_equal "#{title}, #{message}", error.message
    refute_empty error.backtrace
  end

  test 'create_with_meta sets meta as Appsignal tags' do
    handled = %w[true false].sample
    meta = {
      key1: 'value1',
      key2: 'value2'
    }
    meta[:handled] = handled
    expect_appsignal_error tags: meta
    Alert.create_with_meta(:system, meta, '', '', handled:)
  end

  test 'create_with_meta stringifies meta to a 1 level hash' do
    handled = %w[true false].sample
    meta = {
      key1: { subkey1: 'value1' }
    }
    expect_appsignal_error(tags: { key1: "#{{ "subkey1" => "value1" }}", handled: })
    Alert.create_with_meta(:system, meta, '', '', handled:)
  end

  test 'create sends an email with bcc' do
    bcc = [Faker::Internet.email(domain: 'pipplet.com')]
    message_delivery = mock
    message_delivery.expects(:deliver_later).times(1)
    AlertMailer.expects(:alert).with { |params| params[:bcc] == bcc }.returns(message_delivery).once
    perform_enqueued_jobs { FactoryBot.create(:alert, bcc:) }
  end

  test 'create calls Appsignal if skip_appsignal is false or nil, passing handled' do
    handled = [true, false].sample
    Alert.expects(:appsignal).with { |_subject, _message, params| params[:handled] == handled }.returns(nil)
    FactoryBot.create(:alert, skip_appsignal: [false, nil].sample, handled:)
  end

  test 'create doesn\'t calls Appsignal if skip_appsignal is true' do
    Alert.expects(:appsignal).never
    FactoryBot.create(:alert, skip_appsignal: true)
  end
end
