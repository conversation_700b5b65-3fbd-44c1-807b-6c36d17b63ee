# == Schema Information
#
# Table name: brands
#
#  id                                     :integer          not null, primary key
#  additional_css                         :text
#  application_branded                    :boolean          default(FALSE)
#  certificate_branded                    :boolean          default(FALSE)
#  certificate_logo_content_type          :string
#  certificate_logo_file_name             :string
#  certificate_logo_file_size             :bigint
#  certificate_logo_updated_at            :datetime
#  client_name                            :string
#  custom_certificate                     :string
#  custom_style_attributes                :jsonb
#  generated_css                          :text
#  group_name                             :string
#  header_banner_certificate_content_type :string
#  header_banner_certificate_file_name    :string
#  header_banner_certificate_file_size    :bigint
#  header_banner_certificate_updated_at   :datetime
#  hide_end_of_test_forms                 :boolean          default(FALSE)
#  is_default                             :boolean
#  logo_content_type                      :string
#  logo_file                              :string
#  logo_file_name                         :string
#  logo_file_size                         :integer
#  logo_updated_at                        :datetime
#  name                                   :string
#  show_toeic_score_on_certificate        :boolean
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#
require 'test_helper'

class BrandTest < ActiveSupport::TestCase
  include W3CValidators

  def setup
    FactoryBot.create(:brand, name: 'flex_PRO')
    FactoryBot.create(:brand, name: 'flex')
    @api_user = FactoryBot.create(:api_user)
    @api_user_brand = FactoryBot.create(:brand, name: @api_user.name)
  end

  test 'initialize defaults' do
    brand = Brand.new
    assert_equal '#0084ff', brand.primary_color
    assert_equal '#0084ff', brand.secondary_color
    assert_equal '110px', brand.logo_width
    assert_equal 'auto', brand.logo_height
    assert_equal '#0084ff', brand.background_logo_color
    assert_equal '#0084ff', brand.primary_hover_color
  end

  test 'initialize defaults doesnt override params' do
    brand = Brand.new(
      primary_color: 'blackpink',
      secondary_color: 'redvelvet',
      logo_width: '5',
      logo_height: '4'
    )
    assert_equal 'blackpink', brand.primary_color
    assert_equal 'redvelvet', brand.secondary_color
    assert_equal '5', brand.logo_width
    assert_equal '4', brand.logo_height
  end

  test 'valid css' do
    CSSValidator.any_instance.stubs(:validate_text).returns(stub(errors: []))
    brand = FactoryBot.create(:brand, generated_css: 'body { color: blue; }', additional_css: 'body { color: blue; }')
    assert brand.valid?
  end

  test 'invalid css' do
    CSSValidator.any_instance.stubs(:validate_text).returns(stub(errors: ['Parse Error']))
    brand = FactoryBot.build(:brand, generated_css: 'h1 {', additional_css: 'body { ')
    refute brand.valid?
    assert_equal ['Generated css Invalid CSS', 'Additional css Invalid CSS'], brand.errors.full_messages

    # empty additional_css is allowed
    brand = FactoryBot.build(:brand, generated_css: 'h1 {', additional_css: '')
    refute brand.valid?
    assert_equal ['Generated css Invalid CSS'], brand.errors.full_messages
  end

  test 'logo image' do
    brand = FactoryBot.create(:brand, logo_file: 'BrandLogo.svg')
    assert_equal 'BrandLogo.svg', brand.logo_image
    brand = FactoryBot.create(:brand)
    assert_equal '/logos/original/missing.png', brand.logo_image
  end

  test "find_for_ti prioritizes brands linked to test_profiles" do
    test_profile    = FactoryBot.create(:test_profile, name: 'flex pro')
    test_instance   = FactoryBot.create(:test_instance, test_profile:)

    brand = Brand.find_for_ti(test_instance)

    assert_equal 'flex_PRO', brand.name

    test_profile    = FactoryBot.create(:test_profile, name: 'flex')
    test_instance   = FactoryBot.create(:test_instance, test_profile:)

    brand = Brand.find_for_ti(test_instance)

    assert_equal 'flex', brand.name
  end

  test "find_for_ti should fallback on api_user_name if not linked to test_profile" do
    test_profile    = FactoryBot.create(:test_profile, name: 'non_branded_tp')
    test_instance   = FactoryBot.create(:test_instance, test_profile:, api_user: @api_user)

    brand = Brand.find_for_ti(test_instance)

    assert_equal @api_user.name, brand.name
  end

  test "find_for_ti should fallback on api_user_name if not linked to test_profile and force_api_user_brand is true" do
    test_profile = FactoryBot.create(:test_profile, name: 'non_branded_tp')
    @api_user.update(force_api_user_brand: true)
    test_instance = FactoryBot.create(:test_instance, test_profile:, api_user: @api_user)

    brand = Brand.find_for_ti(test_instance)

    assert_equal @api_user.name, brand.name
  end

  test "find_for_ti should fallback on client_name if not linked to test_profile and force_api_user_brand is false" do
    client_name = Faker::Company.name
    client_brand = FactoryBot.create(:brand, client_name:)
    @api_user.update(force_api_user_brand: false)
    test_instance = FactoryBot.create(:test_instance, api_user: @api_user, client_name:)

    brand = Brand.find_for_ti(test_instance)

    assert_equal client_brand, brand
  end

  test "find_for_ti should fallback on api_user if no brand, not linked to test_profile and force_api_user_brand is false, no client_name is found" do
    client_brand = FactoryBot.create(:brand, client_name: Faker::Company.name)
    @api_user.update(force_api_user_brand: false)
    test_instance = FactoryBot.create(:test_instance, api_user: @api_user, client_name: Faker::Company.name)

    brand = Brand.find_for_ti(test_instance)

    refute_equal client_brand, brand
    assert_equal @api_user.name, brand.name
  end

  test "find_for_ti should fallback on client_name if not linked to test_profile, api_user is not found, special_param is nil" do
    client_name = Faker::Company.name
    client_brand = FactoryBot.create(:brand, client_name:)
    test_instance = FactoryBot.create(:test_instance, api_user: nil, client_name:)

    brand = Brand.find_for_ti(test_instance, nil)
    assert_equal client_brand, brand
  end

  test "find_for_ti should fallback on client_name with special_param if not linked to test_profile, api_user is not found, special_param is defined" do
    client_name = Faker::Company.name
    client_brand = FactoryBot.create(:brand, client_name:)
    special_client_brand = FactoryBot.create(:brand, client_name:, application_branded: true)
    test_instance = FactoryBot.create(:test_instance, api_user: nil, client_name:)

    brand = Brand.find_for_ti(test_instance, :application_branded)
    assert_equal special_client_brand, brand
    refute_equal client_brand, brand
  end
end
