module Integrations::BrowserConsoleTestHelper
  REGISTERED_LOG_MESSAGES_REGEX = %r{.*/assets/controllers/proctoring_controller.*}

  def assert_no_console_errors
    assert_no_matching_console_log(severity: 'SEVERE')
  end

  private

  def assert_no_matching_console_log(severity:)
    assert page
    logs = filter_console_logs!(page.driver.browser.logs.get(:browser), severity:)
    assert_empty logs
  end

  def filter_console_logs!(logs, severity:)
    logs.filter! { |log| log.level == severity && log.message.match(REGISTERED_LOG_MESSAGES_REGEX) }
  end
end
