module SessionsTestHelper
  def api_session(api_user: nil)
    @api_user_internal_rw = api_user || FactoryBot.create(:api_user)
    post api_v1_sessions_url, params: { name: @api_user_internal_rw.name, passphrase: @api_user_internal_rw.passphrase }
    @token = JSON.parse(response.body)['access_token']
  end

  def create_and_sign_in_examiner
    user = create_and_sign_in_user(trait: :examiner)
    FactoryBot.create(:examiner, :requestable, user:)
  end

  def create_and_sign_in_admin
    create_and_sign_in_user(trait: :admin)
  end

  def create_and_sign_in_user(trait: nil)
    user = FactoryBot.create(:user, trait)
    user.password = Faker::Internet.password(min_length: 8)
    user.save
    user.confirm
    sign_in user
    user
  end
end
