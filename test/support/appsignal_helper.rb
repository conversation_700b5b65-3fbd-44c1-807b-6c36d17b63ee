module <PERSON>pps<PERSON><PERSON><PERSON><PERSON><PERSON>
  def expect_appsignal_error(message: nil, action: nil, tags: {})
    mock_block = mock
    if tags.any?
      mock_block.expects(:set_tags).with(**tags.stringify_keys).once
    else
      mock_block.stubs(:set_tags).returns(nil)
    end

    if action.present?
      mock_block.expects(:set_action).with(action).once
    else
      mock_block.stubs(:set_action).returns(nil)
    end
    expectation = Appsignal.expects(:send_error)
    expectation = expectation.with { |error| error.to_s == message } if message
    expectation.yields(mock_block).once
  end

  # TODO: opposite of expect_appsignal_error
  def refute_appsignal_error
    Appsignal.expects(:send_error).never
  end
end
