require 'test_helper'
class AlertMailerTest < ActionMailer::TestCase
  test "alert email is sent with correct details" do
    alert = FactoryBot.create(:alert)
    mail = AlertMailer.alert(alert_id: alert.id)

    assert_equal ["alerts+ptest+#{Rails.env}@pipplet.com"], mail.to
    expected = "[PT-LOCAL]#{alert.email_subject}"
    assert_match(/#{Regexp.escape(expected)} #\d+/, mail.subject)
  end

  test "daily API summary email is sent with correct details" do
    mail = AlertMailer.daily_api_summary

    assert_equal ["sales+#{Rails.env}@pipplet.com"], mail.to
    assert_match(/\[PT-LOCAL\]Daily Api Summary #\d+/, mail.subject)
  end

  test "daily summary email is sent with correct details" do
    for_date = Time.zone.today
    mail = AlertMailer.daily_summary(for_date)

    assert_equal ["<EMAIL>"], mail.to
    expected = "[PT-LOCAL][Daily Summary][#{for_date}]"
    assert_match(/#{Regexp.escape(expected)} #\d+/, mail.subject)
  end

  test "certificate check email is sent with correct details" do
    evaluation = FactoryBot.create(:evaluation)
    errors = []
    mail = AlertMailer.certificate_check(evaluation.id, errors)

    assert_equal ["alerts+ptest+#{Rails.env}@pipplet.com"], mail.to
    expected = "[PT-LOCAL]Certificate Testing for review"
    assert_match(/#{Regexp.escape(expected)} #\d+/, mail.subject)
  end

  test "old sent for evaluation email is sent with correct details" do
    tis = FactoryBot.create_list(:test_instance, 3)
    mail = AlertMailer.old_sent_for_evaluation(tis)

    assert_equal ["alerts+ptest+#{Rails.env}@pipplet.com"], mail.to
    expected = "[PT-LOCAL]Test Instances sent for evaluation for more than 23 hours"
    assert_match(/#{Regexp.escape(expected)} #\d+/, mail.subject)
  end

  test "monitored test instance email is sent with correct details" do
    ti = FactoryBot.create(:test_instance)
    mail = AlertMailer.monitored_test_instance(ti)
    subject = "Monitored Test Instance Assessed: "
    subject += "#{ti.user&.first_name} #{ti.user&.last_name}" unless ti.user.nil?
    subject += " - #{ti.client_name}"

    assert_equal ["<EMAIL>"], mail.to
    assert_match subject, mail.subject
  end
end
