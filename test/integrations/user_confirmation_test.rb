require 'test_helper'

# TODO: duplicates of confirmations_controller_test
class UserConfirmationTest < ActionDispatch::SystemTestCase
  test 'should redirect to index if user does not have test_instances' do
    user = FactoryBot.create(:user, :initialized)
    visit user_confirmation_path(confirmation_token: user.confirmation_token)
    assert_text I18n.t('test.none')
  end

  test 'should redirect to the welcome page if URL contains an UUID and user is confirmed' do
    test_instance = FactoryBot.create(:test_instance)
    user = test_instance.user
    user.update(understands_cgu: true)
    user.confirm
    visit user_confirmation_path(confirmation_token: user.confirmation_token, uuid: test_instance.uuid)
    assert_text "Key Tips Before Taking the Test"
    assert_current_path welcome_path(uuid: test_instance.uuid)
  end

  test 'should redirect to the terms and policies page if URL contains an UUID and user is not confirmed' do
    user = FactoryBot.create(:user, understands_cgu: false)
    test_instance = FactoryBot.create(:test_instance, user:)
    visit user_confirmation_path(confirmation_token: user.confirmation_token, uuid: test_instance.uuid)
    assert_current_path edit_terms_and_policies_path
  end

  test 'should display the safe exam browser page if force_secure_browser is required' do
    user = FactoryBot.create(:user)
    client_config = FactoryBot.create(:client_config, :force_secure_browser)
    test_instance = FactoryBot.create(:test_instance, user:, client_config:)
    visit user_confirmation_path(confirmation_token: user.confirmation_token, uuid: test_instance.uuid)
    assert_current_path welcome_path(uuid: test_instance.uuid)
    assert_text "Take Your Test in the Secure Browser"
  end

  test 'when a candidate use another platform than Mac or Windows, the confirmation page should display a warning' do
    user = FactoryBot.create(:user)
    client_config = FactoryBot.create(:client_config, :force_secure_browser)
    test_instance = FactoryBot.create(:test_instance, user:, client_config:)
    Browser::Platform.any_instance.stubs(:mac?).returns(false)
    Browser::Platform.any_instance.stubs(:windows?).returns(false)
    visit user_confirmation_path(confirmation_token: user.confirmation_token, uuid: test_instance.uuid)
    assert_current_path welcome_path(uuid: test_instance.uuid)
    assert_text "Please use a Secure Browser compatible computer (Mac or Windows)"
  end
end
