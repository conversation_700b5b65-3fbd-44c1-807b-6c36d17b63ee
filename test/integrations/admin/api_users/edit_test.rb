require 'test_helper'

module Admin
  module ApiUsers
    class EditTest < ActionDispatch::SystemTestCase
      test 'default_locale defaults to nil' do
        create_and_sign_in_admin
        visit new_admin_api_user_path

        fill_in 'api_user[name]', with: Faker::Company.name
        click_button 'Create Api user'
        api_user = ApiUser.last
        assert_current_path admin_api_user_path(id: api_user.id)
        assert_nil api_user.default_locale
        click_button 'Update Api user'
        api_user.reload
        assert_nil api_user.default_locale
      end
    end
  end
end
