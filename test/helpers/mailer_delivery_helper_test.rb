require 'test_helper'

class MailerDeliveryHelperTest < ActiveSupport::TestCase
  def setup
    @resource_class = Evaluation # TODO : use all valid models when factories are fully implemented
    @resource = FactoryBot.create(@resource_class.name.underscore.to_sym)
    @resource_associations = %i[examiner test_instance]
  end

  class NonActiveModelClass
    def initialize; end
  end

  test '#find_resource_and_verify_associations should return nil if resource_class is not from Activerecord::Base' do
    assert_nil   MailerDeliveryHelper.find_resource_and_verify_associations(resource_id: @resource.id, resource_class: NonActiveModelClass, associated_resources: @resource_associations)
    assert_empty Alert.all
  end

  test '#find_resource_and_verify_associations should return nil if resource_id is not found' do
    invalid_resource_id = @resource.id + Random.rand(100..1000)
    assert_nil MailerDeliveryHelper.find_resource_and_verify_associations(resource_id: invalid_resource_id, resource_class: @resource_class, associated_resources: @resource_associations)

    alert = Alert.last

    assert       alert
    assert_equal '[ApplicationMailer] #block in <class:MailerDeliveryHelperTest> could not deliver an email', alert.meta['title']
    assert_equal "Not found : Evaluation with 'id'=#{invalid_resource_id}", alert.message
  end

  test '#find_resource_and_verify_associations should return nil if an association is nil' do
    @resource.update_columns(examiner_id: nil)
    assert_nil MailerDeliveryHelper.find_resource_and_verify_associations(resource_id: @resource.id, resource_class: @resource_class, associated_resources: @resource_associations)

    alert = Alert.last

    assert       alert
    assert_equal '[ApplicationMailer] #block in <class:MailerDeliveryHelperTest> could not deliver an email', alert.meta['title']
    assert_equal "examiner not found for Evaluation with 'id'=#{@resource.id}", alert.message
  end

  test '#find_resource_and_verify_associations should return the resource if associations are found' do
    assert_equal @resource, MailerDeliveryHelper.find_resource_and_verify_associations(resource_id: @resource.id, resource_class: @resource_class, associated_resources: @resource_associations)
  end
end
