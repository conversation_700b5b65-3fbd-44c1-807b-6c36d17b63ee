require 'test_helper'

class AiApi::OpenTest < ActiveSupport::TestCase
  include AITestHelper::OpenAi

  def setup
    @cecrl_grade = CECRL_LEVELS_RANGE.keys.sample
    @score = ApplicationController.helpers.cecrl_score_to_max_score(@cecrl_grade)
    @evaluation = FactoryBot.create(:evaluation)
    @api_response = build_ai_api_response(cecrl_grade: @cecrl_grade)
    AiApi::Open.any_instance.stubs(:request).returns(@api_response)

    stubs_ai_config
  end

  AI_ENABLED_LANGUAGES.each do |lang|
    test "#{lang}: result returns grades from OpenAI" do
      test_instance = FactoryBot.create(:test_instance, :completed, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)
      spoken_score = AiApi.new(language: test_instance.test_language_sym, section: :spoken, evaluation:).score_and_response(productions: test_instance.productions)[0]
      written_score = AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.productions)[0]
      assert_equal @score, spoken_score, written_score
    end

    test "#{lang}: result returns no grades from OpenAI" do
      stubs_ai_api_response(cecrl_grade: nil)

      test_instance = FactoryBot.create(:test_instance, :completed, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)

      sections = [:written, :spoken]
      sections.each do |section|
        assert_raises(AiApi::NoGradeFoundError) do
          AiApi.new(language: test_instance.test_language_sym, section:, evaluation:).score_and_response(productions: test_instance.productions)
        end
      end
    end

    test "#{lang}: #score_and_response requests the right endpoint" do
      test_instance = FactoryBot.create(:test_instance, :completed, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)

      url = AiApi::Open::OPEN_AI_API_URL
      valid_headers = {
        'Content-Type' => 'application/json',
        'Authorization' => OPEN_AI_API_TOKEN ? "Bearer #{OPEN_AI_API_TOKEN}" : 'Bearer'
      }
      AiApi::Open.any_instance.unstub(:request)
      stub_request(:post, url).to_return(status: 200, body: AITestHelper::OpenAi.build_ai_api_response(cecrl_grade: 'A2').to_json)
      written_model = Rails.application.config.ai[test_instance.test_language_sym][:talent_ai][:written][:model]
      written_productions = test_instance.ordered_completed_productions_for(:written)
      question_text = AiApi::Open.new(
        language: test_instance.test_language_sym,
        model: written_model,
        section: :written,
        evaluation: nil
      ).send(:question_text, 3)

      text_content = <<~HEREDOC.chomp
        #{question_text}

        #{written_productions.map(&:text_production).map { |text| "[#{text}]" }.join("\n")}
        Reply in the most concise way possible giving only one CEFR level. If you cannot tell from the data or the data is in other language, reply 'not enough data'.
      HEREDOC

      valid_body = {
        model: written_model,
        messages: [{ role: 'user', content: text_content }]
      }
      AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: written_productions)

      assert_requested(:post, url, body: valid_body.as_json, headers: valid_headers, times: 1)
    end

    test "#{lang}: #score_and_response raises on request errors" do
      test_instance = FactoryBot.create(:test_instance, :completed, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)

      url = AiApi::Open::OPEN_AI_API_URL
      AiApi::Open.any_instance.unstub(:request)
      stub_request(:post, url).to_timeout

      assert_raises(Net::OpenTimeout) do
        AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.productions)
      end
    end

    test "#{lang}: No productions" do
      test_instance = FactoryBot.create(:test_instance, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)
      assert_raises AiApi::NonRetriableError do
        AiApi.new(language: test_instance.test_language_sym, section: :spoken, evaluation:).score_and_response(productions: test_instance.productions)
      end
      assert_raises AiApi::NonRetriableError do
        AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.productions)
      end
    end

    test "#{lang}: Empty productions" do
      test_instance = FactoryBot.create(:test_instance, :with_empty_productions, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)

      assert_raises AiApi::NonRetriableError do
        AiApi.new(language: test_instance.test_language_sym, section: :spoken, evaluation:).score_and_response(productions: test_instance.productions)
      end

      assert_raises AiApi::NonRetriableError do
        AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.productions)
      end
    end

    test "#{lang}: Empty audio productions" do
      test_instance = FactoryBot.create(:test_instance, :with_empty_spoken_productions, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)

      assert_raises AiApi::NonRetriableError do
        AiApi.new(language: test_instance.test_language_sym, section: :spoken, evaluation:).score_and_response(productions: test_instance.ordered_completed_productions_for(:audio))
      end

      written_score = AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.ordered_completed_productions_for(:written))[0]
      assert_equal @score, written_score
    end

    test "#{lang}: Empty text productions" do
      test_instance = FactoryBot.create(:test_instance, :with_empty_written_productions, test_language: lang)
      evaluation = FactoryBot.create(:evaluation, test_instance:)
      spoken_score = AiApi.new(language: test_instance.test_language_sym, section: :spoken, evaluation:).score_and_response(productions: test_instance.ordered_completed_productions_for(:audio))[0]
      assert_raises AiApi::NonRetriableError do
        AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.ordered_completed_productions_for(:written))
      end
      assert_equal @score, spoken_score
    end
  end

  test 'Should only log the prompt on first assessment attempt' do
    test_instance = FactoryBot.create(:test_instance, :completed, test_language: 'en')
    evaluation = FactoryBot.create(:evaluation, test_instance:)
    stubs_ai_api_response(cecrl_grade: 'A2')
    evaluation.update!(ai_written_attempts: 1)
    AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.productions)
    prompt_logs = EventLog.where(loggable: evaluation, category: :ai).where("content LIKE '%Please evaluate the following%'")
    assert_equal 1, prompt_logs.count

    evaluation.update!(ai_written_attempts: 2)
    assert_no_difference 'prompt_logs.reload.count' do
      AiApi.new(language: test_instance.test_language_sym, section: :written, evaluation:).score_and_response(productions: test_instance.productions)
    end
  end
end
