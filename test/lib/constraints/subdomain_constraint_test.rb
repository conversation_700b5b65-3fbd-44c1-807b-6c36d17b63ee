require 'test_helper'

class Constraints::SubdomainConstraintTest < ActiveSupport::TestCase
  test 'only allow brand subdomains' do
    constraint = Constraints::SubdomainConstraint.new
    brand = FactoryBot.create(:brand, application_branded: true)

    assert constraint.matches?(stub(subdomain: brand.name))
    assert constraint.matches?(stub(subdomain: ''))
    assert constraint.matches?(stub(subdomain: 'www'))
    refute constraint.matches?(stub(subdomain: 'tarzan'))
  end
end
