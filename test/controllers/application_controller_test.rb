require 'test_helper'

class ApplicationControllerTest < ActionDispatch::IntegrationTest
  test '#after_sign_in_path_for admin users redirects to the admin_root_path' do
    user = FactoryBot.create(:user, :admin)
    subject = ApplicationController.new
    subject.stubs(:stored_location_for).returns(nil)

    assert_equal admin_root_path, subject.after_sign_in_path_for(user)
  end

  test '#after_sign_in_path_for examiner users redirects to the examiner_evaluations_path' do
    user = FactoryBot.create(:user)
    FactoryBot.create(:examiner, user:)
    subject = ApplicationController.new
    subject.stubs(:stored_location_for).returns(nil)

    assert_equal examiner_evaluations_path, subject.after_sign_in_path_for(user)
  end

  test '#after_sign_in_path_for users with a single canceled test_instance redirects to the test_cancelled_users_path' do
    user = FactoryBot.create(:user)
    FactoryBot.create(:test_instance, :cancelled, user:)
    subject = ApplicationController.new
    subject.stubs(:stored_location_for).returns(nil)

    assert_equal test_cancelled_users_path, subject.after_sign_in_path_for(user)
  end

  test '#after_sign_in_path_for users with a single finished test_instance redirects to the test_finished_users_path' do
    user = FactoryBot.create(:user)
    FactoryBot.create(:test_instance, status: TestInstance::FINISHED_STATUSES.sample, user:)
    subject = ApplicationController.new
    subject.stubs(:stored_location_for).returns(nil)

    assert_equal test_finished_users_path, subject.after_sign_in_path_for(user)
  end
end
