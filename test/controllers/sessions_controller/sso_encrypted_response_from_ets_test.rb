require 'test_helper'

# Created from a sample request/response on ETS IDAAS (testing)
class SessionsController::SsoEncryptedResponseFromEtsTest < ActionDispatch::IntegrationTest
  include SsoTestHelper
  include Al<PERSON>sHelper

  def setup
    @valid_saml_response = Rails.root.join('test/fixtures/saml/ets_idaas/valid_saml_response_enc.xml').read

    # Fixed values from SAML Response, cannot be changed as response in encrypted
    @sp_entity_id = 'a06d6bdd49528d0d088b726cb68e064f'
    @name_id = '<EMAIL>'
    min_clock_drift_time = DateTime.parse('2025-05-09T13:41:37.947382+00:00')
    @valid_time = min_clock_drift_time
    @invalid_time = min_clock_drift_time + 6.seconds

    # SAML Response expects a preprod URL
    IdentityProvider.any_instance.stubs(:assertion_consumer_service_url).returns("https://preprod.pipplet.dev/public/saml/callback?sp_entity_id=#{@sp_entity_id}")

    @identity_provider = FactoryBot.create(:identity_provider,
                                           sp_entity_id: @sp_entity_id,
                                           metadata_xml: Rails.root.join('test/fixtures/saml/ets_idaas/identity_provider_metadata.xml').read)
    test_instance = FactoryBot.create(:test_instance)
    @user_with_ti = FactoryBot.create(:user, :confirmed, email: @name_id, identity_provider: @identity_provider, test_instances: [test_instance])
  end

  test '#saml_callback with a valid sample ETS response' do
    travel_to @valid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: @valid_saml_response
      }
      assert_equal @user_with_ti, @controller.current_user
    end
  end

  test '#saml_callback with a valid sample ETS response (invalid Time Drift)' do
    travel_to @invalid_time do
      post saml_callback_url, params: {
        sp_entity_id: @identity_provider.sp_entity_id,
        SAMLResponse: @valid_saml_response
      }

      saml_response = OneLogin::RubySaml::Response.new(@valid_saml_response, settings: @identity_provider.saml_settings)

      assert_nil @controller.current_user
      assert_alert(title: "SSO Error, IdentityProvider 'id'=#{@identity_provider.id} 'name'=#{@identity_provider.name}",
                   category: :sso,
                   meta: {
                     response: saml_response.response,
                     settings: @identity_provider.settings
                   },
                   message: 'Current time is on or after NotOnOrAfter condition (2025-05-09 13:41:43 UTC >= 2025-05-09 13:41:37 UTC + 5s), A valid SubjectConfirmation was not found on this Response')
    end
  end
end
