require 'test_helper'

class UsersControllerTest < ActionDispatch::IntegrationTest
  def setup
    @user = create_and_sign_in_user
  end

  test 'update_my_account should bypass sign in the user is updated correctly' do
    put update_my_account_path(id: @user.id), params: { user: user_data }
    assert_redirected_to my_account_path
    assert_equal 'Your account was successfully updated.', flash[:notice]
    assert_equal @user, @controller.current_user
  end

  test 'redirects to edit_terms_and_conditions_path if user does not have terms_and_policies_accepted?' do
    user = FactoryBot.create(:user, understands_cgu: false, ai_consent_granted_at: nil)
    user.confirm
    FactoryBot.create(:test_instance, :completed_talent_ai, user:)
    sign_in user

    # any UserController URL
    get welcome_url

    assert_redirected_to edit_terms_and_policies_url
  end

  private

  def user_data
    password = Faker::Internet.password(min_length: 8)
    {
      password: password,
      password_confirmation: password
    }
  end
end
