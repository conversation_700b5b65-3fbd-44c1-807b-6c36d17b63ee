require 'test_helper'

module Api
  module V1
    class DirectUsersApiControllerTest < ActionDispatch::IntegrationTest
      def setup
        @test_instance = FactoryBot.create(:test_instance)
        test_profile = FactoryBot.create(:test_profile)
        @direct_user = FactoryBot.build(:direct_user, test_profile:, default_test_profile: test_profile)

        stub_request(:get, "#{PippletClientsApi.api_url}/api/v1/campaigns/#{@direct_user.pipplet_clients_campaign_id}")
          .to_return(status: 200, body: {
            'campaign' => {
              'name' => 'Bond',
              'accuracy' => 123,
              'test_language' => 'en',
              'test_profile_id' => @test_instance.test_profile_id,
              'hashed_id' => 123,
              'state' => '',
              'account_id' => 123,
              'account_name' => 'MI6',
              'account_api_group' => '',
              'account_test_mode' => '',
              'client_email' => '<EMAIL>',
              'client_first_name' => '<PERSON>',
              'client_last_name' => '<PERSON>'
            }
          }.to_json)
        @direct_user.save
      end

      test 'create user if valid email' do
        email = '<EMAIL>'
        get api_direct_users_url, params: user_data(email), headers: { 'authorization' => "Bearer #{@direct_user.static_authentication_token}" }
        assert_equal 302, status
        assert User.find_by(email: email)
      end

      test "don't create user with invalid email" do
        email = 'mickeymouse.com'
        get api_direct_users_url, params: user_data(email), headers: { 'authorization' => "Bearer #{@direct_user.static_authentication_token}" }
        assert_equal 302, status
        assert_nil User.find_by(email: email)
      end

      test "create user for industry tests" do
        test_type = :standard_recruitment
        FactoryBot.create(:test_profile, id: PCLIENT_TEST_DEMO_SLUGS[test_type])
        data = user_data_industry_tests(email: nil, test_type:)
        get api_direct_users_url, params: data, headers: { 'authorization' => "Bearer #{@direct_user.static_authentication_token}" }
        assert_equal 302, status
        assert User.find_by(email: data[:email])
      end

      private

      def user_data(email)
        {
          email: email || Faker::Internet.unique.email(domain: 'pipplet.com'),
          first_name: Faker::Name.first_name,
          last_name: Faker::Name.last_name,
          phone_number: Faker::PhoneNumber.phone_number_with_country_code,
          token: @direct_user.static_authentication_token
        }
      end

      def user_data_industry_tests(email:, test_type:)
        {
          email: email || Faker::Internet.unique.email(domain: 'pipplet.com'),
          first_name: Faker::Name.first_name,
          last_name: Faker::Name.last_name,
          phone_number: Faker::PhoneNumber.phone_number_with_country_code,
          test_type: test_type,
          language: 'en'
        }
      end
    end
  end
end
