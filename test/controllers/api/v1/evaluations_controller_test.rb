require 'test_helper'

module Api
  module V1
    class EvaluationsControllerTest < ActionDispatch::IntegrationTest
      def setup
        @test_instance = FactoryBot.create(:test_instance, :graded)
        @evaluation = @test_instance.evaluations.last
        api_session
      end

      test 'should assess an evaluation' do
        @evaluation.reset!
        @evaluation.assign!(:assigned_at)
        @evaluation.save

        assert_equal 'assigned', @evaluation.status
        assert @evaluation.assessed_at.blank?
        assert @evaluation.delivered_at.blank?
        patch api_v1_evaluation_assessed_url(evaluation_id: @evaluation.id), headers: { 'authorization' => "Bearer #{@token}" }
        assert_response :success
        assert_equal 'delivered', @evaluation.reload.status
        assert @evaluation.assessed_at.present?
        assert @evaluation.delivered_at.present?
      end

      test "should return not found if the evaluation can't be assessed" do
        @evaluation.reset!
        @evaluation.assign!(:assigned_at)
        @evaluation.assess!(:assessed_at)
        @evaluation.deliver!(:delivered_at)
        @evaluation.save

        patch api_v1_evaluation_assessed_url(evaluation_id: @evaluation.id), headers: { 'authorization' => "Bearer #{@token}" }
        assert_response :not_found
        patch api_v1_evaluation_assessed_url(evaluation_id: 99_999), headers: { 'authorization' => "Bearer #{@token}" }
        assert_response :not_found
      end

      test 'should get evaluation infos' do
        get get_infos_api_v1_evaluation_url(id: @evaluation.id), headers: { 'authorization' => "Bearer #{@token}" }
        assert_response :created # TODO: Should be 200
        evaluation = response.parsed_body
        assert_equal @evaluation.id, evaluation['evaluation']['evaluation_id']
        assert_equal @test_instance.id, evaluation['evaluation']['test_instance_id']
        # I removed the created_at and updated_at from the response because the format is not the same
        remove_attrs = ->(key, _value) { %w[created_at updated_at].include?(key) }
        assert_equal @evaluation.assessment_type.attributes.reject(&remove_attrs), evaluation['evaluation']['assessment_type'].reject(&remove_attrs)
        assert_equal @test_instance.uuid, evaluation['evaluation']['test_instance']['test_instance_uuid']
      end

      test 'should return not_found if the evaluation does not exist' do
        get get_infos_api_v1_evaluation_url(id: 99_999), headers: { 'authorization' => "Bearer #{@token}" }
        assert_response :not_found
      end

      # Unable to write the test below because we have an issue with the method `ApplicationController.helpers.send_certificate`
      # test 'should send the certificate' do
      #   post send_certificate_api_v1_evaluation_url(id: @evaluation.id), headers: { 'authorization' => "Bearer #{@token}" }
      #   assert_response :success
      # end

      test 'should return not_found if the evaluation does not exist when sending the certificate' do
        post send_certificate_api_v1_evaluation_url(id: 99_999), headers: { 'authorization' => "Bearer #{@token}" }
        assert_response :not_found
      end
    end
  end
end
