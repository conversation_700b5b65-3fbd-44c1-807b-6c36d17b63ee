require 'test_helper'

class HasIntegrationTest < ActionDispatch::IntegrationTest
  include IntegrationTestHelper

  IntegrationTestHelper.controllers_that_have_integration.each do |controller_name, controller_class|
    test "#{controller_name} includes the integration" do
      assert_includes controller_class.included_modules, HasIntegration
    end

    # if controller implements index
    if IntegrationTestHelper.route_to_controller_action?(controller_name: controller_name, action: :index)
      test "#{controller_class.name}#index returns a list of test properly serialized" do
        controller_class.any_instance.expects(:authenticate_api_user).returns(nil)

        languages     = AVAILABLE_TEST_LANGUAGES.sample(2)
        test_profile  = FactoryBot.create(:test_profile)
        test_profile2 = FactoryBot.create(:test_profile)

        override_test_profiles_config(controller_name, test_profile_ids: [test_profile.id, test_profile2.id])
        override_test_languages_config(controller_name, languages: languages)

        get url_for(controller: "api/#{controller_name}/#{controller_name}_api", action: :index)

        assert_equal 200, status
        parsed_response = response.parsed_body

        test_serializer = Integrations.const_get(controller_class.integration_name.to_s.camelize).const_get(:TestSerializer)
        tests = [
          Integrations::Test.new(test_profile_id: test_profile.id,  test_profile_name: test_profile.name,  language: languages.first.to_s),
          Integrations::Test.new(test_profile_id: test_profile2.id, test_profile_name: test_profile2.name, language: languages.first.to_s),
          Integrations::Test.new(test_profile_id: test_profile.id,  test_profile_name: test_profile.name,  language: languages.second.to_s),
          Integrations::Test.new(test_profile_id: test_profile2.id, test_profile_name: test_profile2.name, language: languages.second.to_s)
        ]

        if parsed_response.is_a? Array
          parsed_response.each(&:deep_symbolize_keys!)
        else
          parsed_response.deep_symbolize_keys!
        end

        assert_equal(ActiveModelSerializers::SerializableResource.new(tests, each_serializer: test_serializer, adapter: test_serializer.adapter).as_json, parsed_response)

        override_test_profiles_config(controller_name, test_profile_ids: [])
        override_test_languages_config(controller_name, languages: [])
      end
    end

    test "#{controller_class.name}#fulfill_order should create a test instance, user and api_order" do
      controller          = controller_class.new
      api_user            = FactoryBot.create(:api_user)
      language            = AVAILABLE_TEST_LANGUAGES.sample
      api_order_count     = ApiOrder.count
      test_instance_count = TestInstance.count
      user_count          = User.count

      params = Integrations::TestInstanceRequest.new({
                                                       client_data: {},
                                                       candidate_data: {
                                                         email: Faker::Internet.email(domain: 'pipplet.com'),
                                                         first_name: Faker::Name.first_name,
                                                         last_name: Faker::Name.last_name
                                                       },
                                                       test_profile_id: FactoryBot.create(:test_profile).id,
                                                       language: language.to_s,
                                                       order_information: {}
                                                     })

      controller.send(:fulfill_order, api_user, params)

      assert_equal api_order_count + 1, ApiOrder.count
      assert_equal test_instance_count + 1, TestInstance.count
      assert_equal user_count + 1, User.count
    end
  end
end
