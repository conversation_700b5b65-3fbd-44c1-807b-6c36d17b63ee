class ResfulControllerTest < ActionDispatch::IntegrationTest
  # TODO: DDD : add same behaviour for all restful controllers + implement missing factories
  RESTFUL_CONTROLLERS = [
    Admin::ApiUsersController,
    # Admin::AssessmentQuestionsController, # factory unregistered
    Admin::AssessmentTypesController,
    Admin::BrandsController,
    Admin::ClientConfigsController,
    # Admin::DirectUsersController,
    Admin::EvaluationDelaysController,
    # Admin::EvaluationsController,
    # Admin::EventLogsController, # factory unregistered
    # Admin::ExaminerFeesController,
    Admin::ExaminersController,
    # Admin::ImagesController, # factory unregistered
    # Admin::InvoiceRequestsController, # override
    # Admin::LanguagesController,
    # Admin::NextQuestionablesController, # factory unregistered
    # Admin::ProductionsController,
    # Admin::RemoteClientsController, # factory unregistered
    # Admin::TagsController, # factory unregistered
    Admin::TestInstanceValidationsController,
    Admin::TestInstancesController,
    Admin::TestProfilesController
    # Admin::UserGroupsController, # factory unregistered
    # Admin::UsersController
  ].freeze

  def setup
    user_admin = FactoryBot.create(:user, :admin)
    user_admin.password = Faker::Internet.password(min_length: 8)
    user_admin.save
    user_admin.confirm
    sign_in user_admin
  end

  # sample 3 for performance reasons
  RESTFUL_CONTROLLERS.sample(3).each do |controller_class|
    test "#{controller_class}#edit should not redirect if resource is found" do
      controller = controller_class.new
      resource = FactoryBot.create(controller.object)

      get send(controller.resource_url, { id: resource.id })
      assert_response 200
    end

    test "#{controller_class}#edit should redirect if resource is not found" do
      controller = controller_class.new
      id = (controller.object.to_s.classify.constantize.maximum(:id) || 0) + 1
      get send(controller.resource_url, { id: id })
      assert_response :redirect
    end
  end
end
