require 'test_helper'

module Admin
  class AssessmentTypesControllerTest < ActionDispatch::IntegrationTest
    def setup
      @user_admin = create_and_sign_in_admin

      @test_instance = FactoryBot.create(:test_instance)
    end

    def build_params(json_data:, json_calculation_rules:)
      {
        assessment_type:
          {
            json_data: json_data,
            json_calculation_rules: json_calculation_rules,
            name: Faker::Name.name,
            assessment_time: Faker::Time.between(from: DateTime.now - 1, to: DateTime.now)
          }
      }
    end

    test 'create action should return a flash error and redirect to index if params are invalid' do
      post admin_assessment_types_path, params: build_params(json_data: {}.to_json, json_calculation_rules: { not: :blank }.to_json)
      assert_redirected_to controller: 'admin/assessment_types', action: 'index'
      assert_equal "Json data can't be blank", flash[:error]

      post admin_assessment_types_path, params: build_params(json_data: { not: :blank }.to_json, json_calculation_rules: {}.to_json)
      assert_redirected_to controller: 'admin/assessment_types', action: 'index'
      assert_equal "Json calculation rules can't be blank", flash[:error]
    end

    test 'create action should return a flash error and redirect to index if params are not valid json' do
      post admin_assessment_types_path, params: build_params(json_data: 'invalid_json', json_calculation_rules: 'invalid_json')
      assert_redirected_to controller: 'admin/assessment_types', action: 'index'
      assert_equal 'Json data must be valid JSON, Json calculation rules must be valid JSON', flash[:error]
    end

    test 'create action should redirect to edit if params are valid' do
      post admin_assessment_types_path, params: build_params(json_data: { not: :blank }.to_json, json_calculation_rules: { not: :blank }.to_json)
      assert_redirected_to controller: 'admin/assessment_types', action: 'edit', id: AssessmentType.last.id
    end
  end
end
