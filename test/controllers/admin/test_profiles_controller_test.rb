require 'test_helper'

class Admin::TestProfilesControllerTest < ActionDispatch::IntegrationTest
  def setup
    create_and_sign_in_admin
  end

  test '#create should set translated_languages and additional_translated_languages' do
    languages = AVAILABLE_TEST_LANGUAGES.sample(4)
    additional_languages = AVA<PERSON>ABLE_TEST_LANGUAGES.sample(4)
    params = {
      test_profile:
        {
          name: Faker::Number.number,
          next_questionable_profile: TestProfile.next_questionable_profiles.keys.sample,
          translated_languages_list: languages,
          additional_translated_languages_list: additional_languages
        }
    }

    post admin_test_profiles_path, params: params
    assert_equal 'Update successful!', flash[:notice]
    test_profile = TestProfile.last
    assert_equal languages.join(TestProfile::TRANSLATED_LANGUAGES_SEPARATOR), test_profile.translated_languages
    assert_equal additional_languages.join(TestProfile::TRANSLATED_LANGUAGES_SEPARATOR), test_profile.additional_translated_languages
  end

  test '#update should set translated_languages and additional_translated_languages' do
    languages = AVAILABLE_TEST_LANGUAGES.sample(4)
    additional_languages = AVAILABLE_TEST_LANGUAGES.sample(4)
    test_profile = FactoryBot.create(:test_profile)
    params = {
      test_profile:
        {
          translated_languages_list: languages,
          additional_translated_languages_list: additional_languages
        }
    }

    put admin_test_profile_path(test_profile.id), params: params
    assert_equal 'Update successful!', flash[:notice]
    test_profile.reload
    assert_equal languages.join(TestProfile::TRANSLATED_LANGUAGES_SEPARATOR), test_profile.translated_languages
    assert_equal additional_languages.join(TestProfile::TRANSLATED_LANGUAGES_SEPARATOR), test_profile.additional_translated_languages
  end
end
