require 'test_helper'

class Examiner::InvoiceRequestsControllerTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers
  include SessionsTestHelper
  include FilesTestHelper

  def setup
    @examiner = create_and_sign_in_examiner
    @invoice_request = FactoryBot.create(:invoice_request, examiner: @examiner)
  end

  test 'upload_invoice should return an error if requested_amount is nil' do
    @invoice_request.send_request!('request_sent_at')
    invoice = build_uploaded_file(path: 'Pipplet_Exemple_De_Rapport.pdf', type: 'application/pdf')
    post upload_invoice_examiner_invoice_request_url(id: @invoice_request.id), params: { invoice_pdf: invoice, requested_amount: nil }
    assert_redirected_to examiner_invoice_request_url({ id: @invoice_request.id })
    assert_equal 'Requested amount missing', flash[:alert]
    assert @invoice_request.request_sent?
  end

  test 'upload_invoice should return an error if pdf is missing' do
    @invoice_request.send_request!('request_sent_at')
    post upload_invoice_examiner_invoice_request_url(id: @invoice_request.id), params: { invoice_pdf: nil, requested_amount: 34.4 }
    assert_redirected_to examiner_invoice_request_url({ id: @invoice_request.id })
    assert_equal 'PDF invoice missing', flash[:alert]
    assert @invoice_request.request_sent?
  end

  test 'upload_invoice should update the state of the InvoiceRequest if required parameters are present' do
    @invoice_request.send_request!('request_sent_at')
    invoice = build_uploaded_file(path: 'Pipplet_Exemple_De_Rapport.pdf', type: 'application/pdf')
    post upload_invoice_examiner_invoice_request_url(id: @invoice_request.id), params: { invoice_pdf: invoice, requested_amount: 23.0 }
    assert_redirected_to examiner_invoice_request_url({ id: @invoice_request.id })

    @invoice_request.reload
    assert @invoice_request.invoice_received?
    assert_equal 'Your invoice has been uploaded!', flash[:notice]
    assert_equal "#{@invoice_request.id} Invoice #{@examiner.user.first_name} #{@examiner.user.last_name} - #{@invoice_request.month}.pdf", @invoice_request.invoice_pdf_file_name
  end

  test 'upload_invoice should try to auto validate the InvoiceRequest if required parameters are present' do
    @invoice_request.send_request!('request_sent_at')
    requested_amount = rand(0..100).to_f
    invoice = build_uploaded_file(path: 'Pipplet_Exemple_De_Rapport.pdf', type: 'application/pdf')
    post upload_invoice_examiner_invoice_request_url(id: @invoice_request.id), params: { invoice_pdf: invoice, requested_amount: requested_amount }
    assert_event_log(@invoice_request, content: "[Invoice##{@invoice_request.id}] Not validated auto")

    InvoiceRequest.any_instance.stubs(:is_requested_amount_validated).returns(true)
    FactoryBot.create(:invoice_request, examiner: @examiner)

    post upload_invoice_examiner_invoice_request_url(id: @invoice_request.id), params: { invoice_pdf: invoice, requested_amount: 23.0 }
    assert_event_log(@invoice_request, content: "[Invoice##{@invoice_request.id}] validated auto")
    @invoice_request.reload
    assert @invoice_request.validated_auto?
  end
end
