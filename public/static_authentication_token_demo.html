<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    html, body {
      height: 100%;
    }

    body{
      font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #fdfefe;
    }

    label, input {
      height: 50px;
      font-size: 24px;
      margin: 10px;
      float: left;
    }
    label {
      width: 200px;
      text-align: right;
      height: 45px;
      padding-top: 5px;
    }
    input[type=text], input[type=submit], input[type=button] {
      display: block;
      width: 800px;
      border: 3px solid #a9cce3;
      background-color: #eaf2f8;
      padding: 5px;
    }
    input[type=text].config, input[type=button].config {
      border: 3px solid #d5f5e3;
      background-color: #eafaf1;
    }
    input[type=submit], input[type=button] {
      margin-left: 180px;
    }
    .clear {
      clear: both;
    }
  </style>
</head>
<body>

<h1>Pipplet: Direct user creation demo</h1>

<h2>1- Configure the API client</h2>
<label>Pipplet API URL</label> <input type="text" id="plt_debug_url" class="config" value="http://localhost:3000/api/direct_users/create" /><div class="clear"></div>
<label>Pipplet API Key</label> <input type="text" id="plt_debug_key" class="config" value="WK2RQNDPGB04QN8XKieumfh+GbcQnrC6Ag87V5Y9NAQNZ1xqD8LWYxOo0KdlaKHe9gKG5gHhfgXOMbGQOSjygQ==" /><div class="clear"></div>
<input type="button" value="Apply configuration" class="config" onclick="javascript:update_form();" /><div class="clear"></div>

<script>
  var update_form = function() {
    var key = document.getElementById("plt_debug_key");
    var url = document.getElementById("plt_debug_url");
    var f = document.getElementById("plt_form");
    var t = document.getElementById("plt_token");

    t.value = key.value;
    f.action = url.value;
  };

  var validate_form = function() {
    var f = document.getElementById("plt_form");
    var t = document.getElementById("plt_token");

    if(t.value === "" || f.action === "") {
      alert("Please apply configuration first!");
      return false;
    }
  };
</script>



<br /><hr/>
<h2>2- Populate user details and submit form to create the user</h2>
<p>Please the check source code to extract the HTML code to integrate on your website!</p>

<!-- Pipplet code -->
<form method="GET" action="" id="plt_form">
  <label>First name</label><input type="text" name="first_name" value="John"/><div class="clear"></div>
  <label>Last name</label><input type="text" name="last_name" value="Doe"/><div class="clear"></div>
  <label>Email</label><input type="text" name="email" value="<EMAIL>"/><div class="clear"></div>
  <label>Phone number</label><input type="text" name="phone_number" value="0123456789"/><div class="clear"></div>

  <input type="hidden" id="plt_token" name="token" value="" />

  <input type="submit" value="Create Pipplet user!" onclick="javascript:validate_form();" /><div class="clear"></div>
</form>
<!-- End of Pipplet code -->



</body>
</html>

