var Module=typeof Module!=="undefined"?Module:{};!(function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.EncoderWorker=t():e.EncoderWorker=t()})("undefined"!=typeof self?self:this,(function(){return(function(e){function t(s){if(n[s])return n[s].exports;var i=n[s]={i:s,l:!1,exports:{}};return e[s].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};return t.m=e,t.c=n,t.d=(function(e,n,s){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:s})}),t.n=(function(e){var n=e&&e.__esModule?(function(){return e.default}):(function(){return e});return t.d(n,"a",n),n}),t.o=(function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}),t.p="",t(t.s=0)})([(function(e,t,n){"use strict";((function(t){var n,s,i=new Promise((function(e){s=e}));t.onmessage=(function(e){i.then((function(){switch(e.data.command){case"encode":n&&n.encode(e.data.buffers);break;case"done":n&&n.encodeFinalFrame();break;case"init":n=new r(e.data,Module)}}))});var r=(function(e,t){if(!t)throw new Error("Module with exports required to initialize an encoder instance");this.config=Object.assign({bufferLength:4096,encoderApplication:2049,encoderFrameSize:20,encoderSampleRate:48e3,maxBuffersPerPage:40,numberOfChannels:1,originalSampleRate:44100,resampleQuality:3,serial:Math.floor(Math.random()*Math.pow(2,32))},e),this._opus_encoder_create=t._opus_encoder_create,this._opus_encoder_ctl=t._opus_encoder_ctl,this._speex_resampler_process_interleaved_float=t._speex_resampler_process_interleaved_float,this._speex_resampler_init=t._speex_resampler_init,this._opus_encode_float=t._opus_encode_float,this._free=t._free,this._malloc=t._malloc,this.HEAPU8=t.HEAPU8,this.HEAP32=t.HEAP32,this.HEAPF32=t.HEAPF32,this.pageIndex=0,this.granulePosition=0,this.segmentData=new Uint8Array(65025),this.segmentDataIndex=0,this.segmentTable=new Uint8Array(255),this.segmentTableIndex=0,this.buffersInPage=0,this.initChecksumTable(),this.initCodec(),this.initResampler(),this.generateIdPage(),this.generateCommentPage(),1===this.config.numberOfChannels?this.interleave=(function(e){return e[0]}):this.interleavedBuffers=new Float32Array(this.config.bufferLength*this.config.numberOfChannels)});r.prototype.encode=(function(e){for(var t=this.interleave(e),n=0;n<t.length;){var s=Math.min(this.resampleBufferLength-this.resampleBufferIndex,t.length-n);if(this.resampleBuffer.set(t.subarray(n,n+s),this.resampleBufferIndex),n+=s,this.resampleBufferIndex+=s,this.resampleBufferIndex===this.resampleBufferLength){this._speex_resampler_process_interleaved_float(this.resampler,this.resampleBufferPointer,this.resampleSamplesPerChannelPointer,this.encoderBufferPointer,this.encoderSamplesPerChannelPointer);var i=this._opus_encode_float(this.encoder,this.encoderBufferPointer,this.encoderSamplesPerChannel,this.encoderOutputPointer,this.encoderOutputMaxLength);this.segmentPacket(i),this.resampleBufferIndex=0}}++this.buffersInPage>=this.config.maxBuffersPerPage&&this.generatePage()}),r.prototype.encodeFinalFrame=(function(){for(var e=[],n=0;n<this.config.numberOfChannels;++n)e.push(new Float32Array(this.config.bufferLength-this.resampleBufferIndex/this.config.numberOfChannels));this.encode(e),this.headerType+=4,this.generatePage(),t.postMessage(null),t.close()}),r.prototype.getChecksum=(function(e){for(var t=0,n=0;n<e.length;n++)t=t<<8^this.checksumTable[t>>>24&255^e[n]];return t>>>0}),r.prototype.generateCommentPage=(function(){var e=new DataView(this.segmentData.buffer);e.setUint32(0,1937076303,!0),e.setUint32(4,1936154964,!0),e.setUint32(8,10,!0),e.setUint32(12,1868784978,!0),e.setUint32(16,1919247474,!0),e.setUint16(20,21322,!0),e.setUint32(22,0,!0),this.segmentTableIndex=1,this.segmentDataIndex=this.segmentTable[0]=26,this.headerType=0,this.generatePage()}),r.prototype.generateIdPage=(function(){var e=new DataView(this.segmentData.buffer);e.setUint32(0,1937076303,!0),e.setUint32(4,1684104520,!0),e.setUint8(8,1,!0),e.setUint8(9,this.config.numberOfChannels,!0),e.setUint16(10,3840,!0),e.setUint32(12,this.config.originalSampleRateOverride||this.config.originalSampleRate,!0),e.setUint16(16,0,!0),e.setUint8(18,0,!0),this.segmentTableIndex=1,this.segmentDataIndex=this.segmentTable[0]=19,this.headerType=2,this.generatePage()}),r.prototype.generatePage=(function(){var e=this.lastPositiveGranulePosition===this.granulePosition?-1:this.granulePosition,n=new ArrayBuffer(27+this.segmentTableIndex+this.segmentDataIndex),s=new DataView(n),i=new Uint8Array(n);s.setUint32(0,1399285583,!0),s.setUint8(4,0,!0),s.setUint8(5,this.headerType,!0),s.setUint32(6,e,!0),(e>4294967296||e<0)&&s.setUint32(10,Math.floor(e/4294967296),!0),s.setUint32(14,this.config.serial,!0),s.setUint32(18,this.pageIndex++,!0),s.setUint8(26,this.segmentTableIndex,!0),i.set(this.segmentTable.subarray(0,this.segmentTableIndex),27),i.set(this.segmentData.subarray(0,this.segmentDataIndex),27+this.segmentTableIndex),s.setUint32(22,this.getChecksum(i),!0),t.postMessage(i,[i.buffer]),this.segmentTableIndex=0,this.segmentDataIndex=0,this.buffersInPage=0,e>0&&(this.lastPositiveGranulePosition=e)}),r.prototype.initChecksumTable=(function(){this.checksumTable=[];for(var e=0;e<256;e++){for(var t=e<<24,n=0;n<8;n++)t=0!=(2147483648&t)?t<<1^79764919:t<<1;this.checksumTable[e]=4294967295&t}}),r.prototype.setOpusControl=(function(e,t){var n=this._malloc(4);this.HEAP32[n>>2]=t,this._opus_encoder_ctl(this.encoder,e,n),this._free(n)}),r.prototype.initCodec=(function(){var e=this._malloc(4);this.encoder=this._opus_encoder_create(this.config.encoderSampleRate,this.config.numberOfChannels,this.config.encoderApplication,e),this._free(e),this.config.encoderBitRate&&this.setOpusControl(4002,this.config.encoderBitRate),this.config.encoderComplexity&&this.setOpusControl(4010,this.config.encoderComplexity),this.encoderSamplesPerChannel=this.config.encoderSampleRate*this.config.encoderFrameSize/1e3,this.encoderSamplesPerChannelPointer=this._malloc(4),this.HEAP32[this.encoderSamplesPerChannelPointer>>2]=this.encoderSamplesPerChannel,this.encoderBufferLength=this.encoderSamplesPerChannel*this.config.numberOfChannels,this.encoderBufferPointer=this._malloc(4*this.encoderBufferLength),this.encoderBuffer=this.HEAPF32.subarray(this.encoderBufferPointer>>2,(this.encoderBufferPointer>>2)+this.encoderBufferLength),this.encoderOutputMaxLength=4e3,this.encoderOutputPointer=this._malloc(this.encoderOutputMaxLength),this.encoderOutputBuffer=this.HEAPU8.subarray(this.encoderOutputPointer,this.encoderOutputPointer+this.encoderOutputMaxLength)}),r.prototype.initResampler=(function(){var e=this._malloc(4);this.resampler=this._speex_resampler_init(this.config.numberOfChannels,this.config.originalSampleRate,this.config.encoderSampleRate,this.config.resampleQuality,e),this._free(e),this.resampleBufferIndex=0,this.resampleSamplesPerChannel=this.config.originalSampleRate*this.config.encoderFrameSize/1e3,this.resampleSamplesPerChannelPointer=this._malloc(4),this.HEAP32[this.resampleSamplesPerChannelPointer>>2]=this.resampleSamplesPerChannel,this.resampleBufferLength=this.resampleSamplesPerChannel*this.config.numberOfChannels,this.resampleBufferPointer=this._malloc(4*this.resampleBufferLength),this.resampleBuffer=this.HEAPF32.subarray(this.resampleBufferPointer>>2,(this.resampleBufferPointer>>2)+this.resampleBufferLength)}),r.prototype.interleave=(function(e){for(var t=0;t<this.config.bufferLength;t++)for(var n=0;n<this.config.numberOfChannels;n++)this.interleavedBuffers[t*this.config.numberOfChannels+n]=e[n][t];return this.interleavedBuffers}),r.prototype.segmentPacket=(function(e){for(var t=0;e>=0;){255===this.segmentTableIndex&&(this.generatePage(),this.headerType=1);var n=Math.min(e,255);this.segmentTable[this.segmentTableIndex++]=n,this.segmentData.set(this.encoderOutputBuffer.subarray(t,t+n),this.segmentDataIndex),this.segmentDataIndex+=n,t+=n,e-=255}this.granulePosition+=48*this.config.encoderFrameSize,255===this.segmentTableIndex&&(this.generatePage(),this.headerType=0)}),Module||(Module={}),Module.mainReady=i,Module.OggOpusEncoder=r,Module.onRuntimeInitialized=s,e.exports=Module})).call(t,n(1))}),(function(e,t){var n;n=(function(){return this})();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n})])}));var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}Module["arguments"]=[];Module["thisProgram"]="./this.program";Module["quit"]=(function(status,toThrow){throw toThrow});Module["preRun"]=[];Module["postRun"]=[];var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;if(Module["ENVIRONMENT"]){if(Module["ENVIRONMENT"]==="WEB"){ENVIRONMENT_IS_WEB=true}else if(Module["ENVIRONMENT"]==="WORKER"){ENVIRONMENT_IS_WORKER=true}else if(Module["ENVIRONMENT"]==="NODE"){ENVIRONMENT_IS_NODE=true}else if(Module["ENVIRONMENT"]==="SHELL"){ENVIRONMENT_IS_SHELL=true}else{throw new Error("Module['ENVIRONMENT'] value is not valid. must be one of: WEB|WORKER|NODE|SHELL.")}}else{ENVIRONMENT_IS_WEB=typeof window==="object";ENVIRONMENT_IS_WORKER=typeof importScripts==="function";ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof require==="function"&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER;ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER}if(ENVIRONMENT_IS_NODE){var nodeFS;var nodePath;Module["read"]=function shell_read(filename,binary){var ret;if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);ret=nodeFS["readFileSync"](filename);return binary?ret:ret.toString()};Module["readBinary"]=function readBinary(filename){var ret=Module["read"](filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process["argv"].length>1){Module["thisProgram"]=process["argv"][1].replace(/\\/g,"/")}Module["arguments"]=process["argv"].slice(2);if(typeof module!=="undefined"){module["exports"]=Module}process["on"]("uncaughtException",(function(ex){if(!(ex instanceof ExitStatus)){throw ex}}));process["on"]("unhandledRejection",(function(reason,p){process["exit"](1)}));Module["inspect"]=(function(){return"[Emscripten Module object]"})}else if(ENVIRONMENT_IS_SHELL){if(typeof read!="undefined"){Module["read"]=function shell_read(f){return read(f)}}Module["readBinary"]=function readBinary(f){var data;if(typeof readbuffer==="function"){return new Uint8Array(readbuffer(f))}data=read(f,"binary");assert(typeof data==="object");return data};if(typeof scriptArgs!="undefined"){Module["arguments"]=scriptArgs}else if(typeof arguments!="undefined"){Module["arguments"]=arguments}if(typeof quit==="function"){Module["quit"]=(function(status,toThrow){quit(status)})}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){Module["read"]=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){Module["readBinary"]=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}Module["readAsync"]=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)};if(typeof arguments!="undefined"){Module["arguments"]=arguments}Module["setWindowTitle"]=(function(title){document.title=title})}Module["print"]=typeof console!=="undefined"?console.log:typeof print!=="undefined"?print:null;Module["printErr"]=typeof printErr!=="undefined"?printErr:typeof console!=="undefined"&&console.warn||Module["print"];Module.print=Module["print"];Module.printErr=Module["printErr"];for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=undefined;var STACK_ALIGN=16;function staticAlloc(size){assert(!staticSealed);var ret=STATICTOP;STATICTOP=STATICTOP+size+15&-16;return ret}function alignMemory(size,factor){if(!factor)factor=STACK_ALIGN;var ret=size=Math.ceil(size/factor)*factor;return ret}var functionPointers=new Array(0);var GLOBAL_BASE=1024;var ABORT=0;var EXITSTATUS=0;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;var UTF16Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf-16le"):undefined;var WASM_PAGE_SIZE=65536;var ASMJS_PAGE_SIZE=16777216;function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBuffer(buf){Module["buffer"]=buffer=buf}function updateGlobalBufferViews(){Module["HEAP8"]=HEAP8=new Int8Array(buffer);Module["HEAP16"]=HEAP16=new Int16Array(buffer);Module["HEAP32"]=HEAP32=new Int32Array(buffer);Module["HEAPU8"]=HEAPU8=new Uint8Array(buffer);Module["HEAPU16"]=HEAPU16=new Uint16Array(buffer);Module["HEAPU32"]=HEAPU32=new Uint32Array(buffer);Module["HEAPF32"]=HEAPF32=new Float32Array(buffer);Module["HEAPF64"]=HEAPF64=new Float64Array(buffer)}var STATIC_BASE,STATICTOP,staticSealed;var STACK_BASE,STACKTOP,STACK_MAX;var DYNAMIC_BASE,DYNAMICTOP_PTR;STATIC_BASE=STATICTOP=STACK_BASE=STACKTOP=STACK_MAX=DYNAMIC_BASE=DYNAMICTOP_PTR=0;staticSealed=false;function abortOnCannotGrowMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+TOTAL_MEMORY+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function enlargeMemory(){abortOnCannotGrowMemory()}var TOTAL_STACK=Module["TOTAL_STACK"]||5242880;var TOTAL_MEMORY=Module["TOTAL_MEMORY"]||16777216;if(TOTAL_MEMORY<TOTAL_STACK)Module.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")");if(Module["buffer"]){buffer=Module["buffer"]}else{if(typeof WebAssembly==="object"&&typeof WebAssembly.Memory==="function"){Module["wasmMemory"]=new WebAssembly.Memory({"initial":TOTAL_MEMORY/WASM_PAGE_SIZE,"maximum":TOTAL_MEMORY/WASM_PAGE_SIZE});buffer=Module["wasmMemory"].buffer}else{buffer=new ArrayBuffer(TOTAL_MEMORY)}Module["buffer"]=buffer}updateGlobalBufferViews();function getTotalMemory(){return TOTAL_MEMORY}HEAP32[0]=1668509029;HEAP16[1]=25459;if(HEAPU8[2]!==115||HEAPU8[3]!==99)throw"Runtime error: expected the system to be little-endian!";function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback();continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){Module["dynCall_v"](func)}else{Module["dynCall_vi"](func,callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){if(runtimeInitialized)return;runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__);runtimeExited=true}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var Math_abs=Math.abs;var Math_cos=Math.cos;var Math_sin=Math.sin;var Math_tan=Math.tan;var Math_acos=Math.acos;var Math_asin=Math.asin;var Math_atan=Math.atan;var Math_atan2=Math.atan2;var Math_exp=Math.exp;var Math_log=Math.log;var Math_sqrt=Math.sqrt;var Math_ceil=Math.ceil;var Math_floor=Math.floor;var Math_pow=Math.pow;var Math_imul=Math.imul;var Math_fround=Math.fround;var Math_round=Math.round;var Math_min=Math.min;var Math_max=Math.max;var Math_clz32=Math.clz32;var Math_trunc=Math.trunc;var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return String.prototype.startsWith?filename.startsWith(dataURIPrefix):filename.indexOf(dataURIPrefix)===0}function integrateWasmJS(){var wasmTextFile="encoderWorker.min.wast";var wasmBinaryFile="encoderWorker.min.wasm";var asmjsCodeFile="encoderWorker.min.temp.asm.js";if(typeof Module["locateFile"]==="function"){if(!isDataURI(wasmTextFile)){wasmTextFile=Module["locateFile"](wasmTextFile)}if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=Module["locateFile"](wasmBinaryFile)}if(!isDataURI(asmjsCodeFile)){asmjsCodeFile=Module["locateFile"](asmjsCodeFile)}}var wasmPageSize=64*1024;var info={"global":null,"env":null,"asm2wasm":{"f64-rem":(function(x,y){return x%y}),"debugger":(function(){debugger})},"parent":Module};var exports=null;function mergeMemory(newBuffer){var oldBuffer=Module["buffer"];if(newBuffer.byteLength<oldBuffer.byteLength){Module["printErr"]("the new buffer in mergeMemory is smaller than the previous one. in native wasm, we should grow memory here")}var oldView=new Int8Array(oldBuffer);var newView=new Int8Array(newBuffer);newView.set(oldView);updateGlobalBuffer(newBuffer);updateGlobalBufferViews()}function fixImports(imports){return imports}function getBinary(){try{if(Module["wasmBinary"]){return new Uint8Array(Module["wasmBinary"])}if(Module["readBinary"]){return Module["readBinary"](wasmBinaryFile)}else{throw"on the web, we need the wasm binary to be preloaded and set on Module['wasmBinary']. emcc.py will do that for you when generating HTML (but not JS)"}}catch(err){abort(err)}}function getBinaryPromise(){if(!Module["wasmBinary"]&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then((function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()})).catch((function(){return getBinary()}))}return new Promise((function(resolve,reject){resolve(getBinary())}))}function doNativeWasm(global,env,providedBuffer){if(typeof WebAssembly!=="object"){Module["printErr"]("no native wasm support detected");return false}if(!(Module["wasmMemory"]instanceof WebAssembly.Memory)){Module["printErr"]("no native wasm Memory in use");return false}env["memory"]=Module["wasmMemory"];info["global"]={"NaN":NaN,"Infinity":Infinity};info["global.Math"]=Math;info["env"]=env;function receiveInstance(instance,module){exports=instance.exports;if(exports.memory)mergeMemory(exports.memory);Module["asm"]=exports;Module["usingWasm"]=true;removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){Module["printErr"]("Module.instantiateWasm callback failed with error: "+e);return false}}function receiveInstantiatedSource(output){receiveInstance(output["instance"],output["module"])}function instantiateArrayBuffer(receiver){getBinaryPromise().then((function(binary){return WebAssembly.instantiate(binary,info)})).then(receiver).catch((function(reason){Module["printErr"]("failed to asynchronously prepare wasm: "+reason);abort(reason)}))}if(!Module["wasmBinary"]&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){WebAssembly.instantiateStreaming(fetch(wasmBinaryFile,{credentials:"same-origin"}),info).then(receiveInstantiatedSource).catch((function(reason){Module["printErr"]("wasm streaming compile failed: "+reason);Module["printErr"]("falling back to ArrayBuffer instantiation");instantiateArrayBuffer(receiveInstantiatedSource)}))}else{instantiateArrayBuffer(receiveInstantiatedSource)}return{}}Module["asmPreload"]=Module["asm"];var asmjsReallocBuffer=Module["reallocBuffer"];var wasmReallocBuffer=(function(size){var PAGE_MULTIPLE=Module["usingWasm"]?WASM_PAGE_SIZE:ASMJS_PAGE_SIZE;size=alignUp(size,PAGE_MULTIPLE);var old=Module["buffer"];var oldSize=old.byteLength;if(Module["usingWasm"]){try{var result=Module["wasmMemory"].grow((size-oldSize)/wasmPageSize);if(result!==(-1|0)){return Module["buffer"]=Module["wasmMemory"].buffer}else{return null}}catch(e){return null}}});Module["reallocBuffer"]=(function(size){if(finalMethod==="asmjs"){return asmjsReallocBuffer(size)}else{return wasmReallocBuffer(size)}});var finalMethod="";Module["asm"]=(function(global,env,providedBuffer){env=fixImports(env);if(!env["table"]){var TABLE_SIZE=Module["wasmTableSize"];if(TABLE_SIZE===undefined)TABLE_SIZE=1024;var MAX_TABLE_SIZE=Module["wasmMaxTableSize"];if(typeof WebAssembly==="object"&&typeof WebAssembly.Table==="function"){if(MAX_TABLE_SIZE!==undefined){env["table"]=new WebAssembly.Table({"initial":TABLE_SIZE,"maximum":MAX_TABLE_SIZE,"element":"anyfunc"})}else{env["table"]=new WebAssembly.Table({"initial":TABLE_SIZE,element:"anyfunc"})}}else{env["table"]=new Array(TABLE_SIZE)}Module["wasmTable"]=env["table"]}if(!env["memoryBase"]){env["memoryBase"]=Module["STATIC_BASE"]}if(!env["tableBase"]){env["tableBase"]=0}var exports;exports=doNativeWasm(global,env,providedBuffer);if(!exports)abort("no binaryen method succeeded. consider enabling more options, like interpreting, if you want that: https://github.com/kripken/emscripten/wiki/WebAssembly#binaryen-methods");return exports})}integrateWasmJS();STATIC_BASE=GLOBAL_BASE;STATICTOP=STATIC_BASE+35552;__ATINIT__.push();var STATIC_BUMP=35552;Module["STATIC_BASE"]=STATIC_BASE;Module["STATIC_BUMP"]=STATIC_BUMP;STATICTOP+=16;function _llvm_exp2_f32(x){return Math.pow(2,x)}function _llvm_exp2_f64(){return _llvm_exp2_f32.apply(null,arguments)}function _llvm_stackrestore(p){var self=_llvm_stacksave;var ret=self.LLVM_SAVEDSTACKS[p];self.LLVM_SAVEDSTACKS.splice(p,1);stackRestore(ret)}function _llvm_stacksave(){var self=_llvm_stacksave;if(!self.LLVM_SAVEDSTACKS){self.LLVM_SAVEDSTACKS=[]}self.LLVM_SAVEDSTACKS.push(stackSave());return self.LLVM_SAVEDSTACKS.length-1}function _emscripten_memcpy_big(dest,src,num){HEAPU8.set(HEAPU8.subarray(src,src+num),dest);return dest}function ___setErrNo(value){if(Module["___errno_location"])HEAP32[Module["___errno_location"]()>>2]=value;return value}DYNAMICTOP_PTR=staticAlloc(4);STACK_BASE=STACKTOP=alignMemory(STATICTOP);STACK_MAX=STACK_BASE+TOTAL_STACK;DYNAMIC_BASE=alignMemory(STACK_MAX);HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE;staticSealed=true;Module["wasmTableSize"]=10;Module["wasmMaxTableSize"]=10;Module.asmGlobalArg={};Module.asmLibraryArg={"abort":abort,"enlargeMemory":enlargeMemory,"getTotalMemory":getTotalMemory,"abortOnCannotGrowMemory":abortOnCannotGrowMemory,"___setErrNo":___setErrNo,"_emscripten_memcpy_big":_emscripten_memcpy_big,"_llvm_exp2_f64":_llvm_exp2_f64,"_llvm_stackrestore":_llvm_stackrestore,"_llvm_stacksave":_llvm_stacksave,"DYNAMICTOP_PTR":DYNAMICTOP_PTR,"STACKTOP":STACKTOP};var asm=Module["asm"](Module.asmGlobalArg,Module.asmLibraryArg,buffer);Module["asm"]=asm;var _free=Module["_free"]=(function(){return Module["asm"]["_free"].apply(null,arguments)});var _malloc=Module["_malloc"]=(function(){return Module["asm"]["_malloc"].apply(null,arguments)});var _opus_encode_float=Module["_opus_encode_float"]=(function(){return Module["asm"]["_opus_encode_float"].apply(null,arguments)});var _opus_encoder_create=Module["_opus_encoder_create"]=(function(){return Module["asm"]["_opus_encoder_create"].apply(null,arguments)});var _opus_encoder_ctl=Module["_opus_encoder_ctl"]=(function(){return Module["asm"]["_opus_encoder_ctl"].apply(null,arguments)});var _speex_resampler_destroy=Module["_speex_resampler_destroy"]=(function(){return Module["asm"]["_speex_resampler_destroy"].apply(null,arguments)});var _speex_resampler_init=Module["_speex_resampler_init"]=(function(){return Module["asm"]["_speex_resampler_init"].apply(null,arguments)});var _speex_resampler_process_interleaved_float=Module["_speex_resampler_process_interleaved_float"]=(function(){return Module["asm"]["_speex_resampler_process_interleaved_float"].apply(null,arguments)});var stackRestore=Module["stackRestore"]=(function(){return Module["asm"]["stackRestore"].apply(null,arguments)});var stackSave=Module["stackSave"]=(function(){return Module["asm"]["stackSave"].apply(null,arguments)});Module["asm"]=asm;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}ExitStatus.prototype=new Error;ExitStatus.prototype.constructor=ExitStatus;var initialStackTop;dependenciesFulfilled=function runCaller(){if(!Module["calledRun"])run();if(!Module["calledRun"])dependenciesFulfilled=runCaller};function run(args){args=args||Module["arguments"];if(runDependencies>0){return}preRun();if(runDependencies>0)return;if(Module["calledRun"])return;function doRun(){if(Module["calledRun"])return;Module["calledRun"]=true;if(ABORT)return;ensureInitRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout((function(){setTimeout((function(){Module["setStatus"]("")}),1);doRun()}),1)}else{doRun()}}Module["run"]=run;function exit(status,implicit){if(implicit&&Module["noExitRuntime"]&&status===0){return}if(Module["noExitRuntime"]){}else{ABORT=true;EXITSTATUS=status;STACKTOP=initialStackTop;exitRuntime();if(Module["onExit"])Module["onExit"](status)}if(ENVIRONMENT_IS_NODE){process["exit"](status)}Module["quit"](status,new ExitStatus(status))}Module["exit"]=exit;function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}if(what!==undefined){Module.print(what);Module.printErr(what);what=JSON.stringify(what)}else{what=""}ABORT=true;EXITSTATUS=1;throw"abort("+what+"). Build with -s ASSERTIONS=1 for more info."}Module["abort"]=abort;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}Module["noExitRuntime"]=true;run()



