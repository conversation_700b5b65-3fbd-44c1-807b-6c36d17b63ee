
var OGVDecoderAudioVorbis = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  return (
function(OGVDecoderAudioVorbis) {
  OGVDecoderAudioVorbis = OGVDecoderAudioVorbis || {};

var a;a||(a=typeof OGVDecoderAudioVorbis !== 'undefined' ? OGVDecoderAudioVorbis : {});var f=a;a.memoryLimit&&(a.TOTAL_MEMORY=f.memoryLimit);var k={},l;for(l in a)a.hasOwnProperty(l)&&(k[l]=a[l]);a.arguments=[];a.thisProgram="./this.program";a.quit=function(b,c){throw c;};a.preRun=[];a.postRun=[];var m=!1,n=!1,q=!1,r=!1;m="object"===typeof window;n="function"===typeof importScripts;q="object"===typeof process&&"function"===typeof require&&!m&&!n;r=!m&&!q&&!n;var t="";
if(q){t=__dirname+"/";var u,v;a.read=function(b,c){var d=w(b);d||(u||(u=require("fs")),v||(v=require("path")),b=v.normalize(b),d=u.readFileSync(b));return c?d:d.toString()};a.readBinary=function(b){b=a.read(b,!0);b.buffer||(b=new Uint8Array(b));assert(b.buffer);return b};1<process.argv.length&&(a.thisProgram=process.argv[1].replace(/\\/g,"/"));a.arguments=process.argv.slice(2);process.on("unhandledRejection",x);a.quit=function(b){process.exit(b)};a.inspect=function(){return"[Emscripten Module object]"}}else if(r)"undefined"!=
typeof read&&(a.read=function(b){var c=w(b);return c?y(c):read(b)}),a.readBinary=function(b){var c;if(c=w(b))return c;if("function"===typeof readbuffer)return new Uint8Array(readbuffer(b));c=read(b,"binary");assert("object"===typeof c);return c},"undefined"!=typeof scriptArgs?a.arguments=scriptArgs:"undefined"!=typeof arguments&&(a.arguments=arguments),"function"===typeof quit&&(a.quit=function(b){quit(b)});else if(m||n)n?t=self.location.href:document.currentScript&&(t=document.currentScript.src),
_scriptDir&&(t=_scriptDir),0!==t.indexOf("blob:")?t=t.substr(0,t.lastIndexOf("/")+1):t="",a.read=function(b){try{var c=new XMLHttpRequest;c.open("GET",b,!1);c.send(null);return c.responseText}catch(d){if(b=w(b))return y(b);throw d;}},n&&(a.readBinary=function(b){try{var c=new XMLHttpRequest;c.open("GET",b,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}catch(d){if(b=w(b))return b;throw d;}}),a.readAsync=function(b,c,d){var e=new XMLHttpRequest;e.open("GET",b,!0);e.responseType=
"arraybuffer";e.onload=function(){if(200==e.status||0==e.status&&e.response)c(e.response);else{var h=w(b);h?c(h.buffer):d()}};e.onerror=d;e.send(null)},a.setWindowTitle=function(b){document.title=b};var A=a.print||("undefined"!==typeof console?console.log.bind(console):"undefined"!==typeof print?print:null),B=a.printErr||("undefined"!==typeof printErr?printErr:"undefined"!==typeof console&&console.warn.bind(console)||A);for(l in k)k.hasOwnProperty(l)&&(a[l]=k[l]);k=void 0;var C=0,D=!1;
function assert(b,c){b||x("Assertion failed: "+c)}"undefined"!==typeof TextDecoder&&new TextDecoder("utf8");"undefined"!==typeof TextDecoder&&new TextDecoder("utf-16le");var buffer,E,F,G=a.TOTAL_MEMORY||16777216;5242880>G&&B("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+G+"! (TOTAL_STACK=5242880)");a.buffer?buffer=a.buffer:(buffer=new ArrayBuffer(G),a.buffer=buffer);a.HEAP8=new Int8Array(buffer);a.HEAP16=new Int16Array(buffer);a.HEAP32=F=new Int32Array(buffer);a.HEAPU8=E=new Uint8Array(buffer);
a.HEAPU16=new Uint16Array(buffer);a.HEAPU32=new Uint32Array(buffer);a.HEAPF32=new Float32Array(buffer);a.HEAPF64=new Float64Array(buffer);F[14560]=5301376;function H(b){for(;0<b.length;){var c=b.shift();if("function"==typeof c)c();else{var d=c.s;"number"===typeof d?void 0===c.o?a.dynCall_v(d):a.dynCall_vi(d,c.o):d(void 0===c.o?null:c.o)}}}var I=[],aa=[],ba=[],J=[],K=!1;function ca(){var b=a.preRun.shift();I.unshift(b)}
Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(b,c){var d=b&65535,e=c&65535;return d*e+((b>>>16)*e+d*(c>>>16)<<16)|0});Math.clz32||(Math.clz32=function(b){var c=32,d=b>>16;d&&(c-=16,b=d);if(d=b>>8)c-=8,b=d;if(d=b>>4)c-=4,b=d;if(d=b>>2)c-=2,b=d;return b>>1?c-2:c-b});Math.trunc||(Math.trunc=function(b){return 0>b?Math.ceil(b):Math.floor(b)});var L=0,M=null,N=null;a.preloadedImages={};a.preloadedAudios={};var O=null,P="data:application/octet-stream;base64,";O="data:application/octet-stream;base64,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";
function Q(){x("OOM")}var da=!1;function y(b){for(var c=[],d=0;d<b.length;d++){var e=b[d];255<e&&(da&&assert(!1,"Character code "+e+" ("+String.fromCharCode(e)+")  at offset "+d+" not in 0x00-0xFF."),e&=255);c.push(String.fromCharCode(e))}return c.join("")}
var ea="function"===typeof atob?atob:function(b){var c="",d=0;b=b.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));var h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));var p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));e=e<<2|h>>4;
h=(h&15)<<4|p>>2;var z=(p&3)<<6|g;c+=String.fromCharCode(e);64!==p&&(c+=String.fromCharCode(h));64!==g&&(c+=String.fromCharCode(z))}while(d<b.length);return c};
function w(b){if(String.prototype.startsWith?b.startsWith(P):0===b.indexOf(P)){b=b.slice(P.length);if("boolean"===typeof q&&q){try{var c=Buffer.from(b,"base64")}catch(p){c=new Buffer(b,"base64")}var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength)}else try{var e=ea(b),h=new Uint8Array(e.length);for(c=0;c<e.length;++c)h[c]=e.charCodeAt(c);d=h}catch(p){throw Error("Converting base64 string to bytes failed.");}return d}}
var fa={Math:Math,Int8Array:Int8Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Float32Array:Float32Array,Float64Array:Float64Array},ha={a:x,b:function(b){C=b},c:function(){return C},d:function(b){a.___errno_location&&(F[a.___errno_location()>>2]=b);return b},e:function(){return G},f:function(b,c,d){E.set(E.subarray(c,c+d),b)},g:function(b){Q(b)},h:function(b){if(!a.noExitRuntime&&(D=!0,a.onExit))a.onExit(b);a.quit(b,new R(b))},i:function(b,c,d){var e=a.HEAPU32,h=a.HEAPF32,p=[];if(0!==b)for(var g,
z=0;z<c;z++)g=e[b/4+z],h.buffer.slice?(g=h.buffer.slice(g,g+4*d),g=new Float32Array(g)):(g=h.subarray(g/4,g/4+d),g=new Float32Array(g)),p.push(g);a.audioBuffer=p},j:function(b,c){a.audioFormat={channels:b,rate:c};a.loadedMetadata=!0},k:Q,l:58480,m:58240};// EMSCRIPTEN_START_ASM
var S=(/** @suppress {uselessCode} */ function(global,env,buffer) {
"use asm";var a=new global.Int8Array(buffer),b=new global.Int32Array(buffer),c=new global.Uint8Array(buffer),d=new global.Float32Array(buffer),e=new global.Float64Array(buffer),f=env.l|0,g=env.m|0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0.0,p=global.Math.floor,q=global.Math.abs,r=global.Math.sqrt,s=global.Math.pow,t=global.Math.cos,u=global.Math.sin,v=global.Math.atan,w=global.Math.exp,x=global.Math.log,y=global.Math.ceil,z=global.Math.imul,A=env.a,B=env.b,C=env.c,D=env.d,E=env.e,F=env.f,G=env.g,H=env.h,I=env.i,J=env.j,K=env.k,L=58496,M=5301376,N=0.0;
// EMSCRIPTEN_START_FUNCS
function W(a){a=a|0;var b=0;b=L;L=L+a|0;L=L+15&-16;return b|0}function X(){return L|0}function Y(a){a=a|0;L=a}function Z(a,b){a=a|0;b=b|0;L=a;M=b}function _(){Ha(57668);Fa(57700);return}function $(a,c){a=a|0;c=c|0;var d=0,e=0;e=L;L=L+32|0;d=e;ca(d,a,c);if(!(b[14416]|0))b[d+8>>2]=1;if(Ja(57668,57700,d)|0){d=0;L=e;return d|0}d=b[14416]|0;b[14416]=d+1;if((d|0)<2){d=1;L=e;return d|0}ja(57440,57668)|0;da(57440,57552)|0;J(b[14418]|0,b[14419]|0);d=1;L=e;return d|0}function aa(a,c){a=a|0;c=c|0;var d=0,e=0,f=0;e=L;L=L+48|0;f=e;d=e+32|0;ca(f,a,c);if(qa(57552,f)|0){f=0;L=e;return f|0}ka(57440,57552)|0;f=la(57440,d)|0;I(b[d>>2]|0,b[14418]|0,f|0);ma(57440,f)|0;f=1;L=e;return f|0}function ba(){if(!(b[14416]|0))return;Ia(57668);ia(57440);ga(57552)|0;Ga(57700);return}function ca(a,c,d){a=a|0;c=c|0;d=d|0;b[a>>2]=c;b[a+4>>2]=d;d=a+8|0;b[d>>2]=0;b[d+4>>2]=0;b[d+8>>2]=0;b[d+12>>2]=0;b[d+16>>2]=0;b[d+20>>2]=0;return}function da(a,c){a=a|0;c=c|0;var e=0,f=0;e=c;f=e+112|0;do{b[e>>2]=0;e=e+4|0}while((e|0)<(f|0));b[c+64>>2]=a;b[c+76>>2]=0;b[c+68>>2]=0;if(!(b[a>>2]|0))return 0;e=gc(1,72)|0;b[c+104>>2]=e;d[e+4>>2]=-9999.0;f=c+4|0;c=gc(1,20)|0;b[e+12>>2]=c;Yb(c);c=gc(1,20)|0;b[e+16>>2]=c;Yb(c);c=gc(1,20)|0;b[e+20>>2]=c;Yb(c);c=gc(1,20)|0;b[e+24>>2]=c;Yb(c);c=gc(1,20)|0;b[e+28>>2]=c;Yb(c);c=gc(1,20)|0;b[e+32>>2]=c;Yb(c);c=gc(1,20)|0;b[e+36>>2]=c;Yb(c);b[e+40>>2]=f;Yb(f);f=gc(1,20)|0;b[e+44>>2]=f;Yb(f);f=gc(1,20)|0;b[e+48>>2]=f;Yb(f);f=gc(1,20)|0;b[e+52>>2]=f;Yb(f);f=gc(1,20)|0;b[e+56>>2]=f;Yb(f);f=gc(1,20)|0;b[e+60>>2]=f;Yb(f);f=gc(1,20)|0;b[e+64>>2]=f;Yb(f);f=gc(1,20)|0;b[e+68>>2]=f;Yb(f);return 0}function ea(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=c+7&-8;f=a+72|0;g=b[f>>2]|0;h=a+76|0;c=a+68|0;d=b[c>>2]|0;if((g+e|0)<=(b[h>>2]|0)){a=d;h=g;a=a+h|0;h=h+e|0;b[f>>2]=h;return a|0}if(d|0){i=ec(8)|0;j=a+80|0;b[j>>2]=(b[j>>2]|0)+g;a=a+84|0;b[i+4>>2]=b[a>>2];b[i>>2]=d;b[a>>2]=i}b[h>>2]=e;j=ec(e)|0;b[c>>2]=j;b[f>>2]=0;i=0;j=j+i|0;i=i+e|0;b[f>>2]=i;return j|0}function fa(a){a=a|0;var c=0,d=0,e=0,f=0,g=0;e=a+84|0;c=b[e>>2]|0;if(c|0)do{d=c;c=b[c+4>>2]|0;fc(b[d>>2]|0);fc(d)}while((c|0)!=0);c=a+80|0;d=b[c>>2]|0;if(!d){a=a+72|0;b[a>>2]=0;b[e>>2]=0;return}g=a+68|0;f=a+76|0;b[g>>2]=hc(b[g>>2]|0,(b[f>>2]|0)+d|0)|0;b[f>>2]=(b[f>>2]|0)+(b[c>>2]|0);b[c>>2]=0;a=a+72|0;b[a>>2]=0;b[e>>2]=0;return}function ga(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0;g=b[a+104>>2]|0;h=a+84|0;c=b[h>>2]|0;if(c|0)do{f=c;c=b[c+4>>2]|0;fc(b[f>>2]|0);fc(f)}while((c|0)!=0);e=a+80|0;f=b[e>>2]|0;d=a+68|0;c=b[d>>2]|0;if(f){i=a+76|0;c=hc(c,(b[i>>2]|0)+f|0)|0;b[d>>2]=c;b[i>>2]=(b[i>>2]|0)+(b[e>>2]|0);b[e>>2]=0}b[a+72>>2]=0;b[h>>2]=0;if(c|0)fc(c);if(!g){c=a+112|0;do{b[a>>2]=0;a=a+4|0}while((a|0)<(c|0));return 0}c=g+12|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+16|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+20|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+24|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+28|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+32|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+36|0;_b(b[c>>2]|0);fc(b[c>>2]|0);_b(b[g+40>>2]|0);c=g+44|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+48|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+52|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+56|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+60|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+64|0;_b(b[c>>2]|0);fc(b[c>>2]|0);c=g+68|0;_b(b[c>>2]|0);fc(b[c>>2]|0);fc(g);c=a+112|0;do{b[a>>2]=0;a=a+4|0}while((a|0)<(c|0));return 0}function ha(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;p=b[c+28>>2]|0;if(!p){p=1;return p|0}i=p+8|0;if((b[i>>2]|0)<1){p=1;return p|0}f=b[p>>2]|0;if((f|0)<64){p=1;return p|0}n=p+4|0;if((b[n>>2]|0)<(f|0)){p=1;return p|0}h=b[p+3656>>2]|0;f=a;g=f+112|0;do{b[f>>2]=0;f=f+4|0}while((f|0)<(g|0));o=gc(1,136)|0;b[a+104>>2]=o;b[a+4>>2]=c;b[o+44>>2]=Sa((b[i>>2]|0)+-1|0)|0;q=gc(1,4)|0;b[o+12>>2]=q;f=gc(1,4)|0;i=o+16|0;b[i>>2]=f;g=gc(1,20)|0;b[q>>2]=g;b[f>>2]=gc(1,20)|0;bb(g,b[p>>2]>>h);bb(b[b[i>>2]>>2]|0,b[n>>2]>>h);b[o+4>>2]=(Sa(b[p>>2]|0)|0)+-7;b[o+8>>2]=(Sa(b[n>>2]|0)|0)+-7;a:do if(!d){d=p+2848|0;if((b[d>>2]|0)==0?(m=p+24|0,b[d>>2]=gc(b[m>>2]|0,56)|0,e=b[m>>2]|0,(e|0)>0):0){h=0;while(1){f=p+1824+(h<<2)|0;g=b[f>>2]|0;if(!g)break;if(Za((b[d>>2]|0)+(h*56|0)|0,g)|0){l=21;break}Wa(b[f>>2]|0);b[f>>2]=0;h=h+1|0;e=b[m>>2]|0;if((h|0)>=(e|0))break a}if((l|0)==21)e=b[m>>2]|0;if((e|0)>0){h=0;do{f=p+1824+(h<<2)|0;g=b[f>>2]|0;if(g){Wa(g);b[f>>2]=0;e=b[m>>2]|0}h=h+1|0}while((h|0)<(e|0))}ia(a);q=-1;return q|0}}else{kb(o+20|0,b[p>>2]|0);kb(o+32|0,b[n>>2]|0);f=p+2848|0;if(((b[f>>2]|0)==0?(k=p+24|0,j=gc(b[k>>2]|0,56)|0,b[f>>2]=j,(b[k>>2]|0)>0):0)?(Ya(j,b[p+1824>>2]|0)|0,(b[k>>2]|0)>1):0){e=1;do{Ya((b[f>>2]|0)+(e*56|0)|0,b[p+1824+(e<<2)>>2]|0)|0;e=e+1|0}while((e|0)<(b[k>>2]|0))}g=p+28|0;f=gc(b[g>>2]|0,52)|0;h=o+56|0;b[h>>2]=f;b:do if((b[g>>2]|0)>0){d=p+2868|0;i=c+8|0;e=0;while(1){q=b[p+2852+(e<<2)>>2]|0;ta(f+(e*52|0)|0,q,d,(b[p+(b[q>>2]<<2)>>2]|0)/2|0,b[i>>2]|0);e=e+1|0;if((e|0)>=(b[g>>2]|0))break b;f=b[h>>2]|0}}while(0);b[a>>2]=1}while(0);h=b[n>>2]|0;b[a+16>>2]=h;f=b[c+4>>2]|0;q=f<<2;g=a+8|0;b[g>>2]=ec(q)|0;b[a+12>>2]=ec(q)|0;if((f|0)>0){e=0;do{q=gc(h,4)|0;b[(b[g>>2]|0)+(e<<2)>>2]=q;e=e+1|0}while((e|0)!=(f|0))}b[a+36>>2]=0;b[a+40>>2]=0;f=(b[n>>2]|0)/2|0;b[a+48>>2]=f;b[a+20>>2]=f;f=p+16|0;g=o+48|0;b[g>>2]=gc(b[f>>2]|0,4)|0;d=p+20|0;h=o+52|0;b[h>>2]=gc(b[d>>2]|0,4)|0;if((b[f>>2]|0)>0){e=0;do{q=P[b[(b[57232+(b[p+800+(e<<2)>>2]<<2)>>2]|0)+8>>2]&15](a,b[p+1056+(e<<2)>>2]|0)|0;b[(b[g>>2]|0)+(e<<2)>>2]=q;e=e+1|0}while((e|0)<(b[f>>2]|0))}if((b[d>>2]|0)<=0){q=0;return q|0}e=0;do{q=P[b[(b[57240+(b[p+1312+(e<<2)>>2]<<2)>>2]|0)+8>>2]&15](a,b[p+1568+(e<<2)>>2]|0)|0;b[(b[h>>2]|0)+(e<<2)>>2]=q;e=e+1|0}while((e|0)<(b[d>>2]|0));e=0;return e|0}function ia(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;if(!a)return;j=b[a+4>>2]|0;k=(j|0)!=0;if(k)h=b[j+28>>2]|0;else h=0;n=b[a+104>>2]|0;m=(n|0)!=0;if(m){c=b[n>>2]|0;if(c|0){na(c);fc(b[n>>2]|0)}c=n+12|0;e=b[c>>2]|0;if(e|0){cb(b[e>>2]|0);fc(b[b[c>>2]>>2]|0);fc(b[c>>2]|0)}c=n+16|0;e=b[c>>2]|0;if(e|0){cb(b[e>>2]|0);fc(b[b[c>>2]>>2]|0);fc(b[c>>2]|0)}e=n+48|0;c=b[e>>2]|0;if(c|0){if((h|0)!=0?(f=h+16|0,(b[f>>2]|0)>0):0){T[b[(b[57232+(b[h+800>>2]<<2)>>2]|0)+16>>2]&7](b[c>>2]|0);if((b[f>>2]|0)>1){c=1;do{T[b[(b[57232+(b[h+800+(c<<2)>>2]<<2)>>2]|0)+16>>2]&7](b[(b[e>>2]|0)+(c<<2)>>2]|0);c=c+1|0}while((c|0)<(b[f>>2]|0))}c=b[e>>2]|0}fc(c)}e=n+52|0;c=b[e>>2]|0;if(c|0){if((h|0)!=0?(g=h+20|0,(b[g>>2]|0)>0):0){T[b[(b[57240+(b[h+1312>>2]<<2)>>2]|0)+16>>2]&7](b[c>>2]|0);if((b[g>>2]|0)>1){c=1;do{T[b[(b[57240+(b[h+1312+(c<<2)>>2]<<2)>>2]|0)+16>>2]&7](b[(b[e>>2]|0)+(c<<2)>>2]|0);c=c+1|0}while((c|0)<(b[g>>2]|0))}c=b[e>>2]|0}fc(c)}e=n+56|0;c=b[e>>2]|0;if(c|0){if((h|0)!=0?(i=h+28|0,(b[i>>2]|0)>0):0){wa(c);if((b[i>>2]|0)>1){c=1;do{wa((b[e>>2]|0)+(c*52|0)|0);c=c+1|0}while((c|0)<(b[i>>2]|0))}c=b[e>>2]|0}fc(c)}c=b[n+60>>2]|0;if(c|0)ra(c);$a(n+80|0);lb(n+20|0);lb(n+32|0)}f=a+8|0;c=b[f>>2]|0;if(c|0){if(k?(l=j+4|0,d=b[l>>2]|0,(d|0)>0):0){e=0;while(1){c=b[c+(e<<2)>>2]|0;if(c){fc(c);d=b[l>>2]|0}c=e+1|0;if((c|0)>=(d|0))break;e=c;c=b[f>>2]|0}c=b[f>>2]|0}fc(c);c=b[a+12>>2]|0;if(c|0)fc(c)}if(m){c=b[n+64>>2]|0;if(c|0)fc(c);c=b[n+68>>2]|0;if(c|0)fc(c);c=b[n+72>>2]|0;if(c|0)fc(c);fc(n)}c=a+112|0;do{b[a>>2]=0;a=a+4|0}while((a|0)<(c|0));return}function ja(a,c){a=a|0;c=c|0;var d=0,e=0;if(ha(a,c,0)|0){ia(a);a=1;return a|0}c=b[a+4>>2]|0;d=b[a+104>>2]|0;if((c|0)==0|(d|0)==0){a=0;return a|0}c=b[c+28>>2]|0;if(!c){a=0;return a|0}e=b[c+3656>>2]|0;c=b[c+4>>2]>>e+1;b[a+48>>2]=c;b[a+20>>2]=c>>e;b[a+24>>2]=-1;c=a+56|0;b[c>>2]=-1;b[c+4>>2]=-1;b[c+8>>2]=-1;b[c+12>>2]=-1;b[a+32>>2]=0;a=d+128|0;b[a>>2]=-1;b[a+4>>2]=-1;a=0;return a|0}function ka(a,c){a=a|0;c=c|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;m=b[a+4>>2]|0;K=b[m+28>>2]|0;H=b[a+104>>2]|0;O=b[K+3656>>2]|0;if(!c){O=-131;return O|0}N=a+20|0;j=b[N>>2]|0;L=a+24|0;e=b[L>>2]|0;if(!((j|0)<=(e|0)|(e|0)==-1)){O=-131;return O|0}I=a+40|0;n=b[I>>2]|0;J=a+36|0;b[J>>2]=n;o=b[c+28>>2]|0;b[I>>2]=o;b[a+44>>2]=-1;k=a+64|0;g=k;f=b[g>>2]|0;g=b[g+4>>2]|0;if(!((f|0)==-1&(g|0)==-1)){h=xc(f|0,g|0,1,0)|0;f=C()|0;i=c+56|0;g=b[i>>2]|0;i=b[i+4>>2]|0;if((h|0)==(g|0)&(f|0)==(i|0))g=h;else{f=i;l=6}}else{f=c+56|0;g=b[f>>2]|0;f=b[f+4>>2]|0;l=6}if((l|0)==6){G=a+56|0;b[G>>2]=-1;b[G+4>>2]=-1;G=H+128|0;b[G>>2]=-1;b[G+4>>2]=-1}G=k;b[G>>2]=g;b[G+4>>2]=f;if(!(b[c>>2]|0)){l=j;k=e}else{E=O+1|0;A=b[K+(o<<2)>>2]>>E;B=b[K>>2]>>E;E=b[K+4>>2]>>E;G=b[c+88>>2]|0;D=a+72|0;f=D;G=xc(b[f>>2]|0,b[f+4>>2]|0,G|0,((G|0)<0)<<31>>31|0)|0;f=C()|0;b[D>>2]=G;b[D+4>>2]=f;D=b[c+92>>2]|0;f=a+80|0;G=f;D=xc(b[G>>2]|0,b[G+4>>2]|0,D|0,((D|0)<0)<<31>>31|0)|0;G=C()|0;b[f>>2]=D;b[f+4>>2]=G;f=b[c+96>>2]|0;G=a+88|0;D=G;f=xc(b[D>>2]|0,b[D+4>>2]|0,f|0,((f|0)<0)<<31>>31|0)|0;D=C()|0;b[G>>2]=f;b[G+4>>2]=D;G=b[c+100>>2]|0;D=a+96|0;f=D;G=xc(b[f>>2]|0,b[f+4>>2]|0,G|0,((G|0)<0)<<31>>31|0)|0;f=C()|0;b[D>>2]=G;b[D+4>>2]=f;D=a+48|0;f=b[D>>2]|0;G=(f|0)==0;F=G?E:0;G=G?0:E;z=m+4|0;if((b[z>>2]|0)>0){l=H+4|0;m=a+8|0;p=(A|0)>0;q=(E|0)/2|0;w=(B|0)/2|0;r=0-w|0;s=(B|0)>0;t=H+8|0;u=(E|0)>0;v=(B|0)/-2|0;w=q+w|0;x=B+-1|0;y=E+-1|0;e=0;g=n;f=o;while(1){h=(f|0)!=0;a:do if(!g){j=oa((b[l>>2]|0)-O|0)|0;i=b[(b[m>>2]|0)+(e<<2)>>2]|0;k=i+(F<<2)|0;f=b[(b[c>>2]|0)+(e<<2)>>2]|0;if(!h){if(!s)break;g=0;while(1){o=k+(g<<2)|0;d[o>>2]=+d[j+(g<<2)>>2]*+d[f+(g<<2)>>2]+ +d[j+(x-g<<2)>>2]*+d[o>>2];g=g+1|0;if((g|0)==(B|0))break a}}h=f+(q<<2)+(r<<2)|0;if(s){g=0;do{o=k+(g<<2)|0;d[o>>2]=+d[j+(g<<2)>>2]*+d[h+(g<<2)>>2]+ +d[j+(x-g<<2)>>2]*+d[o>>2];g=g+1|0}while((g|0)!=(B|0));g=B}else g=0;if((g|0)<(w|0))do{b[k+(g<<2)>>2]=b[h+(g<<2)>>2];g=g+1|0}while((g|0)<(w|0))}else if(h){h=oa((b[t>>2]|0)-O|0)|0;i=b[(b[m>>2]|0)+(e<<2)>>2]|0;j=i+(F<<2)|0;f=b[(b[c>>2]|0)+(e<<2)>>2]|0;if(!u)break;g=0;do{o=j+(g<<2)|0;d[o>>2]=+d[h+(g<<2)>>2]*+d[f+(g<<2)>>2]+ +d[h+(y-g<<2)>>2]*+d[o>>2];g=g+1|0}while((g|0)!=(E|0))}else{h=oa((b[l>>2]|0)-O|0)|0;i=b[(b[m>>2]|0)+(e<<2)>>2]|0;j=i+(F<<2)+(q<<2)+(v<<2)|0;f=b[(b[c>>2]|0)+(e<<2)>>2]|0;if(!s)break;g=0;do{o=j+(g<<2)|0;d[o>>2]=+d[h+(g<<2)>>2]*+d[f+(g<<2)>>2]+ +d[h+(x-g<<2)>>2]*+d[o>>2];g=g+1|0}while((g|0)!=(B|0))}while(0);h=i+(G<<2)|0;g=f+(A<<2)|0;if(p){f=0;do{b[h+(f<<2)>>2]=b[g+(f<<2)>>2];f=f+1|0}while((f|0)!=(A|0))}e=e+1|0;if((e|0)>=(b[z>>2]|0))break;g=b[J>>2]|0;f=b[I>>2]|0}f=b[D>>2]|0;e=b[L>>2]|0}b[D>>2]=(f|0)==0?E:0;if((e|0)==-1){b[L>>2]=G;f=G;e=G}else{b[L>>2]=F;f=F;e=(((b[K+(b[I>>2]<<2)>>2]|0)/4|0)+((b[K+(b[J>>2]<<2)>>2]|0)/4|0)>>O)+F|0}b[N>>2]=e;l=e;k=f}g=H+128|0;f=g;e=b[f>>2]|0;f=b[f+4>>2]|0;if((e|0)==-1&(f|0)==-1){h=0;i=0}else{h=((b[K+(b[I>>2]<<2)>>2]|0)/4|0)+((b[K+(b[J>>2]<<2)>>2]|0)/4|0)|0;h=xc(e|0,f|0,h|0,((h|0)<0)<<31>>31|0)|0;i=C()|0}j=g;b[j>>2]=h;b[j+4>>2]=i;j=a+56|0;f=j;e=b[f>>2]|0;f=b[f+4>>2]|0;do if((e|0)==-1&(f|0)==-1){f=c+48|0;e=b[f>>2]|0;f=b[f+4>>2]|0;if(!((e|0)==-1&(f|0)==-1)?(M=j,b[M>>2]=e,b[M+4>>2]=f,(i|0)>(f|0)|(i|0)==(f|0)&h>>>0>e>>>0):0){e=yc(h|0,i|0,e|0,f|0)|0;C()|0;e=(e|0)>0?e:0;if(!(b[c+44>>2]|0)){O=k+(e>>>O)|0;b[L>>2]=(O|0)>(l|0)?l:O;break}else{M=l-k<<O;b[N>>2]=l-(((e|0)>(M|0)?M:e)>>O);break}}}else{h=((b[K+(b[I>>2]<<2)>>2]|0)/4|0)+((b[K+(b[J>>2]<<2)>>2]|0)/4|0)|0;e=xc(e|0,f|0,h|0,((h|0)<0)<<31>>31|0)|0;f=C()|0;h=j;b[h>>2]=e;b[h+4>>2]=f;h=c+48|0;g=b[h>>2]|0;h=b[h+4>>2]|0;if(!((g|0)==-1&(h|0)==-1|(e|0)==(g|0)&(f|0)==(h|0))){if(((f|0)>(h|0)|(f|0)==(h|0)&e>>>0>g>>>0?(M=yc(e|0,f|0,g|0,h|0)|0,C()|0,M|0):0)?b[c+44>>2]|0:0){L=l-k<<O;M=(L|0)<(M|0)?L:M;b[N>>2]=l-(((M|0)>0?M:0)>>>O)}O=j;b[O>>2]=g;b[O+4>>2]=h}}while(0);if(!(b[c+44>>2]|0)){O=0;return O|0}b[a+32>>2]=1;O=0;return O|0}function la(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;g=b[a+24>>2]|0;if((g|0)<=-1){h=0;return h|0}h=b[a+20>>2]|0;if((h|0)<=(g|0)){h=0;return h|0}if(c|0){e=b[(b[a+4>>2]|0)+4>>2]|0;if((e|0)>0){f=a+8|0;a=a+12|0;d=0;do{b[(b[a>>2]|0)+(d<<2)>>2]=(b[(b[f>>2]|0)+(d<<2)>>2]|0)+(g<<2);d=d+1|0}while((d|0)<(e|0))}else a=a+12|0;b[c>>2]=b[a>>2]}h=h-g|0;return h|0}function ma(a,c){a=a|0;c=c|0;var d=0,e=0;d=a+24|0;e=(b[d>>2]|0)+c|0;if(c|0?(e|0)>(b[a+20>>2]|0):0){c=-131;return c|0}b[d>>2]=e;c=0;return c|0}function na(a){a=a|0;cb(a+16|0);fc(b[a+48>>2]|0);fc(b[a+64>>2]|0);fc(b[a+80>>2]|0);fc(b[a+96>>2]|0);fc(b[a+112>>2]|0);fc(b[a+128>>2]|0);fc(b[a+144>>2]|0);fc(b[a+36>>2]|0);fc(b[a+152>>2]|0);fc(b[a+160>>2]|0);Fc(a|0,0,180)|0;return}function oa(a){a=a|0;return b[16+(a<<2)>>2]|0}function pa(a,c,e,f,g,h){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0;k=(g|0)!=0;j=k?f:0;k=k?h:0;l=b[16+(b[c+(j<<2)>>2]<<2)>>2]|0;n=b[16+(b[c+(k<<2)>>2]<<2)>>2]|0;o=b[e+(g<<2)>>2]|0;j=b[e+(j<<2)>>2]|0;k=b[e+(k<<2)>>2]|0;g=(o|0)/4|0;e=(j|0)/4|0;i=g-e|0;j=(j|0)/2|0;f=((o|0)/2|0)+g+((k|0)/-4|0)|0;h=(k|0)/2|0;m=f+h|0;if((i|0)>0){Fc(a|0,0,g-e<<2|0)|0;c=i}else c=0;if((c|0)<(i+j|0)){e=g+j-c-e|0;g=0;while(1){j=a+(c<<2)|0;d[j>>2]=+d[j>>2]*+d[l+(g<<2)>>2];g=g+1|0;if((g|0)==(e|0))break;else c=c+1|0}}if((k|0)>1)do{h=h+-1|0;l=a+(f<<2)|0;d[l>>2]=+d[l>>2]*+d[n+(h<<2)>>2];f=f+1|0}while((f|0)<(m|0));if((o|0)<=(f|0))return;Fc(a+(f<<2)|0,0,o-f<<2|0)|0;return}function qa(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;h=(a|0)!=0;if(h?(d=b[a+64>>2]|0,(d|0)!=0):0){e=b[d+104>>2]|0;d=b[d+4>>2]|0;if(!d){f=1;g=0;k=0;j=0}else{f=1;g=1;k=b[d+28>>2]|0;j=d}}else{e=0;f=0;g=0;k=0;j=0}i=h?a+4|0:0;if(!(h&((k|0)!=0&(g&(f&(e|0)!=0))))){a=-136;return a|0}fa(a);$b(i,b[c>>2]|0,b[c+4>>2]|0);if(cc(i,1)|0){a=-135;return a|0}d=cc(i,b[e+44>>2]|0)|0;if((d|0)==-1){a=-136;return a|0}b[a+40>>2]=d;g=k+32+(d<<2)|0;d=b[g>>2]|0;if(!d){a=-136;return a|0}h=b[d>>2]|0;d=a+28|0;b[d>>2]=h;do if(h){b[a+24>>2]=cc(i,1)|0;i=cc(i,1)|0;b[a+32>>2]=i;if((i|0)==-1){a=-136;return a|0}else{d=b[d>>2]|0;break}}else{b[a+24>>2]=0;b[a+32>>2]=0;d=0}while(0);f=c+16|0;e=b[f+4>>2]|0;i=a+48|0;b[i>>2]=b[f>>2];b[i+4>>2]=e;i=c+24|0;e=b[i+4>>2]|0;f=a+56|0;b[f>>2]=b[i>>2];b[f+4>>2]=e;b[a+44>>2]=b[c+12>>2];f=a+36|0;b[f>>2]=b[k+(d<<2)>>2];e=j+4|0;b[a>>2]=ea(a,b[e>>2]<<2)|0;if((b[e>>2]|0)>0){d=0;do{c=ea(a,b[f>>2]<<2)|0;b[(b[a>>2]|0)+(d<<2)>>2]=c;d=d+1|0}while((d|0)<(b[e>>2]|0))}c=b[(b[g>>2]|0)+12>>2]|0;a=P[b[(b[57252+(b[k+288+(c<<2)>>2]<<2)>>2]|0)+16>>2]&15](a,b[k+544+(c<<2)>>2]|0)|0;return a|0}function ra(a){a=a|0;if(a|0)fc(a);return}function sa(a){a=a|0;if(a|0)fc(a);return}function ta(a,c,e,f,g){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0.0,j=0,k=0,l=0.0,m=0.0,n=0.0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,y=0.0,A=0,B=0,C=0,D=0.0,E=0,F=0.0;C=a+8|0;b[C>>2]=0;b[C+4>>2]=0;C=b[e>>2]|0;b[a+36>>2]=C;k=~~(+Dc(+(+x(+(+(C|0)*8.0))*1.4426950408889634))+-1.0);b[a+32>>2]=k;F=+(g|0);D=+(f|0);y=+(1<<k+1|0);C=~~((+x(+(F*.25*.5/D))*1.4426950216293335+-5.965784072875977)*y-+(C|0));b[a+28>>2]=C;b[a+40>>2]=1-C+~~((+x(+((+(f|0)+.25)*F*.5/D))*1.4426950216293335+-5.965784072875977)*y+.5);C=f<<2;k=ec(C)|0;b[a+16>>2]=k;A=ec(C)|0;b[a+20>>2]=A;u=ec(C)|0;b[a+24>>2]=u;E=a+4|0;b[E>>2]=c;b[a>>2]=f;b[a+44>>2]=g;e=a+48|0;d[e>>2]=1.0;if((g|0)>=26e3)if((g|0)>=38e3){if((g|0)>46e3){i=1.274999976158142;B=4}}else{i=.9399999976158142;B=4}else{i=0.0;B=4}if((B|0)==4)d[e>>2]=i;l=D*2.0;F=+(g|0);m=1.0/F;e=0;j=0;do{t=j;j=j+1|0;h=~~+Dc(+(l*+w(+(+(j|0)*.08664337545633316+2.7488713472395148))*m));i=+d[32688+(t<<2)>>2];if((e|0)<(h|0)?(n=(+d[32688+(j<<2)>>2]-i)/+(h-e|0),(e|0)<(f|0)):0)while(1){d[k+(e<<2)>>2]=i+100.0;e=e+1|0;if((e|0)<(h|0)&(e|0)<(f|0))i=i+n;else break}}while((j|0)!=87);if((e|0)<(f|0)){h=b[k+((e<<2)+-4)>>2]|0;do{b[k+(e<<2)>>2]=h;e=e+1|0}while((e|0)!=(f|0))}t=(f|0)>0;if(t){o=(g|0)/(f<<1|0)|0;p=b[c+120>>2]|0;q=c+112|0;r=c+124|0;s=c+116|0;h=-99;e=1;g=0;do{k=z(o,g)|0;l=+(k|0);l=+v(+(+(z(k,k)|0)*1.8499999754340024e-08))*2.240000009536743+ +v(+(l*7.399999885819852e-04))*13.100000381469727+l*9.999999747378752e-05;a:do if((p+h|0)<(g|0)){i=l-+d[q>>2];do{k=z(h,o)|0;n=+(k|0);if(!(+v(+(n*7.399999885819852e-04))*13.100000381469727+n*9.999999747378752e-05+ +v(+(+(z(k,k)|0)*1.8499999754340024e-08))*2.240000009536743<i))break a;h=h+1|0}while((p+h|0)<(g|0))}while(0);b:do if((e|0)<=(f|0)){k=(b[r>>2]|0)+g|0;while(1){if((e|0)>=(k|0)?(j=z(e,o)|0,n=+(j|0),n=+v(+(n*7.399999885819852e-04))*13.100000381469727+n*9.999999747378752e-05+ +v(+(+(z(j,j)|0)*1.8499999754340024e-08))*2.240000009536743,!(n<+d[s>>2]+l)):0)break b;j=e+1|0;if((e|0)<(f|0))e=j;else{e=j;break}}}while(0);b[u+(g<<2)>>2]=(h<<16)+-65537+e;g=g+1|0}while((g|0)!=(f|0));if(t){i=F*.5;l=1.0/D;e=0;do{b[A+(e<<2)>>2]=~~((+x(+(i*(+(e|0)+.25)*l))*1.4426950216293335+-5.965784072875977)*y+.5);e=e+1|0}while((e|0)!=(f|0))}else B=27}else B=27;if((B|0)==27)i=F*.5;b[a+8>>2]=ua(c+36|0,i/D,f,+d[c+24>>2],+d[c+28>>2])|0;B=ec(12)|0;b[a+12>>2]=B;k=ec(C)|0;b[B>>2]=k;g=ec(C)|0;b[B+4>>2]=g;j=ec(C)|0;b[B+8>>2]=j;if(!t)return;i=.5/D;h=b[E>>2]|0;e=0;do{D=+x(+((+(e|0)+.5)*F*i))*2.885390043258667+-11.931568145751953;D=D<0.0?0.0:D;D=D>=16.0?16.0:D;C=~~D;D=D-+(C|0);y=1.0-D;E=C+1|0;d[k+(e<<2)>>2]=y*+d[h+132+(C<<2)>>2]+ +d[h+132+(E<<2)>>2]*D;d[g+(e<<2)>>2]=y*+d[h+200+(C<<2)>>2]+ +d[h+200+(E<<2)>>2]*D;d[j+(e<<2)>>2]=y*+d[h+268+(C<<2)>>2]+ +d[h+268+(E<<2)>>2]*D;e=e+1|0}while((e|0)!=(f|0));return}function ua(a,c,e,f,g){a=a|0;c=+c;e=e|0;f=+f;g=+g;var h=0.0,i=0,j=0,k=0.0,l=0,m=0,n=0,o=0,q=0,r=0,s=0,t=0,u=0,v=0.0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0;O=L;L=L+32480|0;I=O+32256|0;M=O+1792|0;J=O;N=L;L=L+((1*(e<<2)|0)+15&-16)|0;K=ec(68)|0;Fc(M|0,0,30464)|0;z=f>0.0;A=f<0.0;B=J+224|0;C=J+448|0;D=J+672|0;E=J+896|0;F=J+1120|0;G=J+1344|0;H=J+1568|0;u=0;do{m=u<<2;l=0;do{j=l+m|0;if(j>>>0<88)h=+d[32688+(j<<2)>>2];else h=-30.0;i=j+1|0;if(i>>>0<88){k=+d[32688+(i<<2)>>2];if(h>k)h=k}else if(h>-30.0)h=-30.0;i=j+2|0;if(i>>>0<88){k=+d[32688+(i<<2)>>2];if(h>k)h=k}else if(h>-30.0)h=-30.0;i=j+3|0;if(i>>>0<88){k=+d[32688+(i<<2)>>2];if(h>k)h=k}else if(h>-30.0)h=-30.0;d[I+(l<<2)>>2]=h;l=l+1|0}while((l|0)!=56);n=M+(u*1792|0)+448|0;l=33040+(u*1344|0)|0;Ec(n|0,l|0,224)|0;o=M+(u*1792|0)+672|0;Ec(o|0,33040+(u*1344|0)+224|0,224)|0;q=M+(u*1792|0)+896|0;Ec(q|0,33040+(u*1344|0)+448|0,224)|0;r=M+(u*1792|0)+1120|0;Ec(r|0,33040+(u*1344|0)+672|0,224)|0;s=M+(u*1792|0)+1344|0;Ec(s|0,33040+(u*1344|0)+896|0,224)|0;t=M+(u*1792|0)+1568|0;Ec(t|0,33040+(u*1344|0)+1120|0,224)|0;Ec(M+(u*1792|0)|0,l|0,224)|0;m=M+(u*1792|0)+224|0;Ec(m|0,l|0,224)|0;if(!z){if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+224+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+224+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+448+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+448+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+672+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+672+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+896+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+896+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+1120+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+1120+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+1344+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+1344+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+1568+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{j=16-i|0;l=M+(u*1792|0)+1568+(i<<2)|0;d[l>>2]=+d[l>>2]+(+(((j|0)>-1?j:0-j|0)|0)*g+f);i=i+1|0}while((i|0)!=56)}}else{j=0;do{if(A){i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;v=v<0.0?0.0:v;l=M+(u*1792|0)+(j*224|0)+(i<<2)|0;d[l>>2]=+d[l>>2]+(v>0.0?0.0:v);i=i+1|0}while((i|0)!=56)}else{i=0;do{l=16-i|0;v=+(((l|0)>-1?l:0-l|0)|0)*g+f;l=M+(u*1792|0)+(j*224|0)+(i<<2)|0;d[l>>2]=+d[l>>2]+(v<0.0?0.0:v);i=i+1|0}while((i|0)!=56)}j=j+1|0}while((j|0)!=8)}l=a+(u<<2)|0;va(M+(u*1792|0)|0,+d[l>>2]+50.0);Ec(J|0,I|0,224)|0;va(J,70.0);i=0;do{h=+d[M+(u*1792|0)+(i<<2)>>2];j=J+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(m,+d[l>>2]+50.0);Ec(B|0,I|0,224)|0;va(B,60.0);i=0;do{h=+d[M+(u*1792|0)+224+(i<<2)>>2];j=J+224+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(n,+d[l>>2]+50.0);Ec(C|0,I|0,224)|0;va(C,50.0);i=0;do{h=+d[M+(u*1792|0)+448+(i<<2)>>2];j=J+448+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(o,+d[l>>2]+40.0);Ec(D|0,I|0,224)|0;va(D,40.0);i=0;do{h=+d[M+(u*1792|0)+672+(i<<2)>>2];j=J+672+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(q,+d[l>>2]+30.0);Ec(E|0,I|0,224)|0;va(E,30.0);i=0;do{h=+d[M+(u*1792|0)+896+(i<<2)>>2];j=J+896+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(r,+d[l>>2]+20.0);Ec(F|0,I|0,224)|0;va(F,20.0);i=0;do{h=+d[M+(u*1792|0)+1120+(i<<2)>>2];j=J+1120+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(s,+d[l>>2]+10.0);Ec(G|0,I|0,224)|0;va(G,10.0);i=0;do{h=+d[M+(u*1792|0)+1344+(i<<2)>>2];j=J+1344+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);va(t,+d[l>>2]);Ec(H|0,I|0,224)|0;va(H,0.0);i=0;do{h=+d[M+(u*1792|0)+1568+(i<<2)>>2];j=J+1568+(i<<2)|0;if(h>+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+(i<<2)>>2];j=J+224+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+224+(i<<2)>>2];j=M+(u*1792|0)+224+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+224+(i<<2)>>2];j=J+448+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+448+(i<<2)>>2];j=M+(u*1792|0)+448+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+448+(i<<2)>>2];j=J+672+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+672+(i<<2)>>2];j=M+(u*1792|0)+672+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+672+(i<<2)>>2];j=J+896+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+896+(i<<2)>>2];j=M+(u*1792|0)+896+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+896+(i<<2)>>2];j=J+1120+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+1120+(i<<2)>>2];j=M+(u*1792|0)+1120+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+1120+(i<<2)>>2];j=J+1344+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+1344+(i<<2)>>2];j=M+(u*1792|0)+1344+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+1344+(i<<2)>>2];j=J+1568+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);i=0;do{h=+d[J+1568+(i<<2)>>2];j=M+(u*1792|0)+1568+(i<<2)|0;if(h<+d[j>>2])d[j>>2]=h;i=i+1|0}while((i|0)!=56);u=u+1|0}while((u|0)!=17);f=1.0/c;s=(e|0)>0;r=0;do{t=ec(32)|0;b[K+(r<<2)>>2]=t;v=+(r|0);g=v*.5;A=~~+p(+(+w(+(v*.34657350182533264+4.135165354540845))*f));q=~~+y(+(+x(+(+(A|0)*c+1.0))*2.885390043258667+-11.931568145751953));A=~~+p(+(+x(+(+(A+1|0)*c))*2.885390043258667+-11.931568145751953));q=(r|0)<(q|0)?r:q;q=(q|0)>0?q:0;A=(A|0)<16?A:16;u=(q|0)>(A|0);r=r+1|0;a=r>>>0<17;v=g+3.9657840728759766;o=0;do{z=ec(232)|0;b[t+(o<<2)>>2]=z;if(s){i=0;do{d[N+(i<<2)>>2]=999.0;i=i+1|0}while((i|0)!=(e|0))}if(!u){n=q;while(1){k=+(n|0)*.5;i=0;m=0;do{h=+(m|0)*.125+k;J=~~(+w(+((h+3.9032840728759766)*.6931470036506653))*f);l=~~(+w(+((h+4.028284072875977)*.6931470036506653))*f+1.0);J=(J|0)>0?J:0;J=(J|0)>(e|0)?e:J;i=(J|0)<(i|0)?J:i;l=(l|0)>0?l:0;l=(l|0)>(e|0)?e:l;if((i|0)<(l|0)&(i|0)<(e|0)){h=+d[M+(n*1792|0)+(o*224|0)+(m<<2)>>2];do{j=N+(i<<2)|0;if(+d[j>>2]>h)d[j>>2]=h;i=i+1|0}while((i|0)<(l|0)&(i|0)<(e|0))}m=m+1|0}while((m|0)!=56);if((i|0)<(e|0)){h=+d[M+(n*1792|0)+(o*224|0)+220>>2];do{j=N+(i<<2)|0;if(+d[j>>2]>h)d[j>>2]=h;i=i+1|0}while((i|0)!=(e|0))}if((n|0)<(A|0))n=n+1|0;else break}}if(a){i=0;m=0;do{k=+(m|0)*.125+g;J=~~(+w(+((k+3.9032840728759766)*.6931470036506653))*f);l=~~(+w(+((k+4.028284072875977)*.6931470036506653))*f+1.0);J=(J|0)>0?J:0;J=(J|0)>(e|0)?e:J;i=(J|0)<(i|0)?J:i;l=(l|0)>0?l:0;l=(l|0)>(e|0)?e:l;if((i|0)<(l|0)&(i|0)<(e|0)){h=+d[M+(r*1792|0)+(o*224|0)+(m<<2)>>2];do{j=N+(i<<2)|0;if(+d[j>>2]>h)d[j>>2]=h;i=i+1|0}while((i|0)<(l|0)&(i|0)<(e|0))}m=m+1|0}while((m|0)!=56);if((i|0)<(e|0)){h=+d[M+(r*1792|0)+(o*224|0)+220>>2];do{j=N+(i<<2)|0;if(+d[j>>2]>h)d[j>>2]=h;i=i+1|0}while((i|0)!=(e|0))}}i=0;do{j=~~(+w(+((v+ +(i|0)*.125)*.6931470036506653))*f);do if((j|0)>=0)if((j|0)<(e|0)){b[z+(i+2<<2)>>2]=b[N+(j<<2)>>2];break}else{d[z+(i+2<<2)>>2]=-999.0;break}else d[z+(i+2<<2)>>2]=-999.0;while(0);i=i+1|0}while((i|0)!=56);do if(!(+d[z+8>>2]>-200.0))if(!(+d[z+12>>2]>-200.0))if(!(+d[z+16>>2]>-200.0))if(!(+d[z+20>>2]>-200.0))if(!(+d[z+24>>2]>-200.0))if(!(+d[z+28>>2]>-200.0))if(!(+d[z+32>>2]>-200.0))if(!(+d[z+36>>2]>-200.0))if(!(+d[z+40>>2]>-200.0))if(!(+d[z+44>>2]>-200.0))if(!(+d[z+48>>2]>-200.0))if(!(+d[z+52>>2]>-200.0))if(!(+d[z+56>>2]>-200.0))if(+d[z+60>>2]>-200.0)h=13.0;else{if(+d[z+64>>2]>-200.0){h=14.0;break}if(+d[z+68>>2]>-200.0){h=15.0;break}h=16.0}else h=12.0;else h=11.0;else h=10.0;else h=9.0;else h=8.0;else h=7.0;else h=6.0;else h=5.0;else h=4.0;else h=3.0;else h=2.0;else h=1.0;else h=0.0;while(0);d[z>>2]=h;do if(!(+d[z+228>>2]>-200.0))if(!(+d[z+224>>2]>-200.0))if(!(+d[z+220>>2]>-200.0))if(!(+d[z+216>>2]>-200.0))if(!(+d[z+212>>2]>-200.0))if(!(+d[z+208>>2]>-200.0))if(!(+d[z+204>>2]>-200.0))if(!(+d[z+200>>2]>-200.0))if(!(+d[z+196>>2]>-200.0))if(!(+d[z+192>>2]>-200.0))if(!(+d[z+188>>2]>-200.0))if(!(+d[z+184>>2]>-200.0))if(+d[z+180>>2]>-200.0)h=43.0;else{if(+d[z+176>>2]>-200.0){h=42.0;break}if(+d[z+172>>2]>-200.0){h=41.0;break}if(+d[z+168>>2]>-200.0){h=40.0;break}if(+d[z+164>>2]>-200.0){h=39.0;break}if(+d[z+160>>2]>-200.0){h=38.0;break}if(+d[z+156>>2]>-200.0){h=37.0;break}if(+d[z+152>>2]>-200.0){h=36.0;break}if(+d[z+148>>2]>-200.0){h=35.0;break}if(+d[z+144>>2]>-200.0){h=34.0;break}if(+d[z+140>>2]>-200.0){h=33.0;break}if(+d[z+136>>2]>-200.0){h=32.0;break}if(+d[z+132>>2]>-200.0){h=31.0;break}if(+d[z+128>>2]>-200.0){h=30.0;break}if(+d[z+124>>2]>-200.0){h=29.0;break}if(+d[z+120>>2]>-200.0){h=28.0;break}if(+d[z+116>>2]>-200.0){h=27.0;break}if(+d[z+112>>2]>-200.0){h=26.0;break}if(+d[z+108>>2]>-200.0){h=25.0;break}if(+d[z+104>>2]>-200.0){h=24.0;break}if(+d[z+100>>2]>-200.0){h=23.0;break}if(+d[z+96>>2]>-200.0){h=22.0;break}if(+d[z+92>>2]>-200.0){h=21.0;break}if(+d[z+88>>2]>-200.0){h=20.0;break}if(+d[z+84>>2]>-200.0){h=19.0;break}if(+d[z+80>>2]>-200.0){h=18.0;break}h=17.0}else h=44.0;else h=45.0;else h=46.0;else h=47.0;else h=48.0;else h=49.0;else h=50.0;else h=51.0;else h=52.0;else h=53.0;else h=54.0;else h=55.0;while(0);d[z+4>>2]=h;o=o+1|0}while((o|0)!=8)}while((r|0)!=17);L=O;return K|0}function va(a,b){a=a|0;b=+b;var c=0;d[a>>2]=+d[a>>2]+b;c=a+4|0;d[c>>2]=+d[c>>2]+b;c=a+8|0;d[c>>2]=+d[c>>2]+b;c=a+12|0;d[c>>2]=+d[c>>2]+b;c=a+16|0;d[c>>2]=+d[c>>2]+b;c=a+20|0;d[c>>2]=+d[c>>2]+b;c=a+24|0;d[c>>2]=+d[c>>2]+b;c=a+28|0;d[c>>2]=+d[c>>2]+b;c=a+32|0;d[c>>2]=+d[c>>2]+b;c=a+36|0;d[c>>2]=+d[c>>2]+b;c=a+40|0;d[c>>2]=+d[c>>2]+b;c=a+44|0;d[c>>2]=+d[c>>2]+b;c=a+48|0;d[c>>2]=+d[c>>2]+b;c=a+52|0;d[c>>2]=+d[c>>2]+b;c=a+56|0;d[c>>2]=+d[c>>2]+b;c=a+60|0;d[c>>2]=+d[c>>2]+b;c=a+64|0;d[c>>2]=+d[c>>2]+b;c=a+68|0;d[c>>2]=+d[c>>2]+b;c=a+72|0;d[c>>2]=+d[c>>2]+b;c=a+76|0;d[c>>2]=+d[c>>2]+b;c=a+80|0;d[c>>2]=+d[c>>2]+b;c=a+84|0;d[c>>2]=+d[c>>2]+b;c=a+88|0;d[c>>2]=+d[c>>2]+b;c=a+92|0;d[c>>2]=+d[c>>2]+b;c=a+96|0;d[c>>2]=+d[c>>2]+b;c=a+100|0;d[c>>2]=+d[c>>2]+b;c=a+104|0;d[c>>2]=+d[c>>2]+b;c=a+108|0;d[c>>2]=+d[c>>2]+b;c=a+112|0;d[c>>2]=+d[c>>2]+b;c=a+116|0;d[c>>2]=+d[c>>2]+b;c=a+120|0;d[c>>2]=+d[c>>2]+b;c=a+124|0;d[c>>2]=+d[c>>2]+b;c=a+128|0;d[c>>2]=+d[c>>2]+b;c=a+132|0;d[c>>2]=+d[c>>2]+b;c=a+136|0;d[c>>2]=+d[c>>2]+b;c=a+140|0;d[c>>2]=+d[c>>2]+b;c=a+144|0;d[c>>2]=+d[c>>2]+b;c=a+148|0;d[c>>2]=+d[c>>2]+b;c=a+152|0;d[c>>2]=+d[c>>2]+b;c=a+156|0;d[c>>2]=+d[c>>2]+b;c=a+160|0;d[c>>2]=+d[c>>2]+b;c=a+164|0;d[c>>2]=+d[c>>2]+b;c=a+168|0;d[c>>2]=+d[c>>2]+b;c=a+172|0;d[c>>2]=+d[c>>2]+b;c=a+176|0;d[c>>2]=+d[c>>2]+b;c=a+180|0;d[c>>2]=+d[c>>2]+b;c=a+184|0;d[c>>2]=+d[c>>2]+b;c=a+188|0;d[c>>2]=+d[c>>2]+b;c=a+192|0;d[c>>2]=+d[c>>2]+b;c=a+196|0;d[c>>2]=+d[c>>2]+b;c=a+200|0;d[c>>2]=+d[c>>2]+b;c=a+204|0;d[c>>2]=+d[c>>2]+b;c=a+208|0;d[c>>2]=+d[c>>2]+b;c=a+212|0;d[c>>2]=+d[c>>2]+b;c=a+216|0;d[c>>2]=+d[c>>2]+b;a=a+220|0;d[a>>2]=+d[a>>2]+b;return}function wa(a){a=a|0;var c=0,d=0,e=0;if(!a)return;c=b[a+16>>2]|0;if(c|0)fc(c);c=b[a+20>>2]|0;if(c|0)fc(c);c=b[a+24>>2]|0;if(c|0)fc(c);e=a+8|0;d=b[e>>2]|0;if(d|0){c=0;while(1){fc(b[b[d+(c<<2)>>2]>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+4>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+8>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+12>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+16>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+20>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+24>>2]|0);fc(b[(b[(b[e>>2]|0)+(c<<2)>>2]|0)+28>>2]|0);fc(b[(b[e>>2]|0)+(c<<2)>>2]|0);c=c+1|0;if((c|0)==17)break;d=b[e>>2]|0}fc(b[e>>2]|0)}c=a+12|0;d=b[c>>2]|0;if(d|0){fc(b[d>>2]|0);fc(b[(b[c>>2]|0)+4>>2]|0);fc(b[(b[c>>2]|0)+8>>2]|0);fc(b[c>>2]|0)}c=a;d=c+52|0;do{b[c>>2]=0;c=c+4|0}while((c|0)<(d|0));return}function xa(a,c,e){a=a|0;c=c|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0;k=L;i=b[a>>2]|0;j=L;L=L+((1*(i<<2)|0)+15&-16)|0;g=a+24|0;ya(i,b[g>>2]|0,c,e,140.0,-1);h=(i|0)>0;if(h){f=0;do{d[j+(f<<2)>>2]=+d[c+(f<<2)>>2]-+d[e+(f<<2)>>2];f=f+1|0}while((f|0)!=(i|0))}a=a+4|0;ya(i,b[g>>2]|0,j,e,0.0,b[(b[a>>2]|0)+128>>2]|0);if(!h){L=k;return}f=0;do{g=j+(f<<2)|0;d[g>>2]=+d[c+(f<<2)>>2]-+d[g>>2];f=f+1|0}while((f|0)!=(i|0));if(!h){L=k;return}a=b[a>>2]|0;f=0;do{h=e+(f<<2)|0;c=~~(+d[h>>2]+.5);c=(c|0)<39?c:39;d[h>>2]=+d[a+336+(((c|0)>0?c:0)<<2)>>2]+ +d[j+(f<<2)>>2];f=f+1|0}while((f|0)!=(i|0));L=k;return}function ya(a,c,e,f,g,h){a=a|0;c=c|0;e=e|0;f=f|0;g=+g;h=h|0;var i=0,j=0.0,k=0.0,l=0.0,m=0.0,n=0,o=0.0,p=0.0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0.0,y=0.0,z=0.0;w=L;q=a<<2;t=L;L=L+((1*q|0)+15&-16)|0;u=L;L=L+((1*q|0)+15&-16)|0;v=L;L=L+((1*q|0)+15&-16)|0;r=L;L=L+((1*q|0)+15&-16)|0;s=L;L=L+((1*q|0)+15&-16)|0;j=+d[e>>2]+g;j=j<1.0?1.0:j;k=j*j*.5;j=k*j;d[t>>2]=k;d[u>>2]=k;d[v>>2]=0.0;d[r>>2]=j;d[s>>2]=0.0;if((a|0)>1){o=1.0;i=1;p=0.0;l=0.0;m=k;while(1){x=+d[e+(i<<2)>>2]+g;x=x<1.0?1.0:x;z=x*x;k=z+k;y=z*o;m=y+m;l=y*o+l;j=z*x+j;p=y*x+p;d[t+(i<<2)>>2]=k;d[u+(i<<2)>>2]=m;d[v+(i<<2)>>2]=l;d[r+(i<<2)>>2]=j;d[s+(i<<2)>>2]=p;i=i+1|0;if((i|0)==(a|0))break;else o=o+1.0}}i=b[c>>2]|0;if((i|0)>-1){k=1.0;o=0.0;m=0.0;j=0.0;e=0}else{j=0.0;e=0;do{q=i&65535;i=0-(i>>16)|0;z=+d[t+(i<<2)>>2]+ +d[t+(q<<2)>>2];k=+d[u+(q<<2)>>2]-+d[u+(i<<2)>>2];y=+d[v+(i<<2)>>2]+ +d[v+(q<<2)>>2];o=+d[r+(i<<2)>>2]+ +d[r+(q<<2)>>2];x=+d[s+(q<<2)>>2]-+d[s+(i<<2)>>2];m=o*y-x*k;o=x*z-o*k;k=y*z-k*k;z=(o*j+m)/k;d[f+(e<<2)>>2]=(z<0.0?0.0:z)-g;e=e+1|0;j=j+1.0;i=b[c+(e<<2)>>2]|0}while((i|0)<=-1)}n=i&65535;if((n|0)<(a|0)){do{q=i>>16;z=+d[t+(n<<2)>>2]-+d[t+(q<<2)>>2];k=+d[u+(n<<2)>>2]-+d[u+(q<<2)>>2];y=+d[v+(n<<2)>>2]-+d[v+(q<<2)>>2];o=+d[r+(n<<2)>>2]-+d[r+(q<<2)>>2];x=+d[s+(n<<2)>>2]-+d[s+(q<<2)>>2];m=o*y-x*k;o=x*z-o*k;k=y*z-k*k;z=(o*j+m)/k;d[f+(e<<2)>>2]=(z<0.0?0.0:z)-g;e=e+1|0;j=j+1.0;i=b[c+(e<<2)>>2]|0;n=i&65535}while((n|0)<(a|0));i=e}else i=e;if((i|0)<(a|0)){l=1.0/k;while(1){z=(j*o+m)*l;d[f+(i<<2)>>2]=(z<0.0?0.0:z)-g;i=i+1|0;if((i|0)==(a|0))break;else j=j+1.0}}if((h|0)<1){L=w;return}q=h>>>1;e=q-h|0;if((e|0)>-1){j=0.0;i=0}else{i=h-q|0;c=0;j=0.0;n=q;while(1){e=0-e|0;l=+d[t+(e<<2)>>2]+ +d[t+(n<<2)>>2];k=+d[u+(n<<2)>>2]-+d[u+(e<<2)>>2];z=+d[v+(e<<2)>>2]+ +d[v+(n<<2)>>2];o=+d[r+(e<<2)>>2]+ +d[r+(n<<2)>>2];y=+d[s+(n<<2)>>2]-+d[s+(e<<2)>>2];m=o*z-y*k;o=y*l-o*k;k=z*l-k*k;l=(o*j+m)/k-g;e=f+(c<<2)|0;if(l<+d[e>>2])d[e>>2]=l;c=c+1|0;j=j+1.0;e=c+q|0;if((c|0)==(i|0))break;else{n=e;e=e-h|0}}}e=i+q|0;if((e|0)<(a|0)){n=a-q|0;while(1){c=e-h|0;l=+d[t+(e<<2)>>2]-+d[t+(c<<2)>>2];k=+d[u+(e<<2)>>2]-+d[u+(c<<2)>>2];z=+d[v+(e<<2)>>2]-+d[v+(c<<2)>>2];o=+d[r+(e<<2)>>2]-+d[r+(c<<2)>>2];y=+d[s+(e<<2)>>2]-+d[s+(c<<2)>>2];m=o*z-y*k;o=y*l-o*k;k=z*l-k*k;l=(o*j+m)/k-g;e=f+(i<<2)|0;if(l<+d[e>>2])d[e>>2]=l;e=i+1|0;j=j+1.0;if((e|0)==(n|0)){i=n;break}else{i=e;e=e+q|0}}}if((i|0)>=(a|0)){L=w;return}l=1.0/k;while(1){k=(j*o+m)*l-g;e=f+(i<<2)|0;if(k<+d[e>>2])d[e>>2]=k;i=i+1|0;if((i|0)==(a|0))break;else j=j+1.0}L=w;return}function za(a,c,e,f,g){a=a|0;c=c|0;e=e|0;f=+f;g=+g;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0.0,x=0,y=0.0,z=0,A=0,B=0,C=0,D=0,E=0,F=0.0;E=L;s=b[a>>2]|0;C=a+40|0;z=b[C>>2]|0;D=L;L=L+((1*(z<<2)|0)+15&-16)|0;A=a+4|0;k=b[A>>2]|0;g=+d[k+4>>2]+g;if((z|0)>0){h=0;do{d[D+(h<<2)>>2]=-9999.0;h=h+1|0}while((h|0)!=(z|0))}F=+d[k+8>>2];g=g<F?F:g;j=(s|0)>0;if(j){i=b[a+16>>2]|0;h=0;do{d[e+(h<<2)>>2]=+d[i+(h<<2)>>2]+g;h=h+1|0}while((h|0)!=(s|0));q=b[a+8>>2]|0;f=+d[k+496>>2]-f;if(j){i=a+20|0;o=b[i>>2]|0;p=a+32|0;h=a+36|0;j=a+28|0;k=0;while(1){g=+d[c+(k<<2)>>2];n=b[o+(k<<2)>>2]|0;m=k+1|0;a:do if((m|0)<(s|0))while(1){if((b[o+(m<<2)>>2]|0)!=(n|0))break a;F=+d[c+(m<<2)>>2];g=F>g?F:g;l=m+1|0;if((l|0)<(s|0)){k=m;m=l}else{k=m;m=l;break}}while(0);if(g+6.0>+d[e+(k<<2)>>2]?(u=n>>b[p>>2],u=(u|0)<16?u:16,t=b[h>>2]|0,x=~~((f+g+-30.0)*.10000000149011612),x=(x|0)>0?x:0,x=b[(b[q+(((u|0)>0?u:0)<<2)>>2]|0)+(((x|0)<7?x:7)<<2)>>2]|0,u=x+8|0,v=~~+d[x+4>>2],w=+d[x>>2],x=~~w,(x|0)<(v|0)):0){k=~~((w+-16.0)*+(t|0)-+(t>>1|0)+ +((b[o+(k<<2)>>2]|0)-(b[j>>2]|0)|0));l=x;do{if((k|0)>0?(y=+d[u+(l<<2)>>2]+g,r=D+(k<<2)|0,+d[r>>2]<y):0)d[r>>2]=y;k=k+t|0;l=l+1|0}while((l|0)<(v|0)&(k|0)<(z|0))}if((m|0)<(s|0))k=m;else break}}else B=8}else B=8;if((B|0)==8){i=a+20|0;j=a+28|0;h=a+36|0}h=b[h>>2]|0;Aa(D,h,z);p=b[i>>2]|0;o=b[j>>2]|0;q=b[a>>2]|0;b:do if((q|0)>1){l=b[p>>2]|0;n=(b[A>>2]|0)+32|0;j=l-(o+(h>>1))|0;m=0;i=1;while(1){g=+d[D+(j<<2)>>2];k=((b[p+(i<<2)>>2]|0)+l>>1)-o|0;F=+d[n>>2];g=g>F?F:g;if((j|0)<(k|0)){h=j;do{h=h+1|0;f=+d[D+(h<<2)>>2];if(f>-9999.0){if(g==-9999.0|f<g)B=29}else if(g==-9999.0)B=29;if((B|0)==29){B=0;g=f}}while((h|0)!=(k|0))}else k=j;j=k+o|0;c:do if((m|0)>=(q|0)|(l|0)>(j|0))h=m;else{h=m;do{i=e+(h<<2)|0;if(+d[i>>2]<g)d[i>>2]=g;h=h+1|0;if((h|0)>=(q|0))break c}while((b[p+(h<<2)>>2]|0)<=(j|0))}while(0);i=h+1|0;if((i|0)>=(q|0))break b;j=k;m=h;l=b[p+(h<<2)>>2]|0}}else h=0;while(0);g=+d[D+((b[C>>2]|0)+-1<<2)>>2];if((h|0)>=(q|0)){L=E;return}do{i=e+(h<<2)|0;if(+d[i>>2]<g)d[i>>2]=g;h=h+1|0}while((h|0)!=(q|0));L=E;return}function Aa(a,c,e){a=a|0;c=c|0;e=e|0;var f=0,g=0,h=0,i=0.0,j=0,k=0,l=0,m=0.0,n=0,o=0,p=0,q=0,r=0;r=L;n=e<<2;o=L;L=L+((1*n|0)+15&-16)|0;q=L;L=L+((1*n|0)+15&-16)|0;if((e|0)<=0){L=r;return}k=0;n=0;do{a:do if((n|0)<2){b[o+(n<<2)>>2]=k;b[q+(n<<2)>>2]=b[a+(k<<2)>>2];l=n}else{m=+d[a+(k<<2)>>2];g=n+-1|0;i=+d[q+(g<<2)>>2];b:do if(m<i){g=n;f=o+(n<<2)|0}else{j=n;while(1){f=o+(g<<2)|0;if(!((j|0)>1?(k|0)<((b[f>>2]|0)+c|0):0))break;h=j+-2|0;if(!(i<=+d[q+(h<<2)>>2]))break;if((k|0)>=((b[o+(h<<2)>>2]|0)+c|0))break;h=g+-1|0;i=+d[q+(h<<2)>>2];if(m<i)break b;else{j=g;g=h}}b[o+(j<<2)>>2]=k;d[q+(j<<2)>>2]=m;l=j;break a}while(0);b[f>>2]=k;d[q+(g<<2)>>2]=m;l=g}while(0);n=l+1|0;k=k+1|0}while((k|0)!=(e|0));if((l|0)<=-1){L=r;return}k=c+1|0;f=0;j=0;do{if((j|0)<(l|0)?(p=j+1|0,+d[q+(p<<2)>>2]>+d[q+(j<<2)>>2]):0)g=b[o+(p<<2)>>2]|0;else g=k+(b[o+(j<<2)>>2]|0)|0;h=(g|0)>(e|0)?e:g;if((f|0)<(h|0)){g=b[q+(j<<2)>>2]|0;do{b[a+(f<<2)>>2]=g;f=f+1|0}while((f|0)<(h|0));f=h}j=j+1|0}while((j|0)!=(n|0));L=r;return}function Ba(a,c,e,f,g,h,i){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0.0,l=0.0,m=0.0,n=0,o=0.0,p=0,q=0,r=0.0;q=b[a>>2]|0;j=b[a+4>>2]|0;o=+d[j+12+(f<<2)>>2];if((q|0)<=0)return;p=b[(b[a+12>>2]|0)+(f<<2)>>2]|0;n=j+108|0;m=+d[a+48>>2];l=m*.005;m=m*.0003;if((f|0)!=1){j=0;do{m=+d[p+(j<<2)>>2]+ +d[c+(j<<2)>>2];l=+d[n>>2];m=m>l?l:m;l=+d[e+(j<<2)>>2]+o;d[g+(j<<2)>>2]=m<l?l:m;j=j+1|0}while((j|0)!=(q|0));return}j=0;do{r=+d[p+(j<<2)>>2]+ +d[c+(j<<2)>>2];k=+d[n>>2];r=r>k?k:r;k=+d[e+(j<<2)>>2]+o;d[g+(j<<2)>>2]=r<k?k:r;r=r-+d[i+(j<<2)>>2];k=r+17.200000762939453;if(r>-17.200000762939453){k=1.0-l*k;if(k<0.0)k=9.999999747378752e-05}else k=1.0-m*k;f=h+(j<<2)|0;d[f>>2]=+d[f>>2]*k;j=j+1|0}while((j|0)!=(q|0));return}function Ca(a,c,f,g,h,i,j,k,l){a=a|0;c=c|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,r=0.0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0.0,N=0,O=0,P=0.0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0.0,ba=0.0;$=L;Y=b[f>>2]|0;W=f+4|0;f=b[W>>2]|0;if(!(b[f+500>>2]|0))X=16;else X=b[f+508>>2]|0;O=b[c+132+((b[f>>2]|0)*60|0)+(a<<2)>>2]|0;P=+e[55888+(b[c+252+(a<<2)>>2]<<3)>>3];Q=l<<2;R=L;L=L+((1*Q|0)+15&-16)|0;S=L;L=L+((1*Q|0)+15&-16)|0;T=L;L=L+((1*Q|0)+15&-16)|0;U=L;L=L+((1*Q|0)+15&-16)|0;V=L;L=L+((1*Q|0)+15&-16)|0;_=g+1156|0;M=+e[((Y|0)>1e3?55968:55888)+(b[c+312+(a<<2)>>2]<<3)>>3];N=z(Q,X)|0;f=L;L=L+((1*N|0)+15&-16)|0;b[R>>2]=f;a=L;L=L+((1*N|0)+15&-16)|0;b[S>>2]=a;c=L;L=L+((1*N|0)+15&-16)|0;b[T>>2]=c;m=L;L=L+((1*N|0)+15&-16)|0;b[U>>2]=m;if((l|0)>1?(b[R+4>>2]=f+(X<<2),b[S+4>>2]=a+(X<<2),b[T+4>>2]=c+(X<<2),b[U+4>>2]=m+(X<<2),(l|0)!=2):0){f=2;do{H=b[S>>2]|0;I=b[T>>2]|0;J=b[U>>2]|0;K=z(f,X)|0;b[R+(f<<2)>>2]=(b[R>>2]|0)+(K<<2);b[S+(f<<2)>>2]=H+(K<<2);b[T+(f<<2)>>2]=I+(K<<2);b[U+(f<<2)>>2]=J+(K<<2);f=f+1|0}while((f|0)!=(l|0))}f=b[_>>2]|0;if((Y|0)>0){J=b[U>>2]|0;K=(l|0)>0;I=0;do{H=Y-I|0;H=(X|0)>(H|0)?H:X;Ec(V|0,j|0,Q|0)|0;Fc(J|0,0,N|0)|0;if(K){t=(H|0)>0;u=O-I|0;s=0;do{v=(b[i+(s<<2)>>2]|0)+(I<<2)|0;if(!(b[V+(s<<2)>>2]|0)){if(t){a=b[T+(s<<2)>>2]|0;c=b[R+(s<<2)>>2]|0;m=b[S+(s<<2)>>2]|0;n=b[U+(s<<2)>>2]|0;f=0;do{d[a+(f<<2)>>2]=1.000000013351432e-10;d[c+(f<<2)>>2]=0.0;d[m+(f<<2)>>2]=0.0;b[n+(f<<2)>>2]=0;b[v+(f<<2)>>2]=0;f=f+1|0}while((f|0)<(H|0))}}else{p=b[T+(s<<2)>>2]|0;if(t){f=0;do{b[p+(f<<2)>>2]=b[56064+(b[v+(f<<2)>>2]<<2)>>2];f=f+1|0}while((f|0)<(H|0));o=b[h+(s<<2)>>2]|0;a=o+(I<<2)|0;c=b[U+(s<<2)>>2]|0;if(t){f=0;do{r=+q(+(+d[a+(f<<2)>>2]));b[c+(f<<2)>>2]=!(r/+d[p+(f<<2)>>2]<((f|0)<(u|0)?P:M))&1;f=f+1|0}while((f|0)!=(H|0));if(t){f=b[R+(s<<2)>>2]|0;a=S+(s<<2)|0;m=b[a>>2]|0;c=0;do{G=o+(c+I<<2)|0;r=+d[G>>2];r=r*r;n=f+(c<<2)|0;d[n>>2]=r;d[m+(c<<2)>>2]=r;if(+d[G>>2]<0.0)d[n>>2]=-+d[n>>2];G=p+(c<<2)|0;r=+d[G>>2];d[G>>2]=r*r;c=c+1|0}while((c|0)<(H|0))}else Z=19}else Z=19}else Z=19;if((Z|0)==19){Z=0;a=S+(s<<2)|0;f=b[R+(s<<2)>>2]|0}+Da(b[W>>2]|0,O,f,b[a>>2]|0,p,0,I,H,v)}s=s+1|0}while((s|0)!=(l|0))}f=b[_>>2]|0;if((f|0)>0){E=(H|0)>0;F=k-I|0;G=O-I|0;D=0;do{a=b[g+1160+(D<<2)>>2]|0;c=b[g+2184+(D<<2)>>2]|0;s=(b[i+(a<<2)>>2]|0)+(I<<2)|0;t=(b[i+(c<<2)>>2]|0)+(I<<2)|0;u=b[R+(a<<2)>>2]|0;v=b[R+(c<<2)>>2]|0;w=b[S+(a<<2)>>2]|0;x=b[S+(c<<2)>>2]|0;y=b[T+(a<<2)>>2]|0;A=b[T+(c<<2)>>2]|0;B=b[U+(a<<2)>>2]|0;C=b[U+(c<<2)>>2]|0;a=V+(a<<2)|0;c=V+(c<<2)|0;if(!((b[a>>2]|0)==0?(b[c>>2]|0)==0:0)){b[c>>2]=1;b[a>>2]=1;if(E){p=0;do{do if((p|0)<(F|0)){f=B+(p<<2)|0;c=C+(p<<2)|0;if((b[f>>2]|0)==0?(b[c>>2]|0)==0:0){do if((p|0)>=(G|0)){a=u+(p<<2)|0;aa=+d[a>>2];f=v+(p<<2)|0;ba=+d[f>>2];r=+q(+ba)+ +q(+aa);d[w+(p<<2)>>2]=r;if(ba+aa<0.0){d[a>>2]=-r;break}else{d[a>>2]=r;break}}else{f=v+(p<<2)|0;o=u+(p<<2)|0;ba=+d[o>>2]+ +d[f>>2];d[o>>2]=ba;d[w+(p<<2)>>2]=+q(+ba)}while(0);d[x+(p<<2)>>2]=0.0;d[f>>2]=0.0;b[c>>2]=1;b[t+(p<<2)>>2]=0;break}n=u+(p<<2)|0;ba=+q(+(+d[n>>2]));d[n>>2]=+q(+(+d[v+(p<<2)>>2]))+ba;n=w+(p<<2)|0;d[n>>2]=+d[x+(p<<2)>>2]+ +d[n>>2];b[c>>2]=1;b[f>>2]=1;n=s+(p<<2)|0;f=b[n>>2]|0;o=t+(p<<2)|0;a=b[o>>2]|0;m=0-a|0;if((((f|0)>-1?f:0-f|0)|0)>(((a|0)>-1?a:m)|0)){c=(f|0)>0?f-a|0:a-f|0;b[o>>2]=c;f=b[n>>2]|0;a=f;f=0-f|0}else{b[o>>2]=(a|0)>0?f-a|0:a-f|0;b[n>>2]=a;c=b[o>>2]|0;f=m}if((c|0)>=(((a|0)>-1?a:f)<<1|0)){b[o>>2]=0-c;b[n>>2]=0-(b[n>>2]|0)}}while(0);o=y+(p<<2)|0;n=A+(p<<2)|0;ba=+d[n>>2]+ +d[o>>2];d[n>>2]=ba;d[o>>2]=ba;p=p+1|0}while((p|0)<(H|0))}+Da(b[W>>2]|0,O,u,w,y,B,I,H,s);f=b[_>>2]|0}D=D+1|0}while((D|0)<(f|0))}I=I+X|0}while((Y|0)>(I|0))}if((f|0)<=0){L=$;return}m=0;do{a=j+(b[g+1160+(m<<2)>>2]<<2)|0;c=g+2184+(m<<2)|0;if(!((b[a>>2]|0)==0?(b[j+(b[c>>2]<<2)>>2]|0)==0:0)){b[a>>2]=1;b[j+(b[c>>2]<<2)>>2]=1;f=b[_>>2]|0}m=m+1|0}while((m|0)<(f|0));L=$;return}function Da(a,c,g,h,i,j,k,l,m){a=a|0;c=c|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0.0,o=0,p=0,q=0.0,s=0.0,t=0,u=0,v=0;u=L;t=L;L=L+((1*(l<<2)|0)+15&-16)|0;if(!(b[a+500>>2]|0))o=l;else o=(b[a+504>>2]|0)-k|0;p=(o|0)>(l|0)?l:o;a:do if((p|0)>0){if(!j){o=0;while(1){v=+d[g+(o<<2)>>2]<0.0;s=+Dc(+(+r(+(+d[h+(o<<2)>>2]/+d[i+(o<<2)>>2]))));b[m+(o<<2)>>2]=~~(v?-s:s);o=o+1|0;if((o|0)>=(p|0))break a}}o=0;do{if(!(b[j+(o<<2)>>2]|0)){v=+d[g+(o<<2)>>2]<0.0;s=+Dc(+(+r(+(+d[h+(o<<2)>>2]/+d[i+(o<<2)>>2]))));b[m+(o<<2)>>2]=~~(v?-s:s)}o=o+1|0}while((o|0)<(p|0))}else p=0;while(0);if((p|0)>=(l|0)){s=0.0;L=u;return +s}k=c-k|0;if(!j){o=0;n=0.0;c=p;do{p=h+(c<<2)|0;q=+d[i+(c<<2)>>2];s=+d[p>>2]/q;if(s<.25){b[t+(o<<2)>>2]=p;n=s+n;o=o+1|0}else{v=+d[g+(c<<2)>>2]<0.0;s=+Dc(+(+r(+s)));v=~~(v?-s:s);b[m+(c<<2)>>2]=v;d[p>>2]=q*+(z(v,v)|0)}c=c+1|0}while((c|0)!=(l|0))}else{o=0;n=0.0;do{do if(!(b[j+(p<<2)>>2]|0)){c=h+(p<<2)|0;q=+d[i+(p<<2)>>2];s=+d[c>>2]/q;if(!(s<.25)|(p|0)<(k|0)){v=+d[g+(p<<2)>>2]<0.0;s=+Dc(+(+r(+s)));v=~~(v?-s:s);b[m+(p<<2)>>2]=v;d[c>>2]=q*+(z(v,v)|0);break}else{b[t+(o<<2)>>2]=c;n=s+n;o=o+1|0;break}}while(0);p=p+1|0}while((p|0)!=(l|0))}if(!o){s=n;L=u;return +s}nc(t,o,4,11);if((o|0)<=0){s=n;L=u;return +s}s=+e[a+512>>3];j=0;do{c=b[t+(j<<2)>>2]|0;k=c-h>>2;if(!(s<=n)){p=0;q=0.0}else{n=n+-1.0;p=~~(b[f>>2]=b[g+(k<<2)>>2]&-2147483648|1065353216,+d[f>>2]);q=+d[i+(k<<2)>>2]}b[m+(k<<2)>>2]=p;d[c>>2]=q;j=j+1|0}while((j|0)!=(o|0));L=u;return +n}function Ea(a,c){a=a|0;c=c|0;var e=0.0,f=0.0;f=+d[b[a>>2]>>2];e=+d[b[c>>2]>>2];return (f<e&1)-(f>e&1)|0}function Fa(a){a=a|0;b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;return}function Ga(a){a=a|0;var c=0,d=0,e=0,f=0,g=0;if(!a)return;c=b[a>>2]|0;if(c|0){g=a+8|0;d=b[g>>2]|0;if((d|0)>0){f=0;do{e=b[c+(f<<2)>>2]|0;if(e){fc(e);d=b[g>>2]|0;c=b[a>>2]|0}f=f+1|0}while((f|0)<(d|0))}fc(c)}c=b[a+4>>2]|0;if(c|0)fc(c);c=b[a+12>>2]|0;if(c|0)fc(c);b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;return}function Ha(a){a=a|0;b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;b[a+16>>2]=0;b[a+20>>2]=0;b[a+24>>2]=0;b[a+28>>2]=gc(1,3664)|0;return}function Ia(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0;i=b[a+28>>2]|0;if(!i){b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;b[a+16>>2]=0;b[a+20>>2]=0;b[a+24>>2]=0;b[a+28>>2]=0;return}f=i+8|0;c=b[f>>2]|0;if((c|0)>0){g=i+32|0;e=0;do{d=b[g+(e<<2)>>2]|0;if(d){fc(d);c=b[f>>2]|0}e=e+1|0}while((e|0)<(c|0))}f=i+12|0;c=b[f>>2]|0;if((c|0)>0){g=i+544|0;h=i+288|0;e=0;do{d=b[g+(e<<2)>>2]|0;if(d){T[b[(b[57252+(b[h+(e<<2)>>2]<<2)>>2]|0)+8>>2]&7](d);c=b[f>>2]|0}e=e+1|0}while((e|0)<(c|0))}f=i+16|0;c=b[f>>2]|0;if((c|0)>0){g=i+1056|0;h=i+800|0;e=0;do{d=b[g+(e<<2)>>2]|0;if(d){T[b[(b[57232+(b[h+(e<<2)>>2]<<2)>>2]|0)+12>>2]&7](d);c=b[f>>2]|0}e=e+1|0}while((e|0)<(c|0))}f=i+20|0;c=b[f>>2]|0;if((c|0)>0){g=i+1568|0;h=i+1312|0;e=0;do{d=b[g+(e<<2)>>2]|0;if(d){T[b[(b[57240+(b[h+(e<<2)>>2]<<2)>>2]|0)+12>>2]&7](d);c=b[f>>2]|0}e=e+1|0}while((e|0)<(c|0))}f=i+24|0;if((b[f>>2]|0)>0){g=i+1824|0;c=i+2848|0;e=0;do{d=b[g+(e<<2)>>2]|0;if(d|0)Wa(d);d=b[c>>2]|0;if(d|0)Xa(d+(e*56|0)|0);e=e+1|0}while((e|0)<(b[f>>2]|0))}else c=i+2848|0;c=b[c>>2]|0;if(c|0)fc(c);d=i+28|0;if((b[d>>2]|0)>0){e=i+2852|0;c=0;do{sa(b[e+(c<<2)>>2]|0);c=c+1|0}while((c|0)<(b[d>>2]|0))}fc(i);b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;b[a+16>>2]=0;b[a+20>>2]=0;b[a+24>>2]=0;b[a+28>>2]=0;return}function Ja(c,d,e){c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=L;L=L+32|0;l=m;f=m+20|0;if(!e){l=-133;L=m;return l|0}$b(l,b[e>>2]|0,b[e+4>>2]|0);g=cc(l,8)|0;a[f>>0]=cc(l,8)|0;a[f+1>>0]=cc(l,8)|0;a[f+2>>0]=cc(l,8)|0;a[f+3>>0]=cc(l,8)|0;a[f+4>>0]=cc(l,8)|0;a[f+5>>0]=cc(l,8)|0;a:do if(!(mc(f,57428,6)|0))switch(g|0){case 1:{if(!(b[e+8>>2]|0)){e=-133;break a}e=c+8|0;if(b[e>>2]|0){e=-133;break a}f=b[c+28>>2]|0;if(!f){e=-129;break a}d=cc(l,32)|0;b[c>>2]=d;if(d|0){e=-134;break a}d=c+4|0;b[d>>2]=cc(l,8)|0;b[e>>2]=cc(l,32)|0;b[c+12>>2]=cc(l,32)|0;b[c+16>>2]=cc(l,32)|0;b[c+20>>2]=cc(l,32)|0;b[f>>2]=1<<(cc(l,4)|0);g=1<<(cc(l,4)|0);b[f+4>>2]=g;if((((b[e>>2]|0)>=1?(b[d>>2]|0)>=1:0)?(d=b[f>>2]|0,!((g|0)>8192|((d|0)<64|(g|0)<(d|0)))):0)?(cc(l,1)|0)==1:0){e=0;break a}Ia(c);e=-133;break a}case 3:{if(!(b[c+8>>2]|0)){e=-133;break a}c=d+12|0;if(b[c>>2]|0){e=-133;break a}e=cc(l,32)|0;b:do if((e|0)>=0?(j=l+16|0,(e|0)<=((b[j>>2]|0)+-8|0)):0){f=gc(e+1|0,1)|0;b[c>>2]=f;if(e|0)while(1){e=e+-1|0;a[f>>0]=cc(l,8)|0;if(!e)break;else f=f+1|0}e=cc(l,32)|0;if((e|0)>=0?(i=b[j>>2]|0,(e|0)<=(i-(dc(l)|0)>>2|0)):0){h=d+8|0;b[h>>2]=e;g=e+1|0;b[d>>2]=gc(g,4)|0;i=d+4|0;b[i>>2]=gc(g,4)|0;if((e|0)>0){g=0;do{e=cc(l,32)|0;if((e|0)<0){j=d;break b}f=b[j>>2]|0;if((e|0)>(f-(dc(l)|0)|0)){k=32;break b}b[(b[i>>2]|0)+(g<<2)>>2]=e;f=gc(e+1|0,1)|0;b[(b[d>>2]|0)+(g<<2)>>2]=f;if(e|0){f=b[(b[d>>2]|0)+(g<<2)>>2]|0;while(1){e=e+-1|0;a[f>>0]=cc(l,8)|0;if(!e)break;else f=f+1|0}}g=g+1|0}while((g|0)<(b[h>>2]|0))}if((cc(l,1)|0)==1){e=0;break a}else{j=d;break}}if(!d){e=-133;break a}else k=32}else k=32;while(0);if((k|0)==32)j=d;e=b[j>>2]|0;if(e|0){i=d+8|0;f=b[i>>2]|0;if((f|0)>0){h=0;do{g=b[e+(h<<2)>>2]|0;if(g){fc(g);f=b[i>>2]|0;e=b[j>>2]|0}h=h+1|0}while((h|0)<(f|0))}fc(e)}e=b[d+4>>2]|0;if(e|0)fc(e);e=b[c>>2]|0;if(e|0)fc(e);b[d>>2]=0;b[d+4>>2]=0;b[d+8>>2]=0;b[d+12>>2]=0;e=-133;break a}case 5:{if(!(b[c+8>>2]|0)){e=-133;break a}if(!(b[d+12>>2]|0)){e=-133;break a}j=b[c+28>>2]|0;if(!j){e=-129;break a}if((b[j+24>>2]|0)>0){e=-133;break a}d=cc(l,8)|0;f=j+24|0;b[f>>2]=d+1;c:do if((d|0)>=0){e=0;do{d=Ka(l)|0;b[j+1824+(e<<2)>>2]=d;e=e+1|0;if(!d)break c}while((e|0)<(b[f>>2]|0));f=cc(l,6)|0;if((f|0)>=0){e=0;while(1){if(cc(l,16)|0)break c;if((e|0)<(f|0))e=e+1|0;else break}d=cc(l,6)|0;f=j+16|0;b[f>>2]=d+1;if((d|0)>=0){e=0;do{g=cc(l,16)|0;b[j+800+(e<<2)>>2]=g;if(g>>>0>1)break c;d=P[b[(b[57232+(g<<2)>>2]|0)+4>>2]&15](c,l)|0;b[j+1056+(e<<2)>>2]=d;e=e+1|0;if(!d)break c}while((e|0)<(b[f>>2]|0));d=cc(l,6)|0;f=j+20|0;b[f>>2]=d+1;if((d|0)>=0){e=0;do{g=cc(l,16)|0;b[j+1312+(e<<2)>>2]=g;if(g>>>0>2)break c;d=P[b[(b[57240+(g<<2)>>2]|0)+4>>2]&15](c,l)|0;b[j+1568+(e<<2)>>2]=d;e=e+1|0;if(!d)break c}while((e|0)<(b[f>>2]|0));d=cc(l,6)|0;i=j+12|0;b[i>>2]=d+1;if((d|0)>=0){e=0;do{d=cc(l,16)|0;b[j+288+(e<<2)>>2]=d;if(d|0)break c;d=P[b[(b[14313]|0)+4>>2]&15](c,l)|0;b[j+544+(e<<2)>>2]=d;e=e+1|0;if(!d)break c}while((e|0)<(b[i>>2]|0));d=cc(l,6)|0;f=j+8|0;b[f>>2]=d+1;if((d|0)>=0){e=0;do{h=j+32+(e<<2)|0;b[h>>2]=gc(1,16)|0;g=cc(l,1)|0;b[b[h>>2]>>2]=g;g=cc(l,16)|0;b[(b[h>>2]|0)+4>>2]=g;g=cc(l,16)|0;b[(b[h>>2]|0)+8>>2]=g;g=cc(l,8)|0;h=b[h>>2]|0;b[h+12>>2]=g;if((b[h+4>>2]|0)>0)break c;if((b[h+8>>2]|0)>0)break c;e=e+1|0;if((g|0)<0?1:(g|0)>=(b[i>>2]|0))break c}while((e|0)<(b[f>>2]|0));if((cc(l,1)|0)==1){e=0;break a}}}}}}}while(0);Ia(c);e=-133;break a}default:{e=-133;break a}}else e=-132;while(0);l=e;L=m;return l|0}function Ka(c){c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;l=gc(1,40)|0;b[l+36>>2]=1;a:do if(((cc(c,24)|0)==5653314?(b[l>>2]=cc(c,16)|0,j=cc(c,24)|0,k=l+4|0,b[k>>2]=j,(j|0)!=-1):0)?(j=Sa(b[l>>2]|0)|0,((Sa(b[k>>2]|0)|0)+j|0)<=24):0){b:do switch(cc(c,1)|0){case 0:{d=(cc(c,1)|0)!=0;if(((z(d?1:5,b[k>>2]|0)|0)+7>>3|0)>((b[c+16>>2]|0)-(dc(c)|0)|0))break a;e=b[k>>2]|0;f=l+8|0;b[f>>2]=ec(e)|0;e=(e|0)>0;if(!d){if(!e)break b;d=0;while(1){e=cc(c,5)|0;if((e|0)==-1)break a;a[(b[f>>2]|0)+d>>0]=e+1;d=d+1|0;if((d|0)>=(b[k>>2]|0))break b}}if(e){e=0;do{if(!(cc(c,1)|0))d=0;else{d=cc(c,5)|0;if((d|0)==-1)break a;d=d+1&255}a[(b[f>>2]|0)+e>>0]=d;e=e+1|0}while((e|0)<(b[k>>2]|0))}break}case 1:{d=(cc(c,5)|0)+1|0;if(!d)break a;e=b[k>>2]|0;j=l+8|0;b[j>>2]=ec(e)|0;if((e|0)>0){f=0;while(1){i=cc(c,Sa(e-f|0)|0)|0;if((d|0)>32|(i|0)==-1)break a;e=b[k>>2]|0;if((i|0)>(e-f|0))break a;if((i|0)>0){if((i+-1>>d+-1|0)>1)break a;h=d&255;e=0;g=f;while(1){a[(b[j>>2]|0)+g>>0]=h;e=e+1|0;if((e|0)==(i|0))break;else g=g+1|0}f=i+f|0;e=b[k>>2]|0}if((e|0)>(f|0))d=d+1|0;else break}}break}default:break a}while(0);j=cc(c,4)|0;d=l+12|0;b[d>>2]=j;switch(j|0){case 2:case 1:break;case 0:return l|0;default:break a}b[l+16>>2]=cc(c,32)|0;b[l+20>>2]=cc(c,32)|0;h=l+24|0;b[h>>2]=(cc(c,4)|0)+1;j=cc(c,1)|0;b[l+28>>2]=j;if((j|0)!=-1){switch(b[d>>2]|0){case 1:{if(!(b[l>>2]|0))g=0;else g=Ua(l)|0;break}case 2:{g=z(b[l>>2]|0,b[k>>2]|0)|0;break}default:g=0}j=(z(b[h>>2]|0,g)|0)+7>>3;k=b[c+16>>2]|0;if((j|0)<=(k-(dc(c)|0)|0)){d=ec(g<<2)|0;f=l+32|0;b[f>>2]=d;if((g|0)>0){e=0;do{k=cc(c,b[h>>2]|0)|0;d=b[f>>2]|0;b[d+(e<<2)>>2]=k;e=e+1|0}while((e|0)!=(g|0))}if(!g)return l|0;if((b[d+(g+-1<<2)>>2]|0)!=-1)return l|0}}}while(0);Wa(l);l=0;return l|0}function La(c,d,e){c=c|0;d=d|0;e=e|0;var f=0,g=0;if((d|0)<0){g=0;return g|0}f=c+12|0;g=b[f>>2]|0;if((b[g+4>>2]|0)<=(d|0)){g=0;return g|0}Zb(e,b[(b[c+20>>2]|0)+(d<<2)>>2]|0,a[(b[g+8>>2]|0)+d>>0]|0);g=a[(b[(b[f>>2]|0)+8>>2]|0)+d>>0]|0;return g|0}function Ma(a,c){a=a|0;c=c|0;if((b[a+8>>2]|0)<=0){a=-1;return a|0}c=Na(a,c)|0;if((c|0)<=-1){a=-1;return a|0}a=b[(b[a+24>>2]|0)+(c<<2)>>2]|0;return a|0}function Na(c,d){c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;g=b[c+40>>2]|0;e=ac(d,b[c+36>>2]|0)|0;do if((e|0)>-1){e=b[(b[c+32>>2]|0)+(e<<2)>>2]|0;if((e|0)<0){f=e>>>15&32767;k=(b[c+8>>2]|0)-(e&32767)|0;break}k=e+-1|0;bc(d,a[(b[c+28>>2]|0)+k>>0]|0);d=k;return d|0}else{f=0;k=b[c+8>>2]|0}while(0);h=ac(d,g)|0;e=(h|0)<0;if(e&(g|0)>1)while(1){i=g+-1|0;h=ac(d,i)|0;e=(h|0)<0;if(e&(g|0)>2)g=i;else{g=i;break}}if(e){d=-1;return d|0}i=zc(h|0)|0;i=i>>>4&252645135|i<<4&-252645136;i=i>>>2&858993459|i<<2&-858993460;i=i>>>1&1431655765|i<<1&-1431655766;e=k-f|0;if((e|0)>1){j=b[c+20>>2]|0;h=k;do{k=e>>>1;l=(b[j+(k+f<<2)>>2]|0)>>>0>i>>>0;f=(l?0:k)+f|0;h=h-(l?k:0)|0;e=h-f|0}while((e|0)>1)}e=a[(b[c+28>>2]|0)+f>>0]|0;if((g|0)<(e|0)){bc(d,g);l=-1;return l|0}else{bc(d,e);l=f;return l|0}return 0}function Oa(a,c,e,f){a=a|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;n=L;if((b[a+8>>2]|0)<=0){m=0;L=n;return m|0}g=b[a>>2]|0;m=(f|0)/(g|0)|0;l=L;L=L+((1*(m<<2)|0)+15&-16)|0;k=(m|0)>0;a:do if(k){i=a+16|0;h=0;while(1){j=Na(a,e)|0;if((j|0)==-1){g=-1;break}g=b[a>>2]|0;b[l+(h<<2)>>2]=(b[i>>2]|0)+((z(g,j)|0)<<2);h=h+1|0;if((h|0)>=(m|0))break a}L=n;return g|0}while(0);if((g|0)<1|k^1){m=0;L=n;return m|0}h=0;j=0;while(1){if((h|0)<(f|0)){i=0;a=h;do{k=c+(a<<2)|0;d[k>>2]=+d[k>>2]+ +d[(b[l+(i<<2)>>2]|0)+(j<<2)>>2];i=i+1|0;a=i+h|0}while((i|0)<(m|0)&(a|0)<(f|0))}j=j+1|0;if((j|0)>=(g|0)){g=0;break}else h=h+m|0}L=n;return g|0}function Pa(a,c,e,f){a=a|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;if(!((b[a+8>>2]|0)>0&(f|0)>0)){l=0;return l|0}l=a+16|0;g=0;while(1){h=Na(a,e)|0;if((h|0)==-1){g=-1;h=8;break}k=b[a>>2]|0;j=(b[l>>2]|0)+((z(k,h)|0)<<2)|0;if((k|0)>0&(g|0)<(f|0)){i=0;while(1){h=g+1|0;g=c+(g<<2)|0;d[g>>2]=+d[g>>2]+ +d[j+(i<<2)>>2];i=i+1|0;if(!((i|0)<(k|0)&(h|0)<(f|0))){g=h;break}else g=h}}if((g|0)>=(f|0)){g=0;h=8;break}}if((h|0)==8)return g|0;return 0}function Qa(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0;f=(e|0)>0;if((b[a+8>>2]|0)<=0){if(!f){e=0;return e|0}Fc(c|0,0,e<<2|0)|0;e=0;return e|0}if(!f){e=0;return e|0}k=a+16|0;f=0;while(1){g=Na(a,d)|0;if((g|0)==-1){f=-1;g=11;break}j=b[a>>2]|0;i=(b[k>>2]|0)+((z(j,g)|0)<<2)|0;if((j|0)>0&(f|0)<(e|0)){h=0;while(1){g=f+1|0;b[c+(f<<2)>>2]=b[i+(h<<2)>>2];h=h+1|0;if(!((h|0)<(j|0)&(g|0)<(e|0))){f=g;break}else f=g}}if((f|0)>=(e|0)){f=0;g=11;break}}if((g|0)==11)return f|0;return 0}function Ra(a,c,e,f,g,h){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0;if((b[a+8>>2]|0)<=0){g=0;return g|0}m=(h+e|0)/(f|0)|0;h=(e|0)/(f|0)|0;if((h|0)>=(m|0)){g=0;return g|0}l=a+16|0;e=0;while(1){i=Na(a,g)|0;if((i|0)==-1){h=-1;e=9;break}k=b[a>>2]|0;j=(b[l>>2]|0)+((z(k,i)|0)<<2)|0;if((k|0)>0&(h|0)<(m|0)){i=0;do{o=e+1|0;n=(b[c+(e<<2)>>2]|0)+(h<<2)|0;d[n>>2]=+d[n>>2]+ +d[j+(i<<2)>>2];n=(o|0)==(f|0);e=n?0:o;h=h+(n&1)|0;i=i+1|0}while((i|0)<(k|0)&(h|0)<(m|0))}if((h|0)>=(m|0)){h=0;e=9;break}}if((e|0)==9)return h|0;return 0}function Sa(a){a=a|0;var b=0;if(!a)a=0;else{b=a;a=0;do{b=b>>>1;a=a+1|0}while((b|0)!=0)}return a|0}function Ta(c,d,e){c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;u=L;L=L+144|0;q=u;r=(e|0)!=0;f=ec((r?e:d)<<2)|0;Fc(q|0,0,132)|0;s=(d|0)>0;do if(s){o=(e|0)==0&1;p=q+4|0;n=0;e=0;a:while(1){k=a[c+n>>0]|0;l=k<<24>>24;b:do if(k<<24>>24>0){i=b[q+(l<<2)>>2]|0;if(!(k<<24>>24>31|(i>>>l|0)==0)){t=17;break a}m=e+1|0;b[f+(e<<2)>>2]=i;e=q+(l<<2)|0;c:do if(!(i&1)){h=l;j=i;while(1){b[e>>2]=j+1;g=h+-1|0;if((h|0)<=1)break c;j=b[q+(g<<2)>>2]|0;e=q+(g<<2)|0;if(j&1|0){t=7;break}else h=g}}else{g=l;t=7}while(0);do if((t|0)==7){t=0;if((g|0)==1){b[p>>2]=(b[p>>2]|0)+1;break}else{b[e>>2]=b[q+(g+-1<<2)>>2]<<1;break}}while(0);if(k<<24>>24<32){e=l;while(1){g=e;e=e+1|0;h=q+(e<<2)|0;l=i;i=b[h>>2]|0;if((i>>>1|0)!=(l|0)){e=m;break b}b[h>>2]=b[q+(g<<2)>>2]<<1;if((g|0)>=31){e=m;break}}}else e=m}else e=e+o|0;while(0);n=n+1|0;if((n|0)>=(d|0)){t=19;break}}if((t|0)==17){fc(f);d=0;L=u;return d|0}else if((t|0)==19){if((e|0)!=1){t=21;break}if((b[q+8>>2]|0)==2)break;else{t=21;break}}}else t=21;while(0);d:do if((t|0)==21){do if(((((((((((((((((b[q+4>>2]&1|0)==0?(b[q+8>>2]&3|0)==0:0)?(b[q+12>>2]&7|0)==0:0)?(b[q+16>>2]&15|0)==0:0)?(b[q+20>>2]&31|0)==0:0)?(b[q+24>>2]&63|0)==0:0)?(b[q+28>>2]&127|0)==0:0)?(b[q+32>>2]&255|0)==0:0)?(b[q+36>>2]&511|0)==0:0)?(b[q+40>>2]&1023|0)==0:0)?(b[q+44>>2]&2047|0)==0:0)?(b[q+48>>2]&4095|0)==0:0)?(b[q+52>>2]&8191|0)==0:0)?(b[q+56>>2]&16383|0)==0:0)?(b[q+60>>2]&32767|0)==0:0)?(b[q+64>>2]&65535|0)==0:0)?(b[q+68>>2]&131071|0)==0:0){if(b[q+72>>2]&262143|0)break;if(b[q+76>>2]&524287|0)break;if(b[q+80>>2]&1048575|0)break;if(b[q+84>>2]&2097151|0)break;if(b[q+88>>2]&4194303|0)break;if(b[q+92>>2]&8388607|0)break;if(b[q+96>>2]&16777215|0)break;if(b[q+100>>2]&33554431|0)break;if(b[q+104>>2]&67108863|0)break;if(b[q+108>>2]&134217727|0)break;if(b[q+112>>2]&268435455|0)break;if(b[q+116>>2]&536870911|0)break;if(b[q+120>>2]&1073741823|0)break;if(b[q+124>>2]&2147483647|0)break;if(!(b[q+128>>2]|0))break d}while(0);fc(f);d=0;L=u;return d|0}while(0);if(!s){d=f;L=u;return d|0}if(!r){j=0;do{e=a[c+j>>0]|0;k=f+(j<<2)|0;if(e<<24>>24>0){i=b[k>>2]|0;h=e<<24>>24;e=0;g=0;do{e=i>>>g&1|e<<1;g=g+1|0}while((g|0)<(h|0))}else e=0;j=j+1|0;b[k>>2]=e}while((j|0)!=(d|0));L=u;return f|0}l=0;e=0;do{k=a[c+l>>0]|0;if(k<<24>>24>0){i=b[f+(e<<2)>>2]|0;j=k<<24>>24;g=0;h=0;do{g=i>>>h&1|g<<1;h=h+1|0}while((h|0)<(j|0))}else g=0;if(k<<24>>24){b[f+(e<<2)>>2]=g;e=e+1|0}l=l+1|0}while((l|0)!=(d|0));L=u;return f|0}function Ua(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;i=b[a+4>>2]|0;if((i|0)<1){i=0;return i|0}h=b[a>>2]|0;a=~~+p(+(+s(+(+(i|0)),+(1.0/+(h|0)))));if((h|0)<=0)while(1){}a=(a|0)>1?a:1;while(1){d=(i|0)/(a|0)|0;g=a+1|0;e=1;f=1;c=0;while(1){if((d|0)<(f|0)){c=10;break}f=z(f,a)|0;j=z(e,g)|0;e=(2147483647/(g|0)|0|0)<(e|0)?2147483647:j;c=c+1|0;if((c|0)>=(h|0)){c=11;break}}if((c|0)==11){c=0;if((f|0)<=(i|0)&(e|0)>(i|0))break;if((f|0)>(i|0))c=10;else a=g}if((c|0)==10)a=a+-1|0}return a|0}function Va(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0.0,n=0.0,o=0.0,r=0,t=0,u=0,v=0,w=0,x=0.0,y=0,A=0,B=0;g=b[c+12>>2]|0;if((g+-1|0)>>>0>=2){B=0;return B|0}B=b[c+16>>2]|0;n=+(B&2097151|0);n=+vc((B|0)<0?-n:n,(B>>>21&1023)+-788|0);B=b[c+20>>2]|0;o=+(B&2097151|0);o=+vc((B|0)<0?-o:o,(B>>>21&1023)+-788|0);B=b[c>>2]|0;e=gc(z(B,e)|0,4)|0;switch(g|0){case 1:{A=b[c+4>>2]|0;if((A|0)<1){B=e;return B|0}g=~~+p(+(+s(+(+(A|0)),+(1.0/+(B|0)))));if((B|0)<=0)while(1){}g=(g|0)>1?g:1;while(1){i=(A|0)/(g|0)|0;l=g+1|0;j=1;k=1;h=0;while(1){if((i|0)<(k|0)){h=11;break}k=z(k,g)|0;y=z(j,l)|0;j=(2147483647/(l|0)|0|0)<(j|0)?2147483647:y;h=h+1|0;if((h|0)>=(B|0)){h=13;break}}if((h|0)==13){h=0;if((k|0)<=(A|0)&(j|0)>(A|0))break;if((k|0)>(A|0))h=11;else g=l}if((h|0)==11)g=g+-1|0}if((A|0)<=0){B=e;return B|0}u=(f|0)!=0;v=c+8|0;w=(B|0)>0;y=c+32|0;x=n;t=c+28|0;r=0;h=0;do{if(!(u?(a[(b[v>>2]|0)+r>>0]|0)==0:0)){a:do if(w){c=b[y>>2]|0;l=(b[t>>2]|0)==0;k=z(h,B)|0;if(u){k=z(b[f+(h<<2)>>2]|0,B)|0;i=1;m=0.0;j=0;while(1){n=m+x+ +q(+(+(b[c+((((r|0)/(i|0)|0|0)%(g|0)|0)<<2)>>2]|0)))*o;d[e+(k+j<<2)>>2]=n;i=z(i,g)|0;j=j+1|0;if((j|0)==(B|0))break a;else m=l?m:n}}if(l){i=1;j=0;do{d[e+(j+k<<2)>>2]=+q(+(+(b[c+((((r|0)/(i|0)|0|0)%(g|0)|0)<<2)>>2]|0)))*o+x;i=z(i,g)|0;j=j+1|0}while((j|0)!=(B|0))}else{i=1;m=0.0;j=0;do{m=m+x+ +q(+(+(b[c+((((r|0)/(i|0)|0|0)%(g|0)|0)<<2)>>2]|0)))*o;d[e+(j+k<<2)>>2]=m;i=z(i,g)|0;j=j+1|0}while((j|0)!=(B|0))}}while(0);h=h+1|0}r=r+1|0}while((r|0)!=(A|0));return e|0}case 2:{r=b[c+4>>2]|0;if((r|0)<=0){B=e;return B|0}t=(f|0)!=0;u=c+8|0;v=(B|0)>0;w=c+32|0;c=c+28|0;l=0;g=0;do{if(!(t?(a[(b[u>>2]|0)+l>>0]|0)==0:0)){if(v){j=b[w>>2]|0;k=z(l,B)|0;h=(b[c>>2]|0)==0;i=z(g,B)|0;if(t){i=z(b[f+(g<<2)>>2]|0,B)|0;if(h){h=0;do{d[e+(i+h<<2)>>2]=+q(+(+(b[j+(h+k<<2)>>2]|0)))*o+n;h=h+1|0}while((h|0)!=(B|0))}else{m=0.0;h=0;do{m=m+n+ +q(+(+(b[j+(h+k<<2)>>2]|0)))*o;d[e+(i+h<<2)>>2]=m;h=h+1|0}while((h|0)!=(B|0))}}else if(h){h=0;do{d[e+(h+i<<2)>>2]=+q(+(+(b[j+(h+k<<2)>>2]|0)))*o+n;h=h+1|0}while((h|0)!=(B|0))}else{m=0.0;h=0;do{m=m+n+ +q(+(+(b[j+(h+k<<2)>>2]|0)))*o;d[e+(h+i<<2)>>2]=m;h=h+1|0}while((h|0)!=(B|0))}}g=g+1|0}l=l+1|0}while((l|0)<(r|0));return e|0}default:{B=e;return B|0}}return 0}function Wa(a){a=a|0;var c=0;if(!(b[a+36>>2]|0))return;c=b[a+32>>2]|0;if(c|0)fc(c);c=b[a+8>>2]|0;if(c|0)fc(c);fc(a);return}function Xa(a){a=a|0;var c=0;c=b[a+16>>2]|0;if(c|0)fc(c);c=b[a+20>>2]|0;if(c|0)fc(c);c=b[a+24>>2]|0;if(c|0)fc(c);c=b[a+28>>2]|0;if(c|0)fc(c);c=b[a+32>>2]|0;if(c|0)fc(c);c=a;a=c+56|0;do{b[c>>2]=0;c=c+4|0}while((c|0)<(a|0));return}function Ya(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0.0;d=a+16|0;e=d+40|0;do{b[d>>2]=0;d=d+4|0}while((d|0)<(e|0));b[a+12>>2]=c;j=c+4|0;k=b[j>>2]|0;b[a+4>>2]=k;b[a+8>>2]=k;b[a>>2]=b[c>>2];b[a+20>>2]=Ta(b[c+8>>2]|0,k,0)|0;j=b[j>>2]|0;a:do if((j|0)<1)d=0;else{k=b[c>>2]|0;d=~~+p(+(+s(+(+(j|0)),+(1.0/+(k|0)))));if((k|0)<=0)while(1){}d=(d|0)>1?d:1;while(1){f=(j|0)/(d|0)|0;i=d+1|0;g=1;h=1;e=0;while(1){if((f|0)<(h|0)){e=9;break}h=z(h,d)|0;l=z(g,i)|0;g=(2147483647/(i|0)|0|0)<(g|0)?2147483647:l;e=e+1|0;if((e|0)>=(k|0)){e=11;break}}if((e|0)==11){e=0;if((h|0)<=(j|0)&(g|0)>(j|0))break a;if((h|0)>(j|0))e=9;else d=i}if((e|0)==9)d=d+-1|0}}while(0);b[a+44>>2]=d;l=b[c+16>>2]|0;m=+(l&2097151|0);b[a+48>>2]=~~+Bc(+(+vc((l|0)<0?-m:m,(l>>>21&1023)+-788|0)));l=b[c+20>>2]|0;m=+(l&2097151|0);b[a+52>>2]=~~+Bc(+(+vc((l|0)<0?-m:m,(l>>>21&1023)+-788|0)));return 0}function Za(c,d){c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;s=L;e=c;f=e+56|0;do{b[e>>2]=0;e=e+4|0}while((e|0)<(f|0));p=d+4|0;h=b[p>>2]|0;if((h|0)>0){g=b[d+8>>2]|0;f=0;e=0;do{e=e+((a[g+f>>0]|0)>0&1)|0;f=f+1|0}while((f|0)<(h|0))}else e=0;b[c+4>>2]=h;n=c+8|0;b[n>>2]=e;b[c>>2]=b[d>>2];if(!e){r=0;L=s;return r|0}m=d+8|0;j=Ta(b[m>>2]|0,h,e)|0;k=e<<2;h=L;L=L+((1*k|0)+15&-16)|0;if(!j){e=b[c+16>>2]|0;if(e|0)fc(e);e=b[c+20>>2]|0;if(e|0)fc(e);e=b[c+24>>2]|0;if(e|0)fc(e);e=b[c+28>>2]|0;if(e|0)fc(e);e=b[c+32>>2]|0;if(e|0)fc(e);e=c;f=e+56|0;do{b[e>>2]=0;e=e+4|0}while((e|0)<(f|0));r=-1;L=s;return r|0}f=0;do{r=j+(f<<2)|0;o=zc(b[r>>2]|0)|0;o=o>>>4&252645135|o<<4&-252645136;o=o>>>2&858993459|o<<2&-858993460;b[r>>2]=o>>>1&1431655765|o<<1&-1431655766;b[h+(f<<2)>>2]=r;f=f+1|0}while((f|0)!=(e|0));nc(h,e,4,12);l=L;L=L+((1*k|0)+15&-16)|0;i=ec(k)|0;r=c+20|0;b[r>>2]=i;g=j;f=0;do{b[l+((b[h+(f<<2)>>2]|0)-g>>2<<2)>>2]=f;f=f+1|0}while((f|0)!=(e|0));f=0;do{b[i+(b[l+(f<<2)>>2]<<2)>>2]=b[j+(f<<2)>>2];f=f+1|0}while((f|0)!=(e|0));fc(j);b[c+16>>2]=Va(d,e,l)|0;h=ec(k)|0;b[c+24>>2]=h;i=b[p>>2]|0;j=(i|0)>0;if(j){g=b[m>>2]|0;e=0;f=0;do{if((a[g+f>>0]|0)>0){b[h+(b[l+(e<<2)>>2]<<2)>>2]=f;e=e+1|0}f=f+1|0}while((f|0)<(i|0))}else e=0;o=c+28|0;b[o>>2]=ec(e)|0;k=c+40|0;b[k>>2]=0;if(j){i=0;j=0;e=0;f=b[m>>2]|0;while(1){g=a[f+j>>0]|0;if(g<<24>>24>0){h=i+1|0;a[(b[o>>2]|0)+(b[l+(i<<2)>>2]|0)>>0]=g;f=b[m>>2]|0;e=a[f+j>>0]|0;g=b[k>>2]|0;if((g|0)<(e|0))b[k>>2]=e;else e=g}else h=i;j=j+1|0;if((j|0)>=(b[p>>2]|0))break;else i=h}if((h|0)==1)if((e|0)==1){b[c+36>>2]=1;r=gc(2,4)|0;b[c+32>>2]=r;b[r+4>>2]=1;b[r>>2]=1;r=0;L=s;return r|0}else p=1;else p=h}else p=0;e=b[n>>2]|0;if(e){f=0;while(1){e=e>>>1;g=f+1|0;if(!e)break;else f=g}e=f+-3|0;f=c+36|0;if(g>>>0<9)e=5;else e=(e|0)<8?e:8}else{e=5;f=c+36|0}b[f>>2]=e;m=1<<e;n=gc(m,4)|0;b[c+32>>2]=n;if((p|0)>0){d=0;g=e;do{k=(b[o>>2]|0)+d|0;h=a[k>>0]|0;if((g|0)>=(h|0)?(q=zc(b[(b[r>>2]|0)+(d<<2)>>2]|0)|0,q=q>>>4&252645135|q<<4&-252645136,q=q>>>2&858993459|q<<2&-858993460,q=q>>>1&1431655765|q<<1&-1431655766,(g-h|0)!=31):0){j=d+1|0;i=0;do{b[n+((q|i<<h)<<2)>>2]=j;i=i+1|0;g=b[f>>2]|0;h=a[k>>0]|0}while((i|0)<(1<<g-h|0))}d=d+1|0}while((d|0)!=(p|0));if((e|0)==31){r=0;L=s;return r|0}else e=g}l=-2<<31-e;d=32-e|0;e=0;f=0;k=0;while(1){i=k<<d;j=zc(i|0)|0;j=j>>>4&252645135|j<<4&-252645136;j=j>>>2&858993459|j<<2&-858993460;j=n+((j>>>1&1431655765|j<<1&-1431655766)<<2)|0;if(!(b[j>>2]|0)){g=f+1|0;a:do if((g|0)<(p|0)){h=b[r>>2]|0;while(1){if((b[h+(g<<2)>>2]|0)>>>0>i>>>0)break a;f=g+1|0;if((f|0)<(p|0)){q=g;g=f;f=q}else{f=g;break}}}while(0);b:do if((p|0)>(e|0)){g=b[r>>2]|0;do{if(i>>>0<(b[g+(e<<2)>>2]&l)>>>0)break b;e=e+1|0}while((p|0)>(e|0))}while(0);q=p-e|0;b[j>>2]=(f>>>0<32767?f:32767)<<15|(q>>>0<32767?q:32767)|-2147483648}k=k+1|0;if((k|0)>=(m|0)){e=0;break}}L=s;return e|0}function _a(a,c){a=a|0;c=c|0;a=b[b[a>>2]>>2]|0;c=b[b[c>>2]>>2]|0;return (a>>>0>c>>>0&1)-(a>>>0<c>>>0&1)|0}function $a(a){a=a|0;var c=0;c=a+48|0;do{b[a>>2]=0;a=a+4|0}while((a|0)<(c|0));return}function ab(a){a=a|0;return (b[(b[(b[a+64>>2]|0)+104>>2]|0)+80>>2]|0)!=0|0}function bb(a,c){a=a|0;c=c|0;var e=0,f=0,g=0,h=0.0,i=0.0,j=0,k=0,l=0,m=0,n=0,o=0.0,p=0.0;f=(c|0)/4|0;n=ec(f<<2)|0;j=ec(f+c<<2)|0;g=c>>1;o=+(c|0);l=~~+Dc(+(+x(+o)*1.4426950408889634));b[a+4>>2]=l;b[a>>2]=c;b[a+8>>2]=j;b[a+12>>2]=n;if((c|0)<=3){o=4.0/o;n=a+16|0;d[n>>2]=o;return}h=1.0/+(c|0);i=1.0/+(c<<1|0);e=0;do{p=3.141592653589793*h*+(e<<2|0);m=e<<1;d[j+(m<<2)>>2]=+t(+p);k=m|1;d[j+(k<<2)>>2]=-+u(+p);p=3.141592653589793*i*+(k|0);m=m+g|0;d[j+(m<<2)>>2]=+t(+p);d[j+(m+1<<2)>>2]=+u(+p);e=e+1|0}while((e|0)!=(f|0));m=(c|0)/8|0;f=(c|0)>7;if(!f){p=4.0/o;n=a+16|0;d[n>>2]=p;return}h=1.0/+(c|0);e=0;do{p=3.141592653589793*h*+(e<<2|2|0);k=(e<<1)+c|0;d[j+(k<<2)>>2]=+t(+p)*.5;d[j+(k+1<<2)>>2]=+u(+p)*-.5;e=e+1|0}while((e|0)!=(m|0));k=(1<<l+-1)+-1|0;j=1<<l+-2;if(!f){p=4.0/o;n=a+16|0;d[n>>2]=p;return}f=0;do{e=0;g=0;c=j;do{g=((c&f|0)==0?0:1<<e)|g;e=e+1|0;c=j>>e}while((c|0)!=0);l=f<<1;b[n+(l<<2)>>2]=(k&~g)+-1;b[n+((l|1)<<2)>>2]=g;f=f+1|0}while((f|0)!=(m|0));p=4.0/o;n=a+16|0;d[n>>2]=p;return}function cb(a){a=a|0;var c=0;if(!a)return;c=b[a+8>>2]|0;if(c|0)fc(c);c=b[a+12>>2]|0;if(c|0)fc(c);b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;b[a+16>>2]=0;return}function db(a,c,e){a=a|0;c=c|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0.0,v=0.0,w=0.0,x=0.0,y=0.0,z=0.0,A=0.0,B=0.0,C=0.0;n=b[a>>2]|0;m=n>>1;n=n>>2;j=c+(m<<2)|0;p=e+(m<<2)|0;o=p+(n<<2)|0;k=a+8|0;l=b[k>>2]|0;g=l+(n<<2)|0;f=g;h=o;i=j+-28|0;while(1){q=h;h=h+-16|0;r=i+8|0;s=f+12|0;t=f+8|0;d[h>>2]=-(+d[r>>2]*+d[s>>2])-+d[t>>2]*+d[i>>2];d[q+-12>>2]=+d[s>>2]*+d[i>>2]-+d[t>>2]*+d[r>>2];r=i+24|0;t=f+4|0;s=i+16|0;d[q+-8>>2]=-(+d[r>>2]*+d[t>>2])-+d[f>>2]*+d[s>>2];d[q+-4>>2]=+d[t>>2]*+d[s>>2]-+d[f>>2]*+d[r>>2];i=i+-32|0;if(i>>>0<c>>>0)break;else f=f+16|0}h=o;f=j+-32|0;while(1){r=f+16|0;t=g+-4|0;s=f+24|0;q=g+-8|0;d[h>>2]=+d[q>>2]*+d[s>>2]+ +d[t>>2]*+d[r>>2];d[h+4>>2]=+d[q>>2]*+d[r>>2]-+d[t>>2]*+d[s>>2];s=g+-12|0;g=g+-16|0;t=f+8|0;d[h+8>>2]=+d[g>>2]*+d[t>>2]+ +d[s>>2]*+d[f>>2];d[h+12>>2]=+d[g>>2]*+d[f>>2]-+d[s>>2]*+d[t>>2];f=f+-32|0;if(f>>>0<c>>>0)break;else h=h+16|0}eb(b[a+4>>2]|0,l,p,m);i=b[a>>2]|0;c=b[k>>2]|0;j=e+(i>>1<<2)|0;f=b[a+12>>2]|0;g=e;h=j;i=c+(i<<2)|0;while(1){r=j+(b[f>>2]<<2)|0;t=j+(b[f+4>>2]<<2)|0;y=+d[r+4>>2];B=+d[t+4>>2];z=y-B;u=+d[r>>2];x=+d[t>>2];C=x+u;A=+d[i>>2];w=+d[i+4>>2];v=w*z+C*A;z=w*C-A*z;t=h;h=h+-16|0;y=(B+y)*.5;x=(u-x)*.5;d[g>>2]=v+y;d[t+-8>>2]=y-v;d[g+4>>2]=z+x;d[t+-4>>2]=z-x;r=j+(b[f+8>>2]<<2)|0;s=j+(b[f+12>>2]<<2)|0;x=+d[r+4>>2];z=+d[s+4>>2];v=x-z;y=+d[r>>2];u=+d[s>>2];B=u+y;A=+d[i+8>>2];C=+d[i+12>>2];w=C*v+B*A;v=C*B-A*v;x=(z+x)*.5;u=(y-u)*.5;d[g+8>>2]=w+x;d[h>>2]=x-w;d[g+12>>2]=v+u;d[t+-12>>2]=v-u;g=g+16|0;if(g>>>0>=h>>>0)break;else{f=f+16|0;i=i+16|0}}g=e;h=o;i=o;f=c+(m<<2)|0;while(1){s=f+4|0;r=g+4|0;d[i+-4>>2]=+d[s>>2]*+d[g>>2]-+d[f>>2]*+d[r>>2];d[h>>2]=-(+d[f>>2]*+d[g>>2]+ +d[s>>2]*+d[r>>2]);r=g+8|0;s=f+12|0;t=g+12|0;q=f+8|0;d[i+-8>>2]=+d[s>>2]*+d[r>>2]-+d[q>>2]*+d[t>>2];d[h+4>>2]=-(+d[q>>2]*+d[r>>2]+ +d[s>>2]*+d[t>>2]);t=g+16|0;s=f+20|0;r=g+20|0;q=f+16|0;d[i+-12>>2]=+d[s>>2]*+d[t>>2]-+d[q>>2]*+d[r>>2];i=i+-16|0;d[h+8>>2]=-(+d[q>>2]*+d[t>>2]+ +d[s>>2]*+d[r>>2]);r=g+24|0;s=f+28|0;t=g+28|0;q=f+24|0;d[i>>2]=+d[s>>2]*+d[r>>2]-+d[q>>2]*+d[t>>2];d[h+12>>2]=-(+d[q>>2]*+d[r>>2]+ +d[s>>2]*+d[t>>2]);g=g+32|0;if(g>>>0>=i>>>0)break;else{h=h+16|0;f=f+32|0}}h=e+(n<<2)|0;f=o;g=h;do{t=h;h=h+-16|0;C=+d[f+-4>>2];d[t+-4>>2]=C;d[g>>2]=-C;C=+d[f+-8>>2];d[t+-8>>2]=C;d[g+4>>2]=-C;C=+d[f+-12>>2];f=f+-16|0;d[t+-12>>2]=C;d[g+8>>2]=-C;C=+d[f>>2];d[h>>2]=C;d[g+12>>2]=-C;g=g+16|0}while(g>>>0<f>>>0);g=o;f=o;while(1){t=f;f=f+-16|0;b[f>>2]=b[g+12>>2];b[t+-12>>2]=b[g+8>>2];b[t+-8>>2]=b[g+4>>2];b[t+-4>>2]=b[g>>2];if(f>>>0<=p>>>0)break;else g=g+16|0}return}function eb(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0.0,q=0.0,r=0.0,s=0.0,t=0.0,u=0,v=0,w=0,x=0,y=0,A=0.0,B=0.0,C=0.0,D=0.0,E=0.0,F=0.0,G=0.0,H=0.0,I=0.0,J=0.0,K=0.0,L=0.0,M=0.0,N=0,O=0,P=0,Q=0.0,R=0.0,S=0,T=0.0,U=0.0,V=0,W=0.0,X=0.0,Y=0,Z=0,_=0,$=0.0,aa=0,ba=0.0,ca=0,da=0,ea=0.0,fa=0.0,ga=0,ha=0.0,ia=0.0,ja=0,ka=0.0,la=0.0,ma=0,na=0,oa=0,pa=0.0,qa=0.0;if((a|0)>6){f=b;g=c+(e>>1<<2)+-32|0;h=c+(e<<2)|0;while(1){n=h;h=h+-32|0;l=n+-8|0;s=+d[l>>2];k=g+24|0;q=+d[k>>2];p=s-q;o=n+-4|0;r=+d[o>>2];m=g+28|0;t=r-+d[m>>2];d[l>>2]=q+s;d[o>>2]=+d[m>>2]+r;o=f+4|0;d[k>>2]=+d[f>>2]*p+ +d[o>>2]*t;d[m>>2]=+d[f>>2]*t-+d[o>>2]*p;m=n+-16|0;p=+d[m>>2];o=g+16|0;t=+d[o>>2];r=p-t;k=n+-12|0;s=+d[k>>2];l=g+20|0;q=s-+d[l>>2];d[m>>2]=t+p;d[k>>2]=+d[l>>2]+s;k=f+20|0;m=f+16|0;d[o>>2]=+d[m>>2]*r+ +d[k>>2]*q;d[l>>2]=+d[m>>2]*q-+d[k>>2]*r;l=n+-24|0;r=+d[l>>2];k=g+8|0;q=+d[k>>2];s=r-q;m=n+-20|0;p=+d[m>>2];o=g+12|0;t=p-+d[o>>2];d[l>>2]=q+r;d[m>>2]=+d[o>>2]+p;m=f+36|0;l=f+32|0;d[k>>2]=+d[l>>2]*s+ +d[m>>2]*t;d[o>>2]=+d[l>>2]*t-+d[m>>2]*s;s=+d[h>>2];t=+d[g>>2];p=s-t;n=n+-28|0;r=+d[n>>2];o=g+4|0;q=r-+d[o>>2];d[h>>2]=t+s;d[n>>2]=+d[o>>2]+r;n=f+52|0;m=f+48|0;d[g>>2]=+d[m>>2]*p+ +d[n>>2]*q;d[o>>2]=+d[m>>2]*q-+d[n>>2]*p;g=g+-32|0;if(g>>>0<c>>>0)break;else f=f+64|0}if((a|0)>7){j=a+-6|0;g=1;do{k=1<<g;if((g|0)!=31){l=e>>g;m=4<<g;n=l>>1;f=0;do{o=c+((z(f,l)|0)<<2)|0;a=b;h=o+(n<<2)+-32|0;i=o+(l<<2)|0;while(1){v=i;i=i+-32|0;y=v+-8|0;q=+d[y>>2];x=h+24|0;s=+d[x>>2];t=q-s;w=v+-4|0;r=+d[w>>2];u=h+28|0;p=r-+d[u>>2];d[y>>2]=s+q;d[w>>2]=+d[u>>2]+r;w=a+4|0;d[x>>2]=+d[a>>2]*t+ +d[w>>2]*p;d[u>>2]=+d[a>>2]*p-+d[w>>2]*t;a=a+(m<<2)|0;u=v+-16|0;t=+d[u>>2];w=h+16|0;p=+d[w>>2];r=t-p;x=v+-12|0;q=+d[x>>2];y=h+20|0;s=q-+d[y>>2];d[u>>2]=p+t;d[x>>2]=+d[y>>2]+q;x=a+4|0;d[w>>2]=+d[a>>2]*r+ +d[x>>2]*s;d[y>>2]=+d[a>>2]*s-+d[x>>2]*r;a=a+(m<<2)|0;y=v+-24|0;r=+d[y>>2];x=h+8|0;s=+d[x>>2];q=r-s;w=v+-20|0;t=+d[w>>2];u=h+12|0;p=t-+d[u>>2];d[y>>2]=s+r;d[w>>2]=+d[u>>2]+t;w=a+4|0;d[x>>2]=+d[a>>2]*q+ +d[w>>2]*p;d[u>>2]=+d[a>>2]*p-+d[w>>2]*q;a=a+(m<<2)|0;q=+d[i>>2];p=+d[h>>2];t=q-p;v=v+-28|0;r=+d[v>>2];u=h+4|0;s=r-+d[u>>2];d[i>>2]=p+q;d[v>>2]=+d[u>>2]+r;v=a+4|0;d[h>>2]=+d[a>>2]*t+ +d[v>>2]*s;d[u>>2]=+d[a>>2]*s-+d[v>>2]*t;h=h+-32|0;if(h>>>0<o>>>0)break;else a=a+(m<<2)|0}f=f+1|0}while((f|0)<(k|0))}g=g+1|0}while((g|0)!=(j|0))}}if((e|0)<=0)return;a=0;do{ma=c+(a<<2)|0;n=ma+120|0;B=+d[n>>2];_=ma+56|0;ia=+d[_>>2];W=B-ia;x=ma+124|0;s=+d[x>>2];O=ma+60|0;D=+d[O>>2];I=s-D;B=ia+B;d[n>>2]=B;s=D+s;d[x>>2]=s;d[_>>2]=W;d[O>>2]=I;o=ma+112|0;D=+d[o>>2];Z=ma+48|0;ia=+d[Z>>2];K=D-ia;y=ma+116|0;t=+d[y>>2];N=ma+52|0;$=+d[N>>2];C=t-$;D=ia+D;d[o>>2]=D;t=$+t;d[y>>2]=t;$=K*.9238795042037964-C*.3826834261417389;d[Z>>2]=$;K=C*.9238795042037964+K*.3826834261417389;d[N>>2]=K;u=ma+104|0;C=+d[u>>2];V=ma+40|0;ia=+d[V>>2];T=C-ia;v=ma+108|0;p=+d[v>>2];S=ma+44|0;X=+d[S>>2];r=p-X;C=ia+C;d[u>>2]=C;p=X+p;d[v>>2]=p;X=T-r;d[V>>2]=X*.7071067690849304;T=r+T;d[S>>2]=T*.7071067690849304;b=ma+96|0;r=+d[b>>2];Y=ma+32|0;ia=+d[Y>>2];U=r-ia;w=ma+100|0;A=+d[w>>2];P=ma+36|0;Q=+d[P>>2];M=A-Q;r=ia+r;d[b>>2]=r;A=Q+A;d[w>>2]=A;Q=U*.3826834261417389-M*.9238795042037964;d[Y>>2]=Q;U=M*.3826834261417389+U*.9238795042037964;d[P>>2]=U;f=ma+88|0;M=+d[f>>2];oa=ma+24|0;ia=+d[oa>>2];fa=M-ia;ca=ma+28|0;G=+d[ca>>2];l=ma+92|0;E=+d[l>>2];qa=G-E;M=ia+M;d[f>>2]=M;G=E+G;d[l>>2]=G;d[ca>>2]=fa;na=ma+16|0;E=+d[na>>2];g=ma+80|0;ia=+d[g>>2];pa=E-ia;aa=ma+20|0;L=+d[aa>>2];m=ma+84|0;ba=+d[m>>2];J=L-ba;E=ia+E;d[g>>2]=E;L=ba+L;d[m>>2]=L;ba=J*.9238795042037964+pa*.3826834261417389;pa=J*.3826834261417389-pa*.9238795042037964;ja=ma+8|0;J=+d[ja>>2];i=ma+72|0;ia=+d[i>>2];R=J-ia;ga=ma+12|0;q=+d[ga>>2];j=ma+76|0;ka=+d[j>>2];H=q-ka;J=ia+J;d[i>>2]=J;q=ka+q;d[j>>2]=q;ka=H+R;R=H-R;H=+d[ma>>2];h=ma+64|0;ia=+d[h>>2];ea=H-ia;da=ma+4|0;F=+d[da>>2];k=ma+68|0;ha=+d[k>>2];la=F-ha;H=ia+H;d[h>>2]=H;F=ha+F;d[k>>2]=F;ha=la*.3826834261417389+ea*.9238795042037964;ea=la*.9238795042037964-ea*.3826834261417389;la=ea-U;ia=ha-Q;Q=ha+Q;U=ea+U;ea=ia+la;ia=la-ia;la=(R-T)*.7071067690849304;ha=(X-ka)*.7071067690849304;X=(ka+X)*.7071067690849304;T=(R+T)*.7071067690849304;R=$-ba;ka=K-pa;$=ba+$;K=pa+K;pa=R-ka;R=ka+R;ka=W-qa;ba=I-fa;W=qa+W;I=fa+I;fa=la+ka;la=ka-la;ka=(ea+pa)*.7071067690849304;ea=(pa-ea)*.7071067690849304;d[oa>>2]=ka+fa;d[na>>2]=fa-ka;ka=(R-ia)*.7071067690849304;fa=ba-ha;d[ma>>2]=ka+la;d[ja>>2]=la-ka;R=(ia+R)*.7071067690849304;ba=ha+ba;d[ga>>2]=ea+fa;d[da>>2]=fa-ea;d[ca>>2]=R+ba;d[aa>>2]=ba-R;R=X+W;X=W-X;W=Q+$;Q=$-Q;d[_>>2]=W+R;d[Z>>2]=R-W;W=K-U;R=I-T;d[Y>>2]=W+X;d[V>>2]=X-W;K=U+K;I=T+I;d[S>>2]=Q+R;d[P>>2]=R-Q;d[O>>2]=K+I;d[N>>2]=I-K;K=F-A;I=H-r;r=H+r;A=F+A;F=K+I;I=K-I;K=q-p;H=C-J;C=J+C;p=q+p;q=D-E;J=t-L;D=E+D;t=L+t;L=q-J;q=J+q;J=B-M;E=s-G;B=M+B;s=G+s;G=K+J;K=J-K;J=(F+L)*.7071067690849304;F=(L-F)*.7071067690849304;d[f>>2]=J+G;d[g>>2]=G-J;J=(q-I)*.7071067690849304;G=E-H;d[h>>2]=J+K;d[i>>2]=K-J;q=(I+q)*.7071067690849304;E=H+E;d[j>>2]=F+G;d[k>>2]=G-F;d[l>>2]=q+E;d[m>>2]=E-q;q=C+B;C=B-C;B=r+D;r=D-r;d[n>>2]=B+q;d[o>>2]=q-B;B=t-A;q=s-p;d[b>>2]=B+C;d[u>>2]=C-B;t=A+t;s=p+s;d[v>>2]=r+q;d[w>>2]=q-r;d[x>>2]=t+s;d[y>>2]=s-t;a=a+32|0}while((a|0)<(e|0));return}function fb(a,c,e){a=a|0;c=c|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0.0,x=0.0,y=0,z=0.0,A=0.0,B=0.0,C=0.0,D=0.0,E=0.0,F=0.0;v=L;s=b[a>>2]|0;t=s>>1;u=s>>2;n=s>>3;i=L;L=L+((1*(s<<2)|0)+15&-16)|0;r=i+(t<<2)|0;g=c+(t<<2)+(u<<2)|0;p=a+8|0;q=b[p>>2]|0;h=q+(t<<2)|0;if((s|0)>7){k=((n|0)>2?n:2)+-1|0;m=u+t+-4-(k>>>1<<2)|0;k=k&-2;l=q+(t+-2-k<<2)|0;j=g+4|0;f=g;g=0;while(1){y=f;f=f+-16|0;o=h;h=h+-8|0;w=+d[j>>2]+ +d[y+-8>>2];x=+d[j+8>>2]+ +d[f>>2];o=o+-4|0;d[r+(g<<2)>>2]=+d[h>>2]*w+x*+d[o>>2];d[r+((g|1)<<2)>>2]=+d[h>>2]*x-+d[o>>2]*w;g=g+2|0;if((g|0)>=(n|0))break;else j=j+16|0}f=k+2|0;h=l;j=c+(m<<2)|0}else{f=0;j=g}g=c+4|0;m=t-n|0;if((f|0)<(m|0)){o=t+-1-n-f|0;l=o&-2;n=-2-l|0;o=(o>>>1<<2)+5|0;l=f+l|0;k=h;while(1){y=k;k=k+-8|0;x=+d[j+-8>>2]-+d[g>>2];j=j+-16|0;w=+d[j>>2]-+d[g+8>>2];y=y+-4|0;d[r+(f<<2)>>2]=+d[k>>2]*x+w*+d[y>>2];d[r+((f|1)<<2)>>2]=+d[k>>2]*w-+d[y>>2]*x;f=f+2|0;if((f|0)>=(m|0))break;else g=g+16|0}f=l+2|0;h=h+(n<<2)|0;g=c+(o<<2)|0}if((f|0)<(t|0)){j=h;h=c+(s<<2)|0;while(1){y=j;j=j+-8|0;x=-+d[h+-8>>2]-+d[g>>2];h=h+-16|0;w=-+d[h>>2]-+d[g+8>>2];y=y+-4|0;d[r+(f<<2)>>2]=+d[j>>2]*x+w*+d[y>>2];d[r+((f|1)<<2)>>2]=+d[j>>2]*w-+d[y>>2]*x;f=f+2|0;if((f|0)>=(t|0))break;else g=g+16|0}}eb(b[a+4>>2]|0,q,r,t);j=b[a>>2]|0;l=b[p>>2]|0;k=i+(j>>1<<2)|0;f=b[a+12>>2]|0;g=i;h=k;j=l+(j<<2)|0;while(1){q=k+(b[f>>2]<<2)|0;y=k+(b[f+4>>2]<<2)|0;B=+d[q+4>>2];E=+d[y+4>>2];C=B-E;x=+d[q>>2];A=+d[y>>2];F=A+x;D=+d[j>>2];z=+d[j+4>>2];w=z*C+F*D;C=z*F-D*C;y=h;h=h+-16|0;B=(E+B)*.5;A=(x-A)*.5;d[g>>2]=w+B;d[y+-8>>2]=B-w;d[g+4>>2]=C+A;d[y+-4>>2]=C-A;q=k+(b[f+8>>2]<<2)|0;r=k+(b[f+12>>2]<<2)|0;A=+d[q+4>>2];C=+d[r+4>>2];w=A-C;B=+d[q>>2];x=+d[r>>2];E=x+B;D=+d[j+8>>2];F=+d[j+12>>2];z=F*w+E*D;w=F*E-D*w;A=(C+A)*.5;x=(B-x)*.5;d[g+8>>2]=z+A;d[h>>2]=A-z;d[g+12>>2]=w+x;d[y+-12>>2]=w-x;g=g+16|0;if(g>>>0>=h>>>0)break;else{f=f+16|0;j=j+16|0}}if((s|0)<=3){L=v;return}j=a+16|0;h=l+(t<<2)|0;f=e+(t<<2)|0;g=0;while(1){f=f+-4|0;y=i+4|0;t=h+4|0;d[e+(g<<2)>>2]=(+d[t>>2]*+d[y>>2]+ +d[h>>2]*+d[i>>2])*+d[j>>2];d[f>>2]=(+d[t>>2]*+d[i>>2]-+d[h>>2]*+d[y>>2])*+d[j>>2];g=g+1|0;if((g|0)>=(u|0))break;else{i=i+8|0;h=h+8|0}}L=v;return}function gb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;p=b[a>>2]|0;if((p|0)==1)return;q=b[a+4>>2]|0;o=q+(p<<2)|0;n=b[a+8>>2]|0;l=b[n+4>>2]|0;if((l|0)<=0)return;m=l+1|0;i=0;j=p;a=1;k=p;do{d=b[n+(m-i<<2)>>2]|0;g=(p|0)/(j|0)|0;j=(j|0)/(d|0)|0;e=z(g,j)|0;k=k-(z(g,d+-1|0)|0)|0;h=1-a|0;a:do switch(d|0){case 4:{e=k+g|0;a=o+(k<<2)+-4|0;d=o+(e<<2)+-4|0;e=o+(e+g<<2)+-4|0;if(!h){hb(g,j,c,q,a,d,e);a=0;break a}else{hb(g,j,q,c,a,d,e);a=h;break a}}case 2:{a=o+(k<<2)+-4|0;if(!h){ib(g,j,c,q,a);a=0;break a}else{ib(g,j,q,c,a);a=h;break a}}default:{f=o+(k<<2)+-4|0;if(!(((g|0)==1?a:h)|0)){jb(g,d,j,e,c,c,c,q,q,f);a=1;break a}else{jb(g,d,j,e,q,q,q,c,c,f);a=0;break a}}}while(0);i=i+1|0}while((i|0)!=(l|0));if(!((p|0)>0&(a|0)!=1))return;a=0;do{b[c+(a<<2)>>2]=b[q+(a<<2)>>2];a=a+1|0}while((a|0)!=(p|0));return}function hb(a,b,c,e,f,g,h){a=a|0;b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0.0,y=0.0,A=0.0,B=0.0,C=0.0,D=0.0,E=0.0,F=0.0,G=0.0,H=0;r=z(b,a)|0;q=r<<1;p=(b|0)>0;if(p){n=(a<<2)+-1|0;o=a<<1;i=0;j=r;k=r*3|0;l=0;m=q;while(1){t=c+(j<<2)|0;u=c+(k<<2)|0;x=+d[u>>2]+ +d[t>>2];w=c+(l<<2)|0;v=c+(m<<2)|0;y=+d[v>>2]+ +d[w>>2];s=l<<2;d[e+(s<<2)>>2]=y+x;d[e+(n+s<<2)>>2]=y-x;s=s+o|0;d[e+(s+-1<<2)>>2]=+d[w>>2]-+d[v>>2];d[e+(s<<2)>>2]=+d[u>>2]-+d[t>>2];i=i+1|0;if((i|0)==(b|0))break;else{j=j+a|0;k=k+a|0;l=l+a|0;m=m+a|0}}}if((a|0)<2)return;if((a|0)!=2){if(p){o=a<<1;k=0;n=0;while(1){m=k<<2;i=m+o|0;j=2;l=k;do{u=l;l=l+2|0;v=m;m=m+2|0;w=i;i=i+-2|0;t=l+r|0;H=j+-2|0;C=+d[f+(H<<2)>>2];G=+d[c+(t+-1<<2)>>2];s=j+-1|0;F=+d[f+(s<<2)>>2];y=+d[c+(t<<2)>>2];D=y*F+G*C;G=y*C-F*G;t=t+r|0;F=+d[g+(H<<2)>>2];C=+d[c+(t+-1<<2)>>2];y=+d[g+(s<<2)>>2];A=+d[c+(t<<2)>>2];E=A*y+C*F;C=A*F-y*C;t=t+r|0;y=+d[h+(H<<2)>>2];F=+d[c+(t+-1<<2)>>2];A=+d[h+(s<<2)>>2];B=+d[c+(t<<2)>>2];x=B*A+F*y;F=B*y-A*F;A=x+D;D=x-D;x=F+G;F=G-F;G=+d[c+(l<<2)>>2];y=G+C;C=G-C;G=+d[c+(u+1<<2)>>2];B=G+E;E=G-E;d[e+((v|1)<<2)>>2]=A+B;d[e+(m<<2)>>2]=x+y;d[e+(w+-3<<2)>>2]=E-F;d[e+(i<<2)>>2]=D-C;w=m+o|0;d[e+(w+-1<<2)>>2]=F+E;d[e+(w<<2)>>2]=D+C;w=i+o|0;d[e+(w+-1<<2)>>2]=B-A;d[e+(w<<2)>>2]=x-y;j=j+2|0}while((j|0)<(a|0));n=n+1|0;if((n|0)==(b|0))break;else k=k+a|0}}if(a&1|0)return}i=a+-1+r|0;n=a<<2;o=a<<1;if(!p)return;l=a;m=i;i=i+q|0;j=a;k=0;while(1){E=+d[c+(m<<2)>>2];F=+d[c+(i<<2)>>2];G=(F+E)*-.7071067690849304;F=(E-F)*.7071067690849304;w=c+(l+-1<<2)|0;d[e+(j+-1<<2)>>2]=F+ +d[w>>2];H=j+o|0;d[e+(H+-1<<2)>>2]=+d[w>>2]-F;w=c+(m+r<<2)|0;d[e+(j<<2)>>2]=G-+d[w>>2];d[e+(H<<2)>>2]=+d[w>>2]+G;k=k+1|0;if((k|0)==(b|0))break;else{l=l+a|0;m=m+a|0;i=i+a|0;j=j+n|0}}return}function ib(a,c,e,f,g){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0.0,u=0,v=0,w=0,x=0.0,y=0.0,A=0.0,B=0.0;q=z(c,a)|0;r=a<<1;p=(c|0)>0;if(p){k=r+-1|0;h=q;i=0;j=0;while(1){m=e+(i<<2)|0;n=e+(h<<2)|0;o=i<<1;d[f+(o<<2)>>2]=+d[n>>2]+ +d[m>>2];d[f+(k+o<<2)>>2]=+d[m>>2]-+d[n>>2];j=j+1|0;if((j|0)==(c|0))break;else{h=h+a|0;i=i+a|0}}}if((a|0)<2)return;if((a|0)!=2){if(p){m=q;n=0;o=0;while(1){l=n<<1;h=n;i=l+r|0;j=m;k=2;do{w=j;j=j+2|0;s=i;i=i+-2|0;u=h;h=h+2|0;v=l;l=l+2|0;A=+d[g+(k+-2<<2)>>2];x=+d[e+(w+1<<2)>>2];y=+d[g+(k+-1<<2)>>2];B=+d[e+(j<<2)>>2];t=B*y+x*A;x=B*A-y*x;w=e+(h<<2)|0;d[f+(l<<2)>>2]=x+ +d[w>>2];d[f+(i<<2)>>2]=x-+d[w>>2];u=e+(u+1<<2)|0;d[f+((v|1)<<2)>>2]=+d[u>>2]+t;d[f+(s+-3<<2)>>2]=+d[u>>2]-t;k=k+2|0}while((k|0)<(a|0));o=o+1|0;if((o|0)==(c|0))break;else{m=m+a|0;n=n+a|0}}}if(((a|0)%2|0|0)==1)return}h=a+-1|0;if(!p)return;i=h;j=a;k=0;h=q+h|0;while(1){d[f+(j<<2)>>2]=-+d[e+(h<<2)>>2];b[f+(j+-1<<2)>>2]=b[e+(i<<2)>>2];k=k+1|0;if((k|0)==(c|0))break;else{i=i+a|0;j=j+r|0;h=h+a|0}}return}function jb(a,c,e,f,g,h,i,j,k,l){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0,v=0,w=0,x=0,y=0,A=0.0,B=0.0,C=0.0,D=0.0,E=0,F=0,G=0.0,H=0.0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0.0;H=6.2831854820251465/+(c|0);G=+t(+H);H=+u(+H);L=c+1>>1;J=a+-1>>1;M=z(e,a)|0;N=z(c,a)|0;I=(a|0)==1;do if(!I){if((f|0)>0){m=0;do{b[k+(m<<2)>>2]=b[i+(m<<2)>>2];m=m+1|0}while((m|0)!=(f|0))}q=(c|0)>1;if(q&(e|0)>0){m=1;o=0;do{o=o+M|0;n=0;p=o;while(1){b[j+(p<<2)>>2]=b[h+(p<<2)>>2];n=n+1|0;if((n|0)==(e|0))break;else p=p+a|0}m=m+1|0}while((m|0)!=(c|0))}m=0-a|0;if((J|0)>(e|0)){if(q){w=(e|0)>0;x=(a|0)>2;q=1;s=0;do{s=s+M|0;m=m+a|0;if(w?(y=m+-1|0,x):0){r=0;v=s-a|0;do{v=v+a|0;n=2;o=v;p=y;do{O=l+(p+1<<2)|0;p=p+2|0;Q=o+1|0;o=o+2|0;E=h+(Q<<2)|0;F=l+(p<<2)|0;P=h+(o<<2)|0;d[j+(Q<<2)>>2]=+d[P>>2]*+d[F>>2]+ +d[E>>2]*+d[O>>2];d[j+(o<<2)>>2]=+d[P>>2]*+d[O>>2]-+d[E>>2]*+d[F>>2];n=n+2|0}while((n|0)<(a|0));r=r+1|0}while((r|0)!=(e|0))}q=q+1|0}while((q|0)!=(c|0))}}else if(q){y=(a|0)<3|(e|0)<1;q=1;s=0;do{m=m+a|0;s=s+M|0;if(!y){n=2;p=m+-1|0;v=s;do{w=p;p=p+2|0;v=v+2|0;w=l+(w+1<<2)|0;x=l+(p<<2)|0;o=v;r=0;while(1){O=o+-1|0;Q=h+(O<<2)|0;P=h+(o<<2)|0;d[j+(O<<2)>>2]=+d[P>>2]*+d[x>>2]+ +d[Q>>2]*+d[w>>2];d[j+(o<<2)>>2]=+d[P>>2]*+d[w>>2]-+d[Q>>2]*+d[x>>2];r=r+1|0;if((r|0)==(e|0))break;else o=o+a|0}n=n+2|0}while((n|0)<(a|0))}q=q+1|0}while((q|0)!=(c|0))}n=z(M,c)|0;m=(c|0)>2;if((J|0)<(e|0)){if(!m)break;x=(a|0)<3|(e|0)<1;v=1;w=0;do{w=w+M|0;n=n-M|0;if(!x){m=n;q=w;r=2;do{q=q+2|0;m=m+2|0;o=m-a|0;p=q-a|0;s=0;do{p=p+a|0;o=o+a|0;O=p+-1|0;Q=j+(O<<2)|0;E=o+-1|0;P=j+(E<<2)|0;d[h+(O<<2)>>2]=+d[P>>2]+ +d[Q>>2];O=j+(p<<2)|0;F=j+(o<<2)|0;d[h+(E<<2)>>2]=+d[O>>2]-+d[F>>2];d[h+(p<<2)>>2]=+d[F>>2]+ +d[O>>2];d[h+(o<<2)>>2]=+d[P>>2]-+d[Q>>2];s=s+1|0}while((s|0)!=(e|0));r=r+2|0}while((r|0)<(a|0))}v=v+1|0}while((v|0)<(L|0))}else{if(!m)break;x=(e|0)<1|(a|0)<3;v=1;w=0;do{w=w+M|0;n=n-M|0;if(!x){m=n;q=w;s=0;while(1){o=m;p=q;r=2;do{O=p+1|0;p=p+2|0;Q=j+(O<<2)|0;E=o+1|0;o=o+2|0;P=j+(E<<2)|0;d[h+(O<<2)>>2]=+d[P>>2]+ +d[Q>>2];O=j+(p<<2)|0;F=j+(o<<2)|0;d[h+(E<<2)>>2]=+d[O>>2]-+d[F>>2];d[h+(p<<2)>>2]=+d[F>>2]+ +d[O>>2];d[h+(o<<2)>>2]=+d[P>>2]-+d[Q>>2];r=r+2|0}while((r|0)<(a|0));s=s+1|0;if((s|0)==(e|0))break;else{m=m+a|0;q=q+a|0}}}v=v+1|0}while((v|0)<(L|0))}}while(0);E=(f|0)>0;if(E){m=0;do{b[i+(m<<2)>>2]=b[k+(m<<2)>>2];m=m+1|0}while((m|0)!=(f|0))}s=z(f,c)|0;F=(c|0)>2;do if(F){if((e|0)>0){o=1;q=0;r=s;do{q=q+M|0;r=r-M|0;m=r-a|0;n=q-a|0;p=0;do{n=n+a|0;m=m+a|0;Q=j+(n<<2)|0;P=j+(m<<2)|0;d[h+(n<<2)>>2]=+d[P>>2]+ +d[Q>>2];d[h+(m<<2)>>2]=+d[P>>2]-+d[Q>>2];p=p+1|0}while((p|0)!=(e|0));o=o+1|0}while((o|0)<(L|0));if(!F)break}l=z(c+-1|0,f)|0;h=(c|0)<5|E^1;x=1;C=1.0;D=0.0;y=0;do{y=y+f|0;s=s-f|0;B=C;C=C*G-D*H;D=B*H+D*G;if(E){m=f;n=0;o=l;p=s;q=y;while(1){d[k+(q<<2)>>2]=+d[i+(m<<2)>>2]*C+ +d[i+(n<<2)>>2];d[k+(p<<2)>>2]=+d[i+(o<<2)>>2]*D;n=n+1|0;if((n|0)==(f|0))break;else{m=m+1|0;o=o+1|0;p=p+1|0;q=q+1|0}}}if(!h){A=C;B=D;r=l;v=f;w=2;do{v=v+f|0;r=r-f|0;R=A;A=A*C-B*D;B=R*D+B*C;m=r;n=v;o=s;p=0;q=y;while(1){Q=k+(q<<2)|0;d[Q>>2]=+d[Q>>2]+ +d[i+(n<<2)>>2]*A;Q=k+(o<<2)|0;d[Q>>2]=+d[Q>>2]+ +d[i+(m<<2)>>2]*B;p=p+1|0;if((p|0)==(f|0))break;else{m=m+1|0;n=n+1|0;o=o+1|0;q=q+1|0}}w=w+1|0}while((w|0)<(L|0))}x=x+1|0}while((x|0)<(L|0));if(F&E){n=1;o=0;do{o=o+f|0;m=0;p=o;while(1){Q=k+(m<<2)|0;d[Q>>2]=+d[Q>>2]+ +d[i+(p<<2)>>2];m=m+1|0;if((m|0)==(f|0))break;else p=p+1|0}n=n+1|0}while((n|0)<(L|0))}}while(0);if((a|0)<(e|0)){if((a|0)>0&(e|0)>0){m=0;do{n=0;o=m;p=m;while(1){b[g+(p<<2)>>2]=b[j+(o<<2)>>2];n=n+1|0;if((n|0)==(e|0))break;else{o=o+a|0;p=p+N|0}}m=m+1|0}while((m|0)!=(a|0))}}else if((e|0)>0&(a|0)>0){p=0;q=0;r=0;while(1){m=0;n=q;o=r;while(1){b[g+(o<<2)>>2]=b[j+(n<<2)>>2];m=m+1|0;if((m|0)==(a|0))break;else{n=n+1|0;o=o+1|0}}p=p+1|0;if((p|0)==(e|0))break;else{q=q+a|0;r=r+N|0}}}E=a<<1;r=z(M,c)|0;if(F&(e|0)>0){m=0;q=0;s=r;w=1;do{m=m+E|0;q=q+M|0;s=s-M|0;n=s;o=q;p=m;v=0;while(1){b[g+(p+-1<<2)>>2]=b[j+(o<<2)>>2];b[g+(p<<2)>>2]=b[j+(n<<2)>>2];v=v+1|0;if((v|0)==(e|0))break;else{n=n+a|0;o=o+a|0;p=p+N|0}}w=w+1|0}while((w|0)<(L|0))}if(I)return;m=0-a|0;if((J|0)>=(e|0)){if(!F)return;l=(e|0)<1|(a|0)<3;v=0;w=0;y=1;do{m=m+E|0;v=v+E|0;w=w+M|0;r=r-M|0;if(!l){n=r;o=w;p=v;q=m;x=0;while(1){s=2;do{P=s+o|0;I=j+(P+-1<<2)|0;O=s+n|0;J=j+(O+-1<<2)|0;K=s+p|0;d[g+(K+-1<<2)>>2]=+d[J>>2]+ +d[I>>2];Q=a-s+q|0;d[g+(Q+-1<<2)>>2]=+d[I>>2]-+d[J>>2];P=j+(P<<2)|0;O=j+(O<<2)|0;d[g+(K<<2)>>2]=+d[O>>2]+ +d[P>>2];d[g+(Q<<2)>>2]=+d[O>>2]-+d[P>>2];s=s+2|0}while((s|0)<(a|0));x=x+1|0;if((x|0)==(e|0))break;else{n=n+a|0;o=o+a|0;p=p+N|0;q=q+N|0}}}y=y+1|0}while((y|0)<(L|0));return}if(!F)return;l=(a|0)>2;h=(e|0)>0;y=1;w=0;x=0;do{m=m+E|0;w=w+E|0;x=x+M|0;r=r-M|0;if(l?(K=m+a|0,h):0){v=2;do{n=0;o=v+r|0;p=v+x|0;q=v+w|0;s=K-v|0;while(1){P=j+(p+-1<<2)|0;Q=j+(o+-1<<2)|0;d[g+(q+-1<<2)>>2]=+d[Q>>2]+ +d[P>>2];d[g+(s+-1<<2)>>2]=+d[P>>2]-+d[Q>>2];Q=j+(p<<2)|0;P=j+(o<<2)|0;d[g+(q<<2)>>2]=+d[P>>2]+ +d[Q>>2];d[g+(s<<2)>>2]=+d[P>>2]-+d[Q>>2];n=n+1|0;if((n|0)==(e|0))break;else{o=o+a|0;p=p+a|0;q=q+N|0;s=s+N|0}}v=v+2|0}while((v|0)<(a|0))}y=y+1|0}while((y|0)<(L|0));return}function kb(a,c){a=a|0;c=c|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0.0,l=0,m=0,n=0,o=0.0,p=0,q=0,r=0.0,s=0,v=0,w=0,x=0.0;b[a>>2]=c;e=gc(c*3|0,4)|0;b[a+4>>2]=e;w=gc(32,4)|0;b[a+8>>2]=w;if((c|0)==1)return;v=e+(c<<2)|0;l=w+8|0;a=0;e=c;f=0;g=-1;a:while(1){do{s=g;g=g+1|0;if((s|0)<3)f=b[56048+(g<<2)>>2]|0;else f=f+2|0;h=(e|0)/(f|0)|0}while((e|0)!=(z(h,f)|0));if((f|0)==2){e=h;while(1){j=a+1|0;b[w+(a+2<<2)>>2]=2;i=(a|0)==0;if(!i){if((a|0)>=1){h=1;do{s=j-h|0;b[w+(s+2<<2)>>2]=b[w+(s+1<<2)>>2];h=h+1|0}while((h|0)!=(j|0))}b[l>>2]=2}if((e|0)==1){s=a;e=i;a=j;break a}a=(e|0)/2|0;if((e|0)==(a<<1|0)){e=a;a=j}else{a=j;break}}}else{i=a;e=h;while(1){a=i+1|0;b[w+(i+2<<2)>>2]=f;if((e|0)==1){m=14;break a}h=(e|0)/(f|0)|0;if((e|0)==(z(h,f)|0)){i=a;e=h}else break}}}if((m|0)==14){s=i;e=(i|0)==0}b[w>>2]=c;b[w+4>>2]=a;r=6.2831854820251465/+(c|0);if(!((s|0)>0&(e^1)))return;p=0;a=0;q=1;do{n=b[w+(p+2<<2)>>2]|0;e=q;q=z(n,q)|0;l=(c|0)/(q|0)|0;m=n+-1|0;do if((n|0)>1){n=z(l,m)|0;if((l|0)<=2){a=n+a|0;break}g=0;i=0;j=a;while(1){g=g+e|0;o=r*+(g|0);k=0.0;f=2;h=j;while(1){k=k+1.0;x=o*k;d[v+(h<<2)>>2]=+t(+x);d[v+(h+1<<2)>>2]=+u(+x);f=f+2|0;if((f|0)>=(l|0))break;else h=h+2|0}i=i+1|0;if((i|0)==(m|0))break;else j=j+l|0}a=n+a|0}while(0);p=p+1|0}while((p|0)!=(s|0));return}function lb(a){a=a|0;var c=0;if(!a)return;c=b[a+4>>2]|0;if(c|0)fc(c);c=b[a+8>>2]|0;if(c|0)fc(c);b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;return}function mb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=a+836|0;l=b[a+840>>2]|0;Zb(c,b[a>>2]|0,5);if((b[a>>2]|0)>0){e=a+4|0;d=0;k=-1;do{j=e+(d<<2)|0;Zb(c,b[j>>2]|0,4);j=b[j>>2]|0;k=(k|0)<(j|0)?j:k;d=d+1|0}while((d|0)<(b[a>>2]|0));if((k|0)>=0){f=a+128|0;g=a+192|0;h=a+256|0;i=a+320|0;e=0;while(1){Zb(c,(b[f+(e<<2)>>2]|0)+-1|0,3);j=g+(e<<2)|0;Zb(c,b[j>>2]|0,2);if(!((b[j>>2]|0)!=0?(Zb(c,b[h+(e<<2)>>2]|0,8),(b[j>>2]|0)==31):0)){d=0;do{Zb(c,(b[i+(e<<5)+(d<<2)>>2]|0)+1|0,8);d=d+1|0}while((d|0)<(1<<b[j>>2]|0))}if((e|0)==(k|0))break;else e=e+1|0}}}Zb(c,(b[a+832>>2]|0)+-1|0,2);h=l+-1|0;Zb(c,Sa(h)|0,4);h=Sa(h)|0;e=b[a>>2]|0;if((e|0)<=0)return;i=a+128|0;j=a+4|0;f=0;d=0;g=0;do{f=(b[i+(b[j+(g<<2)>>2]<<2)>>2]|0)+f|0;if((d|0)<(f|0)){do{Zb(c,b[m+(d+2<<2)>>2]|0,h);d=d+1|0}while((d|0)!=(f|0));d=f;e=b[a>>2]|0}g=g+1|0}while((g|0)<(e|0));return}function nb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;q=L;L=L+272|0;p=q;f=b[a+28>>2]|0;o=gc(1,1120)|0;l=cc(c,5)|0;b[o>>2]=l;a:do if((l|0)>0){d=o+4|0;a=0;l=-1;do{e=cc(c,4)|0;b[d+(a<<2)>>2]=e;if((e|0)<0)break a;l=(l|0)<(e|0)?e:l;a=a+1|0}while((a|0)<(b[o>>2]|0));if((l|0)>=0){i=o+128|0;j=o+192|0;k=o+256|0;f=f+24|0;g=o+320|0;e=0;while(1){b[i+(e<<2)>>2]=(cc(c,3)|0)+1;d=cc(c,2)|0;h=j+(e<<2)|0;b[h>>2]=d;if((d|0)<0)break a;a=k+(e<<2)|0;if(!d)a=b[a>>2]|0;else{d=cc(c,8)|0;b[a>>2]=d;a=d}if((a|0)<0)break a;if((a|0)>=(b[f>>2]|0))break a;if((b[h>>2]|0)!=31){a=0;do{d=cc(c,8)|0;b[g+(e<<5)+(a<<2)>>2]=d+-1;if((d|0)<0)break a;a=a+1|0;if((d|0)>(b[f>>2]|0))break a}while((a|0)<(1<<b[h>>2]|0))}if((e|0)<(l|0))e=e+1|0;else{m=19;break}}}else m=19}else m=19;while(0);b:do if((m|0)==19?(b[o+832>>2]=(cc(c,2)|0)+1,n=cc(c,4)|0,(n|0)>=0):0){g=b[o>>2]|0;if((g|0)>0){i=o+128|0;j=o+4|0;d=o+836|0;a=1<<n;f=0;e=0;h=0;do{f=(b[i+(b[j+(h<<2)>>2]<<2)>>2]|0)+f|0;if((f|0)>63)break b;if((e|0)<(f|0)){do{m=cc(c,n)|0;b[d+(e+2<<2)>>2]=m;if(!((m|0)>-1&(m|0)<(a|0)))break b;e=e+1|0}while((e|0)<(f|0));g=b[o>>2]|0}h=h+1|0}while((h|0)<(g|0))}else{f=0;d=o+836|0;a=1<<n}b[d>>2]=0;b[o+840>>2]=a;e=f+2|0;c:do if((f|0)>-2){a=0;do{b[p+(a<<2)>>2]=d+(a<<2);a=a+1|0}while((a|0)<(e|0));nc(p,e,4,13);if((f|0)>-1){a=1;d=b[b[p>>2]>>2]|0;while(1){n=d;d=b[b[p+(a<<2)>>2]>>2]|0;a=a+1|0;if((n|0)==(d|0))break;if((a|0)>=(e|0))break c}if(!o)a=0;else break b;L=q;return a|0}}else nc(p,e,4,13);while(0);p=o;L=q;return p|0}while(0);fc(o);p=0;L=q;return p|0}function ob(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;s=L;L=L+272|0;h=s;p=gc(1,1312)|0;b[p+1296>>2]=c;q=c+836|0;r=p+1288|0;b[r>>2]=b[c+840>>2];f=b[c>>2]|0;if((f|0)>0){d=c+128|0;e=c+4|0;g=0;a=0;do{g=(b[d+(b[e+(a<<2)>>2]<<2)>>2]|0)+g|0;a=a+1|0}while((a|0)<(f|0));a=g+2|0;b[p+1284>>2]=a;if((g|0)>-2){f=a;i=7}else nc(h,a,4,13)}else{b[p+1284>>2]=2;g=0;f=2;i=7}if((i|0)==7){a=0;do{b[h+(a<<2)>>2]=q+(a<<2);a=a+1|0}while((a|0)<(f|0));nc(h,f,4,13);d=q;e=p+260|0;a=0;do{b[e+(a<<2)>>2]=(b[h+(a<<2)>>2]|0)-d>>2;a=a+1|0}while((a|0)<(f|0));d=p+520|0;e=p+260|0;a=0;do{b[d+(b[e+(a<<2)>>2]<<2)>>2]=a;a=a+1|0}while((a|0)<(f|0));d=p+260|0;a=0;do{b[p+(a<<2)>>2]=b[q+(b[d+(a<<2)>>2]<<2)>>2];a=a+1|0}while((a|0)<(f|0))}switch(b[c+832>>2]|0){case 1:{a=256;i=19;break}case 2:{a=128;i=19;break}case 3:{a=86;i=19;break}case 4:{a=64;i=19;break}default:{}}if((i|0)==19)b[p+1292>>2]=a;if((g|0)<=0){L=s;return p|0}i=p+1032|0;j=p+780|0;c=0;m=2;while(1){k=b[q+(c+2<<2)>>2]|0;a=b[r>>2]|0;d=0;e=1;f=0;h=0;while(1){l=b[q+(h<<2)>>2]|0;n=(l|0)>(d|0)&(l|0)<(k|0);f=n?h:f;o=(l|0)<(a|0)&(l|0)>(k|0);e=o?h:e;h=h+1|0;if((h|0)==(m|0))break;else{a=o?l:a;d=n?l:d}}b[i+(c<<2)>>2]=f;b[j+(c<<2)>>2]=e;c=c+1|0;if((c|0)==(g|0))break;else m=m+1|0}L=s;return p|0}function pb(a){a=a|0;if(a|0)fc(a);return}function qb(a){a=a|0;if(a|0)fc(a);return}function rb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;q=b[c+1296>>2]|0;m=b[(b[(b[(b[a+64>>2]|0)+4>>2]|0)+28>>2]|0)+2848>>2]|0;n=a+4|0;if((cc(n,1)|0)!=1){q=0;return q|0}p=c+1284|0;a=ea(a,b[p>>2]<<2)|0;o=c+1292|0;b[a>>2]=cc(n,Sa((b[o>>2]|0)+-1|0)|0)|0;b[a+4>>2]=cc(n,Sa((b[o>>2]|0)+-1|0)|0)|0;a:do if((b[q>>2]|0)>0){h=0;i=2;b:while(1){j=b[q+4+(h<<2)>>2]|0;k=b[q+128+(j<<2)>>2]|0;l=b[q+192+(j<<2)>>2]|0;e=1<<l;if(l){d=Ma(m+((b[q+256+(j<<2)>>2]|0)*56|0)|0,n)|0;if((d|0)==-1){a=0;d=26;break}}else d=0;if((k|0)>0){g=e+-1|0;f=0;do{e=b[q+320+(j<<5)+((d&g)<<2)>>2]|0;d=d>>l;if((e|0)>-1){e=Ma(m+(e*56|0)|0,n)|0;b[a+(f+i<<2)>>2]=e;if((e|0)==-1){a=0;d=26;break b}}else b[a+(f+i<<2)>>2]=0;f=f+1|0}while((f|0)<(k|0))}h=h+1|0;if((h|0)>=(b[q>>2]|0))break a;else i=k+i|0}if((d|0)==26)return a|0}while(0);if((b[p>>2]|0)<=2){q=a;return q|0}l=c+1032|0;h=c+780|0;g=2;do{j=g+-2|0;i=l+(j<<2)|0;f=b[i>>2]|0;d=b[q+836+(f<<2)>>2]|0;j=h+(j<<2)|0;e=b[j>>2]|0;f=b[a+(f<<2)>>2]&32767;k=(b[a+(e<<2)>>2]&32767)-f|0;d=(z((k|0)>-1?k:0-k|0,(b[q+836+(g<<2)>>2]|0)-d|0)|0)/((b[q+836+(e<<2)>>2]|0)-d|0)|0;f=((k|0)<0?0-d|0:d)+f|0;d=(b[o>>2]|0)-f|0;k=a+(g<<2)|0;e=b[k>>2]|0;if(!e)b[k>>2]=f|32768;else{do if((e|0)<(((d|0)<(f|0)?d:f)<<1|0))if(!(e&1)){d=e>>1;break}else{d=0-(e+1>>1)|0;break}else if((d|0)>(f|0)){d=e-f|0;break}else{d=~(e-d);break}while(0);b[k>>2]=d+f&32767;c=a+(b[i>>2]<<2)|0;b[c>>2]=b[c>>2]&32767;c=a+(b[j>>2]<<2)|0;b[c>>2]=b[c>>2]&32767}g=g+1|0}while((g|0)<(b[p>>2]|0));return a|0}function sb(a,c,e,f){a=a|0;c=c|0;e=e|0;f=f|0;var g=0,h=0.0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;v=b[c+1296>>2]|0;w=(b[(b[(b[(b[a+64>>2]|0)+4>>2]|0)+28>>2]|0)+(b[a+28>>2]<<2)>>2]|0)/2|0;if(!e){Fc(f|0,0,w<<2|0)|0;f=0;return f|0}t=b[v+832>>2]|0;g=z(t,b[e>>2]|0)|0;g=(g|0)<255?g:255;g=(g|0)>0?g:0;u=b[c+1284>>2]|0;if((u|0)>1){s=c+260|0;j=0;a=0;r=1;while(1){c=b[s+(r<<2)>>2]|0;i=b[e+(c<<2)>>2]|0;if((i&32767|0)==(i|0)){q=b[v+836+(c<<2)>>2]|0;k=z(i,t)|0;k=(k|0)<255?k:255;k=(k|0)>0?k:0;p=k-g|0;l=q-j|0;m=(p|0)/(l|0)|0;n=p>>31|1;o=z(m,l)|0;o=((p|0)>-1?p:0-p|0)-((o|0)>-1?o:0-o|0)|0;p=(w|0)>(q|0)?q:w;a=f+(j<<2)|0;if((p|0)>(j|0))d[a>>2]=+d[a>>2]*+d[56064+(g<<2)>>2];c=j+1|0;if((c|0)<(p|0)){a=0;while(1){a=a+o|0;i=(a|0)<(l|0);g=g+m+(i?0:n)|0;j=f+(c<<2)|0;d[j>>2]=+d[j>>2]*+d[56064+(g<<2)>>2];c=c+1|0;if((c|0)>=(p|0)){g=k;c=q;a=q;break}else a=a-(i?0:l)|0}}else{g=k;c=q;a=q}}else c=j;r=r+1|0;if((r|0)>=(u|0))break;else j=c}}else a=0;if((a|0)>=(w|0)){f=1;return f|0}h=+d[56064+(g<<2)>>2];do{v=f+(a<<2)|0;d[v>>2]=+d[v>>2]*h;a=a+1|0}while((a|0)!=(w|0));a=1;return a|0}function tb(a,c){a=a|0;c=c|0;return (b[b[a>>2]>>2]|0)-(b[b[c>>2]>>2]|0)|0}function ub(a,c,e,f){a=a|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0.0,o=0,p=0.0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0.0,y=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,fa=0;da=L;L=L+4976|0;S=da+1360|0;ba=da+1088|0;ca=da+816|0;T=da+544|0;U=da+272|0;V=da;J=da+4964|0;K=da+4960|0;O=da+4956|0;P=da+4952|0;Q=da+4948|0;R=da+4944|0;$=b[c+1296>>2]|0;I=b[c+1288>>2]|0;aa=b[c+1284>>2]|0;h=(aa|0)>0;if(h){g=0;do{b[ba+(g<<2)>>2]=-200;g=g+1|0}while((g|0)!=(aa|0));if(h){g=0;do{b[ca+(g<<2)>>2]=-200;g=g+1|0}while((g|0)!=(aa|0));if(h){Fc(T|0,0,aa<<2|0)|0;g=0;do{b[U+(g<<2)>>2]=1;g=g+1|0}while((g|0)!=(aa|0));if(h){Fc(V|0,-1,aa<<2|0)|0;D=aa+-1|0;if((aa|0)==1){ca=0;L=da;return ca|0}E=I+-1|0;F=$+1112|0;g=0;C=0;G=b[c>>2]|0;do{A=C;C=C+1|0;k=G;G=b[c+(C<<2)>>2]|0;B=S+(A*56|0)+8|0;h=B;i=h+48|0;do{b[h>>2]=0;h=h+4|0}while((h|0)<(i|0));b[S+(A*56|0)>>2]=k;b[S+(A*56|0)+4>>2]=G;y=(G|0)<(I|0)?G:E;if((y|0)<(k|0)){u=0;t=0;s=0;r=0;q=0;o=0;m=0;l=0;k=0;j=0;i=0;h=0}else{u=0;t=0;s=0;r=0;q=0;o=0;m=0;l=0;w=0;j=0;i=0;h=0;while(1){n=+d[f+(k<<2)>>2];v=~~(n*7.314285755157471+1023.5);v=(v|0)>0?v:0;v=(v|0)<1023?v:1023;do if(v)if(!(+d[F>>2]+ +d[e+(k<<2)>>2]>=n)){i=i+1|0;h=(z(v,k)|0)+h|0;u=(z(v,v)|0)+u|0;t=(z(k,k)|0)+t|0;s=v+s|0;r=k+r|0;break}else{q=q+1|0;o=(z(v,k)|0)+o|0;m=(z(v,v)|0)+m|0;l=(z(k,k)|0)+l|0;w=v+w|0;j=k+j|0;break}while(0);if((k|0)<(y|0))k=k+1|0;else{k=w;break}}}b[B>>2]=j;b[S+(A*56|0)+12>>2]=k;b[S+(A*56|0)+16>>2]=l;b[S+(A*56|0)+20>>2]=m;b[S+(A*56|0)+24>>2]=o;b[S+(A*56|0)+28>>2]=q;b[S+(A*56|0)+32>>2]=r;b[S+(A*56|0)+36>>2]=s;b[S+(A*56|0)+40>>2]=t;b[S+(A*56|0)+44>>2]=u;b[S+(A*56|0)+48>>2]=h;b[S+(A*56|0)+52>>2]=i;g=q+g|0}while((C|0)!=(D|0))}else W=11}else W=11}else W=11}else W=11;if((W|0)==11){if(aa|0){ca=0;L=da;return ca|0}y=S+8|0;h=y;i=h+48|0;do{b[h>>2]=0;h=h+4|0}while((h|0)<(i|0));b[S>>2]=0;b[S+4>>2]=I;if((I|0)<1){t=0;s=0;r=0;q=0;g=0;o=0;m=0;l=0;k=0;j=0;i=0;h=0}else{w=$+1112|0;t=0;s=0;r=0;q=0;g=0;o=0;m=0;l=0;k=0;j=0;v=0;i=0;h=0;do{n=+d[f+(v<<2)>>2];u=~~(n*7.314285755157471+1023.5);u=(u|0)>0?u:0;u=(u|0)<1023?u:1023;do if(u)if(!(+d[w>>2]+ +d[e+(v<<2)>>2]>=n)){i=i+1|0;h=(z(u,v)|0)+h|0;t=(z(u,u)|0)+t|0;s=(z(v,v)|0)+s|0;r=u+r|0;q=v+q|0;break}else{g=g+1|0;o=(z(u,v)|0)+o|0;m=(z(u,u)|0)+m|0;l=(z(v,v)|0)+l|0;k=u+k|0;j=v+j|0;break}while(0);v=v+1|0}while((v|0)!=(I|0))}b[y>>2]=j;b[S+12>>2]=k;b[S+16>>2]=l;b[S+20>>2]=m;b[S+24>>2]=o;b[S+28>>2]=g;b[S+32>>2]=q;b[S+36>>2]=r;b[S+40>>2]=s;b[S+44>>2]=t;b[S+48>>2]=h;b[S+52>>2]=i}if(!g){ca=0;L=da;return ca|0}b[J>>2]=-200;b[K>>2]=-200;vb(S,aa+-1|0,J,K,$)|0;h=b[J>>2]|0;b[ba>>2]=h;b[ca>>2]=h;g=b[K>>2]|0;K=ca+4|0;b[K>>2]=g;M=ba+4|0;b[M>>2]=g;N=(aa|0)>2;do if(N){E=$+1112|0;F=$+1096|0;G=$+1100|0;I=$+1104|0;D=2;a:while(1){B=b[c+520+(D<<2)>>2]|0;J=b[T+(B<<2)>>2]|0;C=b[U+(B<<2)>>2]|0;g=V+(J<<2)|0;b:do if((b[g>>2]|0)!=(C|0)){t=b[c+520+(J<<2)>>2]|0;u=b[c+520+(C<<2)>>2]|0;b[g>>2]=C;i=b[$+836+(J<<2)>>2]|0;m=b[$+836+(C<<2)>>2]|0;w=b[ba+(J<<2)>>2]|0;y=ca+(J<<2)|0;A=b[y>>2]|0;w=(w|0)<0?A:(A|0)<0?w:A+w>>1;A=ba+(C<<2)|0;v=b[A>>2]|0;s=b[ca+(C<<2)>>2]|0;v=(v|0)<0?s:(s|0)<0?v:s+v>>1;if((w|0)==-1|(v|0)==-1){W=35;break a}g=v-w|0;o=m-i|0;q=(g|0)/(o|0)|0;r=g>>31|1;p=+d[f+(i<<2)>>2];h=~~(p*7.314285755157471+1023.5);h=(h|0)>0?h:0;h=(h|0)<1023?h:1023;s=z(q,o)|0;s=((g|0)>-1?g:0-g|0)-((s|0)>-1?s:0-s|0)|0;g=w-h|0;g=z(g,g)|0;x=+d[E>>2];if(x+ +d[e+(i<<2)>>2]>=p){n=+(w|0);p=+(h|0);if(!(+d[F>>2]+n<p)?!(n-+d[G>>2]>p):0)W=39}else W=39;c:do if((W|0)==39){W=0;i=i+1|0;if((i|0)<(m|0)){h=1;k=0;l=w;do{fa=k+s|0;j=(fa|0)<(o|0);k=fa-(j?0:o)|0;l=l+q+(j?0:r)|0;p=+d[f+(i<<2)>>2];j=~~(p*7.314285755157471+1023.5);j=(j|0)>0?j:0;j=(j|0)<1023?j:1023;fa=l-j|0;g=(z(fa,fa)|0)+g|0;h=h+1|0;if(j|0?+d[e+(i<<2)>>2]+x>=p:0){p=+(l|0);n=+(j|0);if(+d[F>>2]+p<n)break c;if(p-+d[G>>2]>n)break c}i=i+1|0}while((i|0)<(m|0))}else h=1;x=+d[F>>2];n=+(h|0);p=+d[I>>2];if((!(x*x/n>p)?(x=+d[G>>2],!(x*x/n>p)):0)?p<+((g|0)/(h|0)|0|0):0)break;b[ba+(D<<2)>>2]=-200;b[ca+(D<<2)>>2]=-200;break b}while(0);b[O>>2]=-200;b[P>>2]=-200;b[Q>>2]=-200;b[R>>2]=-200;h=vb(S+(t*56|0)|0,B-t|0,O,P,$)|0;g=vb(S+(B*56|0)|0,u-B|0,Q,R,$)|0;h=(h|0)!=0;if(h){b[O>>2]=w;b[P>>2]=b[Q>>2]}if((g|0)!=0?(b[Q>>2]=b[P>>2],b[R>>2]=v,h):0){b[ba+(D<<2)>>2]=-200;b[ca+(D<<2)>>2]=-200}else W=53;d:do if((W|0)==53){W=0;g=b[O>>2]|0;b[y>>2]=g;if(!J)b[ba>>2]=g;g=b[P>>2]|0;b[ba+(D<<2)>>2]=g;h=b[Q>>2]|0;b[ca+(D<<2)>>2]=h;i=b[R>>2]|0;b[A>>2]=i;if((C|0)==1)b[K>>2]=i;if((h&g|0)>-1){e:do if((B|0)>0){g=B;do{h=g;g=g+-1|0;i=U+(g<<2)|0;if((b[i>>2]|0)!=(C|0))break e;b[i>>2]=D}while((h|0)>1)}while(0);g=B+1|0;if((g|0)<(aa|0))do{h=T+(g<<2)|0;if((b[h>>2]|0)!=(J|0))break d;b[h>>2]=D;g=g+1|0}while((g|0)<(aa|0))}}while(0)}while(0);D=D+1|0;if((D|0)>=(aa|0)){W=69;break}}if((W|0)==35)H(1);else if((W|0)==69){X=b[ba>>2]|0;Y=b[ca>>2]|0;Z=b[M>>2]|0;_=b[K>>2]|0;break}}else{X=h;Y=h;Z=g;_=g}while(0);h=ea(a,aa<<2)|0;b[h>>2]=(X|0)<0?Y:(Y|0)<0?X:Y+X>>1;b[h+4>>2]=(Z|0)<0?_:(_|0)<0?Z:_+Z>>1;if(N){g=2;do{Y=g+-2|0;_=b[c+1032+(Y<<2)>>2]|0;Y=b[c+780+(Y<<2)>>2]|0;fa=b[$+836+(_<<2)>>2]|0;_=b[h+(_<<2)>>2]&32767;Z=(b[h+(Y<<2)>>2]&32767)-_|0;fa=(z((Z|0)>-1?Z:0-Z|0,(b[$+836+(g<<2)>>2]|0)-fa|0)|0)/((b[$+836+(Y<<2)>>2]|0)-fa|0)|0;_=((Z|0)<0?0-fa|0:fa)+_|0;fa=b[ba+(g<<2)>>2]|0;Z=b[ca+(g<<2)>>2]|0;fa=(fa|0)<0?Z:(Z|0)<0?fa:Z+fa>>1;b[h+(g<<2)>>2]=(fa|0)<0|(_|0)==(fa|0)?_|32768:fa;g=g+1|0}while((g|0)!=(aa|0))}fa=h;L=da;return fa|0}function vb(a,c,e,f,g){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0.0,i=0.0,j=0.0,k=0.0,l=0.0,m=0.0,n=0,o=0,p=0,q=0.0,r=0;n=b[a>>2]|0;o=b[a+((c+-1|0)*56|0)+4>>2]|0;if((c|0)>0){m=+d[g+1108>>2];i=0.0;l=0.0;k=0.0;j=0.0;g=0;h=0.0;do{r=b[a+(g*56|0)+52>>2]|0;p=b[a+(g*56|0)+28>>2]|0;q=m*+(p+r|0)/+(p+1|0)+1.0;i=i+ +(b[a+(g*56|0)+32>>2]|0)+q*+(b[a+(g*56|0)+8>>2]|0);l=l+ +(b[a+(g*56|0)+36>>2]|0)+q*+(b[a+(g*56|0)+12>>2]|0);k=k+ +(b[a+(g*56|0)+40>>2]|0)+q*+(b[a+(g*56|0)+16>>2]|0);j=j+ +(b[a+(g*56|0)+48>>2]|0)+q*+(b[a+(g*56|0)+24>>2]|0);h=h+ +(r|0)+q*+(p|0);g=g+1|0}while((g|0)!=(c|0))}else{i=0.0;l=0.0;k=0.0;j=0.0;h=0.0}g=b[e>>2]|0;if((g|0)>-1){i=i+ +(n|0);l=l+ +(g|0);k=k+ +(z(n,n)|0);j=j+ +(z(g,n)|0);h=h+1.0}g=b[f>>2]|0;if((g|0)>-1){m=i+ +(o|0);l=l+ +(g|0);k=k+ +(z(o,o)|0);j=j+ +(z(g,o)|0);h=h+1.0}else m=i;i=k*h-m*m;if(i>0.0){q=(l*k-m*j)/i;m=(j*h-m*l)/i;b[e>>2]=~~+Dc(+(m*+(n|0)+q));g=~~+Dc(+(m*+(o|0)+q));b[f>>2]=g;a=b[e>>2]|0;if((a|0)>1023){b[e>>2]=1023;g=b[f>>2]|0;a=1023}if((g|0)>1023){b[f>>2]=1023;a=b[e>>2]|0;g=1023}if((a|0)<0){b[e>>2]=0;g=b[f>>2]|0}if((g|0)<0)g=0;else{r=0;return r|0}}else{b[e>>2]=0;g=1}b[f>>2]=0;r=g;return r|0}function wb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;j=b[c+1284>>2]|0;if(!((d|0)!=0&(e|0)!=0)){f=0;return f|0}c=ea(a,j<<2)|0;if((j|0)<=0){f=c;return f|0}g=65536-f|0;a=0;do{l=d+(a<<2)|0;h=z(b[l>>2]&32767,g)|0;k=e+(a<<2)|0;h=h+32768+(z(b[k>>2]&32767,f)|0)>>16;i=c+(a<<2)|0;b[i>>2]=h;if(b[l>>2]&32768|0?b[k>>2]&32768|0:0)b[i>>2]=h|32768;a=a+1|0}while((a|0)!=(j|0));return c|0}function xb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,A=0,B=0,C=0;C=L;L=L+336|0;v=C+64|0;w=C+32|0;x=C;B=b[d+1296>>2]|0;A=d+1284|0;q=b[A>>2]|0;y=b[(b[(b[c+64>>2]|0)+4>>2]|0)+28>>2]|0;u=b[y+2848>>2]|0;if(!e){Zb(a,0,1);Fc(f|0,0,((b[c+36>>2]|0)/2|0)<<2|0)|0;f=0;L=C;return f|0}a:do if((q|0)>0){i=B+832|0;h=0;while(1){j=e+(h<<2)|0;k=b[j>>2]|0;g=k&32767;switch(b[i>>2]|0){case 1:{g=g>>>2;break}case 2:{g=g>>>3;break}case 3:{g=(g>>>0)/12|0;break}case 4:{g=g>>>4;break}default:{}}b[j>>2]=g|k&32768;h=h+1|0;if((h|0)==(q|0))break a}}while(0);h=b[e>>2]|0;b[v>>2]=h;g=b[e+4>>2]|0;o=v+4|0;b[o>>2]=g;p=d+1292|0;if((q|0)>2){k=2;do{j=k+-2|0;l=b[d+1032+(j<<2)>>2]|0;j=b[d+780+(j<<2)>>2]|0;i=b[B+836+(l<<2)>>2]|0;l=e+(l<<2)|0;m=e+(j<<2)|0;n=b[l>>2]&32767;g=(b[m>>2]&32767)-n|0;i=(z((g|0)>-1?g:0-g|0,(b[B+836+(k<<2)>>2]|0)-i|0)|0)/((b[B+836+(j<<2)>>2]|0)-i|0)|0;i=((g|0)<0?0-i|0:i)+n|0;g=e+(k<<2)|0;j=b[g>>2]|0;if((j&32768|0)!=0|(j|0)==(i|0)){b[g>>2]=i|32768;b[v+(k<<2)>>2]=0}else{h=(b[p>>2]|0)-i|0;h=(h|0)<(i|0)?h:i;g=j-i|0;do if((g|0)<0)if((g|0)<(0-h|0)){g=h+~g|0;break}else{g=~(g<<1);break}else if((h|0)>(g|0)){g=g<<1;break}else{g=h+g|0;break}while(0);b[v+(k<<2)>>2]=g;b[l>>2]=n;b[m>>2]=b[m>>2]&32767}k=k+1|0}while((k|0)!=(q|0));h=b[v>>2]|0;g=b[o>>2]|0}Zb(a,1,1);r=d+1308|0;b[r>>2]=(b[r>>2]|0)+1;r=(Sa((b[p>>2]|0)+-1|0)|0)<<1;s=d+1304|0;b[s>>2]=(b[s>>2]|0)+r;Zb(a,h,Sa((b[p>>2]|0)+-1|0)|0);Zb(a,g,Sa((b[p>>2]|0)+-1|0)|0);if((b[B>>2]|0)>0){p=d+1300|0;n=2;o=0;while(1){q=b[B+4+(o<<2)>>2]|0;r=b[B+128+(q<<2)>>2]|0;l=b[B+192+(q<<2)>>2]|0;m=1<<l;b[w>>2]=0;b[w+4>>2]=0;b[w+8>>2]=0;b[w+12>>2]=0;b[w+16>>2]=0;b[w+20>>2]=0;b[w+24>>2]=0;b[w+28>>2]=0;if(l|0){b[x>>2]=0;b[x+4>>2]=0;b[x+8>>2]=0;b[x+12>>2]=0;b[x+16>>2]=0;b[x+20>>2]=0;b[x+24>>2]=0;b[x+28>>2]=0;i=(l|0)==31;if(!i){h=0;do{g=b[B+320+(q<<5)+(h<<2)>>2]|0;if((g|0)<0)g=1;else g=b[(b[y+1824+(g<<2)>>2]|0)+4>>2]|0;b[x+(h<<2)>>2]=g;h=h+1|0}while((h|0)<(m|0))}b:do if((r|0)>0){if(i){h=0;g=0;i=0;while(1){g=b[w+(i<<2)>>2]<<h|g;i=i+1|0;if((i|0)==(r|0))break b;else h=h+31|0}}j=0;g=0;k=0;while(1){i=b[v+(k+n<<2)>>2]|0;h=0;while(1){if((i|0)<(b[x+(h<<2)>>2]|0)){i=39;break}h=h+1|0;if((h|0)>=(m|0)){i=40;break}}if((i|0)==39)b[w+(k<<2)>>2]=h;else if((i|0)==40)h=b[w+(k<<2)>>2]|0;g=h<<j|g;k=k+1|0;if((k|0)==(r|0))break;else j=j+l|0}}else g=0;while(0);m=La(u+((b[B+256+(q<<2)>>2]|0)*56|0)|0,g,a)|0;b[p>>2]=(b[p>>2]|0)+m}if((r|0)>0){g=0;do{h=b[B+320+(q<<5)+(b[w+(g<<2)>>2]<<2)>>2]|0;if((h|0)>-1?(t=b[v+(g+n<<2)>>2]|0,(t|0)<(b[u+(h*56|0)+4>>2]|0)):0){m=La(u+(h*56|0)|0,t,a)|0;b[s>>2]=(b[s>>2]|0)+m}g=g+1|0}while((g|0)!=(r|0))}o=o+1|0;if((o|0)>=(b[B>>2]|0))break;else n=r+n|0}}t=B+832|0;i=z(b[t>>2]|0,b[e>>2]|0)|0;a=(b[y+(b[c+28>>2]<<2)>>2]|0)/2|0;if((b[A>>2]|0)>1){k=0;g=0;s=1;while(1){j=b[d+260+(s<<2)>>2]|0;h=b[e+(j<<2)>>2]|0;if((h&32767|0)==(h|0)){q=z(b[t>>2]|0,h)|0;r=b[B+836+(j<<2)>>2]|0;p=q-i|0;l=r-k|0;m=(p|0)/(l|0)|0;n=p>>31|1;o=z(m,l)|0;o=((p|0)>-1?p:0-p|0)-((o|0)>-1?o:0-o|0)|0;p=(a|0)>(r|0)?r:a;if((p|0)>(k|0))b[f+(k<<2)>>2]=i;h=k+1|0;if((h|0)<(p|0)){g=0;while(1){g=g+o|0;j=(g|0)<(l|0);i=i+m+(j?0:n)|0;b[f+(h<<2)>>2]=i;h=h+1|0;if((h|0)>=(p|0)){i=q;h=r;g=r;break}else g=g-(j?0:l)|0}}else{i=q;h=r;g=r}}else h=k;s=s+1|0;if((s|0)>=(b[A>>2]|0))break;else k=h}}else g=0;h=c+36|0;if((g|0)>=((b[h>>2]|0)/2|0|0)){f=1;L=C;return f|0}do{b[f+(g<<2)>>2]=i;g=g+1|0}while((g|0)<((b[h>>2]|0)/2|0|0));g=1;L=C;return g|0}function yb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;i=b[a+28>>2]|0;a=ec(96)|0;e=cc(c,8)|0;b[a>>2]=e;g=cc(c,16)|0;b[a+4>>2]=g;h=cc(c,16)|0;b[a+8>>2]=h;b[a+12>>2]=cc(c,6)|0;b[a+16>>2]=cc(c,8)|0;f=cc(c,4)|0;b[a+20>>2]=f+1;a:do if(!((e|0)<1|(g|0)<1)?!((h|0)<1|(f|0)<0):0){g=a+24|0;h=i+24|0;e=0;while(1){d=cc(c,8)|0;b[g+(e<<2)>>2]=d;if((d|0)<0)break a;if((d|0)>=(b[h>>2]|0))break a;d=b[i+1824+(d<<2)>>2]|0;if(!(b[d+12>>2]|0))break a;if((b[d>>2]|0)<1)break a;if((e|0)<(f|0))e=e+1|0;else break}return a|0}else j=9;while(0);if((j|0)==9?(a|0)==0:0){j=0;return j|0}fc(a);j=0;return j|0}function zb(a,c){a=a|0;c=c|0;a=gc(1,32)|0;b[a+4>>2]=b[c>>2];b[a>>2]=b[c+8>>2];b[a+20>>2]=c;b[a+8>>2]=gc(2,4)|0;return a|0}function Ab(a){a=a|0;if(a|0)fc(a);return}function Bb(a){a=a|0;var c=0,d=0,e=0;if(!a)return;e=a+8|0;c=b[e>>2]|0;if(c|0){d=b[c>>2]|0;if(d){fc(d);c=b[e>>2]|0}d=b[c+4>>2]|0;if(d){fc(d);c=b[e>>2]|0}fc(c)}fc(a);return}function Cb(a,c){a=a|0;c=c|0;var e=0,f=0,g=0,h=0.0,i=0,j=0,k=0.0;g=b[c+20>>2]|0;i=a+4|0;e=g+12|0;f=cc(i,b[e>>2]|0)|0;if((f|0)<=0){j=0;return j|0}k=+(f|0)/+((1<<b[e>>2])+-1|0)*+(b[g+16>>2]|0);e=g+20|0;f=cc(i,Sa(b[e>>2]|0)|0)|0;if((f|0)==-1){j=0;return j|0}if((f|0)>=(b[e>>2]|0)){j=0;return j|0}j=(b[(b[(b[(b[a+64>>2]|0)+4>>2]|0)+28>>2]|0)+2848>>2]|0)+((b[g+24+(f<<2)>>2]|0)*56|0)|0;e=c+4|0;a=ea(a,((b[j>>2]|0)+(b[e>>2]|0)<<2)+4|0)|0;if((Qa(j,a,i,b[e>>2]|0)|0)==-1){j=0;return j|0}c=b[e>>2]|0;if((c|0)>0){h=0.0;e=0;while(1){a:do if((e|0)<(c|0)){g=b[j>>2]|0;f=0;while(1){if((f|0)>=(g|0))break a;i=a+(e<<2)|0;d[i>>2]=+d[i>>2]+h;e=e+1|0;if((e|0)<(c|0))f=f+1|0;else break}}while(0);if((e|0)<(c|0))h=+d[a+(e+-1<<2)>>2];else break}}d[a+(c<<2)>>2]=k;j=a;return j|0}function Db(a,c,e,f){a=a|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0.0,j=0,k=0.0,l=0,m=0.0,n=0,o=0,q=0;o=b[c+20>>2]|0;l=c+8|0;g=b[l>>2]|0;q=b[a+28>>2]|0;h=g+(q<<2)|0;if(!(b[h>>2]|0)){a=b[(b[(b[(b[a+64>>2]|0)+4>>2]|0)+28>>2]|0)+(q<<2)>>2]|0;n=(a|0)/2|0;j=b[c>>2]|0;i=+(b[o+4>>2]|0);k=i*.5;b[h>>2]=ec((n<<2)+4|0)|0;a:do if((a|0)>1){m=+(j|0)/(+v(+(i*9.249999877170012e-09*k))*2.240000009536743+i*4.999999873689376e-05+ +v(+(i*3.699999942909926e-04))*13.100000381469727);g=b[l>>2]|0;a=b[g+(q<<2)>>2]|0;i=k*(1.0/+(n|0));h=0;while(1){k=i*+(h|0);l=~~+p(+((+v(+(k*k*1.8499999754340024e-08))*2.240000009536743+ +v(+(k*7.399999885819852e-04))*13.100000381469727+k*9.999999747378752e-05)*m));b[a+(h<<2)>>2]=(j|0)>(l|0)?l:j+-1|0;h=h+1|0;if((h|0)==(n|0)){h=n;break a}j=b[c>>2]|0}}else{g=b[l>>2]|0;h=0;a=b[g+(q<<2)>>2]|0}while(0);b[a+(h<<2)>>2]=-1;b[c+12+(q<<2)>>2]=n}if(!e){Fc(f|0,0,b[c+12+(q<<2)>>2]<<2|0)|0;q=0;return q|0}else{n=b[c+4>>2]|0;Xb(f,b[g+(q<<2)>>2]|0,b[c+12+(q<<2)>>2]|0,b[c>>2]|0,e,n,+d[e+(n<<2)>>2],+(b[o+16>>2]|0));q=1;return q|0}return 0}function Eb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=gc(1,2840)|0;j=b[a+28>>2]|0;b[m>>2]=cc(c,24)|0;b[m+4>>2]=cc(c,24)|0;b[m+8>>2]=(cc(c,24)|0)+1;f=cc(c,6)|0;l=f+1|0;b[m+12>>2]=l;i=cc(c,8)|0;b[m+20>>2]=i;a:do if((i|0)>=0){if((f|0)>=0){h=m+24|0;e=0;g=0;while(1){a=cc(c,3)|0;d=cc(c,1)|0;if((d|0)<0){k=26;break a}if(d){d=cc(c,5)|0;if(!(d>>31&2))a=((d|0)<0?0:d<<3)|a;else{k=26;break a}}b[h+(e<<2)>>2]=a;if(!a)a=0;else{d=a;a=0;do{a=(d&1)+a|0;d=d>>>1}while((d|0)!=0)}g=a+g|0;if((e|0)>=(f|0))break;else e=e+1|0}a=(g|0)>0;if(a){e=m+280|0;d=0;do{f=cc(c,8)|0;if((f|0)<0){k=26;break a}b[e+(d<<2)>>2]=f;d=d+1|0}while((d|0)<(g|0))}else a=0}else{g=0;a=0}f=b[j+24>>2]|0;if((i|0)<(f|0)){if(a){d=m+280|0;a=0;do{e=b[d+(a<<2)>>2]|0;if((e|0)>=(f|0))break a;a=a+1|0;if(!(b[(b[j+1824+(e<<2)>>2]|0)+12>>2]|0))break a}while((a|0)<(g|0))}a=b[j+1824+(i<<2)>>2]|0;e=b[a+4>>2]|0;a=b[a>>2]|0;if((a|0)>=1){d=1;while(1){d=z(d,l)|0;if((d|0)>(e|0)){k=26;break a}if((a|0)<=1)break;else a=a+-1|0}b[m+16>>2]=d;return m|0}else k=26}else k=26}else k=26;while(0);if((k|0)==26?(m|0)==0:0){m=0;return m|0}fc(m);m=0;return m|0}function Fb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;o=gc(1,44)|0;j=b[(b[a+4>>2]|0)+28>>2]|0;b[o>>2]=c;n=b[c+12>>2]|0;b[o+4>>2]=n;j=j+2848|0;m=b[j>>2]|0;b[o+12>>2]=m;m=m+((b[c+20>>2]|0)*56|0)|0;b[o+16>>2]=m;m=b[m>>2]|0;k=gc(n,4)|0;b[o+20>>2]=k;if((n|0)>0){l=c+24|0;i=c+280|0;a=0;h=0;e=0;do{c=l+(h<<2)|0;g=Sa(b[c>>2]|0)|0;if(g){e=(g|0)>(e|0)?g:e;f=k+(h<<2)|0;b[f>>2]=gc(g,4)|0;if((g|0)>0){d=b[c>>2]|0;c=0;do{if(d&1<<c){b[(b[f>>2]|0)+(c<<2)>>2]=(b[j>>2]|0)+((b[i+(a<<2)>>2]|0)*56|0);a=a+1|0}c=c+1|0}while((c|0)!=(g|0))}}h=h+1|0}while((h|0)!=(n|0))}else e=0;c=o+24|0;b[c>>2]=1;f=(m|0)>0;if(f){a=0;d=1;do{d=z(d,n)|0;a=a+1|0}while((a|0)!=(m|0));b[c>>2]=d}else d=1;b[o+8>>2]=e;h=ec(d<<2)|0;b[o+28>>2]=h;if((d|0)<=0)return o|0;i=m<<2;if(!f){a=0;do{b[h+(a<<2)>>2]=ec(i)|0;a=a+1|0}while((a|0)!=(d|0));return o|0}e=0;do{g=ec(i)|0;b[h+(e<<2)>>2]=g;a=e;c=0;f=d;do{f=(f|0)/(n|0)|0;l=(a|0)/(f|0)|0;a=a-(z(l,f)|0)|0;b[g+(c<<2)>>2]=l;c=c+1|0}while((c|0)!=(m|0));e=e+1|0}while((e|0)!=(d|0));return o|0}function Gb(a){a=a|0;if(a|0)fc(a);return}function Hb(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0;if(!a)return;g=a+4|0;d=b[g>>2]|0;h=a+20|0;c=b[h>>2]|0;if((d|0)>0){f=0;do{e=b[c+(f<<2)>>2]|0;if(e){fc(e);d=b[g>>2]|0;c=b[h>>2]|0}f=f+1|0}while((f|0)<(d|0))}fc(c);e=a+24|0;f=a+28|0;c=b[f>>2]|0;if((b[e>>2]|0)>0){d=0;do{fc(b[c+(d<<2)>>2]|0);d=d+1|0;c=b[f>>2]|0}while((d|0)<(b[e>>2]|0))}fc(c);fc(a);return}function Ib(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;if((f|0)<=0)return 0;h=0;g=0;do{if(b[e+(h<<2)>>2]|0){b[d+(g<<2)>>2]=b[d+(h<<2)>>2];g=g+1|0}h=h+1|0}while((h|0)!=(f|0));if(!g)return 0;Jb(a,c,d,g,3);return 0}function Jb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;G=L;C=b[c>>2]|0;D=b[C+8>>2]|0;F=c+16|0;B=b[b[F>>2]>>2]|0;g=b[a+36>>2]>>1;A=b[C+4>>2]|0;g=((A|0)<(g|0)?A:g)-(b[C>>2]|0)|0;if((g|0)<=0){L=G;return}x=(g|0)/(D|0)|0;y=L;L=L+((1*(e<<2)|0)+15&-16)|0;A=(e|0)>0;if(A){h=((B+-1+x|0)/(B|0)|0)<<2;g=0;do{b[y+(g<<2)>>2]=ea(a,h)|0;g=g+1|0}while((g|0)!=(e|0))}t=c+8|0;g=b[t>>2]|0;if((g|0)<=0){L=G;return}u=(x|0)>0;v=(e|0)<1;w=(B|0)>0;q=a+4|0;r=C+16|0;s=c+28|0;o=c+20|0;p=0-B|0;n=0;a:while(1){if(u){l=1<<n;m=(n|0)!=0|v;k=0;g=0;while(1){if(!m){h=0;do{a=Ma(b[F>>2]|0,q)|0;if((a|0)==-1){g=28;break a}if((a|0)>=(b[r>>2]|0)){g=28;break a}j=b[(b[s>>2]|0)+(a<<2)>>2]|0;b[(b[y+(h<<2)>>2]|0)+(k<<2)>>2]=j;h=h+1|0;if(!j){g=28;break a}}while((h|0)<(e|0))}do if(w&(g|0)<(x|0)){if(!A){j=g-x|0;g=g-(j>>>0<p>>>0?p:j)|0;break}j=0;do{a=z(g,D)|0;h=0;do{c=(b[C>>2]|0)+a|0;i=b[(b[(b[y+(h<<2)>>2]|0)+(k<<2)>>2]|0)+(j<<2)>>2]|0;if((b[C+24+(i<<2)>>2]&l|0?(E=b[(b[(b[o>>2]|0)+(i<<2)>>2]|0)+(n<<2)>>2]|0,E|0):0)?(Q[f&7](E,(b[d+(h<<2)>>2]|0)+(c<<2)|0,q,D)|0)==-1:0){g=28;break a}h=h+1|0}while((h|0)<(e|0));j=j+1|0;g=g+1|0}while((j|0)<(B|0)&(g|0)<(x|0))}while(0);if((g|0)<(x|0))k=k+1|0;else break}g=b[t>>2]|0}n=n+1|0;if((n|0)>=(g|0)){g=28;break}}if((g|0)==28){L=G;return}}function Kb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;Zb(c,b[a>>2]|0,24);Zb(c,b[a+4>>2]|0,24);Zb(c,(b[a+8>>2]|0)+-1|0,24);h=a+12|0;Zb(c,(b[h>>2]|0)+-1|0,6);Zb(c,b[a+20>>2]|0,8);if((b[h>>2]|0)<=0)return;g=a+24|0;f=0;i=0;do{d=g+(f<<2)|0;j=(Sa(b[d>>2]|0)|0)>3;e=b[d>>2]|0;if(j){Zb(c,e,3);Zb(c,1,1);Zb(c,b[d>>2]>>3,5)}else Zb(c,e,4);d=b[d>>2]|0;if(!d)d=0;else{e=d;d=0;do{d=(e&1)+d|0;e=e>>>1}while((e|0)!=0)}i=d+i|0;f=f+1|0}while((f|0)<(b[h>>2]|0));if((i|0)<=0)return;e=a+280|0;d=0;do{Zb(c,b[e+(d<<2)>>2]|0,8);d=d+1|0}while((d|0)!=(i|0));return}function Lb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0.0,t=0,u=0;if((f|0)<=0){d=0;return d|0}h=0;g=0;do{if(b[e+(h<<2)>>2]|0){b[d+(g<<2)>>2]=b[d+(h<<2)>>2];g=g+1|0}h=h+1|0}while((h|0)!=(f|0));if(!g){d=0;return d|0}o=b[c>>2]|0;p=b[o+8>>2]|0;f=b[o+12>>2]|0;q=((b[o+4>>2]|0)-(b[o>>2]|0)|0)/(p|0)|0;r=ea(a,g<<2)|0;s=100.0/+(p|0);t=(g|0)>0;if(t){e=q<<2;h=0;do{n=ea(a,e)|0;b[r+(h<<2)>>2]=n;Fc(n|0,0,e|0)|0;h=h+1|0}while((h|0)!=(g|0))}if((q|0)>0){m=(p|0)>0;n=f+-1|0;l=(f|0)>1;k=0;do{j=z(k,p)|0;j=(b[o>>2]|0)+j|0;a:do if(t){if(!m){if(!l){h=0;while(1){b[(b[r+(h<<2)>>2]|0)+(k<<2)>>2]=0;h=h+1|0;if((h|0)==(g|0))break a}}e=0;while(1){h=0;do{if((b[o+2328+(h<<2)>>2]|0)>=0?b[o+2584+(h<<2)>>2]|0:0)break;h=h+1|0}while((h|0)<(n|0));b[(b[r+(e<<2)>>2]|0)+(k<<2)>>2]=h;e=e+1|0;if((e|0)==(g|0))break a}}if(!l){h=0;while(1){b[(b[r+(h<<2)>>2]|0)+(k<<2)>>2]=0;h=h+1|0;if((h|0)==(g|0))break a}}i=0;do{f=b[d+(i<<2)>>2]|0;h=0;a=0;e=0;do{u=b[f+(j+e<<2)>>2]|0;u=(u|0)>-1?u:0-u|0;a=(u|0)>(a|0)?u:a;h=u+h|0;e=e+1|0}while((e|0)!=(p|0));e=~~(+(h|0)*s);h=0;do{if((a|0)<=(b[o+2328+(h<<2)>>2]|0)?(u=b[o+2584+(h<<2)>>2]|0,(u|0)<0|(u|0)>(e|0)):0)break;h=h+1|0}while((h|0)<(n|0));b[(b[r+(i<<2)>>2]|0)+(k<<2)>>2]=h;i=i+1|0}while((i|0)!=(g|0))}while(0);k=k+1|0}while((k|0)!=(q|0))}u=c+40|0;b[u>>2]=(b[u>>2]|0)+1;u=r;return u|0}function Mb(a,c,d,e,f,g,h,i){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;if((g|0)<=0)return 0;i=0;c=0;do{if(b[f+(i<<2)>>2]|0){b[e+(c<<2)>>2]=b[e+(i<<2)>>2];c=c+1|0}i=i+1|0}while((i|0)!=(g|0));if(!c)return 0;Ob(a,d,e,c,h);return 0}
function Nb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;if((f|0)<=0)return 0;h=0;g=0;do{if(b[e+(h<<2)>>2]|0){b[d+(g<<2)>>2]=b[d+(h<<2)>>2];g=g+1|0}h=h+1|0}while((h|0)!=(f|0));if(!g)return 0;Jb(a,c,d,g,4);return 0}function Ob(c,d,e,f,g){c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0;ia=L;L=L+1088|0;ca=ia+1056|0;da=ia+1024|0;ea=ia+512|0;fa=ia;ha=b[d>>2]|0;R=b[ha+8>>2]|0;S=b[ha+12>>2]|0;T=d+16|0;U=b[b[T>>2]>>2]|0;V=((b[ha+4>>2]|0)-(b[ha>>2]|0)|0)/(R|0)|0;Fc(ea|0,0,512)|0;Fc(fa|0,0,512)|0;W=d+8|0;h=b[W>>2]|0;if((h|0)<=0){L=ia;return}X=(V|0)>0;Y=(f|0)<1;Z=(U|0)>0;_=(U|0)>1;$=d+36|0;aa=(f|0)>0;ba=d+20|0;Q=d+32|0;P=0;d=h;do{if(X){M=(P|0)==0;N=1<<P;O=Y|M^1;d=0;do{a:do if(!O){if(!_){h=0;while(1){i=b[(b[g+(h<<2)>>2]|0)+(d<<2)>>2]|0;j=b[T>>2]|0;if((i|0)<(b[j+4>>2]|0)){K=La(j,i,c)|0;b[$>>2]=(b[$>>2]|0)+K}h=h+1|0;if((h|0)==(f|0))break a}}l=0;do{k=b[g+(l<<2)>>2]|0;j=1;h=b[k+(d<<2)>>2]|0;while(1){i=z(h,S)|0;h=j+d|0;if((h|0)<(V|0))i=(b[k+(h<<2)>>2]|0)+i|0;j=j+1|0;if((j|0)==(U|0))break;else h=i}h=b[T>>2]|0;if((i|0)<(b[h+4>>2]|0)){K=La(h,i,c)|0;b[$>>2]=(b[$>>2]|0)+K}l=l+1|0}while((l|0)!=(f|0))}while(0);if(Z&(d|0)<(V|0)){K=0;do{J=z(d,R)|0;J=(b[ha>>2]|0)+J|0;if(aa){I=0;do{H=g+(I<<2)|0;h=(b[H>>2]|0)+(d<<2)|0;i=b[h>>2]|0;if(M){G=fa+(i<<2)|0;b[G>>2]=(b[G>>2]|0)+R}if(b[ha+24+(i<<2)>>2]&N|0?(ga=b[(b[(b[ba>>2]|0)+(i<<2)>>2]|0)+(P<<2)>>2]|0,ga|0):0){E=(b[e+(I<<2)>>2]|0)+(J<<2)|0;F=b[ga>>2]|0;G=(R|0)/(F|0)|0;if((G|0)>0){y=ga+48|0;A=ga+52|0;B=ga+44|0;C=ga+12|0;D=ga+4|0;i=0;h=0;x=F;while(1){j=E+((z(h,F)|0)<<2)|0;t=b[y>>2]|0;v=b[A>>2]|0;s=b[B>>2]|0;r=s>>1;b[ca>>2]=0;b[ca+4>>2]=0;b[ca+8>>2]=0;b[ca+12>>2]=0;b[ca+16>>2]=0;b[ca+20>>2]=0;b[ca+24>>2]=0;b[ca+28>>2]=0;w=(x|0)>0;do if((v|0)==1){if(!w){k=0;break}o=s+-1|0;Ec(ca|0,j|0,x<<2|0)|0;m=x;k=0;n=0;do{m=m+-1|0;l=(b[j+(m<<2)>>2]|0)-t|0;if((l|0)<(r|0))l=(r-l<<1)+-1|0;else l=l-r<<1;k=z(k,s)|0;k=((l|0)<0?0:(l|0)<(s|0)?l:o)+k|0;n=n+1|0}while((n|0)!=(x|0))}else{if(!w){k=0;break}o=(v>>1)-t|0;p=s+-1|0;m=0;n=x;k=0;do{n=n+-1|0;q=(o+(b[j+(n<<2)>>2]|0)|0)/(v|0)|0;if((q|0)<(r|0))l=(r-q<<1)+-1|0;else l=q-r<<1;k=z(k,s)|0;k=((l|0)<0?0:(l|0)<(s|0)?l:p)+k|0;b[ca+(n<<2)>>2]=(z(q,v)|0)+t;m=m+1|0}while((m|0)!=(x|0))}while(0);u=b[(b[C>>2]|0)+8>>2]|0;if((a[u+k>>0]|0)<1){b[da>>2]=0;b[da+4>>2]=0;b[da+8>>2]=0;b[da+12>>2]=0;b[da+16>>2]=0;b[da+20>>2]=0;b[da+24>>2]=0;b[da+28>>2]=0;q=(z(s+-1|0,v)|0)+t|0;r=b[D>>2]|0;b:do if((r|0)>0){if(!w){l=-1;p=0;while(1){do if((a[u+p>>0]|0)>0){if(!((l|0)==-1|(l|0)>0)){o=l;break};b[ca>>2]=b[da>>2];b[ca+4>>2]=b[da+4>>2];b[ca+8>>2]=b[da+8>>2];b[ca+12>>2]=b[da+12>>2];b[ca+16>>2]=b[da+16>>2];b[ca+20>>2]=b[da+20>>2];b[ca+24>>2]=b[da+24>>2];b[ca+28>>2]=b[da+28>>2];o=0;k=p}else o=l;while(0);l=b[da>>2]|0;if((l|0)<(q|0))n=da;else{m=0;l=da;while(1){m=m+1|0;b[l>>2]=0;n=da+(m<<2)|0;l=b[n>>2]|0;if((l|0)<(q|0))break;else l=n}}m=l+v|0;if((l|0)>-1){b[n>>2]=m;l=m}b[n>>2]=0-l;p=p+1|0;if((p|0)==(r|0))break b;else l=o}}n=-1;p=0;do{do if((a[u+p>>0]|0)>0){l=0;m=0;do{t=(b[da+(m<<2)>>2]|0)-(b[j+(m<<2)>>2]|0)|0;l=(z(t,t)|0)+l|0;m=m+1|0}while((m|0)!=(x|0));if(!((n|0)==-1|(l|0)<(n|0)))break;b[ca>>2]=b[da>>2];b[ca+4>>2]=b[da+4>>2];b[ca+8>>2]=b[da+8>>2];b[ca+12>>2]=b[da+12>>2];b[ca+16>>2]=b[da+16>>2];b[ca+20>>2]=b[da+20>>2];b[ca+24>>2]=b[da+24>>2];b[ca+28>>2]=b[da+28>>2];n=l;k=p}while(0);l=b[da>>2]|0;if((l|0)<(q|0))o=da;else{m=0;l=da;while(1){m=m+1|0;b[l>>2]=0;o=da+(m<<2)|0;l=b[o>>2]|0;if((l|0)<(q|0))break;else l=o}}m=l+v|0;if((l|0)>-1){b[o>>2]=m;l=m}b[o>>2]=0-l;p=p+1|0}while((p|0)!=(r|0))}while(0)}if(w&(k|0)>-1){l=0;while(1){b[j>>2]=(b[j>>2]|0)-(b[ca+(l<<2)>>2]|0);l=l+1|0;if((l|0)==(x|0))break;else j=j+4|0}}i=(La(ga,k,c)|0)+i|0;h=h+1|0;if((h|0)==(G|0))break;x=b[ga>>2]|0}h=(b[H>>2]|0)+(d<<2)|0}else i=0;b[Q>>2]=(b[Q>>2]|0)+i;H=ea+(b[h>>2]<<2)|0;b[H>>2]=(b[H>>2]|0)+i}I=I+1|0}while((I|0)!=(f|0))}K=K+1|0;d=d+1|0}while((K|0)<(U|0)&(d|0)<(V|0))}}while((d|0)<(V|0));d=b[W>>2]|0}P=P+1|0}while((P|0)<(d|0));L=ia;return}function Pb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;if((f|0)<=0){c=0;return c|0}g=0;h=0;do{h=h+((b[e+(g<<2)>>2]|0)!=0&1)|0;g=g+1|0}while((g|0)!=(f|0));if(!h){c=0;return c|0}q=b[c>>2]|0;n=b[q+8>>2]|0;g=b[q+12>>2]|0;r=((b[q+4>>2]|0)-(b[q>>2]|0)|0)/(n|0)|0;s=ea(a,4)|0;h=r<<2;e=ea(a,h)|0;b[s>>2]=e;Fc(e|0,0,h|0)|0;e=(b[q>>2]|0)/(f|0)|0;a:do if((r|0)>0){p=g+-1|0;m=(g|0)>1;o=b[s>>2]|0;if((n|0)<=0){if(!m){Fc(o|0,0,h|0)|0;break}h=0;while(1){g=0;do{if((b[q+2328+(g<<2)>>2]|0)>=0?(b[q+2584+(g<<2)>>2]|0)>=0:0)break;g=g+1|0}while((g|0)<(p|0));b[o+(h<<2)>>2]=g;h=h+1|0;if((h|0)==(r|0))break a}}l=b[d>>2]|0;if((f|0)<=1){i=0;g=e;while(1){e=0;h=0;a=g;do{d=b[l+(a<<2)>>2]|0;d=(d|0)>-1?d:0-d|0;e=(d|0)>(e|0)?d:e;a=a+1|0;h=h+f|0}while((h|0)<(n|0));b:do if(m){g=0;do{if((e|0)<=(b[q+2328+(g<<2)>>2]|0)?(b[q+2584+(g<<2)>>2]|0)>=0:0)break b;g=g+1|0}while((g|0)<(p|0))}else g=0;while(0);b[o+(i<<2)>>2]=g;i=i+1|0;if((i|0)==(r|0))break a;else g=a}}k=0;g=e;while(1){i=0;a=0;h=0;j=g;do{e=b[l+(j<<2)>>2]|0;e=(e|0)>-1?e:0-e|0;g=1;do{t=b[(b[d+(g<<2)>>2]|0)+(j<<2)>>2]|0;t=(t|0)>-1?t:0-t|0;h=(t|0)>(h|0)?t:h;g=g+1|0}while((g|0)!=(f|0));i=(e|0)>(i|0)?e:i;j=j+1|0;a=a+f|0}while((a|0)<(n|0));c:do if(m){g=0;do{if((i|0)<=(b[q+2328+(g<<2)>>2]|0)?(h|0)<=(b[q+2584+(g<<2)>>2]|0):0)break c;g=g+1|0}while((g|0)<(p|0))}else g=0;while(0);b[o+(k<<2)>>2]=g;k=k+1|0;if((k|0)==(r|0))break;else g=j}}while(0);t=c+40|0;b[t>>2]=(b[t>>2]|0)+1;t=s;return t|0}function Qb(a,c,d,e,f,g,h,i){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;q=L;L=L+16|0;p=q;i=b[c+36>>2]|0;o=(i|0)/2|0;n=ea(c,z(g<<2,o)|0)|0;b[p>>2]=n;if((g|0)<=0){L=q;return 0}if((i|0)>1){i=0;m=0;do{k=b[e+(m<<2)>>2]|0;l=(b[f+(m<<2)>>2]|0)!=0&1;c=m;j=0;while(1){b[n+(c<<2)>>2]=b[k+(j<<2)>>2];j=j+1|0;if((j|0)==(o|0))break;else c=c+g|0}i=i+l|0;m=m+1|0}while((m|0)!=(g|0))}else{i=0;c=0;do{i=i+((b[f+(c<<2)>>2]|0)!=0&1)|0;c=c+1|0}while((c|0)!=(g|0))}if(!i){L=q;return 0}Ob(a,d,p,1,h);L=q;return 0}function Rb(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;u=b[c>>2]|0;w=b[u+8>>2]|0;x=c+16|0;t=b[b[x>>2]>>2]|0;g=(z(b[a+36>>2]|0,f)|0)>>1;s=b[u+4>>2]|0;g=((s|0)<(g|0)?s:g)-(b[u>>2]|0)|0;if((g|0)<=0)return 0;r=(g|0)/(w|0)|0;s=ea(a,((t+-1+r|0)/(t|0)|0)<<2)|0;a:do if((f|0)>0){g=0;do{if(b[e+(g<<2)>>2]|0)break a;g=g+1|0}while((g|0)<(f|0))}else g=0;while(0);if((g|0)==(f|0))return 0;p=c+8|0;if((b[p>>2]|0)<=0)return 0;q=(r|0)>0;l=a+4|0;m=(t|0)>0;n=u+16|0;o=c+20|0;j=c+28|0;i=0;b:while(1){c:do if(q){k=1<<i;if(i|0){h=0;g=0;while(1){if(m&(g|0)<(r|0)){c=s+(h<<2)|0;a=0;do{e=b[(b[c>>2]|0)+(a<<2)>>2]|0;if((b[u+24+(e<<2)>>2]&k|0?(y=b[(b[(b[o>>2]|0)+(e<<2)>>2]|0)+(i<<2)>>2]|0,y|0):0)?(e=z(g,w)|0,(Ra(y,d,(b[u>>2]|0)+e|0,f,l,w)|0)==-1):0){g=32;break b}a=a+1|0;g=g+1|0}while((a|0)<(t|0)&(g|0)<(r|0))}if((g|0)<(r|0))h=h+1|0;else break c}}h=0;g=0;while(1){e=Ma(b[x>>2]|0,l)|0;if((e|0)==-1){g=32;break b}if((e|0)>=(b[n>>2]|0)){g=32;break b}e=b[(b[j>>2]|0)+(e<<2)>>2]|0;c=s+(h<<2)|0;b[c>>2]=e;if(!e){g=32;break b}d:do if(m&(g|0)<(r|0)){a=0;while(1){e=b[e+(a<<2)>>2]|0;if((b[u+24+(e<<2)>>2]&k|0?(v=b[b[(b[o>>2]|0)+(e<<2)>>2]>>2]|0,v|0):0)?(e=z(g,w)|0,(Ra(v,d,(b[u>>2]|0)+e|0,f,l,w)|0)==-1):0){g=32;break b}e=a+1|0;g=g+1|0;if(!((e|0)<(t|0)&(g|0)<(r|0)))break d;a=e;e=b[c>>2]|0}}while(0);if((g|0)<(r|0))h=h+1|0;else break}}while(0);i=i+1|0;if((i|0)>=(b[p>>2]|0)){g=32;break}}if((g|0)==32)return 0;return 0}function Sb(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if((b[c>>2]|0)>1){Zb(d,1,1);Zb(d,(b[c>>2]|0)+-1|0,4)}else Zb(d,0,1);i=c+1156|0;if((b[i>>2]|0)>0){Zb(d,1,1);Zb(d,(b[i>>2]|0)+-1|0,8);if((b[i>>2]|0)>0){f=c+1160|0;g=a+4|0;h=c+2184|0;e=0;do{l=b[f+(e<<2)>>2]|0;Zb(d,l,Sa((b[g>>2]|0)+-1|0)|0);l=b[h+(e<<2)>>2]|0;Zb(d,l,Sa((b[g>>2]|0)+-1|0)|0);e=e+1|0}while((e|0)<(b[i>>2]|0))}}else Zb(d,0,1);Zb(d,0,2);e=b[c>>2]|0;if((e|0)>1){g=a+4|0;if((b[g>>2]|0)>0){f=c+4|0;e=0;do{Zb(d,b[f+(e<<2)>>2]|0,4);e=e+1|0}while((e|0)<(b[g>>2]|0));j=b[c>>2]|0;k=14}}else{j=e;k=14}if((k|0)==14?(j|0)<=0:0)return;f=c+1028|0;g=c+1092|0;e=0;do{Zb(d,0,8);Zb(d,b[f+(e<<2)>>2]|0,8);Zb(d,b[g+(e<<2)>>2]|0,8);e=e+1|0}while((e|0)<(b[c>>2]|0));return}function Tb(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;d=gc(1,3208)|0;m=b[a+28>>2]|0;l=a+4|0;a:do if((b[l>>2]|0)>=1?(e=cc(c,1)|0,(e|0)>=0):0){if(e){k=cc(c,4)|0;a=k+1|0;b[d>>2]=a;if((k|0)<0)break}else{b[d>>2]=1;a=1}e=cc(c,1)|0;if((e|0)>=0){if(e|0){f=cc(c,8)|0;b[d+1156>>2]=f+1;if((f|0)<0)break;g=d+1160|0;h=d+2184|0;e=0;i=b[l>>2]|0;while(1){j=cc(c,Sa(i+-1|0)|0)|0;b[g+(e<<2)>>2]=j;k=cc(c,Sa((b[l>>2]|0)+-1|0)|0)|0;b[h+(e<<2)>>2]=k;if((j|0)==(k|0)|(k|j|0)<0)break a;i=b[l>>2]|0;if(!((j|0)<(i|0)&(k|0)<(i|0)))break a;if((e|0)>=(f|0))break;else e=e+1|0}}if(!(cc(c,2)|0)){if((a|0)>1){if((b[l>>2]|0)>0){f=d+4|0;e=0;while(1){k=cc(c,4)|0;b[f+(e<<2)>>2]=k;e=e+1|0;if((k|0)>=(a|0)|(k|0)<0)break a;if((e|0)>=(b[l>>2]|0)){n=19;break}}}}else n=19;if((n|0)==19?(a|0)<=0:0){n=d;return n|0}g=d+1028|0;h=m+16|0;i=d+1092|0;f=m+20|0;e=0;do{cc(c,8)|0;m=cc(c,8)|0;b[g+(e<<2)>>2]=m;if((m|0)<0?1:(m|0)>=(b[h>>2]|0))break a;m=cc(c,8)|0;b[i+(e<<2)>>2]=m;e=e+1|0;if((m|0)<0?1:(m|0)>=(b[f>>2]|0))break a}while((e|0)<(a|0));return d|0}}}else n=24;while(0);if((n|0)==24?(d|0)==0:0){n=0;return n|0}fc(d);n=0;return n|0}function Ub(a){a=a|0;if(a|0)fc(a);return}function Vb(a){a=a|0;var c=0,e=0,g=0.0,h=0.0,i=0,j=0,k=0.0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0.0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0.0,O=0.0;M=L;z=b[a+64>>2]|0;B=b[z+4>>2]|0;K=b[B+28>>2]|0;z=b[z+104>>2]|0;A=b[a+104>>2]|0;v=b[a+36>>2]|0;B=B+4|0;D=b[B>>2]<<2;C=L;L=L+((1*D|0)+15&-16)|0;D=ea(a,D)|0;E=ea(a,b[B>>2]<<2)|0;F=ea(a,b[B>>2]<<2)|0;y=A+4|0;g=+d[y>>2];t=b[B>>2]|0;w=L;L=L+((1*(t<<2)|0)+15&-16)|0;G=a+28|0;H=b[G>>2]|0;I=b[K+544+(H<<2)>>2]|0;J=(b[z+56>>2]|0)+((b[A+8>>2]|0)*52|0)+(((H|0)==0?0:2)*52|0)|0;x=a+40|0;b[x>>2]=H;if((t|0)>0){s=(v|0)/2|0;c=s<<2;m=z+4|0;n=a+24|0;o=a+32|0;p=v+-1|0;q=(v|0)>2;u=1.0/+(v|0);l=0;do{t=(d[f>>2]=4.0*u,b[f>>2]|0);i=b[(b[a>>2]|0)+(l<<2)>>2]|0;b[E+(l<<2)>>2]=ea(a,c)|0;j=D+(l<<2)|0;b[j>>2]=ea(a,c)|0;k=+((t&2147483647)>>>0)*7.177114298428933e-07+-764.6162109375+.345;pa(i,m,K,b[n>>2]|0,b[G>>2]|0,b[o>>2]|0);fb(b[b[z+12+(b[G>>2]<<2)>>2]>>2]|0,i,b[j>>2]|0);gb(z+20+((b[G>>2]|0)*12|0)|0,i);h=k+-764.6162109375+ +((b[i>>2]&2147483647)>>>0)*7.177114298428933e-07+.345;d[i>>2]=h;j=w+(l<<2)|0;d[j>>2]=h;if(q){k=k+-382.30810546875;e=1;do{N=+d[i+(e<<2)>>2];t=e+1|0;O=+d[i+(t<<2)>>2];N=k+ +(((d[f>>2]=O*O+N*N,b[f>>2]|0)&2147483647)>>>0)*3.5885571492144663e-07+.345;d[i+(t>>>1<<2)>>2]=N;h=h<N?N:h;e=e+2|0}while((e|0)<(p|0));d[j>>2]=h}if(h>0.0){d[j>>2]=0.0;h=0.0}g=h>g?h:g;l=l+1|0}while((l|0)<(b[B>>2]|0))}else{s=(v|0)/2|0;c=s<<2}t=ea(a,c)|0;r=ea(a,c)|0;c=b[B>>2]|0;a:do if((c|0)>0){p=(v|0)>1;q=z+48|0;o=0;while(1){i=b[I+4+(o<<2)>>2]|0;j=b[D+(o<<2)>>2]|0;l=b[(b[a>>2]|0)+(o<<2)>>2]|0;m=l+(s<<2)|0;b[x>>2]=H;c=ea(a,60)|0;n=F+(o<<2)|0;b[n>>2]=c;e=c+60|0;do{b[c>>2]=0;c=c+4|0}while((c|0)<(e|0));if(p){c=0;do{d[m+(c<<2)>>2]=+((b[j+(c<<2)>>2]&2147483647)>>>0)*7.177114298428933e-07+-764.6162109375+.345;c=c+1|0}while((c|0)!=(s|0))}xa(J,m,t);za(J,l,r,g,+d[w+(o<<2)>>2]);Ba(J,t,r,1,l,j,m);c=I+1028+(i<<2)|0;e=b[c>>2]|0;if((b[K+800+(e<<2)>>2]|0)!=1){c=-1;break}v=ub(a,b[(b[q>>2]|0)+(e<<2)>>2]|0,m,l)|0;b[(b[n>>2]|0)+28>>2]=v;if(ab(a)|0?b[(b[n>>2]|0)+28>>2]|0:0){Ba(J,t,r,2,l,j,m);v=ub(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,m,l)|0;b[(b[n>>2]|0)+56>>2]=v;Ba(J,t,r,0,l,j,m);v=ub(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,m,l)|0;b[b[n>>2]>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v>>2]|0,b[v+28>>2]|0,9362)|0;b[(b[n>>2]|0)+4>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v>>2]|0,b[v+28>>2]|0,18724)|0;b[(b[n>>2]|0)+8>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v>>2]|0,b[v+28>>2]|0,28086)|0;b[(b[n>>2]|0)+12>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v>>2]|0,b[v+28>>2]|0,37449)|0;b[(b[n>>2]|0)+16>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v>>2]|0,b[v+28>>2]|0,46811)|0;b[(b[n>>2]|0)+20>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v>>2]|0,b[v+28>>2]|0,56173)|0;b[(b[n>>2]|0)+24>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v+28>>2]|0,b[v+56>>2]|0,9362)|0;b[(b[n>>2]|0)+32>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v+28>>2]|0,b[v+56>>2]|0,18724)|0;b[(b[n>>2]|0)+36>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v+28>>2]|0,b[v+56>>2]|0,28086)|0;b[(b[n>>2]|0)+40>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v+28>>2]|0,b[v+56>>2]|0,37449)|0;b[(b[n>>2]|0)+44>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v+28>>2]|0,b[v+56>>2]|0,46811)|0;b[(b[n>>2]|0)+48>>2]=v;v=b[n>>2]|0;v=wb(a,b[(b[q>>2]|0)+(b[c>>2]<<2)>>2]|0,b[v+28>>2]|0,b[v+56>>2]|0,56173)|0;b[(b[n>>2]|0)+52>>2]=v}o=o+1|0;c=b[B>>2]|0;if((o|0)>=(c|0))break a}L=M;return c|0}else q=z+48|0;while(0);d[y>>2]=g;o=c<<2;s=L;L=L+((1*o|0)+15&-16)|0;t=L;L=L+((1*o|0)+15&-16)|0;o=(ab(a)|0)==0;v=z+44|0;w=a+24|0;x=a+32|0;y=K+2868|0;p=z+52|0;o=o?7:0;while(1){r=b[A+12+(o<<2)>>2]|0;Zb(r,0,1);Zb(r,H,b[v>>2]|0);if(b[G>>2]|0){Zb(r,b[w>>2]|0,1);Zb(r,b[x>>2]|0,1)}c=b[B>>2]|0;if((c|0)>0){e=0;do{b[C+(e<<2)>>2]=xb(r,a,b[(b[q>>2]|0)+(b[I+1028+(b[I+4+(e<<2)>>2]<<2)>>2]<<2)>>2]|0,b[(b[F+(e<<2)>>2]|0)+(o<<2)>>2]|0,b[E+(e<<2)>>2]|0)|0;e=e+1|0;c=b[B>>2]|0}while((e|0)<(c|0))}Ca(o,y,J,I,D,E,C,b[K+3240+((b[G>>2]|0)*60|0)+(o<<2)>>2]|0,c);if((b[I>>2]|0)>0){m=0;do{n=b[I+1092+(m<<2)>>2]|0;e=b[B>>2]|0;if((e|0)>0){c=0;i=0;do{if((b[I+4+(i<<2)>>2]|0)==(m|0)){b[t+(c<<2)>>2]=(b[C+(i<<2)>>2]|0)!=0&1;b[s+(c<<2)>>2]=b[E+(i<<2)>>2];c=c+1|0;e=b[B>>2]|0}i=i+1|0}while((i|0)<(e|0))}else c=0;l=K+1312+(n<<2)|0;j=R[b[(b[57240+(b[l>>2]<<2)>>2]|0)+20>>2]&7](a,b[(b[p>>2]|0)+(n<<2)>>2]|0,s,t,c)|0;i=b[B>>2]|0;if((i|0)>0){c=0;e=0;do{if((b[I+4+(e<<2)>>2]|0)==(m|0)){b[s+(c<<2)>>2]=b[E+(e<<2)>>2];c=c+1|0}e=e+1|0}while((e|0)<(i|0))}else c=0;S[b[(b[57240+(b[l>>2]<<2)>>2]|0)+24>>2]&3](r,a,b[(b[p>>2]|0)+(n<<2)>>2]|0,s,t,c,j,m)|0;m=m+1|0}while((m|0)<(b[I>>2]|0))}z=(ab(a)|0)==0;if(o>>>0<(z?7:14)>>>0)o=o+1|0;else{c=0;break}}L=M;return c|0}function Wb(a,c){a=a|0;c=c|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0.0,l=0,m=0.0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;B=L;A=b[a+64>>2]|0;z=b[A+4>>2]|0;x=b[z+28>>2]|0;A=b[A+104>>2]|0;y=a+28|0;r=b[x+(b[y>>2]<<2)>>2]|0;b[a+36>>2]=r;z=z+4|0;e=b[z>>2]|0;n=e<<2;o=L;L=L+((1*n|0)+15&-16)|0;p=L;L=L+((1*n|0)+15&-16)|0;q=L;L=L+((1*n|0)+15&-16)|0;w=L;L=L+((1*n|0)+15&-16)|0;if((e|0)>0){f=c+4|0;g=c+1028|0;h=A+48|0;i=r<<1&2147483646;e=0;do{j=b[g+(b[f+(e<<2)>>2]<<2)>>2]|0;j=P[b[(b[57232+(b[x+800+(j<<2)>>2]<<2)>>2]|0)+20>>2]&15](a,b[(b[h>>2]|0)+(j<<2)>>2]|0)|0;b[w+(e<<2)>>2]=j;b[q+(e<<2)>>2]=(j|0)!=0&1;Fc(b[(b[a>>2]|0)+(e<<2)>>2]|0,0,i|0)|0;e=e+1|0;j=b[z>>2]|0}while((e|0)<(j|0))}else j=e;n=c+1156|0;e=b[n>>2]|0;if((e|0)>0){g=c+1160|0;h=c+2184|0;f=0;do{i=q+(b[g+(f<<2)>>2]<<2)|0;l=q+(b[h+(f<<2)>>2]<<2)|0;if(!((b[i>>2]|0)==0?!(b[l>>2]|0):0)){b[i>>2]=1;b[l>>2]=1}f=f+1|0}while((f|0)<(e|0))}if((b[c>>2]|0)>0){h=c+1092|0;i=A+52|0;l=c+4|0;e=0;while(1){if((j|0)>0){g=0;f=0;do{if((b[l+(g<<2)>>2]|0)==(e|0)){b[p+(f<<2)>>2]=(b[q+(g<<2)>>2]|0)!=0&1;b[o+(f<<2)>>2]=b[(b[a>>2]|0)+(g<<2)>>2];f=f+1|0;j=b[z>>2]|0}g=g+1|0}while((g|0)<(j|0))}else f=0;j=b[h+(e<<2)>>2]|0;R[b[(b[57240+(b[x+1312+(j<<2)>>2]<<2)>>2]|0)+28>>2]&7](a,b[(b[i>>2]|0)+(j<<2)>>2]|0,o,p,f)|0;e=e+1|0;if((e|0)>=(b[c>>2]|0))break;j=b[z>>2]|0}e=b[n>>2]|0}if((e|0)>0?(s=b[a>>2]|0,t=c+1160|0,u=c+2184|0,v=(r|0)/2|0,(r|0)>1):0)do{g=e;e=e+-1|0;h=b[s+(b[t+(e<<2)>>2]<<2)>>2]|0;i=b[s+(b[u+(e<<2)>>2]<<2)>>2]|0;f=0;do{j=h+(f<<2)|0;k=+d[j>>2];l=i+(f<<2)|0;m=+d[l>>2];n=m>0.0;do if(k>0.0)if(n){d[l>>2]=k-m;break}else{d[l>>2]=k;d[j>>2]=m+k;break}else if(n){d[l>>2]=m+k;break}else{d[l>>2]=k;d[j>>2]=k-m;break}while(0);f=f+1|0}while((f|0)!=(v|0))}while((g|0)>1);if((b[z>>2]|0)<=0){L=B;return 0}i=c+4|0;f=c+1028|0;g=A+48|0;e=0;do{h=b[f+(b[i+(e<<2)>>2]<<2)>>2]|0;Q[b[(b[57232+(b[x+800+(h<<2)>>2]<<2)>>2]|0)+24>>2]&7](a,b[(b[g>>2]|0)+(h<<2)>>2]|0,b[w+(e<<2)>>2]|0,b[(b[a>>2]|0)+(e<<2)>>2]|0)|0;e=e+1|0;h=b[z>>2]|0}while((e|0)<(h|0));if((h|0)<=0){L=B;return 0}e=0;do{x=b[(b[a>>2]|0)+(e<<2)>>2]|0;db(b[b[A+12+(b[y>>2]<<2)>>2]>>2]|0,x,x);e=e+1|0}while((e|0)<(b[z>>2]|0));L=B;return 0}function Xb(a,c,e,f,g,h,i,j){a=a|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=+i;j=+j;var k=0.0,l=0.0,m=0,n=0.0,o=0.0,p=0,q=0,s=0,u=0.0;u=3.141592653589793/+(f|0);if((h|0)>0){f=0;do{s=g+(f<<2)|0;d[s>>2]=+t(+(+d[s>>2]))*2.0;f=f+1|0}while((f|0)!=(h|0))}if((e|0)<=0)return;if((h|0)<=1){p=(h|0)==1;f=0;do{m=b[c+(f<<2)>>2]|0;k=+t(+(+(m|0)*u))*2.0;if(p){l=(k-+d[g>>2])*.5;l=l*l;k=4.0-k*k}else{l=(k+2.0)*.25;k=2.0-k}k=+w(+((i/+r(+(l+k*.25))-j)*.1151292473077774));h=a+(f<<2)|0;d[h>>2]=+d[h>>2]*k;f=f+1|0;if((b[c+(f<<2)>>2]|0)==(m|0))do{h=a+(f<<2)|0;d[h>>2]=+d[h>>2]*k;f=f+1|0}while((b[c+(f<<2)>>2]|0)==(m|0))}while((f|0)<(e|0));return}s=h+-2|0;q=((s&-2)+3|0)==(h|0);s=g+((s|1)+1<<2)|0;f=0;do{p=b[c+(f<<2)>>2]|0;o=+t(+(+(p|0)*u))*2.0;k=.5;n=.5;m=1;do{k=(o-+d[g+(m+-1<<2)>>2])*k;n=(o-+d[g+(m<<2)>>2])*n;m=m+2|0}while((m|0)<(h|0));if(q){l=(o-+d[s>>2])*k;l=l*l;k=n*n*(4.0-o*o)}else{l=k*k*(o+2.0);k=n*n*(2.0-o)}k=+w(+((i/+r(+(l+k))-j)*.1151292473077774));m=a+(f<<2)|0;d[m>>2]=+d[m>>2]*k;f=f+1|0;if((b[c+(f<<2)>>2]|0)==(p|0))do{m=a+(f<<2)|0;d[m>>2]=+d[m>>2]*k;f=f+1|0}while((b[c+(f<<2)>>2]|0)==(p|0))}while((f|0)<(e|0));return}function Yb(c){c=c|0;var d=0;d=c;b[d>>2]=0;b[d+4>>2]=0;d=ec(256)|0;b[c+8>>2]=d;b[c+12>>2]=d;a[d>>0]=0;b[c+16>>2]=256;return}function Zb(d,e,f){d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;do if(f>>>0<=32){j=d+16|0;i=b[j>>2]|0;k=d+12|0;g=b[k>>2]|0;if((b[d>>2]|0)>=(i+-4|0)){if(!g)return;if((i|0)>2147483391)break;h=d+8|0;g=hc(b[h>>2]|0,i+256|0)|0;if(!g)break;b[h>>2]=g;b[j>>2]=(b[j>>2]|0)+256;g=g+(b[d>>2]|0)|0;b[k>>2]=g}i=b[57088+(f<<2)>>2]&e;j=d+4|0;e=b[j>>2]|0;h=e+f|0;a[g>>0]=i<<e|(c[g>>0]|0);do if((((h|0)>7?(a[(b[k>>2]|0)+1>>0]=i>>>(8-(b[j>>2]|0)|0),(h|0)>15):0)?(a[(b[k>>2]|0)+2>>0]=i>>>(16-(b[j>>2]|0)|0),(h|0)>23):0)?(a[(b[k>>2]|0)+3>>0]=i>>>(24-(b[j>>2]|0)|0),(h|0)>31):0){g=b[j>>2]|0;if(!g){a[(b[k>>2]|0)+4>>0]=0;break}else{a[(b[k>>2]|0)+4>>0]=i>>>(32-g|0);break}}while(0);f=(h|0)/8|0;b[d>>2]=(b[d>>2]|0)+f;b[k>>2]=(b[k>>2]|0)+f;b[j>>2]=h&7;return}while(0);g=b[d+8>>2]|0;if(g|0)fc(g);b[d>>2]=0;b[d+4>>2]=0;b[d+8>>2]=0;b[d+12>>2]=0;b[d+16>>2]=0;return}function _b(a){a=a|0;var c=0;c=b[a+8>>2]|0;if(c|0)fc(c);b[a>>2]=0;b[a+4>>2]=0;b[a+8>>2]=0;b[a+12>>2]=0;b[a+16>>2]=0;return}function $b(a,c,d){a=a|0;c=c|0;d=d|0;var e=0;e=a;b[e>>2]=0;b[e+4>>2]=0;b[a+12>>2]=c;b[a+8>>2]=c;b[a+16>>2]=d;return}function ac(a,d){a=a|0;d=d|0;var e=0,f=0,g=0,h=0;if(d>>>0>32){h=-1;return h|0}h=b[57088+(d<<2)>>2]|0;g=b[a+4>>2]|0;f=g+d|0;e=b[a>>2]|0;d=b[a+16>>2]|0;if((e|0)>=(d+-4|0)){if((e|0)>(d-(f+7>>3)|0)){h=-1;return h|0}if(!f){h=0;return h|0}}e=b[a+12>>2]|0;d=(c[e>>0]|0)>>>g;if((f|0)>8){d=(c[e+1>>0]|0)<<8-g|d;if((f|0)>16){d=(c[e+2>>0]|0)<<16-g|d;if((f|0)>24){d=(c[e+3>>0]|0)<<24-g|d;if(!((g|0)==0|(f|0)<33))d=(c[e+4>>0]|0)<<32-g|d}}}h=d&h;return h|0}function bc(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0;f=a+4|0;c=(b[f>>2]|0)+c|0;d=b[a>>2]|0;e=b[a+16>>2]|0;if((d|0)>(e-(c+7>>3)|0)){b[a+12>>2]=0;b[a>>2]=e;a=1;b[f>>2]=a;return}else{e=(c|0)/8|0;g=a+12|0;b[g>>2]=(b[g>>2]|0)+e;b[a>>2]=e+d;a=c&7;b[f>>2]=a;return}}function cc(a,d){a=a|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;do if(d>>>0>32){f=a;e=a+4|0;d=b[a+16>>2]|0}else{k=b[57088+(d<<2)>>2]|0;e=a+4|0;h=b[e>>2]|0;i=h+d|0;j=b[a>>2]|0;d=b[a+16>>2]|0;if((j|0)>=(d+-4|0)){if((j|0)>(d-(i+7>>3)|0)){f=a;break}if(!i){a=0;return a|0}}f=a+12|0;g=b[f>>2]|0;d=(c[g>>0]|0)>>>h;if((i|0)>8){d=(c[g+1>>0]|0)<<8-h|d;if((i|0)>16){d=(c[g+2>>0]|0)<<16-h|d;if((i|0)>24){d=(c[g+3>>0]|0)<<24-h|d;if(!((h|0)==0|(i|0)<33))d=(c[g+4>>0]|0)<<32-h|d}}}h=(i|0)/8|0;b[f>>2]=g+h;b[a>>2]=h+j;b[e>>2]=i&7;a=d&k;return a|0}while(0);b[a+12>>2]=0;b[f>>2]=d;b[e>>2]=1;a=-1;return a|0}function dc(a){a=a|0;return (((b[a+4>>2]|0)+7|0)/8|0)+(b[a>>2]|0)|0}function ec(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;w=L;L=L+16|0;n=w;do if(a>>>0<245){k=a>>>0<11?16:a+11&-8;a=k>>>3;m=b[14429]|0;d=m>>>a;if(d&3|0){c=(d&1^1)+a|0;a=57756+(c<<1<<2)|0;d=a+8|0;e=b[d>>2]|0;f=e+8|0;g=b[f>>2]|0;if((g|0)==(a|0))b[14429]=m&~(1<<c);else{b[g+12>>2]=a;b[d>>2]=g}v=c<<3;b[e+4>>2]=v|3;v=e+v+4|0;b[v>>2]=b[v>>2]|1;v=f;L=w;return v|0}l=b[14431]|0;if(k>>>0>l>>>0){if(d|0){c=2<<a;c=d<<a&(c|0-c);c=(c&0-c)+-1|0;i=c>>>12&16;c=c>>>i;d=c>>>5&8;c=c>>>d;g=c>>>2&4;c=c>>>g;a=c>>>1&2;c=c>>>a;e=c>>>1&1;e=(d|i|g|a|e)+(c>>>e)|0;c=57756+(e<<1<<2)|0;a=c+8|0;g=b[a>>2]|0;i=g+8|0;d=b[i>>2]|0;if((d|0)==(c|0)){a=m&~(1<<e);b[14429]=a}else{b[d+12>>2]=c;b[a>>2]=d;a=m}v=e<<3;h=v-k|0;b[g+4>>2]=k|3;f=g+k|0;b[f+4>>2]=h|1;b[g+v>>2]=h;if(l|0){e=b[14434]|0;c=l>>>3;d=57756+(c<<1<<2)|0;c=1<<c;if(!(a&c)){b[14429]=a|c;c=d;a=d+8|0}else{a=d+8|0;c=b[a>>2]|0}b[a>>2]=e;b[c+12>>2]=e;b[e+8>>2]=c;b[e+12>>2]=d}b[14431]=h;b[14434]=f;v=i;L=w;return v|0}g=b[14430]|0;if(g){d=(g&0-g)+-1|0;f=d>>>12&16;d=d>>>f;e=d>>>5&8;d=d>>>e;h=d>>>2&4;d=d>>>h;i=d>>>1&2;d=d>>>i;j=d>>>1&1;j=b[58020+((e|f|h|i|j)+(d>>>j)<<2)>>2]|0;d=j;i=j;j=(b[j+4>>2]&-8)-k|0;while(1){a=b[d+16>>2]|0;if(!a){a=b[d+20>>2]|0;if(!a)break}h=(b[a+4>>2]&-8)-k|0;f=h>>>0<j>>>0;d=a;i=f?a:i;j=f?h:j}h=i+k|0;if(h>>>0>i>>>0){f=b[i+24>>2]|0;c=b[i+12>>2]|0;do if((c|0)==(i|0)){a=i+20|0;c=b[a>>2]|0;if(!c){a=i+16|0;c=b[a>>2]|0;if(!c){d=0;break}}while(1){e=c+20|0;d=b[e>>2]|0;if(!d){e=c+16|0;d=b[e>>2]|0;if(!d)break;else{c=d;a=e}}else{c=d;a=e}}b[a>>2]=0;d=c}else{d=b[i+8>>2]|0;b[d+12>>2]=c;b[c+8>>2]=d;d=c}while(0);do if(f|0){c=b[i+28>>2]|0;a=58020+(c<<2)|0;if((i|0)==(b[a>>2]|0)){b[a>>2]=d;if(!d){b[14430]=g&~(1<<c);break}}else{v=f+16|0;b[((b[v>>2]|0)==(i|0)?v:f+20|0)>>2]=d;if(!d)break}b[d+24>>2]=f;c=b[i+16>>2]|0;if(c|0){b[d+16>>2]=c;b[c+24>>2]=d}c=b[i+20>>2]|0;if(c|0){b[d+20>>2]=c;b[c+24>>2]=d}}while(0);if(j>>>0<16){v=j+k|0;b[i+4>>2]=v|3;v=i+v+4|0;b[v>>2]=b[v>>2]|1}else{b[i+4>>2]=k|3;b[h+4>>2]=j|1;b[h+j>>2]=j;if(l|0){e=b[14434]|0;c=l>>>3;d=57756+(c<<1<<2)|0;c=1<<c;if(!(c&m)){b[14429]=c|m;c=d;a=d+8|0}else{a=d+8|0;c=b[a>>2]|0}b[a>>2]=e;b[c+12>>2]=e;b[e+8>>2]=c;b[e+12>>2]=d}b[14431]=j;b[14434]=h}v=i+8|0;L=w;return v|0}else m=k}else m=k}else m=k}else if(a>>>0<=4294967231){a=a+11|0;k=a&-8;e=b[14430]|0;if(e){f=0-k|0;a=a>>>8;if(a)if(k>>>0>16777215)j=31;else{m=(a+1048320|0)>>>16&8;q=a<<m;i=(q+520192|0)>>>16&4;q=q<<i;j=(q+245760|0)>>>16&2;j=14-(i|m|j)+(q<<j>>>15)|0;j=k>>>(j+7|0)&1|j<<1}else j=0;d=b[58020+(j<<2)>>2]|0;a:do if(!d){d=0;a=0;q=61}else{a=0;i=k<<((j|0)==31?0:25-(j>>>1)|0);g=0;while(1){h=(b[d+4>>2]&-8)-k|0;if(h>>>0<f>>>0)if(!h){a=d;f=0;q=65;break a}else{a=d;f=h}q=b[d+20>>2]|0;d=b[d+16+(i>>>31<<2)>>2]|0;g=(q|0)==0|(q|0)==(d|0)?g:q;if(!d){d=g;q=61;break}else i=i<<1}}while(0);if((q|0)==61){if((d|0)==0&(a|0)==0){a=2<<j;a=(a|0-a)&e;if(!a){m=k;break}m=(a&0-a)+-1|0;h=m>>>12&16;m=m>>>h;g=m>>>5&8;m=m>>>g;i=m>>>2&4;m=m>>>i;j=m>>>1&2;m=m>>>j;d=m>>>1&1;a=0;d=b[58020+((g|h|i|j|d)+(m>>>d)<<2)>>2]|0}if(!d){i=a;h=f}else q=65}if((q|0)==65){g=d;while(1){m=(b[g+4>>2]&-8)-k|0;d=m>>>0<f>>>0;f=d?m:f;a=d?g:a;d=b[g+16>>2]|0;if(!d)d=b[g+20>>2]|0;if(!d){i=a;h=f;break}else g=d}}if(((i|0)!=0?h>>>0<((b[14431]|0)-k|0)>>>0:0)?(l=i+k|0,l>>>0>i>>>0):0){g=b[i+24>>2]|0;c=b[i+12>>2]|0;do if((c|0)==(i|0)){a=i+20|0;c=b[a>>2]|0;if(!c){a=i+16|0;c=b[a>>2]|0;if(!c){c=0;break}}while(1){f=c+20|0;d=b[f>>2]|0;if(!d){f=c+16|0;d=b[f>>2]|0;if(!d)break;else{c=d;a=f}}else{c=d;a=f}}b[a>>2]=0}else{v=b[i+8>>2]|0;b[v+12>>2]=c;b[c+8>>2]=v}while(0);do if(g){a=b[i+28>>2]|0;d=58020+(a<<2)|0;if((i|0)==(b[d>>2]|0)){b[d>>2]=c;if(!c){e=e&~(1<<a);b[14430]=e;break}}else{v=g+16|0;b[((b[v>>2]|0)==(i|0)?v:g+20|0)>>2]=c;if(!c)break}b[c+24>>2]=g;a=b[i+16>>2]|0;if(a|0){b[c+16>>2]=a;b[a+24>>2]=c}a=b[i+20>>2]|0;if(a){b[c+20>>2]=a;b[a+24>>2]=c}}while(0);b:do if(h>>>0<16){v=h+k|0;b[i+4>>2]=v|3;v=i+v+4|0;b[v>>2]=b[v>>2]|1}else{b[i+4>>2]=k|3;b[l+4>>2]=h|1;b[l+h>>2]=h;c=h>>>3;if(h>>>0<256){d=57756+(c<<1<<2)|0;a=b[14429]|0;c=1<<c;if(!(a&c)){b[14429]=a|c;c=d;a=d+8|0}else{a=d+8|0;c=b[a>>2]|0}b[a>>2]=l;b[c+12>>2]=l;b[l+8>>2]=c;b[l+12>>2]=d;break}c=h>>>8;if(c)if(h>>>0>16777215)d=31;else{u=(c+1048320|0)>>>16&8;v=c<<u;t=(v+520192|0)>>>16&4;v=v<<t;d=(v+245760|0)>>>16&2;d=14-(t|u|d)+(v<<d>>>15)|0;d=h>>>(d+7|0)&1|d<<1}else d=0;c=58020+(d<<2)|0;b[l+28>>2]=d;a=l+16|0;b[a+4>>2]=0;b[a>>2]=0;a=1<<d;if(!(e&a)){b[14430]=e|a;b[c>>2]=l;b[l+24>>2]=c;b[l+12>>2]=l;b[l+8>>2]=l;break}c=b[c>>2]|0;c:do if((b[c+4>>2]&-8|0)!=(h|0)){e=h<<((d|0)==31?0:25-(d>>>1)|0);while(1){d=c+16+(e>>>31<<2)|0;a=b[d>>2]|0;if(!a)break;if((b[a+4>>2]&-8|0)==(h|0)){c=a;break c}else{e=e<<1;c=a}}b[d>>2]=l;b[l+24>>2]=c;b[l+12>>2]=l;b[l+8>>2]=l;break b}while(0);u=c+8|0;v=b[u>>2]|0;b[v+12>>2]=l;b[u>>2]=l;b[l+8>>2]=v;b[l+12>>2]=c;b[l+24>>2]=0}while(0);v=i+8|0;L=w;return v|0}else m=k}else m=k}else m=-1;while(0);d=b[14431]|0;if(d>>>0>=m>>>0){c=d-m|0;a=b[14434]|0;if(c>>>0>15){v=a+m|0;b[14434]=v;b[14431]=c;b[v+4>>2]=c|1;b[a+d>>2]=c;b[a+4>>2]=m|3}else{b[14431]=0;b[14434]=0;b[a+4>>2]=d|3;v=a+d+4|0;b[v>>2]=b[v>>2]|1}v=a+8|0;L=w;return v|0}h=b[14432]|0;if(h>>>0>m>>>0){t=h-m|0;b[14432]=t;v=b[14435]|0;u=v+m|0;b[14435]=u;b[u+4>>2]=t|1;b[v+4>>2]=m|3;v=v+8|0;L=w;return v|0}if(!(b[14547]|0)){b[14549]=4096;b[14548]=4096;b[14550]=-1;b[14551]=-1;b[14552]=0;b[14540]=0;b[14547]=n&-16^1431655768;a=4096}else a=b[14549]|0;i=m+48|0;j=m+47|0;g=a+j|0;f=0-a|0;k=g&f;if(k>>>0<=m>>>0){v=0;L=w;return v|0}a=b[14539]|0;if(a|0?(l=b[14537]|0,n=l+k|0,n>>>0<=l>>>0|n>>>0>a>>>0):0){v=0;L=w;return v|0}d:do if(!(b[14540]&4)){d=b[14435]|0;e:do if(d){e=58164;while(1){n=b[e>>2]|0;if(n>>>0<=d>>>0?(n+(b[e+4>>2]|0)|0)>>>0>d>>>0:0)break;a=b[e+8>>2]|0;if(!a){q=128;break e}else e=a}c=g-h&f;if(c>>>0<2147483647){a=Gc(c|0)|0;if((a|0)==((b[e>>2]|0)+(b[e+4>>2]|0)|0)){if((a|0)!=(-1|0)){h=c;g=a;q=145;break d}}else{e=a;q=136}}else c=0}else q=128;while(0);do if((q|0)==128){d=Gc(0)|0;if((d|0)!=(-1|0)?(c=d,o=b[14548]|0,p=o+-1|0,c=((p&c|0)==0?0:(p+c&0-o)-c|0)+k|0,o=b[14537]|0,p=c+o|0,c>>>0>m>>>0&c>>>0<2147483647):0){n=b[14539]|0;if(n|0?p>>>0<=o>>>0|p>>>0>n>>>0:0){c=0;break}a=Gc(c|0)|0;if((a|0)==(d|0)){h=c;g=d;q=145;break d}else{e=a;q=136}}else c=0}while(0);do if((q|0)==136){d=0-c|0;if(!(i>>>0>c>>>0&(c>>>0<2147483647&(e|0)!=(-1|0))))if((e|0)==(-1|0)){c=0;break}else{h=c;g=e;q=145;break d}a=b[14549]|0;a=j-c+a&0-a;if(a>>>0>=2147483647){h=c;g=e;q=145;break d}if((Gc(a|0)|0)==(-1|0)){Gc(d|0)|0;c=0;break}else{h=a+c|0;g=e;q=145;break d}}while(0);b[14540]=b[14540]|4;q=143}else{c=0;q=143}while(0);if(((q|0)==143?k>>>0<2147483647:0)?(t=Gc(k|0)|0,p=Gc(0)|0,r=p-t|0,s=r>>>0>(m+40|0)>>>0,!((t|0)==(-1|0)|s^1|t>>>0<p>>>0&((t|0)!=(-1|0)&(p|0)!=(-1|0))^1)):0){h=s?r:c;g=t;q=145}if((q|0)==145){c=(b[14537]|0)+h|0;b[14537]=c;if(c>>>0>(b[14538]|0)>>>0)b[14538]=c;j=b[14435]|0;f:do if(j){c=58164;while(1){a=b[c>>2]|0;d=b[c+4>>2]|0;if((g|0)==(a+d|0)){q=154;break}e=b[c+8>>2]|0;if(!e)break;else c=e}if(((q|0)==154?(u=c+4|0,(b[c+12>>2]&8|0)==0):0)?g>>>0>j>>>0&a>>>0<=j>>>0:0){b[u>>2]=d+h;v=(b[14432]|0)+h|0;t=j+8|0;t=(t&7|0)==0?0:0-t&7;u=j+t|0;t=v-t|0;b[14435]=u;b[14432]=t;b[u+4>>2]=t|1;b[j+v+4>>2]=40;b[14436]=b[14551];break}if(g>>>0<(b[14433]|0)>>>0)b[14433]=g;d=g+h|0;c=58164;while(1){if((b[c>>2]|0)==(d|0)){q=162;break}a=b[c+8>>2]|0;if(!a)break;else c=a}if((q|0)==162?(b[c+12>>2]&8|0)==0:0){b[c>>2]=g;l=c+4|0;b[l>>2]=(b[l>>2]|0)+h;l=g+8|0;l=g+((l&7|0)==0?0:0-l&7)|0;c=d+8|0;c=d+((c&7|0)==0?0:0-c&7)|0;k=l+m|0;i=c-l-m|0;b[l+4>>2]=m|3;g:do if((j|0)==(c|0)){v=(b[14432]|0)+i|0;b[14432]=v;b[14435]=k;b[k+4>>2]=v|1}else{if((b[14434]|0)==(c|0)){v=(b[14431]|0)+i|0;b[14431]=v;b[14434]=k;b[k+4>>2]=v|1;b[k+v>>2]=v;break}a=b[c+4>>2]|0;if((a&3|0)==1){h=a&-8;e=a>>>3;h:do if(a>>>0<256){a=b[c+8>>2]|0;d=b[c+12>>2]|0;if((d|0)==(a|0)){b[14429]=b[14429]&~(1<<e);break}else{b[a+12>>2]=d;b[d+8>>2]=a;break}}else{g=b[c+24>>2]|0;a=b[c+12>>2]|0;do if((a|0)==(c|0)){d=c+16|0;e=d+4|0;a=b[e>>2]|0;if(!a){a=b[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=b[f>>2]|0;if(!e){f=a+16|0;e=b[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}b[d>>2]=0}else{v=b[c+8>>2]|0;b[v+12>>2]=a;b[a+8>>2]=v}while(0);if(!g)break;d=b[c+28>>2]|0;e=58020+(d<<2)|0;do if((b[e>>2]|0)!=(c|0)){v=g+16|0;b[((b[v>>2]|0)==(c|0)?v:g+20|0)>>2]=a;if(!a)break h}else{b[e>>2]=a;if(a|0)break;b[14430]=b[14430]&~(1<<d);break h}while(0);b[a+24>>2]=g;d=c+16|0;e=b[d>>2]|0;if(e|0){b[a+16>>2]=e;b[e+24>>2]=a}d=b[d+4>>2]|0;if(!d)break;b[a+20>>2]=d;b[d+24>>2]=a}while(0);c=c+h|0;f=h+i|0}else f=i;c=c+4|0;b[c>>2]=b[c>>2]&-2;b[k+4>>2]=f|1;b[k+f>>2]=f;c=f>>>3;if(f>>>0<256){d=57756+(c<<1<<2)|0;a=b[14429]|0;c=1<<c;if(!(a&c)){b[14429]=a|c;c=d;a=d+8|0}else{a=d+8|0;c=b[a>>2]|0}b[a>>2]=k;b[c+12>>2]=k;b[k+8>>2]=c;b[k+12>>2]=d;break}c=f>>>8;do if(!c)e=0;else{if(f>>>0>16777215){e=31;break}u=(c+1048320|0)>>>16&8;v=c<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}while(0);c=58020+(e<<2)|0;b[k+28>>2]=e;a=k+16|0;b[a+4>>2]=0;b[a>>2]=0;a=b[14430]|0;d=1<<e;if(!(a&d)){b[14430]=a|d;b[c>>2]=k;b[k+24>>2]=c;b[k+12>>2]=k;b[k+8>>2]=k;break}c=b[c>>2]|0;i:do if((b[c+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=c+16+(e>>>31<<2)|0;a=b[d>>2]|0;if(!a)break;if((b[a+4>>2]&-8|0)==(f|0)){c=a;break i}else{e=e<<1;c=a}}b[d>>2]=k;b[k+24>>2]=c;b[k+12>>2]=k;b[k+8>>2]=k;break g}while(0);u=c+8|0;v=b[u>>2]|0;b[v+12>>2]=k;b[u>>2]=k;b[k+8>>2]=v;b[k+12>>2]=c;b[k+24>>2]=0}while(0);v=l+8|0;L=w;return v|0}c=58164;while(1){a=b[c>>2]|0;if(a>>>0<=j>>>0?(v=a+(b[c+4>>2]|0)|0,v>>>0>j>>>0):0)break;c=b[c+8>>2]|0}f=v+-47|0;a=f+8|0;a=f+((a&7|0)==0?0:0-a&7)|0;f=j+16|0;a=a>>>0<f>>>0?j:a;c=a+8|0;d=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=d-t|0;b[14435]=u;b[14432]=t;b[u+4>>2]=t|1;b[g+d+4>>2]=40;b[14436]=b[14551];d=a+4|0;b[d>>2]=27;b[c>>2]=b[14541];b[c+4>>2]=b[14542];b[c+8>>2]=b[14543];b[c+12>>2]=b[14544];b[14541]=g;b[14542]=h;b[14544]=0;b[14543]=c;c=a+24|0;do{u=c;c=c+4|0;b[c>>2]=7}while((u+8|0)>>>0<v>>>0);if((a|0)!=(j|0)){g=a-j|0;b[d>>2]=b[d>>2]&-2;b[j+4>>2]=g|1;b[a>>2]=g;c=g>>>3;if(g>>>0<256){d=57756+(c<<1<<2)|0;a=b[14429]|0;c=1<<c;if(!(a&c)){b[14429]=a|c;c=d;a=d+8|0}else{a=d+8|0;c=b[a>>2]|0}b[a>>2]=j;b[c+12>>2]=j;b[j+8>>2]=c;b[j+12>>2]=d;break}c=g>>>8;if(c)if(g>>>0>16777215)e=31;else{u=(c+1048320|0)>>>16&8;v=c<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=g>>>(e+7|0)&1|e<<1}else e=0;d=58020+(e<<2)|0;b[j+28>>2]=e;b[j+20>>2]=0;b[f>>2]=0;c=b[14430]|0;a=1<<e;if(!(c&a)){b[14430]=c|a;b[d>>2]=j;b[j+24>>2]=d;b[j+12>>2]=j;b[j+8>>2]=j;break}c=b[d>>2]|0;j:do if((b[c+4>>2]&-8|0)!=(g|0)){e=g<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=c+16+(e>>>31<<2)|0;a=b[d>>2]|0;if(!a)break;if((b[a+4>>2]&-8|0)==(g|0)){c=a;break j}else{e=e<<1;c=a}}b[d>>2]=j;b[j+24>>2]=c;b[j+12>>2]=j;b[j+8>>2]=j;break f}while(0);u=c+8|0;v=b[u>>2]|0;b[v+12>>2]=j;b[u>>2]=j;b[j+8>>2]=v;b[j+12>>2]=c;b[j+24>>2]=0}}else{v=b[14433]|0;if((v|0)==0|g>>>0<v>>>0)b[14433]=g;b[14541]=g;b[14542]=h;b[14544]=0;b[14438]=b[14547];b[14437]=-1;b[14442]=57756;b[14441]=57756;b[14444]=57764;b[14443]=57764;b[14446]=57772;b[14445]=57772;b[14448]=57780;b[14447]=57780;b[14450]=57788;b[14449]=57788;b[14452]=57796;b[14451]=57796;b[14454]=57804;b[14453]=57804;b[14456]=57812;b[14455]=57812;b[14458]=57820;b[14457]=57820;b[14460]=57828;b[14459]=57828;b[14462]=57836;b[14461]=57836;b[14464]=57844;b[14463]=57844;b[14466]=57852;b[14465]=57852;b[14468]=57860;b[14467]=57860;b[14470]=57868;b[14469]=57868;b[14472]=57876;b[14471]=57876;b[14474]=57884;b[14473]=57884;b[14476]=57892;b[14475]=57892;b[14478]=57900;b[14477]=57900;b[14480]=57908;b[14479]=57908;b[14482]=57916;b[14481]=57916;b[14484]=57924;b[14483]=57924;b[14486]=57932;b[14485]=57932;b[14488]=57940;b[14487]=57940;b[14490]=57948;b[14489]=57948;b[14492]=57956;b[14491]=57956;b[14494]=57964;b[14493]=57964;b[14496]=57972;b[14495]=57972;b[14498]=57980;b[14497]=57980;b[14500]=57988;b[14499]=57988;b[14502]=57996;b[14501]=57996;b[14504]=58004;b[14503]=58004;v=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=v-t|0;b[14435]=u;b[14432]=t;b[u+4>>2]=t|1;b[g+v+4>>2]=40;b[14436]=b[14551]}while(0);c=b[14432]|0;if(c>>>0>m>>>0){t=c-m|0;b[14432]=t;v=b[14435]|0;u=v+m|0;b[14435]=u;b[u+4>>2]=t|1;b[v+4>>2]=m|3;v=v+8|0;L=w;return v|0}}b[(kc()|0)>>2]=12;v=0;L=w;return v|0}function fc(a){a=a|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!a)return;d=a+-8|0;f=b[14433]|0;a=b[a+-4>>2]|0;c=a&-8;j=d+c|0;do if(!(a&1)){e=b[d>>2]|0;if(!(a&3))return;h=d+(0-e)|0;g=e+c|0;if(h>>>0<f>>>0)return;if((b[14434]|0)==(h|0)){a=j+4|0;c=b[a>>2]|0;if((c&3|0)!=3){i=h;c=g;break}b[14431]=g;b[a>>2]=c&-2;b[h+4>>2]=g|1;b[h+g>>2]=g;return}d=e>>>3;if(e>>>0<256){a=b[h+8>>2]|0;c=b[h+12>>2]|0;if((c|0)==(a|0)){b[14429]=b[14429]&~(1<<d);i=h;c=g;break}else{b[a+12>>2]=c;b[c+8>>2]=a;i=h;c=g;break}}f=b[h+24>>2]|0;a=b[h+12>>2]|0;do if((a|0)==(h|0)){c=h+16|0;d=c+4|0;a=b[d>>2]|0;if(!a){a=b[c>>2]|0;if(!a){a=0;break}}else c=d;while(1){e=a+20|0;d=b[e>>2]|0;if(!d){e=a+16|0;d=b[e>>2]|0;if(!d)break;else{a=d;c=e}}else{a=d;c=e}}b[c>>2]=0}else{i=b[h+8>>2]|0;b[i+12>>2]=a;b[a+8>>2]=i}while(0);if(f){c=b[h+28>>2]|0;d=58020+(c<<2)|0;if((b[d>>2]|0)==(h|0)){b[d>>2]=a;if(!a){b[14430]=b[14430]&~(1<<c);i=h;c=g;break}}else{i=f+16|0;b[((b[i>>2]|0)==(h|0)?i:f+20|0)>>2]=a;if(!a){i=h;c=g;break}}b[a+24>>2]=f;c=h+16|0;d=b[c>>2]|0;if(d|0){b[a+16>>2]=d;b[d+24>>2]=a}c=b[c+4>>2]|0;if(c){b[a+20>>2]=c;b[c+24>>2]=a;i=h;c=g}else{i=h;c=g}}else{i=h;c=g}}else{i=d;h=d}while(0);if(h>>>0>=j>>>0)return;a=j+4|0;e=b[a>>2]|0;if(!(e&1))return;if(!(e&2)){if((b[14435]|0)==(j|0)){j=(b[14432]|0)+c|0;b[14432]=j;b[14435]=i;b[i+4>>2]=j|1;if((i|0)!=(b[14434]|0))return;b[14434]=0;b[14431]=0;return}if((b[14434]|0)==(j|0)){j=(b[14431]|0)+c|0;b[14431]=j;b[14434]=h;b[i+4>>2]=j|1;b[h+j>>2]=j;return}f=(e&-8)+c|0;d=e>>>3;do if(e>>>0<256){c=b[j+8>>2]|0;a=b[j+12>>2]|0;if((a|0)==(c|0)){b[14429]=b[14429]&~(1<<d);break}else{b[c+12>>2]=a;b[a+8>>2]=c;break}}else{g=b[j+24>>2]|0;a=b[j+12>>2]|0;do if((a|0)==(j|0)){c=j+16|0;d=c+4|0;a=b[d>>2]|0;if(!a){a=b[c>>2]|0;if(!a){d=0;break}}else c=d;while(1){e=a+20|0;d=b[e>>2]|0;if(!d){e=a+16|0;d=b[e>>2]|0;if(!d)break;else{a=d;c=e}}else{a=d;c=e}}b[c>>2]=0;d=a}else{d=b[j+8>>2]|0;b[d+12>>2]=a;b[a+8>>2]=d;d=a}while(0);if(g|0){a=b[j+28>>2]|0;c=58020+(a<<2)|0;if((b[c>>2]|0)==(j|0)){b[c>>2]=d;if(!d){b[14430]=b[14430]&~(1<<a);break}}else{e=g+16|0;b[((b[e>>2]|0)==(j|0)?e:g+20|0)>>2]=d;if(!d)break}b[d+24>>2]=g;a=j+16|0;c=b[a>>2]|0;if(c|0){b[d+16>>2]=c;b[c+24>>2]=d}a=b[a+4>>2]|0;if(a|0){b[d+20>>2]=a;b[a+24>>2]=d}}}while(0);b[i+4>>2]=f|1;b[h+f>>2]=f;if((i|0)==(b[14434]|0)){b[14431]=f;return}}else{b[a>>2]=e&-2;b[i+4>>2]=c|1;b[h+c>>2]=c;f=c}a=f>>>3;if(f>>>0<256){d=57756+(a<<1<<2)|0;c=b[14429]|0;a=1<<a;if(!(c&a)){b[14429]=c|a;a=d;c=d+8|0}else{c=d+8|0;a=b[c>>2]|0}b[c>>2]=i;b[a+12>>2]=i;b[i+8>>2]=a;b[i+12>>2]=d;return}a=f>>>8;if(a)if(f>>>0>16777215)e=31;else{h=(a+1048320|0)>>>16&8;j=a<<h;g=(j+520192|0)>>>16&4;j=j<<g;e=(j+245760|0)>>>16&2;e=14-(g|h|e)+(j<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;a=58020+(e<<2)|0;b[i+28>>2]=e;b[i+20>>2]=0;b[i+16>>2]=0;c=b[14430]|0;d=1<<e;a:do if(!(c&d)){b[14430]=c|d;b[a>>2]=i;b[i+24>>2]=a;b[i+12>>2]=i;b[i+8>>2]=i}else{a=b[a>>2]|0;b:do if((b[a+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;c=b[d>>2]|0;if(!c)break;if((b[c+4>>2]&-8|0)==(f|0)){a=c;break b}else{e=e<<1;a=c}}b[d>>2]=i;b[i+24>>2]=a;b[i+12>>2]=i;b[i+8>>2]=i;break a}while(0);h=a+8|0;j=b[h>>2]|0;b[j+12>>2]=i;b[h>>2]=i;b[i+8>>2]=j;b[i+12>>2]=a;b[i+24>>2]=0}while(0);j=(b[14437]|0)+-1|0;b[14437]=j;if(j|0)return;a=58172;while(1){a=b[a>>2]|0;if(!a)break;else a=a+8|0}b[14437]=-1;return}function gc(a,c){a=a|0;c=c|0;var d=0;if(a){d=z(c,a)|0;if((c|a)>>>0>65535)d=((d>>>0)/(a>>>0)|0|0)==(c|0)?d:-1}else d=0;a=ec(d)|0;if(!a)return a|0;if(!(b[a+-4>>2]&3))return a|0;Fc(a|0,0,d|0)|0;return a|0}function hc(a,c){a=a|0;c=c|0;var d=0,e=0;if(!a){c=ec(c)|0;return c|0}if(c>>>0>4294967231){b[(kc()|0)>>2]=12;c=0;return c|0}d=ic(a+-8|0,c>>>0<11?16:c+11&-8)|0;if(d|0){c=d+8|0;return c|0}d=ec(c)|0;if(!d){c=0;return c|0}e=b[a+-4>>2]|0;e=(e&-8)-((e&3|0)==0?8:4)|0;Ec(d|0,a|0,(e>>>0<c>>>0?e:c)|0)|0;fc(a);c=d;return c|0}function ic(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=a+4|0;m=b[l>>2]|0;d=m&-8;i=a+d|0;if(!(m&3)){if(c>>>0<256){a=0;return a|0}if(d>>>0>=(c+4|0)>>>0?(d-c|0)>>>0<=b[14549]<<1>>>0:0)return a|0;a=0;return a|0}if(d>>>0>=c>>>0){d=d-c|0;if(d>>>0<=15)return a|0;k=a+c|0;b[l>>2]=m&1|c|2;b[k+4>>2]=d|3;m=i+4|0;b[m>>2]=b[m>>2]|1;jc(k,d);return a|0}if((b[14435]|0)==(i|0)){k=(b[14432]|0)+d|0;d=k-c|0;e=a+c|0;if(k>>>0<=c>>>0){a=0;return a|0}b[l>>2]=m&1|c|2;b[e+4>>2]=d|1;b[14435]=e;b[14432]=d;return a|0}if((b[14434]|0)==(i|0)){e=(b[14431]|0)+d|0;if(e>>>0<c>>>0){a=0;return a|0}d=e-c|0;if(d>>>0>15){k=a+c|0;e=a+e|0;b[l>>2]=m&1|c|2;b[k+4>>2]=d|1;b[e>>2]=d;e=e+4|0;b[e>>2]=b[e>>2]&-2;e=k}else{b[l>>2]=m&1|e|2;e=a+e+4|0;b[e>>2]=b[e>>2]|1;e=0;d=0}b[14431]=d;b[14434]=e;return a|0}e=b[i+4>>2]|0;if(e&2|0){a=0;return a|0}j=(e&-8)+d|0;if(j>>>0<c>>>0){a=0;return a|0}k=j-c|0;f=e>>>3;do if(e>>>0<256){e=b[i+8>>2]|0;d=b[i+12>>2]|0;if((d|0)==(e|0)){b[14429]=b[14429]&~(1<<f);break}else{b[e+12>>2]=d;b[d+8>>2]=e;break}}else{h=b[i+24>>2]|0;d=b[i+12>>2]|0;do if((d|0)==(i|0)){e=i+16|0;f=e+4|0;d=b[f>>2]|0;if(!d){d=b[e>>2]|0;if(!d){f=0;break}}else e=f;while(1){g=d+20|0;f=b[g>>2]|0;if(!f){g=d+16|0;f=b[g>>2]|0;if(!f)break;else{d=f;e=g}}else{d=f;e=g}}b[e>>2]=0;f=d}else{f=b[i+8>>2]|0;b[f+12>>2]=d;b[d+8>>2]=f;f=d}while(0);if(h|0){d=b[i+28>>2]|0;e=58020+(d<<2)|0;if((b[e>>2]|0)==(i|0)){b[e>>2]=f;if(!f){b[14430]=b[14430]&~(1<<d);break}}else{g=h+16|0;b[((b[g>>2]|0)==(i|0)?g:h+20|0)>>2]=f;if(!f)break}b[f+24>>2]=h;d=i+16|0;e=b[d>>2]|0;if(e|0){b[f+16>>2]=e;b[e+24>>2]=f}d=b[d+4>>2]|0;if(d|0){b[f+20>>2]=d;b[d+24>>2]=f}}}while(0);if(k>>>0<16){b[l>>2]=m&1|j|2;m=a+j+4|0;b[m>>2]=b[m>>2]|1;return a|0}else{i=a+c|0;b[l>>2]=m&1|c|2;b[i+4>>2]=k|3;m=a+j+4|0;b[m>>2]=b[m>>2]|1;jc(i,k);return a|0}return 0}function jc(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;i=a+c|0;d=b[a+4>>2]|0;do if(!(d&1)){f=b[a>>2]|0;if(!(d&3))return;h=a+(0-f)|0;c=f+c|0;if((b[14434]|0)==(h|0)){a=i+4|0;d=b[a>>2]|0;if((d&3|0)!=3)break;b[14431]=c;b[a>>2]=d&-2;b[h+4>>2]=c|1;b[i>>2]=c;return}e=f>>>3;if(f>>>0<256){a=b[h+8>>2]|0;d=b[h+12>>2]|0;if((d|0)==(a|0)){b[14429]=b[14429]&~(1<<e);break}else{b[a+12>>2]=d;b[d+8>>2]=a;break}}g=b[h+24>>2]|0;a=b[h+12>>2]|0;do if((a|0)==(h|0)){d=h+16|0;e=d+4|0;a=b[e>>2]|0;if(!a){a=b[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=b[f>>2]|0;if(!e){f=a+16|0;e=b[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}b[d>>2]=0}else{f=b[h+8>>2]|0;b[f+12>>2]=a;b[a+8>>2]=f}while(0);if(g){d=b[h+28>>2]|0;e=58020+(d<<2)|0;if((b[e>>2]|0)==(h|0)){b[e>>2]=a;if(!a){b[14430]=b[14430]&~(1<<d);break}}else{f=g+16|0;b[((b[f>>2]|0)==(h|0)?f:g+20|0)>>2]=a;if(!a)break}b[a+24>>2]=g;d=h+16|0;e=b[d>>2]|0;if(e|0){b[a+16>>2]=e;b[e+24>>2]=a}d=b[d+4>>2]|0;if(d){b[a+20>>2]=d;b[d+24>>2]=a}}}else h=a;while(0);a=i+4|0;e=b[a>>2]|0;if(!(e&2)){if((b[14435]|0)==(i|0)){i=(b[14432]|0)+c|0;b[14432]=i;b[14435]=h;b[h+4>>2]=i|1;if((h|0)!=(b[14434]|0))return;b[14434]=0;b[14431]=0;return}if((b[14434]|0)==(i|0)){i=(b[14431]|0)+c|0;b[14431]=i;b[14434]=h;b[h+4>>2]=i|1;b[h+i>>2]=i;return}f=(e&-8)+c|0;d=e>>>3;do if(e>>>0<256){a=b[i+8>>2]|0;c=b[i+12>>2]|0;if((c|0)==(a|0)){b[14429]=b[14429]&~(1<<d);break}else{b[a+12>>2]=c;b[c+8>>2]=a;break}}else{g=b[i+24>>2]|0;c=b[i+12>>2]|0;do if((c|0)==(i|0)){a=i+16|0;d=a+4|0;c=b[d>>2]|0;if(!c){c=b[a>>2]|0;if(!c){d=0;break}}else a=d;while(1){e=c+20|0;d=b[e>>2]|0;if(!d){e=c+16|0;d=b[e>>2]|0;if(!d)break;else{c=d;a=e}}else{c=d;a=e}}b[a>>2]=0;d=c}else{d=b[i+8>>2]|0;b[d+12>>2]=c;b[c+8>>2]=d;d=c}while(0);if(g|0){c=b[i+28>>2]|0;a=58020+(c<<2)|0;if((b[a>>2]|0)==(i|0)){b[a>>2]=d;if(!d){b[14430]=b[14430]&~(1<<c);break}}else{e=g+16|0;b[((b[e>>2]|0)==(i|0)?e:g+20|0)>>2]=d;if(!d)break}b[d+24>>2]=g;c=i+16|0;a=b[c>>2]|0;if(a|0){b[d+16>>2]=a;b[a+24>>2]=d}c=b[c+4>>2]|0;if(c|0){b[d+20>>2]=c;b[c+24>>2]=d}}}while(0);b[h+4>>2]=f|1;b[h+f>>2]=f;if((h|0)==(b[14434]|0)){b[14431]=f;return}}else{b[a>>2]=e&-2;b[h+4>>2]=c|1;b[h+c>>2]=c;f=c}c=f>>>3;if(f>>>0<256){d=57756+(c<<1<<2)|0;a=b[14429]|0;c=1<<c;if(!(a&c)){b[14429]=a|c;c=d;a=d+8|0}else{a=d+8|0;c=b[a>>2]|0}b[a>>2]=h;b[c+12>>2]=h;b[h+8>>2]=c;b[h+12>>2]=d;return}c=f>>>8;if(c)if(f>>>0>16777215)e=31;else{g=(c+1048320|0)>>>16&8;i=c<<g;d=(i+520192|0)>>>16&4;i=i<<d;e=(i+245760|0)>>>16&2;e=14-(d|g|e)+(i<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;c=58020+(e<<2)|0;b[h+28>>2]=e;b[h+20>>2]=0;b[h+16>>2]=0;a=b[14430]|0;d=1<<e;if(!(a&d)){b[14430]=a|d;b[c>>2]=h;b[h+24>>2]=c;b[h+12>>2]=h;b[h+8>>2]=h;return}c=b[c>>2]|0;a:do if((b[c+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=c+16+(e>>>31<<2)|0;a=b[d>>2]|0;if(!a)break;if((b[a+4>>2]&-8|0)==(f|0)){c=a;break a}else{e=e<<1;c=a}}b[d>>2]=h;b[h+24>>2]=c;b[h+12>>2]=h;b[h+8>>2]=h;return}while(0);g=c+8|0;i=b[g>>2]|0;b[i+12>>2]=h;b[g>>2]=h;b[h+8>>2]=i;b[h+12>>2]=c;b[h+24>>2]=0;return}function kc(){return 58212}function lc(a,c){a=+a;c=c|0;var d=0,g=0;if((c|0)<=1023){if((c|0)<-1022){a=a*2.2250738585072014e-308;g=(c|0)<-2044;d=c+2044|0;a=g?a*2.2250738585072014e-308:a;c=g?((d|0)>-1022?d:-1022):c+1022|0}}else{a=a*8988465674311579538646525.0e283;d=(c|0)>2046;g=c+-2046|0;a=d?a*8988465674311579538646525.0e283:a;c=d?((g|0)<1023?g:1023):c+-1023|0}d=wc(c+1023|0,0,52)|0;g=C()|0;b[f>>2]=d;b[f+4>>2]=g;return +(a*+e[f>>3])}function mc(b,c,d){b=b|0;c=c|0;d=d|0;var e=0,f=0;a:do if(!d)b=0;else{while(1){e=a[b>>0]|0;f=a[c>>0]|0;if(e<<24>>24!=f<<24>>24)break;d=d+-1|0;if(!d){b=0;break a}else{b=b+1|0;c=c+1|0}}b=(e&255)-(f&255)|0}while(0);return b|0}function nc(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=L;L=L+208|0;j=m;k=m+192|0;h=z(d,c)|0;i=k;b[i>>2]=1;b[i+4>>2]=0;a:do if(h|0){i=0-d|0;b[j+4>>2]=d;b[j>>2]=d;f=2;c=d;g=d;while(1){c=c+d+g|0;b[j+(f<<2)>>2]=c;if(c>>>0<h>>>0){n=g;f=f+1|0;g=c;c=n}else break}g=a+h+i|0;if(g>>>0>a>>>0){h=g;f=1;c=1;do{do if((c&3|0)!=3){c=f+-1|0;if((b[j+(c<<2)>>2]|0)>>>0<(h-a|0)>>>0)oc(a,d,e,f,j);else qc(a,d,e,k,f,0,j);if((f|0)==1){rc(k,1);f=0;break}else{rc(k,c);f=1;break}}else{oc(a,d,e,f,j);pc(k,2);f=f+2|0}while(0);c=b[k>>2]|1;b[k>>2]=c;a=a+d|0}while(a>>>0<g>>>0)}else{f=1;c=1}qc(a,d,e,k,f,0,j);g=k+4|0;while(1){if((f|0)==1&(c|0)==1)if(!(b[g>>2]|0))break a;else l=19;else if((f|0)<2)l=19;else{rc(k,2);n=f+-2|0;b[k>>2]=b[k>>2]^7;pc(k,1);qc(a+(0-(b[j+(n<<2)>>2]|0))+i|0,d,e,k,f+-1|0,1,j);rc(k,1);c=b[k>>2]|1;b[k>>2]=c;a=a+i|0;qc(a,d,e,k,n,1,j);f=n}if((l|0)==19){l=0;c=sc(k)|0;pc(k,c);a=a+i|0;f=c+f|0;c=b[k>>2]|0}}}while(0);L=m;return}function oc(a,c,d,e,f){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=L;L=L+240|0;l=m;b[l>>2]=a;a:do if((e|0)>1){k=0-c|0;i=a;g=e;e=1;h=a;while(1){i=i+k|0;j=g+-2|0;a=i+(0-(b[f+(j<<2)>>2]|0))|0;if((P[d&15](h,a)|0)>-1?(P[d&15](h,i)|0)>-1:0)break a;h=l+(e<<2)|0;if((P[d&15](a,i)|0)>-1){b[h>>2]=a;g=g+-1|0}else{b[h>>2]=i;a=i;g=j}e=e+1|0;if((g|0)<=1)break a;i=a;h=b[l>>2]|0}}else e=1;while(0);uc(c,l,e);L=m;return}function pc(a,c){a=a|0;c=c|0;var d=0,e=0,f=0;f=a+4|0;if(c>>>0>31){e=b[f>>2]|0;b[a>>2]=e;b[f>>2]=0;c=c+-32|0;d=0}else{d=b[f>>2]|0;e=b[a>>2]|0}b[a>>2]=d<<32-c|e>>>c;b[f>>2]=d>>>c;return}function qc(a,c,d,e,f,g,h){a=a|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;o=L;L=L+240|0;m=o+232|0;n=o;p=b[e>>2]|0;b[m>>2]=p;j=b[e+4>>2]|0;k=m+4|0;b[k>>2]=j;b[n>>2]=a;a:do if((p|0)!=1|(j|0)!=0?(l=0-c|0,i=a+(0-(b[h+(f<<2)>>2]|0))|0,(P[d&15](i,a)|0)>=1):0){e=1;g=(g|0)==0;j=i;while(1){if(g&(f|0)>1){g=a+l|0;i=b[h+(f+-2<<2)>>2]|0;if((P[d&15](g,j)|0)>-1){i=10;break a}if((P[d&15](g+(0-i)|0,j)|0)>-1){i=10;break a}}g=e+1|0;b[n+(e<<2)>>2]=j;p=sc(m)|0;pc(m,p);f=p+f|0;if(!((b[m>>2]|0)!=1|(b[k>>2]|0)!=0)){e=g;a=j;i=10;break a}a=j+(0-(b[h+(f<<2)>>2]|0))|0;if((P[d&15](a,b[n>>2]|0)|0)<1){a=j;e=g;g=0;i=9;break}else{p=j;e=g;g=1;j=a;a=p}}}else{e=1;i=9}while(0);if((i|0)==9?(g|0)==0:0)i=10;if((i|0)==10){uc(c,n,e);oc(a,c,d,f,h)}L=o;return}function rc(a,c){a=a|0;c=c|0;var d=0,e=0,f=0;f=a+4|0;if(c>>>0>31){e=b[a>>2]|0;b[f>>2]=e;b[a>>2]=0;c=c+-32|0;d=0}else{d=b[a>>2]|0;e=b[f>>2]|0}b[f>>2]=d>>>(32-c|0)|e<<c;b[a>>2]=d<<c;return}function sc(a){a=a|0;var c=0;c=tc((b[a>>2]|0)+-1|0)|0;if(!c){c=tc(b[a+4>>2]|0)|0;return ((c|0)==0?0:c+32|0)|0}else return c|0;return 0}function tc(a){a=a|0;var b=0;if(a)if(!(a&1)){b=a;a=0;while(1){a=a+1|0;if(!(b&2))b=b>>>1;else break}}else a=0;else a=32;return a|0}function uc(a,c,d){a=a|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;h=L;L=L+256|0;e=h;a:do if((d|0)>=2?(g=c+(d<<2)|0,b[g>>2]=e,a|0):0)while(1){f=a>>>0<256?a:256;Ec(e|0,b[c>>2]|0,f|0)|0;e=0;do{i=c+(e<<2)|0;e=e+1|0;Ec(b[i>>2]|0,b[c+(e<<2)>>2]|0,f|0)|0;b[i>>2]=(b[i>>2]|0)+f}while((e|0)!=(d|0));a=a-f|0;if(!a)break a;e=b[g>>2]|0}while(0);L=h;return}function vc(a,b){a=+a;b=b|0;return +(+lc(a,b))}function wc(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){B(b<<c|(a&(1<<c)-1<<32-c)>>>32-c|0);return a<<c}B(a<<c-32|0);return 0}function xc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;c=a+c>>>0;return (B(b+d+(c>>>0<a>>>0|0)>>>0|0),c|0)|0}function yc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;d=b-d-(c>>>0>a>>>0|0)>>>0;return (B(d|0),a-c>>>0|0)|0}function zc(a){a=a|0;return (a&255)<<24|(a>>8&255)<<16|(a>>16&255)<<8|a>>>24|0}function Ac(a){a=+a;return a>=0.0?+p(a+.5):+y(a-.5)}function Bc(a){a=+a;return a-+p(a)!=.5?+Ac(a):+Ac(a/2.0)*2.0}function Cc(a){a=+a;return a>=0.0?+p(a+.5):+y(a-.5)}function Dc(a){a=+a;return a-+p(a)!=.5?+Cc(a):+Cc(a/2.0)*2.0}function Ec(c,d,e){c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0;if((e|0)>=8192){F(c|0,d|0,e|0)|0;return c|0}h=c|0;g=c+e|0;if((c&3)==(d&3)){while(c&3){if(!e)return h|0;a[c>>0]=a[d>>0]|0;c=c+1|0;d=d+1|0;e=e-1|0}e=g&-4|0;f=e-64|0;while((c|0)<=(f|0)){b[c>>2]=b[d>>2];b[c+4>>2]=b[d+4>>2];b[c+8>>2]=b[d+8>>2];b[c+12>>2]=b[d+12>>2];b[c+16>>2]=b[d+16>>2];b[c+20>>2]=b[d+20>>2];b[c+24>>2]=b[d+24>>2];b[c+28>>2]=b[d+28>>2];b[c+32>>2]=b[d+32>>2];b[c+36>>2]=b[d+36>>2];b[c+40>>2]=b[d+40>>2];b[c+44>>2]=b[d+44>>2];b[c+48>>2]=b[d+48>>2];b[c+52>>2]=b[d+52>>2];b[c+56>>2]=b[d+56>>2];b[c+60>>2]=b[d+60>>2];c=c+64|0;d=d+64|0}while((c|0)<(e|0)){b[c>>2]=b[d>>2];c=c+4|0;d=d+4|0}}else{e=g-4|0;while((c|0)<(e|0)){a[c>>0]=a[d>>0]|0;a[c+1>>0]=a[d+1>>0]|0;a[c+2>>0]=a[d+2>>0]|0;a[c+3>>0]=a[d+3>>0]|0;c=c+4|0;d=d+4|0}}while((c|0)<(g|0)){a[c>>0]=a[d>>0]|0;c=c+1|0;d=d+1|0}return h|0}function Fc(c,d,e){c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=c+e|0;d=d&255;if((e|0)>=67){while(c&3){a[c>>0]=d;c=c+1|0}f=h&-4|0;i=d|d<<8|d<<16|d<<24;g=f-64|0;while((c|0)<=(g|0)){b[c>>2]=i;b[c+4>>2]=i;b[c+8>>2]=i;b[c+12>>2]=i;b[c+16>>2]=i;b[c+20>>2]=i;b[c+24>>2]=i;b[c+28>>2]=i;b[c+32>>2]=i;b[c+36>>2]=i;b[c+40>>2]=i;b[c+44>>2]=i;b[c+48>>2]=i;b[c+52>>2]=i;b[c+56>>2]=i;b[c+60>>2]=i;c=c+64|0}while((c|0)<(f|0)){b[c>>2]=i;c=c+4|0}}while((c|0)<(h|0)){a[c>>0]=d;c=c+1|0}return h-e|0}function Gc(a){a=a|0;var c=0,d=0;d=b[g>>2]|0;c=d+a|0;if((a|0)>0&(c|0)<(d|0)|(c|0)<0){K(c|0)|0;D(12);return -1}if((c|0)>(E()|0)){if(!(G(c|0)|0)){D(12);return -1}}else b[g>>2]=c;return d|0}function Hc(a,b){a=a|0;b=b|0;return O[a&1](b|0)|0}function Ic(a,b,c){a=a|0;b=b|0;c=c|0;return P[a&15](b|0,c|0)|0}function Jc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;return Q[a&7](b|0,c|0,d|0,e|0)|0}function Kc(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return R[a&7](b|0,c|0,d|0,e|0,f|0)|0}function Lc(a,b,c,d,e,f,g,h,i){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;return S[a&3](b|0,c|0,d|0,e|0,f|0,g|0,h|0,i|0)|0}function Mc(a,b){a=a|0;b=b|0;T[a&7](b|0)}function Nc(a,b,c){a=a|0;b=b|0;c=c|0;U[a&3](b|0,c|0)}function Oc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;V[a&1](b|0,c|0,d|0)}function Pc(a){a=a|0;A(0);return 0}function Qc(a,b){a=a|0;b=b|0;A(1);return 0}function Rc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;A(2);return 0}function Sc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;A(3);return 0}function Tc(a,b,c,d,e,f,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;A(4);return 0}function Uc(a){a=a|0;A(5)}function Vc(a,b){a=a|0;b=b|0;A(6)}function Wc(a,b,c){a=a|0;b=b|0;c=c|0;A(7)}

// EMSCRIPTEN_END_FUNCS
var O=[Pc,Vb];var P=[Qc,nb,ob,rb,yb,zb,Cb,Eb,Fb,Tb,Wb,Ea,_a,tb,Qc,Qc];var Q=[Rc,sb,Db,Oa,Pa,Rc,Rc,Rc];var R=[Sc,Ib,Lb,Nb,Pb,Rb,Sc,Sc];var S=[Tc,Mb,Qb,Tc];var T=[Uc,pb,qb,Ab,Bb,Gb,Hb,Ub];var U=[Vc,mb,Kb,Vc];var V=[Wc,Sb];return{_bitshift64Shl:wc,_free:fc,_i64Add:xc,_i64Subtract:yc,_llvm_bswap_i32:zc,_llvm_rint_f32:Bc,_llvm_rint_f64:Dc,_malloc:ec,_memcpy:Ec,_memset:Fc,_ogv_audio_decoder_destroy:ba,_ogv_audio_decoder_init:_,_ogv_audio_decoder_process_audio:aa,_ogv_audio_decoder_process_header:$,_sbrk:Gc,dynCall_ii:Hc,dynCall_iii:Ic,dynCall_iiiii:Jc,dynCall_iiiiii:Kc,dynCall_iiiiiiiii:Lc,dynCall_vi:Mc,dynCall_vii:Nc,dynCall_viii:Oc,establishStackSpace:Z,stackAlloc:W,stackRestore:Y,stackSave:X}})


// EMSCRIPTEN_END_ASM
(fa,ha,buffer);a._bitshift64Shl=S._bitshift64Shl;a._free=S._free;a._i64Add=S._i64Add;a._i64Subtract=S._i64Subtract;a._llvm_bswap_i32=S._llvm_bswap_i32;a._llvm_rint_f32=S._llvm_rint_f32;
a._llvm_rint_f64=S._llvm_rint_f64;a._malloc=S._malloc;a._memcpy=S._memcpy;a._memset=S._memset;a._ogv_audio_decoder_destroy=S._ogv_audio_decoder_destroy;a._ogv_audio_decoder_init=S._ogv_audio_decoder_init;a._ogv_audio_decoder_process_audio=S._ogv_audio_decoder_process_audio;a._ogv_audio_decoder_process_header=S._ogv_audio_decoder_process_header;a._sbrk=S._sbrk;a.establishStackSpace=S.establishStackSpace;a.stackAlloc=S.stackAlloc;a.stackRestore=S.stackRestore;a.stackSave=S.stackSave;a.dynCall_ii=S.dynCall_ii;
a.dynCall_iii=S.dynCall_iii;a.dynCall_iiiii=S.dynCall_iiiii;a.dynCall_iiiiii=S.dynCall_iiiiii;a.dynCall_iiiiiiiii=S.dynCall_iiiiiiiii;a.dynCall_vi=S.dynCall_vi;a.dynCall_vii=S.dynCall_vii;a.dynCall_viii=S.dynCall_viii;a.asm=S;
if(O){if(String.prototype.startsWith?!O.startsWith(P):0!==O.indexOf(P)){var T=O;O=a.locateFile?a.locateFile(T,t):t+T}if(q||r){var ia=a.readBinary(O);E.set(ia,8)}else{L++;a.monitorRunDependencies&&a.monitorRunDependencies(L);var U=function(b){b.byteLength&&(b=new Uint8Array(b));E.set(b,8);a.memoryInitializerRequest&&delete a.memoryInitializerRequest.response;L--;a.monitorRunDependencies&&a.monitorRunDependencies(L);0==L&&(null!==M&&(clearInterval(M),M=null),N&&(b=N,N=null,b()))},ja=function(){a.readAsync(O,
U,function(){throw"could not load memory initializer "+O;})},ka=w(O);if(ka)U(ka.buffer);else if(a.memoryInitializerRequest){var la=function(){var b=a.memoryInitializerRequest,c=b.response;if(200!==b.status&&0!==b.status)if(c=w(a.memoryInitializerRequestURL))c=c.buffer;else{console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+b.status+", retrying "+O);ja();return}U(c)};a.memoryInitializerRequest.response?setTimeout(la,0):a.memoryInitializerRequest.addEventListener("load",
la)}else ja()}}a.then=function(b){if(a.calledRun)b(a);else{var c=a.onRuntimeInitialized;a.onRuntimeInitialized=function(){c&&c();b(a)}}return a};function R(b){this.name="ExitStatus";this.message="Program terminated with exit("+b+")";this.status=b}R.prototype=Error();R.prototype.constructor=R;N=function ma(){a.calledRun||V();a.calledRun||(N=ma)};
function V(){function b(){if(!a.calledRun&&(a.calledRun=!0,!D)){K||(K=!0,H(aa));H(ba);if(a.onRuntimeInitialized)a.onRuntimeInitialized();if(a.postRun)for("function"==typeof a.postRun&&(a.postRun=[a.postRun]);a.postRun.length;){var b=a.postRun.shift();J.unshift(b)}H(J)}}if(!(0<L)){if(a.preRun)for("function"==typeof a.preRun&&(a.preRun=[a.preRun]);a.preRun.length;)ca();H(I);0<L||a.calledRun||(a.setStatus?(a.setStatus("Running..."),setTimeout(function(){setTimeout(function(){a.setStatus("")},1);b()},
1)):b())}}a.run=V;function x(b){if(a.onAbort)a.onAbort(b);void 0!==b?(A(b),B(b),b=JSON.stringify(b)):b="";D=!0;throw"abort("+b+"). Build with -s ASSERTIONS=1 for more info.";}a.abort=x;if(a.preInit)for("function"==typeof a.preInit&&(a.preInit=[a.preInit]);0<a.preInit.length;)a.preInit.pop()();a.noExitRuntime=!0;V();var W,X;function na(b){if(W&&X>=b)return W;W&&a._free(W);X=b;return W=a._malloc(X)}var Y;Y="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);
function Z(b){var c=Y();b=b();a.cpuTime+=Y()-c;return b}a.loadedMetadata=!!f.audioFormat;a.audioFormat=f.audioFormat||null;a.audioBuffer=null;a.cpuTime=0;Object.defineProperty(a,"processing",{get:function(){return!1}});a.init=function(b){Z(function(){a._ogv_audio_decoder_init()});b()};a.processHeader=function(b,c){var d=Z(function(){var c=b.byteLength,d=na(c);a.HEAPU8.set(new Uint8Array(b),d);return a._ogv_audio_decoder_process_header(d,c)});c(d)};
a.processAudio=function(b,c){var d=Z(function(){var c=b.byteLength,d=na(c);a.HEAPU8.set(new Uint8Array(b),d);return a._ogv_audio_decoder_process_audio(d,c)});c(d)};a.close=function(){};



  return OGVDecoderAudioVorbis
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
      module.exports = OGVDecoderAudioVorbis;
    else if (typeof define === 'function' && define['amd'])
      define([], function() { return OGVDecoderAudioVorbis; });
    else if (typeof exports === 'object')
      exports["OGVDecoderAudioVorbis"] = OGVDecoderAudioVorbis;
    