<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    html, body {
      height: 100%;
    }

    body{ 
      font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
      color: #333;
    }

    label, input {
      height: 60px;
      font-size: 24px;
    }
    label {
      width: 100px;
      text-align: right;
      padding: 18px;
    }
    input[type=text], input[type=submit] {
      display: block;
      width: 800px;
      border: 3px solid #333;
      background-color: #eeeeef;
      margin: 10px;
      padding: 5px;
    }
  </style>
</head>
<body>

<h1>Pipplet: Direct user creation demo</h1>
<p>Please the check source code to extract the javascript code to integrate on your website!</p>

<h3>Click on the button below for a test:</h3>

<!-- Pipplet code -->
<form method="GET" action="http://localhost:3000/api/direct_users/create" id="plt_form">
  <label>First name</label><input type="text" name="first_name" value=""/>
  <label>Last name</label><input type="text" name="last_name" value=""/>
  <label>Email</label><input type="text" name="email" value=""/>
  <label>Phone number</label><input type="text" name="phone_number" value=""/>
  
  <input type="hidden" id="plt_token" name="token"/>
  
  <input type="submit" value="Create Pipplet user!" />
</form>


<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.2/rollups/sha1.js"></script>
<script>
  // Pipplet config  
  var plt_dk = "123456ABC";
    
  // Pipplet code
  var plt_d = new Date(); 
  var plt_date = plt_d.getUTCDate()+"-"+(plt_d.getUTCMonth()+1)+"-"+plt_d.getUTCFullYear();
  //var plt_hash = CryptoJS.SHA1(plt_dk+plt_date);
  var plt_hash = (plt_dk+plt_date);
  
  // Set token
  var plt_t = document.getElementById("plt_token");
  plt_t.value = plt_hash;
</script>
<!-- End of Pipplet code -->




<br /><br /><hr/>

<h1>Pipplet: debugging variables</h1>

<label>Key</label> <input type="text" id="plt_debug_key" readonly/>
<label>Date</label><input type="text" id="plt_debug_date" readonly/>
<label>Hash</label><input type="text" id="plt_debug_hash" readonly/>
<label>Url</label> <input type="text" id="plt_debug_url" readonly/>

<script>
  var k = document.getElementById("plt_debug_key");
  var u = document.getElementById("plt_debug_url");
  var d = document.getElementById("plt_debug_date");
  var h = document.getElementById("plt_debug_hash");
  var f = document.getElementById("plt_form");

  k.value = plt_dk;
  d.value = plt_date;
  h.value = plt_hash;
  u.value = f.action;
</script>

</body>
</html>

