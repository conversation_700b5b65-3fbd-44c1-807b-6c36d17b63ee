# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2025-07-09 13:16:02 UTC using RuboCop version 1.78.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: TreatCommentsAsGroupSeparators, ConsiderPunctuation, Include.
# Include: **/*.gemfile, **/Gemfile, **/gems.rb
Bundler/OrderedGems:
  Exclude:
    - 'Gemfile'

# Offense count: 9
# This cop supports safe autocorrection (--autocorrect).
Capybara/SpecificFinders:
  Exclude:
    - 'test/integrations/end_of_test_test.rb'
    - 'test/integrations/user_terms_and_policies_test.rb'
    - 'test/support/integrations/questions_test_helper.rb'

# Offense count: 42
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, NonImplicitAssociationMethodNames.
# SupportedStyles: explicit, implicit
FactoryBot/AssociationStyle:
  Exclude:
    - 'test/factories/dynamic_question_data.rb'
    - 'test/factories/evaluations.rb'
    - 'test/factories/identity_providers.rb'
    - 'test/factories/invoice_requests.rb'
    - 'test/factories/productions.rb'
    - 'test/factories/question_elements.rb'
    - 'test/factories/questions.rb'
    - 'test/factories/receptions.rb'
    - 'test/factories/remote_clients.rb'
    - 'test/factories/sso_users.rb'
    - 'test/factories/technical_issues.rb'
    - 'test/factories/test_instances.rb'
    - 'test/factories/test_profiles.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Include, EnforcedStyle, ExplicitOnly.
# Include: **/*_spec.rb, **/spec/**/*, **/test/**/*, **/features/support/factories/**/*.rb
# SupportedStyles: require_parentheses, omit_parentheses
FactoryBot/ConsistentParenthesesStyle:
  Exclude:
    - 'test/factories/certificates.rb'
    - 'test/factories/users.rb'

# Offense count: 14
# Configuration parameters: Include.
# Include: **/*_spec.rb, **/spec/**/*, **/test/**/*, **/features/support/factories/**/*.rb
FactoryBot/FactoryAssociationWithStrategy:
  Exclude:
    - 'test/factories/dynamic_question_data.rb'
    - 'test/factories/evaluations.rb'
    - 'test/factories/integrations/smartrecruiters/test_instance_request_data.rb'
    - 'test/factories/integrations/workable/test_instance_request_data.rb'
    - 'test/factories/next_questionables.rb'
    - 'test/factories/productions.rb'
    - 'test/factories/test_instance_requests.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
FactoryBot/FactoryClassName:
  Exclude:
    - 'test/factories/integrations/jobvite/test_instance_request_data.rb'
    - 'test/factories/integrations/jobylon/test_instance_request_data.rb'
    - 'test/factories/integrations/teamtailor/client.rb'
    - 'test/factories/test_instance_requests.rb'
    - 'test/factories/test_instances_services.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowForAlignment, EnforcedStyleForExponentOperator, EnforcedStyleForRationalLiterals.
# SupportedStylesForExponentOperator: space, no_space
# SupportedStylesForRationalLiterals: space, no_space
Layout/SpaceAroundOperators:
  Exclude:
    - 'lib/tasks/evaluations_import.rake'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: final_newline, final_blank_line
Layout/TrailingEmptyLines:
  Exclude:
    - 'test/integrations/admin/evaluations/stuck_evaluations_test.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowInHeredoc.
Layout/TrailingWhitespace:
  Exclude:
    - 'app/models/test_instance.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Lint/AmbiguousOperatorPrecedence:
  Exclude:
    - 'app/jobs/create_technical_issue_job.rb'
    - 'app/models/score.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Lint/BooleanSymbol:
  Exclude:
    - 'app/helpers/admin_helper.rb'

# Offense count: 4
# Configuration parameters: AllowedMethods.
# AllowedMethods: enums
Lint/ConstantDefinitionInBlock:
  Exclude:
    - 'test/models/concerns/anonymizable_test.rb'

# Offense count: 7
# Configuration parameters: IgnoreLiteralBranches, IgnoreConstantBranches, IgnoreDuplicateElseBranch.
Lint/DuplicateBranch:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/models/assessment_type.rb'
    - 'app/models/brand.rb'
    - 'lib/test_instance_request.rb'

# Offense count: 2
Lint/DuplicateMethods:
  Exclude:
    - 'app/services/aws_comprehend_service.rb'

# Offense count: 2
# Configuration parameters: AllowComments, AllowEmptyLambdas.
Lint/EmptyBlock:
  Exclude:
    - 'app/controllers/admin/evaluations_controller.rb'

# Offense count: 8
Lint/IneffectiveAccessModifier:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/algo_post_fetch_filtering/filter_by_questionable_score.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/score.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Lint/LiteralInInterpolation:
  Exclude:
    - 'test/models/alert_test.rb'

# Offense count: 4
# This cop supports unsafe autocorrection (--autocorrect-all).
Lint/PercentStringArray:
  Exclude:
    - 'db/questions/challenges/BD-011.rb'
    - 'db/questions/challenges/CF-109.rb'
    - 'db/questions/challenges/MH-25.rb'
    - 'db/questions/templates/MH-25.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Lint/RedundantDirGlobSort:
  Exclude:
    - 'config/initializers/monkey_patches.rb'
    - 'test/test_helper.rb'

# Offense count: 10
# This cop supports safe autocorrection (--autocorrect).
Lint/RedundantStringCoercion:
  Exclude:
    - 'app/controllers/api/v1/base_controller.rb'
    - 'app/controllers/pages_controller.rb'
    - 'app/models/concerns/identity.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/result.rb'

# Offense count: 22
Lint/RescueException:
  Enabled: false

# Offense count: 5
Lint/ReturnInVoidContext:
  Exclude:
    - 'app/models/question_elements/q_e_audio_production.rb'
    - 'app/models/question_elements/q_e_mic_recorder.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods.
# AllowedMethods: present?, blank?, presence, presence_in, try, try!, in?
Lint/SafeNavigationChain:
  Exclude:
    - 'app/models/evaluation.rb'

# Offense count: 7
# Configuration parameters: AllowKeywordBlockArguments.
Lint/UnderscorePrefixedVariableName:
  Exclude:
    - 'app/models/challenge_log.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/production.rb'
    - 'app/models/question_element.rb'
    - 'app/models/score_version.rb'
    - 'app/models/user.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Lint/UnifiedInteger:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/algo_post_fetch_filtering/filter_by_questionable_score.rb'

# Offense count: 1
# Configuration parameters: AllowedPatterns.
# AllowedPatterns: (?-mix:(exactly|at_least|at_most)\(\d+\)\.times)
Lint/UnreachableLoop:
  Exclude:
    - 'app/models/production.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, IgnoreEmptyBlocks, AllowUnusedKeywordArguments.
Lint/UnusedBlockArgument:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/jobs/archived_jobs/data_export_to_google_drive_job.rb'
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/test_profile_productions_list.rb'
    - 'lib/tasks/evaluations_import.rake'

# Offense count: 22
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, AllowUnusedKeywordArguments, IgnoreEmptyMethods, IgnoreNotImplementedMethods, NotImplementedExceptions.
# NotImplementedExceptions: NotImplementedError
Lint/UnusedMethodArgument:
  Exclude:
    - 'app/controllers/api/lever/lever_api_controller.rb'
    - 'app/controllers/api/smartrecruiters/smartrecruiters_api_controller.rb'
    - 'app/controllers/api/workable/workable_api_controller.rb'
    - 'app/controllers/concerns/has_integration.rb'
    - 'app/controllers/registrations_controller.rb'
    - 'app/jobs/archived_jobs/check_marketplace_health_job.rb'
    - 'app/jobs/archived_jobs/data_export_to_google_drive_job.rb'
    - 'app/jobs/send_linguistics_statistics_job.rb'
    - 'app/models/concerns/scorable.rb'
    - 'app/models/questions/q_audio_content.rb'
    - 'app/models/questions/q_audio_production.rb'
    - 'app/models/questions/q_html.rb'
    - 'app/models/questions/q_mic_recorder.rb'
    - 'app/models/questions/q_radio_words_recorder.rb'
    - 'app/models/questions/q_video.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, ContextCreatingMethods, MethodCreatingMethods, inherit_mode.
# ContextCreatingMethods: class_methods, included, prepended, concern, concerning
Lint/UselessAccessModifier:
  Exclude:
    - 'app/controllers/concerns/stripe_identity_controller.rb'
    - 'app/models/average_grade.rb'
    - 'app/models/concerns/next_questionable_logic/algo_post_fetch_filtering/filter_by_questionable_score.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/score.rb'

# Offense count: 42
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect.
Lint/UselessAssignment:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AutoCorrect.
Lint/UselessMethodDefinition:
  Exclude:
    - 'app/controllers/admin/test_instances_controller.rb'

# Offense count: 1
Lint/UselessOr:
  Exclude:
    - 'app/models/test_instance.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, CheckForMethodsWithNoSideEffects.
Lint/Void:
  Exclude:
    - 'app/controllers/admin/evaluations_controller.rb'
    - 'db/questions/challenges/CF-104.rb'
    - 'db/questions/challenges/CF-105.rb'
    - 'db/questions/challenges/FR_CF-104.rb'
    - 'db/questions/challenges/FR_CF-105.rb'
    - 'db/questions/challenges/FR_CF-29.rb'
    - 'db/questions/challenges/MH-23.rb'

# Offense count: 410
# Configuration parameters: AllowedMethods, AllowedPatterns, CountRepeatedAttributes.
Metrics/AbcSize:
  Max: 189

# Offense count: 223
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns.
# AllowedMethods: refine
Metrics/BlockLength:
  Max: 414

# Offense count: 6
# Configuration parameters: CountBlocks, CountModifierForms.
Metrics/BlockNesting:
  Max: 5

# Offense count: 459
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Max: 443

# Offense count: 9
# Configuration parameters: CountComments, CountAsOne.
Metrics/ModuleLength:
  Max: 484

# Offense count: 19
# Configuration parameters: CountKeywordArgs.
Metrics/ParameterLists:
  Max: 9
  MaxOptionalParameters: 4

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Minitest/AssertIncludes:
  Exclude:
    - 'test/models/user/user_test.rb'

# Offense count: 4
# Configuration parameters: Severity.
Minitest/AssertWithExpectedArgument:
  Exclude:
    - 'test/services/api/test_instances_service_test.rb'

# Offense count: 57
Naming/AccessorMethodName:
  Enabled: false

# Offense count: 180
# Configuration parameters: ExpectMatchingDefinition, CheckDefinitionPathHierarchy, CheckDefinitionPathHierarchyRoots, Regex, IgnoreExecutableScripts, AllowedAcronyms.
# CheckDefinitionPathHierarchyRoots: lib, spec, test, src
# AllowedAcronyms: CLI, DSL, ACL, API, ASCII, CPU, CSS, DNS, EOF, GUID, HTML, HTTP, HTTPS, ID, IP, JSON, LHS, QPS, RAM, RHS, RPC, SLA, SMTP, SQL, SSH, TCP, TLS, TTL, UDP, UI, UID, UUID, URI, URL, UTF8, VM, XML, XMPP, XSRF, XSS
Naming/FileName:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyleForLeadingUnderscores.
# SupportedStylesForLeadingUnderscores: disallowed, required, optional
Naming/MemoizedInstanceVariableName:
  Exclude:
    - 'app/controllers/concerns/restful_controller.rb'

# Offense count: 4
# Configuration parameters: EnforcedStyle, AllowedPatterns, ForbiddenIdentifiers, ForbiddenPatterns.
# SupportedStyles: snake_case, camelCase
# ForbiddenIdentifiers: __id__, __send__
Naming/MethodName:
  Exclude:
    - 'app/controllers/api/talentsoft/assessment_controller.rb'
    - 'app/models/certificate.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/test_instance.rb'

# Offense count: 50
# Configuration parameters: MinNameLength, AllowNamesEndingInNumbers, AllowedNames, ForbiddenNames.
# AllowedNames: as, at, by, cc, db, id, if, in, io, ip, of, on, os, pp, to
Naming/MethodParameterName:
  Exclude:
    - 'app/helpers/admin_helper.rb'
    - 'app/mailers/alert_mailer.rb'
    - 'app/models/average_grade.rb'
    - 'app/models/concerns/next_questionable_logic.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/invoice_request.rb'
    - 'app/models/layout.rb'
    - 'app/models/past_challenge.rb'
    - 'app/models/test_instance.rb'
    - 'app/models/test_profile.rb'

# Offense count: 39
# Configuration parameters: Mode, AllowedMethods, AllowedPatterns, AllowBangMethods, WaywardPredicates.
# AllowedMethods: call
# WaywardPredicates: nonzero?
Naming/PredicateMethod:
  Enabled: false

# Offense count: 58
# Configuration parameters: NamePrefix, ForbiddenPrefixes, AllowedMethods, MethodDefinitionMacros, UseSorbetSigs.
# NamePrefix: is_, has_, have_, does_
# ForbiddenPrefixes: is_, has_, have_, does_
# AllowedMethods: is_a?
# MethodDefinitionMacros: define_method, define_singleton_method
Naming/PredicatePrefix:
  Enabled: false

# Offense count: 23
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: PreferredName.
Naming/RescuedExceptionsVariableName:
  Enabled: false

# Offense count: 15
# Configuration parameters: EnforcedStyle, AllowedIdentifiers, AllowedPatterns, ForbiddenIdentifiers, ForbiddenPatterns.
# SupportedStyles: snake_case, camelCase
Naming/VariableName:
  Exclude:
    - 'app/models/questions/q_radio_linked_images_recorder.rb'
    - 'app/models/questions/q_radio_linked_texts_recorder.rb'
    - 'app/models/questions/q_radio_linked_words_recorder.rb'

# Offense count: 3
# Configuration parameters: EnforcedStyle, CheckMethodNames, CheckSymbols, AllowedIdentifiers, AllowedPatterns.
# SupportedStyles: snake_case, normalcase, non_integer
# AllowedIdentifiers: TLS1_1, TLS1_2, capture3, iso8601, rfc1123_date, rfc822, rfc2822, rfc3339, x86_64
Naming/VariableNumber:
  Exclude:
    - 'app/models/api_user.rb'
    - 'db/migrate/20221014134339_add_metadata_1_to_api_user.rb'
    - 'test/services/wise_service_test.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Performance/Caller:
  Exclude:
    - 'app/helpers/mailer_delivery_helper.rb'

# Offense count: 3
# Configuration parameters: MinSize.
Performance/CollectionLiteralInLoop:
  Exclude:
    - 'app/controllers/api/talentsoft/assessment_controller.rb'
    - 'app/models/test_instance_validation.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/Count:
  Exclude:
    - 'app/models/test_instance.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/InefficientHashSearch:
  Exclude:
    - 'app/models/concerns/evaluations/evaluation_validation.rb'
    - 'app/models/concerns/questionable.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/MapCompact:
  Exclude:
    - 'app/jobs/create_invoice_requests_job.rb'
    - 'app/models/test_instance.rb'
    - 'app/models/user_group.rb'

# Offense count: 1
Performance/MapMethodChain:
  Exclude:
    - 'test/services/evaluations_manager_service/evaluations_manager_service_test.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Performance/RedundantMatch:
  Exclude:
    - 'app/mailers/examiner_mailer.rb'
    - 'app/mailers/pipplet_mailer.rb'
    - 'app/models/evaluation.rb'

# Offense count: 10
# This cop supports safe autocorrection (--autocorrect).
Performance/RegexpMatch:
  Exclude:
    - 'app/controllers/api/v1/test_instances_controller.rb'
    - 'app/controllers/examiner/examiner_controller.rb'
    - 'app/mailers/examiner_mailer.rb'
    - 'app/mailers/pipplet_mailer.rb'
    - 'app/models/concerns/question_validation.rb'
    - 'app/models/evaluation.rb'
    - 'db/migrate/20151020211219_add_group_to_users.rb'
    - 'lib/pipplet_clients_api.rb'

# Offense count: 22
# This cop supports safe autocorrection (--autocorrect).
Performance/StringIdentifierArgument:
  Exclude:
    - 'app/controllers/api/talentsoft/assessment_controller.rb'
    - 'app/models/evaluation.rb'
    - 'test/services/wise_service_test.rb'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/StringInclude:
  Exclude:
    - 'app/models/evaluation.rb'
    - 'app/models/user.rb'
    - 'lib/pipplet_clients_api.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Performance/StringReplacement:
  Exclude:
    - 'app/serializers/integrations/smartrecruiters/test_instance_serializer.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Performance/TimesMap:
  Exclude:
    - 'test/controllers/admin/invoice_requests_controller_test.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: ExpectedOrder, Include.
# ExpectedOrder: index, show, new, edit, create, update, destroy
# Include: **/app/controllers/**/*.rb
Rails/ActionOrder:
  Exclude:
    - 'app/controllers/admin/challenges_controller.rb'
    - 'app/controllers/admin/evaluation_delays_controller.rb'
    - 'app/controllers/admin/evaluations_controller.rb'
    - 'app/controllers/api/v1/language_test_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/ActiveRecordAliases:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/ActiveRecordCallbacksOrder:
  Exclude:
    - 'app/models/examiner.rb'
    - 'app/models/invoice_request.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/ApplicationController:
  Exclude:
    - 'app/controllers/api/direct_users_controller.rb'
    - 'app/controllers/api/workable/workable_api_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/ApplicationJob:
  Exclude:
    - 'app/jobs/monthly_fnp_job.rb'

# Offense count: 13
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: NilOrEmpty, NotPresent, UnlessPresent.
Rails/Blank:
  Exclude:
    - 'app/controllers/api/v1/technical_issues_controller.rb'
    - 'app/controllers/event_logs_controller.rb'
    - 'app/helpers/integrations_helper.rb'
    - 'app/models/challenge.rb'
    - 'app/models/concerns/evaluations/evaluation_validation.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/examiner.rb'
    - 'app/models/recording.rb'
    - 'app/models/test_instance.rb'
    - 'app/services/api/test_instances_service.rb'
    - 'lib/tasks/aws_comprehend_benchmark.rake'
    - 'lib/test_instance_request.rb'

# Offense count: 68
# Configuration parameters: Database, Include.
# SupportedDatabases: mysql, postgresql
# Include: db/**/*.rb
Rails/BulkChangeTable:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/CompactBlank:
  Exclude:
    - 'lib/tasks/test_instance.rake'

# Offense count: 13
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowToTime.
# SupportedStyles: strict, flexible
Rails/Date:
  Exclude:
    - 'app/controllers/admin/examiner_statistics_controller.rb'
    - 'app/jobs/launch_monthly_job.rb'
    - 'app/jobs/launch_weekly_job.rb'
    - 'app/mailers/alert_mailer.rb'
    - 'app/mailers/invoice_request_mailer.rb'
    - 'app/models/invoice_request.rb'
    - 'lib/linguistics_stats.rb'

# Offense count: 11
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforceForPrefixed.
Rails/Delegate:
  Exclude:
    - 'app/models/challenge.rb'
    - 'app/models/test_profile.rb'
    - 'app/serializers/api/v1/language_test_quick_serializer.rb'
    - 'app/serializers/api/v1/language_test_serializer.rb'
    - 'app/serializers/api/v1/technical_issue_serializer.rb'
    - 'app/serializers/api/v1/test_instance_full_serializer.rb'
    - 'app/serializers/api/v1/test_instance_intern_serializer.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Severity.
Rails/DeprecatedActiveModelErrorsMethods:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 2
# Configuration parameters: Severity.
Rails/DuplicateScope:
  Exclude:
    - 'app/models/test_instance.rb'

# Offense count: 74
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Whitelist, AllowedMethods, AllowedReceivers.
# Whitelist: find_by_sql, find_by_token_for
# AllowedMethods: find_by_sql, find_by_token_for
# AllowedReceivers: Gem::Specification, page
Rails/DynamicFindBy:
  Enabled: false

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/EnvironmentComparison:
  Exclude:
    - 'app/helpers/application_helper.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/ExpandedDateRange:
  Exclude:
    - 'app/models/event_log.rb'

# Offense count: 190
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: slashes, arguments
Rails/FilePath:
  Enabled: false

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: IgnoreWhereFirst.
Rails/FindBy:
  Exclude:
    - 'app/models/question_elements/q_e_text.rb'

# Offense count: 12
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods, AllowedPatterns.
# AllowedMethods: order, limit, select, lock
Rails/FindEach:
  Exclude:
    - 'app/models/challenge.rb'
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/default_test.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/invoice_request.rb'
    - 'app/models/questions/q_radio_linked_images_recorder.rb'
    - 'app/models/questions/q_radio_linked_texts_recorder.rb'
    - 'app/models/questions/q_radio_linked_words_recorder.rb'
    - 'app/models/score.rb'
    - 'app/models/test_instance.rb'

# Offense count: 7
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/HasAndBelongsToMany:
  Exclude:
    - 'app/models/challenge.rb'
    - 'app/models/concerns/taggable.rb'
    - 'app/models/question.rb'
    - 'app/models/role.rb'
    - 'app/models/tag.rb'

# Offense count: 41
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/HasManyOrHasOneDependent:
  Enabled: false

# Offense count: 32
# Configuration parameters: Include.
# Include: **/app/helpers/**/*.rb
Rails/HelperInstanceVariable:
  Exclude:
    - 'app/helpers/application_helper.rb'
    - 'app/helpers/mailjet_helper.rb'

# Offense count: 57
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: numeric, symbolic
Rails/HttpStatus:
  Enabled: false

# Offense count: 1
# Configuration parameters: Include.
# Include: spec/**/*.rb, test/**/*.rb
Rails/I18nLocaleAssignment:
  Exclude:
    - 'test/test_helper.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/IndexBy:
  Exclude:
    - 'app/controllers/admin/evaluations_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Rails/IndexWith:
  Exclude:
    - 'app/mailers/alert_mailer.rb'

# Offense count: 56
# Configuration parameters: IgnoreScopes, Include.
# Include: **/app/models/**/*.rb
Rails/InverseOf:
  Exclude:
    - 'app/models/certificate.rb'
    - 'app/models/challenge.rb'
    - 'app/models/direct_user.rb'
    - 'app/models/production.rb'
    - 'app/models/question.rb'
    - 'app/models/question_element.rb'
    - 'app/models/reception.rb'
    - 'app/models/sso_user.rb'
    - 'app/models/test_instance.rb'
    - 'app/models/user.rb'
    - 'app/models/user_group.rb'

# Offense count: 9
# Configuration parameters: Include.
# Include: **/app/controllers/**/*.rb, **/app/mailers/**/*.rb
Rails/LexicallyScopedActionFilter:
  Exclude:
    - 'app/controllers/admin/examiners_controller.rb'
    - 'app/controllers/admin/languages_controller.rb'
    - 'app/controllers/admin/test_instances_controller.rb'
    - 'app/controllers/admin/test_profiles_controller.rb'
    - 'app/controllers/productions_controller.rb'
    - 'app/controllers/users_controller.rb'

# Offense count: 17
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/NegateInclude:
  Exclude:
    - 'app/controllers/admin/challenges_controller.rb'
    - 'app/helpers/application_helper.rb'
    - 'app/jobs/archived_jobs/data_import_from_google_drive_job.rb'
    - 'app/models/challenge.rb'
    - 'app/models/concerns/next_questionable_logic/algo_post_fetch_filtering/filter_by_questionable_score.rb'
    - 'app/models/concerns/next_questionable_logic/profiles/fixed_audition_profile.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/question_element.rb'
    - 'app/models/question_elements/q_e_text_area.rb'
    - 'app/models/test_instance.rb'
    - 'lib/test_instance_request.rb'

# Offense count: 15
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Include.
# Include: **/app/**/*.rb, **/config/**/*.rb, db/**/*.rb, **/lib/**/*.rb
Rails/Output:
  Exclude:
    - 'app/controllers/alerts_controller.rb'
    - 'app/jobs/archived_jobs/data_export_to_google_drive_job.rb'
    - 'app/jobs/archived_jobs/data_import_from_google_drive_job.rb'
    - 'app/jobs/send_last_chance_to_grade_tests_alert_job.rb'
    - 'app/jobs/sync_direct_users_job.rb'
    - 'app/models/score.rb'
    - 'db/migrate/20181219132511_associate_user_to_examiner.rb'
    - 'db/migrate/20190819122120_update_survey.rb'

# Offense count: 29
Rails/OutputSafety:
  Exclude:
    - 'app/helpers/admin_helper.rb'
    - 'app/helpers/bootstrap_flash_helper.rb'
    - 'app/helpers/devise_helper.rb'
    - 'lib/challenge_builder.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/Pluck:
  Exclude:
    - 'app/jobs/test_instance_validation_details_job.rb'
    - 'app/models/assessment_type.rb'
    - 'app/services/aws_transcribe_service.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: conservative, aggressive
Rails/PluckInWhere:
  Exclude:
    - 'app/models/average_grade.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
Rails/PluralizationGrammar:
  Exclude:
    - 'app/models/direct_user.rb'
    - 'app/models/examiner.rb'
    - 'app/models/statistic.rb'
    - 'app/models/test_profile.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Rails/Presence:
  Exclude:
    - 'app/controllers/admin/evaluations_controller.rb'
    - 'app/helpers/mailjet_helper.rb'
    - 'app/models/api_oauth_manager.rb'

# Offense count: 32
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: NotNilAndNotEmpty, NotBlank, UnlessBlank.
Rails/Present:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Include.
# Include: **/Rakefile, **/*.rake
Rails/RakeEnvironment:
  Exclude:
    - 'lib/tasks/heroku.rake'

# Offense count: 43
# This cop supports safe autocorrection (--autocorrect).
Rails/RedundantForeignKey:
  Exclude:
    - 'app/models/direct_user.rb'
    - 'app/models/test_instance.rb'
    - 'app/models/user_group.rb'

# Offense count: 9
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/RedundantPresenceValidationOnBelongsTo:
  Exclude:
    - 'app/models/api_order.rb'
    - 'app/models/challenge_log.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/examiner.rb'
    - 'app/models/technical_issue.rb'
    - 'app/models/test_instance.rb'

# Offense count: 11
# Configuration parameters: Include.
# Include: db/**/*.rb
Rails/ReversibleMigration:
  Exclude:
    - 'db/migrate/20150730134711_resultsv3.rb'
    - 'db/migrate/20151220152416_destroy_user_logs.rb'
    - 'db/migrate/20160102134645_adding_soft_delete_to_some_models.rb'
    - 'db/migrate/20190306144816_make_skip_identity_check_true_by_default.rb'
    - 'db/migrate/20190410121928_redirection_url_test_instance_api.rb'
    - 'db/migrate/20190531121353_evaluations_examiner_link.rb'
    - 'db/migrate/20190619145348_remove_preceding_evaluation_id.rb'
    - 'db/migrate/20200824125400_rename_certificate_link_to_has_attached_certificate.rb'
    - 'db/migrate/20220719095355_add_column_last_tekis_reminder_sent_at_on_test_instance.rb'
    - 'db/migrate/20250610122628_remove_evaluation_id_from_evaluation_delays.rb'

# Offense count: 183
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/RootPathnameMethods:
  Enabled: false

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Rails/RootPublicPath:
  Exclude:
    - 'app/helpers/application_helper.rb'

# Offense count: 16
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: conservative, aggressive
Rails/ShortI18n:
  Exclude:
    - 'app/controllers/api/v1/test_instances_controller.rb'
    - 'app/helpers/application_helper.rb'
    - 'app/helpers/mailjet_helper.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/question_elements/q_e_text.rb'
    - 'lib/talentsoft_api.rb'

# Offense count: 41
# Configuration parameters: ForbiddenMethods, AllowedMethods.
# ForbiddenMethods: decrement!, decrement_counter, increment!, increment_counter, insert, insert!, insert_all, insert_all!, toggle!, touch, touch_all, update_all, update_attribute, update_column, update_columns, update_counters, upsert, upsert_all
Rails/SkipsModelValidations:
  Enabled: false

# Offense count: 79
# Configuration parameters: Include.
# Include: db/**/*.rb
Rails/ThreeStateBooleanColumn:
  Enabled: false

# Offense count: 113
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: strict, flexible
Rails/TimeZone:
  Enabled: false

# Offense count: 13
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/UniqueValidationWithoutIndex:
  Exclude:
    - 'app/models/api_user.rb'
    - 'app/models/brand.rb'
    - 'app/models/challenge.rb'
    - 'app/models/evaluation_delay.rb'
    - 'app/models/examiner.rb'
    - 'app/models/identity_provider.rb'
    - 'app/models/language.rb'
    - 'app/models/registration_code.rb'
    - 'app/models/remote_client.rb'
    - 'app/models/test_profile.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/Validation:
  Exclude:
    - 'app/models/background_job_log.rb'
    - 'app/models/layout.rb'
    - 'app/models/layout_zone.rb'
    - 'app/models/next_questionable.rb'
    - 'app/models/score.rb'
    - 'app/models/user_group.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Rails/WhereEquals:
  Exclude:
    - 'app/controllers/admin/test_instances_controller.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: exists, where
Rails/WhereExists:
  Exclude:
    - 'app/controllers/pages_controller.rb'
    - 'app/models/user.rb'

# Offense count: 1
Security/Eval:
  Exclude:
    - 'app/controllers/admin/question_elements_controller.rb'

# Offense count: 2
Security/Open:
  Exclude:
    - 'app/jobs/archived_jobs/data_import_from_google_drive_job.rb'
    - 'app/jobs/archived_jobs/get_human_score_for_users_job.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: prefer_alias, prefer_alias_method
Style/Alias:
  Exclude:
    - 'app/models/image.rb'

# Offense count: 6
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: always, conditionals
Style/AndOr:
  Exclude:
    - 'app/controllers/admin/examiner_statistics_controller.rb'
    - 'app/controllers/examiner/examiner_controller.rb'
    - 'app/models/api_user.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/BlockComments:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/models/concerns/scorable.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: is_a?, kind_of?
Style/ClassCheck:
  Exclude:
    - 'app/controllers/api/v1/base_controller.rb'
    - 'app/models/challenge_log.rb'
    - 'app/models/question_element.rb'

# Offense count: 15
# This cop supports safe autocorrection (--autocorrect).
Style/ClassMethods:
  Exclude:
    - 'app/models/challenge_log.rb'
    - 'app/models/language.rb'
    - 'app/models/past_challenge.rb'
    - 'app/models/registration_code.rb'
    - 'app/models/score.rb'
    - 'app/models/score_version.rb'

# Offense count: 11
Style/ClassVars:
  Exclude:
    - 'app/models/question.rb'
    - 'app/models/question_element.rb'
    - 'lib/linguistics_stats.rb'
    - 'lib/talentsoft_api.rb'

# Offense count: 21
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/CollectionQuerying:
  Exclude:
    - 'app/controllers/admin/client_configs_controller.rb'
    - 'app/controllers/application_controller.rb'
    - 'app/mailers/alert_mailer.rb'
    - 'app/models/challenge.rb'
    - 'app/models/concerns/evaluations/evaluation_validation.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/invoice_request.rb'
    - 'app/models/questions/q_text_from_text_area.rb'
    - 'app/models/test_profile.rb'
    - 'app/models/user.rb'
    - 'app/services/grade_comparator_service.rb'
    - 'test/models/test_instance/test_instance_test.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/ColonMethodCall:
  Exclude:
    - 'app/models/question_element.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Keywords, RequireColon.
# Keywords: TODO, FIXME, OPTIMIZE, HACK, REVIEW, NOTE
Style/CommentAnnotation:
  Exclude:
    - 'app/controllers/pages_controller.rb'
    - 'app/jobs/check_weekly_user_group_results_job.rb'
    - 'app/models/assessment_type.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/layout.rb'
    - 'app/models/next_questionable.rb'
    - 'app/models/questions/q_radio_linked_words_recorder.rb'
    - 'test/factories/test_instances.rb'

# Offense count: 7
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/CommentedKeyword:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/controllers/examiner/challenges_controller.rb'
    - 'app/models/concerns/question_validation.rb'
    - 'app/models/concerns/scorable.rb'
    - 'app/models/score.rb'
    - 'lib/tasks/evaluations_import.rake'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/ComparableBetween:
  Exclude:
    - 'app/models/test_instance.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, SingleLineConditionsOnly, IncludeTernaryExpressions.
# SupportedStyles: assign_to_condition, assign_inside_condition
Style/ConditionalAssignment:
  Exclude:
    - 'app/controllers/admin/alerts_controller.rb'
    - 'app/controllers/admin/users_controller.rb'
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/controllers/test_instances_controller.rb'
    - 'app/jobs/create_technical_issue_job.rb'
    - 'app/models/assessment_type.rb'
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/default_test.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'

# Offense count: 7
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: allowed_in_returns, forbidden
Style/DoubleNegation:
  Exclude:
    - 'app/models/challenge_log.rb'
    - 'app/models/question_elements/q_e_drop_down.rb'
    - 'app/models/question_elements/q_e_radio_image_displayer.rb'
    - 'app/models/question_elements/q_e_radio_image_recorder.rb'
    - 'app/models/question_elements/q_e_radio_linked_image_displayer.rb'
    - 'app/models/question_elements/q_e_radio_linked_word_displayer.rb'
    - 'app/models/question_elements/q_e_radio_text_displayer.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/EachWithObject:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/test_profile_productions_list.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AutoCorrect, EnforcedStyle.
# SupportedStyles: compact, expanded
Style/EmptyMethod:
  Exclude:
    - 'app/controllers/admin/question_elements_controller.rb'
    - 'app/models/concerns/question_validation.rb'
    - 'app/models/questions/q_audio_content.rb'
    - 'app/models/questions/q_video.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: trailing_conditional, ternary
Style/EmptyStringInsideInterpolation:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'app/models/integrations/teamtailor/partner_results.rb'
    - 'app/models/test_instance.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/EvenOdd:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/profiles/user_profile.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/ExpandPathArguments:
  Exclude:
    - 'Rakefile'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: left_coerce, right_coerce, single_coerce, fdiv
Style/FloatDivision:
  Exclude:
    - 'app/models/assessment_type.rb'
    - 'lib/linguistics_stats.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: format, sprintf, percent
Style/FormatString:
  Exclude:
    - 'app/controllers/admin/invoice_requests_controller.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MaxUnannotatedPlaceholdersAllowed, Mode, AllowedMethods, AllowedPatterns.
# SupportedStyles: annotated, template, unannotated
# AllowedMethods: redirect
Style/FormatStringToken:
  EnforcedStyle: template

# Offense count: 93
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinBodyLength, AllowConsecutiveConditionals.
Style/GuardClause:
  Enabled: false

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: braces, no_braces
Style/HashAsLastArrayItem:
  Exclude:
    - 'app/controllers/admin/api_users_controller.rb'
    - 'app/controllers/admin/client_configs_controller.rb'
    - 'app/controllers/api/teamtailor/teamtailor_api_controller.rb'

# Offense count: 734
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedShorthandSyntax, UseHashRocketsWithSymbolValues, PreferHashRocketsForNonAlnumEndingSymbols.
# SupportedStyles: ruby19, hash_rockets, no_mixed_keys, ruby19_no_mixed_keys
# SupportedShorthandSyntax: always, never, either, consistent, either_consistent
Style/HashSyntax:
  Enabled: false

# Offense count: 9
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/IdenticalConditionalBranches:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/invoice_request.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowIfModifier.
Style/IfInsideElse:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/score.rb'

# Offense count: 87
# This cop supports safe autocorrection (--autocorrect).
Style/IfUnlessModifier:
  Enabled: false

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowedMethods.
# AllowedMethods: nonzero?
Style/IfWithBooleanLiteralBranches:
  Exclude:
    - 'app/helpers/devise_helper.rb'
    - 'app/models/questions/q_radio_linked_texts_displayer.rb'
    - 'app/models/questions/q_radio_linked_words_displayer.rb'
    - 'app/models/questions/q_radio_secret_linked_texts_displayer.rb'
    - 'app/models/questions/q_radio_secret_linked_words_displayer.rb'

# Offense count: 5
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: InverseMethods, InverseBlocks.
Style/InverseMethods:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/test_instance.rb'
    - 'app/models/test_profile.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: line_count_dependent, lambda, literal
Style/Lambda:
  Exclude:
    - 'app/models/production.rb'
    - 'app/models/question_element.rb'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/LineEndConcatenation:
  Exclude:
    - 'app/controllers/api/v1/base_controller.rb'
    - 'app/controllers/application_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/MapCompactWithConditionalBlock:
  Exclude:
    - 'app/jobs/create_invoice_requests_job.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/MapIntoArray:
  Exclude:
    - 'app/models/evaluation.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/MapToHash:
  Exclude:
    - 'app/mailers/alert_mailer.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods, AllowedPatterns.
Style/MethodCallWithoutArgsParentheses:
  Exclude:
    - 'app/controllers/admin/examiner_statistics_controller.rb'
    - 'app/jobs/archived_jobs/get_human_score_for_users_job.rb'
    - 'app/models/question_element.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: require_parentheses, require_no_parentheses, require_no_parentheses_except_multiline
Style/MethodDefParentheses:
  Exclude:
    - 'app/controllers/api/teamtailor/teamtailor_api_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/MinMaxComparison:
  Exclude:
    - 'app/models/test_instance.rb'

# Offense count: 1
Style/MixinUsage:
  Exclude:
    - 'test/test_helper.rb'

# Offense count: 2
Style/MultilineBlockChain:
  Exclude:
    - 'app/mailers/alert_mailer.rb'
    - 'app/models/test_instance.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/MultilineIfModifier:
  Exclude:
    - 'app/services/aws_comprehend_service.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Style/MultilineWhenThen:
  Exclude:
    - 'lib/test_instance_request.rb'

# Offense count: 43
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: literals, strict
Style/MutableConstant:
  Enabled: false

# Offense count: 86
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: both, prefix, postfix
Style/NegatedIf:
  Enabled: false

# Offense count: 14
# This cop supports safe autocorrection (--autocorrect).
Style/NegatedIfElseCondition:
  Exclude:
    - 'app/controllers/admin/alerts_controller.rb'
    - 'app/controllers/admin/users_controller.rb'
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/controllers/api/v1/base_controller.rb'
    - 'app/mailers/alert_mailer.rb'
    - 'app/models/assessment_type.rb'
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/default_test.rb'
    - 'app/models/past_challenge.rb'
    - 'app/models/question_elements/q_e_audio_production.rb'
    - 'app/models/question_elements/q_e_mic_recorder.rb'
    - 'app/models/question_elements/q_e_text.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinBodyLength, AllowConsecutiveConditionals.
# SupportedStyles: skip_modifier_ifs, always
Style/Next:
  Exclude:
    - 'app/models/production.rb'
    - 'app/models/questions/q_radio_linked_texts_recorder.rb'
    - 'app/models/questions/q_radio_linked_words_recorder.rb'
    - 'lib/tasks/create_currencies.rake'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/Not:
  Exclude:
    - 'app/jobs/create_technical_issue_job.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedOctalStyle.
# SupportedOctalStyles: zero_with_o, zero_only
Style/NumericLiteralPrefix:
  Exclude:
    - 'app/controllers/admin/examiner_statistics_controller.rb'

# Offense count: 47
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowedMethods, AllowedPatterns.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Enabled: false

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AlwaysCorrectToMultiline.
Style/OneLineConditional:
  Exclude:
    - 'app/controllers/test_instances_controller.rb'

# Offense count: 12
Style/OpenStructUse:
  Exclude:
    - 'app/helpers/mailjet_helper.rb'
    - 'app/models/test_instance.rb'
    - 'config/initializers/anonymization.rb'
    - 'test/factories/test_instances_services.rb'
    - 'test/helpers/mailjet_helper_test.rb'
    - 'test/models/user/user_test.rb'
    - 'test/services/wise_service_test.rb'

# Offense count: 24
# Configuration parameters: AllowedMethods.
# AllowedMethods: respond_to_missing?
Style/OptionalBooleanParameter:
  Exclude:
    - 'app/helpers/mailjet_helper.rb'
    - 'app/jobs/check_evaluation_for_certificate_issue_job.rb'
    - 'app/jobs/generate_evaluation_certificate_job.rb'
    - 'app/jobs/launch_monthly_job.rb'
    - 'app/jobs/launch_weekly_job.rb'
    - 'app/jobs/send_linguistics_statistics_job.rb'
    - 'app/mailers/pipplet_mailer.rb'
    - 'app/models/api_user.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/remote_client.rb'
    - 'app/models/score.rb'
    - 'app/models/test_instance.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: PreferredDelimiters.
Style/PercentLiteralDelimiters:
  Exclude:
    - 'app/controllers/examiner/challenges_controller.rb'
    - 'app/models/image.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: short, verbose
Style/PreferredHashMethods:
  Exclude:
    - 'app/jobs/archived_jobs/get_human_score_for_users_job.rb'
    - 'app/models/invoice_request.rb'

# Offense count: 8
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle, AllowedCompactTypes.
# SupportedStyles: compact, exploded
Style/RaiseArgs:
  Exclude:
    - 'app/jobs/archived_jobs/data_export_to_google_drive_job.rb'
    - 'app/jobs/archived_jobs/data_import_from_google_drive_job.rb'
    - 'lib/test_instance_request.rb'

# Offense count: 2
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Methods.
Style/RedundantArgument:
  Exclude:
    - 'app/controllers/api/v1/base_controller.rb'
    - 'lib/challenge_builder.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantBegin:
  Exclude:
    - 'app/jobs/overdue_for_tekis_reminder_job.rb'
    - 'app/models/concerns/identity.rb'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowedMethods.
# AllowedMethods: nonzero?
Style/RedundantCondition:
  Exclude:
    - 'app/controllers/api/talentsoft/talentsoft_api_controller.rb'
    - 'app/models/concerns/next_questionable_logic/realtime_selection/closest_user_score_to_boundary.rb'
    - 'app/models/next_questionable.rb'
    - 'app/models/past_challenge.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantConditional:
  Exclude:
    - 'app/models/questions/q_radio_linked_texts_displayer.rb'
    - 'app/models/questions/q_radio_linked_words_displayer.rb'
    - 'app/models/questions/q_radio_secret_linked_texts_displayer.rb'
    - 'app/models/questions/q_radio_secret_linked_words_displayer.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantConstantBase:
  Exclude:
    - 'config/initializers/rubyzip.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AutoCorrect, AllowComments.
Style/RedundantInitialize:
  Exclude:
    - 'test/helpers/mailer_delivery_helper_test.rb'

# Offense count: 12
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/RedundantInterpolation:
  Exclude:
    - 'app/jobs/create_image_for_test_instance_job.rb'
    - 'app/models/concerns/identity.rb'
    - 'app/models/questions/q_radio_linked_images_recorder.rb'
    - 'app/models/questions/q_radio_linked_texts_recorder.rb'
    - 'app/models/questions/q_radio_linked_words_recorder.rb'
    - 'test/models/alert_test.rb'

# Offense count: 10
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantParentheses:
  Exclude:
    - 'app/controllers/admin/alerts_controller.rb'
    - 'app/controllers/admin/api_orders_controller.rb'
    - 'app/controllers/admin/users_controller.rb'
    - 'app/jobs/monthly_fnp_job.rb'
    - 'app/models/registration_code.rb'
    - 'test/models/test_instance_validation_test.rb'

# Offense count: 4
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantRegexpEscape:
  Exclude:
    - 'app/models/direct_user.rb'

# Offense count: 66
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowMultipleReturnValues.
Style/RedundantReturn:
  Enabled: false

# Offense count: 260
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantSelf:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/RedundantSort:
  Exclude:
    - 'app/controllers/admin/invoice_requests_controller.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantSortBy:
  Exclude:
    - 'app/models/production.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
Style/RedundantStringEscape:
  Exclude:
    - 'app/helpers/admin_helper.rb'
    - 'db/migrate/20190726084653_set_assessment_types_questions.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/RescueModifier:
  Exclude:
    - 'app/services/aws_comprehend_service.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: implicit, explicit
Style/RescueStandardError:
  Exclude:
    - 'app/jobs/generate_evaluation_certificate_job.rb'
    - 'lib/challenge_builder.rb'
    - 'lib/tasks/evaluations_import.rake'

# Offense count: 42
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: ConvertCodeThatCanStartToReturnNil, AllowedMethods, MaxChainLength.
# AllowedMethods: present?, blank?, presence, try, try!
Style/SafeNavigation:
  Enabled: false

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowAsExpressionSeparator.
Style/Semicolon:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/test_profile_productions_list.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/SingleArgumentDig:
  Exclude:
    - 'app/jobs/test_graded_callback_job.rb'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowIfMethodIsEmpty.
Style/SingleLineMethods:
  Exclude:
    - 'app/models/api_user.rb'
    - 'app/models/question_element.rb'
    - 'app/models/registration_code.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowModifier.
Style/SoleNestedConditional:
  Exclude:
    - 'app/controllers/pages_controller.rb'
    - 'app/controllers/users_controller.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: RequireEnglish.
# SupportedStyles: use_perl_names, use_english_names, use_builtin_english_names
Style/SpecialGlobalVars:
  EnforcedStyle: use_perl_names

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/StringChars:
  Exclude:
    - 'app/models/test_instance_validation.rb'

# Offense count: 39
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: Mode.
Style/StringConcatenation:
  Enabled: false

# Offense count: 702
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Enabled: false

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiteralsInInterpolation:
  Exclude:
    - 'app/controllers/pages_controller.rb'
    - 'app/jobs/create_technical_issue_job.rb'
    - 'app/models/production.rb'
    - 'test/models/alert_test.rb'

# Offense count: 75
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinSize.
# SupportedStyles: percent, brackets
Style/SymbolArray:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: AllowMethodsWithArguments, AllowedMethods, AllowedPatterns, AllowComments.
# AllowedMethods: define_method, mail, respond_to
Style/SymbolProc:
  Exclude:
    - 'app/models/user.rb'

# Offense count: 9
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, AllowSafeAssignment.
# SupportedStyles: require_parentheses, require_no_parentheses, require_parentheses_when_complex
Style/TernaryParentheses:
  Exclude:
    - 'app/controllers/admin/alerts_controller.rb'
    - 'app/controllers/admin/api_orders_controller.rb'
    - 'app/controllers/admin/users_controller.rb'
    - 'app/models/concerns/next_questionable_logic/algo_fetch_all/default_test.rb'
    - 'app/models/questions/q_radio_linked_words_recorder.rb'
    - 'app/models/registration_code.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArguments:
  Exclude:
    - 'app/controllers/charges_controller.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, diff_comma, no_comma
Style/TrailingCommaInArrayLiteral:
  Exclude:
    - 'app/controllers/admin/assessment_questions_controller.rb'
    - 'db/questions/challenges/CF-50.rb'
    - 'db/questions/challenges/FR_CF-50.rb'

# Offense count: 99
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, diff_comma, no_comma
Style/TrailingCommaInHashLiteral:
  Enabled: false

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/WhileUntilDo:
  Exclude:
    - 'app/models/concerns/next_questionable_logic/profiles/user_profile.rb'
    - 'lib/tasks/evaluations_import.rake'

# Offense count: 7
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, MinSize, WordRegex.
# SupportedStyles: percent, brackets
Style/WordArray:
  Exclude:
    - 'app/jobs/monthly_fnp_job.rb'
    - 'app/models/concerns/next_questionable_logic/profiles/fixed_audition_profile.rb'
    - 'app/models/question_element.rb'
    - 'config/initializers/aws.rb'

# Offense count: 9
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/ZeroLengthPredicate:
  Exclude:
    - 'app/controllers/admin/challenges_controller.rb'
    - 'app/controllers/questions_controller.rb'
    - 'app/models/brand.rb'
    - 'app/models/concerns/questionable.rb'
    - 'app/models/evaluation.rb'
    - 'app/models/remote_client.rb'
    - 'app/models/score.rb'
    - 'app/services/aws_comprehend_service.rb'
