# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_19_130351) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "event_logs", force: :cascade do |t|
    t.integer "loggable_id"
    t.string "loggable_type"
    t.integer "category"
    t.integer "criticity"
    t.string "content"
    t.string "type"
    t.jsonb "metadata", default: {}
    t.datetime "event_date"
    t.index ["loggable_id", "loggable_type", "category"], name: "index_event_logs_on_loggable_id_and_loggable_type_and_category"
    t.index ["type"], name: "index_event_logs_on_type"
  end
end
