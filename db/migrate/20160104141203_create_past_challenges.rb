class CreatePastChallenges < ActiveRecord::Migration[4.2]
  def change
    create_table :past_challenges do |t|
      t.integer :user_id
      t.integer :challenge_id
      t.integer :challenger_user_id
      t.integer :scoring_batch_id

      t.boolean  :rated
      t.datetime :rated_at

      t.boolean :listening
      t.boolean :reading
      t.boolean :writing
      t.boolean :speaking

      t.integer :successes_count
      t.integer :questions_count
      t.integer :question_elements_count

      t.boolean :production
      t.boolean :reception

      t.float :rating
      t.float :rating_deviation

      t.float :challenger_rating
      t.float :challenger_rating_deviation

      t.float :challenge_average_rating
      t.float :challenge_distance_to_closest_boundary

      t.timestamps null: false
    end

    add_index :past_challenges, :user_id
    add_index :past_challenges, :challenge_id
  end
end
