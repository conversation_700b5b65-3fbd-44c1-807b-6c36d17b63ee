class CreateResults < ActiveRecord::Migration[4.2]
  def change
    create_table :results do |t|
      t.integer  :reception_id

      t.boolean  :rated, default: false

      t.string   :user_type
      t.integer  :user_id
      t.float    :user_rating
      t.float    :user_rating_deviation
      t.float    :user_volatility

      t.integer  :challenge_id
      t.float    :challenge_rating
      t.float    :challenge_rating_deviation
      t.float    :challenge_volatility

      t.boolean  :user_won

      t.timestamps
    end
  end
end
