class AddFieldsToApiUser < ActiveRecord::Migration[4.2]
  def change
    add_column :api_users, :pipplet_clients_campaign_test_language, :string
    add_column :api_users, :pipplet_clients_campaign_name, :string
    add_column :api_users, :pipplet_clients_campaign_hashed_id, :string
    add_column :api_users, :pipplet_clients_campaign_state, :string
    add_column :api_users, :pipplet_clients_campaign_accuracy, :string

    add_column :api_users, :pipplet_clients_account_name, :string
    add_column :api_users, :pipplet_clients_account_api_group, :string

    rename_column :api_users, :test_language, :default_test_language
    rename_column :api_users, :language_name, :default_locale
    rename_column :api_users, :challenge_ids, :linguist_ids
  end
end
