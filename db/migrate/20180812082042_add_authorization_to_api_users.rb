class AddAuthorizationToApiUsers < ActiveRecord::Migration[4.2]
  def change
    remove_column :api_users, :next_questionable_profile, :integer
    remove_column :api_users, :pipplet_clients_api_url, :string
    remove_column :api_users, :pipplet_clients_api_token, :string
    remove_column :api_users, :sync_with_pipplet_clients, :boolean
    remove_column :api_users, :pipplet_clients_account_id, :integer
    remove_column :api_users, :pipplet_clients_campaign_id, :integer
    remove_column :api_users, :linguist_ids, :string
    remove_column :api_users, :pipplet_clients_campaign_test_language, :string
    remove_column :api_users, :pipplet_clients_campaign_name, :string
    remove_column :api_users, :pipplet_clients_campaign_hashed_id, :string
    remove_column :api_users, :pipplet_clients_campaign_state, :string
    remove_column :api_users, :pipplet_clients_campaign_accuracy, :string
    remove_column :api_users, :pipplet_clients_account_name, :string
    remove_column :api_users, :pipplet_clients_account_api_group, :string
    remove_column :api_users, :static_authentication_token, :string
    remove_column :api_users, :static_authentication_token_expires_at, :datetime

    add_column :api_users, :access_level, :integer, default: 0, null: false
    add_column :api_users, :deleted_at, :datetime

    # For the migration, give full access to all.
    ApiUser.find_each do |u|
      u.update(access_level: 1) # internal_readwrite
    end
  end
end
