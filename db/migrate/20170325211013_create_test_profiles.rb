class CreateTestProfiles < ActiveRecord::Migration[4.2]
  def change
    create_table :test_profiles do |t|
      t.string   'name', null: false
      t.integer  'status', default: 0, null: false

      t.integer  :next_questionable_profile
      t.integer  :max_questions_to_answer
      t.string   :test_languages
      t.string   :linguist_ids

      t.timestamps null: false
      t.datetime :deleted_at
    end
  end
end
