class AddSanitizedClientNameToTestInstances < ActiveRecord::Migration[7.0]
  def change
    safety_assured do
      add_column :test_instances,
                 :sanitized_client_name,
                 :virtual, # declaring a virtual column
                 type: :text, # what type the virtual column will return
                 as: "lower(client_name)", # computation
                 stored: true # storing the value on create/update
    end
  end
end
