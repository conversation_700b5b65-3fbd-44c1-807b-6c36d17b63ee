class Resultsv3 < ActiveRecord::Migration[4.2]
  def change
    remove_column :results, :user_type, :string
    remove_column :results, :user_id, :integer
    remove_column :results, :user_rating, :float
    remove_column :results, :user_rating_deviation, :float
    remove_column :results, :user_volatility, :float
    remove_column :results, :challenge_id, :integer
    remove_column :results, :challenge_rating, :float
    remove_column :results, :challenge_rating_deviation, :float
    remove_column :results, :challenge_volatility, :float
    remove_column :results, :user_won, :boolean
    remove_column :results, :rated, :boolean

    create_table :challenge_logs do |t|
      t.integer  'challenge_id'
      t.integer  'question_id'

      t.boolean  'success', default: false

      t.boolean  'rated', default: false
      t.datetime 'rated_at'

      t.integer  'production_user_id'
      t.float    'production_user_main_rating'
      t.float    'production_user_main_rating_deviation'
      t.float    'production_user_main_volatility'
      t.float    'production_user_production_rating'
      t.float    'production_user_production_rating_deviation'
      t.float    'production_user_production_volatility'

      t.integer  'reception_user_id'
      t.float    'reception_user_main_rating'
      t.float    'reception_user_main_rating_deviation'
      t.float    'reception_user_main_volatility'
      t.float    'reception_user_reception_rating'
      t.float    'reception_user_reception_rating_deviation'
      t.float    'reception_user_reception_volatility'

      t.string   'question_element_type'
      t.integer  'question_element_id'
      t.float    'question_element_main_rating'
      t.float    'question_element_main_rating_deviation'
      t.float    'question_element_main_volatility'
      t.float    'question_element_production_rating'
      t.float    'question_element_production_rating_deviation'
      t.float    'question_element_production_volatility'
      t.float    'question_element_reception_rating'
      t.float    'question_element_reception_rating_deviation'
      t.float    'question_element_reception_volatility'

      t.timestamps null: false
    end

    drop_table :question_choices
  end
end
