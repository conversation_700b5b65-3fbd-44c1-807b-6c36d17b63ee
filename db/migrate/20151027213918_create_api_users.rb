class CreateApiUsers < ActiveRecord::Migration[4.2]
  def up
    create_table :api_users do |t|
      t.string   :name, null: false
      t.string   :passphrase, null: false
      t.integer  :status, null: false, default: 0
      t.string   :authentication_token
      t.datetime :authentication_token_expires_at

      t.timestamps null: false
    end
  end

  def down
    drop_table :api_users
  end
end
