class ExaminerBillingInfo < ActiveRecord::Migration[6.1]
  def change
    add_column :examiners, :street,                 :string,  default: ''
    add_column :examiners, :street_number,          :string,  default: ''
    add_column :examiners, :city,                   :string,  default: ''
    add_column :examiners, :state,                  :string,  default: ''
    add_column :examiners, :zip_code,               :string,  default: ''
    add_column :examiners, :country_code,           :string,  default: ''
    add_column :examiners, :registration_number,    :string,  default: ''
    add_column :examiners, :company_name,           :string,  default: ''
    add_column :examiners, :vat_number,             :string,  default: ''
    add_column :examiners, :has_vat,                :bool,    default: false

    add_column :invoice_requests, :vat_amount,      :float,   default: 0
    add_column :invoice_requests, :examiner_invoice_reference, :string, default: ''
  end
end
