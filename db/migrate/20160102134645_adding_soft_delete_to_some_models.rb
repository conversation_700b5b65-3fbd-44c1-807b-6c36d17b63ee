class AddingSoftDeleteToSomeModels < ActiveRecord::Migration[4.2]
  def change
    # Soft delete everywhere!
    add_column :productions, :deleted_at, :datetime
    add_column :receptions, :deleted_at, :datetime
    add_column :results, :deleted_at, :datetime
    add_column :challenges, :deleted_at, :datetime
    add_column :questions, :deleted_at, :datetime
    add_column :question_elements, :deleted_at, :datetime

    # Add default values for statuses
    change_column :productions, :status, :integer, default: 0
    change_column :receptions, :status, :integer, default: 0
  end
end
