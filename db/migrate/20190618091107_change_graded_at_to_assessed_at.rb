class ChangeGradedAtToAssessedAt < ActiveRecord::Migration[4.2]
  def change
    rename_column :evaluations, :graded_at, :assessed_at
    add_column :evaluations, :is_reviewed, :bool, default: false

    # Take graded TI, pass all ti evals to delivered and update time stamps
    TestInstance.graded.each do |ti|
      ti.evaluations.each do |e|
        if e.may_deliver?
          e.deliver!('delivered_at')
          e.update(delivered_at: ti.graded_at)
        end
      end
    end

    # Update time stamp assessed_at
    Evaluation.assessed.each do |e|
      e.update(assessed_at: Time.now)
    end
  end
end
