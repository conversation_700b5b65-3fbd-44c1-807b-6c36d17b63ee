class CreateEvaluation < ActiveRecord::Migration[4.2]
  def change
    create_table :evaluations do |t|
      t.integer :test_instance_id
      t.integer :examiner_id
      t.integer   :preceding_evaluation_id
      t.integer   :evaluation_delay_id
      t.integer   :reminder_nb,               default: 0
      t.boolean   :payment_status,            default: false
      t.string 	:status
      t.string 	:payment_type
      t.string 	:evaluation_goal
      t.datetime 	:assigned_at
      t.datetime 	:canceled_at
      t.datetime  :completed_at
      t.datetime	:delivered_at
      t.datetime :graded_at
      t.datetime	:last_reminder_at
      t.datetime	:soon_overdue_at
      t.datetime :validated_at
      t.timestamps
    end

    create_table :evaluation_delays do |t|
      t.integer :evaluation_id
      t.string :delay_label
      t.integer 	:graded_time_limit
      t.integer 	:soon_overdue_time_limit
      t.float :payement_rate
      t.timestamps
    end

    add_column :test_instances, :evaluation_id, :integer
  end
end
