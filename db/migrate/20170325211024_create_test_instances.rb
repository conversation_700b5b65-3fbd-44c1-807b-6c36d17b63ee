class CreateTestInstances < ActiveRecord::Migration[4.2]
  def change
    create_table :test_instances do |t|
      # Associations
      t.integer     :user_id
      t.integer     :test_profile_id
      t.integer     :direct_user_id
      t.integer     :api_user_id

      t.string      :status, default: 'initialized', null: false

      # Test data
      t.datetime    :begin_date
      t.datetime    :end_date
      t.string      :test_language

      # Results
      t.string      :result_cecrl # A1, B1, etc
      t.integer     :result_rating # number from 0 to 100
      t.text        :detailed_evaluation # Text from examiner
      t.datetime    :graded_at

      # PDF report
      t.string      :report_file_name
      t.string      :report_content_type
      t.integer     :report_file_size
      t.datetime    :report_updated_at

      t.timestamps  null: false
      t.datetime    :deleted_at
    end
  end
end
