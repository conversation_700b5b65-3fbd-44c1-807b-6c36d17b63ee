class RemoveUnusedIndexes < ActiveRecord::Migration[6.1]
  def change
    # Low Scans, High Writes (dixit <PERSON>)
    remove_index :event_logs, %i[loggable_type category]

    # completely unused indexes, waste cpu and disk for nothing
    remove_index :dynamic_question_data, ['user_id'], name: :index_dynamic_question_data_on_user_id
    remove_index :next_questionables, ['user_id'], name: :index_next_questionables_on_user_id
    remove_index :dynamic_question_data, ['question_element_id'], name: :index_dynamic_question_data_on_question_element_id
    remove_index :next_questionables, ['reception_id'], name: :index_next_questionables_on_reception_id
    remove_index :score_versions, ['score_id'], name: :index_score_versions_on_score_id
    remove_index :question_elements, ['reception_score_id'], name: :index_question_elements_on_reception_score_id
    remove_index :question_elements, ['production_score_id'], name: :index_question_elements_on_production_score_id
    remove_index :test_profiles, ['test_instance_validation_id'], name: :index_test_profiles_on_test_instance_validation_id
    remove_index :challenges, ['production_layout_id'], name: :index_challenges_on_production_layout_id
    remove_index :challenges, ['reception_layout_id'], name: :index_challenges_on_reception_layout_id
    remove_index :delayed_jobs, %w[priority run_at], name: :delayed_jobs_priority

    # barely used, still waste but less
    remove_index :images, ['attachable_id'], name: :index_attachings_on_attachable_id
    remove_index :test_instances, ['direct_user_id'], name: :index_test_instances_on_direct_user_id
  end
end
