# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'MH-24'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Your car just broke down and you are calling your insurance company. Please explain them what happened and describe the situation.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S12V0.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'You work in an insurance company and have just received a call. Please fill in the form below according to what the caller says.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'What is the call about?',
                           elements: [
                             { word: 'A car accident', static_valid_answer: 'false' },
                             { word: 'A car breakdown', static_valid_answer: 'true' },
                             { word: 'A new client for a car policy', static_valid_answer: 'false' },
                             { word: 'A call for information on a car insurance', static_valid_answer: 'false' }
                           ]
                         })

    radio_words_recorder({
                           label: 'What is the issue?',
                           elements: [
                             { word: 'Fumes coming out of the engine', static_valid_answer: 'true' },
                             { word: 'A flat tire', static_valid_answer: 'false' },
                             { word: 'A broken windshield', static_valid_answer: 'false' },
                             { word: 'Defective brakes', static_valid_answer: 'false' }
                           ]
                         })
  end
end
