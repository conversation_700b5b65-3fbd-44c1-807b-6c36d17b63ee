# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-98'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP10',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Quel est le dernier poste occcupé par votre collègue ? Quelle est sa passion ?', :style => 'h2' }
                  ]
                  # END OF ACTION 3

                })

    text({ :text => 'Chers collègues et surtout, chers amis,', :style => 'p' })
    text({ :text => 'Les années passent et ne se ressemblent pas. Il y a 40 ans, j\'intégrais l\'entreprise DansLesVentes en tant que simple manoeuvre. Peu à peu, j\'ai gravi les échelons, devenant technicien puis chef de projet… Aujourd\'hui, aux portes de ma retraite, je la quitte en tant que Directeur des ventes pleinement satisfait de ses équipes.', :style => 'p' })
    text({ :text => 'Un grand merci à vous tous pour votre collaboration, votre confiance et votre ouverture d\'esprit. Je suis fier, mais aussi un peu ému, d\'avoir participé au développement d\'une entreprise qui m\'est chère et que je n\'ai pas peur d\'appeler deuxième famille.', :style => 'p' })
    text({ :text => 'Durant toutes ces années j\'aurais pris part à de nombreux projets, développé le secteur des ventes en Asie mais aussi accompagné et formé les jeunes pousses de l\'entreprise pour qui il est temps de prendre la relève pendant que je m\'en vais au soleil m\'adonner à ma passion de toujours : la randonnée.', :style => 'p' })
    text({ :text => 'Merci à tous de vous être réunis en cette occasion. Trinquons à l\'avenir et à la prospérité de DansLesVentes !', :style => 'p' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR05',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Vous venez d\'entendre le discours de départ à la retraite d\'un de vos anciens collègues.', :style => 'p' }
                  ]
                })

    audio_production

    radio_words_recorder({
                           label: 'Quel à été son dernier poste au sein de l\'entreprise ?',
                           elements: [
                             { word: 'Directeur général', static_valid_answer: 'false' },
                             { word: 'Assistant de direction', static_valid_answer: 'false' },
                             { word: 'Directeur des ventes', static_valid_answer: 'true' },
                             { word: 'Directeur produit', static_valid_answer: 'false' }
                           ]
                         })

    radio_words_recorder({
                           label: 'Quelle est sa passion ?',
                           elements: [
                             { word: 'Les raquettes', static_valid_answer: 'false' },
                             { word: 'La marche sportive', static_valid_answer: 'false' },
                             { word: 'Le tango', static_valid_answer: 'false' },
                             { word: 'La randonnée', static_valid_answer: 'true' }
                           ]
                         })
  end
end
