# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-99'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP10',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'A quel poste <PERSON>halie <PERSON> a-t-elle été promue ? Dans quel pays ?', :style => 'h2' }
                  ]
                  # END OF ACTION 3

                })

    text({ :text => 'Chers collègues et associés,', :style => 'p' })
    text({ :text => 'Après quinze ans de bons et loyaux service en tant que Directrice des ventes, c\'est avec une certaine fierté mais aussi un peu d\'émotion que nous vous annonçons la promotion de Nathalie Durand au poste de Directrice générale de notre filiale berlinoise. Sa prise de fonctions sera effective à compter du premier octobre prochain.', :style => 'p' })
    text({ :text => 'Succédant à Hans Peter qui a récemment pris une retraite méritée, elle sera en charge de poursuivre notre implantation sur le marché d’outre-Rhin et plus généralement de développer nos activités sur les marchés germanophones.', :style => 'p' })
    text({ :text => 'Nous remerçions chaleureusement Nathalie pour sa contribution à l\'essor de notre entreprise et lui souhaitons beaucoup d\'épanouissement et de réussite dans cette nouvelle étape de sa vie professionnelle.', :style => 'p' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR05',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Vous venez d\'apprendre la promotion d\'une de vos collègues.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'A quel poste a t-elle été promue ?',
                           elements: [
                             { word: 'Directrice générale', static_valid_answer: 'true' },
                             { word: 'Assistante de direction', static_valid_answer: 'false' },
                             { word: 'Directrice des ventes', static_valid_answer: 'false' },
                             { word: 'Directrice commerciale', static_valid_answer: 'false' }
                           ]
                         })

    radio_words_recorder({
                           label: 'Dans quel pays a t-elle été promue ?',
                           elements: [
                             { word: 'L\'Allemagne', static_valid_answer: 'true' },
                             { word: 'L\'Autriche', static_valid_answer: 'false' },
                             { word: 'Le Luxembourg', static_valid_answer: 'false' },
                             { word: 'La Suisse', static_valid_answer: 'false' }
                           ]
                         })
  end
end
