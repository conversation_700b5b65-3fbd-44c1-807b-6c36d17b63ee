# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-95'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP10',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Voici un descriptif de votre service.', :style => 'h2' },
                    { :text => 'Quelles langues sont parlées ?', :style => 'h2' },
                    { :text => 'Quelles zones sont couvertes ?', :style => 'h2' }
                  ]
                  # END OF ACTION 3

                })

    text({ :text => 'Dominique est la Directrice commerciale d\'Electrologs. Elle est en charge de la coordination de l\'équipe commerciale, du suivi des objectifs et de la gestion des projets.
Philippe est en charge des pays francophones d\'Europe. Il travaille pour Electrologs depuis 7 ans. Il parle anglais, français et néerlandais.
André est en charge des clients d\'Amérique du Nord et d\'Australie. Il est d\'origine canadienne et travaille pour Electrologs depuis 5 ans.
Monica est en charge des pays hispanophones et lusophones. Elle vient du Chili et travaille pour Electrologs depuis 4 ans. Elle parle espagnol, portugais et français.
Enfin, Marc, qui a rejoint l\'équipe en début d\'année, est en charge de l\'Afrique et de l\'extension des activités d\'Electrologs au Moyen-Orient. Il a vécu 9 ans en Afrique du Sud et parle couramment arabe.
Vous qui êtes germanophone, vous serez en charge de l\'Allemagne, la Suisse allemande et l\'Autriche qui comptent parmi nos plus gros clients.', :style => 'p' })

    mic_recorder(60)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR05',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Ecoutez la présentation du service dans lequel vous venez de prendre vos fonctions.', :style => 'p' }
                  ]

                })

    audio_production

    checkboxes({
                 label: 'Quelles sont les langues parlées ?',
                 elements: [
                   { value: 'Français', answer: '1' },
                   { value: 'Arabe', answer: '1' },
                   { value: 'Espagnol', answer: '1' },
                   { value: 'Portugais', answer: '1' },
                   { value: 'Allemand', answer: '1' },
                   { value: 'Grec', answer: '0' },
                   { value: 'Néerlandais', answer: '1' },
                   { value: 'Italien', answer: '0' },
                   { value: 'Anglais', answer: '1' },
                   { value: 'Russe', answer: '0' },
                   { value: 'Chinois', answer: '0' }
                 ]
               })

    checkboxes({
                 label: 'Quelles zones sont déjà couvertes par les différents commerciaux ?',
                 elements: [
                   { value: 'Les pays francophones', answer: '1' },
                   { value: 'La Russie', answer: '0' },
                   { value: 'Les Pays-Bas', answer: '0' },
                   { value: 'L\'Amérique du Nord', answer: '1' },
                   { value: 'L\'Australie', answer: '1' },
                   { value: 'Le Moyen-Orient', answer: '0' },
                   { value: 'Les pays hispanophones', answer: '1' },
                   { value: 'Les pays lusophones', answer: '1' },
                   { value: 'L\'Afrique', answer: '1' },
                   { value: 'Les pays germanophones', answer: '1' },
                   { value: 'L\'Asie', answer: '0' }
                 ]
               })
  end
end
