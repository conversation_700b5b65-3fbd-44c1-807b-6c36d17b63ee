# Status: OK
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'CF-59'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'You want to celebrate your mother\'s birthday at the restaurant with the whole family and want to sit at the indicated table.', :style => 'p' },
                    { :text => 'Explain your request to the receptionist. Precise which event you are celebrating.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S21V3.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'You are the receptionist in a restaurant. A client calls to book a table.', :style => 'p' },
                    { :text => 'Click the speaker and answer the questions below accordingly.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'Which event is to be celebrated?',
                           elements: [
                             { word: 'The father\'s birthday', static_valid_answer: 'false' },
                             { word: 'The mother\'s birthday', static_valid_answer: 'true' },
                             { word: 'Valentine\'s Day', static_valid_answer: 'false' },
                             { word: 'The 50th wedding anniversary', static_valid_answer: 'false' }
                           ]
                         })
  end
end
