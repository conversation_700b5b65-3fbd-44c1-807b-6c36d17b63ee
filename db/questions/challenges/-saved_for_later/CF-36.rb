# Status: OK
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'CF-36'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'You have just bought new IT material for you office but something is not working properly. You have decided to call the brand\'s customer service. Using the drawing, explain the situation.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S16V3.jpg', credit: '' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [{ :text => 'You are working in a customer service. A customer is calling to complain about a problem.', :style => 'p' }]
                })

    audio_production

    radio_words_recorder({
                           label: 'Which tool is not working?',
                           elements: [
                             { word: 'the mouse', static_valid_answer: 'true' },
                             { word: 'the hardware', static_valid_answer: 'false' },
                             { word: 'the software', static_valid_answer: 'false' },
                             { word: 'the printer', static_valid_answer: 'false' }
                           ]
                         })
  end
end
