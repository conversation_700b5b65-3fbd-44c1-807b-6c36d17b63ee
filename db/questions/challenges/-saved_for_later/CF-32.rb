# Status: OK
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'CF-32'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'You are in a job interview and it is time to negotiate your salary. Using the illustration, explain what salary you are expecting for this position ($55,000). Explain also that you would need a company car, and flexible working hours.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S15V1.jpg', credit: '' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'You are a recruiter negotiating the salary of your new employee.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'What are the candidate\'s salary expectations?',
                           elements: [
                             { word: '$25,000', static_valid_answer: 'false' },
                             { word: '$15,000', static_valid_answer: 'false' },
                             { word: '$55.000', static_valid_answer: 'true' }
                           ]
                         })

    checkboxes({
                 label: 'What is their special requirements?',
                 elements: [
                   { value: 'a reserved parking space', answer: '0' },
                   { value: 'a company vehicle', answer: '1' },
                   { value: 'luncheon vouchers', answer: '0' },
                   { value: 'flexible time off', answer: '1' },
                   { value: 'teleworking', answer: '0' }
                 ]
               })
  end
end
