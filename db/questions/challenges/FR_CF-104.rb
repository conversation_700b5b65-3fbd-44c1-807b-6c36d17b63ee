# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-104'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP03',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Où se passe la scène ? Quelle action est réalisée ?', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S23V1.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })
    23

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR05',
                  # END OF ACTION 4
                  texts: [
                    { :text => '', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'Où se passe la scène ?',
                           elements: [
                             { word: 'Sur un terrain de handball', static_valid_answer: 'false' },
                             { word: 'A la plage', static_valid_answer: 'false' },
                             { word: 'Sur un terrain de football', static_valid_answer: 'true' },
                             { word: 'Dans une salle de sports', static_valid_answer: 'false' }
                           ]
                         })

    radio_words_recorder({
                           label: 'Quelle action est décrite ?',
                           elements: [
                             { word: 'Le joueur est blessé', static_valid_answer: 'false' },
                             { word: 'L\'équipe rouge marque un but', static_valid_answer: 'false' },
                             { word: 'L\'équipe bleue marque un but', static_valid_answer: 'true' },
                             { word: 'Le gardien empêche l\'équipe adverse de marquer un but', static_valid_answer: 'false' }
                           ]
                         })
  end
end
