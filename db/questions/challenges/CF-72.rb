# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'CF-72'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'You are discussing the latest football match at the coffee machine with your colleagues. Describe what is happeing on the picture below.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S23V3.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'You are discussing the latest football match at the coffee machine with your colleagues. Listen to your colleague\'s speech', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'What is happening?',
                           elements: [
                             { word: 'The referee is giving a yellow card to the blue player', static_valid_answer: 'false' },
                             { word: 'The referee is giving a red card to the red player', static_valid_answer: 'false' },
                             { word: 'The referee is giving a red card to the blue player', static_valid_answer: 'false' },
                             { word: 'The referee is giving a yellow card to the red player', static_valid_answer: 'true' }
                           ]
                         })
  end
end
