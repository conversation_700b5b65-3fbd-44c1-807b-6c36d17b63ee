# Status: OK
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'CF-31'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'You are in a job interview and it is time to negotiate your salary. Give your current salary ($50,000) and your salary expectations for this position ($55,000).', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S15V1.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'You are a recruiter negotiating the salary of your new employee.', :style => 'p' }
                  ]
                })

    audio_production

    radio_words_recorder({
                           label: 'What are the candidate\'s salary expectations?',
                           elements: [
                             { word: '$50,000', static_valid_answer: 'false' },
                             { word: '$55,000', static_valid_answer: 'true' },
                             { word: '$65.000', static_valid_answer: 'false' }
                           ]
                         })
  end
end
