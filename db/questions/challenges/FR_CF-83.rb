# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-83'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Vous êtes un recruteur et vous recevez un candidat en entretien. Utilisez le dessin pour décrire les compétences que vous recherchez.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S15V2.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Vous êtes en entretien d\'embauche.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'Quelles sont les compétences requises pour ce poste ?',
                           elements: [
                             { word: 'Des compétences en langues', static_valid_answer: 'true' },
                             { word: 'Des compétences en informatique', static_valid_answer: 'false' },
                             { word: 'Des compétences dans la vente', static_valid_answer: 'false' },
                             { word: 'Des compétences en management', static_valid_answer: 'false' }
                           ]
                         })
  end
end
