# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-86'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Vous êtes dans une agence de voyages. Utilisez le dessin pour expliquez ce que vous désirez.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S27V0.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Vous êtes agent de voyages et en rendez-vous avec un client.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'Où veut se rendre le client ?',
                           elements: [
                             { word: 'Aux sports d\'hiver', static_valid_answer: 'false' },
                             { word: 'En Irlande', static_valid_answer: 'false' },
                             { word: 'Sur une île paradisiaque', static_valid_answer: 'true' },
                             { word: 'En Islande', static_valid_answer: 'false' }
                           ]
                         })

    radio_words_recorder({
                           label: 'Comment le client veut-il voyager ?',
                           elements: [
                             { word: 'En avion', static_valid_answer: 'true' },
                             { word: 'En bateau', static_valid_answer: 'false' },
                             { word: 'En train', static_valid_answer: 'false' },
                             { word: 'En aéroglisseur', static_valid_answer: 'false' }
                           ]
                         })
  end
end
