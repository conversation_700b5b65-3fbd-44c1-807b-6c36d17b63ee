# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-87'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'C\'est le premier jour de travail de votre nouveau collègue. Le dessin ci-dessous indique les horaires d\'affluence à la cantine. Expliquez lui quel est le meilleur horaire pour éviter la foule au déjeuner et à quel moment il y a le plus d\'affluence.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S28V0.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'C\'est votre premier jour de travail. Ecoutez les explications de votre nouveau collègue.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'Quel est le service décrit ?',
                           elements: [
                             { word: 'La réception', static_valid_answer: 'false' },
                             { word: 'La salle de sport', static_valid_answer: 'false' },
                             { word: 'La cantine', static_valid_answer: 'true' },
                             { word: 'Le service courrier', static_valid_answer: 'false' }
                           ]
                         })

    dropdown({
               label: 'Quel est le meilleur créneau pour s\'y rendre ?',
               elements: [
                 { element: '11h30 - 12h00', static_valid_answer: 'correct' },
                 { element: '12h00 - 12h30', static_valid_answer: 'incorrect' },
                 { element: '12h30 - 13h00', static_valid_answer: 'incorrect' },
                 { element: 'Après 13h00', static_valid_answer: 'incorrect' }
               ]
             })

    dropdown({
               label: 'Quel est le créneau avec le plus d\'affluence ?',
               elements: [
                 { element: '11h30 - 12h00', static_valid_answer: 'incorrect' },
                 { element: '12h00 - 12h30', static_valid_answer: 'correct' },
                 { element: '12h30 - 13h00', static_valid_answer: 'incorrect' },
                 { element: 'Après 13h00', static_valid_answer: 'incorrect' }
               ]
             })
  end
end
