# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_CF-62'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Vous ne vous sentez pas bien et décidez d\'aller à la pharmacie.', :style => 'p' },
                    { :text => 'Expliquez votre problème en utlisant le dessin.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S22V2.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Vous êtes pharmacien et vous vous occupez d\'un client.', :style => 'p' }
                  ]

                })

    image({ name: 'S22V1.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    audio_production

    radio_words_recorder({
                           label: 'Quels sont les symptômes du client ?',
                           elements: [
                             { word: 'De la toux', static_valid_answer: 'true' },
                             { word: 'Des maux de tête', static_valid_answer: 'false' },
                             { word: 'De la fièvre', static_valid_answer: 'false' },
                             { word: 'Des bouffées de chaleur', static_valid_answer: 'false' }
                           ]
                         })
  end
end
