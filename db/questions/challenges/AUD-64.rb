Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'AUD-64'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IA08',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  # END OF ACTION 3
                })

    image({ name: 'AUD-64.jpg', credit: 'Photo credit: <a href="https://www.flickr.com/photos/usdagov/**********/">USDAgov</a> via <a href="https://visualhunt.com">VisualHunt.com</a> / <a href="http://creativecommons.org/licenses/by/2.0/"> CC BY</a>' })

    text_area({
                label: 'instant_AUD-08_qetext01',
                elements: [
                  placeholder: 'Type in here...',
                  forbidden_words: %w[],
                  minimum_length: 50
                ]
              })
  end

  ###################################
  #== This is the reception part ==#
  ###################################

  reception do
    # Do nothing mate
  end
end
