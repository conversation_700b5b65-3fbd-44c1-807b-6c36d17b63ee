# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'CF-106'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP03',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Where does the scene take place?', :style => 'p' },
                    { :text => 'On which occasion was the picture taken?', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S33V0.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR05',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Listen to your colleague\'s story.', :style => 'p' }
                  ]

                })

    audio_production

    radio_words_recorder({
                           label: 'Where does the scene take place?',
                           elements: [
                             { word: 'In Paris', static_valid_answer: 'false' },
                             { word: 'In Egypt', static_valid_answer: 'true' },
                             { word: 'In London', static_valid_answer: 'false' },
                             { word: 'In Beijing', static_valid_answer: 'false' }
                           ]
                         })

    radio_words_recorder({
                           label: 'On which occasion was the picture taken?',
                           elements: [
                             { word: 'During a business trip', static_valid_answer: 'false' },
                             { word: 'At a party', static_valid_answer: 'false' },
                             { word: 'During family holidays', static_valid_answer: 'true' },
                             { word: 'At a wedding', static_valid_answer: 'false' }
                           ]
                         })
  end
end
