# Status:
Dir.glob('lib//*.rb').each { |r| load r }

IMAGES_ASSETS_PATH = File.join(Rails.root, %w[db questions images])

ChallengeBuilder.create_with_template('two-columns') do
  # c.production_duration = 180000
  # c.reception_duration  = 180000

  # ACTION 1 :
  # - Insert here the unique ID of your challenge
  c.linguist_id = 'FR_MH-7'
  # END OF ACTION 1

  ###################################
  #== This is the production part ==#
  ###################################

  production do
    instruction({

                  # ACTION 2 :
                  # - Change IP03 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IP22',
                  # END OF ACTION 2

                  # ACTION 3 :
                  # - Add as many {:text => 'glfzlf', :style => 'h2"} elements as you like, separated by a comma. Each one will be a line in the question header.
                  texts: [
                    { :text => 'Vous êtes dans un café et souhaitez commander à boire et à manger. Commandez les denrées indiquées par les flêches bleues.', :style => 'p' }
                  ]
                  # END OF ACTION 3

                })

    image({ name: 'S5V0a.jpg', credit: 'Illustration: © Virginia Garrido-Millan' })

    mic_recorder(30)
  end

  ###################################
  #== This is the production part ==#
  ###################################

  reception do
    instruction({

                  # ACTION 4:
                  # - Change IR05 by the correct label of the question
                  # - See pipplet/congif/locales/instructions.en.yml for reference
                  label: 'IR15',
                  # END OF ACTION 4
                  texts: [
                    { :text => 'Vous êtes serveur dans un café. Ecoutez la commande de votre client.', :style => 'p' }
                  ]

                })

    audio_production

    checkboxes({
                 label: 'Sélectionnez la commande du client.',
                 elements: [
                   { value: 'Sandwich', answer: '1' },
                   { value: 'Cookie', answer: '0' },
                   { value: 'Muffin', answer: '0' },
                   { value: 'Brownie', answer: '1' },
                   { value: 'Cappuccino', answer: '1' },
                   { value: 'Expresso', answer: '0' }
                 ]
               })
  end
end
