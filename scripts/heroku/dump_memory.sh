#!/bin/bash

# Default values
app_name="pipplet-preprod"
dynos=("web.1")

# Parse options
while getopts "a:" opt; do
    case $opt in
        a) app_name=$OPTARG ;;
        \?) echo "Invalid option: -$OPTARG" >&2
            exit 1 ;;
    esac
done

# Shift the positional arguments so only dyno names remain
shift $((OPTIND - 1))

# Check if any dynos are passed, otherwise use the default dyno value
if [ "$#" -eq 0 ]; then
    dynos=("web.1")
else
    dynos=("$@")
fi

# Ensure the local directory exists
mkdir -p ./tmp

for dyno in "${dynos[@]}"; do
    echo "Running heroku ps:exec on dyno: $dyno for app: $app_name"

    # Run the heroku ps:exec command and directly process its output line by line
    heroku ps:exec -a "$app_name" --dyno="$dyno" "ps -eo pid,ppid,comm,rss,vsz --sort -rss | grep ruby" | while IFS= read -r line; do
        # Extract PID and RSS values from the line
        pid=$(echo "$line" | awk '{print $1}')
        rss=$(echo "$line" | awk '{print $4}')

        # Convert RSS from kilobytes to megabytes and round to the nearest integer
        rss_mb=$(echo "$(( (rss + 512) / 1024 ))")m

        json_filename="${app_name}_${dyno}_${pid}_rss_${rss_mb}.json"

        echo "Running rbtrace for PID: $pid on dyno $dyno"

        # Update rbtrace command for heap dump
        echo "rbtrace --pid=${pid} --eval=\"Thread.new{require 'objspace'; GC.start(); io=File.open('/tmp/${json_filename}', 'w'); ObjectSpace.dump_all(output: io); io.close}.join\" --timeout=60;exit" | heroku ps:exec -a "$app_name" --dyno="$dyno"
        if [ $? -ne 0 ]; then
            echo "Failed to execute rbtrace on PID $pid for app $app_name on dyno $dyno"
            exit 1
        fi
        echo "Successfully executed rbtrace for PID: $pid"

        # Copy the heap dump file from the dyno to the local machine
        echo "Copying heap dump for PID: $pid from dyno $dyno to local ./tmp directory"
        heroku ps:copy "/tmp/${json_filename}" --dyno="$dyno" --app="$app_name" --output "./tmp/${json_filename}"
        if [ $? -ne 0 ]; then
            echo "Failed to copy heap dump for PID $pid from dyno $dyno"
            exit 1
        fi
        echo "Successfully copied heap dump for PID: $pid"

        # Remove the heap dump file from the dyno
        echo "Removing heap dump file from dyno $dyno"
        heroku ps:exec -a "$app_name" --dyno="$dyno" "rm /tmp/${json_filename}"
        if [ $? -ne 0 ]; then
            echo "Failed to remove heap dump file from dyno $dyno"
            exit 1
        fi
        echo "Successfully removed heap dump file from dyno $dyno"

        # Clear object allocation tracking
        echo "Clearing object allocation tracking for PID $pid on dyno $dyno"
        echo "rbtrace --pid=${pid} --eval=\"Thread.new{GC.start;require 'objspace';ObjectSpace.trace_object_allocations_stop;ObjectSpace.trace_object_allocations_clear}.join\" ; exit" | heroku ps:exec -a "$app_name" --dyno="$dyno"
        if [ $? -ne 0 ]; then
            echo "Failed to clear object allocation tracking for PID $pid on dyno $dyno"
            exit 1
        fi
        echo "Successfully cleared object allocation tracking for PID: $pid"
    done
done

echo "All dynos processed for app $app_name."
