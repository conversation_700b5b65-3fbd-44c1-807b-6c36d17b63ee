# == Schema Information
#
# Table name: examiners
#
#  id                                    :integer          not null, primary key
#  ai_config                             :jsonb
#  bank_accountnumber                    :string
#  bank_bic                              :string
#  bank_branch_code                      :string
#  bank_city                             :string
#  bank_country                          :string
#  bank_currency                         :string
#  bank_iban                             :string
#  bank_name                             :string
#  bank_sortcode                         :string
#  beta_features                         :boolean          default(FALSE)
#  city                                  :string           default("")
#  company_name                          :string           default("")
#  country_code                          :string           default("")
#  default_currency                      :string           default("EUR")
#  deleted_at                            :datetime
#  email                                 :string
#  has_vat                               :boolean          default(FALSE)
#  is_ai                                 :boolean          default(FALSE)
#  is_requestable                        :boolean          default(FALSE)
#  language                              :string
#  max_assessment_time                   :integer
#  max_assessment_time_limit             :integer          default(240)
#  max_eval_alert_at                     :datetime
#  max_evaluations                       :integer
#  name                                  :string
#  next_unavailable_end_date             :datetime
#  next_unavailable_start_date           :datetime
#  number_of_eval_since_last_peer_review :integer          default(0)
#  peer_review_interval                  :integer          default(0)
#  peer_review_number                    :integer          default(1)
#  rank                                  :integer
#  real                                  :boolean          default(TRUE)
#  registration_number                   :string           default("")
#  state                                 :string           default("")
#  status                                :integer          default("initialized")
#  street                                :string           default("")
#  street_number                         :string           default("")
#  vat_number                            :string           default("")
#  zip_code                              :string           default("")
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  user_id                               :integer
#  wise_recipient_id                     :bigint
#
# Indexes
#
#  examiners_real_index  (status) WHERE ((deleted_at IS NULL) AND ("real" IS TRUE))
#

class Examiner < ApplicationRecord
  include AASM
  BANK_FIELDS = %w[bank_currency bank_country bank_city bank_name bank_iban bank_bic bank_accountnumber bank_sortcode].to_set.freeze

  # Soft delete object
  acts_as_paranoid

  # ----------------------------------------
  # :section: Statuses
  # ----------------------------------------
  # Default is initialized (database setup)
  enum :status, {
    initialized: 0,
    active: 1,
    trusted: 2,
    expired: 3,
    on_trial_period: 4,
    pre_trial_period: 5
  }

  aasm column: :status, enum: true do
    state :initialized, initial: true
    state :on_trial_period
    state :active
    state :trusted
    state :pre_trial_period
    state :expired

    event :activate do
      transitions from: %i[pre_trial_period initialized on_trial_period trusted], to: :active
    end

    event :try do
      transitions from: %i[pre_trial_period initialized active trusted expired], to: :on_trial_period
    end

    event :trust do
      transitions from: %i[pre_trial_period initialized on_trial_period active expired], to: :trusted
    end

    event :pre_try, after_commit: [-> { PreTrialJob.perform_later(id) }] do
      transitions from: %i[initialized on_trial_period active], to: :pre_trial_period
    end

    event :expire, after_commit: [:after_expire_callback] do
      transitions from: %i[initialized pre_trial_period on_trial_period active trusted], to: :expired
    end
  end

  # ----------------------------------------
  # :section: Validations
  # ----------------------------------------
  validates :language, presence: true
  validates :max_assessment_time, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :max_assessment_time_limit, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :user, uniqueness: true, presence: true
  validates :next_unavailable_start_date, presence: true, if: -> { next_unavailable_end_date.present? }
  validates :next_unavailable_end_date, presence: true, if: -> { next_unavailable_start_date.present? }
  validates :next_unavailable_end_date, comparison: { greater_than: :next_unavailable_start_date }, if: -> { next_unavailable_start_date.present? && next_unavailable_end_date.present? }

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  has_many :evaluations
  has_many :invoice_requests
  has_many :examiner_fees
  has_many :examiner_other_services

  belongs_to :user, autosave: true

  # ----------------------------------------
  # :section: Nested attributes
  # ----------------------------------------
  accepts_nested_attributes_for :user, :examiner_other_services

  # ----------------------------------------
  # :section: Callbacks
  # ----------------------------------------
  before_destroy :remove_role
  after_create :create_examiner_fees
  after_create :deliver_set_password_email
  before_destroy :move_evaluations_to_trash_examiner
  after_update -> { update(max_assessment_time: max_assessment_time_limit) }, if: -> { max_assessment_time_limit_changed? and max_assessment_time > max_assessment_time_limit }
  after_update :examiner_changed_max_assessment_time, if: -> { previous_changes[:max_assessment_time].present? }
  before_validation :update_vat
  after_validation :create_wise_recipient, if: -> { (changes['bank_iban'].present? || changes['bank_accountnumber'].present?) || wise_recipient_id.blank? }
  after_update_commit :billing_information_callback
  before_validation :assign_existing_user_by_email, on: :create
  before_validation :assign_default_user_attributes, on: :create

  def update_vat
    self.has_vat = country_code.present? && InvoiceRequest::VAT[country_code.to_s.to_sym].present? && vat_number.present?
  end

  def examiner_changed_max_assessment_time
    Statistic.create(label: "examiner_#{id}_max_assessment_time", data: max_assessment_time, data_time: Time.now)
  end

  # ----------------------------------------
  # :section: Scope
  # ----------------------------------------
  scope :usable_status, -> { where({ status: [Examiner.statuses[:active], Examiner.statuses[:trusted]] }) }
  scope :available, -> { where('(next_unavailable_start_date IS NULL) OR (next_unavailable_end_date IS NULL) OR (next_unavailable_start_date > ?) OR (next_unavailable_end_date < ?)', Time.now, Time.now) }
  scope :usable, -> { real.usable_status.available }
  scope :not_available, -> { where('(next_unavailable_start_date IS NOT NULL) AND (next_unavailable_end_date IS NOT NULL) AND (next_unavailable_start_date < ?) AND (next_unavailable_end_date > ?)', Time.now, Time.now) }
  scope :real, -> { where({ real: true }) }
  scope :with_rank, -> { where.not(rank: nil) }
  scope :ti_bulk_assign, -> { where(status: [Examiner.statuses[:active], Examiner.statuses[:trusted], Examiner.statuses[:on_trial_period]]) }
  scope :ordered_by_first_name, -> { joins(:user).order('users.first_name ASC') }
  scope :ai, -> { where(is_ai: true) }
  # ----------------------------------------
  # :section: Data duplication
  # ----------------------------------------

  # ----------------------------------------
  # :section: User delegation
  # ----------------------------------------
  delegate :email, to: :user, allow_nil: true
  delegate :full_name, to: :user, allow_nil: true
  alias name full_name

  # ----------------------------------------
  # :section: Instance methods
  # ----------------------------------------

  def after_expire_callback
    user_groups = UserGroup.user_group_for_examiner(self)
    user_groups.each do |ug|
      ug.discharge_examiner_for(language)
    end
    remove_role
  end

  def billing_info_complet?
    required_info = street.present? and street_number.present? and city.present? and zip_code.present? and country_code.present?
    required_info_french_country = has_vat? ? (registration_number.present? and vat_number.present?) : true
    required_info and required_info_french_country
  end

  def ready?
    active? || trusted?
  end

  def available?
    return true if next_unavailable_start_date.nil? || next_unavailable_end_date.nil?

    now = Time.now.to_i
    next_unavailable_start_date.to_i > now || now > next_unavailable_end_date.to_i
  end

  def can_assign_evaluation?(evaluation)
    errors = []

    addition_assessemnt_time = evaluation.assessment_type.assessment_time

    errors << 'More or equal assigned evaluations than max assessment time. ' if free_assessment_time_left < addition_assessemnt_time

    errors << 'Examiner expired. ' if expired?

    errors << 'Examiner unavailable. ' unless available?

    { result: errors.empty?, errors: errors }
  end

  def max_assessment_time_release_date
    date = evaluations.assessed_after(24.hours.ago).order('assessed_at asc').first&.assessed_at

    if date.nil?
      nil
    else
      24.business_hour.after(date)
    end
  end

  def remove_role
    return unless user&.is_examiner?

    user.remove_role :examiner
    save!
  end

  def send_max_eval_alert?
    max_eval_alert_at.nil? || (Time.now - max_eval_alert_at) > 1.day
  end

  def late_assessments(start_date = created_at, end_date = Time.now)
    evaluations.delivered.where('assigned_at > ?', start_date).where(assigned_at: ...end_date).select(&:was_late?)
  end

  def nb_eval_simple
    evaluations = self.evaluations.completed

    evaluations.simple.size
  end

  def nb_eval_written
    evaluations = self.evaluations.completed

    evaluations.written.size
  end

  def nb_eval_detailed
    evaluations = self.evaluations.completed

    evaluations.where('certification_type = ? OR certification_type = ?', 'detailed', 'flex').size
  end

  def build_invoice_request(evaluations)
    invoice_request = InvoiceRequest.created_or_sent.find_or_create_by(examiner_id: id)
    invoice_request.save!

    invoice_request.build_invoice_request(evaluations, self)
    invoice_request.associate_standing_examiner_other_services
    invoice_request
  end

  # TODO: add to EvaluationsManagerService
  def discharge_examiner
    evals = evaluations.assigned + evaluations.overdue + evaluations.soon_overdue
    evals.each do |e|
      ti = e.test_instance
      e.cancel!('canceled_at')
      # TODO : use the updated way to add Evaluations to TIs
      #  ti.add_and_force_assign_evaluation(examiner: self)
      ti.create_evaluation
      e = ti.evaluations.last
      e.examiner = self
      e.save!

      e.assign!('assigned_at')
    end
  end

  def assessment_time_used
    evaluations_in_backlog = evaluations.in_backlog_last(24.hours.ago).group(:assessment_type_id).count
    evaluations_in_backlog.sum do |assessment_type_id, count|
      AssessmentType.find(assessment_type_id).assessment_time * count
    end
  end

  def free_assessment_time_left
    return 0 if !available?

    free_time = max_assessment_time - assessment_time_used
    free_time.negative? ? 0 : free_time
  end

  def allocated_capacity_rate(start_date: 30.days.ago, end_date: Time.now)
    epsilon = 0.0001
    diff_days = (end_date - start_date) / 1.day.to_f
    assessment_time_in_period = evaluations.can_be_in_backlog.assigned_between(start_date, end_date).total_assessment_time
    max_assessment_time_in_period = diff_days * max_assessment_time
    (assessment_time_in_period + epsilon) / (max_assessment_time_in_period + epsilon)
  end

  def current_invoice_request
    invoice_requests.where.not(status: 'paid').last
  end

  def ongoing_pre_trial_evaluations?
    !evaluations.assigned.pre_trials.empty?
  end

  # TODO: add to EvaluationsManagerService
  def check_assigned_pre_trial
    ### Check if at least 3 pre trial was sent to examiner
    ### If not send 3 random pre trial eval
    nb_ti_needed = 3 - evaluations.pre_trials.assigned.count

    return unless nb_ti_needed.positive?

    tis = TestInstance.distinct
                      .graded
                      .joins(:evaluations)
                      .where('test_instances.created_at::date >= ?', '2021-12-01'.to_date)
                      .where(test_language: language)
                      .where.not(evaluations: { id: evaluations.pluck(:id) })
                      .order(graded_at: :desc)
                      .limit(nb_ti_needed)
    msg = ''
    tis.each do |ti|
      # TODO : use the updated way to add Evaluations to TIs
      #       ti.add_and_force_assign_evaluation(
      #         evaluation_goal: Evaluation::EVALUATION_GOAL[:pre_trial],
      #         evaluation_delay: EvaluationDelay.find_by(delay_label: 'pre_trial') || EvaluationDelay.first
      #         examiner: self
      #       )
      evaluation = Evaluation.new_for_test_instance(ti)
      evaluation.examiner = self
      evaluation.evaluation_goal = Evaluation::EVALUATION_GOAL[:pre_trial]
      evaluation.evaluation_delay = EvaluationDelay.find_by(delay_label: 'pre_trial') || EvaluationDelay.first
      evaluation.save!
      evaluation.force_assign!('assigned_at')
      msg = "\n#{evaluation.get_admin_edit_link}"
    end
    Alert.system('[Pre trial period][Random Pre trial eval assigned]', "No tis matches the criterias, eval assigned: #{msg}")
  end

  # TODO: add to EvaluationsManagerService or remove
  def send_pre_trial_evals
    [['A1+', 'A2-', 'A2', 'A2+'], ['B1-', 'B1', 'B1+', 'B2-', 'B2'], ['C1-', 'C1', 'C1+', 'C2-']].each do |grade_range|
      (0..5).reverse_each do |nb_trusted|
        ti = TestInstance.find_trial_tis(language, nb_trusted, grade_range, [TestProfile.certification_types[:detailed], TestProfile.certification_types[:flex]], id).first
        ti ||= TestInstance.find_trial_tis(language, nb_trusted, grade_range, [TestProfile.certification_types[:simple]], id).first

        next unless ti.present?

        # TODO : use the updated way to add Evaluations to TIs
        #       ti.add_and_force_assign_evaluation(
        #         examiner: self,
        #         evaluation_goal: Evaluation::EVALUATION_GOAL[:pre_trial],
        #         assessment_type: AssessmentType.find_by(name: 'detailed'),
        #         evaluation_delay: EvaluationDelay.find_by(delay_label: 'pre_trial') || EvaluationDelay.first,
        #       )
        evaluation = Evaluation.new_for_test_instance(ti)
        evaluation.examiner = self
        evaluation.evaluation_goal = Evaluation::EVALUATION_GOAL[:pre_trial]
        evaluation.assessment_type = AssessmentType.find_by(name: 'detailed')
        evaluation.evaluation_delay = EvaluationDelay.find_by(delay_label: 'pre_trial') || EvaluationDelay.first
        evaluation.save!
        evaluation.force_assign!('assigned_at')
        break
      end
    end

    check_assigned_pre_trial
  end

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------
  def self.list_examiner_by_allocated_capacity_rate_asc(test_instance:)
    forbidden_examiners_ids = test_instance.get_already_used_examiner_ids

    Examiner.real.where.not(id: forbidden_examiners_ids).for(test_instance.test_language_sym).sort_by do |ex|
      ex.allocated_capacity_rate(start_date: 24.hours.ago)
    end
  end

  def self.get_daily_workload_rate(lang:)
    examiners = Examiner.usable_for(lang)
    examiners_capacity = examiners.sum(:max_assessment_time).to_f
    return 0.0 if examiners_capacity.zero?

    workload = Evaluation.joins(:assessment_type).where(examiner: examiners.pluck(:id)).in_backlog_last(24.hours.ago).sum(:assessment_time).to_f
    workload / examiners_capacity
  end

  def self.with_smallest_allocated_capacity_rate(language:)
    examiners = Examiner.where(language: language, real: true)
    sorted_trusted_examiners = examiners.trusted.sort_by do |examiner|
      examiner.allocated_capacity_rate(start_date: 1.days.ago)
    end
    sorted_active_examiners = examiners.active.sort_by do |examiner|
      examiner.allocated_capacity_rate(start_date: 1.days.ago)
    end
    sorted_trusted_examiners.first || sorted_active_examiners.first
  end

  def self.for(lang)
    return [] unless AVAILABLE_TEST_LANGUAGES.include?(lang.to_sym)

    Examiner.usable.where(language: lang).order(rank: :asc)
  end

  def self.usable_for(lang)
    return [] unless AVAILABLE_TEST_LANGUAGES.include?(lang.to_sym)

    Examiner.real.usable.where(language: lang).order(rank: :asc)
  end

  def self.rank_list_for(lang)
    return [] unless AVAILABLE_TEST_LANGUAGES.include?(lang.to_sym)

    Examiner.where(language: lang).order(rank: :asc)
  end

  def self.first_for(lang)
    return nil unless AVAILABLE_TEST_LANGUAGES.include?(lang)

    Examiner.usable.where(language: lang).order(rank: :asc).first
  end

  def self.on_trial_period_for(lang)
    return [] unless AVAILABLE_TEST_LANGUAGES.include?(lang.to_sym)

    Examiner.on_trial_period.where(language: lang).order(rank: :asc)
  end

  def self.pipplet_admin_examiner(lang)
    admin_examiner_email = "pippletadminexaminer_#{lang}@pipplet.com"
    find_or_create_pipplet_examiner(lang, admin_examiner_email, 'Admin')
  end

  def self.find_or_create_pipplet_examiner(language, email, name = '', ai_config: nil)
    examiner = Examiner.joins(:user).find_by(language:, users: { email: })
    if examiner.nil?
      user = User.find_by_email(email) || User.new({
                                                     first_name: "Pipplet_#{name}_examiner_#{language}",
                                                     last_name: 'Pipplet',
                                                     email:
                                                   })
      examiner = Examiner.new({
                                user:,
                                language:,
                                max_assessment_time: 60 * 24 * 30, ## One month in minutes
                                max_assessment_time_limit: 60 * 24 * 30, ## One month in minutes
                                rank: 100,
                                status: Examiner.statuses[:trusted],
                                real: false,
                                ai_config:,
                                is_ai: ai_config.present?
                              })
    end
    examiner.assign_attributes(ai_config:) if ai_config && examiner.ai_config.blank?
    examiner.save!
    examiner
  end

  def self.find_admin_linguistic_examiner(lang)
    find_or_create_pipplet_examiner(lang, "linguistics+adminexaminer#{lang}@pipplet.com", 'LinguisticsAdmin')
  end

  def self.assign_evaluations_to_trash_examiner(evaluations, lang)
    email = "linguistics+trash_examiner_#{lang}@pipplet.com"
    name = 'trash'
    examiner = Examiner.joins(:user).find_by(users: { email: email }) || Examiner.find_or_create_pipplet_examiner(lang, email, name)
    examiner.evaluations << evaluations
  end

  def pipplet_admin_examiner?
    name.start_with?('Pipplet_admin_examiner')
  end

  def assessment_fee(assessment_type_name:)
    examiner_fee = examiner_fees.joins(:assessment_type)
                                .find_by(assessment_types: { name: assessment_type_name })
    examiner_fee ? examiner_fee.fee.to_f : 0
  end

  def self.find_or_create_ai_examiner(lang:, ai_workflow: :talent_ai)
    config = Rails.application.config.ai[lang.to_sym][ai_workflow]
    # TODO: Make model mandatory
    examiner_name = <<~HEREDOC.squish.delete(' ')
      w_#{config[:written][:type]}
        #{'_' + config[:written][:model].gsub('-preview', '') if config[:written][:model]}
      _s_#{config[:spoken][:type]}
        #{'_' + config[:spoken][:model].gsub('-preview', '') if config[:spoken][:model]}
    HEREDOC
    ai_examiner_email = "linguistics+#{examiner_name}+#{lang}+v#{AI_EXAMINER_VERSION}@pipplet.com"
    find_or_create_pipplet_examiner(lang, ai_examiner_email, "#{examiner_name}_ai", ai_config: config)
  end

  private

  def move_evaluations_to_trash_examiner
    Examiner.assign_evaluations_to_trash_examiner(evaluations, language)
  end

  def create_examiner_fees
    AssessmentType.where(name: EXAMINER_FEES_VALUES.keys).find_each do |assessment_type|
      ExaminerFee.create(examiner: self, currency: EXAMINER_FEES_DEFAULT_CURRENCY.upcase, fee: EXAMINER_FEES_VALUES[assessment_type.name], assessment_type:)
    end
  end

  def create_wise_recipient
    return if (bank_iban.blank? && bank_accountnumber.blank?) || @skip_create_wise_recipient

    @skip_create_wise_recipient = true
    wise_service = WiseService.new(recipient_id: wise_recipient_id)
    self.wise_recipient_id = wise_service.create_recipient(name: name, email: email, currency: bank_currency,
                                                           account_number: bank_iban.presence || bank_accountnumber.presence,
                                                           sort_code: bank_sortcode,
                                                           branch_code: bank_branch_code)
  rescue StandardError
    errors.add(:bank_iban)
  end

  def billing_information_callback
    data = previous_changes.slice(*BANK_FIELDS)
    return unless data.any?

    message = <<~HEREDOC
      Examiner: #{name}
      Link: #{Rails.application.routes.url_helpers.edit_admin_examiner_url(id)}
    HEREDOC

    data.each do |key, values|
      message << <<~HEREDOC
        - #{key} changed from #{values.first || 'empty value'} to #{values.last}
      HEREDOC
    end

    Alert.examiner_activity("[EXAMINER][#{name}] - Billing information changed", message)

    ExaminerService.new.update_cells_on_spreadsheet(recipient_id: wise_recipient_id, bank_currency:) if data[:bank_currency]
  end

  def assign_existing_user_by_email
    existing_user = User.find_by_email(self.user&.email)
    self.user = existing_user if existing_user
  end

  def assign_default_user_attributes
    self.user ||= User.new
    self.user.language_name = language unless self.user.language_name
    self.user.group = 'internal' unless self.user.group
    self.user.add_role :examiner unless self.user.has_role?(:examiner)
    self.user.understands_cgu = true
    self.user.skip_confirmation!
    self.user.reset_password_token = SecureRandom.hex(64)
  end

  def deliver_set_password_email
    ExaminerMailer.set_password(id).deliver_later if real?
  end
end
