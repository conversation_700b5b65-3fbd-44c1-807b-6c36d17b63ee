# == Schema Information
#
# Table name: layouts
#
#  id         :integer          not null, primary key
#  name       :string
#  filename   :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#

class Layout < ApplicationRecord
  class << self
    attr_accessor :view_path
  end
  @view_path = 'layout_templates'

  # Note: We could add 2 associations to challenges

  has_many :layout_zones, dependent: :destroy do
    def with_name(n)
      find_by(name: n.to_s)
    end
  end

  validates_presence_of   :name, :filename
  validates_uniqueness_of :name, :filename

  def template_name
    self.class.view_path.to_s + '/' + self.filename
  end

  # def zone(n)
  #   layout_zones.with_name(n)
  # end
end
