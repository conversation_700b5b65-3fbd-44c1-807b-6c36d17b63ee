# == Schema Information
#
# Table name: registration_codes
#
#  id              :integer          not null, primary key
#  name            :string           not null
#  code            :string           not null
#  expiration_date :datetime
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  max_usages      :integer
#  status          :integer          default("inactive"), not null
#

class RegistrationCode < ApplicationRecord
  # ----------------------------------------
  # :section: Statuses
  # ----------------------------------------
  # Default is inactive (database setup)
  enum :status, { inactive: 0, active: 1, expired: 2 }

  # Move through statuses
  def activate!; active!; end
  def expire!;   expired!; end

  # ----------------------------------------
  # :section: Validations
  # ----------------------------------------
  validates :name, uniqueness: true, presence: true, length: { maximum: 50 }
  validates :code, uniqueness: true, presence: true, length: { minimum: 4, maximum: 50 }, format: /\A[a-zA-Z0-9\-_]+\z/

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  has_many :users

  # ----------------------------------------
  # :section: Instance methods
  # ----------------------------------------

  # Confirm that a given code can be used
  # @return: boolean
  def is_valid?
    return false if self.status != 'active'
    return false if (self.expiration_date) ? Time.now > self.expiration_date : false
    return false if (self.max_usages && self.max_usages > 0) ? users.count >= self.max_usages : false

    true
  end

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------

  # Returns the list of all currently valid registration codes
  # @return: Array[RegistrationCode]
  def RegistrationCode.valids
    where(status: RegistrationCode.statuses.fetch(:active))
      .where("(registration_codes.expiration_date IS NULL OR registration_codes.expiration_date > ?)", Time.now)
      .select { |r| r.max_usages.nil? || (r.max_usages > 0 && r.max_usages > r.users.count) }
  end
end
