# == Schema Information
#
# Table name: test_instance_validations
#
#  id                           :bigint           not null, primary key
#  name                         :string           default("")
#  final_validation_rules       :json
#  validation_launching_rules   :json
#  conditional_validation_rules :json
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  tekis_rules                  :json
#  redos_rules                  :json
#
class TestInstanceValidation < ApplicationRecord
  has_many :test_profiles

  def validation_launching_rules_sym
    validation_launching_rules.deep_symbolize_keys
  end

  def final_validation_rules_sym
    final_validation_rules.deep_symbolize_keys
  end

  def conditional_validation_rules_sym
    conditional_validation_rules.deep_symbolize_keys
  end

  def tekis_rules_sym
    tekis_rules.deep_symbolize_keys
  end

  def default_validation_details(passed: true, details: [])
    {
      passed: passed,
      details: details
    }
  end

  def global_pre_validations
    @productions.count == @test_instance.test_profile.question_set_size
  end

  def save_validation_details(json_details)
    @test_instance.save_validation_details(json_details)
    @test_instance.reload
  end

  def post_validations(conditional_val_res)
    if conditional_val_res.dig(:full_skiped, :passed) == true
      EventLog.add(@test_instance, :validation, 'Full skip/timeout : deliver_no_answers_evaluation', :warning)
      evaluation = @test_instance.evaluations.created.last
      evaluation.deliver_no_answers_evaluation if evaluation.present?
    end
    if conditional_val_res.dig(:full_admin_answered, :passed) == true
      EventLog.add(@test_instance, :validation, 'Admin answered : deliver_no_answers_evaluation', :warning)
      evaluation = @test_instance.evaluations.created.last
      evaluation.deliver_no_answers_evaluation if evaluation.present?
    end

    ## Check test instance tags and create a no_answers_evaluation if needed
    if @test_instance.tags.pluck(:name).intersect? Tag::TEST_INSTANCE[:deliver_no_answers_evaluation]
      EventLog.add(@test_instance, :validation, 'Cheating detected : deliver_no_answers_evaluation', :warning)
      evaluation = @test_instance.evaluations.created.last
      evaluation.deliver_no_answers_evaluation if evaluation.present?
    end
  end

  def set_object_validations(test_instance)
    @productions = test_instance.ordered_completed_productions
    @test_instance = test_instance
  end

  def start_validations
    conditional_val_res = compute_validations(launch_validations, conditional_validation_rules_sym)
    final_res = compute_validations(conditional_val_res, final_validation_rules_sym)
    save_validation_details(conditional_val_res)

    if final_res[:validations][:passed]
      @test_instance.test_instance_validation_detail.pass!
    else
      @test_instance.test_instance_validation_detail.fail!
    end
    post_validations(conditional_val_res)
    final_res[:validations][:passed]
  end

  def get_detailed_internal_validations(test_instance)
    set_object_validations(test_instance)
    launch_validations
  end

  def get_tekis_details(test_instance)
    set_object_validations(test_instance)

    tekis_result = {}
    internal_validations = launch_validations
    tekis_rules_sym.each do |key, rule_info|
      evaluation = evaluate(rule_info[:rule], internal_validations)[:passed]
      tekis_result[key.to_sym] = { evaluation: evaluation, email_template: rule_info[:email_template] }
    end
    tekis_result
  end

  def get_teki_category(test_instance)
    set_object_validations(test_instance)
    return nil unless global_pre_validations

    tekis_details = get_tekis_details(test_instance)
    tekis_details.each do |key, details|
      return key if details[:evaluation]
    end
    nil
  end

  def get_teki_email_template(key)
    return nil if key.nil?

    tekis_rules_sym.dig(key.to_sym, :email_template)
  end

  def launch_auto_redo(test_instance)
    set_object_validations(test_instance)
    redo_asked = false
    return redo_asked unless global_pre_validations

    if @test_instance.test_instance_validation_detail&.failed?
      internal_validations = launch_validations
      redos_rules.each do |rule_info|
        rule_info.deep_symbolize_keys!
        passed = evaluate(rule_info[:rule], internal_validations)
        if passed[:passed] == false
          redo_asked = true
          @test_instance.redo_questionable('production', @productions[rule_info[:prod_index]].id)
        end
      end
    end

    redo_asked
  end

  def get_validation_result(test_instance)
    set_object_validations(test_instance)
    if global_pre_validations
      start_validations
    else
      save_validation_details(
        {
          pre_validations: default_validation_details(passed: false, details: ['One or more productions are missing'])
        }
      )
    end
    @test_instance.test_instance_validation_detail.passed?
  end

  def compute_sum_of_and(validation_results, res_id_key, key)
    key = key.to_sym
    res_id_key = res_id_key.to_sym
    validation_result = validation_results[res_id_key]

    if validation_results[key].nil?
      validation_results[key] = validation_result.deep_dup
    else
      validation_results[key][:passed] &&= validation_result[:passed]
      validation_results[key][:details] += validation_result[:details]
    end
  end

  def set_not_keys(validation_hash)
    new_hash = validation_hash.deep_dup
    validation_hash.each do |key, details|
      new_hash[:"!#{key}"] = { passed: !details[:passed], details: details[:details] }
    end
    new_hash
  end

  def launch_validations
    validation_results = {}
    validation_launching_rules_sym.each do |instance_method, iterative_args|
      method_to_call = method(instance_method)
      iterative_args.each do |key, args|
        res_id_key = :"#{instance_method}_#{key}"
        validation_results[res_id_key] = method_to_call.call(args)
        compute_sum_of_and(validation_results, res_id_key, key)
        compute_sum_of_and(validation_results, res_id_key, instance_method)
      end
    end
    set_not_keys(validation_results)
  end

  def compute_validations(validation_result, rules)
    conditional_validation_result = {}
    rules.each do |key, condition|
      conditional_validation_result[key] = evaluate(condition, validation_result)
    end
    conditional_validation_result
  end

  def evaluate(conditions, validation_result)
    conditions = [conditions].flatten
    passed = true
    details = []

    conditions.each_with_index do |key, ind|
      next if %w[&& ||].include?(conditions[ind - 1])

      case key
      when Array
        res = evaluate(key, validation_result)
        passed = res[:passed]
      when '&&'
        res = evaluate(conditions[ind + 1], validation_result)
        passed &&= res[:passed]
      when '||'
        res = evaluate(conditions[ind + 1], validation_result)
        passed ||= res[:passed]
      else
        res = validation_result[key.to_sym]
        passed = res[:passed]
        EventLog.add(@test_instance, :validation, "Validation: #{key} - #{passed ? 'Yes' : 'No'} #{res[:details].join(', ')}", :info)
      end

      details += res[:details].to_a
    end
    { passed: passed, details: details }
  end

  ################################ Methods launched by validations ################################

  # passed = true will create an admin_answered evaluation
  def full_prod_timeout_skip_validation(data)
    valid_details = default_validation_details
    productions = data[:productions].map { |production| @productions[production] }
    valid_recording_duration = Production.valid_total_recording_duration?(productions:)
    valid_total_text_length = Production.valid_total_text_length?(productions:)

    if valid_recording_duration
      EventLog.add(@test_instance, :admin, 'The total audio length is valid.', :info)
    else
      EventLog.add(@test_instance, :admin, 'The total audio length is invalid.', :info)
    end

    if valid_total_text_length
      EventLog.add(@test_instance, :admin, 'The total number of characters is valid.', :info)
    else
      EventLog.add(@test_instance, :admin, 'The total number of characters is invalid.', :info)
    end

    if valid_recording_duration && valid_total_text_length
      valid_details[:passed] = false
      return valid_details
    end

    productions.reject(&:admin_answered?).each_with_index do |production, index|
      if production.cant_be_assessed?
        valid_details[:details] << "Production #{index + 1}: [id: #{production.id}] is skipped, being_answered, language failed or timeout without answer"
      else
        valid_details[:passed] = false
      end
    end

    valid_details
  end

  def full_prod_admin_answered_skip_validation(data)
    valid_details = default_validation_details
    total = 0
    data[:productions].each do |production_ind|
      production = @productions[production_ind]
      next if !production.admin_answered? || production.has_answer?

      total += 1
      valid_details[:details] << "Production #{production_ind + 1}: [id: #{production.id}] is admin_answered"
    end

    valid_details[:passed] = total.eql?(data[:productions].size)
    valid_details
  end

  def check_caracters_match_with_language_validation(data)
    valid_details = default_validation_details
    production = @productions[data[:prod]]

    return valid_details if production.timeout? && production.recording_duration == 0 && production.text_length == 0

    if production.text_length > 0 && @test_instance.perform_check_characters? && !production.check_caracters_match_with_language
      valid_details[:passed] = false
      valid_details[:details] << "Production  #{data[:prod] + 1}: [id: #{production.id}] has characters that do not match the language"
    end

    # Check if audio transcription text equal the test language
    if production.production_metadatum.present? && production.production_metadatum.updated_transcription?
      validations = production.production_metadatum.validations

      unless validations[:identified_language]
        valid_details[:passed] = false
        valid_details[:details] << "Production  #{data[:prod] + 1}: [id: #{production.id}] has characters that do not match the language"
        production.language_failed!
      end
    end

    valid_details
  end

  def too_short_validation(data)
    valid_details = default_validation_details
    production = @productions[data[:prod]]

    return valid_details if production.admin_answered? || production.skipped? || production.timeout?

    if production.too_short?
      valid_details[:passed] = false
      valid_details[:details] << "Production #{data[:prod] + 1}: [id: #{production.id}] has to short answer"
    end

    valid_details
  end

  def transcribe_too_short_validation(data)
    valid_details = default_validation_details
    production = @productions[data[:prod]]

    return valid_details if production.admin_answered? || production.skipped?
    return valid_details if production.timeout? && production.recording_duration == 0 && production.text_length == 0

    if production.production_metadatum.present? && production.production_metadatum.updated_transcription?
      validations = production.production_metadatum.validations

      unless validations[:minium_transcription_length]
        valid_details[:passed] = false
        valid_details[:details] << "Production #{data[:prod] + 1}: [id: #{production.id}] has to short transcription"
      end
    end
    valid_details
  end

  def no_audio(data)
    valid_details = default_validation_details(passed: false)
    audios_len = data[:productions].sum do |prod_ind|
      @productions[prod_ind].recording_duration
    end

    if audios_len == 0
      valid_details[:passed] = true
      valid_details[:details] << 'No audio recording'
    end

    valid_details
  end

  def has_recording(data)
    valid_details = default_validation_details
    production = @productions[data[:prod]]

    return valid_details if production.admin_answered? || production.skipped?
    return valid_details if production.timeout? && production.recording_duration == 0 && production.text_length == 0

    if production.production_metadatum.present? && production.production_metadatum.updated_transcription?
      audios_len = production.recording_duration
      transcription = production.production_metadatum.production_transcription

      if (audios_len > 0) && (transcription.to_s.split.count <= 3) && (transcription.to_s.split('').count < 15)
        valid_details[:passed] = false
        valid_details[:details] << "Production #{data[:prod] + 1}: [id: #{production.id}] has an empty recording"
      end
    end

    valid_details
  end
end
