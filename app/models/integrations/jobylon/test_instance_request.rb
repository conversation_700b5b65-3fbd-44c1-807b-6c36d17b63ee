class Integrations::Jobylon::TestInstanceRequest < Integrations::TestInstanceRequest
  attr_accessor :data

  def initialize(attributes = nil, api_user: nil)
    attributes[:test_profile_id] = api_user.default_test_profile_id
    attributes[:language] = api_user.default_test_language
    super(attributes)
  end

  def client_data
    {}
  end

  def candidate_data
    {
      email: data[:candidate][:email],
      first_name: data[:candidate][:first_name],
      last_name: data[:candidate][:last_name]
    }
  end

  def order_information
    {
      company_id: data[:company][:id],
      candidate_id: data[:candidate][:id]
    }
  end
end
