class Integrations::Teamtailor::PartnerResults
  # +--------------+--------+------------------------------------------------------------------------------------------------------------+
  # |  Attributes  |        |                                                                                                            |
  # +--------------+--------+------------------------------------------------------------------------------------------------------------+
  # | Attribute    | Type   | Description                                                                                                |
  # | assessment   | object | Assessment object.                                                                                         |
  # | details      | object | Details object. May be any valid JSON object at most 2 levels deep. The data is displayed to the customer. |
  # | status       | string | May be “sending”, “sent”, “pending”, “completed” or “failed”.                                              |
  # | summary      | string | Summary text.                                                                                              |
  # | url          | string | URL for viewing a report on your website.                                                                  |
  # | candidate-id | string | Immutable UUID identifier for candidate.                                                                   |
  # | job-id       | number | ID of the job webhook was sent from.                                                                       |
  # | attachments  | list   | List of attachment objects containing keys: “url” and “description”.                                       |
  # +--------------+--------+------------------------------------------------------------------------------------------------------------+
  # +-------------------+---------+------------------------------------------+
  # | Assessment object |         |                                          |
  # +-------------------+---------+------------------------------------------+
  # | Attribute         | Type    | Description                              |
  # | score             | integer | Must be between 0 and 100.               |
  # | grade             | string  | May be “failed”, “passed” or “excelled”. |
  # | duration          | integer | Duration of test in seconds              |
  # +-------------------+---------+------------------------------------------+

  def graded?
    @status && @status == 'graded'
  end

  def rated?
    @result_rating && @result_rating.is_a?(Integer)
  end

  def initialize(job_id:, candidate_id:, api_order:)
    @job_id = job_id
    @candidate_id = candidate_id
    @status = api_order.test_instance&.status
    @result_rating = api_order.test_instance&.result_rating
    @test_url = api_order.test_instance&.test_url
    @report_url = api_order.test_instance&.report
    if rated?
      @cecrl_rating = api_order.test_instance&.result_rating_to_cecrl
      @score_date = api_order.test_instance&.graded_at&.to_fs(:long_ordinal)
    end
    if api_order.test_instance&.end_date && api_order.test_instance.begin_date
      @duration = api_order.test_instance.end_date - api_order.test_instance.begin_date
    end
  end

  def serializable_hash
    base = {
      data: {
        type: 'partner-results',
        attributes: {
          status: graded? ? 'completed' : 'sending',
          summary: if rated?
                     I18n.t("teamtailor.partner_results.summary.completed#{graded? ? '' : '_no_grade'}")
                   else
                     I18n.t('teamtailor.partner_results.summary.sending')
                   end,
          'candidate-id': @candidate_id,
          'job-id': @job_id,
          details: {},
          attachments: []
        }
      }
    }

    if graded?
      base[:data][:attributes][:attachments] << { url: @report_url, description: I18n.t('teamtailor.partner_results.attachments.description.pdf_report_url') } if @report_url
      base[:data][:attributes][:assessment] = { score: @result_rating || 0, grade: 'passed', duration: @duration.to_i }
      base[:data][:attributes][:details]['Overall score rating'] = @result_rating
      base[:data][:attributes][:details]['Overall score cecrl'] = @cecrl_rating
      base[:data][:attributes][:details]['Overall score date'] = @score_date
    else
      base[:data][:attributes][:attachments] << { url: @test_url, description: I18n.t('teamtailor.partner_results.attachments.description.test_url') }
    end
    base
  end
end
