class Integrations::Teamtailor::Client
  # +---------------+--------+------------------------------------------------------------------------+
  # | Data included in the JWT token sent by <PERSON><PERSON>ail<PERSON> and configured with Integrations::Teamtailor::ActivationData. |                                                                   |
  # +---------------+--------+------------------------------------------------------------------------+
  # | Attribute     | Type   | Description                                                            |
  # | first-name    | string | First name for billing purposes                                        |
  # | last-name     | string | Last name for billing purposes                                         |
  # | email         | string | Email for billing purposes                                             |
  # +---------------+--------+------------------------------------------------------------------------+

  include ActiveModel::Validations

  attr_reader :first_name, :last_name, :email

  validates_email_format_of :email, allow_blank: false, check_mx: true, mx_message: 'invalid email domain'
  validates :first_name, presence: true
  validates :last_name, presence: true

  def initialize(client_data)
    @first_name = client_data['first-name']
    @last_name = client_data['last-name']
    @email = client_data['email']
  end

  def serializable_hash
    {
      'email' => @email,
      'first-name' => @first_name,
      'last-name' => @last_name
    }
  end
end
