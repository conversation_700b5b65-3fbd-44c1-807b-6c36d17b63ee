class Integrations::Lever::TestInstanceRequest < Integrations::TestInstanceRequest
  attr_accessor :event, :data

  def initialize(attributes = nil, api_user: nil)
    @api_user = api_user
    @opportunity = attributes[:opportunity]

    attributes[:test_profile_id] = @opportunity.tag ? TestProfile.find_by_name(@opportunity.tag[1])&.id : @api_user&.default_test_profile_id
    attributes[:language] = @opportunity.tag ? @opportunity.tag[2] : @api_user&.default_test_language

    super(attributes)
  end

  def client_data
    {}
  end

  def candidate_data
    split_name = @opportunity.name.split(' ', 2)
    {
      email: @opportunity.email,
      first_name: split_name[0],
      last_name: split_name[1] || '-'
    }
  end

  def order_information
    {
      opportunity_id: data['opportunityId']
    }
  end
end
