class Integrations::Lever::ConfigOpportunity
  include ActiveModel::Validations

  attr_reader :tags, :name, :perform_as

  def initialize(api_user:, tags:)
    @tags = tags
    @name = I18n.t('lever.config_opportunity.name')
    @perform_as = Lever::Api::Core::Users.get(api_user).id
    @archived_reason_id = Lever::Api.get_archive_reason(api_user)['id']
    @created_at = DateTime.now.strftime('%Q').to_i
  end

  def serializable_hash
    {
      tags: @tags,
      name: @name,
      createdAt: @created_at,
      archived: {
        reason: @archived_reason_id,
        archivedAt: @created_at
      }
    }
  end
end
