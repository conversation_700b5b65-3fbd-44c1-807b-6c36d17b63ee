class Integrations::Jobvite::TestInstanceRequest < Integrations::TestInstanceRequest
  # rubocop:disable Naming/MethodName
  attr_accessor :id, :applicationId, :network_error
  # rubocop:enable Naming/MethodName

  validates :language, inclusion: { in: AVAILABLE_TEST_LANGUAGES.map(&:to_s) }
  validates :test_profile_id, inclusion: { in: Rails.application.config.jobvite[:test_profile_ids] }

  validate do |test_instance_request|
    if test_instance_request.network_error
      errors.clear
      errors.add(:base, 'Network error while retrieving Candidate')
    end
  end

  def initialize(attributes = {}, api_user: nil)
    begin
      @candidate = ::Jobvite::Api::Core::Candidate.show(attributes['id'], api_user:)
      attributes[:test_profile_id] = @candidate.test_profile_id || api_user.default_test_profile_id
      attributes[:language] = @candidate.test_language || api_user.default_test_language
    rescue StandardError
      @network_error = true
    end
    super(attributes)
  end

  def client_data
    {}
  end

  def candidate_data
    {
      email: @candidate&.email,
      first_name: @candidate&.first_name,
      last_name: @candidate&.last_name
    }
  end

  def order_information
    {
      candidate_id: id,
      application_id: applicationId
    }
  end
end
