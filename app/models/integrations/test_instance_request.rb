class Integrations::TestInstanceRequest
  include ActiveModel::Model
  include ActiveModel::Validations
  include ActiveModel::Serialization

  attr_accessor(
    :candidate_data,
    :order_information,
    :client_data,
    :test_profile_id,
    :language
  )

  validates :candidate_data,    presence: true, if: proc { candidate_data.nil? }
  validates :order_information, presence: true, if: proc { order_information.nil? }
  validates :client_data,       presence: true, if: proc { client_data.nil? }
  validates :test_profile_id,   presence: true, format: { with: /\A\d+\z/ }
  validates :language,          presence: true, format: { with: /\A[a-z]{2,}\z/ }
  validate  :candidate_data do
    errors.add(:candidate, 'email is invalid')    if ValidatesEmailFormatOf.validate_email_format(candidate_data[:email])
    errors.add(:candidate, 'first Name is blank') if candidate_data[:first_name].blank?
    errors.add(:candidate, 'last Name is blank')  if candidate_data[:last_name].blank?
  end

  def initialize(attributes = {}, api_user: nil)
    @api_user = api_user
    filtered_attributes = attributes.select { |k, _v| self.class.attribute_method?(k.to_sym) }
    super(filtered_attributes)
  end
end
