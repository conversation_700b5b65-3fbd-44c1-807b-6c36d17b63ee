# == Schema Information
#
# Table name: score_versions
#
#  id               :integer          not null, primary key
#  score_id         :integer
#  scoring_batch_id :integer
#  created_at       :datetime
#  rating           :float
#  rating_deviation :float
#  volatility       :float
#  scorable_type    :string
#  scorable_id      :integer
#

class ScoreVersion < ApplicationRecord
  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  belongs_to :score
  belongs_to :scoring_batch

  # ----------------------------------------
  # :section: Class Methods
  # ----------------------------------------
  def ScoreVersion.create_from_score(_score)
    return if ScoreVersion.exists?({
                                     score_id: _score.id,
                                     scoring_batch_id: _score.scoring_batch_id,

                                     rating: _score.rating,
                                     rating_deviation: _score.rating_deviation,
                                     volatility: _score.volatility,
                                     scorable_type: _score.scorable_type,
                                     scorable_id: _score.scorable_id
                                   })

    ScoreVersion.create({
                          score_id: _score.id,
                          scoring_batch_id: _score.scoring_batch_id,

                          rating: _score.rating,
                          rating_deviation: _score.rating_deviation,
                          volatility: _score.volatility,
                          scorable_type: _score.scorable_type,
                          scorable_id: _score.scorable_id,

                          created_at: Time.now
                        })
  end
end
