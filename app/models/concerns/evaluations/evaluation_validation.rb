module Evaluations::EvaluationValidation
  extend ActiveSupport::Concern

  # TODO: to eventually remove when behaviour is checked
  UNEXPECTED_ERRORS = {
    double_delivery: 'An evaluation is already delivered on the TI'
  }

  included do
    def assessment_form_validation
      errors = []

      if certification_type == 'detailed'
        errors << 'no strengths' if get_json_assessment_value('strenghts').nil?
        errors << 'no improving' if get_json_assessment_value('recomandation').nil?
      end

      errors
    end

    def admin_examiner_review_validation
      ## We want all assessments by AI examiners to be reviewed by linguistics, except when A1- assessments
      errors = []
      return errors if examiner_average?

      do_check = test_instance.evaluations.client_grade_checks.empty?

      ex = Examiner.pipplet_admin_examiner(test_language)
      errors = ['Admin examiner, waiting manual review'] if (examiner_id == ex.id) && score.positive? && do_check
      errors
    end

    def duplicate_test_instance_validation
      errors = []
      return errors if skip_duplicate_validation?

      duplicates = test_instance.check_duplicate
      return errors if duplicates.blank?

      duplicates.includes(:evaluations).find_each do |ti|
        old_eval = ti.evaluations.delivered.last
        diff_index = 0

        has_error = false
        score = 'NA'

        if old_eval.present?
          begin
            score = old_eval.certificate&.grades&.overall&.last&.cecrl_score
            diff_index = CECRL_LEVELS_VALUES[:cecrl].index(certificate&.grades&.overall&.last&.cecrl_score) - CECRL_LEVELS_VALUES[:cecrl].index(score)
          rescue StandardError
            # Ignored
          end
          if ti.recruitment?
            has_error = duplicate_delayed?(ti, diff_index, old_eval)
          elsif (diff_index >= 3) || diff_index.negative?
            has_error = true
          end
        elsif ti.evaluations.assigned.present? && ti.evaluations.assigned.count(&:standard?).positive?
          has_error = true
        end
        next unless has_error

        date_to_check = old_eval.assigned_at ? old_eval.assigned_at.strftime('%d/%m/%y') : ti.end_date&.strftime('%d/%m/%y')
        errors << "Dup #{ti.id} - #{ti.client_name} - #{score} (#{date_to_check || 'NC'})"
      end
      errors
    end

    def duplicate_delayed?(test_instance, diff_index, old_eval)
      return false unless test_instance.recruitment?
      return false unless old_eval.assigned_at.present?

      if diff_index.negative?
        return true if diff_index <= -3
        return false if diff_index == -1 && old_eval.assigned_at.after?(assigned_at - 2.weeks)

        !old_eval.assigned_at.before?(assigned_at - 1.year)
      elsif diff_index.positive? && diff_index >= 3
        old_eval.assigned_at.after?(assigned_at - 6.months)
      else
        false
      end
    end

    def certificate_validation(skip_send, _admin_eval)
      errors = []
      errors << 'error with the certificate' if skip_send
      unless test_instance.talent_ai?
        SYNTHESIS_GRADES.each do |grde|
          errors << "grade #{grde} nil" if assessment_type.json_calculation_rules.keys.include?(grde) && certificate&.grades&.where(label: grde).blank?
        end
      end

      errors
    end

    def evaluation_type_validation
      errors = []
      errors << "Evaluation goal is #{evaluation_goal}" if !standard? && !examiner_average? && !client_grade_check?
      errors << 'examiner status not trusted' if examiner.status != 'trusted'
      errors
    end

    def technical_issues_validation
      errors = []
      errors << 'Technical Issue ongoing' if test_instance.has_in_progress_tekis?
      errors
    end

    def assessment_tags_validation
      errors = []
      tags.each do |tag|
        next unless Tag::EVALUATION[:block_evaluation_delivery].include?(tag.name)

        errors << "Block with the tag: #{tag.name}"
        optional_comment = json_assessment["assessment_cheating_explanation-Comment"] ||
                           json_assessment["assessment_robust_explanation-Comment"]
        errors << "Comment: #{optional_comment}" if optional_comment
      end
      errors
    end

    def test_instance_tags_validation
      test_instance.tags.filter_map do |tag|
        "Block with the TI tag: #{tag.name}" if Tag::TEST_INSTANCE[:block_evaluation_delivery].include?(tag.name)
      end
    end

    def evaluation_already_delivered_validation
      test_instance.evaluations.delivered.any? ? [UNEXPECTED_ERRORS[:double_delivery]] : []
    end

    # TODO: to eventually remove when behaviour is checked
    def log_unexpected_errors_on_test_instance(errors)
      return unless UNEXPECTED_ERRORS.values & errors == UNEXPECTED_ERRORS.values

      EventLog.add(test_instance, :validation, "#{self.class.name} 'id'=#{id} failed validations with unexpected errors: #{errors}", :danger)
    end

    def validate(skip_send, admin_eval)
      test_instance = self.test_instance

      errors = []
      errors += assessment_form_validation
      errors += certificate_validation(skip_send, admin_eval)
      errors += evaluation_type_validation
      errors += test_instance.client_check_eval_errors
      errors << 'user is vip' if self.test_instance.user.is_vip?
      errors += duplicate_test_instance_validation
      errors += technical_issues_validation
      errors += admin_examiner_review_validation
      errors += assessment_tags_validation
      errors += test_instance_tags_validation
      errors += evaluation_already_delivered_validation

      log_unexpected_errors_on_test_instance(errors)

      errors
    end
  end
end
