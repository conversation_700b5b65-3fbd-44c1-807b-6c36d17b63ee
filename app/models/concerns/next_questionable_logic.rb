module NextQuestionableLogic
  extend ActiveSupport::Concern
  include NextQuestionableLogic::Helper

  # ----------------------------------------
  # :section: Add associations, class_attribute, validations, etc.
  # ----------------------------------------
  included do
    has_many :next_questionables, dependent: :delete_all do
      def random_order
        order('RANDOM()')
      end

      # This method must by used by algorithms in NextQuestionableLogic::RealtimeSelection
      # FILTERING: Pre-filter un-usable questions (those whose status has changed)
      # -> It removes productions which are not ready anymore (used by another user or removed by admin)
      # -> It removes receptions which are not ready anymore (used by another user or removed by admin)
      # -> It removes questionable whose challenged is not published ready anymore (removed by admin)
      #
      # @return: ActiveRecord query ready to be chained further
      def filtered_for_realtime_selection
        where_prod  = "(productions.id IS NOT NULL AND productions.status=#{Production.statuses.fetch(:ready)})"
        where_recep = "(receptions.id IS NOT NULL AND receptions.status=#{Reception.statuses.fetch(:ready)})"

        joins('LEFT OUTER JOIN productions on productions.id = next_questionables.production_id')
          .joins('LEFT OUTER JOIN receptions on receptions.id = next_questionables.reception_id')
          .joins(:challenge).merge(Challenge.published)
          .where("#{where_prod} OR #{where_recep}")
      end
    end
  end

  # After the user is confirmed, we initialize his test (prepare a list of next_questionables)
  def initialize_test
    # If we are in standard mode, do it asynchrounously as it is long
    # If we are in showcase or audition mode, do it synchronously as there is not many questions and they are required now

    # This only works for next_questionable_profile == fixed_audition
    # Other NQ profiles must be migrated to TestProfile/TestInstance model
    if ['fixed_audition'].include?(next_questionable_profile)
      update_next_questionables
    else
      # Launch async job to generate the list of next_questionable
      ComputeNextQuestionablesJob.perform_later(self)
    end
  end

  # ----------------------------------------
  # :section: Public API for NextQuestionableLogic
  # ----------------------------------------
  # TODO:
  # - How to add newly generated productions to next_questionable? In a separate CronJob?
  # - After the call to expire_questionable, for any user has less than X Challenges available
  # as NextQuestionable and last computation happened more than Y minutes ago, then recompute all NQ for
  # these users (In a separate Job)
  # - Implement MarketplaceHealthCheck job
  # - Move Recording from Production to Reception
  # - Create Reception#duplicate method
  # - Implement Demo Profile correctly (not random)
  # - Check that rake demo:setup still works!
  # - Add manually_set_next_question
  #
  # Tests...
  # - WRITE TESTS for NextQuestionable model, NextQuestionableLogic concern
  # - Re-write tests for modified models/concerns: Questionable, User, Challenge, BackgroundJobLog, Production, Reception, Alert, ChallengeLogs
  # - Write tests for Jobs
  #

  # Accessor for the questionable that is currently being answered
  # We look for the latest questionable that has been assigned to the user, but is still in status 'ready'
  #
  # @return: Production/Reception object or nil
  def current_questionable
    # Look for a production in progress
    p = productions
        .where(status: Production.statuses.fetch(:being_answered))
        .order('productions.displayed_at DESC')
        .first

    # Look for a reception in progress
    r = receptions
        .where(status: Reception.statuses.fetch(:being_answered))
        .order('receptions.displayed_at DESC')
        .first

    # Return the right questionable
    if p
      if r # both p and r exist. We return the most recent one.
        p.updated_at > r.updated_at ? p : r
      else # r is nil, p is not nil.
        p
      end

    else # p is nil
      r || nil
    end
  end

  #
  # Preparation method: prepare the list of eligible next_questionable for the user
  # Speed: slow (done in an async Job)
  #
  # Compute all possible next_questionables and save subset in db
  # @return: result of the insert transaction
  def update_next_questionables
    # 1- get list of all possible next_questionables
    # 2- apply some filters
    next_prod_and_recep = next_questionable_profile_module.post_fetch_filter(
      self, next_questionable_profile_module.fetch_all(self)
    )

    # 3- update db / cache
    # delete all next_questionables
    next_questionables.delete_all

    # Intermediate lists of object ids to batch SQL queries
    production_id_list = []

    # For productions...
    challenge_id_list = next_prod_and_recep[:productions].map(&:challenge_id)
    # For receptions...
    next_prod_and_recep[:receptions].each do |q|
      challenge_id_list << q.challenge_id
      production_id_list << q.production_id
    end

    # Building challenge_average_rating_hash
    challenge_average_rating_hash = []
    Challenge.select('challenges.id, challenges.average_rating_production, challenges.average_rating_reception').where(id: challenge_id_list.uniq).each do |c|
      challenge_average_rating_hash[c.id] = {
        production: c.average_rating(:production),
        reception: c.average_rating(:reception)
      }
    end

    # User score HashMap of the productions associated to receptions
    questionable_user_hash = []
    user_id_list = []

    Production.select('productions.id, productions.user_id').where(id: production_id_list.uniq).each do |p|
      questionable_user_hash[p.id] = { user_id: p.user_id }
      user_id_list << p.user_id
    end

    Score.select('scores.scorable_id, scores.rating').where(scorable_type: 'User', label: 'production',
                                                            scorable_id: user_id_list).each do |s|
      questionable_user_hash.map! do |elt|
        { user_id: s.scorable_id, rating: s.rating } if elt && elt[:user_id] == s.scorable_id
      end
    end

    # Bulk insert all Next Questionables
    NextQuestionable.bulk_insert(set_size: 500) do |worker|
      # create new next_questionable objects
      next_prod_and_recep[:productions].each do |q|
        worker.add(
          challenge_id: q.challenge_id,
          challenge_average_rating: challenge_average_rating_hash[q.challenge_id][:production],
          test_instance_id: id,
          user_id: user.id,
          reception_id: nil,
          production_id: q.id,
          questionable_user_id: nil,
          questionable_user_rating: nil
        )
      end

      next_prod_and_recep[:receptions].each do |q|
        next if questionable_user_hash[q.production_id].nil? || questionable_user_hash[q.production_id][:rating].nil?

        worker.add(
          challenge_id: q.challenge_id,
          challenge_average_rating: challenge_average_rating_hash[q.challenge_id][:reception],
          test_instance_id: id,
          user_id: user.id,
          reception_id: q.id,
          production_id: nil,
          questionable_user_id: questionable_user_hash[q.production_id][:user_id],
          questionable_user_rating: questionable_user_hash[q.production_id][:rating]
        )
      end
    end

    # Just in case we ran this method after modifying the list of questions, we refresh the cache
    refresh_cached_count_of_questions_to_answer
    refresh_skills_tested
    save!
  end

  def refresh_skills_tested
    self.skills_tested = Challenge.where(id: get_full_challenge_id_array.flatten)
                                  .pluck(:list_skills)
                                  .flatten
                                  .uniq
                                  .sort
  end

  #
  # Ongoing method: Delete a Questionable from all lists of NextQuestionable after it's been used.
  # Speed: fast (done in real time)
  #
  # Class method! Expires a given questionable from all lists
  # NextQuestionableLogic.expire_questionable(Production.find(53))
  #
  # @return: result of the insert transaction
  def self.expire_questionable(q)
    # Ensure current user is not presented with the challenge again
    NextQuestionable.where(challenge_id: q.challenge_id, user_id: q.user_id, test_instance_id: q.id).delete_all

    # Remove used questionable for all users
    case q
    when Production
      NextQuestionable.where(production_id: q.id).delete_all
    when Reception
      NextQuestionable.where(reception_id: q.id).delete_all
    else
      raise ArgumentError
    end
  end

  #
  # Real-time method: Among all possible next_questionables pre-computed, pick the best one!
  # Speed: fast (done in an real-time)
  #
  # @return: a Reception or Production object or nil
  def next_questionable
    # If user has answered the required number of question, next_questionable is nil
    return nil if has_answered_enough_questions?

    # Extract NextQuestionable (must be only one SQL query!)
    nq = next_questionable_profile_module.realtime_selection(self)

    # Instantiate questionable
    if nq && nq.production_id
      Production.find_by_id(nq.production_id)
    elsif nq && nq.reception_id
      Reception.find_by_id(nq.reception_id)

      # Return nil if no NextQuestionable are returned
    end
  end

  # ----------------------------------------
  # :section: Helpers for this module
  # ----------------------------------------
  # Mapping between the profile and the ruby module
  def next_questionable_profile_module
    case next_questionable_profile
    when 'reviewer'       then NextQuestionableLogic::Profiles::ReviewerProfile
    when 'producer'       then NextQuestionableLogic::Profiles::ProducerProfile
    when 'showcase_guest' then NextQuestionableLogic::Profiles::ShowcaseGuestProfile
    when 'audition'       then NextQuestionableLogic::Profiles::AuditionProfile
    when 'fixed_audition' then NextQuestionableLogic::Profiles::FixedAuditionProfile
    else
      NextQuestionableLogic::Profiles::UserProfile
    end
  end

  # ----------------------------------------
  # :section: Helpers defined in each profile
  # ----------------------------------------
  def count_of_productions_to_answer
    next_questionable_profile_module.count_of_productions_to_answer(self)
  end

  def count_of_receptions_to_answer
    next_questionable_profile_module.count_of_receptions_to_answer(self)
  end

  # def count_of_questions_to_answer
  #   next_questionable_profile_module.count_of_questions_to_answer(self)
  # end

  # def count_of_questions_answered
  #   next_questionable_profile_module.count_of_questions_answered(self)
  # end

  # Has the user answered the minimum number of productions and receptions? (i.e. can we compute their score?)
  def has_answered_enough_questions?
    next_questionable_profile_module.has_answered_enough_questions?(self)
  end
end
