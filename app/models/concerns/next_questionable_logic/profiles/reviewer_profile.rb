module NextQuestionableLogic::Profiles
  module ReviewerProfile
    # ----------------------------------------
    # :section: Select algorithms for this profile
    # ----------------------------------------
    def self.fetch_all(user)
      raise ArgumentError if !user.is_a?(User)

      NextQuestionableLogic::AlgoFetchAll::Receptions.run(user)
    end

    def self.post_fetch_filter(user, productions_and_receptions)
      raise ArgumentError if !user.is_a?(User)

      NextQuestionableLogic::AlgoPostFetchFiltering::NoFilter.run(user, productions_and_receptions)
    end

    def self.realtime_selection(user)
      raise ArgumentError if !user.is_a?(User)

      NextQuestionableLogic::RealtimeSelection::Random.run(user)
    end

    # ----------------------------------------
    # :section: Configuration for this profile
    # ----------------------------------------
    def self.default_count_of_questions_to_answer(user)
      raise ArgumentError if !user.is_a?(User)

      0
    end

    def self.count_of_productions_to_answer(user)
      raise ArgumentError if !user.is_a?(User)

      0
    end

    def self.count_of_receptions_to_answer(user)
      raise ArgumentError if !user.is_a?(User)

      999999999
    end

    def self.has_answered_enough_questions?(user)
      raise ArgumentError if !user.is_a?(User)

      false
    end
  end
end
