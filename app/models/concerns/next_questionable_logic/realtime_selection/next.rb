module NextQuestionableLogic::RealtimeSelection
  module Next
    # ----------------------------------------
    # :description: Pick a random NextQuestionable element
    #
    # 1- FILTERING: done by next_questionables#filtered_for_realtime_selection
    # 2- SELECTION: done by next_questionables#random_order
    # ----------------------------------------
    def self.run(test_instance)
      raise ArgumentError if !test_instance.is_a?(TestInstance)

      test_instance.next_questionables.order(id: :asc)
                   .filtered_for_realtime_selection
                   .first
    end
  end
end
