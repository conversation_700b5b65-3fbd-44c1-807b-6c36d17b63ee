module QuestionValidation
  extend ActiveSupport::Concern
  # ----------------------------------------
  # :section: Add associations, class_attribute, validations, etc.
  # ----------------------------------------
  included do
    # This attribute will hold all que
    attr_accessor :question_errors

    # Just in case we always initialize the question_errors attribute
    after_initialize do |question|
      question.question_errors = [] if question.question_errors.nil?
    end
  end

  # ----------------------------------------
  # :section: Validation helpers
  # ----------------------------------------
  def validation_errors(form_data_for_question, questionable = nil)
    self.question_errors = []
    question_validations(form_data_for_question, questionable)
    self.question_errors
  end

  # ----------------------------------------
  # :section: Validation methods
  #
  # Translating messages in /locales/models/question_validation/
  # ----------------------------------------
  def check_if_nil(form_data, message = nil)
    if form_data.nil?
      question_errors << (message || I18n.t('question_validation.cannot_be_nil'))
    end
  end

  def check_if_empty(form_data, message = nil)
    if form_data.nil? || form_data.to_s.strip.empty?
      question_errors << (message || I18n.t('question_validation.cannot_be_empty'))
    end
  end

  def check_if_contains_word_from_list(form_data, word_array, message = nil)
    word_array.each do |word|
      # TODO: check performance. We could use Regexp.union
      # We ignore the word if it is preceded or followed by letters but raise an error if not
      if Regexp.new('(\A|\W)' + word + '(\z|\W)', Regexp::IGNORECASE | Regexp::MULTILINE) =~ Regexp.escape(form_data.to_s)
        question_errors << (message || "#{I18n.t('question_validation.answer_cannot_contain_word')} '#{word}'.")
      end
    end
  end

  def check_if_text_long_enough(form_data, minimum_length, message = nil)
    if form_data.nil? || (!form_data.nil? && form_data.to_s.length < minimum_length)
      question_errors << (message || I18n.t('question_validation.text_too_short'))
    end
  end

  def check_if_contains_word_from_dictionnary(form_data, dictionnary_key, message = nil)
  end

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------
  module ClassMethods
  end # ClassMethods
end
