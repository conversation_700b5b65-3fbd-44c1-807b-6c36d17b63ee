# == Schema Information
#
# Table name: images
#
#  id                :integer          not null, primary key
#  name              :string
#  caption           :text
#  position          :integer          default(0)
#  attachable_id     :integer
#  attachable_type   :string
#  data_file_name    :string
#  data_content_type :string
#  data_file_size    :integer
#  data_updated_at   :datetime
#  created_at        :datetime
#  updated_at        :datetime
#

class Image < ApplicationRecord
  belongs_to :attachable, :polymorphic => true
  has_attached_file :data, {
    :styles => {
      :thumbnail => ["64x64#", :png],
      :medium => ["300x225>", :png],
      :large => ["700x525>", :png]
    },
    :s3_protocol => Rails.env.development? ? :http : :https,
    :path => "/:class/:style/:hash.:extension",
    :hash_secret => "qT60JCzayxmLwF7lwFRJRmDtD15n7LQcOpMr5AMENFxJSr5geOz10FieVk0xXr3V"
  }

  validates_attachment_presence :data, :on => :create
  validates_attachment_content_type :data, :content_type => ["image/jpg", "image/jpeg", "image/png"]

  # Instance method
  def url(*)
    data.url(*)
  end

  def filename
    data_file_name
  end

  def content_type
    data_content_type
  end

  def safe_name
    if name && !name.empty?
      name
    else
      File.basename(data_file_name, '.*').split('.').first.to_s.capitalize
    end
  end

  def browser_safe?
    %w(jpg gif png).include?(url.split('.').last.sub(/\?.+/, "").downcase)
  end
  alias_method :web_safe?, :browser_safe?

  # This method assumes you have images that correspond to the filetypes.
  # For example "image/png" becomes "image-png.png"
  # def icon
  #   "#{data_content_type.gsub(/[\/\.]/,'-')}.png"
  # end

  # Class method

  # Set passed-in order for passed-in ids.
  # def self.order(ids)
  #   if ids
  #     update_all(
  #       ['position = FIND_IN_SET(id, ?)', ids.join(',')],
  #       { :id => ids }
  #     )
  #   end
  # end
end
