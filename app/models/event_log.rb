# == Schema Information
#
# Table name: event_logs
#
#  id            :bigint           not null, primary key
#  category      :integer
#  content       :string
#  criticity     :integer
#  event_date    :datetime
#  loggable_type :string
#  metadata      :jsonb
#  type          :string
#  loggable_id   :integer
#
# Indexes
#
#  index_event_logs_on_loggable_id_and_loggable_type_and_category  (loggable_id,loggable_type,category)
#  index_event_logs_on_type                                        (type)
#

class EventLog < LogRecord
  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  belongs_to :loggable, polymorphic: true

  enum :category, { webcam: 0, validation: 1, question: 2, duplicate: 3, certificate: 4, process: 5, javascript: 6,
                    redo: 7, admin: 8, invoice: 9, identity: 10, ai: 12, order_accepted: 13,
                    order_rejected: 14, order_not_recognized: 15, order_received: 16, lighton_ai_api: 17, overdue_with_tekis_ti: 18,
                    mailjet_sms: 19, browser_details: 20, open_ai_api: 21 }

  enum :criticity, { default: 0, info: 1, warning: 2, danger: 3 }

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :test_instance, ->(id) { where("(loggable_type='Production' AND loggable_id IN ( SELECT id FROM productions WHERE productions.test_instance_id=? )) OR (loggable_type='TestInstance' AND loggable_id=?)", id, id) }
  scope :ats_order_alerts, -> { where(loggable_type: 'Alert', category: %w[order_received order_accepted order_rejected]) }
  scope :from_yesterday, -> { where(event_date: DateTime.yesterday.beginning_of_day..DateTime.yesterday.end_of_day) }

  enum :loggable_type, {
    alert: 'Alert',
    api_order: 'ApiOrder',
    dynamic_question_datum: 'DynamicQuestionDatum',
    evaluation: 'Evaluation',
    examiner: 'Examiner',
    invoice_request: 'InvoiceRequest',
    production: 'Production',
    test_instance: 'TestInstance',
    test_instance_validation: 'TestInstanceValidation',
    user: 'User'
  }

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------
  # ajouter param from_user pour les actions admin
  def self.add(loggable, category, content, criticity, event_date = Time.now)
    EventLog.create({
                      loggable_id: loggable&.id,
                      loggable_type: loggable&.class&.name,
                      category:,
                      content:,
                      event_date:,
                      criticity:
                    })
  rescue StandardError => e
    Alert.system('Unable to create EventLog', e.message, meta: {
                   loggable_id: loggable&.id,
                   loggable_type: loggable&.class&.name,
                   category:,
                   content:,
                   event_date:,
                   criticity:
                 })
  end

  def self.backtrace(loggable, ignore_last: 1, must_contain: '', message: '')
    locations = caller_locations(ignore_last + 1).filter { |location| location.to_s.include? must_contain }
                                                 .map { |location| "#{message}:" + location.to_s.remove("#{Rails.root}#{must_contain}") }
    self.add(loggable, :process, locations.join(', '), :info)
  end

  def self.status_change(loggable)
    EventLog.add(loggable, :process, "Status changed to #{loggable.aasm.to_state} (event: #{loggable.aasm.current_event}). Callbacks will now trigger", :info)
  end

  def self.categories_stats_for(loggable)
    event_logs = get_event_logs_for(loggable)
    return unless event_logs

    all_categories = event_logs.group(:category).count
    nb_row = event_logs.count.to_f
    uniques_categories = all_categories.keys

    inv_cat_hash = EventLog.invers_category_hash
    stats = {}
    uniques_categories.each do |c|
      stats[inv_cat_hash[c]] = (all_categories[c].to_f / nb_row) * 100
    end

    stats
  end

  def self.invers_category_hash
    res_hash = {}
    EventLog.categories.each { |k, v| res_hash[v] = k }
    res_hash
  end

  def self.get_event_logs_for(loggable)
    return unless loggable

    loggable_type = loggable.class.name
    EventLog.where(loggable_type: loggable_type, loggable_id: loggable.id)
  end
end
