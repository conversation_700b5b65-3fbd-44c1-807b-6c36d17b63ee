# == Schema Information
#
# Table name: question_elements
#
#  id                  :integer          not null, primary key
#  content             :text             default({})
#  deleted_at          :datetime
#  order               :integer
#  scorable            :boolean
#  static_valid_answer :string
#  status              :integer          default("published")
#  type                :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  production_score_id :integer
#  question_id         :integer
#  reception_score_id  :integer
#
# Indexes
#
#  index_question_elements_on_question_id  (question_id)
#

class QuestionElements::QEHtml < QuestionElement
  # ----------------------------------------
  # :section: Hard coded class attributes
  # ----------------------------------------
  # This must lists the required elements in the content hash. No less no more.
  # It is validated automatically and will trigger an error if any element is
  # missing or not listed below.
  REQUIRED_CONTENT = %w[code]

  # ----------------------------------------
  # :section: Methods overridden from question_element.rb
  # These are the methods that you will want to override to implement all necessary
  # functionality of your QuestionElement.
  # ----------------------------------------

  # Some methods that you might want to override at your own discretion:
  #   - template_name
  #   - template_filename
  #   - valid_answer
  # (see app/models/question_element.rb for original implementation)

  # ----------------------------------------
  # :section: Custom accessors for *content* and *dqd* objects.
  # Protecting some internal variables from being accessed in views directly
  #
  # Accessors of *content* and dqd should not be could from views as it would highly
  # decrease the the maintainability and readability of the code. In addition, data
  # stored in dynamic_question_data are all dynamic, and significant validation and
  # error management code should be written in custom accessor to make them more
  # robust for usage in views.
  #
  # Views should only use these custom accessors which you should write below.
  # ----------------------------------------

  def code
    content['code']
  end
end
