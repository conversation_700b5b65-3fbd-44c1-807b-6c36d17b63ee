# frozen_string_literal: true

# == Schema Information
#
# Table name: evaluations
#
#  id                           :integer          not null, primary key
#  ai_spoken_attempts           :integer          default(0)
#  ai_spoken_completed          :boolean          default(FALSE)
#  ai_spoken_response           :text
#  ai_spoken_score              :integer
#  ai_written_attempts          :integer          default(0)
#  ai_written_completed         :boolean          default(FALSE)
#  ai_written_response          :text
#  ai_written_score             :integer
#  anonymized_at                :datetime
#  assessed_at                  :datetime
#  assessment_link              :string
#  assigned_at                  :datetime
#  canceled_at                  :datetime
#  certification_type           :string
#  check_details                :string
#  checks_passed                :boolean          default(FALSE)
#  completed_at                 :datetime
#  deleted_at                   :datetime
#  delivered_at                 :datetime
#  evaluation_goal              :string
#  is_reviewed                  :boolean          default(FALSE)
#  json_assessment              :json
#  last_reminder_at             :datetime
#  overdue_at                   :datetime
#  payment_status               :string
#  payment_type                 :string
#  pdf_certificate_content_type :string
#  pdf_certificate_file_name    :string
#  pdf_certificate_file_size    :bigint
#  pdf_certificate_updated_at   :datetime
#  presented_for_review         :boolean          default(FALSE)
#  presented_for_review_at      :datetime
#  reminder_nb                  :integer          default(0)
#  soon_overdue_at              :datetime
#  start_assessment_at          :datetime
#  status                       :string
#  urgent                       :boolean          default(FALSE)
#  validated_at                 :datetime
#  created_at                   :datetime
#  updated_at                   :datetime
#  assessment_type_id           :integer
#  evaluation_delay_id          :integer
#  examiner_id                  :integer
#  invoice_request_id           :integer
#  reviewed_evaluation_id       :bigint
#  test_instance_id             :integer
#
# Indexes
#
#  index_evaluations_on_assessment_type_id      (assessment_type_id)
#  index_evaluations_on_deleted_at              (deleted_at)
#  index_evaluations_on_examiner_id             (examiner_id)
#  index_evaluations_on_reviewed_evaluation_id  (reviewed_evaluation_id)
#  index_evaluations_on_status                  (status)
#  index_evaluations_on_test_instance_id        (test_instance_id)
#

class Evaluation < ApplicationRecord
  self.ignored_columns += [
    :ai_job_attempts
  ]

  include AASM
  include Evaluations::EvaluationValidation
  include Taggable
  include ApplicationHelper
  include Evaluations::AutomationConditions
  include Anonymizable

  # Soft delete object
  acts_as_paranoid

  EVALUATION_GOAL = { standard: 'Standard',
                      peer_review: 'Peer_review',
                      multiple_evaluations: 'Multiple_evaluations',
                      trial: 'Trial',
                      client_grade_check: 'Client_grade_check',
                      pre_trial: 'Pre_trial',
                      examiner_average: 'Examiner_average',
                      holding_peer_review: 'Holding_peer_review',
                      cut_check: 'Cut_check', # Deprecated
                      double_check: 'Double_check' }.freeze # Deprecated

  # define Boolean methods for evaluation_goals
  # standard? peer_review? multiple_evaluations? cut_check? double_check? trial? client_grade_check? pre_trial? examiner_average? holding_peer_review?
  # define Scopes for evaluation_goals : multiple_evaluations already pluralized
  # standards peer_reviews multiple_evaluations cut_checks double_checks trials client_grade_checks pre_trials examiner_averages holding_peer_reviews
  EVALUATION_GOAL.each do |key, value|
    define_method("#{key}?") { evaluation_goal == EVALUATION_GOAL[key] }
    scope key.to_s.pluralize.to_sym, -> { where(evaluation_goal: value) }
  end

  def self.evaluation_goals
    EVALUATION_GOAL.keys
  end

  PAYEMENT_TYPE            = { full: 'Full', partial: 'Partial', not_paid: 'Not_paid' }.freeze
  SELECT_QUERY_EDIT        = 'test_instances.test_language, evaluation_delays.delay_label, evaluation_delays.payement_rate, evaluation_delays.graded_time_limit, evaluation_delays.soon_overdue_time_limit, evaluations.*'
  CAN_BE_PAID_STATUSES     = %i[canceled assessed delivered].freeze
  ACTIVE_STATUSES          = %i[assigned soon_overdue overdue assessed delivered].freeze
  CANCELLABLE_STATUSES     = %i[created assigned soon_overdue overdue assessed canceled].freeze
  NON_CANCELLABLE_STATUSES = %i[delivered].freeze
  ONGOING_STATUSES         = %i[assigned soon_overdue overdue].freeze

  ASSESSMENT_ROBUST_RULES = {
    no_robustness_issues: { condition: {}, tag: '' },
    partially_answered: { condition: { method: :verify_present?, variable: :overall }, tag: 'incomplete-answers' },
    no_oral_responses: { condition: { method: :verify_score_is_zero?, variable: :spoken }, tag: 'blank-oral' },
    no_written_responses: { condition: { method: :verify_score_is_zero?, variable: :written }, tag: 'blank-written' },
    audio_difficult_assess: { condition: { method: :verify_present?, variable: :spoken }, tag: 'audio-issues' },
    audio_impossible_assess: { condition: {}, tag: 'inaudible' },
    short_responses: { condition: { method: :verify_present?, variable: :overall }, tag: 'short-answers' },
    all_questions_wrong_language: { condition: {}, tag: 'wrong-language' },
    some_questions_wrong_language: { condition: {}, tag: 'wrong-language-partial' },
    not_spontaneous_responses: { condition: { method: :verify_present?, variable: :overall }, tag: 'not-spontaneous' },
    other: { condition: {}, tag: 'robust-other-issues' }
  }.freeze

  ASSESSMENT_CHEATING_RULES = {
    no_cheating_suspicion: { condition: {}, tag: '' },
    two_voices_recording: { condition: {}, tag: 'cheating-multi-voices' },
    suspicious_difference: { condition: {}, tag: 'spoken-written-difference' },
    not_spontaneous: { condition: {}, tag: '' },
    copied_some_responses: { condition: {}, tag: 'cheating-plagiarism' },
    other: { condition: {}, tag: 'cheating-other-issues' }
  }.freeze

  before_update :set_presented_for_review_at, if: -> { presented_for_review_changed? }

  after_commit do |evaluation|
    evaluation.add_tag_by_name('multiple-evaluations') if previous_changes[:evaluation_goal].present? && evaluation.evaluation_goal == EVALUATION_GOAL[:multiple_evaluations]
  end

  after_create do |evaluation|
    EventLog.backtrace(evaluation, ignore_last: 11, must_contain: '/app/models/', message: 'Created from')
  end

  has_attached_file :pdf_certificate, {
    s3_protocol: :https,
    path: '/:class/:style/:hashed_name.:extension',
    hash_secret: 'qT60JCzayxmLwF7lwFRJRmDtD15n7LQcOpMr5AMENFxJSr5geOz10FieVk0xXr3V'
  }

  validates_attachment_content_type :pdf_certificate, content_type: ['application/pdf']

  def self.report_hashed_name_secret
    'qT60JCzayxmLwF7lwFRJRmDtD15n7LQcOpMr5AMENFxJSr5geOz10FieVk0xXr3V'
  end

  # Specific method to create a hashed filename and that can also be called from the controller when uploading to S3
  def hashed_name
    if Rails.env.production?
      Digest::MD5.hexdigest("--#{self.class.name}--#{id}-- #{self.class.report_hashed_name_secret} audition--")
    else
      Digest::MD5.hexdigest("--#{self.class.name}--#{id}-- #{self.class.report_hashed_name_secret} #{Rails.env}--")
    end
  end

  validates   :test_instance_id, presence: true
  validates   :examiner_id, presence: true
  validates   :evaluation_delay_id, presence: true
  belongs_to  :test_instance
  belongs_to  :examiner
  belongs_to  :evaluation_delay
  belongs_to  :assessment_type
  belongs_to  :invoice_request
  has_one     :certificate
  has_many    :event_logs, as: :loggable

  belongs_to :reviewed_evaluation, class_name: 'Evaluation'

  aasm column: :status do
    state :created, initial: true
    state :assigned
    state :soon_overdue
    state :overdue
    state :assessed
    state :delivered
    state :canceled

    after_all_events :update_evaluation_state_time

    # The evaluation is created and assigned to a examiner

    event :assign, before_transaction: %i[assign_to_examiner manage_evaluations_before_assign check_urgent_mode], after_commit: %i[send_evaluation_to_examiner manage_evaluations_after_assign trial_examiners_check trigger_ai_assessment prefill_json_assessment decrease_number_of_eval_since_last_peer_review] do
      transitions from: :created, to: :assigned, guard: proc { examiner.present? }
    end

    # The evaluation is created and forced assigned to a examiner

    event :force_assign, before_transaction: %i[manage_evaluations_before_assign check_urgent_mode], after_commit: %i[send_evaluation_to_examiner manage_evaluations_after_assign trigger_ai_assessment decrease_number_of_eval_since_last_peer_review] do
      transitions from: :created, to: :assigned
    end

    # The evaluation is created and assigned by an admin
    event :assign_to_admin do
      transitions from: :created, to: :assigned
    end

    # Certificate has been delivered to client
    event :deliver, after_commit: %i[after_deliver_callback check_for_multiple_delivered_evaluations] do
      transitions from: %i[assessed delivered], to: :delivered
    end

    # Evaluation is soon overdue
    event :soon_overdue, after_commit: :send_reminder_to_examiner do
      transitions from: [:assigned], to: :soon_overdue
    end

    # Evaluation is overdue
    event :overdue, after_commit: :send_overdue_to_examiner do
      transitions from: [:soon_overdue], to: :overdue
    end

    # Evaluation is canceled
    event :cancel, after_commit: :cancel_evaluation_callback do
      transitions from: CANCELLABLE_STATUSES, to: :canceled
    end

    # Evaluation has been assessed by examiner
    event :assess, after_commit: [:manage_evaluations_after_assess, :after_assess_callback] do
      transitions from: %i[assessed assigned overdue soon_overdue], to: :assessed
    end

    event :assess_no_callback do
      transitions from: %i[assessed assigned overdue soon_overdue], to: :assessed
    end
  end

  aasm :payment_status, column: :payment_status do
    state :payment_waiting_assessment, initial: true
    state :payment_to_be_paid
    state :payment_not_to_be_paid

    event :reset_waiting_assessment do
      transitions from: %i[payment_not_to_be_paid payment_waiting_assessment payment_to_be_paid], to: :payment_waiting_assessment
    end

    event :to_pay do
      transitions from: %i[payment_not_to_be_paid payment_waiting_assessment payment_to_be_paid], to: :payment_to_be_paid
    end

    event :to_not_pay do
      transitions from: %i[payment_to_be_paid payment_not_to_be_paid], to: :payment_not_to_be_paid
    end

    after_all_transitions :log_status_change
  end

  # ----------------------------------------
  # :section: Enums
  # ----------------------------------------
  enum :certification_type, { simple: 'simple', detailed: 'detailed', flex: 'flex', written: 'written', spoken: 'spoken' }
  # scope :simple, :detailed, :flex, :written, :spoken

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :ongoing,           -> { where(status: ONGOING_STATUSES) }
  scope :active,            -> { where(status: ACTIVE_STATUSES) }
  scope :can_be_canceled,   -> { where(status: CANCELLABLE_STATUSES) }
  scope :completed,         -> { where(status: %i[assessed delivered]) }
  scope :not_canceled,      -> { where.not(status: :canceled) }
  scope :can_be_reset,      -> { ongoing }
  scope :can_be_in_backlog, -> { active }
  scope :in_backlog_last,   ->(date) { can_be_in_backlog.where('assigned_at >= ? OR status IN (?)', date, ONGOING_STATUSES) }

  scope :assessed_before,                         ->(date) { where(assessed_at: ..date) }
  scope :assessed_between,                        ->(start_date, end_date) { where(evaluations: { assessed_at: start_date..end_date }) }
  scope :no_invoice_request,                      -> { where(invoice_request_id: nil) }
  scope :with_real_examiner,                      -> { joins(:examiner).where('examiners.real' => true) }
  scope :with_ai_examiner,                        -> { joins(:examiner).where('examiners.is_ai' => true) }
  scope :without_ai_examiner,                     -> { joins(:examiner).where(examiners: { is_ai: [nil, false] }) }
  scope :available_for_payment_before,            ->(date) { where(status: CAN_BE_PAID_STATUSES).payment_to_be_paid.with_real_examiner.no_invoice_request.assessed_before(date) }
  scope :with_trusted_examiner,                   -> { joins(:examiner).where(examiners: { status: 2 }) }
  scope :with_certificate,                        -> { joins(:certificate) }
  scope :assigned_between,                        ->(start_date, end_date) { where('evaluations.assigned_at > ? AND evaluations.assigned_at < ?', start_date, end_date) }
  scope :assigned_after,                          ->(date) { assigned_between(date, Time.now) }
  scope :total_assessment_time,                   -> { joins(:assessment_type).sum(:assessment_time) }
  scope :assessed_after,                          ->(date) { where(assessed_at: date..) }
  scope :completed_pre_trial,                     -> { pre_trials.completed }
  scope :peer_reviews_reviewed,                   -> { peer_reviews.where.not(reviewed_evaluation_id: nil) }
  scope :with_test_instances_sent_for_evaluation, -> { joins(:test_instance).where(test_instances: { status: :sent_for_evaluation, test_mode: false }) }
  scope :with_graded_test_instances,              -> { joins(:test_instance).where(test_instances: { status: :graded, test_mode: false }) }
  scope :with_regraded_test_instances, lambda {
    joins(:test_instance).where(test_instances: { test_mode: false })
                         .where.not(test_instances: { regrade_requested_at: nil })
  }
  scope :with_no_real_examiner,                   -> { joins(:examiner).where(examiners: { real: false }) }
  scope :completed_and_assessed_between, lambda { |start_date, end_date|
    completed.assessed_between(start_date, end_date)
             .joins(certificate: :grades, test_instance: [:average_grades])
             .order(assessed_at: :desc)
  }

  scope :without_matching_evaluations_on_test_instance, lambda { |condition|
    where(
      <<~SQL.squish
        evaluations.test_instance_id NOT IN (#{Evaluation.select(:test_instance_id).where(condition).to_sql})
      SQL
    )
  }

  scope :with_matching_evaluations_on_test_instance, lambda { |condition|
    where(
      <<~SQL.squish
        evaluations.test_instance_id IN (#{Evaluation.select(:test_instance_id).where(condition).to_sql})
      SQL
    )
  }

  scope :without_ongoing_technical_issues_on_test_instance, lambda {
    where(
      <<~SQL.squish
        evaluations.test_instance_id NOT IN (#{TestInstance.select(:id).with_ongoing_technical_issues.to_sql})
      SQL
    )
  }

  scope :stuck_standard, lambda {
    standards
      .assessed
      .without_matching_evaluations_on_test_instance(evaluation_goal: Evaluation::EVALUATION_GOAL[:examiner_average])
      .without_matching_evaluations_on_test_instance(evaluation_goal: Evaluation::EVALUATION_GOAL[:holding_peer_review], status: :assigned)
      .with_test_instances_sent_for_evaluation
      .without_ongoing_technical_issues_on_test_instance
      .includes(
        :tags,
        :evaluation_delay,
        :examiner,
        test_instance: [:user, :tags],
        certificate: [:overall_grades]
      )
  }

  scope :stuck_multiple_evaluations, lambda {
    multiple_evaluations
      .assessed
      .with_matching_evaluations_on_test_instance(evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review], status: :assessed)
      .without_matching_evaluations_on_test_instance(evaluation_goal: Evaluation::EVALUATION_GOAL[:examiner_average], status: Evaluation.aasm.states.map(&:name) - [:canceled])
      .with_test_instances_sent_for_evaluation
      .without_ongoing_technical_issues_on_test_instance
      .includes(
        :evaluation_delay,
        :tags,
        :examiner,
        test_instance: [:user, :tags],
        certificate: [:overall_grades]
      )
  }

  scope :stuck_regraded, lambda {
    peer_reviews
      .assessed
      .without_ai_examiner
      .with_test_instances_sent_for_evaluation
      .with_regraded_test_instances
      .without_ongoing_technical_issues_on_test_instance
      .includes(
        :evaluation_delay,
        :tags,
        :examiner,
        test_instance: [:user, :tags],
        certificate: [:overall_grades]
      )
  }

  scope :stuck_regraded_with_sent_for_evaluation, lambda {
    delivered
      .without_ai_examiner
      .without_matching_evaluations_on_test_instance(status: %i[created assigned soon_overdue])
      .with_test_instances_sent_for_evaluation
      .with_regraded_test_instances
      .without_ongoing_technical_issues_on_test_instance
      .includes(
        :evaluation_delay,
        :tags,
        :examiner,
        test_instance: [:user, :tags],
        certificate: [:overall_grades]
      )
  }

  scope :stuck_examiner_average, lambda {
    examiner_averages
      .without_matching_evaluations_on_test_instance(status: :delivered)
      .without_ongoing_technical_issues_on_test_instance
      .with_test_instances_sent_for_evaluation
      .assessed
      .includes(
        :evaluation_delay,
        :tags,
        :examiner,
        test_instance: [:user, :tags],
        certificate: [:overall_grades]
      )
  }

  def ai_completed?
    ai_spoken_completed? && ai_written_completed?
  end

  def self.stuck
    ShardsHelper.reading_replica_on_production do
      stuck_standard + stuck_multiple_evaluations + stuck_examiner_average + stuck_regraded + stuck_regraded_with_sent_for_evaluation
    end
  end

  def self.tag_and_return_stuck_evaluations
    evaluations = stuck
    evaluations.each { |e| e.add_tag_by_name('s-eval') }
    evaluations
  end

  def is_late_at
    evaluation_delay.graded_time_limit.business_hours.after(assigned_at)
  rescue StandardError
    return if assigned_at.blank?

    24.business_hours.after(assigned_at)
  end

  def is_soon_late_at
    diff_hours = evaluation_delay.graded_time_limit - evaluation_delay.soon_overdue_time_limit
    is_late_at - diff_hours.hours
  end

  def was_late?
    assessed_at > evaluation_delay.graded_time_limit.business_hours.after(assigned_at)
  end

  def hours_from_assigned_to_assessed
    assigned_at.business_time_until(assessed_at) / 3600
  end

  def is_soon_late?
    Time.now > is_soon_late_at
  end

  def is_late?
    Time.now > is_late_at
  end

  def set_presented_for_review_at
    self.presented_for_review_at = Time.now if presented_for_review
  end

  def update_evaluation_assigned_at_state_time
    send('assigned_at=', Time.now)
    save!
  end

  def self.score_difference(current_eval, old_eval)
    new_score = current_eval.certificate.grades.where({ label: [:overall] }).last.score.to_f
    old_score = old_eval.certificate.grades.where({ label: [:overall] }).last.score.to_f
    value = (new_score - old_score).abs
    percentage = (value / new_score) * 100

    is_lower = new_score < old_score

    { is_lower: is_lower, percentage: percentage, value: value }
  end

  def pre_trial_alerting
    if pre_trial? && !examiner.ongoing_pre_trial_evaluations?
      pre_trial_completed_eval = examiner.evaluations.completed_pre_trial
      Alert.linguistics("[Examiner #{examiner.name}][Pre trial process finished]", "Pre trial evals: #{pre_trial_completed_eval.map { |e| "\n#{e.get_admin_edit_link}" }}")
    end
  end

  def trial_alerting
    if examiner.on_trial_period?
      nb_eval_completed = examiner.evaluations.completed.count
      Alert.linguistics("[Examiner #{examiner.name}][Trial process #{nb_eval_completed} eval completed]", Rails.application.routes.url_helpers.compare_evaluations_admin_examiners_url(id: examiner_id)) if (nb_eval_completed % 10).zero?
    end
  end

  def handle_regraded
    diff_index = (CECRL_LEVELS_VALUES[:cecrl].index(test_instance.get_delivered_eval.overall_cecrl_score) - CECRL_LEVELS_VALUES[:cecrl].index(overall_cecrl_score)).abs
    if diff_index < 2
      regrade_confirmation
    else
      self.check_details ||= []
      self.check_details = self.check_details_to_array.push "Regrade discrepancy, diff index #{diff_index}"
      save!
      add_tag_by_name('grade-appeal')
    end
  end

  def regrading?
    test_instance.regrading? &&
      test_instance.evaluations.peer_reviews.not_canceled.last == self
  end

  def regrade_confirmation
    return unless test_instance.may_grade?

    sync_response = PippletClientsApi.update_user(test_instance_uuid: test_instance.uuid, attributes: { regrade_status: :confirmed })
    if sync_response.success?
      test_instance.grade!
      regrade_confirmed(:client)
    else
      Alert.api("[PippletClientsApi::regrade_confirmation] Error while sending regrade confirmation for #{test_instance.uuid}", sync_response.body)
    end
  end

  def get_gform_review_link
    params = {
      'entry.1296324853': test_instance_id,
      'entry.59236534': examiner.email,
      'entry.1779959223': examiner.name
    }
    "https://docs.google.com/forms/d/e/1FAIpQLScmqwJQqqMeDaeCsAX-gNUscF6SNBUkl7h6K_fNTTUc2L_wHw/viewform?#{params.to_query}"
  end

  def set_default_examiner!
    if standard?
      # Find or create user_group
      user_group = UserGroup.get_user_group(test_instance.user)
      # Find or assigne examiner for user_group self.test_language
      examiner = user_group.get_examiner(test_language)
      update!(examiner: examiner)
    end
  end

  def change_if_unavailable
    unless examiner.available?
      new_examiner = examiners_backup_order.first
      update!(examiner: new_examiner) if new_examiner.present?
    end
  end

  def can_be_assign_to_standard_examiner?
    examiners = [examiner] + examiners_backup_order
    forbidden_examiners_ids = test_instance.get_already_used_examiner_ids
    examiners.each do |examiner|
      return true if examiner.can_assign_evaluation?(self)[:result] && !forbidden_examiners_ids.include?(examiner.id)
    end
    false
  end

  def test_language
    test_instance&.test_language
  end

  def examiners_backup_order
    language = test_language
    # prevent assigning to examiner that already has evaluation
    forbidden_examiners_ids = test_instance.evaluations.not_canceled.pluck(:examiner_id)

    Examiner.usable_for(language).with_rank.where.not(id: forbidden_examiners_ids).sort_by do |examiner|
      (examiner.rank - self.examiner.rank).abs
    end
  end

  def get_next_available_examiner
    examiners_backup_order.each do |examiner|
      EventLog.add(test_instance, :process, "Attempt to assign evaluation##{id} to examiner #{examiner.name}##{examiner.id}", :info)
      can_assign = examiner.can_assign_evaluation?(self)
      if can_assign[:result]
        return examiner
      else
        EventLog.add(test_instance, :process, "Fail to assign to examiner #{examiner.name}##{examiner.id} : #{can_assign[:errors]}", :warning)
      end
    end
    nil
  end

  def choose_examiner
    self.examiner = get_next_available_examiner ||
                    Examiner.list_examiner_by_allocated_capacity_rate_asc(test_instance:).first ||
                    Examiner.find_admin_linguistic_examiner(test_language)
    save!
  end

  def get_admin_edit_link
    Rails.application.routes.url_helpers.admin_evaluation_url(self)
  end

  def remaining_time
    late_time = is_late_at

    return unless late_time

    [(is_late_at - Time.now).to_i / 3600, 0].max
  end

  def late_ratio
    (Time.now - assigned_at).to_f / (is_late_at - assigned_at)
  end

  def getRatio
    assigned_at ? late_ratio : 0
  end

  def score
    certificate.grades&.overall&.last&.score if certificate.present?
  end

  def generate_and_save_certificate
    ## Check if all necessary data is here, otherwise send an alert
    if certificate.nil?
      message = "Current Evaluation being delivered was : #{id}"
      Alert.system('Evaluation is assessed but do not have a certificate', message)
      return
    end

    certificate_parameters = get_certificate_parameters
    if certificate_parameters['locale'].present?
      I18n.with_locale(certificate_parameters['locale']) do
        pdf_html = ApplicationController.new.render_to_string(
          template: certificate_parameters['template'],
          locals: { evaluation: self, test_instance: test_instance, user: test_instance.user, cecrl_score: overall_cecrl_score, full_score: overall_score, brand: Brand.find_by_id(certificate_parameters['brand_id']) }
        )

        self.pdf_certificate = StringIO.new(WickedPdf.new.pdf_from_string(
                                              pdf_html,
                                              margin: {
                                                top: 0, # default 10 (mm)
                                                bottom: 0,
                                                left: 0,
                                                right: 0
                                              },
                                              encoding: 'UTF-8',
                                              orientation: certificate_parameters['orientation'],
                                              dpi: '300'
                                            ))
        save!
      end
    end
  end

  ## Returns {
  ##  "brand_id" =>
  ##  "orientation" =>
  ##  "custom" =>
  ##  "template" =>
  ##  "locale"
  ## }
  def get_certificate_parameters
    certificate_parameters = {}

    brand = Brand.find_for_ti(test_instance, :certificate_branded)
    certificate_parameters['locale'] = certificate.certif_language
    if brand.nil?
      certificate_parameters['orientation'] = 'portrait'
      certificate_parameters['custom'] = assessment_type.get_certificate_name
    else
      settings = brand.get_certificate_settings
      certificate_parameters['orientation'] = settings['orientation']
      certificate_parameters['custom'] = settings['custom']
      certificate_parameters['brand_id'] = brand.id
    end

    certificate_parameters['template'] = if certificate.grades.empty?
                                           "certificate/template_certificate_#{certificate_parameters['custom']}_empty"
                                         elsif brand.present? && brand.name == 'flex_PRO'
                                           "certificate/template_certificate_flex_#{certificate_parameters['custom']}"
                                         elsif test_instance.talent_ai?
                                           "certificate/template_certificate_talent_ai_#{certificate_parameters['custom']}"
                                         else
                                           "certificate/template_certificate_#{certificate_parameters['custom']}"
                                         end

    certificate_parameters
  end

  def send_overdue_reminders
    url = get_admin_edit_link
    Alert.system("[Evaluation Overdue][evaluation: #{id}]", "Check Evaluation here: #{url}")
  end

  def self.assign_default_examiner!(ti)
    ti.examiner || Examiner.first_for(ti.test_language_sym)
  end

  def self.new_for_test_instance(test_instance)
    evaluation = nil
    if test_instance
      evaluation = Evaluation.new(
        test_instance_id: test_instance.id,
        assessment_type: AssessmentType.find_for_test_instance(test_instance),
        certification_type: test_instance.test_profile.certification_type,
        examiner: assign_default_examiner!(test_instance),
        payment_type: Evaluation::PAYEMENT_TYPE[:full],
        evaluation_goal: Evaluation::EVALUATION_GOAL[:standard],
        evaluation_delay: EvaluationDelay.get_default
      )
    end
    evaluation
  end

  def set_for_peer_review!
    update(evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review])
  end

  def status_action_name
    action = case status.to_sym
             when :assigned, :soon_overdue, :overdue
               'Assess this candidate'
             when :assessed, :delivered
               'Done'
             else
               'Unknown'
             end

    action = 'Reassess this candidate' if is_reviewed? && ONGOING_STATUSES.include?(status.to_sym)
    action
  end

  def is_assessment_type(key)
    key = key.downcase.to_sym
    type = assessment_type.name.downcase.to_sym
    key == type
  end

  def find_index(i)
    i += 1
    questions_size = test_instance.ordered_completed_productions.size
    if questions_size == 8 && i > 3
      case i
      when 4
        "#{i - 1}.A"
      when 5
        "#{i - 2}.B"
      else
        (i - 2).to_s
      end
    elsif [8, 7].include?(questions_size) && i > 1
      case i
      when 2
        "#{i}.A"
      when 3
        "#{i - 1}.B"
      else
        (i - 1).to_s
      end
    else
      i.to_s
    end
  end

  def assessing_time
    if assessed_at && start_assessment_at
      (assessed_at - start_assessment_at).to_i
    else
      0
    end
  end

  def assigned_to_deliver_time
    if assessed_at && assigned_at
      (assessed_at - assigned_at).to_i
    else
      0
    end
  end

  def generate_certificate
    certificate&.destroy
    calculated_scores = assessment_type.calculate_scores(json_assessment)
    certif = Certificate.new
    calculated_scores.each do |criteria, scores|
      grade = Grade.new({ label: criteria, score: scores[:score].round, cecrl_score: scores[:cecrl_score] })
      certif.grades << grade
    end
    certif.save!
    self.certificate = certif
    save!
  end

  def overall_grade
    if certificate && !certificate.grades.empty?
      overall_grade_element = certificate.grades.where(label: 'overall').last
      if !overall_grade_element.nil? && !overall_grade_element.cecrl_score.nil?
        overall_grade_element.cecrl_score.to_s
      else
        'NA'
      end
    else
      'NA'
    end
  end

  def overall_score
    self.certificate&.grades&.overall&.last&.score || 0
  end

  def reset!
    # Send email telling examiner that the evaluation was removed from his evaluation list
    ExaminerMailer.evaluation_canceled(self).deliver_later if %i[assigned soon_overdue overdue].include?(status.to_sym)

    # Eval allready assessed set is_reviewed to true
    self.is_reviewed = true if assessed?

    self.status = :created
    self.assigned_at = nil
    self.delivered_at = nil
    self.canceled_at = nil
    self.overdue_at = nil
    self.invoice_request_id = nil
    self.soon_overdue_at = nil
    self.assessed_at = nil
    save
  end

  def is_no_answer_provided?
    assessment_type.get_all_scorable_keys.each do |k|
      return false if get_json_assessment_value(k) && get_json_assessment_value(k) != assessment_type.default_no_answer_value
    end
    true
  end

  def deliver_no_answers_evaluation(pass_checks: true)
    unless %w[completed sent_for_evaluation graded].include?(test_instance.status)
      test_instance.complete!
      test_instance.pass_validations!
    end

    test_instance.cancel_evaluations
    eval = create_and_assess_no_answers_evaluation
    GenerateEvaluationCertificateJob.perform_later(eval.id, false, pass_checks, true)
  end

  # TODO: add to EvaluationsManagerService
  def create_and_assess_no_answers_evaluation
    json = json_assessment
    assessment_type.get_all_scorable_keys.each do |scorable_key|
      json[scorable_key] = assessment_type.default_no_answer_value
    end

    assessment_type.default_beginners_values.each do |default_beginners_values, value|
      json[default_beginners_values] = value
    end

    # TODO : use the updated way to add Evaluations to TIs
    #       test_instance.add_and_force_assign_evaluation(
    #         certification_type:,
    #         assessment_type_id:,
    #         evaluation_goal:,
    #         examiner: Examiner.pipplet_admin_examiner(test_instance.test_language),
    #         json_assessment: json,
    #         tags:
    #       )
    test_instance.create_evaluation
    eval = test_instance.evaluations.last
    eval.status = 'assigned'
    eval.assigned_at = Time.now
    eval.certification_type = certification_type
    eval.assessment_type_id = assessment_type_id
    eval.evaluation_goal = evaluation_goal
    eval.examiner = Examiner.pipplet_admin_examiner(test_instance.test_language)
    eval.json_assessment = json
    eval.tags = tags
    eval.save!
    EventLog.add(self, :admin, 'deliver_no_answers_evaluation# Assign admin examiner', :info)
    eval.generate_certificate
    eval.assess_no_callback!('assessed_at')
    eval
  end

  def check_evaluation_for_certificate_issue(skip_send = false, admin_eval = false)
    errors = validate(skip_send, admin_eval)

    if errors.empty?
      self.checks_passed = true
      self.check_details = nil
    else
      self.test_instance.update!(had_check_failed: true)
      self.checks_passed = false
      self.check_details = errors
    end
    save!

    { status: checks_passed, errors: check_details }
  end

  def check_details_to_array
    begin
      ret = Array.class_eval(check_details)
    rescue StandardError
      ret = check_details
    end
    ret
  end

  # TODO: clean this method
  def manage_certificate_issue(skip_send = false, _send_to_google_doc = false, pass_checks = true, admin_eval = false)
    EventLog.add(self, :certificate, 'ADMIN#manage_certificate_issue_start#', :default)
    checks = pass_checks ? check_evaluation_for_certificate_issue(skip_send, admin_eval) : { status: true, errors: [] }

    if checks[:status] == true
      begin
        deliver_certificate(:client)
      rescue StandardError => e
        message = "Evaluation : #{id}"
        Alert.system("Certificate could not be sent : #{e}", message, :certificate_delivery)
      else
        deliver!('delivered_at')
        EventLog.add(self, :process, "Evaluation #{id} delivered", :info)
      end
    end
    EventLog.add(self, :certificate, 'ADMIN#manage_certificate_issue_end#', :default)
  end

  def check_if_partially_evaluated?
    return false if %w[written spoken].include?(certification_type)

    written_score = certificate.grades.written&.first&.score.to_i.zero?
    spoken_score = certificate.grades.spoken&.first&.score.to_i.zero?
    (written_score && !spoken_score) || (!written_score && spoken_score)
  end

  def deliver_certificate(to)
    EventLog.add(self, :certificate, 'ADMIN#deliver_certificate#', :default)

    if test_instance&.client_config&.deliver_certificate_to_client == false
      EventLog.add(self, :certificate, 'ADMIN#deliver_certificate# Aborted for no deliver_certificate_to_client', :default)
      return false
    end
    recipients, cc = recipients_and_cc(to:, test_instance:, client_config: test_instance&.client_config)
    if recipients.nil?
      if test_instance&.from_internal_api?
        Alert.system('[Evaluation][Sending Certificate]', "The certificate was not sent to anyone because no email found for Evaluation: #{get_admin_edit_link}\nTi: #{Rails.application.routes.url_helpers.admin_test_instance_url(test_instance)}", :certificate_delivery)
      end
      return nil
    end
    MailjetHelper.send_certificate(self, recipients, cc)
  end

  def regrade_confirmed(to)
    EventLog.add(self, :certificate, 'ADMIN#regrade_confirmation#', :default)
    test_instance = self.test_instance

    if test_instance&.client_config&.deliver_certificate_to_client == false
      EventLog.add(self, :certificate, 'ADMIN#regrade_confirmation# Aborted for no deliver_certificate_to_client', :default)
      return false
    end

    recipients, cc = recipients_and_cc(to:, test_instance:, client_config: test_instance&.client_config)
    if recipients.nil?
      Alert.system('[Evaluation][Sending Regrade Confirmation]', "The regrade confirmation was not sent to anyone because no email found for Evaluation: #{get_admin_edit_link}\nTi: #{Rails.application.routes.url_helpers.admin_test_instance_url(test_instance)}", :certificate_delivery)
      return nil
    end
    MailjetHelper.send_regrade_confirmation(self, recipients, cc)
  end

  def recipients_and_cc(to:, test_instance:, client_config:)
    recipients = []
    user_cc = nil
    cc = nil
    if to == :client
      user_email = test_instance.get_certificate_email_destination
      return nil if user_email.nil?

      user_cc = client_config.cced_email if client_config && !client_config.cced_email.blank?

      user_email.split(',').each do |email|
        recipients << {
          'Email' => email.strip
        }
      end
      unless user_cc.nil?
        cc = []
        user_cc.split(',').each do |email|
          cc << {
            'Email' => email.strip
          }
        end
      end
    else
      recipients << {
        'Email' => "alerts+ptest+#{Rails.env}@pipplet.com"
      }
    end
    [recipients, cc]
  end

  def amount_of_possible_score_change
    begin
      possible_increase = CECRL_LEVELS_VALUES[:cecrl].index('C2+') - CECRL_LEVELS_VALUES[:cecrl].index(overall_cecrl_score)
      possible_decrease = CECRL_LEVELS_VALUES[:cecrl].index('A1-') - CECRL_LEVELS_VALUES[:cecrl].index(overall_cecrl_score)
    rescue StandardError
      possible_increase = 0
      possible_decrease = 0
    end
    { possible_increase: possible_increase, possible_decrease: possible_decrease }
  end

  def define_adjusted_json_assessment(change_level:, grade_labels:)
    json = get_json_assessment_in_keys

    new_json = json.dup

    # Remove the entries of the opinion of the examiner
    json = json.slice(*assessment_type.get_all_scorable_keys)

    # Delete entry about the candidate being a beginner, as we are increasing the scores
    strenghts_data = new_json[assessment_type.get_entry_from_key('strenghts')]
    if !strenghts_data.blank? && strenghts_data.include?(assessment_type.default_beginners_values['strenghts'])
      strenghts_data.delete(assessment_type.default_beginners_values['strenghts'])
      json.delete(assessment_type.get_entry_from_key('strenghts'))
      if new_json[assessment_type.get_entry_from_key('strenghts')].size.zero?
        # if empty, add _07 strenth to avoid being blocked bc of no strengths
        new_json[assessment_type.get_entry_from_key('strenghts')].push(assessment_type.default_strenght_when_empty)
      end
    end

    a = assessment_type

    entries = grade_labels.map do |lab|
      a.get_scorable_keys_for_skill(lab)
    end

    entries.flatten!

    # Loop through the json to do modify it
    json.each do |key, value|
      ## Next if the entry has not been selected to be raised
      next unless entries.include?(key)

      ## Next if the user did'nt answered to the question
      next if (value == a.default_no_answer_value) || value.match(/No answer.*provided/) || (value == 'No written answers were provided') || (value == 'No oral answers were provided')

      # For this entry in the json get the Assessment Question and its available notes
      id = a.find_assessment_question_by_key(key)['assessment_question_id']
      notes = AssessmentQuestion.find(id).json_data['choices'].to_a

      ## Get the index of the current grade being given in the scores array of this question
      index = notes.index(value)

      # Modify the json
      new_index = index - change_level

      ## If we are trying to increase the score of the maximum available grades, don't
      new_index = 0 if new_index.negative?
      new_index = 17 if new_index > 17
      new_json[key] = notes[new_index]
    end

    new_json
  end

  def initialize_adjusted_certificate(change_level:)
    # ["A1-", "A1", "A1+", ... ]
    cecrl_scores = CECRL_LEVELS_RANGE.keys.to_a

    grades = certificate.grades.map do |grade|
      current_index = cecrl_scores.index(grade.cecrl_score)
      new_index = current_index + change_level
      new_index = 0 if new_index.negative?
      new_index = 17 if new_index > 17
      new_cecrl_score = cecrl_scores[new_index]
      Grade.new({ label: grade.label, score: cecrl_score_to_max_score(new_cecrl_score), cecrl_score: new_cecrl_score })
    end
    Certificate.new(grades: grades)
  end

  # TODO: should be on TestInstance, add_and_deliver_evaluation, with a new helper method to select the right json_assessment entries
  def duplicate_with_adjusted_score(change_level, grade_labels, evaluation_goal = nil)
    # TODO : use the updated way to add Evaluations to TIs
    #       test_instance.add_and_force_assign_evaluation(
    #         ...
    #       )
    new_eval = dup
    new_eval.test_instance_id = test_instance_id

    new_eval.evaluation_goal = evaluation_goal || Evaluation::EVALUATION_GOAL[:standard]
    new_eval.update({ status: 'created', invoice_request_id: nil })
    admin = Examiner.pipplet_admin_examiner(test_language)
    EventLog.add(self, :admin, 'duplicate_with_adjusted_score# Assign admin examiner', :info)
    new_eval.examiner_id = admin.id
    new_eval.assign_to_admin!('assigned_at')

    # no assessment found but a certificate exists (eval has been graded by AI, certificate grade has been forced)
    if get_json_assessment_in_keys.slice(*assessment_type.get_all_scorable_keys).empty?
      if certificate&.grades&.any?
        certificate = initialize_adjusted_certificate(change_level:)
        new_eval.update(certificate: certificate)
        new_eval.assess_no_callback!('assessed_at')
      else
        Alert.system('Could not adjust scores from an evaluation without certificate grades or a json_assessment', "Evaluations : source: #{id}, destination: #{new_eval.id}")
      end
    else
      new_eval.json_assessment = define_adjusted_json_assessment(change_level:, grade_labels:)
      new_eval.assess_no_callback!('assessed_at')
      new_eval.generate_certificate
      new_eval.save!
    end
    new_eval
  end

  def clone_blocking_explanations!(evaluations:)
    cheating_explanations = evaluations.compact.filter_map(&:current_blocking_cheating_assessment_explanation)
    robustness_explanations = evaluations.compact.filter_map(&:current_blocking_robustness_assessment_explanation)

    json_assessment['assessment_cheating_explanation'] = cheating_explanations.first if cheating_explanations.any?
    json_assessment['assessment_robust_explanation'] = robustness_explanations.first if robustness_explanations.any?
    save!

    if cheating_explanations.count > 1 || robustness_explanations.count > 1
      EventLog.add(self, :process, "Clone Blocking Explanations : Different evaluations with blocking explanations in json_assessment, first one taken. Evaluation sources: #{evaluations.pluck(:id)}", :info)
    end
  end

  def assessed_or_delivered
    %w[assessed delivered].include?(status)
  end

  def certificate_type_is?(type)
    certification_type == type
  end

  def get_json_assessment_value(param, type = nil)
    type ||= check_param_type(param)
    ## Check what is the json assessment format, is it the same as the request or not?
    real_param = if type == identify_json_assessment_type
                   param
                 elsif type == :key
                   ## We have been provided a key and the json assessment is still in entries, get the entry
                   assessment_type.get_entry_from_key(param)
                   ## We have been provided an entry and the json assessment is now in keys, get the keys
                 else
                   assessment_type.get_key_from_entry(param)
                 end
    json_assessment[real_param]
  end

  def check_param_type(param)
    if param.match(/entry\./)
      :entry
    else
      :key
    end
  end

  def identify_json_assessment_type
    if json_assessment&.keys.first&.match(/entry\./)
      :entry
    else
      :key
    end
  end

  def get_json_assessment_in_keys
    if identify_json_assessment_type == :key
      json_assessment
    else
      transform_json_assessment_to_keys
    end
  end

  def transform_json_assessment_to_keys
    new_json = {}
    json_assessment.each do |key, value|
      entry = assessment_type.get_key_from_entry(key)
      new_json[entry] = value
    end
    self.json_assessment = new_json
    save!
    json_assessment
  end

  # Check if the same TI has one or more evaluation in delivered status
  def ti_has_delivered_evaluations?
    Evaluation.delivered
              .where(test_instance: test_instance_id)
              .size
              .positive?
  end

  def skip_duplicate_validation?
    (overall_score&.zero? && test_instance.is_full_skipped_or_being_answered?) || test_instance.talent_ai?
  end

  def ai_spoken_pass?
    certificate&.grades&.spoken&.last&.score.to_i >= TALENT_AI_BINARY_B2_SCORE_CUT
  end

  def ai_written_pass?
    certificate&.grades&.written&.last&.score.to_i >= TALENT_AI_BINARY_B2_SCORE_CUT
  end

  def ai_overall_pass?
    ai_spoken_pass? && ai_written_pass?
  end

  def can_be_delivered?
    !holding_peer_reviewed? &&
      (assessed? || soon_overdue?) &&
      evaluation_goal != Evaluation::EVALUATION_GOAL[:client_grade_check] &&
      !test_instance.regrading?
  end

  def holding_peer_reviewed?
    test_instance.evaluations.holding_peer_reviews.any?
  end

  def current_cheating_assessment_rule
    Evaluation::ASSESSMENT_CHEATING_RULES[json_assessment["assessment_cheating_explanation"]&.to_sym] || {}
  end

  def current_robustness_assessment_rule
    Evaluation::ASSESSMENT_ROBUST_RULES[json_assessment["assessment_robust_explanation"]&.to_sym] || {}
  end

  def current_cheating_assessment_rule_index
    Evaluation::ASSESSMENT_CHEATING_RULES.keys.index json_assessment["assessment_cheating_explanation"]&.to_sym
  end

  def current_robustness_assessment_rule_index
    Evaluation::ASSESSMENT_ROBUST_RULES.keys.index json_assessment["assessment_robust_explanation"]&.to_sym
  end

  def current_blocking_cheating_assessment_explanation
    return nil unless json_assessment && current_cheating_assessment_rule&.[](:tag).in?(Tag::EVALUATION[:block_evaluation_delivery])

    json_assessment["assessment_cheating_explanation"]
  end

  def current_blocking_robustness_assessment_explanation
    return nil unless json_assessment && current_robustness_assessment_rule&.[](:tag).in?(Tag::EVALUATION[:block_evaluation_delivery])

    json_assessment["assessment_robust_explanation"]
  end

  def add_tags_from_assessment_rules
    return if json_assessment.blank?

    [current_cheating_assessment_rule, current_robustness_assessment_rule].each do |assessment_rule|
      next if assessment_rule[:tag].blank?

      condition_method = assessment_rule.dig(:condition, :method)
      condition_variable = assessment_rule.dig(:condition, :variable)
      if condition_method.blank? || public_send(condition_method, condition_variable)
        add_tag_by_name(assessment_rule[:tag])
        EventLog.add(self, :process, "Automated assessment rule tag applied: '#{assessment_rule[:tag]}'", :default)
      end
    end
  end

  def overall_cecrl_score
    @overall_cecrl_score ||= certificate.grades.overall.last.cecrl_score
  end

  def previous_delivered
    Evaluation.where(test_instance_id: test_instance_id, status: :delivered).last
  end

  def anonymize_attributes!(force_associations: false, include_all_associations: true) # rubocop:disable Lint/UnusedMethodArgument
    event_logs.destroy_all
    if pdf_certificate
      pdf_certificate.clear
      raise ActiveRecord::RecordInvalid, self unless pdf_certificate.save
    end
  end

  # TODO: refactor find_assessment_question_by_key to return an AR Question
  def pretty_json_assessment
    return nil if json_assessment.nil?

    pretty_json_assessment = json_assessment.deep_dup

    question_id = assessment_type.find_assessment_question_by_key('assessment_cheating_explanation')&.[]('assessment_question_id')
    question = AssessmentQuestion.find(question_id) if question_id
    label = question.choice_at_index(current_cheating_assessment_rule_index)&.[]('text') if question
    pretty_json_assessment['assessment_cheating_explanation'] = label if label

    question_id = assessment_type.find_assessment_question_by_key('assessment_robust_explanation')&.[]('assessment_question_id')
    question = AssessmentQuestion.find(question_id) if question_id
    label = question.choice_at_index(current_robustness_assessment_rule_index)&.[]('text') if question
    pretty_json_assessment['assessment_robust_explanation'] = label if label

    pretty_json_assessment
  end

  def update_evaluation_state_time(*args)
    field = args[0]
    return unless field

    send("#{field}=", Time.now)
    save
  end

  def would_deliver_with_security_violation?
    has_security_violation_tags? || test_instance&.has_security_violation_tags?
  end

  private

  # ----------------------------------------
  # :section: AASM
  # ----------------------------------------

  # subsection: Utils
  # ----------------------------------------

  def log_status_change
    EventLog.status_change(self)
  end

  # subsection: Assign / Force Assign
  # ----------------------------------------

  def assign_to_examiner
    # don't change examiner if eval is_reviewed
    unless is_reviewed?
      begin
        set_default_examiner!
        change_if_unavailable
      rescue ActiveRecord::RecordInvalid => e
        # An email is also sent to linguistics+alerts when an admin examiner is assigned
        Alert.system("Error while assigning a default Examiner for Evaluation 'id'=#{id}", e.message)
      end
    end
    # when evaluation validated check if examiner have room for an evaluation else change examiner
    choose_examiner if examiner.nil? || !examiner.can_assign_evaluation?(self)[:result]
  end

  def check_urgent_mode
    self.urgent = true if CLIENTS_IN_URGENT_MODE.include?(test_instance.client_name)
  end

  def manage_evaluations_before_assign
    EvaluationsManagerService.new(test_instance:).update_reference_evaluation(self)
  end

  def manage_evaluations_after_assign
    EvaluationsManagerService.new(test_instance:).create_client_grade_check_peer_reviews
    EvaluationsManagerService.new(test_instance:).create_peer_review_interval_peer_reviews(self)
  end

  def send_evaluation_to_examiner
    return if test_instance.talent_ai?

    if is_reviewed?
      ExaminerMailer.evaluation_reassess(self).deliver_later
    else
      ExaminerMailer.new_test_to_assess(id).deliver_later
    end
  end

  # TODO: add to EvaluationsManagerService
  def trial_examiners_check
    trial_examiners = Examiner.available.on_trial_period_for(test_language)
    trial_examiners.each do |t_ex|
      next unless t_ex.can_assign_evaluation?(self)[:result] && (t_ex.evaluations.in_backlog_last(24.hours.ago).count < 3) && test_instance.evaluations.can_be_in_backlog.where(examiner_id: t_ex.id).empty?

      # TODO : use the updated way to add Evaluations to TIs
      #       test_instance.add_and_force_assign_evaluation(
      #         evaluation_goal:  Evaluation::EVALUATION_GOAL[:trial],
      #         examiner: t_ex
      #       )
      evaluation = Evaluation.new_for_test_instance(test_instance)
      evaluation.examiner = t_ex
      evaluation.evaluation_goal = Evaluation::EVALUATION_GOAL[:trial]
      EventLog.add(evaluation, :process, "Evaluation #{evaluation.id} has been created for Trial", :info)
      evaluation.save!
      evaluation.force_assign!('assigned_at')
    end
  end

  def trigger_ai_assessment
    return unless examiner.is_ai?

    certification_type = test_instance&.test_profile&.certification_type
    unless certification_type && certification_type.in?(TestProfile::AI_CERTIFICATION_TYPES)
      AiService.complete_section(section: :spoken, evaluation: self, error_message: 'TestProfile has an unsupported CertificationType')
      AiService.complete_section(section: :written, evaluation: self, error_message: 'TestProfile has an unsupported CertificationType')
      return
    end

    configuration_message = <<-HEREDOC.squish
                   Configuration:
                   Written: #{examiner.ai_config.dig('written', 'type')}AI |
                   Spoken: #{examiner.ai_config.dig('spoken', 'type')}AI
    HEREDOC
    EventLog.add(self, :ai, configuration_message, :info)
    AiServiceEvalJob.perform_later(section: :spoken, evaluation_id: id)
    AiServiceEvalJob.perform_later(section: :written, evaluation_id: id)
  end

  def prefill_json_assessment
    filler = nil
    unless Production.valid_total_recording_duration?(productions: test_instance.ordered_completed_productions_for(:audio))
      filler = JsonAssessmentFillerService.new(self)
      filler.fill_section(section: :spoken, score: nil)
    end

    unless Production.valid_total_text_length?(productions: test_instance.ordered_completed_productions_for(:written))
      filler ||= JsonAssessmentFillerService.new(self)
      filler.fill_section(section: :written, score: nil)
    end
  end

  def decrease_number_of_eval_since_last_peer_review
    if peer_review? || holding_peer_review?
      examiner.update!(number_of_eval_since_last_peer_review: 0)
    end
  end

  # subsection: Deliver
  # ----------------------------------------

  def after_deliver_callback
    EventLog.add(self, :process, 'ADMIN#after_deliver_callback#', :default)
    test_instance.mark_as_graded(self)
    test_instance.collect_evaluation_tags!(self)
    return unless test_instance.regrading?

    PippletClientsApi.update_user(test_instance_uuid: test_instance.uuid, attributes: { regrade_status: :unconfirmed })
  end

  def check_for_multiple_delivered_evaluations
    if peer_review?
      test_instance.evaluations.standards.update_all(evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review])
      update(evaluation_goal: Evaluation::EVALUATION_GOAL[:standard])
    end
  end

  # subsection: Soon Overdue
  # ----------------------------------------

  def send_reminder_to_examiner
    ExaminerMailer.evaluation_soon_overdue(self).deliver_later
    self.reminder_nb += 1
  end

  # subsection: Overdue
  # ----------------------------------------

  def send_overdue_to_examiner
    ExaminerMailer.evaluation_overdue(self).deliver_later
    self.reminder_nb += 1
  end

  # subsection: Cancel
  # ----------------------------------------

  def cancel_evaluation_callback
    ExaminerMailer.evaluation_canceled(self).deliver_later if ONGOING_STATUSES.include?(aasm.from_state)
    test_instance.calc_average_grades
    # TODO: temporary debugging
    EventLog.backtrace(self, ignore_last: 0, must_contain: '/app/models/', message: 'Cancelled from')
  end

  # subsection: Assess
  # ----------------------------------------

  def manage_evaluations_after_assess
    EvaluationsManagerService.new(test_instance:).on_evaluation_assessed(self)
  end

  def after_assess_callback
    pre_trial_alerting
    trial_alerting

    to_pay! if !pre_trial?

    EventLog.add(self, :process, 'ADMIN#after_assess_callback#', :default)
    ExaminerMailer.evaluation_received(self).deliver_later

    # If the TI is marked as monitored it send an email to linguistic
    AlertMailer.monitored_test_instance(test_instance).deliver_now if test_instance.monitored?

    if examiner_average?
      clone_blocking_explanations!(evaluations: test_instance.evaluations.assessed)
    end

    test_instance.calc_average_grades
    add_tags_from_assessment_rules

    if (standard? && test_instance.talent_ai?) ||
       examiner_average?
      CheckEvaluationForCertificateIssueJob.perform_later(id, true)
    end
  end
end
