# == Schema Information
#
# Table name: grades
#
#  id             :integer          not null, primary key
#  cecrl_score    :string           default("")
#  label          :string
#  score          :integer
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  certificate_id :integer
#
# Indexes
#
#  grades_certificate_index  (certificate_id)
#

class Grade < ApplicationRecord
  belongs_to :certificate

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :overall,	-> { where({ label: 'overall' }) }
  scope :written,	-> { where({ label: 'written' }) }
  scope :spoken,	-> { where({ label: 'spoken' }) }
  scope :by_label, ->(label) { where(label: label) }
end
