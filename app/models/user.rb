# == Schema Information
#
# Table name: users
#
#  id                             :integer          not null, primary key
#  accepts_marketing_contacts     :boolean          default(FALSE)
#  ai_consent_granted_at          :datetime
#  anonymized_at                  :datetime
#  authentication_token           :string
#  confirmation_sent_at           :datetime
#  confirmation_token             :string
#  confirmed_at                   :datetime
#  current_sign_in_at             :datetime
#  current_sign_in_ip             :string
#  current_ti                     :integer
#  deleted_at                     :datetime
#  disabled                       :datetime
#  email                          :string           default(""), not null
#  encrypted_password             :string           default(""), not null
#  failed_attempts                :integer          default(0), not null
#  first_name                     :string
#  group                          :string
#  identity_status                :string
#  is_vip                         :boolean          default(FALSE)
#  language_name                  :string
#  last_name                      :string
#  last_sign_in_at                :datetime
#  last_sign_in_ip                :string
#  locked_at                      :datetime
#  phone_number                   :string
#  remember_created_at            :datetime
#  reset_password_sent_at         :datetime
#  reset_password_token           :string
#  sign_in_count                  :integer          default(0), not null
#  status                         :integer
#  testdata                       :text
#  unconfirmed_email              :string
#  understands_cgu                :boolean
#  unlock_token                   :string
#  uuid                           :string           not null
#  verified_at                    :datetime
#  created_at                     :datetime
#  updated_at                     :datetime
#  identity_provider_id           :bigint
#  registration_code_id           :integer
#  strip_identity_image_id        :string
#  stripe_verification_session_id :string
#
# Indexes
#
#  index_users_on_confirmation_token    (confirmation_token) UNIQUE
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_identity_provider_id  (identity_provider_id)
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_users_on_unlock_token          (unlock_token) UNIQUE
#

class User < ApplicationRecord
  include Rails.application.routes.url_helpers

  include Identity::User

  include Anonymizable

  # Flag to indicate that we are updating the user from the API
  # See usage in +in_first_registration_phase?+
  attr_accessor :skip_cgu_and_password_validations
  attr_accessor :currently_granting_ai_consent

  before_validation :define_ai_consent_granted_at_when_granting
  before_save :decapitalized_name
  # ----------------------------------------
  # :section: Manage Authentication Token
  # ----------------------------------------
  before_save :ensure_authentication_token
  after_update :changed_groupe_callback, if: -> { group_changed? }

  def define_ai_consent_granted_at_when_granting
    self.ai_consent_granted_at = DateTime.current if ActiveModel::Type::Boolean.new.cast(currently_granting_ai_consent)
  end

  def decapitalized_name
    self.first_name = upcased_to_capitalized(first_name) if first_name.present? && (first_name == first_name.upcase || first_name == first_name.capitalize)
    self.last_name = upcased_to_capitalized(last_name) if last_name.present? && (last_name == last_name.upcase || last_name == last_name.capitalize)
  end

  def changed_groupe_callback
    return true if group_was.nil?

    url = admin_user_path(id)
    message = "User #{email} changed group from #{group_was} to #{group} go to: #{Rails.application.routes.default_url_options[:host] + url}"
    Alert.user_activity(self, "[USER CHANGE GROUP][User #{email} changed group]", message)
  end

  # ----------------------------------------
  # :section: Modules and Libraries
  # ----------------------------------------
  # Include default devise modules. Others available are:
  # :lockable, :timeoutable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :trackable, :validatable,
         :timeoutable, :confirmable

  # Soft delete object
  acts_as_paranoid

  # Add roles for user
  # Very simple Roles library without any authorization enforcement supporting scope on resource object.
  rolify

  # ----------------------------------------
  # :section: User Statuses
  # ----------------------------------------
  include AASM

  enum :status, {
    initialized: 0,            # first_name, last_name, email provided
    created: 1,                # password, password_confirmation, understand_cgu provided
    # language_info_given: 2,  # answered the language/test information page
    # audio_tested: 3,         # tested their audio settings
    # tutored: 4,              # went through (or skipped) the tutorial: currently answering questions
    # score_available: 5       # have answered enough questions to receive their score
    connected: 10,             # connected once
    email_confirmed: 20        # confirmed email

  }

  # Sweet transitions with callback, etc.
  # https://github.com/aasm/aasm
  aasm column: :status, enum: true do
    state :initialized, initial: true
    state :email_confirmed
    state :created
    state :connected

    event :connect do
      transitions from: [:initialized], to: :connected
    end
  end

  aasm(:identity_status, column: 'identity_status') do
    state :unverified,	initial: true
    state :verified
    state :pending
    state :verification_failed

    event :identity_set_to_unverified do
      transitions to: :unverified
    end

    event :identity_set_to_verified, after_commit: [:handle_stripe_report] do
      transitions from: %i[verification_failed unverified pending], to: :verified
    end

    event :identity_set_to_pending do
      transitions from: %i[pending unverified verification_failed verified], to: :pending
    end

    event :identity_set_to_verification_failed, after_commit: [:create_id_check_fail_teki] do
      transitions to: :verification_failed
    end
  end

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :enabled, -> { where(disabled: nil) }

  # ----------------------------------------
  # :section: Callbacks
  # ----------------------------------------

  ### SETTING UP DEFAULT VALUES WHEN CREATING THE ACCOUNT ###
  before_create do |user|
    # Ensure we have a UUID
    if user.uuid.nil?
      loop do
        user.uuid = SecureRandom.uuid
        break unless User.exists?(uuid: user.uuid)
      end
    end

    # Ensure that we have a group, default being 'web:self-service'
    # Other options are:
    #   - 'internal:guest' (see User.create_guest_user)
    #   - 'api:CLIENT_NAME'
    #   - 'code:CODE_NAME'
    #   - 'web:authorized-email'
    #   - 'web:whitelisted-email'
    user.group = 'web:self-service' if user.group.blank?

    # Set the initial status of the user to initialized
    user.status = User.statuses.fetch(:initialized)

    # Returning false from a before_* callback, it will cancel the save operation. Avoiding that.
    true
  end

  after_create do |user|
    # User creation alert
    # Alert.user_activity(self, 'Creation: ' + user.email, "User object:\n #{self.pretty_inspect}")
    user.confirmation_token = Devise.friendly_token(10)
    user.save(validate: false)
  end

  # ----------------------------------------
  # :section: Validations
  # ----------------------------------------
  validates :first_name, :last_name, presence: true
  validates_email_format_of :email, allow_blank: true, check_mx: true, mx_message: 'invalid email domain'

  ### VALIDATIONS FOR ACCOUNT INITIALIZATION ###

  # To be allowed to registered, users must meet one of the conditions below
  #  - they are being created through the API
  #  - their email address is from an authorized domain
  #  - their email address is in the white list
  #  - they have provided a valid registration code
  #
  # This checks are only performed during registration. Once registered, their is no more validation on emails.

  validate :allowed_to_register
  def allowed_to_register
    # Only perform these checks for initial registrations
    return unless new_record?
    # User being created through the API or internally
    return true if group.present? && (%w[api internal].include?(group_type) || is_examiner?)

    # User has email address from an authorized domain
    return (self.group = 'web:authorized-email') if /\A([\w.%+-]+)(@pipplet\.com\z)|(@gn3\.fr\z)/i.match?(email.strip)

    # User has provided a valid registration code
    return (self.group = "code:#{registration_code.code.downcase}") if registration_code&.is_valid?

    # Display a Generic error
    errors[:base] << I18n.t('activerecord.errors.models.user.no_email_or_no_token')
  end

  validate :group_format
  def group_format
    # Only perform these checks for initial registrations
    return unless new_record? && group.present?

    group_type = self.group_type
    group_name = self.group_name

    if %w[api web code internal error].include?(group_type)

      # Rules for API
      ## DELETED ON 22/02/2018 => Was causing troubles
      # if group_type == 'api' && (group_name.blank? || (group_name =~ /^[a-z0-9\-]+$/).nil?)
      #  errors[:group] << 'is not valid. It must respect this format /^[a-z0-9-]+$/'
      # end

    else
      errors[:group] << "is not valid. (provided: '#{group}')"
    end
  end

  # We need to override this Devise method to remove the validation on password for account initilization
  def password_required?
    super if !new_record? && !skip_cgu_and_password_validations
  end

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  # Language is for the application interface
  belongs_to :language, class_name: 'Language', foreign_key: 'language_name'

  # Logs for user
  has_many :event_logs, as: :loggable

  # User_Language is used to identify which language the candidats speak
  has_many :user_languages

  # Questionables answered by user
  has_many :productions
  has_many :receptions

  # Testscore is the result of an user to a language test
  has_many :test_scores

  # Users can register directly if they hold a registration code
  belongs_to :registration_code

  # Cache of past challenges, computed from ChallengeLogs
  # has_many :past_challenges, dependent: :delete_all

  # belongs_to :api_user
  ## DEPRECATED_TO_REMOVE
  has_many :test_instances, dependent: :destroy
  ## DEPRECATED_TO_REMOVE
  # def flag_abandonned_tests!
  #  each {|ti| ti.flag_abandonned_test! }
  # end

  # DEPRECATED - doesn't work for multiple tests per user
  # def for(test_profile_id, test_language)
  #   where({
  #     test_profile_id: test_profile_id,
  #     test_language: test_language
  #   }).first
  # end
  # end

  # User group custom data
  belongs_to :user_group, class_name: 'UserGroup', foreign_key: 'group', primary_key: 'name'

  # Added with Anonymization
  has_many :dynamic_question_data
  has_many :api_users, through: :test_instances
  has_many :evaluations, through: :test_instances
  has_many :api_orders

  belongs_to :identity_provider
  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :in_group, ->(g) { where(group: g) }

  # ----------------------------------------
  # :section: Accessors for all test related objects
  # ----------------------------------------

  scope :without_test_instances_created_after, lambda { |last_created_at|
    left_outer_joins(:test_instances)
      .where.not(id: TestInstance.where('created_at > ?', last_created_at).select(:user_id))
  }

  def locale
    language_name&.to_sym || Language.default
  end

  def current_test_instance
    test_instance = test_instances.not_completed.find_by(id: current_ti)
    return test_instance if test_instance

    test_instance = test_instances.not_completed.order(created_at: :asc).first
    return unless test_instance && persisted?

    update_columns(current_ti: test_instance.id)
    test_instance
  end

  def current_test_instance_duration
    total = 0
    current_test_instance_questions.each { |q| total += q[1] }
    total
  end

  def current_test_instance_questions
    (next_questionables&.includes(production: :challenge) || []).collect { |nq| [nq.question_type, nq.question_duration] }
  end

  def has_next_test_instance?
    test_instances.not_completed.count > 1
  end

  def skip_test_instance!
    current_test_instance.abandon!
  end

  def previous_test_instance
    test_instances.ended.order(end_date: :desc).first
  end

  # Next questionables
  delegate :next_questionable,              to: :current_test_instance, allow_nil: true
  delegate :next_questionables,             to: :current_test_instance, allow_nil: true
  delegate :current_questionable,           to: :current_test_instance, allow_nil: true
  # Test configuration
  delegate :next_questionable_profile,      to: :current_test_instance, allow_nil: true
  delegate :test_language,                  to: :current_test_instance, allow_nil: true
  delegate :has_answered_enough_questions?, to: :current_test_instance, allow_nil: true

  # Ids of full list of all challenges presented to the user, regardless of the outcome
  # -> this also includes timeout, reported productions, etc.
  def past_challenges_id
    productions.exclude_ignored.collect { |p| p.challenge.id unless p.challenge.nil? } +
      receptions.exclude_ignored.collect { |r| r.challenge.id unless r.challenge.nil? }
  end

  # Ids of full list of all challenges TO BE presented to the user, regardless of the outcome
  def future_challenges_id
    (next_questionables || []).map(&:challenge_id)
  end

  def past_and_future_challenges_id
    (past_challenges_id + future_challenges_id).uniq
  end

  # ----------------------------------------
  # :section: Simple accessors
  # ----------------------------------------
  def full_name
    "#{first_name || ''} #{last_name || ''}".squish
  end

  def full_name_with_id
    full_name + " (id:#{id})"
  end

  # Customer getter and setter to link a user to the registration code that they provided
  # Checks for validity of this code are performed in the validation phase
  def code_of_registration_code=(code)
    return if code.blank?

    _registration_code = RegistrationCode.find_by(code: code.strip)
    self.registration_code = _registration_code if _registration_code
  end

  def code_of_registration_code
    registration_code&.code
  end

  def group_type
    if group && group.present?
      group.partition(':').first
    else
      ''
    end
  end

  def group_name
    if group && group.present?
      group.partition(':').last
    else
      ''
    end
  end

  def skip_cgu_and_password_validations!
    self.skip_cgu_and_password_validations = true
  end

  def account_registration_url(options = {})
    generate_confirmation_token! if confirmation_token.blank?
    options[:confirmation_token] = confirmation_token
    options[:protocol] ||= (Rails.env.development? ? 'http' : 'https')
    Rails.application.routes.url_helpers.user_confirmation_url(options)
  end

  def ensure_authentication_token
    self.authentication_token = generate_authentication_token if authentication_token.blank?
  end

  private

  def upcased_to_capitalized(name)
    name&.gsub(/([A-Z]+)/) { |word| word.capitalize }
  end

  def generate_authentication_token
    loop do
      token = Devise.friendly_token
      break token unless User.where(authentication_token: token).exists?
    end
  end

  public

  # ----------------------------------------
  # :section: Instance methods
  # ----------------------------------------

  def terms_and_policies_accepted?
    understands_cgu? &&
      (requires_ai_consent? == false ||
        (requires_ai_consent? == ai_consent_granted_at.present?))
  end

  def is_disabled?
    !disabled.nil?
  end

  # Disable a user account. It prevents the user from logging in.
  def disable!
    update(disabled: Time.now) unless is_disabled?
  end

  # Re-enable a user account that was previously disabled. It allows the user from logging in.
  def enable!
    update(disabled: nil) if is_disabled?
  end

  # Accessors for admin role
  def is_admin?
    has_role? :admin
  end

  def is_examiner?
    has_role?(:examiner) && examiner.present?
  end

  def examiner
    @examiner ||= Examiner.find_by(user_id: id)
  end

  # Set user as producer: add role, update next_question_alogorithm and set next question
  def make_producer!
    add_role :producer
    # update(next_questionable_profile: :producer)
  end

  # Set user as producer: add role, update next_question_alogorithm and set next question
  def make_reviewer!
    add_role :reviewer
    # update(next_questionable_profile: :reviewer)
  end

  # ----------------------------------------
  # :section: Special methods for devise
  # ----------------------------------------
  # Changed the status of user after email confirmation
  def after_confirmation
    created!

    # Alert.user_activity(self, 'Activation', "User object:\n #{self.pretty_inspect}")
  end

  # This is a devise callback initiated after successfully authenticating.
  # This can be used to insert your own logic that is only run after the user successfully authenticates.
  def after_database_authentication
    # Alert.user_activity(self, 'Authentication', "User object:\n #{self.pretty_inspect}")
  end

  # This is a devise callback called during authentication
  #
  # After authenticating a user and in each request, Devise checks if your model is active by calling
  # model.active_for_authentication?. This method is overwritten by other devise modules. For instance,
  # :confirmable overwrites .active_for_authentication? to only return true if your model was confirmed.
  #
  # http://www.rubydoc.info/github/plataformatec/devise/master/Devise/Models/Authenticatable
  def active_for_authentication?
    super && special_condition_is_valid?
  end

  # Whenever active_for_authentication? returns false, Devise asks the reason why your model is inactive using the
  # inactive_message method.
  def inactive_message
    special_condition_is_valid? ? super : :user_account_has_been_disabled
  end

  def special_condition_is_valid?
    !is_disabled?
  end

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------
  # def add_test_instance(options = {})
  #   return ArgumentError if options[:test_profile_id].nil?
  #   return ArgumentError if options[:test_language].nil?

  #   test_instances.create({
  #     test_profile_id: options[:test_profile_id],
  #     direct_user_id:  options[:direct_user_id],
  #     api_user_id:     options[:api_user_id],
  #     test_language:   options[:test_language],
  #     test_mode:       ((options[:test_mode] == true || options[:test_mode].nil?) ? true : false)
  #   })
  # end

  def update_group_from_api(new_group_name)
    # Update user group
    # Ensure that we bypass validations
    skip_cgu_and_password_validations!
    update(group: "api:#{new_group_name}")
  end

  def handle_stripe_report
    stripe_data = get_stripe_identity_verification_report
    face_img_file = stripe_data[:report].selfie.selfie
    image = Stripe::File.retrieve(face_img_file)
    update(verified_at: Time.now, strip_identity_image_id: image.id)
  end

  def has_dummy_email?
    /dummycandidate/.match?(email) || false
  end

  # def update_group_from_direct_user_cache(du)
  #   if du && du.is_a?(DirectUser) && du.pipplet_clients_account_api_group && !du.pipplet_clients_account_api_group.empty?
  #     self.update_group_from_api(du.pipplet_clients_account_api_group)
  #   end
  # end

  # Main, central, method to create user and manage associated test_instances
  #
  # If a user exists with the email provided, we find it:
  #   - if the user already has a test for the couple (test_profile, test_language), nothing happens
  #   - otherwise we create a test instance
  #
  # If no user exists for that email
  #   - User account is created
  #   - TestInstance is created and associated to this user
  #
  #
  # :source can be: :api_user, :direct_user, :api_order
  # :source_id must be the corresponding object id
  #
  # def User.find_or_create_with_test_instance(params)
  #   # Extract params
  #   user_params     = params[:user]
  #   test_profile_id = params[:test_profile_id]
  #   test_language   = params[:test_language]
  #   user_locale     = (params[:user_locale] || '').to_sym
  #   source          = params[:source]
  #   source_id       = params[:source_id]
  #   test_mode       = (params[:test_mode] == true || params[:test_mode].nil?) ? true : false

  #   # Basic validations of arguments
  #   raise ArgumentError.new("[User.find_or_create_with_test_instance] No user params") if user_params.nil?
  #   raise ArgumentError.new("[User.find_or_create_with_test_instance] Invalid test_language: #{test_language}") if test_language.nil? || !AVAILABLE_TEST_LANGUAGES.include?(test_language.to_sym)
  #   raise ArgumentError.new("[User.find_or_create_with_test_instance] Invalid user_locale: #{user_locale}") if user_locale.nil?  || !Language.available.include?(user_locale.to_sym)

  #   # Check that requested language is available for the test_profile
  #   tp = TestProfile.active.find(test_profile_id)
  #   raise ArgumentError.new("[User.find_or_create_with_test_instance] Invalid or Inactive test_profile: #{test_profile_id} ") if tp.nil?
  #   raise ArgumentError.new("[User.find_or_create_with_test_instance] Test_language: #{test_language} not available for TestProfile '#{tp.name}' (#{tp.id})") if !tp.has_test_language?(test_language)

  #   # We prepare all necessary data to create or update the user
  #   group = case source
  #     when :api_user    then 'api:' + (user_params[:group]||'').to_s
  #     when :api_order   then ApiOrder.exists?(source_id) ? ApiOrder.find(source_id).group.to_s : ('api:api-order-'+source_id || '').to_s
  #     when :direct_user then 'api:waiting-for-group-from-api'
  #     else
  #       "error:no-group"
  #   end
  #   email = (user_params[:email] || "").strip.downcase

  #   # Get user, if it exists
  #   user = User.where(email: (user_params[:email] || "").strip.downcase).first

  #   # If user already exists
  #   if user
  #     # Update user with latest info
  #     user.skip_cgu_and_password_validations!
  #     user.update({
  #         first_name:   user_params[:first_name],
  #         last_name:    user_params[:last_name],
  #         phone_number: user_params[:phone_number],
  #         group: group,
  #         language_name: user_locale.to_s
  #     })

  #     # Update group from DU
  #     if source == :direct_user
  #       du = DirectUser.where(id: source_id).first
  #       user.update_group_from_direct_user_cache(du) if du
  #     end

  #     # Cancel previous test mode instances for this user
  #     TestInstance.not_cancelled.where({user_id: user.id, test_mode: true}).each do |ti|
  #       ti.cancel!
  #     end

  #     # If the existing user has a test instance for the couple (test_profile, test_language), nothing to do
  #     ti = TestInstance.not_test_mode.not_cancelled.where({user_id: user.id, test_profile_id: test_profile_id, test_language: test_language}).first
  #     # If the user is being created again through an API, we send a notification to Pipplet Team to manage this case manually
  #     if [:api_order].include?(source) && ti
  #       m = "[User.find_or_create_with_test_instance] User #{user.id} is trying to take the same test again! TestLanguage: #{test_language} TestProfile '#{tp.name}' (#{tp.id})\n\n"
  #       m += "Source Api Order: '#{source_id}'\n"
  #       m += "Test Language: '#{test_language}'\n"
  #       m += "Test Profile: '#{tp.to_yaml}'\n"
  #       m += "\nUser object:\n"
  #       m += user.pretty_inspect

  #       s = "[MANUAL ACTION REQUIRED][#{source.to_s} ##{source_id.to_s}]  User #{user.full_name_with_id} is being asked take the same test again through the API"

  #       Alert.test_instance_activity(ti, s, m)
  #     end

  #     if ti
  #       if [:api_user].include?(source)
  #         ## We would create a new TI each time
  #         Alert.system("[New Feature in production]", "We have created a new TI for user #{user.id} while Ti #{ti.id} already existed." )
  #       else
  #         return user
  #       end
  #     end

  #   # If it's a new user, we create it
  #   else

  #     user = User.new(
  #         email:        email,
  #         first_name:   user_params[:first_name],
  #         last_name:    user_params[:last_name],
  #         phone_number: user_params[:phone_number],
  #         group: group,
  #         language_name: user_locale.to_s
  #     )
  #     user.skip_cgu_and_password_validations!
  #     user.skip_confirmation_notification!
  #     user.created!

  #   end # if user

  #   # -------------------------------------------------------------------------- #
  #   # At this stage, we have a user. Now we need to get or create a test_instance
  #   # -------------------------------------------------------------------------- #

  #   if user && user.valid?
  #     # --- At this stage we have a valid user ---
  #     # ---     Creating the TestInstance      ---
  #     ti = if source == :direct_user && !source_id.nil?
  #       user.add_test_instance({
  #         test_profile_id: test_profile_id,
  #         direct_user_id: source_id,
  #         test_language: test_language,
  #         test_mode: test_mode
  #       })
  #     elsif source == :api_user && !source_id.nil?
  #       user.add_test_instance({
  #         test_profile_id: test_profile_id,
  #         api_user_id: source_id,
  #         test_language: test_language,
  #         test_mode: test_mode
  #       })
  #     else
  #       user.add_test_instance({
  #         test_profile_id: test_profile_id,
  #         test_language: test_language,
  #         test_mode: test_mode
  #       })
  #     end

  #     # --- Post User & TestInstance creation callbacks --- #
  #     if source == :api_order
  #       # Update ApiOrder if it exists
  #       ao = ApiOrder.where(id: source_id).first
  #       if ao
  #         ao.update({
  #           user_id: user.id,
  #           test_instance_id: ti.id
  #         })
  #       end

  #       # Send email notification for candidates to pass the test
  #       ti.send_email_notification_to_candidate!
  #     end

  #     if source == :direct_user
  #       if !defined?(du) || !du
  #         du = DirectUser.where(id: source_id).first
  #       end

  #       # Trigger creation of user in pipplet-clients (async task)
  #       # by calling the job PippletClientsApi.create_user(test_instance_id, direct_user_id)
  #       if du
  #         # Set/Update user group
  #         user.update_group_from_direct_user_cache(du)

  #         if du.sync_with_pipplet_clients?
  #           CreatePippletClientsUserJob.perform_later(ti.id, du.pipplet_clients_campaign_id)
  #         end

  #         ## Send Email to candidate if candidate is B2C
  #         if ti.test_profile.test_taker_type == 'b2c'
  #           ApplicationController.helpers.send_welcome_b2c_email(ti)
  #         end
  #       end
  #     end
  #     # --- EO: Post User & TestInstance creation callbacks --- #
  #   end # if user && user.valid?

  #   return user
  # end

  def group_id
    if group_name.blank?
      'unknow'
    else
      group_name.split('-').first
    end
  end

  def self.super_producer
    _user = User.find_by_email('<EMAIL>')
    unless _user
      _user = User.new(
        email: '<EMAIL>',
        first_name: 'Pipplet',
        last_name: 'SuperProducer',
        understands_cgu: true,
        group: 'internal:super_producer',
        password: 'hduaslfh843219fhufDSAGAJGI#%!',
        password_confirmation: 'hduaslfh843219fhufDSAGAJGI#%!',
        questions_to_answer: 5
      )
      _user.skip_confirmation!
      _user.update_attributes(status: User.statuses.fetch(:email_confirmed))
      _user.save!
      # _user.score.update_attribute(:rating, 1800)
      # _user.score.update_attribute(:rating_deviation, 0)
    end

    _user
  end

  def redirection_url
    ti = test_instances.where.not(redirection_url: nil).detect(&:active?)
    ti ? ti.redirection_url : nil
  end

  def client_name
    ti = test_instances.where.not(client_name: nil).detect(&:active?)
    ti ? ti.client_name : nil
  end

  def internationalised_phone_number
    Phonelib.parse(phone_number)
  end

  def create_id_check_fail_teki
    EventLog.add(self, :identity, 'ID Check has failed', :warning)
    self.test_instances.skip_identity_started.each do |test_instance|
      next unless test_instance.need_strong_id_check?

      EventLog.add(test_instance, :identity, 'ID Check has failed for the related user', :warning)
      test_instance.create_teki(status: 'ID Check', from: 'validation', force: true, teki_category: 'ID Check')
    end
  end

  def has_talent_ai_test_instances?
    @has_talent_ai_test_instances ||= test_instances.detect { |ti| ti.talent_ai? == true }.present?
  end

  def requires_ai_consent?
    has_talent_ai_test_instances? && ai_consent_granted_at.blank?
  end

  def current_taken_test_instances
    test_instances.not_completed
  end

  def anonymize_attributes!(force_associations: false, include_all_associations: true)
    attributes = { first_name: Anonymized.name,
                   last_name: Anonymized.name,
                   email: Anonymized.email,
                   understands_cgu: true }

    attributes[:unconfirmed_email] = Anonymized.email if unconfirmed_email.present?
    attributes[:phone_number] = Anonymized.phone_number if phone_number.present?
    attributes[:last_sign_in_ip] = Anonymized.ip if last_sign_in_ip.present?
    attributes[:current_sign_in_ip] = Anonymized.ip if current_sign_in_ip.present?

    ActiveRecord::Base.connection.transaction do
      if include_all_associations
        test_instances.each do |test_instance|
          test_instance.anonymize!(force: force_associations, include_all_associations: false) if test_instance.created_at < Rails.application.config.anonymization.last_created_at && !test_instance.anonymized?
        end
      end
      update!(attributes)
    end
  end

  def anonymize_related!
    delete_stripe_face_img_from_aws
  end
end
