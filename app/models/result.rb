# == Schema Information
#
# Table name: results
#
#  id               :integer          not null, primary key
#  reception_id     :integer
#  created_at       :datetime
#  updated_at       :datetime
#  status           :integer          default("created")
#  deleted_at       :datetime
#  test_instance_id :integer
#

class Result < ApplicationRecord
  # Soft delete object (when you destroy an object, it sets deleted_at, but doesn't delete it)
  acts_as_paranoid

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  belongs_to :reception

  delegate :production,     to: :reception
  delegate :challenge,      to: :reception
  delegate :challenge_logs, to: :reception

  # This relationship exists, but unsure it's necessary to access it from
  # Result objects. It should be accessed through QuestionElements.
  # has_many :dynamic_question_data, as: :dqd_able

  ## Status, used mainly in the default scope to only get published challenges
  enum :status, { created: 0, validated: 1 }

  # ----------------------------------------
  # :section: Validate results
  # ----------------------------------------
  # This method triggers the validation process for all questions
  # As a side effect, it creates ChallengeLog objects which must be
  # processed to update scores.
  #
  # IMPORTANT! Only call this method using ValidateResultJob.perform_later(Result) as SQL is quite heavy.
  def validate!
    return if validated?

    challenge_validated_at = Time.now

    reception.questions.each do |question|
      # Validate all QuestionElements in a Question
      question.question_elements.scorable.for(reception).each do |question_element|
        # Create ChallengeLogs
        ChallengeLog.create_from_objects(
          reception.challenge_id,
          question,
          question_element,
          production,
          reception,
          reception.production.production_user,
          reception.reception_user,
          question_element.validated?(self),
          challenge_validated_at
        )
      end
    end

    self.validated!
  end

  # ----------------------------------------
  # :section: Access stat related to this challenge
  # ----------------------------------------
  def result_overview
    "#{total_successes.to_s}/#{total_matches.to_s}"
  end

  def total_successes
    challenge_logs.where(success: true).count
  end

  def total_matches
    challenge_logs.count
  end
end
