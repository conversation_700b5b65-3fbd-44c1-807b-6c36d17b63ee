# == Schema Information
#
# Table name: api_orders
#
#  id                :integer          not null, primary key
#  anonymized_at     :datetime
#  order_errors      :string
#  order_information :string           default({})
#  order_reference   :string
#  source            :integer
#  status            :integer
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  api_user_id       :integer
#  test_instance_id  :integer
#  user_id           :integer
#
# Indexes
#
#  api_orders_ti_index                  (test_instance_id)
#  index_api_orders_on_order_reference  (order_reference)
#

class ApiOrder < ApplicationRecord
  include Anonymizable

  serialize :order_information, coder: JSON

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  belongs_to :user
  belongs_to :api_user
  belongs_to :test_instance

  # ----------------------------------------
  # :section: Validations
  # ----------------------------------------
  # validates :user, presence: true
  validates :api_user, presence: true

  # ----------------------------------------
  # :section: Statuses
  # ----------------------------------------
  # Used mainly in the default scope to only get published challenges,
  enum :status, { received: 0, accepted: 1, rejected: 2, resulted: 3, completed: 4, archived: 5, errored: 6 }

  # ----------------------------------------
  # :section: Sources
  # ----------------------------------------
  INTEGRATION_SOURCES = {
    smartrecruiters: 0,
    greenhouse: 4,
    talentsoft: 5,
    teamtailor: 6,
    lever: 7,
    workable: 9,
    jobylon: 10,
    factorial: 11,
    jobvite: 12
  }.freeze

  API_SOURCES = {
    pipplet_clients: 2, # pclients calls /api/v1/test_instances_controller
    external: 3, # platforms integrated with us call /api/v1/language_tests_controller
    dashboard: 8 # with ApiUser 'name'='main-dashboard'
  }.freeze

  SSO_SOURCES = {
    alv: 13
  }.freeze

  enum :source, INTEGRATION_SOURCES.merge(API_SOURCES).merge(SSO_SOURCES).freeze

  # ----------------------------------------
  # :section: Instance methods
  # ----------------------------------------

  def accept!
    accepted!
    case source
    when 'teamtailor', 'lever'
      Integrations::PublishOrderUpdateJob.perform_later(id, source:)
    end
  end

  def reject!(rejected_test_instance: nil)
    rejected!
    case source
    when 'lever'
      Integrations::Lever::ErrorJob.perform_later(api_user.id, order_information['opportunity_id'], rejected_test_instance&.errors&.full_messages&.to_sentence) if rejected_test_instance&.errors&.full_messages
      Integrations::Lever::ErrorJob.perform_later(api_user.id, order_information['opportunity_id'], rejected_test_instance&.user&.errors&.full_messages&.to_sentence) if rejected_test_instance&.user&.errors&.full_messages
    end
  end

  def test_graded_callback
    case source
    when 'smartrecruiters', 'teamtailor', 'lever', 'talentsoft', 'workable', 'jobylon', 'factorial', 'jobvite'
      Integrations::PublishOrderUpdateJob.perform_later(id, source:)
    when 'alv'
      Alv::PublishOrderUpdateJob.perform_later(id, source:)
    when 'pipplet_clients'
      # NO CallBack
    else
      TestGradedCallbackJob.perform_later(self)
    end
  end

  def service_name
    return nil unless order_information.is_a? Hash

    order_information&.dig('params', 'pipplet_clients_service', 'service_name')
  end

  def sub_service_name
    return nil unless order_information.is_a? Hash

    order_information&.dig('params', 'pipplet_clients_sub_service', 'sub_service_name')
  end

  def anonymize_attributes!(force_associations: false, include_all_associations: true) # rubocop:disable Lint/UnusedMethodArgument
    update!(order_information: '').then { return } if order_information.nil? || order_information.is_a?(String)

    if order_information.dig('params', 'user')

      order_information['params']['user']['email'] = Anonymized.email
      order_information['params']['user']['first_name'] = Anonymized.name
      order_information['params']['user']['last_name'] = Anonymized.name
      order_information['params']['user']['phone_number'] = Anonymized.phone_number
    end

    if order_information.dig('params', 'pipplet_clients_client')
      order_information['params']['pipplet_clients_client']['email'] = Anonymized.email
      order_information['params']['pipplet_clients_client']['first_name'] = Anonymized.name
      order_information['params']['pipplet_clients_client']['last_name'] = Anonymized.name
    end

    order_information['raw_post'] = '' if order_information.key? 'raw_post'
    save!
  end
end
