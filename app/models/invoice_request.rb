# == Schema Information
#
# Table name: invoice_requests
#
#  id                              :integer          not null, primary key
#  status                          :string
#  examiner_id                     :integer
#  created_at                      :datetime
#  paid_at                         :datetime
#  invoice_currency                :string           default("EUR")
#  bank_account_currency           :string           default("EUR")
#  total_in_fee_currency           :float
#  request_sent_at                 :datetime
#  invoice_received_at             :datetime
#  json_examiner_fees              :json
#  validated_at                    :datetime
#  simple_evals_counter            :integer
#  detailed_evals_counter          :integer
#  month                           :string
#  invoice_pdf_file_name           :string
#  invoice_pdf_content_type        :string
#  invoice_pdf_file_size           :bigint
#  invoice_pdf_updated_at          :datetime
#  requested_amount                :float
#  validated_auto_at               :datetime
#  vat_amount                      :float            default(0.0)
#  examiner_invoice_reference      :string           default("")
#  requested_amount_evaluations    :float
#  requested_amount_other_services :float
#  written_evals_counter           :integer          default(0)
#  spoken_evals_counter            :integer          default(0)
#

class InvoiceRequest < ApplicationRecord
  include AASM

  after_create :update_examiner_fees, :set_default_currency, :calculate_and_save_invoice_amount
  before_save :get_total_and_update_in_fee_currency, if: :invoice_currency_changed?
  before_save :update_invoice_pdf_file_name, if: :invoice_pdf_file_name_changed?
  has_attached_file :invoice_pdf, {
    s3_protocol: :https,
    path: '/:class/:style/:hash.:extension',
    hash_secret: 'qT60JCzayxmLwF7lwFRJRmDtD15n7LQcOpMr5AMENFxJSr5geOz10FieVk0xXr3V'
  }
  validates_attachment_content_type :invoice_pdf, content_type: ['application/pdf']

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  belongs_to :examiner
  has_many :evaluations, dependent: :nullify, after_add: %i[update_counters check_for_month_update],
                         after_remove: %i[update_counters check_for_month_update]
  has_many :assessment_types, through: :evaluations
  has_many :examiner_other_services
  aasm column: :status do
    state :created,	initial: true
    state :request_sent
    state :invoice_received
    state :validated
    state :validated_auto
    state :paid

    event :send_request, after_commit: %i[send_to_examiner update_state_time schedule_reminders] do
      transitions from: %i[created request_sent], to: :request_sent
    end

    event :receive_invoice, after_commit: [:update_state_time] do
      transitions from: %i[request_sent invoice_received validated validated_auto], to: :invoice_received
    end

    event :set_validated, after_commit: [:update_state_time] do
      transitions from: %i[invoice_received request_sent created], to: :validated
    end

    event :set_validated_auto, after_commit: [:update_state_time] do
      transitions from: %i[invoice_received request_sent created], to: :validated_auto
    end

    event :set_paid, after_commit: [:update_state_time] do
      transitions from: %i[paid validated validated_auto], to: :paid
    end
  end

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------
  scope :created_or_sent,             -> { where('status = ? or status = ?', 'created', 'request_sent') }
  scope :with_invoice,                -> { where({ status: %i[invoice_received validated validated_auto paid] }) }

  # TVA from https://www.economie.gouv.fr/entreprises/tout-savoir-sur-tva
  VAT = {
    GF: { VAT: 0.0 },
    PF: { VAT: 0.16 },
    TF: { VAT: 0.0 },
    GP: { VAT: 0.085 },
    MQ: { VAT: 0.085 },
    YT: { VAT: 0.0 },
    NC: { VAT: 0.0 },
    RE: { VAT: 0.085 },
    BL: { VAT: 0.0 },
    MF: { VAT: 0.0 },
    PM: { VAT: 0.0 },
    WF: { VAT: 0.0 },
    FR: { VAT: 0.2 }
  }

  EU_COUNTRIES_OUTSIDE_FRANCE = %w[BE BG CZ DK DE EE IE EL ES HR IT CY LV LT LU HU MT NL AT PL PT RO SI SK FI SE GR]

  def update_invoice_pdf_file_name
    self.invoice_pdf_file_name = "#{id} Invoice #{I18n.transliterate(examiner.user.first_name)} #{I18n.transliterate(examiner.user.last_name)} - #{month}.pdf"
  end

  def has_invoice?
    %i[request_sent invoice_received validated validated_auto paid].include?(status.to_sym)
  end

  def schedule_reminders
    InvoiceRequestReminderJob.set(wait: 5.days).perform_later(id)
    InvoiceRequestReminderJob.set(wait: 10.days).perform_later(id)
  end

  def is_requested_amount_validated
    unless requested_amount
      message = "[Invoice##{id} validation] requested_amount=nil"
      Alert.system(message)
      EventLog.add(self, :invoice, message, :danger)
      return false
    end

    requested_amount_converted = convert_requested_amount_to_euros
    calculate_and_save_invoice_amount

    EventLog.add(self, :invoice,
                 "[Invoice##{id} comparison] Requested_amount : #{requested_amount_converted} | Calculated_amount : #{calculate_invoice_amount}", :info)

    ((requested_amount_converted - (requested_amount_converted * (INVOICE_AMOUNT_COMPARISON_PERCENTAGE_DIFFERENCE / 100.to_f))) <= self.total_in_fee_currency) &&
      (self.total_in_fee_currency <= (requested_amount_converted + (requested_amount_converted * (INVOICE_AMOUNT_COMPARISON_PERCENTAGE_DIFFERENCE / 100.to_f))))
  end

  def convert_requested_amount_to_euros
    currency = Currency.find_by_code(invoice_currency)
    unless valid_for_conversion?(currency)
      EventLog.add(self, :invoice,
                   "[Invoice##{id} no conversion] Requested amount : #{requested_amount} #{currency.code} TO #{requested_amount} #{currency.code}", :info)
      return requested_amount
    end

    result = requested_amount / currency.eur_exchange_rate
    EventLog.add(self, :invoice,
                 "[Invoice##{id} conversion] Requested amount : #{requested_amount} #{currency.code} TO #{result} #{currency.code}", :info)
    result
  end

  def calculate_and_save_invoice_amount
    get_total_and_update_in_fee_currency
    save!
  end

  def unique_assessment_types_names
    assessment_types.uniq.pluck(:name)
  end

  def calculate_invoice_amount
    total_eval_amount + total_examiner_other_services_amount
  end

  def total_eval_amount
    total_eval = 0
    unique_assessment_types_names.each do |assessment_type_name|
      total_eval += total_eval_cost_by_type(assessment_type_name)
    end
    total_eval
  end

  def get_total_and_update_in_fee_currency(currency = Currency.find_by_code(invoice_currency))
    self.total_in_fee_currency = calculate_invoice_amount
    convert_to_euro(currency) if valid_for_conversion?(currency)
  end

  def nb_eval_by_type(assessment_type_name)
    assessment_types.where(name: assessment_type_name).count
  end

  def assessment_fee_by_type(assessment_type_name:)
    examiner.assessment_fee(assessment_type_name: assessment_type_name)
  end

  def total_eval_cost_by_type(assessment_type_name)
    nb_eval_by_type(assessment_type_name) * assessment_fee_by_type(assessment_type_name: assessment_type_name)
  end

  def total_examiner_other_services_amount
    examiner_other_services.sum(:amount)
  end

  def build_invoice_request(evaluations, _examiner)
    Evaluation.where(id: evaluations.pluck(:id), invoice_request_id: nil).update_all(invoice_request_id: id)
    update(status: 'created')
    update_counters
    check_for_month_update
    self
  end

  def associate_standing_examiner_other_services
    self.examiner.examiner_other_services.standing.each do |examiner_other_service|
      examiner_other_service.invoice_request = self
      examiner_other_service.save!
    end
  end

  def update_state_time(field)
    send(:"#{field}=", Time.now)
    save
  end

  def update_examiner_fees
    exf = {}
    AssessmentType.all.each do |a|
      e = ExaminerFee.joins(:assessment_type)
                     .where(examiner_id: examiner_id, assessment_type: { name: a.name })
      if e.blank?
        (e = ExaminerFee.create(
          assessment_type: a,
          examiner_id: examiner_id,
          fee: a.get_fee,
          currency: Examiner.find(examiner_id).default_currency
        )).save!
      end
      exf[a.name.to_s.to_sym] = e
    end
    self.json_examiner_fees = { simple: exf[:simple], detailed: exf[:detailed], written: exf[:written], spoken: exf[:spoken] }
    calculate_and_save_invoice_amount
    save!
  end

  def set_default_currency
    self.invoice_currency = examiner.default_currency
  end

  def get_fees_currency
    ## Check if all fees are in the same currency, if so returns the currency, else returns an error
    fees_currencies = json_examiner_fees.values.flatten.pluck('currency').uniq
    if fees_currencies.size > 1
      Alert.system("[InvoiceRequest ##{id}] Examiner fees currencies are not the same: #{fees_currencies.join(', ')}")
      return nil
    end
    fees_currencies.first
  end

  def send_to_examiner
    # Sent to default_recipients for now : alerts+ptest
    SendInvoiceRequestJob.perform_later(self)
  end

  def send_invoice_request
    send_request!('request_sent_at') if evaluations.count > 5
  end

  def update_counters(_evaluation = nil)
    self.simple_evals_counter = nb_eval_by_type('simple')
    self.detailed_evals_counter = nb_eval_by_type('detailed')
    self.written_evals_counter = nb_eval_by_type('written')
    self.spoken_evals_counter = nb_eval_by_type('spoken')
    save!
  end

  def check_for_month_update(evaluation = nil)
    evaluation = evaluations.order('assessed_at DESC').first if evaluation.nil?
    return unless evaluation&.assessed_at

    self.month = evaluation.assessed_at.strftime('%Y-%m')
    save!
  end

  def try_auto_validation
    if is_requested_amount_validated && status == 'invoice_received' && examiner.invoice_requests.count > 1
      EventLog.add(self, :invoice, "[Invoice##{id}] validated auto", :info)
      set_validated_auto!('validated_auto_at')
    else
      EventLog.add(self, :invoice, "[Invoice##{id}] Not validated auto", :info)
    end
  end

  def is_validated
    validated? || validated_auto?
  end

  # ----------------------------------------
  # :section: Class methods
  # ----------------------------------------
  def self.create_invoice_requests(go_back = 1)
    date = (Time.zone.now - go_back.month).end_of_month
    evaluation_ids = Evaluation.available_for_payment_before(date).pluck(:id)
    CreateInvoiceRequestsJob.perform_later(evaluation_ids)
  end

  def self.send_invoice_requests(invoice_request_ids)
    invoice_requests = InvoiceRequest.where(id: invoice_request_ids)
    invoice_requests.each(&:send_invoice_request)
  end

  def self.get_csv_export_for_month(month: Date.today.strftime('%Y-%M'))
    ## Definition of Accounting Period
    # Accounting Period is defined by ETS. They follow our fiscal year which starts on the first of October
    # So 2022/01 is the first month of the fiscal year 2022, and is October, so 2022/01 in fiscal year is 2021-10 in civil year.

    ## Precision about periods
    # In this function we are given a month for selecting Invoice Request.
    # The month will be the month of the time the request. Let's say we request the IR for the month of October 2021.
    # However the IR sent on the month of October 2021 concern evaluations that have been assessed in September, because InvoiceRequest is based on the evaluations of the preceding month.
    # And the Accounting Period is linked to the month of the evaluaations.
    # So Request for October 2021 will contain evaluations assessed in September 2021.
    # So requestion for October 2021 will be in Accounting Period of September 2021. Which writes 2021/12.

    invoice_requests = InvoiceRequest.paid.where(month: month)
    full_date = month.split('-').map(&:to_i)

    ## This is why here we are translating the accounting period based on a >= 11 condition
    if full_date[1] >= 9
      m = full_date[1] - 8
      y = full_date[0] + 1
    else
      y = full_date[0]
      m = full_date[1] + 4
    end
    accounting_period = "#{y}/#{m}"

    CSV.generate do |csv|
      # headers
      csv << ['Account Nb', 'Accounting Period', 'Invoice date', 'Invoice number', 'Line description', 'Currency',
              'Value', '', '', '', 'Cost Centre Code', '', '', 'TVA Tax Code']
      invoice_requests.each do |ir|
        next if ir.blank?

        examiner = ir.examiner

        if InvoiceRequest::EU_COUNTRIES_OUTSIDE_FRANCE.include?(examiner.country_code)
          # UE Examiner
          ##  Negative amount to the account of the examiner, with cost center MOOP
          data = {
            account_nb: examiner.id.to_s,
            currency: ir.invoice_currency,
            value: (-ir.requested_amount).to_s,
            cost_center_code: 'MOOP'
          }
          csv << get_line_from_ir_and_data(accounting_period, ir, data)

          ##  Negative amount of 20% on account 210314
          data = {
            account_nb: '210314',
            currency: ir.invoice_currency,
            value: (-(ir.requested_amount * 0.2)).to_s
          }
          csv << get_line_from_ir_and_data(accounting_period, ir, data)

          ##  Positive amount of 20% on account 210320
          data = {
            account_nb: '210320',
            currency: ir.invoice_currency,
            value: (ir.requested_amount * 0.2).to_s
          }
          csv << get_line_from_ir_and_data(accounting_period, ir, data)
          ## Positive amount of the full amount to account 620130, with cost center MOOP, zz and TVA Tax code PEU
          data = {
            account_nb: '620130',
            currency: ir.invoice_currency,
            value: ir.requested_amount.to_s,
            cost_center_code: 'MOOP',
            zz: 'ZZ',
            tva_tax_code: 'PEU'
          }
          csv << get_line_from_ir_and_data(accounting_period, ir, data)

        elsif examiner.country_code == 'FR'

          if examiner.has_vat
            # FR Examiner, no VAT
            ## Negative amount to the account of the examiner

            data = {
              account_nb: examiner.id.to_s,
              currency: ir.invoice_currency,
              value: (-ir.requested_amount).to_s
            }
            csv << get_line_from_ir_and_data(accounting_period, ir, data)

            ## Positive amount of the VAT on account 210313
            data = {
              account_nb: '210313',
              currency: ir.invoice_currency,
              value: ir.vat_amount.to_s
            }
            csv << get_line_from_ir_and_data(accounting_period, ir, data)

            ## Positive amount of the HT amount to account 620120, with cost center MOOP, zz and TVA Tax code P2000
            data = {
              account_nb: '620120',
              currency: ir.invoice_currency,
              value: (ir.requested_amount - ir.vat_amount).to_s,
              cost_center_code: 'MOOP',
              zz: 'ZZ',
              tva_tax_code: 'P2000'
            }
            csv << get_line_from_ir_and_data(accounting_period, ir, data)

          else
            # FR Examiner, with VAT
            ## Negative total amount to the account of the examiner
            data = {
              account_nb: examiner.id.to_s,
              currency: ir.invoice_currency,
              value: (-ir.requested_amount).to_s
            }
            csv << get_line_from_ir_and_data(accounting_period, ir, data)

            ## Positive amount of the full amount to account 620110, with cost center MOOP, zz and TVA Tax code PEXPT

            data = {
              account_nb: '620110',
              currency: ir.invoice_currency,
              value: ir.requested_amount.to_s,
              cost_center_code: 'MOOP',
              zz: 'ZZ',
              tva_tax_code: 'PEXPT'
            }
            csv << get_line_from_ir_and_data(accounting_period, ir, data)
          end
        else

          # Outside EU Examiner
          ##  Negative amount to the account of the examiner
          data = {
            account_nb: examiner.id.to_s,
            currency: ir.invoice_currency,
            value: (-ir.requested_amount).to_s
          }
          csv << get_line_from_ir_and_data(accounting_period, ir, data)

          ## Positive amount of the full amount to account 620140, with cost center MOOP, zz and TVA Tax code PEXPT
          data = {
            account_nb: '620140',
            currency: ir.invoice_currency,
            value: ir.requested_amount.to_s,
            cost_center_code: 'MOOP',
            zz: 'ZZ',
            tva_tax_code: 'PEXPT'
          }
          csv << get_line_from_ir_and_data(accounting_period, ir, data)

        end
      end
    end
  end

  def self.get_line_from_ir_and_data(period, ir, data)
    columns = {
      account_nb: 0,
      accounting_period: 1,
      invoice_date: 2,
      invoice_number: 3,
      line_description: 4,
      currency: 5,
      value: 6,
      cost_center_code: 10,
      zz: 12,
      tva_tax_code: 13
    }

    generic_data = {
      accounting_period: period,
      invoice_date: ir.invoice_pdf_updated_at.strftime('%m/%d/%Y'),
      invoice_number: ir.examiner_invoice_reference,
      currency: ir.invoice_currency,
      line_description: ir.examiner.name.to_s
    }

    data.merge!(generic_data)

    line = []
    data.each do |key, value|
      next unless columns.has_key?(key)

      index = columns[key]
      line[index] = value
    end

    line
  end

  private

  def convert_to_euro(currency)
    self.total_in_fee_currency *= currency.eur_exchange_rate
  end

  def valid_for_conversion?(currency)
    valid = false
    examiner_fees_currencies = json_examiner_fees.values.flatten.pluck('currency').uniq

    # All examiner fees should be in the same currency
    if examiner_fees_currencies.size > 1
      Alert.system("[InvoiceRequest ##{id}] Examiner fees currencies are not the same: #{examiner_fees_currencies.join(', ')}")
    end

    if currency.code.eql?('EUR')
      # Invoice currency is EUR => No conversion
      unless examiner_fees_currencies.first.eql?('EUR')
        # Examiner fees should be in EUR
        Alert.system("[InvoiceRequest ##{id}] Examiner fees currencies is not in EUR. invoice_currency: #{currency.code} => json_examiner_fees: #{examiner_fees_currencies.join(', ')}")
      end
    elsif currency.code.eql?(examiner_fees_currencies.first)
      # Invoice currency is the same as examiner fees currency => No conversion
      ## If examiner default currency is not the same one, change examiner default currency
      unless examiner.default_currency.eql?(currency.code)
        examiner.default_currency = currency.code
        examiner.save!
      end
    else
      # Invoice currency is not EUR and examiner fees currency is EUR => Conversion
      valid = true
    end
    valid
  end
end
