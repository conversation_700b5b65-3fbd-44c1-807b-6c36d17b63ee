# == Schema Information
#
# Table name: questions
#
#  id             :integer          not null, primary key
#  deleted_at     :datetime
#  label          :string
#  order          :integer
#  question_for   :integer
#  type           :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  challenge_id   :integer
#  layout_zone_id :integer
#
# Indexes
#
#  index_questions_on_challenge_id    (challenge_id)
#  index_questions_on_layout_zone_id  (layout_zone_id)
#

class Questions::QRadioLinkedTextsRecorder < Question
  # ----------------------------------------
  # :section: Hard coded class attributes
  # ----------------------------------------
  # Skills tested by this question
  @tests_reading   = true
  @tests_writing   = false
  @tests_speaking  = false
  @tests_listening = true
  @usable_in       = [:reception]

  # ----------------------------------------
  # :section: Documentation of this Question
  #
  # QuestionElements used by this question:
  #   * QERadioTextRecorder
  #
  # Which ones have a score?
  #   * QERadioTextRecorder
  #
  # Format of Hash necessary to create question_elements:
  # {
  #   text: "a text"
  #   radio_identifier: XX
  # }
  #
  # Example:
  # # {
  # #   text: "A story about a monkey"
  # #   radio_identifier: 1
  # # }
  #
  # You must write the method +build_question_element+ if this
  # question has question_elements. If their creation is complex, can
  # can also overwrite the method +self.build+.
  #
  # You can also implement custom callbacks to be called before
  # or after looping on all QuestionElements:
  #   * before_build_question_elements
  #   * after_build_question_elements
  # ----------------------------------------

  # This MUST return a +unique+ Question element object!
  # This is the method where you want to set the static_valid_answer attributes
  def build_question_element(content, index)
    # This method *MUST* be overridden!

    QuestionElements::QERadioTextRecorder.create({
                                                   question_id: self.id,
                                                   order: index,
                                                   scorable: true,
                                                   content: content
                                                 })
  end

  def build_question_element_default
    # This method *MUST* be overridden!
    # ... and remember to set some static_valid_answer attributes is they aren't in DQD.

    qeR = QuestionElements::QERadioLinkedTextRecorder.create({
                                                               question_id: self.id,
                                                               order: self.question_elements.count,
                                                               scorable: true,
                                                               content: {
                                                                 word: "",
                                                                 qe_radio_linked_text_displayer_id: ""
                                                               }
                                                             })

    Challenge.find(self.challenge_id).questions.where(:question_for => 0).each do |q|
      if %w[Questions:QRadioSecretLinkedTextsDisplayer Questions:QRadioLinkedTextsDisplayer].include?(q.type)
        qeD = QuestionElements::QERadioLinkedTextDisplayer.create({
                                                                    question_id: q.id,
                                                                    order: q.question_elements.count,
                                                                    scorable: false,
                                                                    content: { word: "" }
                                                                  })
        content = { word: "", qe_radio_linked_text_displayer_id: qeD.id.to_s }
        qeR.update_attribute(:content, content)
      end
    end
    return qeR
  end

  # Additional custom callbacks
  # def before_build_question_elements; end
  # def after_build_question_elements; end

  def before_build_question_elements
    # We copy the worwd and also add a reference to the original QE. This is helpful for validation.
    # Specific case, redactors could have used "Texts" but only written words. In that case the creation of the displayer has correclty been transformed, so we have to handle 2 behaviors here

    if displayer.class.name.include("Texts")
      displayer.question_elements.where(type: 'QuestionElements::QERadioLinkedTextDisplayer').each_with_index do |qe, index|
        QuestionElements::QERadioLinkedTextRecorder.create({
                                                             question_id: self.id,
                                                             order: index,
                                                             scorable: true,
                                                             content: {
                                                               word: qe.content['text'],
                                                               qe_radio_linked_text_displayer_id: qe.id
                                                             }
                                                           })
      end
    else
      displayer.question_elements.where(type: 'QuestionElements::QERadioLinkedWordDisplayer').each_with_index do |qe, index|
        QuestionElements::QERadioLinkedWordRecorder.create({
                                                             question_id: self.id,
                                                             order: index,
                                                             scorable: true,
                                                             content: {
                                                               word: qe.content['word'],
                                                               qe_radio_linked_word_displayer_id: qe.id
                                                             }
                                                           })
      end
    end
  end

  # For this type of question, QE are generated automatically from the QE in the production.
  def displayer
    return @displayer if defined? @displayer

    @displayer = begin
      # Displayer are
      # same class name with displayer instead of recorder
      # Class name with "RadioSecret" and displayer instead of recorder
      potential_displayers = [
        "#{self.class.name.gsub('Recorder', 'Displayer')}",
        "#{self.class.name.gsub('Recorder', 'Displayer').gsub('Radio', 'RadioSecret')}",
        "#{self.class.name.gsub('Recorder', 'Displayer').gsub('Texts', 'Words')}",
        "#{self.class.name.gsub('Recorder', 'Displayer').gsub('Radio', 'RadioSecret').gsub('Texts', 'Words')}"
      ]
      displayer = challenge.questions.for(:production).order(:id).find_by(type: potential_displayers)
      raise 'QuestionError. Displayer not found for QRadioLinkedWordsRecorder' unless displayer

      displayer
    end
  end

  # ----------------------------------------
  # :section: Methods overridden from question.rb
  # These are the methods that you will want to override to implement all
  # necessary functionality of your Question.
  # ----------------------------------------

  # Automatically generate DQD for question_elements linked to this question
  # when the question is first created.
  #
  # Important note! If you are going to set the valid answer here, meaning that the
  # valid answer for each question element will be stored in a DQD and not in their
  # static_valid_answer attribute, then you must set it in +dqd.data['valid_answer']+
  #
  # If not, you need to overwrite the method +valid_answer+ for this question element.
  #
  # *returns:* @boolean based on successful generation.
  def generate_dqd!(questionable_object)
    ## Get a displayer, either secret or not
    displayer = self.challenge.questions.find_by(type: "Questions::QRadioLinkedTextsDisplayer")
    displayer = self.challenge.questions.find_by(type: "Questions::QRadioSecretLinkedTextsDisplayer") if selected.nil?
    raise "QRadioLinkedTextsRecorder without any QRadioLinkedTextsDisplayer or QRadioSecretLinkedTextsDisplayer for challenge #{self.challenge.id}" if displayer.nil?

    # Create a dynamic answers hash and fill it with pair of key/value like radio_identifier/selected_status from the production
    dynamic_answers = {}
    qes_displayer = question_elements.for(displayer)
    qes_displayer.each do |qe_displayer|
      dynamic_answers[qe_displayer.radio_identifier] = qe_displayer.selected
    end

    # Set the qe selected status from the dynamic answer hash.
    qes_recorder = question_elements.for(questionable_object)
    qes_recorder.each do |qe_recorder|
      qe_recorder.selected = dynamic_answers[qe_recorder.radio_identifier]
    end
    true
  end

  # Random data for Production
  #
  # This method is used by automation scripts, but also serves as documention purpose.
  # Return a random Hash of data exactly as you would expect it to be sent back from
  # the view, after a user answered the question through the HTML form.
  # def random_production_hash
  #   {}
  # end

  # Random data for Reception
  #
  # This method is used by automation scripts, but also serves as documention purpose.
  # Return a random Hash of data exactly as you would expect it to be sent back from
  # the view, after a user answered the question through the HTML form.
  def random_reception_hash
    {
      # self.id.to_s => self.question_elements[rand(self.question_elements.length)].radio_identifier
    }
  end

  # This method, called for both reception and production questions validates the
  # input of the user, ensuring that the question has been answered correctly.
  #
  # If there are some errors, they should be added to the usual ActiveRecord errors
  # for this question.
  #
  # For example:
  #   * for a text field, check for specific words
  #   * for a text field, check that it's not empy
  #   * for a group of radio buttons/checkboxes, ensure that at least one is checked
  #
  # Expected format of form_data is:
  #   {
  #     <question_element_id> => <user_answer>,
  #     <question_element_id> => <user_answer>,
  #     <question_element_id> => <user_answer>
  #   }
  # or, for radio button questions, simply "<user_answer>"
  #
  # *returns:* nothing.
  def question_validations(form_data_for_question)
    # In a radio recorder we directly get the value of the question here, not a hash
    user_answer = form_data_for_question
    check_if_empty(user_answer)
  end

  # Describe how this question works.
  # Displayed in Question Builder interface.
  #
  # returns:* @string
  # def definition
  #   # It is strongly recommended to override this method.
  #   "-"
  # end

  # Other methods that you might want to override at your own discretion:
  #   - template_name
  #   - template_filename
  #   - validate_consistency
  # (see app/models/question.rb for original implementation)
end
