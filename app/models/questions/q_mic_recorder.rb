# == Schema Information
#
# Table name: questions
#
#  id             :integer          not null, primary key
#  deleted_at     :datetime
#  label          :string
#  order          :integer
#  question_for   :integer
#  type           :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  challenge_id   :integer
#  layout_zone_id :integer
#
# Indexes
#
#  index_questions_on_challenge_id    (challenge_id)
#  index_questions_on_layout_zone_id  (layout_zone_id)
#

class Questions::QMicRecorder < Question
  # ----------------------------------------
  # :section: Hard coded class attributes
  # ----------------------------------------
  # Skills tested by this question
  @tests_reading   = false
  @tests_writing   = false
  @tests_speaking  = true
  @tests_listening = false
  @usable_in       = [:production]

  # ----------------------------------------
  # :section: Documentation of this Question
  #
  # QuestionElements used by this question:
  #   * QEMicRecorder
  #
  # Which ones have a score?
  #   * None
  #
  # Format of Hash necessary to create question_elements:
  # {
  #   max_duration: <an integer>
  # }
  #
  # Example:
  # # {
  # #   max_duration: 20
  # # }
  #
  # You must write the method +build_question_element+ if this
  # question has question_elements. If their creation is complex, can
  # can also overwrite the method +self.build+.
  #
  # You can also implement custom callbacks to be called before
  # or after looping on all QuestionElements:
  #   * before_build_question_elements
  #   * after_build_question_elements
  # ----------------------------------------

  # This MUST return a +unique+ Question element object!
  # This is the method where you want to set the static_valid_answer attributes
  def build_question_element(content, index)
    QuestionElements::QEMicRecorder.create({
                                             question_id: self.id,
                                             order: index,
                                             scorable: false,
                                             content: content
                                           })
  end

  def build_question_element_default
    qe = QuestionElements::QEMicRecorder.create({
                                                  question_id: self.id,
                                                  order: self.question_elements.count,
                                                  scorable: false,
                                                  content: { max_duration: "" }
                                                })
    return qe
  end

  # Additional custom callbacks
  # def before_build_question_elements; end
  # def after_build_question_elements; end

  # ----------------------------------------
  # :section: Methods overridden from question.rb
  # These are the methods that you will want to override to implement all
  # necessary functionality of your Question.
  # ----------------------------------------

  # Automatically generate DQD for question_elements linked to this question
  # when the question is first created.
  #
  # *returns:* @boolean based on successful generation.
  def generate_dqd!(questionable_object)
    # This question does not generate any DQD
    true
  end

  # Random data for Production
  #
  # This method is used by automation scripts, but also serves as documention purpose.
  # Return a random Hash of data exactly as you would expect it to be sent back from
  # the view, after a user answered the question through the HTML form.
  def random_production_hash
    # TODO : Check how we can validate this as the recording does not really exists when the user validates the question (it is being uploaded to S3).
    # We should be able to send temp recording length at least
    {}
  end

  # Random data for Reception
  #
  # This method is used by automation scripts, but also serves as documention purpose.
  # Return a random Hash of data exactly as you would expect it to be sent back from
  # the view, after a user answered the question through the HTML form.
  # def random_reception_hash
  #  {}
  # end

  # This method, called for both reception and production questions validates the
  # input of the user, ensuring that the question has been answered correctly.
  #
  # If there are some errors, they should be added to the usual ActiveRecord errors
  # for this question.
  #
  # For example:
  #   * for a text field, check for specific words
  #   * for a text field, check that it's not empy
  #   * for a group of radio buttons/checkboxes, ensure that at least one is checked
  #
  # Expected format of form_data is:
  #   {
  #     <question_element_id> => <user_answer>,
  #     <question_element_id> => <user_answer>,
  #     <question_element_id> => <user_answer>
  #   }
  # or, for radio button questions, simply "<user_answer>"
  #
  # *returns:* nothing.
  def question_validations(form_data, questionable = nil)
    qe_mic_recorder = question_elements.order(:id).find_by(type: 'QuestionElements::QEMicRecorder')
    if qe_mic_recorder
      user_answer = form_data[qe_mic_recorder.id.to_s]
      check_if_empty(user_answer)
    end
  end

  # Describe how this question works.
  # Displayed in Question Builder interface.
  #
  # returns:* @string
  # def definition
  #   # It is strongly recommended to override this method.
  #   "-"
  # end
  def max_duration
    qe_mic_recorder = question_elements.order(:id).find_by(type: 'QuestionElements::QEMicRecorder')
    qe_mic_recorder.max_duration
  end

  # Other methods that you might want to override at your own discretion:
  #   - template_name
  #   - template_filename
  #   - validate_consistency
  # (see app/models/question.rb for original implementation)
end
