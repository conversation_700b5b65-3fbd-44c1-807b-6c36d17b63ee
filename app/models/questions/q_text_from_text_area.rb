# == Schema Information
#
# Table name: questions
#
#  id             :integer          not null, primary key
#  deleted_at     :datetime
#  label          :string
#  order          :integer
#  question_for   :integer
#  type           :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  challenge_id   :integer
#  layout_zone_id :integer
#
# Indexes
#
#  index_questions_on_challenge_id    (challenge_id)
#  index_questions_on_layout_zone_id  (layout_zone_id)
#

class Questions::QTextFromTextArea < Question
  # ----------------------------------------
  # :section: Hard coded class attributes
  # ----------------------------------------
  # Skills tested by this question
  @tests_reading   = true
  @tests_writing   = false
  @tests_speaking  = false
  @tests_listening = false
  @usable_in       = [:reception]

  # ----------------------------------------
  # :section: Documentation of this Question
  #
  # QuestionElements used by this question:
  #   * QEText
  #
  # Which ones have a score?
  #   * none
  #
  # Format of Hash necessary to create question_elements:
  # {
  #   text: string,
  #   style: string
  # }
  #
  # Example:
  # # {
  # #   text: "This is a text about a story",
  # #   style: "h1"
  # # }
  #
  # You must write the method +build_question_element+ if this
  # question has question_elements. If their creation is complex, can
  # can also overwrite the method +self.build+.
  #
  # You can also implement custom callbacks to be called before
  # or after looping on all QuestionElements:
  #   * before_build_question_elements
  #   * after_build_question_elements
  # ----------------------------------------

  # This MUST return a +unique+ Question element object!
  # This is the method where you want to set the static_valid_answer attributes
  def build_question_element(content, index)
    if content.nil? || content[:text].nil?
      content = { text: '', style: 'p' }
    end

    if content[:style].nil?
      content[:style] = 'p'
    end

    QuestionElements::QEText.create({
                                      question_id: self.id,
                                      order: index,
                                      scorable: false,
                                      content: content
                                    })
  end

  def build_question_element_default
    qe = QuestionElements::QEText.create({
                                           question_id: self.id,
                                           order: self.question_elements.count,
                                           scorable: false,
                                           content: {}
                                         })
    return qe
  end

  # Additional custom callbacks
  # def before_build_question_elements; end

  # Ensure that the QEText object always exists.
  def after_build_question_elements
    if self.question_elements.count == 0
      QuestionElements::QEText.create({
                                        question_id: self.id,
                                        order: 0,
                                        scorable: false,
                                        content: { text: '', style: 'p' }
                                      })
    end
  end

  # ----------------------------------------
  # :section: Methods overridden from question.rb
  # These are the methods that you will want to override to implement all
  # necessary functionality of your Question.
  # ----------------------------------------

  # Automatically generate DQD for question_elements linked to this question
  # when the question is first created.
  #
  # Important note! If you are going to set the valid answer here, meaning that the
  # valid answer for each question element will be stored in a DQD and not in their
  # static_valid_answer attribute, then you must set it in +dqd.data['valid_answer']+
  #
  # If not, you need to overwrite the method +valid_answer+ for this question element.
  #
  # *returns:* @boolean based on successful generation.
  # def generate_dqd!(questionable_object)
  #   true
  # end

  # Random data for Production
  #
  # This method is used by automation scripts, but also serves as documention purpose.
  # Return a random Hash of data exactly as you would expect it to be sent back from
  # the view, after a user answered the question through the HTML form.
  # def random_production_hash
  #   {}
  # end

  # Random data for Reception
  #
  # This method is used by automation scripts, but also serves as documention purpose.
  # Return a random Hash of data exactly as you would expect it to be sent back from
  # the view, after a user answered the question through the HTML form.
  # def random_reception_hash
  #   {}
  # end

  # This method, called for both reception and production questions validates the
  # input of the user, ensuring that the question has been answered correctly.
  #
  # If there are some errors, they should be added to the usual ActiveRecord errors
  # for this question.
  #
  # For example:
  #   * for a text field, check for specific words
  #   * for a text field, check that it's not empy
  #   * for a group of radio buttons/checkboxes, ensure that at least one is checked
  #
  # Expected format of form_data is:
  #   {
  #     <question_element_id> => <user_answer>,
  #     <question_element_id> => <user_answer>,
  #     <question_element_id> => <user_answer>
  #   }
  # or, for radio button questions, simply "<user_answer>"
  #
  # *returns:* nothing.
  # def question_validations(form_data, questionable=nil)
  #   # This method *MUST* be overridden!
  #   true
  # end

  # Describe how this question works.
  # Displayed in Question Builder interface.
  #
  # returns:* @string
  # def definition
  #   # It is strongly recommended to override this method.
  #   "-"
  # end

  # Other methods that you might want to override at your own discretion:
  #   - template_name
  #   - template_filename
  #   - validate_consistency
  # (see app/models/question.rb for original implementation)
end
