# == Schema Information
#
# Table name: evaluation_delays
#
#  id                      :integer          not null, primary key
#  delay_label             :string
#  graded_time_limit       :integer
#  payement_rate           :float
#  soon_overdue_time_limit :integer
#  created_at              :datetime
#  updated_at              :datetime
#

class EvaluationDelay < ApplicationRecord
  ############### Model bindings ###############
  has_many :evaluation

  ############### Model validation fileds ###############
  validates :delay_label, presence: true, uniqueness: true
  validates :graded_time_limit, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :soon_overdue_time_limit, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :payement_rate, presence: true, numericality: { greater_than_or_equal_to: 0 }

  ############### Model callBacks ###############
  before_validation :check_validate_params
  before_destroy :update_evaluations

  DEFAULT = "default"

  def self.get_default
    find_by(delay_label: EvaluationDelay::DEFAULT) || create_default
  end

  def self.create_default
    EvaluationDelay.create!(
      delay_label: EvaluationDelay::DEFAULT,
      graded_time_limit: 24,
      soon_overdue_time_limit: 24,
      payement_rate: 10.0
    )
  end

  def update_evaluations
    self.evaluation.update_all(evaluation_delay: EvaluationDelay.get_default)
  end

  private

  def check_validate_params
    return unless self.graded_time_limit < self.soon_overdue_time_limit

    self.errors.add(:graded_time_limit, '"Graded time limit" must be greater than the "Soon overdue limit"')
  end
end
