<div class="navbar navbar-default navbar-fixed-bottom" role="navigation" id="admin_footer">
  <div class="container">
    <div class="row">
      <div class="col-xs-2">
        <h4> > Admin footer</h4>
      </div>
      <div class="current_question_info col-xs-4">
        Challenge infos
        <ul>
          <li>Challenge id : <span id="admin_footer_challenge_linguist_id"></span>(<span id="admin_footer_challenge_id"></span>)</li>
          <li>Status : <span id="admin_footer_challenge_status"></span></li>
          <li>Question type : <span id="admin_footer_question_type"></span></li>
          <li>Question id : <span id="admin_footer_question_id"></span></li>
        </ul>
      </div>


      <div class="col-xs-2">
        <button type="button" class="btn btn-default" onclick="Pipplet.Test.timerStop();">
          Stop Timer
        </button>
      </div>

      <div class="col-xs-2">
        <button type="button" class="btn btn-default" onclick="Pipplet.Test.currentQuestion.skipQuestion('next_question');">
          Skip & continue
        </button>
      </div>
      <div class="col-xs-2">
        <button type="button" class="btn btn-default" onclick="Pipplet.Test.currentQuestion.skipQuestion('end_test');">
          Skip & exit
        </button>
        <button type="button" class="btn btn-default" onclick="Pipplet.Test.admin.updateNextQuestionables(this);">
          Update Next Questionables
        </button>
      </div>

            <!--
      <div class="challenge_selection col-xs-4">
        Challenge selection
        <form class="form-inline">
          <%=select_tag "challenge_selector", options_for_select(Challenge.all.collect{ |c| ["#{c.linguist_id} (#{c.id})", c.id] }.sort_by{|c| c[1]}), class: 'form-control'%>
          <button type="button" class="btn btn-default" id="change_challenge_link">Select</button>
          <button type="button" class="btn btn-default" id="next_challenge_link">Next</button>
        </form>
      </div>
      --!>

    </div>
</div>
</div>
