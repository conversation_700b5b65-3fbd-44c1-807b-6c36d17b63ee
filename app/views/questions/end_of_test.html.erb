<%
  hide_end_of_test_forms = @last_test_instance.nil? || @last_test_instance.test_mode == true || (current_brand && current_brand.hide_end_of_test_forms? == true)
%>

<!-- This is the holder for the proper End Test page => No more questions are required to the user
<div class="alert alert-success" role="alert" id="thankyou" style="display:none"><%= t('survey.thankyou') %></div>
<div class="alert alert-success" role="alert" id="thankyou_shareyes" style="display:none"><%= t('survey.thankyou_shareyes') %></div>
<div class="alert alert-success" role="alert" id="thankyou_shareno" style="display:none"><%= t('survey.thankyou_shareno') %></div>
-->
<div id="endtest_holder" class="spacer-40 main-content">
  <% if current_user.test_instances.not_completed.empty? %>
    <% if current_user.redirection_url %>
      <div id="endtest_empty_ti_holder">
        <% url = current_user.redirection_url %>
        <h2><%= t(get_branded_raw_key('end_test.thank_you')) %></h2>
        <p><%= t('end_test.redirection_step', client: current_user.client_name) %></p>
        <%= image_tag 'High-five-rafiki.svg', size: '350' %>
        <div class="btn-group" role="group" aria-label="...">
          <a class="button primary btn-spacer spacer" id="survey_button" href="<%= t('end_test.audition_survey_url') %>" onclick="<%= t('end_test.audition_survey_url') != '#' ? '' : 'showSurvey()' %>"><%= t('end_test.go_to_survey') %></a>
        </div>
        <div class="btn-group" role="group">
          <a href="<%= url %>" class="button secondary btn-spacer spacer">
            <%= t('user.continue') %>
          </a>
        </div>
        <div class="spacer-20"></div>
      </div>
    <% elsif secure_browser? %>
      <div id="endtest_empty_ti_holder">
        <h2><%= t('secure_browser.end_of_test.title') %></h2>
        <h3><%= t('secure_browser.end_of_test.subtitle') %></h3>
        <%= image_tag 'High-five-rafiki.svg', size: '350' %>
        <p>
          <%= t('secure_browser.end_of_test.description') %>
        </p>
      </div>
    <% else %>
      <div id="endtest_empty_ti_holder">
        <h2><%= t(get_branded_raw_key('end_test.thank_you')) %></h2>
        <p><%= t('end_test.next_step') %></p>
        <%= image_tag 'High-five-rafiki.svg', size: '350' %>
        <% if @last_test_instance&.direct_user&.candidate_practice? %>
          <div class="practice-pack-boost">
            <h3>
              <%= t('practice_pack.boost.title') %>
            </h3>
            <div>
              <p>
                <%= sanitize t('practice_pack.boost.description') %>
              </p>
              <%= link_to t('practice_pack.boost.cta'), "https://www.pipplet.com/#{'fr/' if I18n.locale == :fr}your-practice-pack", target: '_blank', class: 'button secondary' %>
            </div>
          </div>
        <% end %>

        <% unless hide_end_of_test_forms %>
          <div id="survey_intro">
            <p><%= t('end_test.survey_1') %> <%= t('end_test.survey_2') %></p>

            <div class="btn-group" role="group" aria-label="...">
              <a class="button primary btn-spacer spacer" id="survey_button" href="<%= t('end_test.audition_survey_url') %>" onclick="<%= t('end_test.audition_survey_url') != '#' ? '' : 'showSurvey()' %>"><%= t('end_test.go_to_survey') %></a>
            </div>

            <div class="spacer-20"></div>
          </div>
        <% end %>
      </div>
    <% end %>

  <% else %>
    <div class="spacer-20">
      <h2><%= t('end_test.next_test_title') %></h2>
      <p><%= t('end_test.next_test_copy', prev_test_language: I18n.t('languages.' + @last_test_instance.test_language), next_test_language: I18n.t('languages.' + @next_test.test_language)) %></p>
      <div class='v-space-4'>
        <%= render partial: 'users/tests_display', locals: { all_test_instances_to_display: @current_user.test_instances.not_completed } %>
      </div>
    </div>
  <% end %>

  <% if current_user.redirection_url %>
    <% url = current_user.redirection_url %>
    <div style="display: none" id="redirection_url">
      <h2><%= t(get_branded_raw_key('end_test.thank_you')) %></h2>
      <p><%= t('end_test.redirection_step', client: current_user.client_name) %></p>
      <div class="btn-group" role="group">
        <a href="<%= url %>" class="button primary btn-spacer spacer">
          <%= t('user.continue') %>
        </a>
      </div>
      <div class="spacer-20"></div>
    </div>
  <% end %>

  <% if @last_test_instance && !secure_browser? %>
    <% unless hide_end_of_test_forms %>
      <p><%= t('end_test.technical_report_copy') %></p>
      <div class="btn-group" role="group" aria-label="...">
        <a target="_blank" class="button secondary btn-spacer spacer" id="technical_report_button" href='<%= t("end_test.technical_report_url") %>&entry.1680372141=<%= @last_test_instance.id %>&entry.1943340736=<%= current_user.email %>'><%= t('end_test.go_to_technical_report') %></a>
      </div>
    <% end %>

    <% if @last_test_instance.test_profile_id == 104 %>
      <h2><%= t('end_test.certification_title') %></h2>
      <div class="row" style="height: 100%;">
        <div class="col-md-2">
          <img id=" logo_flex" style="height: 140px;margin-top: -20px;margin-right: -10px;" src="https://pipplet-test-dev.s3.amazonaws.com/brands/original/31557b0d58612b9393c13a703b7823d3.?1582035084">
        </div>
        <div class="col-md-10" style="height: 100%;">
          <%= t('end_test.certification_text') %>
          <a style="color: #0097A7" href="https://cdn.pipplet.com/documents/Flyer+apprenants+FLEX.pdf"><%= t('end_test.certification.link') %></a>
        </div>
      </div>
    <% end %>

  <% end %>
</div>
