<div id="endtest_holder">
<div class="row">

  <% #If the current user has no productions, it means it is his first time here, we have a special message %>
  <% if current_user.productions.empty? %>
    <p class="alert"><%= t('no_more_questions.new_user_sorry') %></p>
    <p><%= t('no_more_questions.contact') %></p>

  <% #If not, this is a bug %>
  <% else %>
    <p class="alert"><%= t('no_more_questions.sorry') %></p>
    <p><%= t('no_more_questions.contact') %></p>
  <% end %>

  <div class="row">
    <div class="col-xs-8 answer-button">
      <a href="/welcome" class="button secondary"><%= t('no_more_questions.back_home') %></a>
    </div>
  </div>

</div>
</div>
