<div id="accept_cgu" class="main-content" data-controller="registrations">
  <h2><%= t('devise.confirmations.hello', name: current_user.first_name) %></h2>
  <p class='text-justify'><%= raw t('home.intro') %></p>
  <%= form_for(current_user, url: terms_and_policies_path) do |f| %>
    <%= f.hidden_field :understands_cgu, value: true %>
    <%= f.hidden_field :uuid, value: @uuid %>
    <div class="text-left fw-normal">
      <% if current_user.requires_ai_consent? %>
        <label class="required">
          <%= f.check_box :currently_granting_ai_consent,
                          id: 'currently-granting-ai-consent',
                          class: 'align-top',
                          data: {
                            registrations_target: 'input',
                            action: 'input->registrations#toggleSubmit'
                          }
          %>
          <span class='left-space-2 top-space-1 font-weight-normal font-italic'><%= t('grants_ai_consent') %></span>
        </label>
      <% end %>
      <% if current_brand == Brand.default_brand %>
        <label class="space-bottom-0">
          <%= f.check_box :accepts_marketing_contacts, class: 'align-top' %>
          <span class='left-space-2 font-weight-normal font-italic'><%= t('accepts_marketing_contacts') %></span>
        </label>
      <% end %>
    </div>
    <%= f.button t('devise.confirmations.accept_cgu'), class: 'button primary v-space-4', type: 'submit', disabled: current_user.requires_ai_consent?, data: { test: 'confirmations', registrations_target: 'submit' } %>
  <% end %>
  <div class="text-left"><%= raw t('terms_of_use_privacy_policy_message') %></div>
  <%= image_tag 'Welcome-rafiki.svg', size: '350' %>
</div>
