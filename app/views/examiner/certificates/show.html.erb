<div>
  <h2> Please confirm your grades: </h2>

  <div style='margin-bottom: 20px;'>
    <p> Overall scores  are calculated based on your grades for each criterion. </p>
    <% if @certificate.grades.find_by(label: "academic").present? %>
      <p>“Academic” is an average calculated on the basis of the Grammar and Vocabulary scores.</p>
    <% end %>
  </div>

  <ul class="list-group row">
    <div class="col-xs-10 col-sm-6 col-md-4">

    <% grade = @certificate.grades.find_by(label: "overall") %>
    <% if grade.present? %>
      <div style='margin-bottom: 20px;'>
        <li class="list-group-item" style='margin-bottom: 20px;'>
          <div class="row">

            <div class="col-xs-5 col-sm-5">
              <b><%= "Overall:" %></b>
            </div>

            <div class="col-xs-7 col-sm-7" style="text-align: end">
              <%= grade.score == 0 ? "No answers provided" : "#{grade.cecrl_score}" %>
            </div>

          </div>
        </li>
      <div>
    <% end %>

    <div style='margin-bottom: 20px;'>
      <% @main_grades_order.each do |grade_info| %>
        <% printable_label = grade_info.keys[0] %>
        <% grade_label =  grade_info.values[0] %>

        <% grade = @certificate.grades.find_by(label: grade_label) %>

        <% if grade.present? %>
          <li class="list-group-item">
            <div class="row">

              <div class="col-xs-5 col-sm-5">
                <%= "#{printable_label}:" %>
              </div>

              <div class="col-xs-7 col-sm-7" style="text-align: end; maring-right: 10px">
                <%= grade.score == 0 ? "No answers provided" : "#{grade.cecrl_score}" %>
              </div>

            </div>
          </li>
        <% end %>
      <% end %>
    </div>

    <div>
      <% @sub_grades_order.each do |grade_info| %>
        <% printable_label = grade_info.keys[0] %>
        <% grade_label =  grade_info.values[0] %>

        <% grade = @certificate.grades.find_by(label: grade_label) %>

        <% if grade.present? %>
          <li class="list-group-item">
            <div class="row">

              <div class="col-xs-5 col-sm-5">
                <%= "#{printable_label}:" %>
              </div>

              <div class="col-xs-7 col-sm-7" style="text-align: end">
                <%= grade.score == 0 ? "No answers provided" : "#{grade.cecrl_score}" %>
              </div>

            </div>
          </li>
        <% end %>
      <% end %>
    </div>

    </div>
  </ul>


  <div class="btn-group col-md" role="group" style="margin-top: 20px; display: block">
    <%= link_to "Go back to assessment", examiner_evaluation_path(@evaluation.id), class: 'btn btn-default', type: 'button' %>
    <%= button_to "Confirm",  submit_assessment_examiner_evaluation_path(@evaluation), class: 'btn btn-primary spacer-left-15', type: "button" %>
  </div>

</div>
