<tr>
  <td> <%=  evaluation.test_instance_id %> </td>

  <td> <%= evaluation.assessment_type&.name %> </td>

  <% ['overall', 'spoken', 'written'].each do |skill| %>
    <% grade = evaluation.certificate&.grades&.find {|g| g.label == skill } %>
    <%# If grade is not present in this evaluation, just display blank columns %>
    <% if grade&.cecrl_score.blank? %>
      <td></td><td></td><td></td>
      <% next %>
    <% end %>
    <% av_grade = evaluation.test_instance.average_grades.find {|g| g.label == skill } %>
    <% sub_lvl_diff = av_grade&.cecrl_score.blank? ? -1 : (CECRL_LEVELS_VALUES[:cecrl].find_index { |v| v ==  grade&.cecrl_score } - CECRL_LEVELS_VALUES[:cecrl].find_index { |v| v ==  av_grade&.cecrl_score }).abs %>

    <% if sub_lvl_diff == 0 %>
      <td style="background-color: #A4CC85;">
    <% elsif sub_lvl_diff >= 2 %>
      <td style="background-color: #fed8b1;">
    <% else %>
      <td>
    <% end %>
      <%= grade&.cecrl_score || "NA"%>
    </td>

    <td>
      <%= av_grade&.cecrl_score || "NA" %>
    </td>

    <td> <%= sub_lvl_diff >= 0 ? sub_lvl_diff : "NA" %> </td>
  <% end %>

  <td>
    <b> <%= link_to "Go to evaluation", examiner_evaluation_path(evaluation, beta: true, only_show: true), target: "_blank"%> </b>
  </td>

    <td>
    <%= link_to "Give feedback", evaluation.get_gform_review_link, target: "_blank" %>
  </td>

  <td>
    <% if not evaluation.presented_for_review %>
      <i class="glyphicon glyphicon-ok" style="color: green; margin-left: 3px; float: left; margin-right: 5px"></i>
    <% end %>
  </td>

</tr>
