<% instructions = production.ordered_questions_for_zone(:instructions).first -%>
<% if instructions  -%>
	<%= render template: instructions.template_name, locals: {question: instructions, production_or_reception: production} %>
<% end %>
<% production.ordered_questions_for_zone(:main).each do |question| -%>
	<%= render partial: '/questions/question_validation_error', locals: {question_id: question.id, questionable: production} %>
	<%= render template: question.template_name, locals: {f: nil, question: question, questionable: production} %>
<% end -%>