<% time_sec_used = production.time_to_answer.round(1) %>
<% time_min_used = Time.at(time_sec_used).utc.strftime("%M:%S") %>

<% time_sec_gave = production.duration.round(1) / 1000 %>
<% time_min_gave = Time.at(time_sec_gave).utc.strftime("%M:%S") %>

<% if production.timeout? %>
	<%= t('question.info_timeout', {time_min_used: time_min_used, time_min_gave: time_min_gave }).html_safe %>
<% else %>
		<%= t('question.info', {time_min_used: time_min_used, time_min_gave: time_min_gave }).html_safe %>
<% end %>

<% if production.has_text_production? and production.text_production_q_element.present? %>
	<% charact_count = production.text_length %>
	<% minimum_length = I18n.with_locale(test_instance.test_language) do %>
	  <% I18n.t("challenges.#{production.challenge.linguist_id}.minimum_length").to_i == 0 ? production.text_production_q_element.minimum_length(test_instance.test_language).to_i : I18n.t("challenges.#{production.challenge.linguist_id}.minimum_length").to_i %>
	<% end %>
	<% percentage = (charact_count.to_f / minimum_length.to_f) * 100 %>
	<% percentage = percentage > 100 ? 100 : percentage.to_i %>
	<p> The candidate answered <b style=<%= percentage < 50 ? 'color:orange' : '' %>> <%= percentage %>% </b> of the minimum number of required characters. </p>
<% end %>

<% if production.skipped? && production.text_production.blank? && production.audio_production.blank? %>
	<%= t('question.skipped_by_candidate').html_safe %>
<% end %>
