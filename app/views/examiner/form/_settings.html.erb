<%= form_section('Unavailable dates') %>

<p class='col-sm-12 spacer-bottom-20'> You will not receive any evaluations between the times entered below.
If you will be unavailable for a week or more, for a vacation or other life event, please consider letting us know as far in advance as possible. Please keep in mind that the time zone that applies to this is CET (Central European Time), i. e. UTC+1.</p>

<p class='col-sm-12 spacer-bottom-20'> Adding new dates will overwrite previous entries. </p>

<div class="form-group">
  <%= form_date_field(f, :next_unavailable_start_date, t('activerecord.attributes.examiner.next_unavailable_start_date'), "col-sm-4 control-label", "col-sm-7 form-control") %>
</div>

<div class="form-group">
  <%= form_date_field(f, :next_unavailable_end_date, t('activerecord.attributes.examiner.next_unavailable_end_date'), "col-sm-4 control-label", "col-sm-7 form-control") %>
</div>

<div class="form-group">
  <div class="col-sm-offset-4 col-sm-7">
    <%= f.submit "Update my availabilities", class: 'btn btn-primary' %>
  </div>
</div>
