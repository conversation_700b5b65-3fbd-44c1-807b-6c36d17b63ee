<% if !question_element.text.nil? && !question_element.text.empty? && i18n_set?('localised_qe_text.'+question_element.text) %>
  <%= raw "<#{question_element.style} class='#{rtl_class}'>" %>
  <%= raw translate_to_test_language("localised_qe_text.#{question_element.text}")%>
  <%= raw "</#{question_element.style}>" %>
<% else %>
  <% if defined?(questionable) && questionable -%>
    <%= question_element.text(questionable) %>
  <% else -%>
    <%= simple_format(question_element.text, {}, wrapper_tag: question_element.style) %>
  <% end -%>
<% end %>