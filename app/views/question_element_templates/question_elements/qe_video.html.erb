<% if !question_element.url.nil? && !question_element.url.empty? && i18n_set?('localised_qe_video_content.'+question_element.url) %>
  <% url = translate_to_test_language("localised_qe_video_content.#{question_element.url}")%>
<% else %>
  <% url = question_element.url -%>
<% end %>

<video width="80%" controls>
<source src="<%= url %>">
</video>
<br>
<% if !question_element.content["credit"].nil? %>
    <label class="text-muted credit"> <%=raw question_element.content["credit"]%> </label>
<% end %>