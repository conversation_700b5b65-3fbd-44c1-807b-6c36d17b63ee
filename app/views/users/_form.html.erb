<% if @user.errors.any? %>
  <div id="error_explanation">
    <h2><%= @user.errors.count > 1 ? I18n.t('errors.template.header.other') : I18n.t('errors.template.header.one') %></h2>

    <ul>
    <% @user.errors.full_messages.each do |msg| %>
      <li><%= msg %></li>
    <% end %>
    </ul>
  </div>
<% end %>

  <div class="form-group">
    <%= f.label :first_name, :class => 'col-sm-4 control-label' %>
    <div class="col-sm-7">
      <%= f.text_field :first_name, :class => 'form-control', :placeholder => t('activerecord.attributes.user.first_name') %>
    </div>
  </div>
  <div class="form-group">
    <%= f.label :last_name, :class => 'col-sm-4 control-label' %>
    <div class="col-sm-7">
      <%= f.text_field :last_name, :class => 'form-control', :placeholder => t('activerecord.attributes.user.last_name') %>
    </div>
  </div>
  <div class="form-group">
    <%= f.label :phone_number, :class => 'col-sm-4 control-label' %>
    <div class="col-sm-7">
      <%= f.telephone_field :phone_number, :class => 'form-control', :placeholder => 'Phone number' %>
    </div>
  </div>

  <div class="form-group">
    <%= f.label :password, t('activerecord.attributes.user.new_password'), :class => 'col-sm-4 control-label' %>
    <div class="col-sm-7">
      <%= f.password_field :password, autocomplete: 'new-password', :class => 'form-control', :placeholder => t('activerecord.attributes.user.new_password_placeholder') %>
    </div>
  </div>
  <div class="form-group">
    <%= f.label :password_confirmation , t('activerecord.attributes.user.new_password_confirmation'), :class => 'col-sm-4 control-label' %>
    <div class="col-sm-7">
      <%= f.password_field :password_confirmation, autocomplete: 'new-password', :class => 'form-control', :placeholder => t('activerecord.attributes.user.new_password_confirmation_placeholder') %>
    </div>
  </div>

  <div class="form-group">
    <div class="col-sm-offset-4 col-sm-7">
      <%= f.submit t('devise.general.update'), :class => 'btn btn-primary' %>
      <%= link_to t('devise.general.back_home'), welcome_path, :class => 'btn btn-link' %>
    </div>
  </div>
