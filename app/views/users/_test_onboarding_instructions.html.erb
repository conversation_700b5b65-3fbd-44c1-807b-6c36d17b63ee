<div id="test_introduction" class="main-content full-width">
  <h2><%= t('audio_test.title') %></h2>
  <div class="grid-onboarding">
    <% if @current_test_instance.need_speaking? %>
      <div class="instruction-card">
        <%= image_tag 'lamp-icon.svg' %>
        <h4><%= t('onboarding.instructions-title.concentration') %></h4>
        <p><%= t('audio_test.quietplace') %></p>
      </div>
      <div class="instruction-card">
        <%= image_tag 'headset-icon.svg' %>
        <h4><%= t('onboarding.instructions-title.audio') %></h4>
        <p><%= t('audio_test.headset') %></p>
      </div>
      <div class="instruction-card">
        <%= image_tag 'speak-icon.svg' %>
        <h4><%= t('onboarding.instructions-title.speak') %></h4>
        <p><%= t('audio_test.loud') %></p>
      </div>
    <% else %>
      <div class="instruction-card">
        <%= image_tag 'lamp-icon.svg' %>
        <h4><%= t('onboarding.instructions-title.concentration') %></h4>
        <p><%= t('audio_test.quietplace_no_speaking') %></p>
      </div>
    <% end %>
    <% unless @current_test_instance.speaking_only? %>
      <div class="instruction-card">
        <%= image_tag 'adapted-keyboard.svg' %>
        <h4><%= t('onboarding.instructions-title.keyboard') %></h4>
        <p><%= raw t('audio_test.adapted_keyboard') %></p>
      </div>
    <% end %>
  </div>
  <div class='flex'>
    <button class="button primary v-space-4 margin-auto-left" data-test="tips_continue" id="go_tips_to_succeed"><%= t('general.continue') %></button>
  </div>
</div>
