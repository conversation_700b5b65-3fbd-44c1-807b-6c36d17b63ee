<div id="test_tips" class="main-content full-width">
  <h2><%= t('test.tips.security_guidelines.title') %></h2>
  <p><%= t('test.tips.security_guidelines.description').html_safe %></p>
  <div class="grid-onboarding">
    <div class="instruction-card">
      <%= image_tag 'desk.svg' %>
      <h4><%= t('test.tips.security_guidelines.block_1.title') %></h4>
      <p><%= t('test.tips.security_guidelines.block_1.description').html_safe %></p>
    </div>
    <div class="instruction-card">
      <%= image_tag 'computer-screen.svg' %>
      <h4><%= t('test.tips.security_guidelines.block_2.title') %></h4>
      <p><%= t('test.tips.security_guidelines.block_2.description') %></p>
    </div>
    <% unless @current_test_instance.speaking_only? %>
      <div class="instruction-card">
        <%= image_tag 'writing.svg' %>
        <h4><%= t('test.tips.security_guidelines.block_3.title') %></h4>
        <p><%= t('test.tips.security_guidelines.block_3.description') %></p>
      </div>
      <div class="instruction-card">
        <%= image_tag 'check.svg' %>
        <h4><%= t('test.tips.security_guidelines.block_4.title') %></h4>
        <p><%= t('test.tips.security_guidelines.block_4.description') %></p>
      </div>
    <% end %>
  </div>

  <div class="flex">
    <% if defined?(current_user) && @current_test_instance.keyboard_check? %>
      <button class="button primary v-space-4 margin-auto-left" data-test="tips_continue" id="start_keyboard_test"><%= t('general.continue') %></button>
    <% elsif defined?(current_user) && @current_test_instance && @current_test_instance.perform_identity_check? %>
      <button class="button primary v-space-4 margin-auto-left" data-test="tips_continue" id="start_check_camera"><%= t('audio_test.start_test') %></button>
    <% else %>
      <% if @current_test_instance.need_speaking? %>
        <button class="button primary v-space-4 margin-auto-left" data-test="tips_continue" id="start_audio_test" type="button"><%= t('audio.start_audio_test') %></button>
      <% else %>
        <button class="button primary v-space-4 margin-auto-left" data-test="tips_continue" id="start_tutorial_no_audio_test" type="button"><%= t('audio_test.start_test') %></button>
      <% end %>
    <% end %>
  </div>
</div>
