<div id="test_tips" class="main-content full-width">
  <h2 ><%= t('test.tips.succeed.title') %></h2>
  <div class="grid-onboarding">
    <% if @current_test_instance.need_speaking? %>
      <div class="instruction-card">
        <%= image_tag 'worksheet_icon.svg' %>
        <h4><%= t('onboarding.tips.plan') %></h4>
        <p><%= t('test.tips.succeed.plan') %></p>
      </div>
    <% end %>
    <div class="instruction-card">
      <%= image_tag 'arrows_icon.svg' %>
      <h4><%= t('onboarding.tips.elaborate') %></h4>
      <p><%= t('test.tips.succeed.detail') %></p>
    </div>
    <% unless @current_test_instance.speaking_only? %>
      <div class="instruction-card">
        <%= image_tag 'puzzle_icon.svg' %>
        <h4><%= t('onboarding.tips.structure') %></h4>
        <p><%= t('test.tips.succeed.structure') %></p>
      </div>
      <div class="instruction-card">
        <%= image_tag 'search_icon.svg' %>
        <h4><%= t('onboarding.tips.review') %></h4>
        <p><%= t('test.tips.succeed.re_read') %></p>
      </div>
    <% end %>
  </div>

  <div class="flex">
    <button class="button primary v-space-4 margin-auto-left" data-test="tips_continue" id="start_security_guidelines" type="button"><%= t('audio.start_audio_test') %></button>
  </div>
</div>
