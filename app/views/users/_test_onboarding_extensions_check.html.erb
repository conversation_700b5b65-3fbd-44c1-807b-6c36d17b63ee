<div id="extensions_check"
     style="display: none;"
     class='main-content'
     data-controller="extensions-page"
     data-extensions-page-target="page"
     data-extensions-page-config-value="<%= Rails.application.config.proctoring[:js].to_json %>">
  <h2><%= t('test.extensions_check.title') %></h2>
  <span class="text-left">
    <p><%= t('test.extensions_check.disclamer') %></p>
    <h3><%= t('test.extensions_check.detected') %></h3>
    <p><%= t('test.extensions_check.disable') %></p>
  </span>
  <div class="flex">
    <% if defined?(current_user) && @current_test_instance.keyboard_check? %>
      <button class="button primary v-space-4 margin-auto-left" id="start_keyboard_test_extensions"><%= t('general.continue') %></button>
    <% elsif defined?(current_user) && @current_test_instance && @current_test_instance.perform_identity_check? %>
      <button class="button primary v-space-4 margin-auto-left" id="start_check_camera_extensions"><%= t('audio_test.start_test') %></button>
    <% else %>
      <% if @current_test_instance.need_speaking? %>
        <button class="button primary v-space-4 margin-auto-left" id="start_audio_test_extensions" type="button"><%= t('audio.start_audio_test') %></button>
      <% else %>
        <button class="button primary v-space-4 margin-auto-left" id="start_tutorial_no_audio_test_extensions" type="button"><%= t('audio_test.start_test') %></button>
      <% end %>
    <% end %>
  </div>
</div>

