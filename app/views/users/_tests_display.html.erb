<% client_name_is_uniq = all_test_instances_to_display.map(&:client_name).uniq.count == 1 %>
<div class='grid-onboarding-tests'>
  <% all_test_instances_to_display.each do |test_instance| %>
    <div class='instruction-card'>
      <h3><%= I18n.t("languages.#{test_instance.test_language.downcase}") %></h3>
      <span class='text-center v-space-4'>
        <% if client_name_is_uniq %>
          <%= I18n.t("test-card.from") %>
          <b><%= test_instance.client_name %></b>
        <% end %>
      </span>
      <% if test_instance.writing_only? %>
        <span class='text-center'><%= I18n.t('onboarding.test_writting') %></span>
      <% elsif test_instance.speaking_only? %>
        <span class='text-center'><%= I18n.t('onboarding.test_speaking') %></span>
      <% else %>
        <span class='text-center'><%= I18n.t('onboarding.test_writting_speaking') %></span>
      <% end %>
      <% if test_instance.cancelled? %>
        <span class='text-center top-space-4'><i><%= I18n.t('test.cancelled', clientname: test_instance.client_name) %></i></span>
      <% elsif test_instance.finished? %>
        <span class='text-center top-space-4 text-success'><%= I18n.t('test.completed') %></span>
      <% else %>
        <%= link_to t('test.button'), set_current_test_instance_users_path(test_instance_id: test_instance.id), { data: { test: "take-test-#{test_instance.id}" }, method: :post, class: 'button primary align-self center test-button top-space-4' } %>
      <% end %>
    </div>
  <% end %>
</div>
