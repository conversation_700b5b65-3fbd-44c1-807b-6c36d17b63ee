<%= form_for(current_user, :url => first_language_info_path,:method => :put, :html => {:class => ''}) do |f| %>
  <% if current_user.errors.any? %>
  <div id="error_explanation">
    <h2><%= current_user.errors.count > 1 ? I18n.t('errors.template.header.other') : I18n.t('errors.template.header.one') %></h2>

    <ul>
    <% current_user.errors.full_messages.each do |msg| %>
      <li><%= msg %></li>
    <% end %>
    </ul>
  </div>

<% end %>


    <div class = "welcome-form-group" id="level-form-group">
      <h2><%= t('language_information.title') %></h2>
      <p><%=t('language_information.level_estimation')%></p>
      <div class="form-inline">
        <div class="form-group">
            <div class="btn-group" data-toggle="buttons">
              <% (1..10).each do |b| %>
                <label class="btn btn-default <%= b == current_user.level_estimation ? 'active' : '' %>">
                  <input type="radio" name="level_estimation" id="level_estimation" autocomplete="off" <%= b == current_user.level_estimation ? 'checked' : '' %> value="<%=b%>" ><%=b%>
                </label>
              <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class = "welcome-form-group" id="test-form-group">
      <p> <%=t('language_information.other_tests')%></p>
      <div class="form-inline">
        <div class="form-group">
          <div class="btn-group" data-toggle="buttons">
            <label class="btn btn-default <%= current_user.previous_tests_status? ? 'active' : '' %>" data-toggle="collapse" data-target="#test_details" >
              <input type="radio" name="previous_tests_status" value='1' <%= current_user.previous_tests_status? ? 'checked' : '' %>> <%=t('general.yes') %>
            </label>
            <label class="btn btn-default <%= !current_user.previous_tests_status? ? 'active' : '' %>" id="previous_tests_off">
              <input type="radio" name="previous_tests_status" value='0' <%= !current_user.previous_tests_status? ? 'checked' : '' %>> <%=t('general.no') %>
            </label>
          </div>
        </div>
      </div>
      </div>
      <div class="welcome-form-group collapse <%= current_user.previous_tests_status? ? 'in' : '' %>" id="test_details">
        <p> <%=t('language_information.other_tests_results') %>  </p>
      <div class="form-inline">
        <div class="form-group">
            <% test_index = 0 %>
            <% current_user.test_scores.each do |ts|%>
              <%= label_tag t('language_information.test_label')%>
              <%= text_field_tag 'tests[test'+test_index.to_s+'[name]]', nil, :autofocus => true, :autocomplete => false, :class => 'test_list form-control short_input', :value => ts.name %>
              <%= label_tag t('language_information.score_label')%>
              <%= text_field_tag 'tests[test'+test_index.to_s+'[score]]', nil, :autofocus => true, :autocomplete => false, :class => 'form-control short_input', :value => ts.score %>
              <%test_index+=1%>
            <% end %>
            <%= label_tag t('language_information.test_label')%>
            <%= text_field_tag 'tests[test'+test_index.to_s+'[name]]', nil, :autofocus => true, :autocomplete => false, :class => 'test_list form-control short_input', :placeholder => t('language_information.test_placeholder') %>
            <%= label_tag t('language_information.score_label')%>
            <%= text_field_tag 'tests[test'+test_index.to_s+'[score]]', nil, :autofocus => true, :autocomplete => false, :class => 'form-control short_input', :placeholder => t('language_information.score_placeholder') %>
        </div>
        <button type="button" class="btn btn-default no_border" aria-label="Plus" id="add_test">
          <span class="glyphicon glyphicon-plus-sign" aria-hidden="true"></span>
        </button>
      </div>
      </div>

  <div class = "welcome-form-group" id="language-form-group">
    <p><%=t('language_information.mother_tongue')%></p>
    <div class="form-inline">
      <% SHORT_LANGUAGES_LIST.each do |l| %>
        <div class="form-group">
          <div class="checkbox">
            <% check_status = current_user.user_languages.where(:name => l.name).length > 0 ? true : false %>
            <label>
            <%= check_box_tag "languages[#{l.name}]", l.name, check_status %>
            <%= t("languages.#{l.iso_639_1}").capitalize %>
            </label>
          </div>
        </div>
      <% end %>
    </div>
    <div class="form-inline">
      <div class="form-group">
        <%= label_tag t('language_information.other')%>
        <%= text_field_tag 'languages[other_language]', nil, :autofocus => true, :class => 'languages_list form-control short_input', :placeholder => t('language_information.other') %>
      </div>
      <button type="button" class="btn btn-default no_border" aria-label="Plus" id="add_language">
          <span class="glyphicon glyphicon-plus-sign" aria-hidden="true"></span>
      </button>
    </div>
  </div>

  <div class="form-group">
    <%= f.submit t('general.continue'), :class => 'btn btn-primary btn-lg', :id => 'validation_language' %>
  </div>

<% end %>

<% # TODO : See if this is a problem to have a new document.ready here, however the functions are really only used on this page, it would be a shame to load that everytime %>

<%= content_tag(:script, nonce: content_security_policy_nonce) do %>
  var nb_test = 0;
  var nb_language = 0;
  $(document).ready(function() {
      //$('.test_list').typeahead({source:<%=raw(LANGUAGE_TESTS_LIST.to_json) %>, autoSelect: true});
      //$('.languages_list').typeahead({source:<%=raw(LanguageList::COMMON_LANGUAGES.collect{|l| I18n.t("languages.#{l.iso_639_1}")}.to_json) %>, autoSelect: true});
      $('#add_test').click(addTest);
      $('#add_language').click(addLanguage);
      $('#validation_language').click(validateLanguage);
      $('#previous_tests_off').click(function(){
        $('#test_details').collapse('hide');
      });
    });
    validateLanguage = function(){
      /*if($("#test_status_yes").prop('checked') == true) {
        if(($("#tests_test0_name").val() == "") || ($("#tests_test0_score").val() == "")) {
          return false;
        }
      }*/
    };

    addTest = function(){
      nb_test = nb_test+1;
      $('#test-form-group').append("<div class='form-inline'><div class='form-group'><input type='text' name='tests[test"+nb_test+"[name]]' id='tests_test"+nb_test+"_name' autofocus='autofocus' class='test_list form-control short_input' placeholder='+'<%=t("language_information.test_placeholder")%>'+'><label for='Score_obtained'><%= t('language_information.score_label') %></label><input type='text' name='tests[test"+nb_test+"[score]]' id='tests_test"+nb_test+"_score' autofocus='autofocus' class='form-control short_input' placeholder='Score'></div><button type='button' class='btn btn-default no_border' aria-label='Plus' id='add_test'><span class='glyphicon glyphicon-plus-sign' aria-hidden='true'></span></button></div>");
      //$('.test_list').typeahead({source:<%=raw(LANGUAGE_TESTS_LIST.to_json) %>, autoSelect: true});
      $(this).remove();
      $('#add_test').click(addTest);
    };

    addLanguage = function(){
      nb_language = nb_language+1;
      $('#language-form-group').append("<div class='form-inline'><div class='form-group'><label for='_'> Other </label><input type='text' name='languages[other_language"+nb_language+"]' id='languages_other_language"+nb_language+"' autofocus='autofocus' class='languages_list form-control short_input' placeholder='Other Language'></div><button type='button' class='btn btn-default no_border' aria-label='Plus' id='add_language'><span class='glyphicon glyphicon-plus-sign' aria-hidden='true'></span></button></div>");
      //$('.languages_list').typeahead({source:<%=raw(LanguageList::COMMON_LANGUAGES.collect{|l| I18n.t("languages.#{l.iso_639_1}")}.to_json) %>, autoSelect: true});
      $(this).remove();
      $('#add_language').click(addLanguage);
    };
<% end %>
