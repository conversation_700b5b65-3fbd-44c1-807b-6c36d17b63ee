<table class="admin_table table">
  <tr>
    <th><%=t("voice_recordings.instruction")%></th>
    <th><%=t("voice_recordings.answer")%></th>
  </tr>
  
<% @questions.each do |q| %>
  <tr>
    <td><%= t("instructions.#{q[:instruction_label]}.label") %>
      <% if !q[:details].empty? %>
        <% if !q[:details][:image].empty? %>
          <div><img src="<%=q[:details][:image]%>"></img></div>
        <% end%>
      <% end %>
    </td>
    <td>
      <% if !q[:recording_url].empty? %>
        <audio controls><source src= <%=q[:recording_url]%> type="audio/ogg"><%= t("voice_recordings.audio_error") %></audio>
        <% else %>
          <%= t("voice_recordings.missing") %>
        <% end %>
    </td>
  </tr>
<% end %>
</table>
