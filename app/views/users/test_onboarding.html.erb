<% if @current_test_instance.need_speaking? %>
  <div id="test_onboarding_authorise_audio">
    <%= render partial: 'test_onboarding_authorise_audio' %>
  </div>
  <div id="test_onboarding_microphone_check">
    <%= render partial: 'test_onboarding_microphone_check' %>
  </div>
<% end %>

<% if @current_test_instance.keyboard_check? %>
  <div id="test_onboarding_keyboard_test">
    <%= render partial: 'test_onboarding_keyboard_check' %>
  </div>
<% end %>

<div id="test_onboarding_extensions_check">
  <%= render partial: 'test_onboarding_extensions_check' %>
</div>

<div id="test_onboarding_tutorial" style="display: none;">
  <%= render partial: 'test_onboarding_tutorial' %>
</div>

<% if @current_test_instance.perform_identity_check? %>
  <%= render partial: 'test_onboarding_camera_check' %>
<% end %>

<div id="test_onboarding_security_guidelines" style="display: none;">
  <%= render partial: 'test_onboarding_security_guidelines' %>
</div>

<div id="test_tips_to_succeed" style="display: none;">
  <%= render partial: 'test_onboarding_tips_to_succeed' %>
</div>

<%= render partial: 'test_onboarding_instructions', locals: {current_test_instance: @current_test_instance} %>

<%= content_tag(:script, nonce: content_security_policy_nonce) do %>
  $(document).ready(function() {
    Pipplet.TestOnboardingBreadCrumbs.transitionTo('notice');
    Pipplet.TestOnboardingBreadCrumbs.show();
  });
<% end %>
