<div id='keyboard_test' style='display:none;' class='main-content full-width'>
  <h2 ><%= I18n.t('test_onboarding.keyboard_check_title', test_langue: I18n.t("languages.#{@current_test_instance.test_language}")) %></h2>
  <h4 ><%= I18n.t('test_onboarding.keyboard_check_subtitle') %></h4>
  <div class='unselectable top-space-6 bottom-space-4'><%= I18n.t('test_onboarding.keyboard_check_content', word: KEYBOARD_CHECK_LANGUAGE[@current_test_instance.test_language]) %></div>
  <input class='align-self center' type='text' id='keyboard_test_input' placeholder='<%= I18n.t('test_onboarding.keyboard_check_placeholder') %>'>
  <div hidden id='valid_msg' class='text-success'><%= I18n.t('test_onboarding.keyboard_check_valid') %></div>
  <div hidden id='error_msg' class='text-danger'><%= I18n.t('test_onboarding.keyboard_check_not_valid') %></div>
  <div class='top-space-4' >
    <% if current_user.language_name ==  'fr' %>
      <a onclick="$crisp.push(['do', 'helpdesk:article:open', ['fr', 'aqa8y1']])">
        <%= I18n.t('test_onboarding.keyboard_install_keyboard') %>
      </a>
    <% else %>
      <a onclick="$crisp.push(['do', 'helpdesk:article:open', ['en-us', '2ljukm']])">
        <%= I18n.t('test_onboarding.keyboard_install_keyboard') %>
      </a>
    <% end %>
  </div>
  <div class='flex'>
    <button id='trigger_validations' class='button primary v-space-3 margin-auto-left'><%= @current_test_instance.need_speaking? ? I18n.t('audio.start_audio_test') : I18n.t('audio_test.start_test') %></button>
  </div>
  <div hidden id='text_to_match'><%= KEYBOARD_CHECK_LANGUAGE[@current_test_instance.test_language] %></div>
  <% if @current_test_instance.perform_identity_check? %>
    <button hidden id="start_check_camera" class='toClick'></button>
  <% else %>
    <% if @current_test_instance.need_speaking? %>
      <button hidden id="start_audio_test" class='toClick'></button>
    <% else %>
      <button hidden id="start_tutorial_no_audio_test" class='toClick'></button>
    <% end %>
  <% end %>
</div>
