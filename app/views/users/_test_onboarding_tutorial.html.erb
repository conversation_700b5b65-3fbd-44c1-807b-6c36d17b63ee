<%= tag :meta, name: 'tutorial_ok_button', content: I18n.translate('tutorial.ok') %>
<%= tag :meta, name: 'tutorial_start_button', content: I18n.translate('tutorial.start') %>
<div class="right-background"></div>
<div class="question-layout-basic tutorial">
  <div class="questionable row">
    <div class="main col-md-6">

      <h1><span id="question-position" class="title"><%= t('tutorial.title') %></span></h1>

      <div data-toggle="popover" data-toggle-order="1" data-title='<%= t('tutorial.welcome_title') %>' data-placement="bottom" data-content="<%= t('tutorial.welcome') %>"></div>
      <div data-popover-for='top-left' data-toggle="popover" data-toggle-order="10" data-placement="bottom" data-content="<%= t('tutorial.no_prev') %>"></div>

      <div data-popover-for="question-position" data-toggle="popover" data-toggle-order="2" data-placement="bottom" data-content="<%= t('tutorial.question_position') %>"></div>

      <div class="question_content">

        <div id="question_data_sample" style="height:100%;width:100%;min-height:300px;min-width:100px;position:relative;">
          <div style="height:20px;width:20px;position:absolute;top:50%;left:20%;" data-toggle="popover" data-placement="" data-content="<%= t('tutorial.question_area') %>" data-toggle-order="3" data-viewport="#question_data_sample"></div>
        </div>
      </div>

      <div data-popover-for="question_timer" data-toggle="popover" data-toggle-order="4" data-placement="bottom" data-content="<%= t('tutorial.timer') %>"></div>
    </div>

    <div class="side col-md-6 center">
      <div class="side-buttons-fixed">
        <div id="audio-player-container">

          <% if current_user.current_test_instance.need_speaking? %>
            <%= tag :input, { disabled: true, type: 'button', value: '', class: 'recording_recorder recorder button-outline-primary', 'data-toggle': 'popover', 'data-toggle-order': '5', 'data-placement': 'right', 'data-content': t('tutorial.record_button'), 'data-container': 'body', id: 'play-button', meta_action: 'play', 'hide-and-show-next': !current_user.current_test_instance.speaking_only? } %>
            <% unless current_user.current_test_instance.speaking_only? %>
              <div id="toHide" data-toggle="popover" data-toggle-order="6" data-placement="top" data-content="<%= t('tutorial.textarea') %>">
                <textarea disabled style="margin-top: 62px;" class="form-control error" placeholder="Please type your answer here." rows="8" data-mincar="50" data-counttextlabel="Number of characters: " autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"></textarea>
                <div class="text-count-wrapper error under_limit">
                  Number of characters:
                  <span class="text-count">0</span>
                </div>
              </div>
            <% end %>
          <% else %>
            <textarea disabled style="margin-top: 62px;" class="form-control error" placeholder="Please type your answer here." rows="8" data-mincar="50" data-counttextlabel="Number of characters: " data-toggle="popover" data-toggle-order="6" data-placement="top" data-content="<%= t('tutorial.textarea') %>" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"></textarea>
            <div class="text-count-wrapper error under_limit">
              Number of characters: <span class="text-count">0</span>
            </div>
          <% end %>

        </div>

        <div class="question_controls form-inline">
          <% unless current_user.current_test_instance.speaking_only? %>
            <div class="control-buttons">
              <%= submit_tag t('question.answer_buttons.submit'), class: 'button primary', 'data-toggle': 'popover', 'data-toggle-order': 7, disabled: true, 'data-placement': 'right', 'data-content': t('tutorial.next'), 'data-container': 'body', id: 'next-question' %>
            </div>
          <% end %>
          <div class="control-progress" data-toggle="popover" data-toggle-order="8" data-placement="top" data-content="<%= t('tutorial.progress') %>" data-container="body">
            <div class="progress">
              <div class="progress-bar progress-bar-primary" style="width: <%= (12.0/30*100).to_s %>%"></div>
            </div>
            <div class="progress-info">
              <%= t('score_information.question') + " 3 / 7" %>
            </div>
          </div>
        </div>
      </div>
      <% if current_user.current_test_instance && current_user.current_test_instance.test_profile.show_skip_button %>
        <div id="skip_button">
          <div data-popover-for="skip-question-link" data-toggle="popover" data-toggle-order="9" data-placement="top" data-content="<%= t("tutorial.skip_button") %>">
          </div>
          <div class="row skip-question-row">
            <a id='skip-question-link' class="button secondary"><%= I18n.t('skip_question.button') %></a>
          </div>
        </div>
      <% end %>

    </div>
  </div>
</div>
