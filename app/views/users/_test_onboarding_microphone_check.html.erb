<%= render partial: 'test_onboarding_modal_no_sound' %>
<%= render partial: 'test_onboarding_modal_bad_sound' %>
<%= render partial: 'test_onboarding_modal_test_failed' %>

<% if !browser.safari? %>
  <audio class="hidden question-audio" controls>
    <source src="https://pippletdotcom.s3.eu-west-1.amazonaws.com/network_check_audio.ogg" type="audio/ogg" />
  </audio>
<% end %>

<div id="audio-test-content" class='main-content' style="display: none;">
  <h2><%= t('audio_test.check') %></h2>
  <ul class='onboarding-list'>
    <li><h2>1</h2><p><%= t('audio_test.click_button') %></p></li>
    <li><h2>2</h2><p><%= t('audio_test.click_button_2') %></p></li>
    <li><h2>3</h2><p><%= t('audio_test.click_play') %></p></li>
  </ul>
  <div class='flex row h-center v-center'>
    <input type="button" class="recording_recorder button-outline-primary" data-test="recording_test" id="mic_test_record_button" meta_action="record" />
    <div id="audio_player" class="collapse">
      <div class="vertical-center">
        <div class="mic-test-icon">
          <svg version="1.1" id="arrow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 256 128" xml:space="preserve">
            <polygon class="arrow" points="245,64 207.6,42.4 207.6,53.2 10,53.2 10,74.8 207.6,74.8 207.6,85.6" />
          </svg>
        </div>
        <audio id="audio-elt" controls style="display: none;" type="audio/ogg"></audio>
        <input type="button" class="recording_player button-outline-primary" data-test="recording_play" id="play-button" meta_action="play" value="" style="display: none;" />
      </div>
    </div>
  </div>
  <div id="bad_audio_alert" class="alert alert-warning collapse">
    <p><%= t('audio_test.test_failed_alert.p') %></p>
    <%= link_to t('audio_test.test_failed_alert.link'), "#", data: {target: '#test_failed', toggle: 'modal'} %>
  </div>
  <div id="audio_download_failed" class="alert alert-warning spacer-left-15 collapse">
    <p><%= t('audio.download_failed.body').html_safe %></p>
    <%= link_to t('audio_test.test_failed_alert.link'), t('audio.download_failed.link'), target: "_blank" %>
  </div>
  <div id="launch_test_button" class="collapse">
    <h4 class='v-space-6'><%= t('audio_test.check_voice') %></h4>
    <div class="flex row h-center v-center space-between" id='test_id' aria-label="..." role="group">
      <% if current_user.language_name ==  'fr' %>
        <button class="button secondary" type="button" onclick="$crisp.push(['do', 'helpdesk:article:open', ['fr', '1s32z90']])"><%= t('onboarding.problem') %></button>
      <% else %>
        <button class="button secondary" type="button" onclick="$crisp.push(['do', 'helpdesk:article:open', ['en-us', '14rzuhr']])"><%= t('onboarding.problem') %></button>
      <% end %>
      <button class="button primary space-2" data-test="recording_validate" id="start_tutorial"><%= t('audio.mic_ok') %></button>
    </div>
  </div>
</div>

<div id='audio-test-no_audio_device_available_or_allowed' style='display: none;'>
  <h2><%= t('audio.no_audio_device_available_or_allowed_title') %></h2>
  <p><%= t('audio.no_audio_device_available_or_allowed_1') %></p>
  <a href="/welcome" class="button primary"><%= t('audio.no_audio_device_available_or_allowed_2') %></a>
</div>

<div id='audio-test-upload_to_s3_failed' style='display: none;' class='main-content'>
  <h2><%= t('audio.upload_to_s3_failed_title') %></h2>
  <p><%= t('audio.upload_to_s3_failed_1') %></p>
  <a href="/welcome" class="button primary v-space-4"><%= t('audio.upload_to_s3_failed_2') %></a>
</div>
