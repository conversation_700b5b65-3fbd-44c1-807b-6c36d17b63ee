<% if current_brand && current_brand.name == "pippletdemo" %>
  <%= content_tag(:script, nonce: content_security_policy_nonce) do %>
    dataLayer.push({'event': 'Trial Test Started'});
  <% end %>
<% end %>


<div class="col-sm-12 col-md-offset-2 col-md-8 spacer-40">
  <h2 class="col-sm-offset-3 col-sm-9"><%=t(get_branded_raw_key('home.title'))%></h2>

<% case @connect_status -%>
<% when :invalid_url -%>
  <p class="col-sm-offset-3 col-sm-9"><%=t('connect.invalid_url')%></p>
  <p class="col-sm-offset-3 col-sm-9"><%=t('connect.invalid_please_contact_url_provider')%></p>

<% when :invalid_c_parameter -%>
  <p class="col-sm-offset-3 col-sm-9"><%=t('connect.invalid_or_inactive_campaign')%></p>
  <p class="col-sm-offset-3 col-sm-9"><%=t('connect.invalid_please_contact_url_provider')%></p>

<% when :ok -%>
  <%= form_for @user, url: "/users/connect?c=#{@current_direct_user.pipplet_clients_campaign_hashed_id}", :method => "post", :html => {:class => "form-horizontal"}, multipart: true, authenticity_token: true do |f| %>

  <div class="form-group">
    <p class="col-sm-offset-3 col-sm-9"><%=t('connect.fill_in', language: I18n.t('languages.'+@current_direct_user.pipplet_clients_campaign_test_language))%></p>
  </div>

  <% if defined?(@user) && @user && @user.errors.any? %>
    <div id="error_explanation">
      <h2><%= @user.errors.count > 1 ? I18n.t('errors.template.header.other') : I18n.t('errors.template.header.one') %></h2>
      <ul>
      <% @user.errors.full_messages.each do |msg| %>
        <li><%= msg %></li>
      <% end %>
      </ul>
    </div>
  <% end %>

    <input type="hidden" name="c" value="<%= @current_direct_user.pipplet_clients_campaign_hashed_id %>" />

    <div class="form-group">
      <%= label_tag 'first_name', t('activerecord.attributes.user.first_name'), :class => 'col-sm-3 control-label' %>
      <div class="col-sm-6">
        <%= f.text_field :first_name, :autofocus => true, :class => 'form-control', :placeholder => t('activerecord.attributes.user.first_name') %>
      </div>
    </div>
    <div class="form-group">
      <%= label_tag 'last_name', t('activerecord.attributes.user.last_name'), :class => 'col-sm-3 control-label' %>
      <div class="col-sm-6">
        <%= f.text_field :last_name, :autofocus => true, :class => 'form-control', :placeholder => t('activerecord.attributes.user.last_name') %>
      </div>
    </div>
    <div class="form-group">
      <%= label_tag 'email', 'Email', :class => 'col-sm-3 control-label' %>
      <div class="col-sm-6">
        <%= f.email_field :email, :autofocus => true, :class => 'form-control', :placeholder => 'Email' %>
      </div>
    </div>
    <div class="form-group">
      <div class="col-sm-offset-3 col-sm-9">
        <button type="submit" class="button primary"><%=t('connect.proceed')%></button>
      </div>
    </div>
  <%end%>

<%end%>
</div>
