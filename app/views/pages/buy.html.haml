= javascript_include_tag 'https://js.stripe.com/v3/'
= javascript_include_tag 'https://checkout.stripe.com/checkout.js'
= javascript_include_tag "modules/stripe.js"
#spinner
.container#b2C
  .row.introduction.spacer-20
    %h2=t('buy.title')
    %p=raw t('buy.introduction')
  .form-horizontal.white-background
    .form-group
      %h4.padding-bottom-md.margin-right-md
        ='1. '
        =t('buy.choose_language')
      #language_select{"data-toggle" => "buttons", "data-order-description" => I18n.t('buy.order_description')}
        - @available_languages.each_slice(8).each do |slice|
          .btn-group.btn-group-md.btn-group-justified.spacer-10
          - slice.each do |lang, key| 
            %label.btn.btn-default
              %input{:type => "radio", :name => "lang", :value => lang, 'data-key' => key }= I18n.t('languages.'+lang)

    .form-group       
      %h4.padding-bottom-md.margin-right-md
        ='2. '
        =t('buy.infos_title')
      #b2c_form.form.padding-right-lg.form-horizontal
        .form-group.row
          = label_tag 'first_name', I18n.t('activerecord.attributes.user.first_name'), :class => 'control-label col-xs-2'
          .col-xs-6
            = text_field_tag :first_name, current_user ? current_user.first_name : "", placeholder: "#{I18n.t('activerecord.attributes.user.first_name')}", :class => 'form-control', :required => true
        .form-group.row
          = label_tag 'last_name', I18n.t('activerecord.attributes.user.last_name'), :class => 'control-label col-xs-2'
          .col-xs-6
            = text_field_tag :last_name, current_user ? current_user.last_name : "", placeholder: "#{I18n.t('activerecord.attributes.user.last_name')}", :class => 'form-control', :required => true
        .form-group.row
          = label_tag 'email', I18n.t('activerecord.attributes.user.email'), :class => 'control-label col-xs-2'
          .col-xs-6
            = email_field_tag :email, current_user ? current_user.email : "", placeholder: "#{I18n.t('activerecord.attributes.user.email')}", :class => 'form-control', :required => true
  
      .form-group
        .col-xs-2
        .col-xs-10
          =check_box_tag 'accept-terms', true
          %span=raw t('buy.accepts_terms')
      
      #order-row
        #alert_biling.alert.alert-warning.alert-dismissible{"style"=>"display:none;","data-missing-terms" => I18n.t('buy.missing_terms'), "data-missing-elements" => I18n.t('buy.missing_elements')}
          %button.close{"data-dismiss"=>"alert", "aria-label"=>"Close"}
            %span{"aria-hidden"=>"true"} &times;
          %span#alert_content
        = link_to t('buy.order_by_card'), '#', {:class => 'btn btn-primary margin-bottom-lg margin-top-sm', :id => 'direct-stripe-button', "data-panel-label"=>I18n.t('onboarding.order_panel'), "data-loading-text"=>"...", "data-text-order"=>I18n.t('onboarding.order'), "data-text-order-send-part"=>I18n.t('onboarding.order_send_part')}
      
  .row#test-is-ready{:style => 'display:none;text-align:center;  '}
    %p
      = raw t('buy.success_top')
    %a#go-button.btn.btn-primary{:href => '#'}
      =t('buy.go-button')
    %p
      = raw t('buy.success_bottom')
