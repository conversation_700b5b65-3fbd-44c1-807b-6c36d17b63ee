<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title><%= content_for?(:title) ? yield(:title) : "Pipplet Test" %></title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="Pipplet">
    <meta name="google" content="notranslate">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <% if @current_test_instance %>
      <%= tag :meta, name: 'current_test_instance_uuid', content: @current_test_instance.uuid %>
      <%= tag :meta, name: 'skip_tutorial', content: @current_test_instance.skip_tutorial? %>
    <% end %>

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= capybara_lockstep if defined?(Capybara::Lockstep) %>

    <%= stylesheet_link_tag "application", media: "all" %>
    <%= stylesheet_link_tag 'new', media: 'all' %>
    <%= render partial: "favicons" %>

    <%= javascript_importmap_tags %>
    <%= javascript_include_tag 'application_old', nonce: true %>
    <% if browser.safari? || browser.edge? %>
      <%= javascript_include_tag 'pipplet/pipplet_ogvjs', nonce: true %>
      <%= javascript_include_tag '/javascript/vendor/ogvjs-1.6.0/ogv.js', nonce: true %>
    <% end %>

    <%= render partial: "crisp" %>
    <%= render partial: "gtm" %>

    <%= content_tag(:script, nonce: content_security_policy_nonce) do %>
      addEventListener('error', window.__e = function f(e) {
        f.q = f.q || [];
        f.q.push(e)
      });
    <% end %>
    <link href="<%= brand_stylesheet_path(current_brand, format: :css) %>" rel="stylesheet" type="text/css"/>
  </head>

  <body data-controller="context" class="<%= "branded #{current_brand&.compilation_name}" %>" data-no-turbolink>
    <% if controller_name == "questions" || (controller_name == "users" && ["test_cancelled", "welcome", "test_finished"].include?(action_name)) %>
      <%= render partial: "test_navbar" %>
    <% elsif current_user&.is_examiner? %>
      <%= render partial: "examiner_navbar" %>
    <% else %>
      <%= render partial: "standard_navbar" %>
    <% end %>

    <% unless Rails.env.production? %>
      <div class="env-banner <%= Rails.env %>">
        <span><%= Rails.env.capitalize %></span>
      </div>
    <% end %>

    <div id="test_onboarding_breadcrumbs">
      <% if (controller_name == "questions" && action_name == "index") %>
        <%= render partial: '/users/test_onboarding_breadcrumbs', locals: { current_step: :start, display: true } %>
      <% elsif (controller_name == "users" && action_name == "welcome") %>
        <%= render partial: '/users/test_onboarding_breadcrumbs', locals: { current_step: :tutorial, display: true } %>
      <% end %>
    </div>

    <div class="container main-container" id="main-content">
      <noscript>
        <div class="alert alert-error" role="alert">
          <%= t('general.no_javascript') %>
        </div>
      </noscript>

      <div id="alert_messages">
        <%= bootstrap_flash %>
      </div>

      <%= yield %>

      <div class="push"></div>

      <% if !["confirmations", "questions", "users"].include?(controller_name) %>
        <div class="footer col-xs-12">
          <p><a target="_blank" href="http://www.pipplet.com">www.pipplet.com</a></p>
        </div>
      <% end %>
    </div> <!-- /container -->

    <%= render partial: "analytics" %>

    <div class="modal" role="dialog" tabindex="-1" id="time-out-test-load-connected">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title"> <%= t('test.bad_connection.title') %> </h3>
          </div>
          <div class="modal-body">
            <%= t('test.bad_connection.body') %>
          </div>
          <div class="modal-footer">
            <%= button_to t('test.bad_connection.cancel'), welcome_path, method: :get, class: 'btn btn-primary' %>
          </div>
        </div>
      </div>
    </div>

    <div class="modal" role="dialog" tabindex="-1" id="time-out-test-load-offline">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title"> <%= t('test.no_connection.title') %> </h3>
          </div>
          <div class="modal-body">
            <%= t('test.no_connection.body').html_safe %>
          </div>
          <div class="modal-footer" data-dismiss="modal">
            <%= button_to t('test.no_connection.cancel'), welcome_path, method: :get, class: 'btn btn-primary' %>
          </div>
        </div>
      </div>
    </div>

    <div id="spinner"></div>
  </body>
</html>
