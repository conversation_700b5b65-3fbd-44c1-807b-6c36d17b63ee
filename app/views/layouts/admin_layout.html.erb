<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="Pipplet">
    <meta name="google" content="notranslate">

    <title><%= content_for?(:title) ? yield(:title) : "Pipplet Admin" %></title>

    <%= render 'favicons', admin: true %>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= capybara_lockstep if defined?(Capybara::Lockstep) %>

    <%= stylesheet_link_tag 'application', media: 'all' %>
    <%= javascript_include_tag 'https://code.jquery.com/jquery-3.7.1.min.js', nonce: true %>
    <%= javascript_include_tag 'admin_old', nonce: true %>
    <%= javascript_importmap_tags 'admin' %>
    <%= javascript_include_tag 'examiner_statistics', nonce: true %>
    <% if browser.safari? || browser.edge? %>
      <%= javascript_include_tag 'pipplet/pipplet_ogvjs', nonce: true %>
      <%= javascript_include_tag '/javascript/vendor/ogvjs-1.6.0/ogv.js', nonce: true %>
    <% end %>

    <!-- Latest compiled and minified CSS -->
    <%= stylesheet_link_tag 'https://unpkg.com/multiple-select@1.5.2/dist/multiple-select.min.css' %>

    <!-- Latest compiled and minified JavaScript -->
    <%= javascript_include_tag 'https://unpkg.com/multiple-select@1.5.2/dist/multiple-select.min.js', nonce: true %>

    <%= stylesheet_link_tag 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css' %>
    <%= javascript_include_tag 'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js', nonce: true %>
  </head>

  <body data-controller="context" >
    <%=render partial: "standard_navbar"%>

    <% unless Rails.env.production? %>
      <div class="env-banner <%= Rails.env %>">
        <span><%= Rails.env.capitalize %></span>
      </div>
    <% end %>

    <div class="container-fluid" id="admin-container">

      <div class="row">
        <div id="admin_navbar" class="col-md-1 sidebar">
          <ul class="nav nav-sidebar">
            <li class="nav-item<%= " active" if params[:controller] == "admin/admin"%>"><a href="/admin">&rarr; Dashboard &larr;</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/test_instances"%>"><a href="/admin/test_instances">Test Instances</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/evaluations"%>"><a href="/admin/evaluations">Evaluations</a></li>
            <li class="nav-item<%= " active" if (params[:controller] == "admin/evaluations" && params[:action] == "stuck_evaluations_index")%>"><a href="/admin/evaluations/stuck_evaluations_index">S - Evaluations</a></li>
            <li class="nav-item<%= " active" if (params[:controller] == "admin/evaluations" && params[:action] == "overdue_evaluations_index")%>"><a href="/admin/evaluations/overdue_evaluations_index">O - Evaluations</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/examiners"%>"><a href="/admin/examiners">Examiners</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/languages"%>"><a href="/admin/languages">Languages</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/test_profiles"%>"><a href="/admin/test_profiles">Test Profiles</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/challenges"%>"><a href="/admin/challenges">Challenges</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/user_groups"%>"><a href="/admin/user_groups">User groups</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/direct_users"%>"><a href="/admin/direct_users">Direct Users</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/brands"%>"><a href="/admin/brands">Brands</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/event_logs"%>"><a href="/admin/event_logs">Event Logs</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/behaviour_logs"%>"><a href="/admin/behaviour_logs">Behaviour Logs</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/tags"%>"><a href="/admin/tags">Tags</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/identity_providers"%>"><a href="/admin/identity_providers">Identity Providers</a></li>
            <hr style="margin:5px; margin-left:10%; margin-right:10%; width:80%;">
            <li class="nav-item<%= " active" if params[:controller] == "admin/alerts"%>"><a href="/admin/alerts">Alerts</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/api_users"%>"><a href="/admin/api_users">Api Users</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/sso_users"%>"><a href="/admin/sso_users">Sso Users</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/challenges/import"%>"><a href="/admin/challenges/import">Challenges Import</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/challenges"%>"><a href="/admin/client_configs">Client Configs</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/languages"%>"><a href="/admin/languages">Languages</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/productions"%>"><a href="/admin/productions/select_production">Productions</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/question_elements"%>"><a href="/admin/question_elements">Question Elements</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/remote_clients"%>"><a href="/admin/remote_clients">Remote Clients</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/statistics"%>"><a href="/admin/statistics">Statistics</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/evaluation_delays"%>"><a href="/admin/evaluation_delays">Evaluation Delays</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/test_instance_validations"%>"><a href="/admin/test_instance_validations">TI  validations</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/assessment_types"%>"><a href="/admin/assessment_types">Assessment Type</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/assessment_questions"%>"><a href="/admin/assessment_questions">Assessment Question</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/evaluations" && params[:action] == "evaluation_payment_index"%>"><a href="/admin/evaluations/evaluation_payment_index?go_back=1">Evaluations Payment</a></li>
            <li class="nav-item<%= " active" if params[:controller] == "admin/invoice_requests"%>"><a href="/admin/invoice_requests">Invoice Requests</a></li>
          </ul>
        </div>



        <div id="admin_main" class="col-md-11 col-md-offset-1 main">
          <div class="row">
              <div id="alert_messages" style="margin-top: 10px"><%= bootstrap_flash %></div>

            <% if params[:controller] == "admin/challenges" && @challenge %>
              <div style="color: black; background-color: #f5f5f5;padding: 10px 10px;font-family: 'Courier New', Courier, monospace;text-align:center; border-top:5px solid white;">
                <a href="/admin/challenges/<%=@challenge.id%>">#<%=@challenge.id%> (<%=@challenge.linguist_id%>)</a> -
                <a href="/admin/challenges/<%=@challenge.id%>/production">Production</a> -
                <a class="glyphicon glyphicon-info-sign" href="/admin/challenges/<%=@challenge.id%>/infos_production"></a> -
                <a href="/admin/challenges/<%=@challenge.id%>/edit_production">Edit (P)</a></td> -
                <a href="/admin/challenges/<%=@challenge.id%>/reception">Reception</a> -
                <a class="glyphicon glyphicon-info-sign" href="/admin/challenges/<%=@challenge.id%>/infos_reception"></a> -
                <a href="/admin/challenges/<%=@challenge.id%>/edit_reception">Edit (R)</a>
                 - Language
                <%= form_tag("/admin/challenges/#{@challenge.id}/production", method: :get, style: 'display: inline;') do %>
                  <%= select_tag('lang', options_for_select(AVAILABLE_TEST_LANGUAGES.map{|l| [l,l]}, @test_language), {:onchange => "this.form.submit();"}) %>
                <% end %>
              </div>
            <% end %>

            <%= yield %>
          </div>
        </div>


      </div><!-- /row -->
    </div><!-- /container-fluid -->
    <%= render partial: "analytics"%>

  </body>
</html>
