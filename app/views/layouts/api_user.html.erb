<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title><%= content_for?(:title) ? yield(:title) : "Pipplet Test" %></title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="Pipplet">

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= capybara_lockstep if defined?(Capybara::Lockstep) %>
    <%= stylesheet_link_tag "application", media: "all" %>

    <%= render partial: "favicons" %>
    <%= javascript_include_tag 'https://code.jquery.com/jquery-3.7.1.min.js', nonce: true %>

    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/dt-1.11.5/b-2.2.2/b-colvis-2.2.2/b-html5-2.2.2/b-print-2.2.2/r-2.2.9/datatables.min.css"/>
    <%= javascript_include_tag 'https://cdn.datatables.net/v/dt/dt-1.11.5/b-2.2.2/b-colvis-2.2.2/b-html5-2.2.2/b-print-2.2.2/r-2.2.9/datatables.min.js', nonce: true %>
    <%= javascript_include_tag 'https://cdn-audition.pipplet.com/static_assets/survey.jquery.min.js', nonce: true %>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <%= javascript_include_tag 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', nonce: true %>
    <%= content_tag(:script, nonce: content_security_policy_nonce) do %>
      addEventListener('error', window.__e = function f(e) {
        f.q = f.q || [];
        f.q.push(e)
      });
    <% end %>
  </head>

  <body data-controller="context" class="<%= "branded #{current_brand&.compilation_name}" %>" style="padding-top: 0" data-no-turbolink>
    <div class="container main-container" id="main-content">
      <noscript>
        <div class="alert alert-error" role="alert">
          <%= t('general.no_javascript') %>
        </div>
      </noscript>

      <div id="alert_messages">
        <%= bootstrap_flash %>
      </div>

      <%= yield %>

      <div class="push"></div>

      <% unless %w[questions users].include?(controller_name) %>
      <div class="footer col-xs-12">
        <p><a target="_blank" href="http://www.pipplet.com">www.pipplet.com</a></p>
      </div>
      <% end %>
    </div>

    <%= render partial: "analytics" %>
  </body>
</html>
