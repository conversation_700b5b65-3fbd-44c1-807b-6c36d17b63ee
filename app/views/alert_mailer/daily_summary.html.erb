<h1>Real Tests</h1>

<h1> Languages Insight </h1>
<% @high_workload_rate_languages.each do |lang, workload| %>
  <p> Workload for language <%= lang %>: <%= (workload * 100).round %>% </p>
  <% if not @not_available_examiners[lang.to_s].nil? %>
    <p> Examiner(s) not available: </p>
    <ul>
      <% @not_available_examiners[lang.to_s].each do |examiner| %>
        <li> <%= link_to examiner.name, admin_examiner_url(examiner.id) %> is not available</li>
      <% end %>
    </ul>
  <% end %>
<% end %>

<h2>Sanity Check</h2>
<ul>
  <li>Count of TI in progress with no questionables : <%= @test_instances_in_progress_no_questionables.length %></li>
  <li>Count of TI in Sent for Evaluation for more than 24hours
    : <%= @test_instances_sent_for_evaluation_over_24h.length %></li>
  <li>Count of TI in Validation Failed for more than 24hours
    : <%= @test_instances_validation_failed_over_24h.length %></li>
</ul>


<h2>Test Instances completed per client</h2>
<span>Count : <%= @test_instances_not_test_mode_count %></span>
<table>
  <tr>
    <th>Group</th>
    <th>Count</th>
  </tr>
  <% @test_instances_not_test_mode.each_pair do |group, ti_list| %>
    <tr>
      <td><%= group %></td>
      <td><%= ti_list.size %></td>
    </tr>
  <% end %>
</table>

<h1>Test Sent count per language</h1>
<span>Count : <%= @test_instances_created_not_test_mode_count %></span>
<table>
  <tr>
    <th>Examiner Name</th>
    <th>Count</th>
  </tr>
  <% @test_instances_created_not_test_mode.each_pair do |language, ti_list| %>
    <tr>
      <td colspan="6" style="background-color:#ccc;text-align:center;"><%= I18n.t("languages.#{language.first}") %></td>
    </tr>
    <% ti_list.group_by(&:examiner_id).each_pair do |examiner_id, ti_details| %>
      <tr>
        <td>
          <%= Examiner.find_by(id: examiner_id)&.name %>
        </td>
        <td>
          <%= ti_details.size %>
        </td>
      </tr>
    <% end %>
  <% end %>
</table>

<% if @test_instances_in_progress_no_questionables.length > 0 %>
  <h3>Test Instances In Progress with No Questionables <%= @test_instances_in_progress_no_questionables.length %></h3>
  <table>
    <tr>
      <th>First Name</th>
      <th>Last Name</th>
      <th>Email</th>
      <th>Language</th>
      <th>Status</th>
      <th>Link</th>
    </tr>
    <% @test_instances_in_progress_no_questionables.each do |ti| %>
      <tr>
        <td><%= ti.user.first_name %></td>
        <td><%= ti.user.last_name %></td>
        <td><%= ti.user.email %></td>
        <td><%= ti.test_language %></td>
        <td><%= ti.status %></td>
        <td><a href="<%= admin_test_instance_url(ti) %>">link</a></td>
      </tr>
    <% end %>
  </table>
<% end %>

<% if @test_instances_sent_for_evaluation_over_24h.length > 0 %>
  <h3>Test Instances Sent for Evaluation, completed for more than 24 hours
    : <%= @test_instances_sent_for_evaluation_over_24h.length %></h3>
  <table>
    <tr>
      <th>First Name</th>
      <th>Last Name</th>
      <th>Email</th>
      <th>Language</th>
      <th>Status</th>
      <th>Link</th>
    </tr>
    <% @test_instances_sent_for_evaluation_over_24h.each do |ti| %>
      <tr>
        <td><%= ti.user.first_name %></td>
        <td><%= ti.user.last_name %></td>
        <td><%= ti.user.email %></td>
        <td><%= ti.test_language %></td>
        <td><%= ti.status %></td>
        <td><a href="<%= admin_test_instance_url(ti) %>">link</a></td>
      </tr>
    <% end %>
  </table>
<% end %>

<% if @test_instances_validation_failed_over_24h.length > 0 %>
  <h3>Test Instances Validation Failed, completed for more than 24 hours
    : <%= @test_instances_validation_failed_over_24h.length %></h3>
  <table>
    <tr>
      <th>First Name</th>
      <th>Last Name</th>
      <th>Email</th>
      <th>Language</th>
      <th>Status</th>
      <th>Link</th>
    </tr>
    <% @test_instances_validation_failed_over_24h.each do |ti| %>
      <tr>
        <td><%= ti.user.first_name %></td>
        <td><%= ti.user.last_name %></td>
        <td><%= ti.user.email %></td>
        <td><%= ti.test_language %></td>
        <td><%= ti.status %></td>
        <td><a href="<%= admin_test_instance_url(ti) %>">link</a></td>
      </tr>
    <% end %>
  </table>
<% end %>

<h2>Test Instances</h2>
<table>
  <tr>
    <th>First Name</th>
    <th>Last Name</th>
    <th>Email</th>
    <th>Language</th>
    <th>Status</th>
    <th>Link</th>
  </tr>
  <% @test_instances_not_test_mode.each_pair do |group, ti_list| %>
    <tr>
      <td colspan="6" style="background-color:#ccc;text-align:center;"><%= group %></td>
    </tr>
    <% ti_list.each do |ti| %>
      <tr>
        <td><%= ti.user.first_name %></td>
        <td><%= ti.user.last_name %></td>
        <td><%= ti.user.email %></td>
        <td><%= ti.test_language %></td>
        <td><%= ti.status %></td>
        <td><a href="<%= admin_test_instance_url(ti) %>">link</a></td>
      </tr>
    <% end %>
  <% end %>
</table>


<h1>Test Mode</h1>
<span>Count : <%= @test_instances_test_mode_count %></span>
<table>
  <tr>
    <th>First Name</th>
    <th>Last Name</th>
    <th>Email</th>
    <th>Language</th>
    <th>Status</th>
    <th>Link</th>
  </tr>
  <% @test_instances_test_mode.each_pair do |group, ti_list| %>
    <tr>
      <td colspan="6" style="background-color:#ccc;text-align:center;"><%= group %></td>
    </tr>
    <% ti_list.each do |ti| %>
      <tr>
        <td><%= ti.user.first_name %></td>
        <td><%= ti.user.last_name %></td>
        <td><%= ti.user.email %></td>
        <td><%= ti.test_language %></td>
        <td><%= ti.status %></td>
        <td><a href="<%= admin_test_instance_url(ti) %>">link</a></td>
      </tr>
    <% end %>
  <% end %>
</table>

<h1>Test Sent</h1>
<table>
  <tr>
    <th>First Name</th>
    <th>Last Name</th>
    <th>Email</th>
    <th>Status</th>
    <th>Link</th>
    <th>Group</th>
  </tr>
  <% @test_instances_created_not_test_mode.each_pair do |language, ti_list| %>
    <tr>
      <td colspan="6" style="background-color:#ccc;text-align:center;"><%= I18n.t("languages.#{language.first}") %></td>
    </tr>
    <% ti_list.group_by(&:examiner_id).each_pair do |examiner_id, ti_details| %>
      <tr>
        <td></td>
        <td colspan="5" style="background-color:#ddd;text-align:center;">
          <%= Examiner.find_by(id: examiner_id)&.name %>
        </td>
      </tr>
      <% ti_details.each do |ti| %>
        <tr>
          <td><%= ti.user.first_name %></td>
          <td><%= ti.user.last_name %></td>
          <td><%= ti.user.email %></td>
          <td><%= ti.status %></td>
          <td><a href="<%= admin_test_instance_url(ti) %>">link</a></td>
          <td><%= ti.user.group %></td>
        </tr>
      <% end %>
    <% end %>
  <% end %>
</table>
