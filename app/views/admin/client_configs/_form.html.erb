<h3>Configuration details</h3>
<%= form_text_field(f, :client_name) %>
<%= form_check_box(f, :active) %>
<%= form_check_box(f, :deliver_certificate_to_client) %>
<%= form_check_box(f, :skip_check_caracters) %>
<h4>Test Instance Expiring</h4>
<%= form_check_box(f, :expire_test_instances) %>
<%= form_text_field(f, :expire_test_instances_limit) %>
<h4>Reminders Management</h4>
<%= form_check_box(f, :send_reminders) %>
<%= form_text_field(f, :count_of_reminders_to_send) %>
<%= form_text_field(f, :days_between_reminders) %>
<h4>Urgent Mode</h4>
<%= form_check_box(f, :urgent_mode) %>
<h4>Force Secure Browser</h4>
<%= form_check_box(f, :force_secure_browser) %>
<h4>Strong Identity Check</h4>
<%= form_check_box(f, :strong_identity_check) %>
<h4>Peer Review Config</h4>
<%= form_text_readonly(f, :systematic_peer_review) %>
<%= form_text_field(f, :systematic_peer_review_counter) %>
<h4>Client Comments</h4>
<div id="client_config_comment_list">
  <% @resource.client_comments.each_with_index do |cc, index| %>
    <%= render :partial => "client_config_comment", :locals => { :client_config_comment => cc, :index => index } %>
    <div class="form-group">
      <div class="col-sm-4 control-label"></div>
      <div class="col-sm-7">
        <%= link_to "Delete this client comment", delete_client_comment_admin_client_config_url(@resource, cc), class: 'btn btn-warning' %>
      </div>
    </div>
  <% end %>
</div>
<% if !@resource.new_record? %>
  <div class="form-group">
    <div class="col-sm-4 control-label"></div>
    <div class="col-sm-7">
      <%= link_to "Add a client comment", add_client_comment_admin_client_config_url(@resource), class: 'btn btn-primary' %>
    </div>
  </div>
<% end %>
<h4>Block direct candidate contact</h4>
<%= form_check_box(f, :block_direct_candidate_contact) %>
<h4>Email</h4>
<%= form_email_field(f, :surcharged_certificate_email) %>
<%= form_email_field(f, :cced_email) %>
<%= form_check_box(f, :has_attached_certificate) %>
<%= form_text_field(f, :custom_message) %>
<%= form_check_box(f, :practice_pack_mention) %>
<h4>Api</h4>
<%= form_check_box(f, :expose_dashboard_url_in_api) %>
<%= form_check_box(f, :expose_identity_photos_in_api) %>
<h4>AI</h4>
<%= form_check_box(f, :disable_ai_assessments) %>
<%= form_check_box(f, :disable_ai_model_training) %>
<h4>Proctoring</h4>
<%= form_check_box(f, :enable_wheebox_proctoring) %>
