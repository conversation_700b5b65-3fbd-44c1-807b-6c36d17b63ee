<li id="question_<%= q.id %>_question_element_<%= qe.id %>"><%= label_tag :type, qe.type %>
  <p class="text-warning">When you are editing this word on this page, you are editing it on production's page too :
  <a href="/admin/challenges/<%= @challenge.id %>/edit_production" class="text-warning"><u>Go to the production's page >></u></a></p>
  <% qe.content.each_pair do |key,value| %>
  <!-- form pour les question_elements -->
    <% if key == "word" %>
      <%= form_tag "/admin/challenges/#{@challenge.id}/#{@current_page}", :method => "put", :remote => true, :class => "form-horizontal" do %>
        <ul class="no_list_style">
          <div id="edit_<%= qe.id %>"></div>
          <li><%= label_tag :content, key, :class => "text-muted"%></li>
          <li><%= text_field_tag "question_elements["+qe.id.to_s+"]["+key+"]", value, :size => 60 %>
          <%= submit_tag "Update", :class => "btn btn-default btn-sm" %></li>
        </ul>
        <% end %>
    <% else %>
      <ul class="no_list_style">
        <div id="edit_<%= qe.id %>"></div>
        <li><%= label_tag :content, key + " : ", :class => "text-muted"%>
        <%= label_tag :content, value, :class => "text-muted"%></li>
      </ul>
    <% end %>
  <% end %>
  <%= form_tag "/admin/challenges/#{@challenge.id}/remove_question_element", :method => "put", :remote => true, :class => "form-horizontal" do %>
    <%= hidden_field_tag "question_id", q.id %>
    <%= hidden_field_tag "question_element_id", qe.id %>
    <%= submit_tag "Remove this Question Element", :class => "btn btn-warning btn-sm" %>
  <% end %>
  <br>
</li>