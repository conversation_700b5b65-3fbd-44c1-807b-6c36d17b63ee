<%= form_section('Main') %>
<%= form_text_field(f, :name) %>

<%= form_section('Examiners') %>

<% AVAILABLE_TEST_LANGUAGES.each do |l| %>
  <% attr_lang = "examiner_"+l.to_s+"_id" %>
  <% if @resource.respond_to?(attr_lang.to_sym) %>
    <%= form_select_with_prompt_field(f, attr_lang.to_sym, Examiner.includes(:user).for(l.to_sym).map{|e| [e.id, e.name] }.push([nil, "None"]), :first, :last) %>
  <% end %>
<% end %>

<% if @resource.persisted? %>
    <%= form_section('Timestamps') %>
    <%= form_text_readonly(f, :created_at) %>
    <%= form_text_readonly(f, :updated_at) %>
<% end %>

<%= form_section('Average results') %>
<table class="admin_table table table-striped table-bordered dataTable">
  <thead>
    <tr>
      <th>Packet first date</th>
      <th>Size</th>
      <th>Average</th>
      <th>P-1 gap</th>
    </tr>
  </thead>

  <tbody>
    <% @resource.packed_average_results.each_with_index do |packet, index| %>
      <tr>
        <td><%= packet[:first_date] %></td>
        <td><%= packet[:size] %></td>
        <td><%= get_cecrl_score(packet[:average]) %> ( <%= packet[:average] %> ) </td>
        <td><%= packet[:diff_average] %></td>
      </tr>
    <% end %>
  </tbody>

  <tfoot>
    <tr>
      <th>Index</th>
      <th>Size</th>
      <th>Average</th>
      <th>P-1 gap</th>
    </tr>
  </tfoot>
</table>

