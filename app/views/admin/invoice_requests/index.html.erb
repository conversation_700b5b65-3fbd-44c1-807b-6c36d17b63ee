<%= link_to "Send all invoice requests", send_all_invoice_requests_admin_invoice_requests_path(:invoice_request_ids => @resources.created.pluck(:id)), method: :post, class: 'counter-h1 btn btn-default float', style: 'margin-left:15px;' %>
<%= link_to "Download all invoices received", download_invoices_admin_invoice_requests_path(:invoice_requests_ids => @resources.with_invoice.pluck(:id)), method: :get, class: 'counter-h1 btn btn-default float' %>

<%= form_tag(admin_invoice_requests_url, method: :get, :class => 'form-horizontal row') do %>
  <div class="form-group">
    <label class="control-label col-sm-2">Status</label>
    <div class="col-sm-10" style="width:45%;">
      <% @all_states.each do |state| %>
        <label class="radio-inline">
          <%= radio_button_tag 'state', state, @state == state %> <%= state %>
        </label>
      <% end %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Month</label>
    <div class="col-sm-10">
      <% @all_month.each do |selected_month| %>
        <label class="radio-inline">
          <%= radio_button_tag 'selected_month', selected_month, @selected_month == selected_month %> <%= selected_month %>
        </label>
      <% end %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Year</label>
    <div class="col-sm-10">
      <%= select_tag('selected_year', options_for_select(@all_years, @selected_year)) %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Force all dates</label>
    <div class="col-sm-10">
      <% [true, false].each do |force_all_dates| %>
        <label class="radio-inline">
          <%= radio_button_tag 'force_all_dates', force_all_dates, @force_all_dates == force_all_dates %> <%= force_all_dates %>
        </label>
      <% end %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Examiner</label>
    <div class="col-sm-10">
      <%= select_tag(
        'examiner_id',
        options_for_select([['All', 'all']] + @all_examiners.map { |examiner| [examiner.name, examiner.id] }, @examiner.present? ? @examiner.id : nil)
        ) %>
    </div>
  </div>

  <%= submit_tag 'Search', :class => 'col-sm-offset-2 col-sm-3 btn btn-primary' %>
<% end %>

<% if @resources %>
  <div style="float:right;margin:10px 0;">
    <%= button_tag 'Set paid', class: 'btn btn-danger', id: 'invoice_requests_paid_all_action' %>
  </div>
<% end %>

<table data-page-length='100' class="admin_table table table-striped table-bordered dataTable">
  <thead>
  <tr>
    <th>
      Select all
      <br>
      <input type="checkbox" id="invoice_request_select_all">
    </th>
    <th>Invoice Request</th>
    <th>Month</th>
    <th>Examiner</th>
    <th>Status</th>
    <th>Evaluations simple</th>
    <th>Evaluations detailed</th>
    <th>Evaluations written</th>
    <th>Evaluations spoken</th>
    <th>Received at</th>
    <th>Requested amount for evaluations</th>
    <th>Requested amount for other services</th>
    <th>Requested amount</th>
    <th>Currency</th>
    <th>Invoice</th>
    <th>Validate</th>
    <th>Set paid</th>
    <th>Paid at</th>
  </tr>
  </thead>
  <tbody>
  <% @resources.each do |invoice_request| %>
    <tr>
      <td>
        <input type="checkbox" name="#invoicerequestsetpaid<%= invoice_request.id %>" class="invoice_request_set_paid">
      </td>
      <td data-order="<%= invoice_request.id %>"> <%= link_to "##{invoice_request.id}", controller: "invoice_requests", action: "edit", id: invoice_request.id %> </td>
      <td> <%= invoice_request.month %></td>
      <td data-order="<%= invoice_request.examiner.name %>"> <%= link_to "#{invoice_request.examiner.name}", edit_admin_examiner_path(invoice_request.examiner_id) %> </td>
      <td><p id=<%= "status#{invoice_request.id}" %>> <%= invoice_request.status %> </p></td>
      <td> <%= invoice_request.simple_evals_counter %> </td>
      <td> <%= invoice_request.detailed_evals_counter %> </td>
      <td> <%= invoice_request.written_evals_counter %> </td>
      <td> <%= invoice_request.spoken_evals_counter %> </td>
      <td> <%= invoice_request.invoice_received_at %> </td>
      <td><%= invoice_request.requested_amount_evaluations %></td>
      <td><%= invoice_request.requested_amount_other_services %></td>
      <td><%= invoice_request.requested_amount %></td>
      <td><%= invoice_request.invoice_currency %></td>
      <td>
        <% if invoice_request.invoice_pdf.present? %>
          <%= link_to "Link", invoice_request.invoice_pdf.url, target: "_blank" %>
        <% end %>
      </td>
      <td>
        <% if invoice_request.request_sent? || invoice_request.invoice_received? %>
          <%= link_to "Validate", validate_admin_invoice_request_path(invoice_request, :render => "index", :params => request.query_parameters), method: :patch, :remote => true, :id => "#invoicerequestvalidate#{invoice_request.id}", onclick: 'this.disable=true', data: { disable_with: 'Processing...' }, class: 'btn btn-xs btn-primary' %>
        <% end %>
      </td>
      <td>
        <% if invoice_request.is_validated %>
          <%= link_to "Set paid", set_paid_admin_invoice_request_path(invoice_request, :render => "index", :params => request.query_parameters), method: :patch, :remote => true, :id => "#invoicerequestsetpaid#{invoice_request.id}", class: 'btn btn-xs btn-primary' %>
        <% end %>
      </td>
      <td> <%= invoice_request.paid_at %> </td>
    </tr>
  <% end %>
  </tbody>

  <tfoot>
  <tr>
    <th></th>
    <th>Invoice Request</th>
    <th>Month</th>
    <th>Examiner</th>
    <th>Status</th>
    <th>Evaluations simple</th>
    <th>Evaluations detailed</th>
    <th>Evaluations written</th>
    <th>Evaluations spoken</th>
    <th>Created at</th>
    <th>Requested amount for evaluations</th>
    <th>Requested amount for other services</th>
    <th>Requested amount</th>
    <th>Currency</th>
    <th>Invoice</th>
    <th>Validate</th>
    <th>Set paid</th>
    <th>Paid at</th>
  </tr>
  </tfoot>
</table>
