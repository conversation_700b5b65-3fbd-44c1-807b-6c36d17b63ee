<h3> Users</h3>
<ul>
  <li>Users : <%= User.count %></li>
  <li>Users with a score : <%= User.score_available.count %></li>
</ul>

<table id="index_with_details_table" class="admin_table table table-striped table-bordered dataTable">
  <thead>
    <tr>
      <th>
        Created at
      </th>
      <th>
        Group (click to filter)
      </th>
      <th>
        Email (ID)
      </th>
      <th>
        Status
      </th>
      <th>
        Ratio of completion
      </th>
      <th>
        Score-main (red = over 2500 and blue = under 500)
      </th>
      <th>
        Score-average
      </th>
      <th>
        Score-production
      </th>
      <th>
        Score-reception
      </th>
      <th>
        Success in Production
      </th>
      <th>
        Success in Reception
      </th>
    </tr>
  </thead>
  <tbody>
    <% @users.each do |u| %>
      <tr>
        <td>
          <%= u.created_at.to_date %>
        </td>
        <td>
          <a href="/admin/users?group=<%= u.group %>"><%= u.group %></a>
        </td>
        <td>
          <a href="/admin/users/<%=u.id%>"><%=u.email %></a>
        </td>
        <td>
          <%= u.status %>
        </td>
        <td>
        <%= u.overall_ratio_of_test_completion.to_i %>
        </td>
        
        <% if u.scores.where(:label => "main").last.rating.to_i > 2500 %>
          <td class="over_2500">
        <% elsif u.scores.where(:label => "main").last.rating.to_i < 500 %>
          <td class="under_500">
        <% else %>
            <td>
        <% end %>
        <%= u.scores.where(:label => "main").last.rating.to_i %>    
        </td>
        
        <% if u.scores.where(:label => "average").last.rating.to_i > 2500 %>
          <td class="over_2500">
        <% elsif u.scores.where(:label => "average").last.rating.to_i < 500 %>
          <td class="under_500">
        <% else %>
            <td>
        <% end %>
          <%= u.scores.where(:label => "average").last.rating.to_i %>
        </td>
        
        <% if u.scores.where(:label => "production").last.rating.to_i > 2500 %>
          <td class="over_2500">
        <% elsif u.scores.where(:label => "production").last.rating.to_i < 500 %>
          <td class="under_500">
        <% else %>
            <td>
        <% end %>
          <%= u.scores.where(:label => "production").last.rating.to_i %>
        </td>
        
        <% if u.scores.where(:label => "reception").last.rating.to_i > 2500 %>
          <td class="over_2500">
        <% elsif u.scores.where(:label => "reception").last.rating.to_i < 500 %>
          <td class="under_500">
        <% else %>
            <td>
        <% end %>
          <%= u.scores.where(:label => "reception").last.rating.to_i %>
        </td>
        
        <td>
          <%= @user_successes[u.id][:production_successes] %>
        </td>
        <td>
          <%= @user_successes[u.id][:reception_successes] %>
        </td>
      </tr>
    <% end %>
  </tbody>
  <tfoot>
    <tr>
      <th>
        Created at
      </th>
      <th>
        Group (click to filter)
      </th>
      <th>
        Email (ID)
      </th>
      <th>
        Status
      </th>
      <th>
        Ratio of completion
      </th>
      <th>
        Score-main (red = over 2500 and blue = under 500)
      </th>
      <th>
        Score-average
      </th>
      <th>
        Score-production
      </th>
      <th>
        Score-reception
      </th>
      <th>
        Success in Production
      </th>
      <th>
        Success in Reception
      </th>
    </tr>
  </tfoot>
</table>