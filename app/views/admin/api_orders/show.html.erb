<nav><ol class='cd-multi-steps text-center'>
<% @api_order.class.statuses.keys.each do |s| %>
<%= case
    when @api_order.status == s
        "<li class='current'><div>#{s.humanize}</div>".html_safe
    else 
        "<li><div>#{s.humanize}</div>".html_safe
    end%></li>
<% end %>
</ol></nav>

<div class="col-md-12 form-horizontal">
  <%= form_section('Basic info') %>
  <%= text_only('Api Order Id', @api_order.id) %>
  <%= text_only('Source', @api_order.source) %>
  <%= text_only('Creation date', @api_order.created_at) %>


  <%= form_section('Optional references') %>
  <% if @api_order.user %>
  <div class="form-group">
      <label class="col-sm-4 control-label">User</label>
      <div class="col-sm-7">
          <%= link_to @api_order.user.full_name_with_id, admin_user_path(@api_order.user), class: 'btn btn-default' %>
      </div>
  </div>
  <% else %>
    No User
  <% end %>
  <% if @api_order.test_instance %>
  <div class="form-group">
      <label class="col-sm-4 control-label">Test Instance</label>
      <div class="col-sm-7">
          <%= link_to @api_order.test_instance_id, admin_test_instance_path(@api_order.test_instance), class: 'btn btn-default' %>
      </div>
  </div>
  <% else %>
    No Test Instance
  <% end %>
</div>

<div class="col-md-12">
  <%= form_section('Order details') %>
  <% begin%>
  <pre><%=JSON.pretty_generate(@api_order.order_information)%></pre>
  <% rescue %>
  <pre><%= JSON.pretty_generate(URI.decode_www_form(@api_order.order_information).to_h) %></pre>
  <% end %>
</div>

<div class="col-md-12">
  <%= form_section('Order errors') %>
  <pre><%=@api_order.order_errors%>&nbsp;</pre>
</div>
