<tr style="background-color: <%= ['#f0f0f0', '#ffffff'][eval_index % 2] %>">
  <td>
    <input class="select eval_digest_checkbox" type="checkbox" evaluation-id="<%= evaluation.id %>"
    <%= "disabled" if evaluation.presented_for_review_at.present?  %> />
  </td>

  <td> <%= link_to evaluation.test_instance_id, {controller: 'test_instances', action: 'edit', id: evaluation.test_instance_id}, target: '_blank', style: "color: #{['purple', 'blue'][eval_index % 2]}" %>
  </td>

  <td> <%= link_to evaluation.id, { controller: 'evaluations', action: 'edit', id: evaluation.id }, target: '_blank' %>
  </td>

  <td>
    <%= evaluation.assessment_type&.name %>
  </td>

  <td>
    <%= link_to "#{evaluation.examiner.name}", edit_admin_examiner_path(evaluation.examiner), target: "_blank", style: "font-weight: #{@grade_comparator_service.examiner&.id == evaluation.examiner.id ? "bold" : "none"}" %>
      <% if evaluation.examiner.trusted? %>
        <i class="glyphicon glyphicon-ok" style="color: green; margin-left: 3px"></i>
      <% end%>
  </td>

  <td>
    <%= evaluation.test_instance.client_name %>
  </td>

  <td>
    <% grade = evaluation.certificate&.grades&.find {|g| g.label == "overall" } %>
    <% begin %>
      <%= grade&.score %> (<%= grade&.cecrl_score %>)
    <% rescue %>
      NA)
    <% end %>
  </td>

  <td>
    <% av_grade = evaluation.test_instance.average_grades.find {|g| g.label == "overall" } %>
    <% begin %>
      <%= av_grade&.score %> (<%= av_grade&.cecrl_score %>)
    <% rescue %>
      NA)
    <% end %>
  </td>

  <% score = grade&.score.to_i %>
  <% av_score = av_grade&.score.to_i %>
  <% diff = score - av_score %>
  <td style='background-color: <%= diff.abs > 10 ? "#ffb2ae" : "none"; %>'>
    <p >
      <%= score > av_score ? "+#{diff}" : "#{diff}" %>
    </p>
  </td>

  <td>
    <% grade = evaluation.certificate&.grades&.find {|g| g.label == "spoken"} %>
    <% begin %>
      <%= grade&.score %> (<%= grade&.cecrl_score %>)
    <% rescue %>
      NA)
    <% end %>
  </td>

  <td>
    <% av_grade = evaluation.test_instance.average_grades.find {|g| g.label == "spoken"} %>
    <% begin %>
      <%= av_grade&.score %> (<%= av_grade&.cecrl_score %>)
    <% rescue %>
      NA)
    <% end %>
  </td>

  <% score = grade&.score.to_i %>
  <% av_score = av_grade&.score.to_i %>
  <% diff = score - av_score %>
  <td style='background-color: <%= diff.abs > 10 ? "#ffb2ae" : "none"; %>'>
    <p >
      <%= score > av_score ? "+#{diff}" : "#{diff}" %>
    </p>
  </td>

  <td>
    <% grade = evaluation.certificate&.grades&.find {|g| g.label == "written" } %>
    <% begin %>
      <%= grade&.score %> (<%= grade&.cecrl_score %>)
    <% rescue %>
      NA)
    <% end %>
  </td>

  <td>
    <% av_grade = evaluation.test_instance.average_grades.find {|g| g.label == "written" } %>
    <% begin %>
      <%= av_grade&.score %> (<%= av_grade&.cecrl_score %>)
    <% rescue %>
      NA)
    <% end %>
  </td>

  <% score = grade&.score.to_i %>
  <% av_score = av_grade&.score.to_i %>
  <% diff = score - av_score %>
  <td style='background-color: <%= diff.abs > 10 ? "#ffb2ae" : "none"; %>'>
    <p >
      <%= score > av_score ? "+#{diff}" : "#{diff}" %>
    </p>
  </td>
</tr>
