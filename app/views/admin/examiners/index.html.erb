<%= render partial: "#{@generic_view_path}/index_header" %>

<%= link_to 'Examiner Stats', statistics_admin_examiners_path, class: "btn btn-default" %>

<row>
  <h3>All examiners</h3>
</row>

<table class="admin_table table table-striped table-bordered dataTable">
  <thead>
    <tr>
      <th>#</th>
      <th>Name</th>
      <th>Email</th>
      <th>Lang</th>
      <th>Rank</th>
      <th>Status</th>
      <th>Max assessment time</th>
      <th>Current Evals</th>
      <th>Free assessment time</th>
      <th>Next available date</th>
    </tr>
  </thead>

  <tbody>
    <% @resources.each do |examiner| %>
      <tr>
        <td><%= link_to examiner.id, send(@resource_edit_path, examiner) %></td>
        <td><%= link_to examiner.name, send(@resource_edit_path, examiner) %></td>
        <td><%= link_to examiner.email, send(@resource_edit_path, examiner) %></td>
        <td><%= link_to examiner.language, send(@resource_edit_path, examiner) %></td>
        <td><%= examiner.rank %></td>
        <td><%= examiner.trusted? ? '<span class="label label-success">trusted</span>'.html_safe : examiner.status %></td>
        <% time_minutes = examiner.max_assessment_time %>
        <td><%= "#{time_minutes.minutes / 1.days}d #{Time.at(time_minutes.minutes).utc.strftime("%Hh %Mmin")}" %></td>
        <td><%= @nb_examiner_evals_count[examiner.id] %></td>
        <% free_time = (examiner.max_assessment_time - @examiners_taken_time[examiner.id].to_i) %>
        <% free_time = (free_time < 0 || !examiner.available?) ? 0 : free_time %>
        <td>
          <% available = false %>
          <% style = "" %>
          <% if free_time >= @max_assessment_time || !(examiner.trusted? || examiner.active?) %>
            <% available = true %>
          <% elsif  free_time >= @min_assessment_time %>
            <% style = "color: #ffa64d; font-weight: bold"%>
          <% else %>
            <% style="color:  #ff8566; font-weight: bold" %>
          <% end %>
          <p style="<%=style%>">
            <%= display_time_in_days_hours_minutes(free_time) %>
          <p>

        </td>
        <td>
          <%= examiner.max_assessment_time_release_date unless available
          %>
        </td>
      </tr>
    <% end %>
  </tbody>

  <tfoot>
    <tr>
      <th>#</th>
      <th>Name</th>
      <th>Email</th>
      <th>Lang</th>
      <th>Rank</th>
      <th>Status</th>
      <th>Max assessment time</th>
      <th>Current evals</th>
      <th>Free assessment time</th>
      <th>Next available date</th>
    </tr>
  </tfoot>

</table>
