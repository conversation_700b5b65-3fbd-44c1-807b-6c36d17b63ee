<p>Based on previous 3 months of assessed evaluations</p>
<table class="admin_table table table-striped table-bordered dataTable" data-order="[4]">
  <thead>
    <tr>
      <th>Examiner</th>
      <th>Status</th>
      <th>Language</th>
      <th>Nb assessed eval</th>
      <th>Percentage of overdue</th>
      <th>Rolling Weekly Workload</th>
      <th>Max Assessment Time</th>
      <th>Avg Max Assessment Time</th>
      <th>% Grade Discrepancy >= 15 pts</th>
    </tr>
  </thead>
  
  <tbody>
    <% @examiners.each do |examiner| %>
      <% nb_assessed_eval = @nb_eval_assessed[examiner.id].to_i %>
      <tr>
        <td> <%= link_to examiner.name, admin_examiner_url(examiner.id) %> </td>
        <td> <%= examiner.status %> </td>
        <td> <%= examiner.language %> </td>
        <td> <%= nb_assessed_eval %> </td>
        <td> 
          <%= ((@nb_eval_assessed_overdued[examiner.id].to_f / (nb_assessed_eval + 0.0001)) * 100).round %>% 
        </td>
        <td> 
          <% percent = (examiner.allocated_capacity_rate * 100).truncate %>
          <%= percent > 100 ? 100 : percent %>% 
        </td>
        <td data-order=<%= examiner.max_assessment_time %>> 
          <%= "#{examiner.max_assessment_time / 60}h #{examiner.max_assessment_time % 60}min" %>
        </td>
        <td>
          <% avg_max_ass_time = Statistic.examiner_average_max_assessment_time(examiner.id, 3.months.ago) %>
          <%= "#{avg_max_ass_time / 60}h #{avg_max_ass_time % 60}min" %>
        </td>

        <% percent = ((@eval_with_big_delta[examiner.id].to_f / (@total_evals_per_exam[examiner.id].to_f + 0.0001)) * 100).round %>
        <td data-order=<%= percent %> > <%= percent %>% of (<%= @total_evals_per_exam[examiner.id].to_i %> Evals)
        </td>
    </tr>
    <% end %>
  </tbody>

  <tfoot>
    <tr>
      <th>Examiner</th>
      <th>Status</th>
      <th>Language</th>
      <th>Nb assessed eval</th>
      <th>Percentage of overdue</th>
      <th>Rolling Weekly Workload</th>
      <th>Max Assessment Time</th>
      <th>Avg Max Assessment Time</th>
      <th>% Grade Discrepancy >= 15 pts</th>
    </tr>
  </tfoot>

</table>