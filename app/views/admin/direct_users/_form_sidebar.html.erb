<% if @resource.persisted? %>
    <h3>Actions</h3>
    <p><%= link_to 'Refresh Pipplet-clients data', refresh_pclients_data_admin_direct_user_path(@resource), method: :put, class: 'btn btn-info btn-block' %></p>

    <p><%= link_to 'Refresh static authentication token', generate_static_authentication_token_admin_direct_user_path(@resource), method: :put, class: 'btn btn-info btn-block' %></p>

    <% brand_name = @resource.brand_name %>
    <p><%= link_to 'Direct user creation form', connect_users_url(subdomain: brand_name, c: @resource.pipplet_clients_campaign_hashed_id), class: 'btn btn-success btn-block' %></p>

    <p><%= link_to 'Pipplet Clients: Campaign', PippletClientsApi.api_url + "/campaigns/#{@resource.pipplet_clients_campaign_id}/edit", class: 'btn btn-success btn-block' %></p>
    <p><%= link_to 'Pipplet Clients: Account (admin)', PippletClientsApi.api_url + "/admin/accounts/#{@resource.pipplet_clients_account_id}/edit", class: 'btn btn-success btn-block' %></p>

    <p><%= link_to 'Delete '+@resource_beautiful_name , send(@resource_path, @resource), method: :delete, class: 'btn btn-danger btn-block', data: { confirm: 'Are you sure?' }  %></p>
<% end %>
