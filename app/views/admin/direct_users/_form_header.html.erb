<% if @resource.persisted? %> 
<div class="col-md-10"> 
    <nav><ol class='cd-multi-steps text-center'>
    <% @resource.class.statuses.each do |s,v| %>
    <%= case
        when @resource.status == s
            "<li class='current'><div>#{s.humanize}</div>".html_safe
        when s == 'active'
            '<li>'.html_safe+link_to(s.humanize, activate_admin_direct_user_path(@resource), method: :put)
        when s == 'expired'
            '<li>'.html_safe+link_to(s.humanize, expire_admin_direct_user_path(@resource), method: :put)
        else 
            "<li><div>#{s.humanize}</div>".html_safe
        end%></li>
    <% end %>
    </ol></nav>
</div>
<div class="col-md-2"> 
    <div class="text-center"> 
        <% if @resource.is_usable? %> 
        <div class="btn btn-success"><%= @resource_beautiful_name %> is usable!</div>
        <% else %>
        <div class="btn btn-danger"><%= @resource_beautiful_name %> is not usable!</div>
        <% end %>
    </div>
</div>
<% end %>