<% if !@resource.persisted? %>
  <%= form_section('Mandatory references') %>
  <%= form_select_with_prompt_field(f, :test_profile_id,
                                    TestProfile.order(name: :asc), :id, :name) %>
  <%= form_text_field(f, :user_id) %>
  <%= form_select_with_prompt_field(f, :test_language,
                                    AVAILABLE_TEST_LANGUAGES, :to_s, :to_s) %>

  <%= form_section('Optional references') %>
  <%= form_select_with_prompt_field(f, :direct_user_id,
                                    DirectUser.order(pipplet_clients_campaign_id: :asc), :id, :name) %>
  <%= form_select_with_prompt_field(f, :api_user_id,
                                    ApiUser.order(id: :asc), :id, :name) %>
  <%= form_text_field(f, :pipplet_clients_campaign_id) %>

<% else %>

  <!-- Nav tabs -->
  <ul class="nav nav-tabs" role="tablist">
    <li class="active"><a href="#assessment" role="tab" data-toggle="tab">Assessment</a></li>
    <li><a href="#configuration" role="tab" data-toggle="tab">Configuration</a></li>
    <li><a href="#user" role="tab" data-toggle="tab">User</a></li>
    <li><a href="#source" role="tab" data-toggle="tab">Source</a></li>
    <li><a href="#apiorder" role="tab" data-toggle="tab">Api Order</a></li>
    <li><a href="#test_instance_validation_detail" role="tab" data-toggle="tab">TI Validation</a></li>
    <li><a href="#technical_issues" role="tab" data-toggle="tab">Technical Issues</a></li>
    <li><a href="#tags" role="tab" data-toggle="tab">Tags</a></li>
    <% if @resource.perform_identity_check? %>
      <li><a href="#identity" role="tab" data-toggle="tab">Identity</a></li>
    <% end %>
  </ul>

  <!-- Tab panes -->
  <div class="tab-content">

    <div class="tab-pane active" id="assessment">
      <div class="form-group">
        <label class="col-sm-4 control-label">Test Profile</label>
        <div class="col-sm-7">
          <%= link_to @resource.test_profile.name, admin_test_profile_path(@resource.test_profile), class: 'btn btn-default' %>
        </div>
      </div>

      <%= form_text_field(f, :question_set_id) %>

      <div class="form-group">
        <label class="col-sm-4 control-label">Service | Subservice</label>
        <div class="col-sm-7">
          <input data-test='pipplet-clients-service-sub-service-names' class="form-control" type="text" value="<%= @resource.api_order&.service_name %> | <%= @resource.api_order&.sub_service_name %>" readonly="readonly">
        </div>
      </div>

      <%= form_section('Questions answered and NextQuestionables') %>
      <% count_of_questions_answered = @resource.count_of_questions_answered %>
      <% count_of_questions_to_answer = @resource.count_of_questions_to_answer %>

      <p>Questions answered:
        <% if @resource.has_answered_enough_questions? %>
          <span class="btn btn-success center"><%= count_of_questions_answered %>
            of <%= count_of_questions_to_answer %></span>
        <% else %>
          <span class="btn btn-danger btn-xs"><%= count_of_questions_answered %>
            of <%= count_of_questions_to_answer %></span>
        <% end %>
        required</p>

      <table class="admin_table table table-striped table-bordered" style="margin-top: 20px;">
        <thead>
        <tr>
          <th>Production</th>
          <th>Challenge</th>
          <th>Type</th>
          <th>Actions</th>
          <th>Display date</th>
          <th>Time to answer / Allowed</th>
          <th>Status</th>
          <th>Answer</th>
          <th></th>
        </tr>
        </thead>
        <tbody>
        <% @resource.productions.order(created_at: :asc).each do |production| %>
          <tr>
            <td><%= link_to "Id: #{production.id}", edit_production_content_admin_production_path(production) %></td>
            <td><%= link_to "#{production.challenge.linguist_id} (id:#{production.challenge_id})".html_safe, production_admin_challenge_path(production.challenge_id) + "?utf8=✓&lang=#{@resource.test_language}" %></td>
            <td><%= production.has_recording? ? 'audio' : 'text' %></td>
            <td>
              <%= link_to 'Redo!',
                          redo_questionable_admin_test_instance_path(@resource, test_instance: { questionable_id: production.id, questionable_type: 'production' }),
                          method: :put,
                          data: { confirm: @resource.get_delivered_eval.present? ? "Warning: Evaluation Already Sent.\r\n\r\nAn evaluation has already been sent for this candidate.\r\nAre you sure you want to perform a redo?" : nil },
                          class: 'btn btn-info btn-xs' %>
            </td>
            <td><%= production.displayed_at %></td>
            <td><%= production.time_to_answer.round(1) %> sec/<%= production.duration / 1000 %>sec</td>
            <td><%= production.status %></td>
            <td><%= production_data(production) %></td>
            <td><%= link_to 'Delete', admin_production_path(production, redirect_to: edit_admin_test_instance_path(@resource.id)), method: :delete, class: 'btn btn-danger btn-xs', data: { confirm: 'Are you sure?' } %></td>
          </tr>
        <% end %>

        <tr>
          <td colspan="9">
            <span style="font-size:15px;line-height:30px;">Next questionables</span>
            <%= link_to 'Recompute Next Questionables', recompute_next_questionables_admin_test_instance_path(@resource), method: :put, class: 'btn btn-info btn-xs', style: "float: right;" %>
            <%= link_to "Delete all redos", bulk_destroy_admin_next_questionables_path(next_questionables_ids: @resource.next_questionables.pluck(:id)), method: :post, class: 'btn btn-info btn-xs', data: { confirm: 'Are you sure? All redos will be deleted.' }, style: "float: right; margin-right: 10px;" %>
            <%= link_to 'Full reset (no backup)',
                        full_reset_admin_test_instance_path(@resource),
                        method: :put,
                        class: 'btn btn-danger btn-xs',
                        data: { confirm: @resource.get_delivered_eval.present? ? "Warning: Evaluation Already Sent.\r\n\r\nAn evaluation has already been sent for this candidate.\r\nAre you sure you want to perform a full reset?" : 'Are you sure? All productions and receptions will be deleted, without any backup!!' },
                        style: "float: right; margin-right: 10px;" %>
          </td>
        </tr>

        <% @resource.next_questionables.each_with_index do |nq, _i| %>
          <tr>
            <td><%= "Id: #{nq.questionable_id}" %></td>
            <td><%= link_to "#{nq.challenge.linguist_id} (id:#{nq.challenge_id})".html_safe, production_admin_challenge_path(nq.challenge_id) %></td>
            <td><%= nq.production.has_recording? ? 'audio' : 'text' %></td>
            <td colspan="5"></td>
            <td><%= link_to 'Delete', admin_next_questionable_path(nq, redirect_to: edit_admin_test_instance_path(@resource.id)), method: :delete, class: 'btn btn-danger btn-xs', data: { confirm: 'Are you sure?' } %></td>
          </tr>
        <% end %>
        </tbody>
      </table>

      <% if @resource.result_rating.present? %>
        <div class="form-group">
          <div class="col-sm-4 control-label font-weight-bold">
            Result
          </div>
          <div class="col-sm-7">
            <pre><%= get_cecrl_score(@resource.result_rating) %> | <%= @resource.result_rating %></pre>
          </div>
        </div>
      <% end %>
      <% if @resource.average_grades.where(label: 'overall').present? %>
        <div class="form-group">
          <div class="col-sm-4 control-label font-weight-bold">
            Average Grade
          </div>
          <div class="col-sm-7">
            <pre><%= @resource.average_grade_of_trusted_examiners %></pre>
          </div>
        </div>
      <% end %>

      <table class="admin_table table table-striped table-bordered dataTable">
        <thead>
        <tr>
          <th>Evaluation id</th>
          <th>Evaluation Status</th>
          <th>Examiner</th>
          <th>Evaluation Goal</th>
          <th>Score</th>
          <% if @resource.average_grades.where(label: 'overall').present? %>
            <th>Vs. Average</th>
          <% end %>
        </tr>
        </thead>
        <tbody>
        <% @resource.evaluations.each do |evaluation| %>
          <tr>
            <td><a> <%= link_to controller: :evaluations, action: "edit", id: evaluation.id do %>
                <strong> <%= "##{evaluation.id}" %> </strong>
              <% end %> </a>
            </td>
            <td width="20%">
              <%= render partial: "admin/evaluations/evaluation_advancment", locals: { evaluation: evaluation } %>
              <%= evaluation.status %>
            </td>
            <td><a> <%= link_to controller: :examiners, action: "edit", id: evaluation.examiner.id do %>
                <strong> <%= evaluation.examiner.name %> </strong>
              <% end %> </a>
              <% if evaluation.examiner.trusted? %>
                <i class="glyphicon glyphicon-ok" style="color: green; margin-left: 3px"></i>
              <% end %>
            </td>
            <td><%= evaluation.evaluation_goal %> </td>
            <td>
              <% begin %>
                <%= evaluation.certificate.grades.overall.last.score %>
                (<%= evaluation.certificate.grades.overall.last.cecrl_score %>)
              <% rescue %>
                NA
              <% end %>
            </td>
            <% if @resource.average_grades.where(label: 'overall').present? && evaluation.certificate.present? && (not evaluation.certificate.grades.overall.empty?) %>
              <td>
                <% diff = evaluation.certificate.grades.overall.last.score - @resource.average_grades.overall.last.score %>
                <% if diff.present? %>
                  <%= diff > 0 ? "+#{diff}" : "#{diff}" %>
                <% else %>
                  ''
                <% end %>
              </td>
            <% end %>
          </tr>
        <% end %>
        </tbody>
      </table>

    </div><!-- /questions -->


    <div class="tab-pane" id="configuration">
      <%= form_section('Skip some steps') %>
      <%= form_check_box(f, :skip_audio_test, label = 'Skip hardware check') %>
      <%= form_check_box(f, :skip_tutorial) %>
      <%= form_check_box(f, :skip_identity_check) %>
      <%= form_check_box(f, :skip_browser_check) %>
      <%= form_check_box(f, :skip_email) %>
      <%= form_check_box(f, :monitored) %>

      <%= form_section('Email reminders') %>
      <%= form_check_box(f, :send_reminders) %>
      <%= form_text_readonly(f, :count_of_reminders_sent) %>
      <%= form_text_readonly(f, :last_reminder_sent_at) %>

      <%= form_section('SMS reminders') %>
      <%= form_check_box(f, :sms_reminders) %>
      <%= form_text_readonly(f, :count_of_sms_reminders) %>
      <%= form_text_readonly(f, :last_sms_reminder_sent_at) %>

      <%= form_section('Internals') %>
      <%= form_text_readonly(f, :uuid) %>
      <%= form_text_readonly(f, :created_at) %>
      <%= form_text_readonly(f, :updated_at) %>
      <%= form_text_readonly(f, :deleted_at) %>

      <%= form_section('Additonal Time') %>
      <%= form_text_field(f, :time_multiplier) %>
    </div><!-- /configuration -->


    <div class="tab-pane" id="user">
      <div class="form-group">
        <label class="col-sm-4 control-label">User</label>
        <div class="col-sm-7">
          <%= link_to @resource.user.full_name_with_id, admin_user_path(@resource.user), :class => 'btn btn-default' %>
        </div>
      </div>

      <div class="col-sm-10 col-sm-offset-2">
        <dl class="dl-horizontal">
          <dt>Name</dt>
          <dd><%= @resource.user.full_name %></dd>
          <dt>Email:</dt>
          <dd><%= @resource.user.email %></dd>
          <dt>Phone:</dt>
          <dd><%= @resource.user.phone_number %></dd>
          <dt>Status:</dt>
          <dd><%= @resource.user.status %></dd>
          <dt>Created at:</dt>
          <dd><%= @resource.user.created_at %></dd>
          <dt>Last seen:</dt>
          <dd><%= @resource.user.current_sign_in_at %></dd>
          <dt>Group:</dt>
          <dd><%= @resource.user.group %></dd>
        </dl>
      </div>
    </div><!-- /user -->

    <div class="tab-pane" id="source">
      <% if @resource.direct_user %>
        <div class="form-group">
          <label class="col-sm-4 control-label">Direct User</label>
          <div class="col-sm-7">
            <%= link_to @resource.direct_user.name, admin_direct_user_path(@resource.direct_user), class: 'btn btn-default' %>
          </div>
        </div>
      <% end %>

      <% if @resource.api_user %>
        <div class="form-group">
          <label class="col-sm-4 control-label">Api User</label>
          <div class="col-sm-7">
            <%= link_to @resource.api_user.name, admin_api_user_path(@resource.api_user), class: 'btn btn-default' %>
          </div>
        </div>
      <% end %>

      <% if @resource.pipplet_clients_campaign_id.present? %>
        <div class="form-group">
          <label class="col-sm-4 control-label">Pipplet Clients</label>
          <div class="col-sm-7">
            <%= link_to "Campaign #{@resource.pipplet_clients_campaign_id}", "#{PippletClientsApi.api_url}/admin/campaigns/#{@resource.pipplet_clients_campaign_id}", class: 'btn btn-default' %>
          </div>
        </div>
      <% end %>
      <% if @resource.client_id.present? %>
        <div class="form-group">
          <label class="col-sm-4 control-label">Account id</label>
          <div class="col-sm-7">
            <%= link_to "Account #{@resource.client_id}", "#{PippletClientsApi.api_url}/admin/accounts/#{@resource.client_id}/edit", class: 'btn btn-default' %>
          </div>
        </div>
      <% end %>
      <% if !@resource.api_user && !@resource.direct_user && !@resource.pipplet_clients_campaign_id %>
        No link to ApiUser, DirectUser or PippletClients
      <% end %>

      <%= form_text_readonly(f, :source_type) %>
      <%= form_text_readonly(f, :client_type) %>
      <%= form_text_field(f, :client_name) %>
      <%= form_text_readonly(f, :client_contact_email) %>
      <%= form_text_readonly(f, :client_contact_first_name) %>
      <%= form_text_readonly(f, :client_contact_last_name) %>
      <%= form_text_readonly(f, :client_contact_locale) %>
    </div><!-- /source -->


    <div class="tab-pane" id="apiorder">
      <% if @resource.api_order %>
        <% @api_order = @resource.api_order %>
        <div class="form-group">
          <label class="col-sm-4 control-label">Api Order</label>
          <div class="col-sm-7">
            <%= link_to "Api Order #{@api_order.id}", admin_api_order_path(@api_order), class: 'btn btn-default' %>
          </div>
        </div>

        <%= form_section('Order details') %>
        <% begin %>
          <pre><%= JSON.pretty_generate(@api_order.order_information) %></pre>
        <% rescue %>
          <pre><%= JSON.pretty_generate(URI.decode_www_form(@api_order.order_information).to_h) %></pre>
        <% end %>

        <%= form_section('Order errors') %>
        <pre><%= @api_order.order_errors %>&nbsp;</pre>

      <% else %>
        No API Order for this test instance.
      <% end %>
    </div><!-- /apiorder -->

    <div class="tab-pane" id="test_instance_validation_detail">
      <% if @resource.test_instance_validation_detail.present? %>
        <%= render partial: "/admin/test_instances/test_instance_validation_detail", locals: { test_instance: @resource } %>
      <% else %>
        No validation details for this test instance.
      <% end %>
    </div> <!-- /validation details -->

    <div class="tab-pane" id="technical_issues">
      <% if @resource.technical_issues.present? %>
        <table class="admin_table table table-striped table-bordered dataTable" style="margin-top: 20px;">
          <thead>
          <tr>
            <th>Id</th>
            <th>Status</th>
            <th>Created at</th>
            <th> Actions</th>
          </tr>
          </thead>
          <tbody>
          <% @resource.technical_issues.order(created_at: :asc).each do |technical_issue| %>
            <tr>
              <td> <%= technical_issue.id %> </td>
              <td> <%= technical_issue.status %> </td>
              <td> <%= technical_issue.created_at %> </td>
              <td>
                <% if technical_issue.status != 'closed' %>
                  <%= link_to 'Close',
                              admin_technical_issue_path(id: technical_issue.id, technical_issue: { status: 'closed' }),
                              method: :put,
                              class: 'btn btn-danger btn-xs',
                              data: { confirm: "[EXPERT MODE ONLY] A n'utiliser qu'en cas de test plan ou de cas très particulier" }
                  %>
                <% else %>
                  <%= link_to 'Re-open',
                              admin_technical_issue_path(id: technical_issue.id, technical_issue: { status: 'ongoing' }),
                              method: :put,
                              class: 'btn btn-danger btn-xs',
                              data: { confirm: "[EXPERT MODE ONLY] A n'utiliser qu'en cas de test plan ou de cas très particulier" }
                  %>
                <% end %>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>
      <% else %>
        No Technical Issues for this test instance.
      <% end %>
    </div><!-- /technical_issues -->

    <div class="tab-pane" id="tags">
      <%= form_collection_check_boxes(f, :tag_ids, @tags, :id, :name) do |b|
        b.check_box(style: 'margin-right: 1px') + b.label(style: 'margin-left: 2px') + tags_labels(b.object)
      end
      %>
    </div>

    <% if @resource.perform_identity_check? %>
      <div class="tab-pane" id="identity">
        <!-- Status of perform_identity_check with a green check mark if true -->
        <div class="form-group">
          <label class="col-sm-4 control-label">Perform Identity Check</label>
          <div class="col-sm-7">
            <% if @resource.perform_identity_check? %>
              <i class="glyphicon glyphicon-ok" style="color: green; margin-left: 3px"></i>
            <% else %>
              <i class="glyphicon glyphicon-remove" style="color: red; margin-left: 3px"></i>
            <% end %>
          </div>
        </div>
        <!-- Status of need_strong_id_check? with a green check mark if true -->
        <div class="form-group">
          <label class="col-sm-4 control-label">Need Strong ID Check</label>
          <div class="col-sm-7">
            <% if @resource.need_strong_id_check? %>
              <i class="glyphicon glyphicon-ok" style="color: green; margin-left: 3px"></i>
            <% else %>
              <i class="glyphicon glyphicon-remove" style="color: red; margin-left: 3px"></i>
            <% end %>
          </div>
        </div>

        <!-- Status of identity_status of the @resource.user object -->
        <div class="form-group">
          <label class="col-sm-4 control-label">User Identity Status</label>
          <div class="col-sm-7">
            <%= @resource.user.identity_status %>
          </div>
        </div>

        <!-- stripe_identity_status element, to be updated by Ajax, default is 'waiting to be updated' -->
        <div class="form-group">
          <label class="col-sm-4 control-label">Stripe Identity Status</label>
          <div class="col-sm-7">
            <% if @resource.need_strong_id_check? %>
              <span id="stripe_identity_status_result"><%= @resource.user.identity_status %></span>
              <% unless @resource.user.verified? %>
                <button class="btn btn-default" id="stripe_identity_status" data-url=<%= get_stripe_identity_verification_status_admin_user_path(@resource.user.id) %>>Get
                  Stripe Identity result
                </button>
              <% end %>
            <% else %>
              No strong ID check
            <% end %>
          </div>
        </div>

        <!-- get_check_face_matching_similarity result. By default a button saying "Get face match result" When clicked this button will update the content with the result-->
        <div class="form-group">
          <label class="col-sm-4 control-label">Face Matching Similarity</label>
          <div class="col-sm-7">
            <% if @resource.need_strong_id_check? && !@resource.user.verified? %>
              <button class="btn btn-default" id="get_check_face_matching_similarity" data-url=<%= get_check_face_matching_similarity_admin_test_instance_path(@resource.id) %>>Get
                face match result
              </button>
              <span id="check_face_matching_similarity_result">
                <% if @resource.need_strong_id_check? %>
                  Waiting...
                <% else %>
                  No strong ID check
                <% end %>
              </span>
            <% end %>
          </div>
        </div>


        <div class="form-group">
          <label class="col-sm-4 control-label">Stripe identity Verification Data</label>
          <div class="col-sm-7">
            <%= link_to 'Stripe Verification Data', @resource.user.get_stripe_identity_verification_url, :class => 'btn btn-default btn-block', target: "_blank" %>
          </div>
        </div>

        <div class="form-group">
          <label class="col-sm-4 control-label">S3 Identity Check images</label>
          <div class="col-sm-7">
            <%= link_to 'S3 Identity Check', "https://s3.console.aws.amazon.com/s3/buckets/#{ENV.fetch('AWS_IDENTITY_BUCKET_NAME')}/#{Rails.env}/ti#{@resource.id}/", :class => 'btn btn-default btn-block', target: "_blank" %>
            <% @resource.identity_photos_urls.each do |photo_url| %>
              <%= image_tag photo_url, width: '70px' %>
            <% end %>
          </div>
        </div>

        <div class="form-group">
          <label class="col-sm-4 control-label">Fallback images</label>
          <div class="col-sm-7">
            <%= link_to 'Fallback images', admin_images_path({ attachable_type: 'TestInstance', attachable_id: @resource.id }), :class => 'btn btn-default btn-block', target: "_blank" %>
          </div>
        </div>
      </div> <!-- /identity -->
    <% end %>

  </div><!-- /tabs -->

<% end %>
