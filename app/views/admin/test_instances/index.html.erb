<%= render partial: "#{@generic_view_path}/index_header" %>

<%= form_tag(admin_test_instances_url, method: :get, :class => 'form-horizontal row') do %>

  <div class="form-group">
  <label class="control-label col-sm-2">Email</label>
    <div class="col-sm-10">
  <%= text_field_tag 'email', params[:email] %>
      </div>
  </div>

  <div class="form-group">
  <label class="control-label col-sm-2">Group</label>
    <div class="col-sm-10">
  <%= text_field_tag :group, params[:group] %>
    </div>
  </div>

  <div class="form-group">
  <label class="control-label col-sm-2">User Token</label>
    <div class="col-sm-10">
  <%= text_field_tag 'confirmation_token', params[:confirmation_token] %>
    </div>
  </div>


  <div class="form-group">
  <label class="control-label col-sm-2">Api user id</label>
    <div class="col-sm-10">
  <%= text_field_tag 'api_user_id', params[:api_user_id] %>
      </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Test Profile</label>
    <div class="col-sm-3">
      <%= select_tag("test_profile_id", options_from_collection_for_select(@test_profiles, "id", "name", @test_profile_id), include_blank: 'Any Test Profile', style: 'width:100%') %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Test Instance Status</label>
    <div class="col-sm-3">
      <%= select_tag('status', options_for_select(@statuses, @status), {multiple: true, style: "width: 100%"}) %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Test Language</label>
    <div class="col-sm-3">
      <%= select_tag('test_language', options_for_select(@test_languages, @test_language), {multiple: true, style: "width: 100%"}) %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Monitored</label>
      <div class="col-sm-10">
        <label class="radio-inline">
          <%= radio_button_tag 'monitored', '', @monitored.to_s.empty? %>
          Any
        </label>
        <label class="radio-inline">
          <%= radio_button_tag 'monitored', false, @monitored == false %>
          False
        </label>
        <label class="radio-inline">
          <%= radio_button_tag 'monitored', true, @monitored == true %>
          True
        </label>
      </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Test Mode</label>
    <div class="col-sm-10">
      <label class="radio-inline">
        <%= radio_button_tag 'test_mode', '', @test_mode.to_s.empty? %>
        Any
      </label>
      <label class="radio-inline">
        <%= radio_button_tag 'test_mode', false, @test_mode == false %>
        False
      </label>
      <label class="radio-inline">
        <%= radio_button_tag 'test_mode', true, @test_mode == true %>
        True
      </label>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Date filters</label>
      <div class="col-sm-10">
        <%= select_tag('date_filter', options_for_select(%w[created_at begin_date end_date last_tekis_email_send_at sent_for_evaluation_date last_reminder_sent_at], params[:date_filter])) %>
        <span>
          From
          <%= date_field_tag 'beginning_date', params[:beginning_date] %>
        </span>
        <span>
          To
          <%= date_field_tag 'ending_date', params[:ending_date] %>
        </span>
      </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Sort By</label>
    <div class="col-sm-10">
      <label class="radio-inline">
        <%= radio_button_tag 'ordering', 'created_at', @ordering=='created_at' %>
        Last Created
      </label>
      <label class="radio-inline">
        <%= radio_button_tag 'ordering', 'updated_at', @ordering!='created_at' %>
        Last Updated
      </label>
      <label class="radio-inline">
        <%= radio_button_tag 'ordering', 'sent_for_evaluation_date', @ordering=='sent_for_evaluation_date' %>
        Last Sent For Evaluation
      </label>
      <label class="radio-inline">
        <%= radio_button_tag 'ordering', 'graded_at', @ordering=='graded_at' %>
        Last Graded
      </label>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Limit</label>
    <div class="col-sm-10">
      <% @limits.each do |limit| %>
        <label class="radio-inline">
          <%= radio_button_tag 'limit', limit, @limit == limit %> <%= limit %>
        </label>
      <% end %>
    </div>
  </div>

  <%= submit_tag 'Search', :class => 'col-sm-offset-2 col-sm-3 btn btn-primary' %>
<% end %>


<table class="admin_table table table-striped table-bordered dataTable">
  <thead>
    <tr>
      <th>#</th>
      <th>User</th>
      <th>Test profile</th>
      <th>Lang</th>
      <th>Group</th>
      <th>Creation</th>
      <th>Begin date</th>
      <th>End date</th>
      <th>Has time multiplier ?</th>
      <th>Status</th>
      <th>Delivered Grade</th>
    </tr>
  </thead>

  <tbody>
    <% @resources.each do |ti| %>
      <tr>
        <td><%= link_to ti.id, send(@resource_edit_path, ti) %><% if ti.anonymized? %> <span class="label label-default">Anonymized</span><% end %></td>
        <td><%= link_to ti.user.full_name_with_id+" "+ti.user.email, send(@resource_edit_path, ti) %><% if ti.user.anonymized? %> <span class="label label-default">Anonymized</span><% end %></td>
        <td><%= link_to ti.test_profile.name, admin_test_profile_url(ti.test_profile_id) %></td>
        <td><%= link_to ti.test_language, send(@resource_edit_path, ti) %></td>
        <td><%= link_to ti.user.group, send(@resources_path, {group: ti.user.group}) %></td>
        <td><%= ti.created_at.to_formatted_s(:admin) if !ti.created_at.nil?%></td>
        <td><%= ti.begin_date.to_formatted_s(:admin) if !ti.begin_date.nil?%></td>
        <td><%= ti.end_date.to_formatted_s(:admin) if !ti.end_date.nil? %></td>
        <td><%= ti.time_multiplier != 1 ? 'yes' : 'no' %></td>
        <td><%= ti.status %></td>
        <td><%= ti.overall_grade %></td>
      </tr>
    <% end %>
  </tbody>

  <tfoot>
    <tr>
      <th>#</th>
      <th>User</th>
      <th>Test profile</th>
      <th>Lang</th>
      <th>Group</th>
      <th>Creation</th>
      <th>Begin date</th>
      <th>End date</th>
      <th>Has time multiplier ?</th>
      <th>Status</th>
      <th>Delivered Grade</th>
    </tr>
  </tfoot>
</table>
