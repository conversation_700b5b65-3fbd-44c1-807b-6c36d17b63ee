<div>
  <div class="row">

    <% ([@ti] + @duplicates).each_with_index do |ti, ind| %>
      <% if ind != 0 and (ind % 3) == 0 %>
        </div>
        <div class="row">
      <% end %>
      <div class="col-md-4">
        <%= render partial: "duplicate_comparator_test_instance_card", locals: {test_instance: ti, main_test_instance: ind == 0} %>
        <%= render partial: "duplicate_comparator_evaluations_card", locals: {test_instance: ti, evaluations: ti.evaluations, main_test_instance: ind == 0} %>
      </div>
    <% end %> 

  </div>
</div>