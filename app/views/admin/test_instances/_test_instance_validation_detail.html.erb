<% if test_instance.test_instance_validation_detail.passed? %>
    <b> The test_instance validations:
      <b style="color:green"> passed </b>
    </b>
<% else %>
    <b> The test_instance validations:
      <b style="color:red"> failed </b>
    </b>
<% end %>
<br>
<br>

<% test_instance.test_instance_validation_detail.validation_details_sym.each do |key, details| %>
  <div>
    <b style="color: <%= details[:passed] ? 'green' : 'red' %>"> <%= key %> : </b>
    <br>
    <% if details[:details].empty? %>
      <p style="margin-left: 20px;"> None </p>
    <% else %>
      <% details[:details].each do |detail_message| %>
       <p style="margin-left: 20px;"> <%= detail_message %> </p>
      <% end %>
    <% end %>
  </div>
<% end %>
