<%= render partial: "#{@generic_view_path}/index_header" %>

<%= form_tag(admin_evaluations_url, method: :get, :class => 'form-horizontal row') do %>

  <div class="form-group">
    <label class="control-label col-sm-2">Evaluation Status</label>
    <div class="col-sm-10">
      <%= select_tag 'selected_evaluation_states', options_for_select(@eval_states, @selected_eval_states), { multiple: true, class: "admin_multi_select_search", style: "width: 200px" } %>
    </div>
  </div>


  <div class="form-group">
    <label class="control-label col-sm-2">Evaluation goal</label>
    <div class="col-sm-10">
      <%= select_tag 'selected_evaluation_goals', options_for_select(@eval_goals, @selected_eval_goals), { multiple: true, class: "admin_multi_select_search", style: "width: 200px" } %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Evaluation Tags</label>
    <div class="col-sm-10">
      <%= select_tag 'selected_tags', options_for_select(@eval_tags, @selected_eval_tags), { multiple: true, class: "admin_multi_select_search", style: "width: 200px" } %>
    </div>
  </div>


  <div class="form-group">
    <label class="control-label col-sm-2">TestInstance Status</label>
    <div class="col-sm-10">
      <%= select_tag 'selected_test_instance_statuses', options_for_select(@test_instance_status, @ti_selected_status), { multiple: true, class: "admin_multi_select_search", style: "width: 200px" } %>
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-sm-2">Limit</label>
    <div class="col-sm-10">
      <% @limits.each do |limit| %>
        <label class="radio-inline">
          <%= radio_button_tag 'limit', limit, @limit == limit %> <%= limit %>
        </label>
      <% end %>
    </div>
  </div>
  <div class="form-group">
    <label class="control-label col-sm-2">Language</label>
    <div class="col-sm-10">
      <%= select_tag 'search_language', options_for_select(AVAILABLE_TEST_LANGUAGES.map { |l| [l, l] }, @search_language), include_blank: true %>
    </div>
  </div>

  <%= submit_tag 'Search', :class => 'col-sm-offset-2 col-sm-3 btn btn-primary' %>
<% end %>

<table class="admin_table table table-striped table-bordered dataTable">
  <thead>
  <tr>
    <th>Evaluation</th>
    <th>TI</th>
    <th>Examiner</th>
    <th>Lang</th>
    <th>Status</th>
    <th>Tags</th>
    <th>Updated at</th>
    <th>Assigned at</th>
    <th>Evaluation Type</th>
    <th>Evaluation Goal</th>
    <th>Client Name</th>
    <th>Client Type</th>
    <th>Test Profile</th>
    <th>X</th>
  </tr>
  </thead>
  <tbody>
  <% @resources.each do |evaluation| %>
    <tr>
      <td>
        <%= link_to "##{evaluation.id}", controller: "evaluations", action: "edit", id: evaluation.id %>
      </td>
      <td>
        <%= link_to "##{evaluation.test_instance_id}", controller: "test_instances", action: "edit", id: evaluation.test_instance_id %>
      </td>
      <td>
        <%= link_to "#{evaluation.examiner.name}", edit_admin_examiner_path(evaluation.examiner_id) if evaluation.examiner %>
      </td>
      <td> <%= evaluation.test_language %> </td>
      <td>
        <%= render partial: "admin/evaluations/evaluation_advancment", locals: { evaluation: evaluation } %>
        <%= evaluation.status %>
      </td>
      <td>
        <% evaluation.tags.each do |tag| %>
          <%= "##{tag.name}" %>
        <% end %>
      </td>
      <td> <%= evaluation.updated_at %> </td>
      <td> <%= evaluation.assigned_at %> </td>
      <td> <%= @assessment_types[evaluation.assessment_type_id]&.name %> </td>
      <td> <%= evaluation.evaluation_goal %> </td>
      <td> <%= evaluation.test_instance.client_name %> </td>
      <td> <%= evaluation.test_instance.test_profile&.client_type %> </td>
      <td>
          <%= link_to_if evaluation.test_instance.test_profile.present?,
                         "#{evaluation.test_instance.test_profile&.name}",
                         edit_admin_test_profile_path(evaluation.test_instance.test_profile_id) %>
      </td>
      <td>
        <%= link_to admin_evaluation_path(evaluation), method: :delete, data: { confirm: 'Are you sure ?' } do %>
          <i class="glyphicon glyphicon-trash"></i>
        <% end %>
      </td>
    </tr>
  <% end %>
  </tbody>

  <tfoot>
  <tr>
    <th>Evaluation</th>
    <th>TI</th>
    <th>Examiner</th>
    <th>Lang</th>
    <th>Status</th>
    <th>Tags</th>
    <th>Updated at</th>
    <th>Assigned at</th>
    <th>Evaluation Type</th>
    <th>Evaluation Goal</th>
    <th>Client Name</th>
    <th>Client Type</th>
    <th>Test Profile</th>
    <th>X</th>
  </tr>
  </tfoot>
</table>
