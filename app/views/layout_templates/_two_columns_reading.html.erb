<div class="questionable row"> 
      <div class="main col-md-6">
          <% instructions = production.ordered_questions_for_zone(:instructions).first -%>
          <% if instructions  -%>
            <%= render template: instructions.template_name, locals: {question: instructions, production_or_reception: production} %>
          <% end %>

        <div class="question-set text-frame">
          <% production.ordered_questions_for_zone(:main).each do |question| -%>
            <div class="question question_<%=question.id%> <%=question.has_scorable_elements? ? 'scorable' : ''%> <%=production.has_error_for?(question.id) ? 'question_error' : ''%>">

                <%= render partial: '/questions/question_validation_error', locals: {question_id: question.id, questionable: production} %>
                <%= render template: question.template_name, locals: {f: f, question: question, questionable: production} %>

                <% if question.has_scorable_elements? && production.reception? %>
                  <%= render partial: '/questions/question_answer_not_provided', locals: {f: f, question: question, questionable: production} %>
                <% end %>
            </div>
          <% end -%>
        </div>
      </div>

      <div class="side col-md-6">
        <% production.ordered_questions_for_zone(:side).each do |question| -%>
            <div class="question question_<%=question.id%> <%=question.has_scorable_elements? ? 'scorable' : ''%> <%=production.has_error_for?(question.id) ? 'question_error' : ''%>">

            <%= render partial: '/questions/question_validation_error', locals: {question_id: question.id, questionable: production} %>
            <%= render template: question.template_name, locals: {f: f, question: question, questionable: production} %>
          </div>
        <% end -%>
        <%= render partial: '/questions/question_controls', locals: {questionable: production} %>
      </div>
  </div>
  <% if current_user && current_user.current_test_instance && current_user.current_test_instance.test_profile.show_skip_button %>
    <%= render partial: '/questions/skip_question'%>
  <% end %>

  <% if current_user && current_user.current_test_instance%>
    <%= render partial: "questions/skip_short_question_confirm_modal" %>
  <% end %>