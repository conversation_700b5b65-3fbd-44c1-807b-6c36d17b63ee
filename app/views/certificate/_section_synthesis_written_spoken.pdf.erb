<section id="synthesis">
  <h1><%= I18n.t("certificate.#{grade}.title.score") %></h1>
  <div class="line"></div>
  <div id="score">
    <% displayed_grades = [] %>
    <% @available_grades = evaluation.certificate.available_grades %>
    <% synthesis_grades = @available_grades.select { |grade| SYNTHESIS_GRADES.include?(grade.label) && grade.score.positive? }.index_by(&:label) %>
    <% if !synthesis_grades[grade].nil? %>
      <div class="flex-row-dir margin-20-bottom">
        <p></p>
        <p class="with-width"><strong><%= synthesis_grades[grade].cecrl_score %></strong></p>
        <progress class="full" max="100" value=<%= synthesis_grades[grade].score %>></progress>
        <% displayed_grades << synthesis_grades[grade] %>
      </div>
    <% else %>
      <div class="flex-row-dir margin-20-bottom">
        <p><%= I18n.t("certificate.synthesis.#{grade}.score") %></p>
        <p class="with-width"><strong>N/A</strong></p>
        <progress max="100" value="0" ></progress>
      </div>
    <% end %>
    <div class="flex-row-dir">
      <p></p>
      <p class="with-width"></p>
      <%= render partial: '/certificate/progress_meter_full' %>
    </div>
  </div>
  <div id="description">
    <% if evaluation.certificate.grades.where(:label => grade).present? %>
      <article id="<%= grade %>">
        <% if evaluation.certificate.grades.where(:label => grade).last.score != 0 %>
          <p class="align-with-score" >
            <%= I18n.t("certificate.writing.level.#{evaluation.certificate.grades.where(:label => grade).last.cecrl_score.downcase}") + " " + I18n.t("certificate.reading.level.#{evaluation.certificate.grades.where(:label => grade).last.cecrl_score.downcase}") %>
          </p>
        <% else %>
          <p class="align-with-score" >
            <%= I18n.t("cecrl.description.no_answer", user_first_name: user.first_name.split.map(&:capitalize).join(' ')) %>
          </p>
        <% end %>
      </article>
    <% end %>
  </div>
</section>
