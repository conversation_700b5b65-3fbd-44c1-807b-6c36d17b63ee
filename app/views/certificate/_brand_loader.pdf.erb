<%if brand.present? && brand.certificate_branded %>
    <%= wicked_pdf_proper_stylesheet("branded/application.branded.#{brand.compilation_name}.css") %>
    <% if brand.certificate_logo.present? %>
        <% @current_logo = brand.certificate_logo.url %>
    <% else %>
        <% @current_logo = brand.logo_file %>
    <% end %>
    <% @logo_brand = brand.logo_file.nil? ? "" : image_url(brand.logo_file) %>
    <% @logo_certificate = image_url(brand.certificate_logo.url) %>
<% else %>
    <%= stylesheet_link_tag wicked_pdf_asset_base64("branded/application.branded.Pipplet.css") %>
    <% @current_logo = 'logo_with_name_certif.svg' %>
<% end %>
<% @current_logo_url = image_url(@current_logo) %>
<style>
    @import "https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap";
</style>
