<html id="certificate">
  <head>
    <meta charset="utf-8">
    <% if evaluation.would_deliver_with_security_violation? %>
        <%= render partial: '/certificate/security_violation_banner' %>
    <% end %>
    <%= wicked_pdf_proper_stylesheet("certificate_portrait.css") %>
    <%= render partial: 'certificate/brand_loader', locals: { brand: brand } %>
  </head>
  <body id="pdf_certificate" class=<%= brand.nil? ? "" : brand.compilation_name %>>
    <header>
      <section>
        <article id="title">
          <%= render partial: 'certificate/brand_logo', locals: { brand: brand } %>
          <div id="titre">
            <strong><%= I18n.t("certificate.head") %></strong>
          </div>
        </article>
        <article id="user">
          <div id="user_detail">
            <%= render partial: 'certificate/user_details_standard', locals: { user: user, test_instance: test_instance, evaluation: evaluation } %>
            <div class="flex-row-dir">
              <p class="align-items-user">
                <strong><%= I18n.t("certificate.registration_number") %></strong>
              </p>
              <p>
                <%= test_instance.uuid %>
              </p>
            </div>
          </div>
          <%= render partial: 'certificate/user_note', locals: { cecrl_score: cecrl_score, test_instance: test_instance, is_partial: evaluation.check_if_partially_evaluated? } %>
        </article>
      </section>
    </header>
    <main>
      <%= render partial: 'certificate/section_synthesis_written_spoken', locals: { grade: 'spoken', user: user, evaluation: evaluation } %>
      <%= render partial: 'certificate/section_details_written_spoken', locals: { grade: 'spoken', user: user, evaluation: evaluation, brand: brand, test_instance: test_instance } %>
    </main>
    <%= render partial: 'certificate/footer', locals: { evaluation: evaluation, test_instance: test_instance, user: user, cecrl_score: cecrl_score, full_score: full_score, brand: brand } %>
  </body>
</html>
