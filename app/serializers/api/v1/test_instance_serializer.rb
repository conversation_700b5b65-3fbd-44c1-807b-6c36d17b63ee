class Api::V1::TestInstanceSerializer < Api::V1::BaseSerializer
  # ----------------------------------------
  # :section: TestInstance attributes
  # ----------------------------------------
  attributes :test_instance_uuid, :test_instance_id, :completion, :completion_date, :score, :report_url, :status, :technical_issues, :strong_identity_check

  def test_instance_id
    object.id
  end

  def test_instance_uuid
    object.uuid
  end

  def completion
    object.ratio_of_required_questions_answered
  end

  def completion_date
    object.end_date
  end

  def technical_issues
    {
      presence: object.has_in_progress_tekis?
    }
  end

  def score
    eval_delivered = object.get_delivered_eval
    {
      overall_score_rating: object.result_rating,
      overall_score_cecrl: object.result_rating.present? ? object.result_rating_to_cecrl : nil,
      overall_score_date: object.result_rating.present? ? object.graded_at.to_i : nil,
      score_details: eval_delivered&.certificate.present? ? eval_delivered.certificate.grades.map { |g| { "label" => g.label.capitalize, "score" => g.score } } : []
    }
  end

  def report_url
    if object.has_valid_report?
      object.report.url
    end
  end

  def status
    # TestInstance States
    # state :initialized, :initial => true  # Object has been created
    # state :ready                          # Test is ready to be answered by the user
    # state :connected                      # User connected to the interface for this test
    # state :audio_tested                   # User tested audio
    # state :tutored                        # User viewed the tutorial
    # state :in_progress                    # User started answering questions
    # state :completed                      # User answered all questions
    # state :validations_failed             # All questions answered, but failed automatic validations
    # state :validations_passed             # All questions answered and passed automatic validations
    # state :graded                         # Evaluation completed, score received

    # For API: Initialized => Emailed => Connected => Ongoing => Completed => Graded => Inactive
    #   Initialized: User entré dans le système par le client.
    #   Emailed: l'email pour inscription et passage du test envoyé
    #   Connected: l'user s'est inscrit au test
    #   Ongoing: l'user a commencé à répondre aux questions
    #   Completed: l'user à terminé le test (mais son score n'est pas encore calculé)
    #   Graded: l'user à obtenu un score
    #   Inactive: l'user a été supprimé par le client RH ou autre, il est invisible sur l'interface.
    case object.status
    when 'initialized' then 'initialized'
    when 'ready', 'connected' then 'connected'
    when 'audio_tested', 'tutored', 'in_progress', 'validations_failed', 'completed', 'pending' then 'ongoing'
    when 'graded' then 'graded'
    when 'validations_passed' then 'completed'
    when 'sent_for_evaluation' then 'sent_for_evaluation'
    when 'cancelled' then 'cancelled'
    else
      'unknown'
    end
  end

  def strong_identity_check
    object.strong_identity_check?
  end

  # ----------------------------------------
  # :section: User attributes
  # ----------------------------------------
  attributes :user

  def user
    Api::V1::UserSerializer.new(object.user).attributes
  end
end
