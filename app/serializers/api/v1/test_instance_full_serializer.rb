class Api::V1::TestInstanceFullSerializer < Api::V1::TestInstanceSerializer
  # ----------------------------------------
  # :section: TestInstance attributes
  # ----------------------------------------
  attributes :test_instance_id, :test_language, :test_profile, :question_set_id, :begin_date, :source, :internal_status, :certificate, :client_config, :technical_issues, :tags

  def test_instance_id
    object.id
  end

  def begin_date
    object.begin_date
  end

  def end_date
    object.end_date
  end

  def internal_status
    object.status
  end

  def test_language
    object.test_language
  end

  def source
    {
      client_type: object.client_type,
      client_id: object.client_id,
      client_name: object.client_name,
      client_contact_email: object.client_contact_email,
      client_contact_first_name: object.client_contact_first_name,
      client_contact_last_name: object.client_contact_last_name,
      pipplet_clients_campaign_id: object.pipplet_clients_campaign_id,
      source_type: object.source_type
    }
  end

  def technical_issues
    {
      presence: object.has_in_progress_tekis?
    }
  end

  def certificate
    eval_delivered = object.get_delivered_eval

    if eval_delivered.present? && eval_delivered.certificate.present?
      eval_delivered.certificate.grades.map { |g| { "label" => g.label.capitalize, "score" => g.score } }
    else
      []
    end
  end

  def test_profile
    {
      test_profile_id: object.test_profile_id,
      test_profile_name: object.test_profile&.name,
      challenges: (object.ordered_challenge_linguist_ids || []).join(';')
    }
  end

  def client_config
    client_config = object.client_config
    if client_config.nil?
      nil
    else
      {
        client_config_id: client_config.id,
        client_name: client_config.client_name,
        block_direct_candidate_contact: client_config.block_direct_candidate_contact
      }
    end
  end

  def tags
    object.tags.pluck(:name)
  end

  # Also overload the UserSerializer
  def user
    Api::V1::UserFullSerializer.new(object.user).attributes
  end
end
