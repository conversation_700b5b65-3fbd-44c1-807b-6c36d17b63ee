class AwsDocumentUploaderService
  attr_reader :file, :file_url, :file_name

  def initialize(file:, file_name: SecureRandom.uuid)
    @file = file
    @file_name = file_name
    @errors = {}
  end

  def upload_file_to_aws
    RestClient.put(
      s3_direct_post[:url],
      @file
    )
    @file_url = s3_direct_post[:url].split('?').first
    self
  end

  def s3_direct_post
    signer = Aws::S3::Presigner.new
    url, headers = signer.presigned_request(
      :put_object, bucket: ENV.fetch('S3_BUCKET_NAME', nil), key: "exports/#{@file_name}#{File.extname(@file)}"
    )
    { url: url, headers: headers }
  end
end
