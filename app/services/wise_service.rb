class WiseService
  def initialize(recipient_id: nil)
    @url = Rails.env.production? ? TRANSFERWISE_API_URL : TRANSFERWISE_API_SANDBOX_URL
    @recipient_id = recipient_id
  end

  def create_recipient(name:, email:, account_number:, currency:, sort_code: nil, branch_code: nil)
    deactivate_recipient
    payload(name:, email:, account_number:, currency:, sort_code:, branch_code:)

    response = RestClient.post("#{@url}/v1/accounts", @data.to_json, headers)
    data = JSON.parse(response.body)
    recipient_id = data['id']

    if @recipient_id.present?
      ExaminerService.new.update_cells_on_spreadsheet(previous_recipient_id: @recipient_id,
                                                      recipient_id: recipient_id)
    end

    recipient_id
  rescue StandardError => e
    Alert.api("[WISE] Error while creating recipient for the examiner #{name} (#{email})", { errors: e.message }.to_s, skip_appsignal: true)
    raise e
  end

  def deactivate_recipient
    return unless @recipient_id

    response = RestClient.delete("#{@url}/v2/accounts/#{@recipient_id}", headers)
    response.code == 200
  rescue RestClient::Forbidden => _e
    # 403 means that the recipient is already deactivated or doesn't exist
    false
  rescue StandardError => e
    Alert.api("[WISE] Error while deactivating recipient #{@recipient_id}", { errors: e.message }.to_s, skip_appsignal: true)
    false
  end

  private

  def headers
    {
      accept: :json,
      Authorization: "Bearer #{ENV.fetch('WISE_TOKEN', nil)}",
      'Content-Type': 'application/json'
    }
  end

  def payload(name:, email:, account_number:, currency:, sort_code:, branch_code:)
    @data = {
      currency:,
      profile: ENV.fetch('WISE_PROFILE_ID', nil),
      ownedByCustomer: false,
      accountHolderName: name,
      details: {
        legalType: "BUSINESS",
        email:
      }
    }
    details_account(account_number:, sort_code:, branch_code:)
  end

  def details_account(account_number:, sort_code:, branch_code:)
    wise_config_key = WISE_CONFIG.key?(@data[:currency].to_s) ? @data[:currency] : 'DEFAULT'
    @data[:type] = WISE_CONFIG[wise_config_key][:type]
    @data[:details].merge!(WISE_CONFIG[wise_config_key][:details].call(account_number:, sort_code:, branch_code:))
  end
end
