class ExaminerService
  def update_cells_on_spreadsheet(**args)
    worksheet_name = 'Active examiners data'
    session = GoogleDrive::Session.from_config("config/gdrive_config.json")
    @worksheet = session.spreadsheet_by_key(GOOGLE_SHEET_EXAMINERS_FOR_URL).worksheet_by_title(worksheet_name)
    unless @worksheet
      Appsignal.send_error(Exception.new("[WISE] Error, worksheet #{worksheet_name} for #{GOOGLE_SHEET_EXAMINERS_FOR_URL} not found"))
      return
    end
    @header_cells = @worksheet.rows.first

    update_recipient_id(previous_recipient_id: args[:previous_recipient_id],
                        recipient_id: args[:recipient_id])

    update_bank_currency(recipient_id: args[:recipient_id],
                         bank_currency: args[:bank_currency])

    @worksheet.save
  end

  private

  def update_recipient_id(previous_recipient_id:, recipient_id:)
    return unless previous_recipient_id.present? && recipient_id.present?
    return unless (line_number = line_number(recipient_id: previous_recipient_id))

    name_cell_index = @header_cells.index('TW recipient ID')
    @worksheet[line_number + 1, name_cell_index + 1] = recipient_id
  end

  def update_bank_currency(recipient_id:, bank_currency:)
    return if bank_currency.blank?
    return unless (line_number = line_number(recipient_id:))

    ['Account currency', 'Invoice currency'].each do |header_name|
      @worksheet[line_number + 1, @header_cells.index(header_name) + 1] = bank_currency
    end
  end

  def line_number(recipient_id:)
    line_number = @worksheet.rows.find_index { |row| row[@header_cells.index('TW recipient ID')] == recipient_id.to_s }
    unless line_number
      Alert.api("[WISE] Error, recipient #{@recipient_id} not found", nil, skip_appsignal: true)
      return
    end
    line_number
  end
end
