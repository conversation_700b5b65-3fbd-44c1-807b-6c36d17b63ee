class JsonAssessmentFillerService
  def initialize(evaluation)
    @evaluation = evaluation
  end

  def fill_section(section:, score:, force: false)
    update_json_assessment(partial_assessment: @evaluation.assessment_type.scorable_assessment_for_skill_and_score(skill: section, score:), force:)
  end

  def fill_robustness(explanation:, comment: nil, force: false, append_comment: false)
    force = true if append_comment
    comment ||= '' if explanation.to_sym == :other
    partial_assessment = { 'assessment_robust_explanation' => explanation }
    if comment
      partial_assessment['assessment_robust_explanation-Comment'] = if append_comment
                                                                      comments = [@evaluation.json_assessment['assessment_robust_explanation-Comment'], comment].compact
                                                                      comments * ' | '
                                                                    else
                                                                      comment
                                                                    end
    else
      partial_assessment.delete('assessment_robust_explanation-Comment')
    end
    update_json_assessment(partial_assessment:, force:)
  end

  def fill_cheating(explanation:, comment: nil, force: false)
    comment ||= '' if explanation.to_sym == :other
    partial_assessment = { 'assessment_cheating_explanation' => explanation }
    if comment
      partial_assessment['assessment_cheating_explanation-Comment'] = comment
    else
      partial_assessment.delete('assessment_cheating_explanation-Comment')
    end

    update_json_assessment(partial_assessment:, force:)
  end

  private

  def update_json_assessment(partial_assessment:, force:)
    partial_assessment.each do |key, value|
      next if @evaluation.json_assessment.key?(key) && force == false

      @evaluation.json_assessment[key] = value
    end
    @evaluation.save!
  end
end
