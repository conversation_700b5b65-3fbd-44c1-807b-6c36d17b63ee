class SmsService::SendSmsBaseService
  attr_accessor :user, :message, :errors, :from

  def initialize(user_id:, message:)
    @user = User.find(user_id)
    @message = message
    @from = 'Pipplet'
    @errors = false
  end

  private

  def log(user:, category:, criticity:, message:)
    EventLog.create(
      loggable_type: 'User',
      loggable_id: user.id,
      category: category,
      content: message,
      criticity: criticity
    )
  end

  def trigger_error
    @error = true
  end
end
