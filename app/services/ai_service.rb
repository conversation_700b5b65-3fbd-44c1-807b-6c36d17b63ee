class AiService
  class << self
    include ApplicationHelper
    include ScoresHelper

    def add_ai_event_log(evaluation:, section:, message:, criticity: :info, action: :continuing)
      content = <<~HEREDOC.delete("\n")
        [#{evaluation.examiner.ai_config.dig(section.to_s, 'type')}][#{section}]
        [Attempt ##{evaluation.public_send(:"ai_#{section}_attempts")}]
        [#{message}]
        [#{action}...]
      HEREDOC
      EventLog.add(evaluation, :ai, content, criticity)
    end

    def save_ai_score_and_response(section:, evaluation:)
      production_type = section == :spoken ? :audio : :written
      productions = evaluation.test_instance.ordered_completed_productions_for(production_type)
      ai_api = AiApi.new(language: evaluation.test_instance.test_language_sym, section:, evaluation:)
      score, response = ai_api.score_and_response(productions:)
      evaluation.update!("ai_#{section}_response" => response, "ai_#{section}_score" => score)
      [score, response]
    end

    def complete_section(evaluation:, section:, score: nil, error_message: nil)
      evaluation.with_lock do
        AiService.fill_json_assessment(evaluation:, section:, score:, error_message:)
        evaluation.update!("ai_#{section}_completed" => true)
        AiService.attempt_assessment_completion(evaluation:)
      end
    end

    def fill_json_assessment(evaluation:, section:, score: nil, error_message: nil)
      examiner = evaluation.examiner
      filler = JsonAssessmentFillerService.new(evaluation)
      ai_type = examiner.ai_config.dig(section.to_s, 'type')

      if error_message
        comment = <<-HEREDOC.squish
                     An error occurred from :
                     #{section}: #{ai_type}AI
                     Message : #{error_message}
        HEREDOC
        filler.fill_robustness(explanation: 'other', comment:, append_comment: true)
      end

      filler.fill_section(section:, score:)
      evaluation.reload.json_assessment
    end

    def attempt_assessment_completion(evaluation:)
      if evaluation.ai_completed?
        evaluation.generate_certificate
        evaluation.assess!('assessed_at')

        if evaluation.ai_spoken_score.nil? &&
           evaluation.ai_written_score.nil? &&
           evaluation.peer_review? # Peer review case, cancel it we don't care anymore
          evaluation.cancel!('canceled_at')
          return
        end
      end
    end
  end
end
