// === Pipplet question class
// currentPippletTest has a queue of question, and a "currentQuestion" attribute
function pippletQuestion(pipplet_test) {
  // "use strict";

  // Initialise all variables belonging to a question
  if(typeof pipplet_test === "undefined" || pipplet_test !== currentPippletTest || !currentPippletTest.initialised) {
    throw "PippletQuestion has been called without the context of a proper pippletTest";
  }

  this.questionTimer = null;
  this.questionHolder = "#question_holder";
  this.question_id = null;
  this.question_identifier = null;
  this.questionData = null;
  this.uploader = null;
  this.finalQuestionTimeElapsed = 0;
  this.questionDuration = 0;
  this.visibilityChangeCount = 0;
  this.questionsErrorsLog = [];

  // Loading question page, it can either be a production, a reception or the end test page
  this.loadQuestionData = function() {
    var self = this;

    this.questionIndex = currentPippletTest.questionList.length;
    // currentPippletTest.stepManager.set("question", "before_request");
    $.ajax({
      type: "GET",
      url: "/questions",
      success: function(data) {
        // currentPippletTest.stepManager.set("question", "requestion_completed");
        // Get variables about the question from the page so we can pass them to the uploader
        //self.questionData = data;
        pippletHelper.debug("[pippletQuestion::loadQuestionData][id:"+data.question_id+"] data is loaded!");

        self.question_id = $(data).filter('meta[name=question_id]').attr("content");
        self.question_type = $(data).filter('meta[name=question_type]').attr("content");
        self.challenge_id = $(data).filter('meta[name=challenge_id]').attr("content");
        self.challenge_linguist_id = $(data).filter('meta[name=challenge_linguist_id]').attr("content");
        self.challenge_status = $(data).filter('meta[name=challenge_status]').attr("content");

/*        self.answer_type = $(data).filter('meta[name=answer_type]').attr("content");
        self.answer_id = $(data).filter('meta[name=answer_id]').attr("content");*/
        self.question_identifier = $(data).filter('meta[name=question_identifier]').attr("content");
        self.questionDuration = parseInt($(data).filter('meta[name=question_duration]').attr("content"));

        if($(data).find(".recorder").length > 0) {
          currentPippletTest.recorder.setMaxDuration($(data).find(".recorder"));
        }

        $(self.questionHolder).empty().append(data);


        pippletHelper.applyJsTweaks();

        // Set events on loaded elements
        // DEBUG
        //pippletHelper.debug("Question load was performed");
        //pippletHelper.debug("Question id is : "+self.question_id);
        // DEBUG

        // Test if we are displyaing a question or an end test page
        if($("#endtest_holder").length > 0) {
          self.initialiseEndTestHolder();
          pippletHelper.trackAnalytics('endtest');
        } else {
          self.initialiseQuestionHolder();
          pippletHelper.trackAnalytics('question');
        }
      },
      error: function(status) {
        pippletHelper.debug("[pippletQuestion::loadQuestionData][id:"+this.question_id+"] error while loading question data");
        pippletHelper.debug(status);
      }
    });

  }; // End loadQuestionData

  // This is the end test page that is shown when there are no more question
  // It waits for all recording to be uploaded before going back
  this.initialiseEndTestHolder = function(){
    $("#back_to_welcome").click(function(){
      if(currentPippletTest.hasNonFinishedUploaders() == true) {
        currentPippletTest.finishRecordingsBeforeTest();
      } else {
        currentPippletTest.finishTest();
      }
      return false;
    });

/*    $("#survey_button").click(function(){
      var url = $(this).attr('data-survey_url');
      if(currentPippletTest.hasNonFinishedUploaders() === true) {
        currentPippletTest.finishRecordingsBeforeTest(url);
      } else {
        currentPippletTest.finishTest(url);
      }
      return false;
    });*/

//    $("#technical_report_button").click(function(){
//      console.log("Technical Report Clic");
//      var url = $(this).attr('data-technical_report_url');
//      if(currentPippletTest.hasNonFinishedUploaders() === true) {
//        currentPippletTest.finishRecordingsBeforeTest(url);
//      } else {
//        currentPippletTest.finishTest(url);
//      }
//      return false;
//    });

    // Remove all listeners
    currentPippletTest.removeWindowListeners();

    // Save all pippletTestData for this user
    currentPippletTest.saveTestDataToUser();

    currentPippletTest.displayQuestion();
  }; // End initialiseEndTestHolder


  // Initialise all question elements when displaying a question (reception or production)
  this.initialiseQuestionHolder = function(){
    pippletHelper.debug("[pippletQuestion::initialiseQuestionHolder][id:"+this.question_id+"] init");
    var self = this;

    $("#alert_messages").hide();

    $(self.questionHolder).find("#next-question").click(function(){
      pippletHelper.debug("[pippletQuestion::initialiseQuestionHolder][id:"+this.question_id+"] 'next question' has been clicked");
      currentPippletTest.currentQuestion.setAnswerButtonsToLoading();
      if(currentPippletTest.recorder.state === "recording") {
        currentPippletTest.recorder.buttonClickStop();
      }
      currentPippletTest.timeoutStatus = 0;
      currentPippletTest.currentQuestion.finishQuestion("answered");
      return false;
    });

    $(self.questionHolder).find("#end-test").click(function(){
      pippletHelper.debug("[pippletQuestion::initialiseQuestionHolder][id:"+this.question_id+"] 'end test' has been clicked");
      currentPippletTest.currentQuestion.setAnswerButtonsToLoading();
      if(currentPippletTest.recorder.state === "recording") {
        currentPippletTest.recorder.stop();
      }
      currentPippletTest.timeoutStatus = 0;
      currentPippletTest.currentQuestion.finishQuestionAndTest("answered");
      return false;
    });

    $(self.questionHolder).find('#question-audio').on('ended', function(e){
      $(self.questionHolder).find('#question-audio').removeClass("playing");
    });

    // Enable play button when audio is here
    $(self.questionHolder).find("#play-button").click(function(){
      $(self.questionHolder).find('#question-audio').trigger('play');
      $(self.questionHolder).find('#question-audio').addClass("playing");
    });

    // Enable report link that will show the report form
    $(self.questionHolder).find("#report_recording").click(function(e){
      e.preventDefault();
      pippletHelper.debug("[pippletQuestion::initialiseQuestionHolder][id:"+this.question_id+"] 'report recording' has been clicked");
      currentPippletTest.currentQuestion.reportQuestion();
    });

    // Load uploader separately
    $.get("/questions/uploader", { question_id:  self.question_id, question_identifier:  self.question_identifier}, function(data_uploader){
      var holder = $("#uploaders_holder").append(data_uploader);
      //uploadform = $(holder).find(".upload-form", $(data_uploader));
      //uploadform = $(holder).find(".upload-form");

      currentPippletTest.currentQuestion.uploaderOnLoad("s3_uploader"+self.question_identifier, self.question_id, self.answer_type, self.answer_id);
      currentPippletTest.displayQuestion();
    });
  }; // End of initialiseQuestionHolder


  // Set answer buttons to a "loading" state with the message
  // Optional container can be passed as argument, usefull for reporting page as we have answer buttons of the question and of the reporting form
  // Any function that sends something to the server from the question page should call that
  this.setAnswerButtonsToLoading = function(container) {
    if(typeof container !== "undefined") {
      $(container).find("#end-test").button('loading');
      $(container).find("#next-question").button('loading');
    } else {
      $("#end-test").button('loading');
      $("#next-question").button('loading');
    }
  };

  this.resetAnswerButtonsToLoading = function() {
    $("#end-test").button('reset');
    $("#next-question").button('reset');
  };



  // Display a report question page
  // this.reportQuestion = function(){
  //   currentPippletTest.stopTimer();
  //   $.ajax({
  //     type: "GET",
  //     url: "/questions/report",
  //     data: $.param({ question_id: currentPippletTest.currentQuestion.question_id}),
  //     success: function(data) {
  //       // Load the report form, hide the question container (still here as we will only send data here)
  //       // Add form to the main-content container
  //       // Add events to next-question and finish events
  //       // TODO : Use Finish Question but with a report argument !!

  //       pippletHelper.debug("[pippletQuestion::reportQuestion][id:"+this.question_id+"][AJAX: /questions/report] Success (data below)");
  //       pippletHelper.debug(data);
  //       $(currentPippletTest.currentQuestion.questionHolder).hide();
  //       $("#main-content").append(data);

  //       $("#reporting_holder").find("#next-question").click(function(){
  //         currentPippletTest.currentQuestion.setAnswerButtonsToLoading("#reporting_holder");
  //         currentPippletTest.currentQuestion.finishQuestion("reported");
  //         return false;
  //       });

  //       $("#reporting_holder").find("#end-test").click(function(){
  //         currentPippletTest.currentQuestion.setAnswerButtonsToLoading("#reporting_holder");
  //         currentPippletTest.currentQuestion.finishQuestionAndTest("reported");
  //         return false;
  //       });
  //     },
  //       error: function(status) {
  //       pippletHelper.debug("[pippletQuestion::reportQuestion][id:"+this.question_id+"][AJAX: /questions/report] Error. Status: "+status);
  //     }
  //   });
  // }; // End of reportQuestion

  // This function terminates dynamic objects linked to the question when it is finish (the question or the whole test), like timer and audio
  this.finishQuestionObjects = function(){
    this.finalQuestionTimeElapsed = this.currentQuestionTimeElapsed();
    this.finalQuestionTimeElapsedInMin = this.finalQuestionTimeElapsed / 1000 / 60;
    //this.logQuestionDuration();
    currentPippletTest.stopTimer();
    currentPippletTest.recorder.stop();
    // Add stop recorder
  };

  this.currentQuestionTimeElapsed = function() {
    var timeElapsed = this.questionDuration*currentPippletTest.questionTimer.getPercentageElapsed();
    return timeElapsed;
  }


  // This is the standard "Next question" action
  this.finishQuestion = function(questionStatus){
    currentPippletTest.currentQuestion.textAreaValue = $('.question textarea.form-control').val();

    currentPippletTest.currentQuestion.finishQuestionObjects();
    this.endStatus = questionStatus;
    var nextStep = "next_question";
    questionStatus = typeof questionStatus !== "undefined" ? questionStatus : "no_status";
    if(questionStatus == "timeout") {
      nextStep = "timeout";
    }
    pippletHelper.debug("[pippletQuestion::finishQuestion][id:"+this.question_id+"] questionStatus='"+questionStatus+"', nextStep='"+nextStep+"'. Before sendFormData");

    // Specific loop if there is a hanging recording
    if(currentPippletTest.processingRecording == true) {
      this.waitForRecording(questionStatus, nextStep);
    } else {
      this.sendFormData(questionStatus, nextStep);
    }
  };

  // This corresponds to the event on the "End Test" button that is displayed on a question.
  // We save the question and then end the test
  this.finishQuestionAndTest = function(questionStatus) {
    currentPippletTest.currentQuestion.finishQuestionObjects();
    questionStatus = typeof questionStatus !== "undefined" ? questionStatus : "no_status";
    this.endStatus = questionStatus;
    // If this is a standard
    // Specific loop if there is a hanging recording
    if(currentPippletTest.processingRecording == true) {
      this.waitForRecording(questionStatus, "end_test");
    } else {
      this.sendFormData(questionStatus, "end_test");
    }
  };

  // This function allows the 'Next Question' button, can be called whenever necessary (user has made a record, user has answered,..)
  this.authorise_next_question = function(){
    pippletHelper.debug("[pippletQuestion::authorise_next_question][id:"+this.question_id+"]");
    $(".answer-button .button input.wait-audio").removeClass("wait-audio");
  };

  this.setUploaderId = function(current_uploader_id){
    $("#current_recorder_id").val(current_uploader_id);
  };

  // IMPORTANT FUNCTION
  // This sends question data.
  // Can be called from numerous functions, with a different "questionStatus" argument and a specific "nextStep" (default are answered and next-question)

  this.waitForRecording = function(question_status, next_step, processingTime) {
    pippletHelper.debug("[pippletQuestion::waitForRecording][id:"+this.question_id+"]");

    processingTime = (typeof processingTime !== "undefined") ? processingTime : 0;
    setTimeout(function(){
      if(currentPippletTest.processingRecording === false || processingTime > 10000) {
        currentPippletTest.currentQuestion.sendFormData(question_status,next_step);
      } else {
        currentPippletTest.currentQuestion.waitForRecording(question_status, next_step, processingTime);
      }
    }, 200);
  };

  this.sendVisibilityChangeData = function(questionId){

    data = {
      'question_id': questionId,
      'count': this.visibilityChangeCount
    };

    pippletHelper.debug("[pippletQuestion::sendVisibilityChangeData]Sending");

    $.ajax({
      type: "POST",
      dataType: "json",
      async: true,
      url: "/event_logs/set_tab_change_count_for_ti",
      data: data,
      success: function() {
        pippletHelper.debug("[pippletQuestion::sendVisibilityChangeData]Success");
      },
      error: function(status) {
        pippletHelper.debug("[pippletQuestion::sendVisibilityChangeData] Error"+status);
      }
    });
  };

  this.sendFormData = function(questionStatus, nextStep) {
    pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"] questionStatus='"+questionStatus+"', nextStep='"+nextStep+"', date="+new Date());

    // Stop if a sendFormData for this question is being executed, if not set a flag on currentQuestion that a sendFormData is being executed and pursue
    if (currentPippletTest.currentQuestion.isSendingFormData === true) {
      pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"] Multiple Send for Data for currentQuestion");
      return false;
    } else {
      currentPippletTest.currentQuestion.isSendingFormData = true;
    }

    // Get question status and end test arguments values or set default ones. Then add question Status to form values.
    questionStatus = typeof questionStatus !== "undefined" ? questionStatus : "no_status";
    nextStep = typeof nextStep !== "undefined" ? nextStep : "next_question";

    $("#status").val(questionStatus);
    var parameters = $("#question_holder .question-form").serializeArray();

    // If this is a report, add reportInfo form data by concaneting (remove hidden attributes like utf8 and authenticity token)
    if(questionStatus === "reported") {
      parameters = parameters.concat($('#report_question_form :not(":hidden")').serializeArray());
    }

    // If we are calling this function from the beforeunload event (user wants to leave the page), make the request synchronous so we wait for the result
    var asyncStatus = false;
    if (nextStep === "leaving") {
      asyncStatus = false;
    } else {
      asyncStatus = true;
    }

    // Remove all tooltip and validation errors before sending the request
    $(".question.validation_error").tooltip("destroy");
    $(".question.validation_error").removeClass("validation_error");

    pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"][AJAX] Before request (parameters below)");
    pippletHelper.debug(parameters);

    $.ajax({
      type: "POST",
      dataType: "json",
      async: asyncStatus,
      url: $("#question_holder .question-form").attr('action'),
      data: $.param(parameters), // serializes the form's elements.
      success: function(data) {
        pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"][AJAX] Success callback");
        currentPippletTest.currentQuestion.isSendingFormData = false;
        currentPippletTest.currentQuestion.sendVisibilityChangeData(currentPippletTest.currentQuestion.id());
        currentPippletTest.notify.dismiss();
        if(questionStatus === "reported") {
          $("#reporting_holder").remove();
        }
        switch(nextStep) {
          case "timeout" :
            currentPippletTest.displayTimeoutMessage();
            break;
          case "end_test" :
            currentPippletTest.finishTest();
            break;
          case "next_question" :
            currentPippletTest.nextQuestion();
            break;
          case "leaving":
            break;
        }

        // Commenting this line to avoid double clicking bug
        //currentPippletTest.currentQuestion.resetAnswerButtonsToLoading();
      },
      error: function(status) {
        currentPippletTest.currentQuestion.isSendingFormData = false;
        currentPippletTest.currentQuestion.resetAnswerButtonsToLoading();
        pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"][AJAX] Error callback (displaying status below)");
        pippletHelper.debug(status);
        if(typeof status.responseJSON !== "undefined") {
          if (status.responseJSON.error_status == "NOT VALID"){
            currentPippletTest.currentQuestion.handleInvalidAnswer(status.responseJSON);

          } else if(status.responseJSON.error_status == "BAD REQUEST"){
            pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"][AJAX] Error callback / Question validation error : Bad request");
          }
        } else {
          pippletHelper.debug("[pippletQuestion::sendFormData][id:"+this.question_id+"][AJAX] Error callback / Question validation error : Default action");
        }
      }
    }); // $.ajax
  }; // this.sendFormData

  this.skipQuestion = function(nextStep) {
    $.ajax({
      type: "GET",
      dataType: "json",
      async: false,
      url: "/questions/skip",
      success: function(data) {
        pippletHelper.debug("[pippletQuestion::Skip Question][id:"+this.question_id+"][AJAX] Success callback");
        switch(nextStep) {
          case "end_test" :
            currentPippletTest.finishTest();
            break;
          case "next_question" :
            currentPippletTest.nextQuestion();
            break;
        }
        // Commenting this line to avoid double clicking bug
        //currentPippletTest.currentQuestion.resetAnswerButtonsToLoading();
      },
      error: function(status) {
        pippletHelper.debug("[pippletQuestion::Skip Question][id:"+this.question_id+"][AJAX] Error callback (displaying status below)");
        pippletHelper.debug(status);
      }
    }); // $.ajax

  }; // this.SkipQuestion

  this.handleInvalidAnswer = function(status) {
    pippletHelper.debug("[pippletQuestion::handleInvalidAnswer][id:"+this.question_id+"] Status (below)");
    pippletHelper.debug(status);

    jQuery.each(status.validation_errors, function(error){
      // Question id is the error key
      var question_id = error;

      pippletHelper.debug("[pippletQuestion::handleInvalidAnswer][id:"+this.question_id+"][looping on errors] question_id: "+question_id+". Content of element '.question ."+question_id+"'");
      pippletHelper.debug($(".question ."+question_id));

      $(".question."+question_id).addClass("validation_error");

      // Test if question is a textarea. If so placement will be on the right
      var error_placement;
      if ($(".question."+question_id).find("textarea").length > 0) {
        error_placement = 'top';
      }
      else {
        error_placement = 'bottom';
      }

      $(".question."+question_id).tooltip({
        title:status.validation_errors[question_id],
        placement: error_placement,
        container: $(".question."+question_id)
      });
    });
    $(".question.validation_error").tooltip("show");
    currentPippletTest.notify.validation_errors();
    currentPippletTest.questionTimer.restart();
  };

  this.uploaderOnLoad = function(uploadform, question_id, answer_type, answer_id) {
    this.uploader = new pippletUploader();
    this.uploader.initialiseUploader(uploadform, question_id, answer_type, answer_id);
  };

} // End of PippletQuestion
