// "use strict";

// === Main global variable
var currentPippletTest;


// == pippletTest Class
// There must be only one instance : currentPippletTest (cf global variable) that is called from many places

function pippletTest() {
  // "use strict";

  this.questionTimer = null;
  this.recorder = null;
  this.questionHolder = "#question_holder";
  this.currentQuestion = null;
  this.questionList = [];
  this.timeoutStatus = 0;
  this.initialised = null;
  this.adminMode = false;

  this.performIdentityCheck = false;
  this.camera = null;

  this.admin = null;
  this.notify = null;

  this.stepManager = new pippletStepManager();

  // Set up test
  // Check if user audio config has already been tested, if not show an audioTest page
  //
  // BE CAREFUL, the "setMediaContext" function is called directly from this function normally.
  // However when audioTest is needed, the loadAudioTest function calls setMediaContext
  this.initialiseTest = function() {
    pippletHelper.debug("[pippletTest::initialiseTest] setMediaContext");

    // Add Listeners
    this.addWindowListeners();
    var self = this;

    // Load question listner
    $("#launch_test").click(function() {
      // Initialise other pippletTestElements
      // As initialisation can be made asynchronously and user can come back and forth to the test
      // we test if elements exist before creating them
      if(currentPippletTest.questionTimer === null) {
        self.questionTimer = new questionTimer("#question_timer");
        self.questionTimer.initialise();
      }

      $("#test_final_instructions").hide();
      $("#test_onboarding_breadcrumbs_holder").hide();
      currentPippletTest.loadQuestion();
    });

    if(currentPippletTest.recorder === null) {
      setMediaContext(window);
    }
    currentPippletTest.stepManager.set("initialised", "in_function");
    this.initialised = true;


    if(currentPippletTest.notify === null) {
      this.notify = new pippletNotifyer();
    }

    // Check id?
    var id_checks = $("meta[name=test_instance_idc]").attr("content");
    currentPippletTest.performIdentityCheck = (id_checks === "true");

    // Load camera
    if(currentPippletTest.performIdentityCheck && currentPippletTest.camera === null && window.JpegCamera) {
      pippletHelper.debug("[pippletTest::beforeCameraCreate]");
      camera_lang = $("meta[name=camera_lang]").attr("content");
      if(camera_lang == undefined) {
        camera_lang = "en";
      }
      
      currentPippletTest.camera = new Pipplet.Camera({
        shutter : false, /* set true to hear snapshots shutter*/
        snapshots: {
          interval: 3000 /*  Snapshots Interval*/
        },
        message :{
          lang : camera_lang /* Set language messages */
        },
        AWS: {	/* S3 configuration  */
          s3: {
            params: {
              Bucket: "xxx",
              accessKeyId: "xxx",
              secretAccessKey: "xxx"
            }
          }
        },
        testid : $("meta[name=test_instance_id]").attr("content") || 123
      });

      pippletHelper.debug("[pippletTest::beforeCameraStart]");
      currentPippletTest.camera.start();
    }

    $(this.questionHolder).hide();

    return true;
  };

  this.addWindowListeners = function() {
    // Set before quit or reload event (displaying a window and a message)
    window.onbeforeunload = function() {
      pippletHelper.debug("[pippletTest::onbeforeunload]");
      pippletHelper.trackAnalytics("window_beforeunload");

      currentPippletTest.saveTestDataToUser();

      // Save answers
      // If we save answers here, there we cannot re-submit the form
      // currentPippletTest.beforeLeavingPage();

      var leaving_test = $("meta[name=leaving_test]").attr("content");
      return leaving_test;
    }; // window.onbeforeunload

    window.onunload = function() {
      pippletHelper.debug("[pippletTest::unload]");
      pippletHelper.trackAnalytics("window_unload");
    }; // window.onunload

    window.onblur = function() {
      pippletHelper.debug("[pippletTest::onblur]");
      pippletHelper.trackAnalytics("window_blur");

      if(!(currentPippletTest === undefined)) {
        if(!(currentPippletTest.currentQuestion == null)){
          currentPippletTest.currentQuestion.logVisibilityChange();
        }
      }

      // Useless because it"s only seen when the user comes back
      // $("#leaving_test").modal("show");

      // Showing a Javascript confirm box forces the tab to gain focus again
      // var leaving_test = $("meta[name=leaving_test]").attr("content");
      // if(confirm(leaving_test)) {
      //   pippletHelper.debug("[window.onblur] confirm: OK");
      //   pippletHelper.trackAnalytics("window_onblur_ok");
      //   currentPippletTest.beforeLeavingPage();
      // } else {
      //   pippletHelper.debug("[window.onblur] confirm: Cancel");
      //   pippletHelper.trackAnalytics("window_onblur_cancel");
      // }
    }; // window.onblur

    window.onfocus = function() {
      pippletHelper.debug("[pippletTest::onfocus]");
      pippletHelper.trackAnalytics("window_focus");
    }; // window.onfocus


    pippletHelper.enableLeavingTestWarning();

    // Same thing for back button
    /*if (window.history && window.history.pushState) {
      $(window).on("popstate", function() {
        var hashLocation = location.hash;
        var hashSplit = hashLocation.split("#!/");
        var hashName = hashSplit[1];

        if (hashName !== "") {
          var hash = window.location.hash;
          if (hash === "") {
            pippletHelper.debug("[pippletTest::window.on("popstate"] Back button was pressed.");
          }
        }
      });
    window.history.pushState("forward", null, "./#forward");
    }*/

  };


  this.removeWindowListeners = function() {
    window.onfocus = function() {};
    window.onbeforeunload = function() {};
    window.onblur = function() {};
    window.onunload = function() {};

    pippletHelper.disableLeavingTestWarning();
  };

  this.checkInitialised = function(){
    if(!currentPippletTest.initialised) {
      throw "Pipplet test has not been properly initialised, function cannot be called";
    }
  };


  // Load question function
  // Basically create a new question, add it to the queue "questionList" and then load question data
  this.loadQuestion = function() {
      this.checkInitialised();
      if(!(this.currentQuestion == null)) {
        this.questionList.push(this.currentQuestion);
      }

      $("#question_timer").show();

      currentPippletTest.stepManager.set("question", "before_function");
      this.currentQuestion = new pippletQuestion(this);
      this.currentQuestion.loadQuestionData();

      pippletHelper.debug("[pippletTest::loadQuestion] new question loaded!");
  };


  // Next question function
  // Hide question holder and timer and load another question
  this.nextQuestion = function(timeout) {
    pippletHelper.debug("[pippletTest::nextQuestion]");

    this.checkInitialised();
    //delete this.currentQuestion.questionTimer;
    $("#question_holder").fadeOut();
    $(currentPippletTest.questionTimer.timerElement).hide();
    currentPippletTest.loadQuestion();
  }; // end of nextQuestion


  // finishRecordingsBeforeTest function
  // Waits for all recordings to be ended before finishing test
  // Recursive function
  this.finishRecordingsBeforeTest = function(processing_time, redirect_address) {
    pippletHelper.debug("[pippletTest::finishRecordingsBeforeTest]");

    processingTime = typeof processingTime !== "undefined" ? processingTime : 0;
    setTimeout(function(){
      if(currentPippletTest.hasNonFinishedUploaders() == false || processingTime > 10000) {
        currentPippletTest.finishTest(redirect_address);
      } else {
        currentPippletTest.finishRecordingsBeforeTest(processingTime, redirect_address);
      }
    }, 200);
  }; // End of finishRecordingsBeforeTest


  // finishTest function
  // Removes beforeunload system message
  // TODO handle ongoing uploads
  // Goes back to home page
  this.finishTest = function(address) {
    pippletHelper.debug("[pippletTest::finishTest]");

    this.saveTestDataToUser();

    this.checkInitialised();
    // TODO
    // Handle current uploads
    currentPippletTest.stepManager.set("end_test","in_function");

    // Redirect
    address = typeof address !== "undefined" ? address : "/welcome";
    window.location = address;
  };


  this.displayTimeoutMessage = function(){
    this.checkInitialised();
    currentPippletTest.stepManager.set("timeout", "in_function");
    currentPippletTest.stepManager.set("timeout", "before_request");
    $.ajax({
        type: "GET",
        url: "/questions/timeout",
        success: function(data) {
          currentPippletTest.stepManager.set("timeout", "request_completed");
          $("#question_holder").empty().append(data);
          $("#question_holder").show();
          $("#question_holder").find("#next-question").click(function(){
            currentPippletTest.currentQuestion.setAnswerButtonsToSaving();
            currentPippletTest.loadQuestion();
            return false;
          });

          $("#question_holder").find("#end-test").click(function(){
            currentPippletTest.currentQuestion.resetAnswerButtons();
            currentPippletTest.finishTest();
            return false;
          });
          pippletHelper.trackAnalytics("timeout");
        },
        error: function(e) {
          pippletHelper.debug("[pippletTest::displayTimeoutMessage] In error");
          pippletHelper.debug(e);
        }
      });
  };


  // displayQuestion function
  // Display question data (question holder) and start timer
  this.displayQuestion = function() {
    this.checkInitialised();
    pippletHelper.debug("[pippletTest::displayQuestion] In question display");
    self = this;

    // Detect specific "no more question" page
    var endTest = false;
    if($("#question_holder .question").length == 0) {
      endTest = true;
    }

    $("#question_holder").fadeIn(400, function(){
      if(!endTest) {
        $(currentPippletTest.questionTimer.timerElement).show();
        currentPippletTest.questionTimer.start();
      }
    });
  }; // end of displayQuestion


  // == Utility functions
  // Below are utility functions

  // hasNonFinisedUploaders accessor
  // Tell if currentPippletTest has ongoing uploading
  // Test all questions from questions list for their uploader status.
  // If an uploader exists and its status is not sucess or failed, that means it is still going on
  this.hasNonFinishedUploaders = function() {
    this.checkInitialised();
    var i = 0;
    for(i = 0; i<this.questionList.length; i++) {
      if((this.questionList[i].uploader != null) && (this.questionList[i].uploader.status != null) && (this.questionList[i].uploader.status != "success") && (this.questionList[i].uploader.status != "failed")) {
        return true;
      }
    }
    return false;
  }; // end of hasNonFinishedUploaders

  // This function is called when the user wants to leave the page (reloading browser, changing address in address bar, ...)
  this.beforeLeavingPage = function(){
    this.checkInitialised();
    currentPippletTest.currentQuestion.sendFormData("answered", "leaving");
  };

  // This function is a shortcut that stops the current timer
  // Useful to prevent the timer from going while the "next-question" request is being processed
  this.stopTimer = function() {
    this.checkInitialised();
    $(currentPippletTest.questionTimer.timerObject.circleProgress("widget")).stop();
  };


  // This function saves all currentPippletTesData to the database
  // So we know what happened
  this.saveTestDataToUser = function() {
    var strPippletTest = "Test not initialised. User Id :"+pippletAnalytics.userId;

    if(typeof currentPippletTest !== "undefined") {
      currentPippletTest.questionList.push(currentPippletTest.currentQuestion);
      strPippletTest = JSON.stringify(currentPippletTest.questionList);
    }

    $.ajax({
      type: "POST",
      dataType: "json",
      url: "/users/log_testdata",
      data: { testdata: strPippletTest }, // serializes the form"s elements.
      success: function(data){},
      error: function(data){}
    });
  }; // saveTestDataToUser

}
