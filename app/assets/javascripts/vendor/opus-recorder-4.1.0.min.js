!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Recorder=e():t.Recorder=e()}("undefined"!=typeof self?self:this,function(){return function(t){function e(i){if(o[i])return o[i].exports;var n=o[i]={i:i,l:!1,exports:{}};return t[i].call(n.exports,n,n.exports,e),n.l=!0,n.exports}var o={};return e.m=t,e.c=o,e.d=function(t,o,i){e.o(t,o)||Object.defineProperty(t,o,{configurable:!1,enumerable:!0,get:i})},e.n=function(t){var o=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(o,"a",o),o},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=0)}([function(t,e,o){"use strict";(function(e){var o=e.AudioContext||e.webkitAudioContext,i=function(t){if(!i.isRecordingSupported())throw new Error("Recording is not supported in this browser");this.state="inactive",this.config=Object.assign({bufferLength:4096,encoderApplication:2049,encoderFrameSize:20,encoderPath:"encoderWorker.min.js",encoderSampleRate:48e3,leaveStreamOpen:!1,maxBuffersPerPage:40,mediaTrackConstraints:!0,monitorGain:0,numberOfChannels:1,recordingGain:1,resampleQuality:3,streamPages:!1,wavBitDepth:16},t),this.initWorker()};i.isRecordingSupported=function(){return o&&e.navigator&&e.navigator.mediaDevices&&e.navigator.mediaDevices.getUserMedia&&e.WebAssembly},i.prototype.clearStream=function(){this.stream&&(this.stream.getTracks?this.stream.getTracks().forEach(function(t){t.stop()}):this.stream.stop(),delete this.stream),this.audioContext&&(this.audioContext.close(),delete this.audioContext)},i.prototype.encodeBuffers=function(t){if("recording"===this.state){for(var e=[],o=0;o<t.numberOfChannels;o++)e[o]=t.getChannelData(o);this.encoder.postMessage({command:"encode",buffers:e})}},i.prototype.initAudioContext=function(t){return t&&t.context&&(this.audioContext=t.context),this.audioContext||(this.audioContext=new o),this.audioContext},i.prototype.initAudioGraph=function(){var t=this;this.encodeBuffers=function(){delete this.encodeBuffers},this.scriptProcessorNode=this.audioContext.createScriptProcessor(this.config.bufferLength,this.config.numberOfChannels,this.config.numberOfChannels),this.scriptProcessorNode.connect(this.audioContext.destination),this.scriptProcessorNode.onaudioprocess=function(e){t.encodeBuffers(e.inputBuffer)},this.monitorGainNode=this.audioContext.createGain(),this.setMonitorGain(this.config.monitorGain),this.monitorGainNode.connect(this.audioContext.destination),this.recordingGainNode=this.audioContext.createGain(),this.setRecordingGain(this.config.recordingGain),this.recordingGainNode.connect(this.scriptProcessorNode)},i.prototype.initSourceNode=function(t){var o=this;return t&&t.context?e.Promise.resolve(t):this.stream&&this.sourceNode?e.Promise.resolve(this.sourceNode):e.navigator.mediaDevices.getUserMedia({audio:this.config.mediaTrackConstraints}).then(function(t){return o.stream=t,o.audioContext.createMediaStreamSource(t)})},i.prototype.initWorker=function(){var t=this,o=function(e){t.streamPage(e.data)},i=function(e){t.storePage(e.data)};this.recordedPages=[],this.totalLength=0,this.encoder=new e.Worker(this.config.encoderPath),this.encoder.addEventListener("message",this.config.streamPages?o:i)},i.prototype.pause=function(){"recording"===this.state&&(this.state="paused",this.onpause())},i.prototype.resume=function(){"paused"===this.state&&(this.state="recording",this.onresume())},i.prototype.setRecordingGain=function(t){this.config.recordingGain=t,this.recordingGainNode&&this.audioContext&&this.recordingGainNode.gain.setTargetAtTime(t,this.audioContext.currentTime,.01)},i.prototype.setMonitorGain=function(t){this.config.monitorGain=t,this.monitorGainNode&&this.audioContext&&this.monitorGainNode.gain.setTargetAtTime(t,this.audioContext.currentTime,.01)},i.prototype.start=function(t){if("inactive"===this.state){var e=this;return this.initAudioContext(t),this.initAudioGraph(),this.initSourceNode(t).then(function(t){e.state="recording",e.encoder.postMessage(Object.assign({command:"init",originalSampleRate:e.audioContext.sampleRate,wavSampleRate:e.audioContext.sampleRate},e.config)),e.sourceNode=t,e.sourceNode.connect(e.monitorGainNode),e.sourceNode.connect(e.recordingGainNode),e.onstart()})}},i.prototype.stop=function(){"inactive"!==this.state&&(this.state="inactive",this.monitorGainNode.disconnect(),this.scriptProcessorNode.disconnect(),this.recordingGainNode.disconnect(),this.sourceNode.disconnect(),this.config.leaveStreamOpen||this.clearStream(),this.encoder.postMessage({command:"done"}))},i.prototype.storePage=function(t){if(null===t){var e=new Uint8Array(this.totalLength);this.recordedPages.reduce(function(t,o){return e.set(o,t),t+o.length},0),this.ondataavailable(e),this.initWorker(),this.onstop()}else this.recordedPages.push(t),this.totalLength+=t.length},i.prototype.streamPage=function(t){null===t?(this.initWorker(),this.onstop()):this.ondataavailable(t)},i.prototype.ondataavailable=function(){},i.prototype.onpause=function(){},i.prototype.onresume=function(){},i.prototype.onstart=function(){},i.prototype.onstop=function(){},t.exports=i}).call(e,o(1))},function(t,e){var o;o=function(){return this}();try{o=o||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(o=window)}t.exports=o}])});