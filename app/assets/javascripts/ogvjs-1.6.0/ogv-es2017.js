!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ogvjs=t():e.ogvjs=t()}(window,function(){return function(e){var t={};function __webpack_require__(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,__webpack_require__),r.l=!0,r.exports}return __webpack_require__.m=e,__webpack_require__.c=t,__webpack_require__.d=function(e,t,i){__webpack_require__.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__webpack_require__.t=function(e,t){if(1&t&&(e=__webpack_require__(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(__webpack_require__.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)__webpack_require__.d(i,r,function(t){return e[t]}.bind(null,r));return i},__webpack_require__.n=function(e){var t=e&&e.__esModule?function getDefault(){return e.default}:function getModuleExports(){return e};return __webpack_require__.d(t,"a",t),t},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.p="",__webpack_require__(__webpack_require__.s=13)}([function(e,t){e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}},function(e,t){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(e){"object"==typeof window&&(i=window)}e.exports=i},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(17)),s=r(i(18)),o=r(i(19));const a={audio:{proxy:n.default,worker:"ogv-worker-audio.js"},video:{proxy:s.default,worker:"ogv-worker-video.js"}},u={OGVDecoderAudioOpus:"audio",OGVDecoderAudioOpusW:"audio",OGVDecoderAudioVorbis:"audio",OGVDecoderAudioVorbisW:"audio",OGVDecoderVideoTheora:"video",OGVDecoderVideoTheoraW:"video",OGVDecoderVideoVP8:"video",OGVDecoderVideoVP8W:"video",OGVDecoderVideoVP9:"video",OGVDecoderVideoVP9W:"video",OGVDecoderVideoAV1:"video",OGVDecoderVideoAV1W:"video"};var d=new class OGVLoaderWeb extends o.default{constructor(){super(),this.scriptStatus={},this.scriptCallbacks={}}getGlobal(){return window}defaultBase(){let e,t,i=document.querySelectorAll("script"),r=/^(?:|(.*)\/)ogv(?:-support|-es2017)?\.js(?:\?|#|$)/;for(let n=0;n<i.length;n++)if((e=i[n].getAttribute("src"))&&(t=e.match(r)))return t[1]}loadClass(e,t,i){(i=i||{}).worker?this.workerProxy(e,t):super.loadClass(e,t,i)}loadScript(e,t){if("done"==this.scriptStatus[e])t();else if("loading"==this.scriptStatus[e])this.scriptCallbacks[e].push(t);else{this.scriptStatus[e]="loading",this.scriptCallbacks[e]=[t];let i=document.createElement("script"),r=t=>{let i=this.scriptCallbacks[e];delete this.scriptCallbacks[e],this.scriptStatus[e]="done",i.forEach(e=>{e()})};i.addEventListener("load",r),i.addEventListener("error",r),i.src=e,document.querySelector("head").appendChild(i)}}workerProxy(e,t){let i=u[e],r=a[i];if(!r)throw new Error("Requested worker for class with no proxy: "+e);let n,s=r.proxy,o=r.worker,d=this.urlForScript(this.scriptForClass(e)),h=this.urlForScript(o);var c=function construct(t){return new s(n,e,t)};if(h.match(/^https?:|\/\//i)){var l,f,p,_,m,g=!1,y=!1;function completionCheck(){if(1==g&&1==y){try{m=new Blob([p+" "+_],{type:"application/javascript"})}catch(e){window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder,(m=new BlobBuilder).append(p+" "+_),m=m.getBlob()}n=new Worker(URL.createObjectURL(m)),t(function(e){return Promise.resolve(new c(e))})}}(l=new XMLHttpRequest).open("GET",d,!0),l.onreadystatechange=function(){4==l.readyState&&200==l.status&&(p=l.responseText,g=!0,completionCheck())},l.send(),(f=new XMLHttpRequest).open("GET",h,!0),f.onreadystatechange=function(){4==f.readyState&&200==f.status&&(_=f.responseText,y=!0,completionCheck())},f.send()}else n=new Worker(h),t(function(e){return Promise.resolve(new c(e))})}};t.default=d},function(e,t){!function(){"use strict";function FrameSink(e,t){throw new Error("abstract")}FrameSink.prototype.drawFrame=function(e){throw new Error("abstract")},FrameSink.prototype.clear=function(){throw new Error("abstract")},e.exports=FrameSink}()},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var n=i(11),s=function(e){function DownloadBackend(){return function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,DownloadBackend),function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(DownloadBackend.__proto__||Object.getPrototypeOf(DownloadBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(DownloadBackend,n),r(DownloadBackend,[{key:"bufferToOffset",value:function bufferToOffset(e){var t=this;return new Promise(function(i,r){t.eof||t.offset>=e?i():function(){var n=null;t._onAbort=function(e){n(),r(e)};var s=function checkBuffer(){t.offset>=e&&!t.eof&&(n(),i())},o=function checkDone(){n(),i()},a=function checkError(){n(),r(new Error("error streaming"))};n=function oncomplete(){t.buffering=!1,t.off("buffer",s),t.off("done",o),t.off("error",a),t._onAbort=null},t.buffering=!0,t.on("buffer",s),t.on("done",o),t.on("error",a)}()})}},{key:"initXHR",value:function initXHR(){(function get(e,t,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,t);if(void 0===r){var n=Object.getPrototypeOf(e);return null===n?void 0:get(n,t,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0})(DownloadBackend.prototype.__proto__||Object.getPrototypeOf(DownloadBackend.prototype),"initXHR",this).call(this)}},{key:"onXHRStart",value:function onXHRStart(){var e=this;this.xhr.addEventListener("progress",function(){return e.onXHRProgress()}),this.xhr.addEventListener("error",function(){return e.onXHRError()}),this.xhr.addEventListener("load",function(){return e.onXHRLoad()}),this.emit("open")}},{key:"onXHRProgress",value:function onXHRProgress(){throw new Error("abstract")}},{key:"onXHRError",value:function onXHRError(){this.emit("error")}},{key:"onXHRLoad",value:function onXHRLoad(){this.eof=!0,this.emit("done")}}]),DownloadBackend}();e.exports=s},function(e,t){var i,r,n=e.exports={};function defaultSetTimout(){throw new Error("setTimeout has not been defined")}function defaultClearTimeout(){throw new Error("clearTimeout has not been defined")}function runTimeout(e){if(i===setTimeout)return setTimeout(e,0);if((i===defaultSetTimout||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(t){try{return i.call(null,e,0)}catch(t){return i.call(this,e,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){i=defaultSetTimout}try{r="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){r=defaultClearTimeout}}();var s,o=[],a=!1,u=-1;function cleanUpNextTick(){a&&s&&(a=!1,s.length?o=s.concat(o):u=-1,o.length&&drainQueue())}function drainQueue(){if(!a){var e=runTimeout(cleanUpNextTick);a=!0;for(var t=o.length;t;){for(s=o,o=[];++u<t;)s&&s[u].run();u=-1,t=o.length}s=null,a=!1,function runClearTimeout(e){if(r===clearTimeout)return clearTimeout(e);if((r===defaultClearTimeout||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function Item(e,t){this.fun=e,this.array=t}function noop(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)t[i-1]=arguments[i];o.push(new Item(e,t)),1!==o.length||a||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=noop,n.addListener=noop,n.once=noop,n.off=noop,n.removeListener=noop,n.removeAllListeners=noop,n.emit=noop,n.prependListener=noop,n.prependOnceListener=noop,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=
/**
 * Proxy object for web worker interface for codec classes.
 *
 * Used by the high-level player interface.
 *
 * <AUTHOR> Vibber <<EMAIL>>
 * @copyright 2015-2019 Brion Vibber
 * @license MIT-style
 */
function OGVProxyClass(e){return class{constructor(t,i,r){r=r||{},this.worker=t,this.transferables=function(){let e=new ArrayBuffer(1024),i=new Uint8Array(e);try{return t.postMessage({action:"transferTest",bytes:i},[e]),!e.byteLength}catch(e){return!1}}();for(let t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);this.processingQueue=0,Object.defineProperty(this,"processing",{get:function get(){return this.processingQueue>0}}),this.messageCount=0,this.pendingCallbacks={},this.worker.addEventListener("message",e=>{this.handleMessage(e)}),this.proxy("construct",[i,r],()=>{})}proxy(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];if(!this.worker)throw'Tried to call "'+e+'" method on closed proxy object';let n="callback-"+ ++this.messageCount+"-"+e;i&&(this.pendingCallbacks[n]=i);let s={action:e,callbackId:n,args:t||[]};this.processingQueue++,this.transferables?this.worker.postMessage(s,r):this.worker.postMessage(s)}terminate(){this.worker&&(this.worker.terminate(),this.worker=null,this.processingQueue=0,this.pendingCallbacks={})}handleMessage(e){if(this.processingQueue--,"callback"!==e.data.action)return;let t=e.data,i=t.callbackId,r=t.args,n=this.pendingCallbacks[i];if(t.props)for(let e in t.props)t.props.hasOwnProperty(e)&&(this[e]=t.props[e]);n&&(delete this.pendingCallbacks[i],n.apply(this,r))}}};t.default=r},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(8));const s={MEDIA_ERR_ABORTED:1,MEDIA_ERR_NETWORK:2,MEDIA_ERR_DECODE:3,MEDIA_ERR_SRC_NOT_SUPPORTED:4};class OGVMediaError{constructor(e,t){this.code=e,this.message=t}}(0,n.default)(OGVMediaError,s),(0,n.default)(OGVMediaError.prototype,s);var o=OGVMediaError;t.default=o},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function extend(e,t){for(let i in t)t.hasOwnProperty(i)&&(e[i]=t[i])};t.default=r},function(e,t,i){"use strict";function split(e,t,i){let r=e.split(t,i).map(e=>(function trim(e){return e.replace(/^\s+/,"").replace(/\s+$/,"")})(e));if("number"==typeof i)for(;r.length<i;)r.push(null);return r}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=class OGVMediaType{constructor(e){e=String(e),this.major=null,this.minor=null,this.codecs=null;let t=split(e,";");if(t.length){let e=t.shift();if(e){let t=split(e,"/",2);this.major=t[0],this.minor=t[1]}for(let e in t){let i=t[e].match(/^codecs\s*=\s*"(.*?)"$/);if(i){this.codecs=split(i[1],",");break}}}}};t.default=r},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var n=function(){function TinyEvents(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,TinyEvents),this._e={}}return r(TinyEvents,[{key:"on",value:function on(e,t){(this._e[e]||(this._e[e]=[])).push(t)}},{key:"off",value:function off(e,t){var i=this._e[e]||[],r=i.indexOf(t);t>=0&&i.splice(r,1)}},{key:"emit",value:function emit(e,t){(this._e[e]||[]).slice().forEach(function(e){return e(t)})}}]),TinyEvents}();e.exports=n},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var n=i(10);function getXHRLength(e){if(206==e.status)return function getXHRRangeTotal(e){var t=getXHRRangeMatches(e);return t?parseInt(t[3],10):-1}(e);var t=e.getResponseHeader("Content-Length");return null===t||""===t?-1:parseInt(t,10)}function getXHRRangeMatches(e){var t=e.getResponseHeader("Content-Range");return t&&t.match(/^bytes (\d+)-(\d+)\/(\d+)/)}var s=function(e){function Backend(e){var t=e.url,i=e.offset,r=e.length,n=e.cachever,s=void 0===n?0:n;!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Backend);var o=function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(Backend.__proto__||Object.getPrototypeOf(Backend)).call(this));return o.url=t,o.offset=i,o.length=r,o.cachever=s,o.loaded=!1,o.seekable=!1,o.headers={},o.eof=!1,o.bytesRead=0,o.xhr=new XMLHttpRequest,o}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(Backend,n),r(Backend,[{key:"load",value:function load(){var e=this;return new Promise(function(t,i){var r=null;e._onAbort=function(e){r(),i(e)};var n=function checkOpen(){if(2==e.xhr.readyState){if(206==e.xhr.status){var n=function getXHRRangeStart(e){var t=getXHRRangeMatches(e);return t?parseInt(t[1],10):0}(e.xhr);if(e.offset!=n)return console.log("Expected start at "+e.offset+" but got "+n+"; working around Safari range caching bug: https://bugs.webkit.org/show_bug.cgi?id=82672"),e.cachever++,e.emit("cachever"),e.abort(),r(),void e.load().then(t).catch(i);e.seekable=!0}e.xhr.status>=200&&e.xhr.status<300?(e.length=getXHRLength(e.xhr),e.headers=function getXHRHeaders(e){var t={};return e.getAllResponseHeaders().split(/\r?\n/).forEach(function(e){var i=e.split(/:\s*/,2);i.length>1&&(t[i[0].toLowerCase()]=i[1])}),t}(e.xhr),e.onXHRStart()):(r(),i(new Error("HTTP error "+e.xhr.status)))}},s=function checkError(){r(),i(new Error("network error"))},o=function checkBackendOpen(){r(),t()};r=function oncomplete(){e.xhr.removeEventListener("readystatechange",n),e.xhr.removeEventListener("error",s),e.off("open",o),e._onAbort=null},e.initXHR(),e.xhr.addEventListener("readystatechange",n),e.xhr.addEventListener("error",s),e.on("open",o),e.xhr.send()})}},{key:"bufferToOffset",value:function bufferToOffset(e){return Promise.reject(new Error("abstract"))}},{key:"abort",value:function abort(){if(this.xhr.abort(),this._onAbort){var e=this._onAbort;this._onAbort=null;var t=new Error("Aborted");t.name="AbortError",e(t)}}},{key:"initXHR",value:function initXHR(){var e=this.url;this.cachever&&(e+="?buggy_cachever="+this.cachever),this.xhr.open("GET",e);var t=null;(this.offset||this.length)&&(t="bytes="+this.offset+"-"),this.length&&(t+=this.offset+this.length-1),null!==t&&this.xhr.setRequestHeader("Range",t)}},{key:"onXHRStart",value:function onXHRStart(){throw new Error("abstract")}}]),Backend}();e.exports=s},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=class OGVTimeRanges{constructor(e){this._ranges=e,this.length=e.length}start(e){if(e<0||e>this.length||e!==(0|e))throw new RangeError("Invalid index");return this._ranges[e][0]}end(e){if(e<0||e>this.length||e!==(0|e))throw new RangeError("Invalid index");return this._ranges[e][1]}};t.default=r},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"OGVCompat",{enumerable:!0,get:function get(){return s.default}}),Object.defineProperty(t,"OGVLoader",{enumerable:!0,get:function get(){return o.default}}),Object.defineProperty(t,"OGVMediaError",{enumerable:!0,get:function get(){return a.default}}),Object.defineProperty(t,"OGVMediaType",{enumerable:!0,get:function get(){return u.default}}),Object.defineProperty(t,"OGVPlayer",{enumerable:!0,get:function get(){return d.default}}),Object.defineProperty(t,"OGVTimeRanges",{enumerable:!0,get:function get(){return h.default}}),t.OGVVersion=void 0;var n=r(i(14)),s=r(i(15)),o=r(i(2)),a=r(i(7)),u=r(i(9)),d=r(i(21)),h=r(i(12));n.default.polyfill();t.OGVVersion="1.6.0-20190226222001-c4648f0","object"==typeof window&&(window.OGVCompat=s.default,window.OGVLoader=o.default,window.OGVMediaError=a.default,window.OGVMediaType=u.default,window.OGVTimeRanges=h.default,window.OGVPlayer=d.default,window.OGVVersion="1.6.0-20190226222001-c4648f0")},function(e,t,i){(function(t,i){
/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.5+7f2b526d
 */var r;r=function(){"use strict";function isFunction(e){return"function"==typeof e}var e=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},r=0,n=void 0,s=void 0,o=function asap(e,t){l[r]=e,l[r+1]=t,2===(r+=2)&&(s?s(flush):f())},a="undefined"!=typeof window?window:void 0,u=a||{},d=u.MutationObserver||u.WebKitMutationObserver,h="undefined"==typeof self&&void 0!==t&&"[object process]"==={}.toString.call(t),c="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function useSetTimeout(){var e=setTimeout;return function(){return e(flush,1)}}var l=new Array(1e3);function flush(){for(var e=0;e<r;e+=2){(0,l[e])(l[e+1]),l[e]=void 0,l[e+1]=void 0}r=0}var f=void 0;function then(e,t){var i=this,r=new this.constructor(noop);void 0===r[p]&&makePromise(r);var n=i._state;if(n){var s=arguments[n-1];o(function(){return invokeCallback(n,r,s,i._result)})}else subscribe(i,r,e,t);return r}function resolve$1(e){if(e&&"object"==typeof e&&e.constructor===this)return e;var t=new this(noop);return resolve(t,e),t}f=h?function useNextTick(){return function(){return t.nextTick(flush)}}():d?function useMutationObserver(){var e=0,t=new d(flush),i=document.createTextNode("");return t.observe(i,{characterData:!0}),function(){i.data=e=++e%2}}():c?function useMessageChannel(){var e=new MessageChannel;return e.port1.onmessage=flush,function(){return e.port2.postMessage(0)}}():void 0===a?function attemptVertx(){try{var e=Function("return this")().require("vertx");return n=e.runOnLoop||e.runOnContext,function useVertxTimer(){return void 0!==n?function(){n(flush)}:useSetTimeout()}()}catch(e){return useSetTimeout()}}():useSetTimeout();var p=Math.random().toString(36).substring(2);function noop(){}var _=void 0,m=1,g=2,y={error:null};function getThen(e){try{return e.then}catch(e){return y.error=e,y}}function handleMaybeThenable(e,t,i){t.constructor===e.constructor&&i===then&&t.constructor.resolve===resolve$1?function handleOwnThenable(e,t){t._state===m?fulfill(e,t._result):t._state===g?reject(e,t._result):subscribe(t,void 0,function(t){return resolve(e,t)},function(t){return reject(e,t)})}(e,t):i===y?(reject(e,y.error),y.error=null):void 0===i?fulfill(e,t):isFunction(i)?function handleForeignThenable(e,t,i){o(function(e){var r=!1,n=function tryThen(e,t,i,r){try{e.call(t,i,r)}catch(e){return e}}(i,t,function(i){r||(r=!0,t!==i?resolve(e,i):fulfill(e,i))},function(t){r||(r=!0,reject(e,t))},e._label);!r&&n&&(r=!0,reject(e,n))},e)}(e,t,i):fulfill(e,t)}function resolve(e,t){e===t?reject(e,function selfFulfillment(){return new TypeError("You cannot resolve a promise with itself")}()):!function objectOrFunction(e){var t=typeof e;return null!==e&&("object"===t||"function"===t)}(t)?fulfill(e,t):handleMaybeThenable(e,t,getThen(t))}function publishRejection(e){e._onerror&&e._onerror(e._result),publish(e)}function fulfill(e,t){e._state===_&&(e._result=t,e._state=m,0!==e._subscribers.length&&o(publish,e))}function reject(e,t){e._state===_&&(e._state=g,e._result=t,o(publishRejection,e))}function subscribe(e,t,i,r){var n=e._subscribers,s=n.length;e._onerror=null,n[s]=t,n[s+m]=i,n[s+g]=r,0===s&&e._state&&o(publish,e)}function publish(e){var t=e._subscribers,i=e._state;if(0!==t.length){for(var r=void 0,n=void 0,s=e._result,o=0;o<t.length;o+=3)r=t[o],n=t[o+i],r?invokeCallback(i,r,n,s):n(s);e._subscribers.length=0}}function invokeCallback(e,t,i,r){var n=isFunction(i),s=void 0,o=void 0,a=void 0,u=void 0;if(n){if((s=function tryCatch(e,t){try{return e(t)}catch(e){return y.error=e,y}}(i,r))===y?(u=!0,o=s.error,s.error=null):a=!0,t===s)return void reject(t,function cannotReturnOwn(){return new TypeError("A promises callback cannot return that same promise.")}())}else s=r,a=!0;t._state!==_||(n&&a?resolve(t,s):u?reject(t,o):e===m?fulfill(t,s):e===g&&reject(t,s))}var b=0;function makePromise(e){e[p]=b++,e._state=void 0,e._result=void 0,e._subscribers=[]}var v=function(){function Enumerator(t,i){this._instanceConstructor=t,this.promise=new t(noop),this.promise[p]||makePromise(this.promise),e(i)?(this.length=i.length,this._remaining=i.length,this._result=new Array(this.length),0===this.length?fulfill(this.promise,this._result):(this.length=this.length||0,this._enumerate(i),0===this._remaining&&fulfill(this.promise,this._result))):reject(this.promise,function validationError(){return new Error("Array Methods must be provided an Array")}())}return Enumerator.prototype._enumerate=function _enumerate(e){for(var t=0;this._state===_&&t<e.length;t++)this._eachEntry(e[t],t)},Enumerator.prototype._eachEntry=function _eachEntry(e,t){var i=this._instanceConstructor,r=i.resolve;if(r===resolve$1){var n=getThen(e);if(n===then&&e._state!==_)this._settledAt(e._state,t,e._result);else if("function"!=typeof n)this._remaining--,this._result[t]=e;else if(i===T){var s=new i(noop);handleMaybeThenable(s,e,n),this._willSettleAt(s,t)}else this._willSettleAt(new i(function(t){return t(e)}),t)}else this._willSettleAt(r(e),t)},Enumerator.prototype._settledAt=function _settledAt(e,t,i){var r=this.promise;r._state===_&&(this._remaining--,e===g?reject(r,i):this._result[t]=i),0===this._remaining&&fulfill(r,this._result)},Enumerator.prototype._willSettleAt=function _willSettleAt(e,t){var i=this;subscribe(e,void 0,function(e){return i._settledAt(m,t,e)},function(e){return i._settledAt(g,t,e)})},Enumerator}(),T=function(){function Promise(e){this[p]=function nextId(){return b++}(),this._result=this._state=void 0,this._subscribers=[],noop!==e&&("function"!=typeof e&&function needsResolver(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof Promise?function initializePromise(e,t){try{t(function resolvePromise(t){resolve(e,t)},function rejectPromise(t){reject(e,t)})}catch(t){reject(e,t)}}(this,e):function needsNew(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}return Promise.prototype.catch=function _catch(e){return this.then(null,e)},Promise.prototype.finally=function _finally(e){var t=this.constructor;return isFunction(e)?this.then(function(i){return t.resolve(e()).then(function(){return i})},function(i){return t.resolve(e()).then(function(){throw i})}):this.then(e,e)},Promise}();return T.prototype.then=then,T.all=function all(e){return new v(this,e).promise},T.race=function race(t){var i=this;return e(t)?new i(function(e,r){for(var n=t.length,s=0;s<n;s++)i.resolve(t[s]).then(e,r)}):new i(function(e,t){return t(new TypeError("You must pass an array to race."))})},T.resolve=resolve$1,T.reject=function reject$1(e){var t=new this(noop);return reject(t,e),t},T._setScheduler=function setScheduler(e){s=e},T._setAsap=function setAsap(e){o=e},T._asap=o,T.polyfill=function polyfill(){var e=void 0;if(void 0!==i)e=i;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var r=null;try{r=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===r&&!t.cast)return}e.Promise=T},T.Promise=T,T},e.exports=r()}).call(this,i(5),i(1))},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(16));var s=new class{constructor(){this.benchmark=new n.default}hasTypedArrays(){return!!window.Uint32Array}hasWebAudio(){return!(!window.AudioContext&&!window.webkitAudioContext)}hasFlash(){if(-1!==navigator.userAgent.indexOf("Trident"))try{new ActiveXObject("ShockwaveFlash.ShockwaveFlash");return!0}catch(e){return!1}return!1}hasAudio(){return this.hasWebAudio()||this.hasFlash()}isBlacklisted(e){let t=!1;return[/\(i.* OS [6789]_.* like Mac OS X\).* Mobile\/.* Safari\//,/\(Macintosh.* Version\/6\..* Safari\/\d/].forEach(i=>{e.match(i)&&(t=!0)}),t}isSlow(){return this.benchmark.slow}isTooSlow(){return this.benchmark.tooSlow}supported(e){return"OGVDecoder"===e?this.hasTypedArrays()&&!this.isBlacklisted(navigator.userAgent):"OGVPlayer"===e&&(this.supported("OGVDecoder")&&this.hasAudio()&&!this.isTooSlow())}};t.default=s},function(e,t,i){"use strict";e.exports=function BogoSlow(){var e,t=this;e=window.performance&&window.performance.now?function timer(){return window.performance.now()}:function timer(){return Date.now()};var i=null;Object.defineProperty(t,"speed",{get:function get(){return null===i&&function run(){var t=0,r=e();!function fibonacci(e){return t++,e<2?e:fibonacci(e-2)+fibonacci(e-1)}(30);var n=e()-r;i=t/n}(),i}}),Object.defineProperty(t,"slowCutoff",{get:function get(){return 5e4}}),Object.defineProperty(t,"tooSlowCutoff",{get:function get(){return 0}}),Object.defineProperty(t,"slow",{get:function get(){return t.speed<t.slowCutoff}}),Object.defineProperty(t,"tooSlow",{get:function get(){return t.speed<t.tooSlowCutoff}})}},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(6));var s=class OGVDecoderAudioProxy extends((0,n.default)({loadedMetadata:!1,audioFormat:null,audioBuffer:null,cpuTime:0})){init(e){this.proxy("init",[],e)}processHeader(e,t){this.proxy("processHeader",[e],t,[e])}processAudio(e,t){this.proxy("processAudio",[e],t,[e])}close(){this.terminate()}};t.default=s},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(6));var s=class OGVDecoderVideoProxy extends((0,n.default)({loadedMetadata:!1,videoFormat:null,frameBuffer:null,cpuTime:0})){init(e){this.proxy("init",[],e)}processHeader(e,t){this.proxy("processHeader",[e],t,[e])}processFrame(e,t){this.proxy("processFrame",[e],t,[e])}close(){this.terminate()}sync(){this.proxy("sync",[],()=>{})}};t.default=s},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(20));const s="1.6.0-20190226222001-c4648f0",o={OGVDemuxerOgg:"ogv-demuxer-ogg.js",OGVDemuxerOggW:"ogv-demuxer-ogg-wasm.js",OGVDemuxerWebM:"ogv-demuxer-webm.js",OGVDemuxerWebMW:"ogv-demuxer-webm-wasm.js",OGVDecoderAudioOpus:"ogv-decoder-audio-opus.js",OGVDecoderAudioOpusW:"ogv-decoder-audio-opus-wasm.js",OGVDecoderAudioVorbis:"ogv-decoder-audio-vorbis.js",OGVDecoderAudioVorbisW:"ogv-decoder-audio-vorbis-wasm.js",OGVDecoderVideoTheora:"ogv-decoder-video-theora.js",OGVDecoderVideoTheoraW:"ogv-decoder-video-theora-wasm.js",OGVDecoderVideoVP8:"ogv-decoder-video-vp8.js",OGVDecoderVideoVP8W:"ogv-decoder-video-vp8-wasm.js",OGVDecoderVideoVP8MTW:"ogv-decoder-video-vp8-mt-wasm.js",OGVDecoderVideoVP9:"ogv-decoder-video-vp9.js",OGVDecoderVideoVP9W:"ogv-decoder-video-vp9-wasm.js",OGVDecoderVideoVP9MTW:"ogv-decoder-video-vp9-mt-wasm.js",OGVDecoderVideoAV1:"ogv-decoder-video-av1.js",OGVDecoderVideoAV1W:"ogv-decoder-video-av1-wasm.js",OGVDecoderVideoAV1MTW:"ogv-decoder-video-av1-mt-wasm.js"};var a=class OGVLoaderBase{constructor(){this.base=this.defaultBase()}defaultBase(){}wasmSupported(){return n.default.wasmSupported()}scriptForClass(e){return o[e]}urlForClass(e){let t=this.scriptForClass(e);if(t)return this.urlForScript(t);throw new Error("asked for URL for unknown class "+e)}urlForScript(e){if(e){let t=this.base;return void 0===t?t="":t+="/",t+e+"?version="+encodeURIComponent(s)}throw new Error("asked for URL for unknown script "+e)}loadClass(e,t,i){i=i||{};let r=this.getGlobal(),n=this.urlForClass(e),o=t=>((t=t||{}).locateFile=(e=>"data:"===e.slice(0,5)?e:this.urlForScript(e)),t.mainScriptUrlOrBlob=this.scriptForClass(e)+"?version="+encodeURIComponent(s),r[e](t));"function"==typeof r[e]?t(o):this.loadScript(n,()=>{t(o)})}};t.default=a},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=new class WebAssemblyChecker{constructor(){this.tested=!1,this.testResult=void 0}wasmSupported(){if(!this.tested){try{"object"==typeof WebAssembly?this.testResult=function testSafariWebAssemblyBug(){let e=new Uint8Array([0,97,115,109,1,0,0,0,1,6,1,96,1,127,1,127,3,2,1,0,5,3,1,0,1,7,8,1,4,116,101,115,116,0,0,10,16,1,14,0,32,0,65,1,54,2,0,32,0,40,2,0,11]),t=new WebAssembly.Module(e);return 0!==new WebAssembly.Instance(t,{}).exports.test(4)}():this.testResult=!1}catch(e){console.log("Exception while testing WebAssembly",e),this.testResult=!1}this.tested=!0}return this.testResult}};t.default=r},function(e,t,i){"use strict";(function(e){var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=r(i(24)),o=r(i(30)),a=r(i(39)),u=(r(i(40)),r(i(2))),d=r(i(41)),h=r(i(8)),c=r(i(7)),l=r(i(9)),f=r(i(12)),p=r(i(42));const _={NETWORK_EMPTY:0,NETWORK_IDLE:1,NETWORK_LOADING:2,NETWORK_NO_SOURCE:3,HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},m={INITIAL:"INITIAL",SEEKING_END:"SEEKING_END",LOADED:"LOADED",PRELOAD:"PRELOAD",READY:"READY",PLAYING:"PLAYING",SEEKING:"SEEKING",ENDED:"ENDED",ERROR:"ERROR"},g={NOT_SEEKING:"NOT_SEEKING",BISECT_TO_TARGET:"BISECT_TO_TARGET",BISECT_TO_KEYPOINT:"BISECT_TO_KEYPOINT",LINEAR_TO_TARGET:"LINEAR_TO_TARGET"},y={EXACT:"exact",FAST:"fast"};let b;function OGVJSElement(){let e=document.createElement("ogvjs");return Object.setPrototypeOf?Object.setPrototypeOf(e,Object.getPrototypeOf(this)):e.__proto__=this.__proto__,e}b="undefined"==typeof performance||void 0===typeof performance.now?Date.now:performance.now.bind(performance),OGVJSElement.prototype=Object.create(HTMLElement.prototype,{});class OGVPlayer extends OGVJSElement{constructor(e){super(),(e=e||{}).base=e.base||u.default.base,this._options=e,this._instanceId="ogvjs"+ ++OGVPlayer.instanceCount,void 0!==e.worker?this._enableWorker=!!e.worker:this._enableWorker=!!window.Worker,void 0!==e.wasm?this._enableWASM=!!e.wasm:this._enableWASM=u.default.wasmSupported(),this._enableThreading=!!e.threading,this._state=m.INITIAL,this._seekState=g.NOT_SEEKING,this._detectedType=null,this._canvas=document.createElement("canvas"),this._frameSink=null,this.className=this._instanceId,(0,h.default)(this,_),this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0",this._canvas.style.width="100%",this._canvas.style.height="100%",this._canvas.style.objectFit="contain",this.appendChild(this._canvas),this._startTime=b(),this._codec=null,this._audioInfo=null,this._videoInfo=null,this._actionQueue=[],this._audioFeeder=null,this._muted=!1,this._initialPlaybackPosition=0,this._initialPlaybackOffset=0,this._prebufferingAudio=!1,this._initialSeekTime=0,this._currentSrc="",this._streamEnded=!1,this._mediaError=null,this._dataEnded=!1,this._byteLength=0,this._duration=null,this._lastSeenTimestamp=null,this._nextProcessingTimer,this._nextFrameTimer=null,this._loading=!1,this._started=!1,this._paused=!0,this._ended=!1,this._startedPlaybackInDocument=!1,this._stream=void 0,this._framesProcessed=0,this._targetPerFrameTime=1e3/60,this._actualPerFrameTime=0,this._totalFrameTime=0,this._totalFrameCount=0,this._playTime=0,this._bufferTime=0,this._drawingTime=0,this._proxyTime=0,this._totalJitter=0,this._droppedAudio=0,this._delayedAudio=0,this._lateFrames=0,this._poster="",this._thumbnail=null,this._frameEndTimestamp=0,this._audioEndTimestamp=0,this._decodedFrames=[],this._pendingFrames=[],this._lastFrameDecodeTime=0,this._lastFrameVideoCpuTime=0,this._lastFrameAudioCpuTime=0,this._lastFrameDemuxerCpuTime=0,this._lastFrameDrawingTime=0,this._lastFrameBufferTime=0,this._lastFrameProxyTime=0,this._lastVideoCpuTime=0,this._lastAudioCpuTime=0,this._lastDemuxerCpuTime=0,this._lastBufferTime=0,this._lastProxyTime=0,this._lastDrawingTime=0,this._lastFrameTimestamp=0,this._currentVideoCpuTime=0,this._lastTimeUpdate=0,this._timeUpdateInterval=250,this._seekTargetTime=0,this._bisectTargetTime=0,this._seekMode=null,this._lastSeekPosition=null,this._seekBisector=null,this._didSeek=null,this._depth=0,this._needProcessing=!1,this._pendingFrame=0,this._pendingAudio=0,this._framePipelineDepth=8,this._frameParallelism=this._enableThreading?Math.min(4,navigator.hardwareConcurrency)||1:0,this._audioPipelineDepth=12,this._videoInfo=null,this._audioInfo=null,this._width=0,this._height=0,this._volume=1,Object.defineProperties(this,{src:{get:function getSrc(){return this.getAttribute("src")||""},set:function setSrc(e){this.setAttribute("src",e),this._loading=!1,this._prepForLoad("interactive")}},buffered:{get:function getBuffered(){let e;return e=this._stream&&this._byteLength&&this._duration?this._stream.getBufferedRanges().map(e=>e.map(e=>e/this._stream.length*this._duration)):[[0,0]],new f.default(e)}},seekable:{get:function getSeekable(){return this.duration<1/0&&this._stream&&this._stream.seekable&&this._codec&&this._codec.seekable?new f.default([[0,this._duration]]):new f.default([])}},currentTime:{get:function getCurrentTime(){return this._state==m.SEEKING?this._seekTargetTime:this._codec?this._state!=m.PLAYING||this._paused?this._initialPlaybackOffset:this._getPlaybackTime():this._initialSeekTime},set:function setCurrentTime(e){this._seek(e,y.EXACT)}},duration:{get:function getDuration(){return this._codec&&this._codec.loadedMetadata?null!==this._duration?this._duration:1/0:NaN}},paused:{get:function getPaused(){return this._paused}},ended:{get:function getEnded(){return this._ended}},seeking:{get:function getSeeking(){return this._state==m.SEEKING}},muted:{get:function getMuted(){return this._muted},set:function setMuted(e){this._muted=e,this._audioFeeder?this._audioFeeder.muted=this._muted:this._started&&!this._muted&&this._codec&&this._codec.hasAudio&&(this._log("unmuting: switching from timer to audio clock"),this._initAudioFeeder(),this._startPlayback(this._audioEndTimestamp)),this._fireEventAsync("volumechange")}},poster:{get:function getPoster(){return this._poster},set:function setPoster(e){if(this._poster=e,!this._started){this._thumbnail&&this.removeChild(this._thumbnail);let e=new Image;e.src=this._poster,e.className="ogvjs-poster",e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.width="100%",e.style.height="100%",e.style.objectFit="contain",e.style.visibility="hidden",e.addEventListener("load",()=>{this._thumbnail===e&&(OGVPlayer.styleManager.appendRule("."+this._instanceId,{width:e.naturalWidth+"px",height:e.naturalHeight+"px"}),OGVPlayer.updatePositionOnResize(),e.style.visibility="visible")}),this._thumbnail=e,this.appendChild(e)}}},videoWidth:{get:function getVideoWidth(){return this._videoInfo?this._videoInfo.displayWidth:0}},videoHeight:{get:function getVideoHeight(){return this._videoInfo?this._videoInfo.displayHeight:0}},ogvjsVideoFrameRate:{get:function getOgvJsVideoFrameRate(){return this._videoInfo?0==this._videoInfo.fps?this._totalFrameCount/(this._totalFrameTime/1e3):this._videoInfo.fps:0}},ogvjsAudioChannels:{get:function getOgvJsAudioChannels(){return this._audioInfo?this._audioInfo.channels:0}},ogvjsAudioSampleRate:{get:function getOgvJsAudioChannels(){return this._audioInfo?this._audioInfo.rate:0}},width:{get:function getWidth(){return this._width},set:function setWidth(e){this._width=parseInt(e,10),this.style.width=this._width+"px",OGVPlayer.updatePositionOnResize()}},height:{get:function getHeight(){return this._height},set:function setHeight(e){this._height=parseInt(e,10),this.style.height=this._height+"px",OGVPlayer.updatePositionOnResize()}},autoplay:{get:function getAutoplay(){return!1},set:function setAutoplay(e){}},controls:{get:function getControls(){return!1},set:function setControls(e){}},loop:{get:function getLoop(){return!1},set:function setLoop(e){}},crossOrigin:{get:function getCrossOrigin(){return null},set:function setCrossOrigin(e){}},currentSrc:{get:function getCurrentSrc(){return this._currentSrc}},defaultMuted:{get:function getDefaultMuted(){return!1}},defaultPlaybackRate:{get:function getDefaultPlaybackRate(){return 1}},error:{get:function getError(){return this._state===m.ERROR?this._mediaError?this._mediaError:new c.default("unknown error occurred in media procesing"):null}},preload:{get:function getPreload(){return this.getAttribute("preload")||""},set:function setPreload(e){this.setAttribute("preload",e)}},readyState:{get:function getReadyState(){return this._stream&&this._codec&&this._codec.loadedMetadata?OGVPlayer.HAVE_ENOUGH_DATA:OGVPlayer.HAVE_NOTHING}},networkState:{get:function getNetworkState(){return this._stream?this._stream.waiting?OGVPlayer.NETWORK_LOADING:OGVPlayer.NETWORK_IDLE:this.readyState==OGVPlayer.HAVE_NOTHING?OGVPlayer.NETWORK_EMPTY:OGVPlayer.NETWORK_NO_SOURCE}},playbackRate:{get:function getPlaybackRate(){return 1},set:function setPlaybackRate(e){}},played:{get:function getPlayed(){return new f.default([[0,this.currentTime]])}},volume:{get:function getVolume(){return this._volume},set:function setVolume(e){this._volume=+e,this._audioFeeder&&(this._audioFeeder.volume=this._volume),this._fireEventAsync("volumechange")}}}),this.onframecallback=null,this.onloadstate=null,this.onprogress=null,this.onsuspend=null,this.onabort=null,this.onemptied=null,this.onstalled=null,this.onloadedmetadata=null,this.onloadeddata=null,this.oncanplay=null,this.oncanplaythrough=null,this.onplaying=null,this.onwaiting=null,this.onseeking=null,this.onseeked=null,this.onended=null,this.ondurationchange=null,this.ontimeupdate=null,this.onplay=null,this.onpause=null,this.onratechange=null,this.onresize=null,this.onvolumechange=null}_time(e){let t=b();e();let i=b()-t;return this._lastFrameDecodeTime+=i,i}_log(e){let t=this._options;if(t.debug){let i=b()-this._startTime;t.debugFilter&&!e.match(t.debugFilter)||console.log("["+Math.round(10*i)/10+"ms] "+e)}}_fireEvent(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this._log("fireEvent "+e);let i,r="function"==typeof Event;r?i=new CustomEvent(e):(i=document.createEvent("Event")).initEvent(e,!1,!1);for(let e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);let n=this.dispatchEvent(i);!r&&"resize"===e&&this.onresize&&n&&this.onresize.call(this,i)}_fireEventAsync(t){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this._log("fireEventAsync "+t),e(()=>{this._fireEvent(t,i)})}static initSharedAudioContext(){a.default.initSharedAudioContext()}_initAudioFeeder(){let e=this._options,t={base:e.base||u.default.base,bufferSize:8192};e.audioContext&&(t.audioContext=e.audioContext),e.audioDestination&&(t.output=e.audioDestination);let i=this._audioFeeder=new a.default(t);i.init(this._audioInfo.channels,this._audioInfo.rate),i.bufferThreshold=1,i.volume=this.volume,i.muted=this.muted,i.onbufferlow=(()=>{this._log("onbufferlow"),this._stream&&(this._stream.buffering||this._stream.seeking)||this._pendingAudio||this._pingProcessing()}),i.onstarved=(()=>{this._dataEnded?this._log("onstarved: appear to have reached end of audio"):(this._log("onstarved: halting audio due to starvation"),this._stopPlayback(),this._prebufferingAudio=!0),this._isProcessing()||this._pingProcessing(0)})}_startPlayback(e){if(this._audioFeeder){this._audioFeeder.start();let e=this._audioFeeder.getPlaybackState();this._initialPlaybackPosition=e.playbackPosition}else this._initialPlaybackPosition=b()/1e3;void 0!==e&&(this._initialPlaybackOffset=e),this._prebufferingAudio=!1,this._log("continuing at "+this._initialPlaybackPosition+", "+this._initialPlaybackOffset)}_stopPlayback(){this._initialPlaybackOffset=this._getPlaybackTime(),this._log("pausing at "+this._initialPlaybackOffset),this._audioFeeder&&this._audioFeeder.stop()}_getPlaybackTime(e){if(this._prebufferingAudio||this._paused)return this._initialPlaybackOffset;{let t;return(t=this._audioFeeder?(e=e||this._audioFeeder.getPlaybackState()).playbackPosition:b()/1e3)-this._initialPlaybackPosition+this._initialPlaybackOffset}}_stopVideo(){this._log("STOPPING"),this._state=m.INITIAL,this._seekState=g.NOT_SEEKING,this._started=!1,this._ended=!1,this._frameEndTimestamp=0,this._audioEndTimestamp=0,this._lastFrameDecodeTime=0,this._prebufferingAudio=!1,this._actionQueue.splice(0,this._actionQueue.length),this._stream&&(this._stream.abort(),this._stream=null,this._streamEnded=!1),this._codec&&(this._codec.close(),this._codec=null,this._pendingFrame=0,this._pendingAudio=0,this._dataEnded=!1),this._videoInfo=null,this._audioInfo=null,this._audioFeeder&&(this._audioFeeder.close(),this._audioFeeder=null),this._nextProcessingTimer&&(clearTimeout(this._nextProcessingTimer),this._nextProcessingTimer=null),this._nextFrameTimer&&(clearTimeout(this._nextFrameTimer),this._nextFrameTimer=null),this._frameSink&&(this._frameSink.clear(),this._frameSink=null),this._decodedFrames&&(this._decodedFrames=[]),this._pendingFrames&&(this._pendingFrames=[]),this._initialSeekTime=0,this._initialPlaybackPosition=0,this._initialPlaybackOffset=0,this._duration=null}_doFrameComplete(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._startedPlaybackInDocument&&!document.body.contains(this)&&e(()=>{this.stop()});let i=b(),r=i-this._lastFrameTimestamp,s=this._actualPerFrameTime-this._targetPerFrameTime;this._totalJitter+=Math.abs(s),this._playTime+=r;let o={cpuTime:this._lastFrameDecodeTime,drawingTime:this._drawingTime-this._lastFrameDrawingTime,bufferTime:this._bufferTime-this._lastFrameBufferTime,proxyTime:this._proxyTime-this._lastFrameProxyTime,demuxerTime:0,videoTime:0,audioTime:0,clockTime:this._actualPerFrameTime,late:t.dropped,dropped:t.dropped};function n(e){return Math.round(10*e)/10}this._codec&&(o.demuxerTime=this._codec.demuxerCpuTime-this._lastFrameDemuxerCpuTime,o.videoTime+=this._currentVideoCpuTime-this._lastFrameVideoCpuTime,o.audioTime+=this._codec.audioCpuTime-this._lastFrameAudioCpuTime),o.cpuTime+=o.demuxerTime,this._lastFrameDecodeTime=0,this._lastFrameTimestamp=i,this._codec?(this._lastFrameVideoCpuTime=this._currentVideoCpuTime,this._lastFrameAudioCpuTime=this._codec.audioCpuTime,this._lastFrameDemuxerCpuTime=this._codec.demuxerCpuTime):(this._lastFrameVideoCpuTime=0,this._lastFrameAudioCpuTime=0,this._lastFrameDemuxerCpuTime=0),this._lastFrameDrawingTime=this._drawingTime,this._lastFrameBufferTime=this._bufferTime,this._lastFrameProxyTime=this._proxyTime,this._log("drew frame "+t.frameEndTimestamp+": clock time "+n(r)+" (jitter "+n(s)+") cpu: "+n(o.cpuTime)+" (mux: "+n(o.demuxerTime)+" buf: "+n(o.bufferTime)+" draw: "+n(o.drawingTime)+" proxy: "+n(o.proxyTime)+") vid: "+n(o.videoTime)+" aud: "+n(o.audioTime)),this._fireEventAsync("framecallback",o),(!this._lastTimeUpdate||i-this._lastTimeUpdate>=this._timeUpdateInterval)&&(this._lastTimeUpdate=i,this._fireEventAsync("timeupdate"))}_seekStream(e){this._stream.seeking&&this._stream.abort(),this._stream.buffering&&this._stream.abort(),this._streamEnded=!1,this._dataEnded=!1,this._ended=!1,this._stream.seek(e).then(()=>{this._readBytesAndWait()}).catch(e=>{this._onStreamError(e)})}_onStreamError(e){"AbortError"===e.name?this._log("i/o promise canceled; ignoring"):(this._log("i/o error: "+e),this._mediaError=new c.default(c.default.MEDIA_ERR_NETWORK,String(e)),this._state=m.ERROR,this._stopPlayback())}_seek(e,t){if(this._log("requested seek to "+e+", mode "+t),this.readyState==this.HAVE_NOTHING)return this._log("not yet loaded; saving seek position for later"),void(this._initialSeekTime=e);if(this._stream&&!this._stream.seekable)throw new Error("Cannot seek a non-seekable stream");if(this._codec&&!this._codec.seekable)throw new Error("Cannot seek in a non-seekable file");let i=i=>{this._stream&&this._stream.buffering&&this._stream.abort(),this._stream&&this._stream.seeking&&this._stream.abort(),this._actionQueue.splice(0,this._actionQueue.length),this._stopPlayback(),this._prebufferingAudio=!1,this._audioFeeder&&this._audioFeeder.flush(),this._state=m.SEEKING,this._seekTargetTime=e,this._seekMode=t,this._codec?this._codec.flush(i):i()};i(()=>{this._isProcessing()||this._pingProcessing(0)}),this._actionQueue.push(()=>{i(()=>{this._doSeek(e)})})}_doSeek(e){this._streamEnded=!1,this._dataEnded=!1,this._ended=!1,this._state=m.SEEKING,this._seekTargetTime=e,this._lastSeekPosition=-1,this._decodedFrames=[],this._pendingFrames=[],this._pendingFrame=0,this._pendingAudio=0,this._didSeek=!1,this._codec.seekToKeypoint(e,t=>{if(t)return this._seekState=g.LINEAR_TO_TARGET,this._fireEventAsync("seeking"),this._didSeek?void 0:void this._pingProcessing();this._codec.getKeypointOffset(e,e=>{e>0?(this._seekState=g.LINEAR_TO_TARGET,this._seekStream(e)):(this._seekState=g.BISECT_TO_TARGET,this._startBisection(this._seekTargetTime)),this._fireEventAsync("seeking")})})}_startBisection(e){let t=Math.max(0,this._stream.length-65536);this._bisectTargetTime=e,this._seekBisector=new d.default({start:0,end:t,process:(e,t,i)=>i!=this._lastSeekPosition&&(this._lastSeekPosition=i,this._codec.flush(()=>{this._seekStream(i)}),!0)}),this._seekBisector.start()}_continueSeekedPlayback(){this._seekState=g.NOT_SEEKING,this._state=m.READY,this._frameEndTimestamp=this._codec.frameTimestamp,this._audioEndTimestamp=this._codec.audioTimestamp,this._codec.hasAudio?this._seekTargetTime=this._codec.audioTimestamp:this._seekTargetTime=this._codec.frameTimestamp,this._initialPlaybackOffset=this._seekTargetTime;let e=()=>{this._lastTimeUpdate=this._seekTargetTime,this._fireEventAsync("timeupdate"),this._fireEventAsync("seeked"),this._isProcessing()||this._pingProcessing()};if(this._codec.hasVideo&&this._codec.frameReady)return this._codec.decodeFrame(t=>{t&&(this._thumbnail&&(this.removeChild(this._thumbnail),this._thumbnail=null),this._frameSink.drawFrame(this._codec.frameBuffer)),e()}),void this._codec.sync();e()}_doProcessLinearSeeking(){let e;if(e=this._codec.hasVideo?this._targetPerFrameTime/1e3:1/256,this._codec.hasVideo){if(this._pendingFrame)return;if(!this._codec.frameReady)return void this._codec.process(e=>{e?this._pingProcessing():this._streamEnded?(this._log("stream ended during linear seeking on video"),this._dataEnded=!0,this._continueSeekedPlayback()):this._readBytesAndWait()});if(this._seekMode===y.FAST&&this._codec.keyframeTimestamp==this._codec.frameTimestamp)return void this._continueSeekedPlayback();if(this._codec.frameTimestamp+e<this._seekTargetTime)return this._codec.decodeFrame(()=>{this._pingProcessing()}),void this._codec.sync();if(!this._codec.hasAudio)return void this._continueSeekedPlayback()}if(this._codec.hasAudio){if(this._pendingAudio)return;return this._codec.audioReady?this._codec.audioTimestamp+e<this._seekTargetTime?void this._codec.decodeAudio(()=>{this._pingProcessing()}):void this._continueSeekedPlayback():void this._codec.process(e=>{e?this._pingProcessing():this._streamEnded?(this._log("stream ended during linear seeking on audio"),this._dataEnded=!0,this._continueSeekedPlayback()):this._readBytesAndWait()})}}_doProcessBisectionSeek(){let e,t;if(this._codec.hasVideo)t=this._codec.frameTimestamp,e=this._targetPerFrameTime/1e3;else{if(!this._codec.hasAudio)throw new Error("Invalid seek state; no audio or video track available");t=this._codec.audioTimestamp,e=1/256}t<0?this._codec.process(e=>{if(e)this._pingProcessing();else if(this._streamEnded){if(this._log("stream ended during bisection seek"),!this._seekBisector.right())throw this._log("failed going back"),new Error("not sure what to do")}else this._readBytesAndWait()}):t-e/2>this._bisectTargetTime?this._seekBisector.left()||(this._log("close enough (left)"),this._seekTargetTime=t,this._continueSeekedPlayback()):t+e/2<this._bisectTargetTime?this._seekBisector.right()||(this._log("close enough (right)"),this._seekState=g.LINEAR_TO_TARGET,this._pingProcessing()):this._seekState==g.BISECT_TO_TARGET&&this._codec.hasVideo&&this._codec.keyframeTimestamp<this._codec.frameTimestamp?(this._log("finding the keypoint now"),this._seekState=g.BISECT_TO_KEYPOINT,this._startBisection(this._codec.keyframeTimestamp)):(this._log("straight seeking now"),this._seekState=g.LINEAR_TO_TARGET,this._pingProcessing())}_setupVideo(){this._videoInfo.fps>0?this._targetPerFrameTime=1e3/this._videoInfo.fps:this._targetPerFrameTime=16.667,this._canvas.width=this._videoInfo.displayWidth,this._canvas.height=this._videoInfo.displayHeight,OGVPlayer.styleManager.appendRule("."+this._instanceId,{width:this._videoInfo.displayWidth+"px",height:this._videoInfo.displayHeight+"px"}),OGVPlayer.updatePositionOnResize();let e={};void 0!==this._options.webGL&&(e.webGL=this._options.webGL),this._options.forceWebGL&&(e.webGL="required"),this._frameSink=s.default.attach(this._canvas,e)}_doProcessing(){if(this._didSeek&&(this._didSeek=!1),this._nextProcessingTimer=null,this._isProcessing(),this._depth>0)throw new Error("REENTRANCY FAIL: doProcessing recursing unexpectedly");let e=0;do{if(this._needProcessing=!1,this._depth++,this._doProcessingLoop(),this._depth--,this._needProcessing&&this._isProcessing())throw new Error("REENTRANCY FAIL: waiting on input or codec but asked to keep processing");++e>500&&(this._log("stuck in processing loop; breaking with timer"),this._needProcessing=0,this._pingProcessing(0))}while(this._needProcessing)}_doProcessingLoop(){if(this._actionQueue.length){this._actionQueue.shift()()}else if(this._state==m.INITIAL)this._doProcessInitial();else if(this._state==m.SEEKING_END)this._doProcessSeekingEnd();else if(this._state==m.LOADED)this._doProcessLoaded();else if(this._state==m.PRELOAD)this._doProcessPreload();else if(this._state==m.READY)this._doProcessReady();else if(this._state==m.SEEKING)this._doProcessSeeking();else if(this._state==m.PLAYING)this._doProcessPlay();else{if(this._state!=m.ERROR)throw new Error("Unexpected OGVPlayer state "+this._state);this._doProcessError()}}_doProcessInitial(){if(this._codec.loadedMetadata){if(!this._codec.hasVideo&&!this._codec.hasAudio)throw new Error("No audio or video found, something is wrong");this._codec.hasAudio&&(this._audioInfo=this._codec.audioFormat),this._codec.hasVideo&&(this._videoInfo=this._codec.videoFormat,this._setupVideo()),isNaN(this._codec.duration)||(this._duration=this._codec.duration),null===this._duration&&this._stream.seekable?(this._state=m.SEEKING_END,this._lastSeenTimestamp=-1,this._codec.flush(()=>{this._seekStream(Math.max(0,this._stream.length-131072))})):(this._state=m.LOADED,this._pingProcessing())}else this._codec.process(e=>{if(e)this._pingProcessing();else{if(this._streamEnded)throw new Error("end of file before headers found");this._log("reading more cause we are out of data"),this._readBytesAndWait()}})}_doProcessSeekingEnd(){this._codec.frameReady?(this._log("saw frame with "+this._codec.frameTimestamp),this._lastSeenTimestamp=Math.max(this._lastSeenTimestamp,this._codec.frameTimestamp),this._codec.discardFrame(()=>{this._pingProcessing()})):this._codec.audioReady?(this._log("saw audio with "+this._codec.audioTimestamp),this._lastSeenTimestamp=Math.max(this._lastSeenTimestamp,this._codec.audioTimestamp),this._codec.discardAudio(()=>{this._pingProcessing()})):this._codec.process(e=>{e?this._pingProcessing():this._stream.eof?(this._log("seek-duration: we are at the end: "+this._lastSeenTimestamp),this._lastSeenTimestamp>0&&(this._duration=this._lastSeenTimestamp),this._state=m.LOADED,this._codec.flush(()=>{this._streamEnded=!1,this._dataEnded=!1,this._seekStream(0)})):this._readBytesAndWait()})}_doProcessLoaded(){this._state=m.PRELOAD,this._fireEventAsync("loadedmetadata"),this._fireEventAsync("durationchange"),this._codec.hasVideo&&this._fireEventAsync("resize"),this._pingProcessing(0)}_doProcessPreload(){!this._codec.frameReady&&this._codec.hasVideo||!this._codec.audioReady&&this._codec.hasAudio?this._codec.process(e=>{e?this._pingProcessing():this._streamEnded?this._ended=!0:this._readBytesAndWait()}):(this._state=m.READY,this._fireEventAsync("loadeddata"),this._pingProcessing())}_doProcessReady(){if(this._log("initial seek to "+this._initialSeekTime),this._initialSeekTime>0){let e=this._initialSeekTime;this._initialSeekTime=0,this._log("initial seek to "+e),this._doSeek(e)}else if(this._paused)this._log("paused while in ready");else{let e=()=>{this._log("finishStartPlaying"),this._state=m.PLAYING,this._lastFrameTimestamp=b(),this._codec.hasAudio&&this._audioFeeder?this._prebufferingAudio=!0:this._startPlayback(),this._pingProcessing(0),this._fireEventAsync("play"),this._fireEventAsync("playing")};!this._codec.hasAudio||this._audioFeeder||this._muted?e():(this._initAudioFeeder(),this._audioFeeder.waitUntilReady(e))}}_doProcessSeeking(){if(this._seekState==g.NOT_SEEKING)throw new Error("seeking in invalid state (not seeking?)");if(this._seekState==g.BISECT_TO_TARGET)this._doProcessBisectionSeek();else if(this._seekState==g.BISECT_TO_KEYPOINT)this._doProcessBisectionSeek();else{if(this._seekState!=g.LINEAR_TO_TARGET)throw new Error("Invalid seek state "+this._seekState);this._doProcessLinearSeeking()}}_doProcessPlay(){let e=this._codec;if(this._paused)this._log("paused during playback; stopping loop");else if((!e.hasAudio||e.audioReady||this._pendingAudio||this._dataEnded)&&(!e.hasVideo||e.frameReady||this._pendingFrame||this._decodedFrames.length||this._dataEnded)){var t,i,r,n=null,s=0,o=!1,a=0;if(e.hasAudio&&this._audioFeeder?(n=this._audioFeeder.getPlaybackState(),s=this._getPlaybackTime(n),o=this._dataEnded&&0==this._audioFeeder.durationBuffered,this._prebufferingAudio&&(this._audioFeeder.durationBuffered>=2*this._audioFeeder.bufferThreshold&&(!e.hasVideo||this._decodedFrames.length>=this._framePipelineDepth)||this._dataEnded)&&(this._log("prebuffering audio done; buffered to "+this._audioFeeder.durationBuffered),this._startPlayback(s),this._prebufferingAudio=!1),n.dropped!=this._droppedAudio&&this._log("dropped "+(n.dropped-this._droppedAudio)),n.delayed!=this._delayedAudio&&this._log("delayed "+(n.delayed-this._delayedAudio)),this._droppedAudio=n.dropped,this._delayedAudio=n.delayed,(t=this._audioFeeder.durationBuffered<=2*this._audioFeeder.bufferThreshold)&&(this._codec.audioReady?this._pendingAudio>=this._audioPipelineDepth&&(this._log("audio decode disabled: "+this._pendingAudio+" packets in flight"),t=!1):t=!1)):(s=this._getPlaybackTime(),t=this._codec.audioReady&&this._audioEndTimestamp<s),this._codec.hasVideo){i=this._decodedFrames.length>0,r=this._pendingFrame+this._decodedFrames.length<this._framePipelineDepth+this._frameParallelism&&this._codec.frameReady,i&&(a=1e3*(this._decodedFrames[0].frameEndTimestamp-s),this._actualPerFrameTime=this._targetPerFrameTime-a);let e=this._targetPerFrameTime;if(this._prebufferingAudio)r&&this._log("decoding a frame during prebuffering"),i=!1;else if(i&&this._dataEnded&&o)this._log("audio timeline ended? ready to draw frame");else if(i&&-a>=e){let e=-1;for(let t=0;t<this._decodedFrames.length-1;t++)this._decodedFrames[t].frameEndTimestamp<s&&(e=t-1);if(e>=0)for(;e-- >=0;){this._lateFrames++;let e=this._decodedFrames.shift();this._log("skipping already-decoded late frame at "+e.frameEndTimestamp),a=1e3*(e.frameEndTimestamp-s),this._frameEndTimestamp=e.frameEndTimestamp,this._actualPerFrameTime=this._targetPerFrameTime-a,this._framesProcessed++,e.dropped=!0,this._doFrameComplete(e)}let t=this._codec.nextKeyframeTimestamp,i=t-this._targetPerFrameTime/1e3*(this._framePipelineDepth+this._pendingFrame);if(t>=0&&t!=this._codec.frameTimestamp&&s>=i){this._log("skipping late frame at "+this._decodedFrames[0].frameEndTimestamp+" vs "+s+", expect to see keyframe at "+t);for(let e=0;e<this._decodedFrames.length;e++){let t=this._decodedFrames[e];this._lateFrames++,this._framesProcessed++,this._frameEndTimestamp=t.frameEndTimestamp,a=1e3*(t.frameEndTimestamp-s),this._actualPerFrameTime=this._targetPerFrameTime-a,t.dropped=!0,this._doFrameComplete(t)}this._decodedFrames=[];for(let e=0;e<this._pendingFrames.length;e++){let t=this._pendingFrames[e];this._lateFrames++,this._framesProcessed++,this._frameEndTimestamp=t.frameEndTimestamp,a=1e3*(t.frameEndTimestamp-s),this._actualPerFrameTime=this._targetPerFrameTime-a,t.dropped=!0,this._doFrameComplete(t)}for(this._pendingFrames=[],this._pendingFrame=0;this._codec.frameReady&&this._codec.frameTimestamp<t;){let e={frameEndTimestamp:this._codec.frameTimestamp,dropped:!0};a=1e3*(e.frameEndTimestamp-s),this._actualPerFrameTime=this._targetPerFrameTime-a,this._lateFrames++,this._codec.discardFrame(()=>{}),this._framesProcessed++,this._doFrameComplete(e)}return void(this._isProcessing()||this._pingProcessing())}}else i&&a<=4||(i=!1)}if(r){this._log("play loop: ready to decode frame; thread depth: "+this._pendingFrame+", have buffered: "+this._decodedFrames.length),0==this._videoInfo.fps&&this._codec.frameTimestamp-this._frameEndTimestamp>0&&(this._targetPerFrameTime=1e3*(this._codec.frameTimestamp-this._frameEndTimestamp)),this._totalFrameTime+=this._targetPerFrameTime,this._totalFrameCount++;let e=this._frameEndTimestamp=this._codec.frameTimestamp;this._pendingFrame++,this._pendingFrames.push({frameEndTimestamp:e});let t=this._pendingFrames,i=!1,r=this._time(()=>{this._codec.decodeFrame(r=>{t===this._pendingFrames?(this._log("play loop callback: decoded frame"),this._pendingFrame--,this._pendingFrames.shift(),r?this._decodedFrames.push({yCbCrBuffer:this._codec.frameBuffer,videoCpuTime:this._codec.videoCpuTime,frameEndTimestamp:e}):this._log("Bad video packet or something"),this._codec.process(()=>{this._isProcessing()||this._pingProcessing(i?void 0:0)})):this._log("play loop callback after flush, discarding")})});this._pendingFrame&&(i=!0,this._proxyTime+=r,this._pingProcessing(),this._dataEnded&&this._codec.sync())}else if(t){this._log("play loop: ready for audio; depth: "+this._pendingAudio),this._pendingAudio++;let e=this._codec.audioTimestamp,t=this._time(()=>{this._codec.decodeAudio(t=>{if(this._pendingAudio--,this._log("play loop callback: decoded audio"),this._audioEndTimestamp=e,t){let e=this._codec.audioBuffer;if(e&&(this._bufferTime+=this._time(()=>{this._audioFeeder&&this._audioFeeder.bufferData(e)}),!this._codec.hasVideo)){this._framesProcessed++;let e={frameEndTimestamp:this._audioEndTimestamp};this._doFrameComplete(e)}}this._isProcessing()||this._pingProcessing()})});this._pendingAudio&&(this._proxyTime+=t,this._codec.audioReady?this._pingProcessing():this._doProcessPlayDemux())}else if(i){this._log("play loop: ready to draw frame"),this._nextFrameTimer&&(clearTimeout(this._nextFrameTimer),this._nextFrameTimer=null),this._thumbnail&&(this.removeChild(this._thumbnail),this._thumbnail=null);let e=this._decodedFrames.shift();this._currentVideoCpuTime=e.videoCpuTime,this._drawingTime+=this._time(()=>{this._frameSink.drawFrame(e.yCbCrBuffer)}),this._framesProcessed++,this._doFrameComplete(e),this._pingProcessing()}else if(!this._decodedFrames.length||this._nextFrameTimer||this._prebufferingAudio)if(this._dataEnded&&!(this._pendingAudio||this._pendingFrame||this._decodedFrames.length)){this._log("play loop: playback reached end of data "+[this._pendingAudio,this._pendingFrame,this._decodedFrames.length]);let e=0;this._codec.hasAudio&&this._audioFeeder&&(e=1e3*this._audioFeeder.durationBuffered),e>0?(this._log("play loop: ending pending "+e+" ms"),this._pingProcessing(Math.max(0,e))):(this._log("play loop: ENDING NOW: playback time "+this._getPlaybackTime()+"; frameEndTimestamp: "+this._frameEndTimestamp),this._stopPlayback(),this._prebufferingAudio=!1,this._initialPlaybackOffset=Math.max(this._audioEndTimestamp,this._frameEndTimestamp),this._ended=!0,this._paused=!0,this._fireEventAsync("pause"),this._fireEventAsync("ended"))}else this._prebufferingAudio&&(e.hasVideo&&!e.frameReady||e.hasAudio&&!e.audioReady)?(this._log("play loop: prebuffering demuxing"),this._doProcessPlayDemux()):this._log("play loop: waiting on async/timers");else{let e=a;this._log("play loop: setting a timer for drawing "+e),this._nextFrameTimer=setTimeout(()=>{this._nextFrameTimer=null,this._pingProcessing()},e)}}else this._log("play loop: demuxing"),this._doProcessPlayDemux()}_doProcessPlayDemux(){let e=this._codec.frameReady,t=this._codec.audioReady;this._codec.process(i=>{this._codec.frameReady&&!e||this._codec.audioReady&&!t?(this._log("demuxer has packets"),this._pingProcessing()):i?(this._log("demuxer processing to find more packets"),this._pingProcessing()):(this._log("demuxer ran out of data"),this._streamEnded?(this._log("demuxer reached end of data stream"),this._dataEnded=!0,this._pingProcessing()):(this._log("demuxer loading more data"),this._readBytesAndWait()))})}_doProcessError(){}_isProcessing(){return this._stream&&(this._stream.buffering||this._stream.seeking)||this._codec&&this._codec.processing}_readBytesAndWait(){if(this._stream.buffering||this._stream.seeking)return void this._log("readBytesAndWait during i/o");this._stream.read(32768).then(e=>{this._log("got input "+[e.byteLength]),e.byteLength&&this._actionQueue.push(()=>{this._codec.receiveInput(e,()=>{this._pingProcessing()})}),this._stream.eof&&(this._log("stream is at end!"),this._streamEnded=!0),this._isProcessing()||this._pingProcessing()}).catch(e=>{this._onStreamError(e)})}_pingProcessing(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;if(this._stream&&this._stream.waiting)return void this._log("waiting on input");this._nextProcessingTimer&&(this._log("canceling old processing timer"),clearTimeout(this._nextProcessingTimer),this._nextProcessingTimer=null);e>-1/256?this._nextProcessingTimer=setTimeout(()=>{this._pingProcessing()},e):this._depth?this._needProcessing=!0:this._doProcessing()}_startProcessingVideo(e){if(this._started||this._codec)return;this._framesProcessed=0,this._bufferTime=0,this._drawingTime=0,this._proxyTime=0,this._started=!0,this._ended=!1;let t={base:this._options.base,worker:this._enableWorker,threading:this._enableThreading,wasm:this._enableWASM};this._options.memoryLimit&&!this._enableWASM&&(t.memoryLimit=this._options.memoryLimit),this._detectedType&&(t.type=this._detectedType),this._codec=new p.default(t),this._lastVideoCpuTime=0,this._lastAudioCpuTime=0,this._lastDemuxerCpuTime=0,this._lastBufferTime=0,this._lastDrawingTime=0,this._lastProxyTime=0,this._lastFrameVideoCpuTime=0,this._lastFrameAudioCpuTime=0,this._lastFrameDemuxerCpuTime=0,this._lastFrameBufferTime=0,this._lastFrameProxyTime=0,this._lastFrameDrawingTime=0,this._currentVideoCpuTime=0,this._codec.onseek=(e=>{this._didSeek=!0,this._stream&&this._seekStream(e)}),this._codec.init(()=>{this._codec.receiveInput(e,()=>{this._readBytesAndWait()})})}_loadCodec(e){this._stream.read(1024).then(t=>{let i=new Uint8Array(t);i.length>4&&i[0]=="O".charCodeAt(0)&&i[1]=="g".charCodeAt(0)&&i[2]=="g".charCodeAt(0)&&i[3]=="S".charCodeAt(0)?this._detectedType="video/ogg":i.length>4&&26==i[0]&&69==i[1]&&223==i[2]&&163==i[3]?this._detectedType="video/webm":this._detectedType="video/ogg",e(t)})}_prepForLoad(e){this._stopVideo();let t=()=>{this._options.stream?this._stream=this._options.stream:this._stream=new o.default({url:this.src,cacheSize:16777216,progressive:!1}),this._stream.load().then(()=>{this._loading=!1,this._currentSrc=this.src,this._byteLength=this._stream.seekable?this._stream.length:0;let e=this._stream.headers["x-content-duration"];"string"==typeof e&&(this._duration=parseFloat(e)),this._loadCodec(e=>{this._startProcessingVideo(e)})}).catch(e=>{this._onStreamError(e)})};this._currentSrc="",this._loading=!0,this._actionQueue.push(()=>{e&&"none"===this.preload?this._loading=!1:t()}),this._pingProcessing(0)}load(){this._prepForLoad()}canPlayType(e){let t=new l.default(e);function checkTypes(e){if(t.codecs){let i=0,r=0;return t.codecs.forEach(t=>{e.indexOf(t)>=0?i++:r++}),0===i?"":r>0?"":"probably"}return"maybe"}return"ogg"!==t.minor||"audio"!==t.major&&"video"!==t.major&&"application"!==t.major?"webm"!==t.minor||"audio"!==t.major&&"video"!==t.major?"":checkTypes(["vorbis","opus","vp8","vp9"]):checkTypes(["vorbis","opus","theora"])}play(){this._muted||this._options.audioContext||OGVPlayer.initSharedAudioContext(),this._paused&&(this._startedPlaybackInDocument=document.body.contains(this),this._paused=!1,this._state==m.SEEKING||(this._started&&this._codec&&this._codec.loadedMetadata?(this._ended&&this._stream&&this._byteLength?(this._log(".play() starting over after end"),this._seek(0)):this._log(".play() while already started"),this._state=m.READY,this._isProcessing()||this._pingProcessing()):this._loading?this._log(".play() while loading"):(this._log(".play() before started"),this._stream||this.load())))}getPlaybackStats(){return{targetPerFrameTime:this._targetPerFrameTime,framesProcessed:this._framesProcessed,videoBytes:this._codec?this._codec.videoBytes:0,audioBytes:this._codec?this._codec.audioBytes:0,playTime:this._playTime,demuxingTime:this._codec?this._codec.demuxerCpuTime-this._lastDemuxerCpuTime:0,videoDecodingTime:this._codec?this._codec.videoCpuTime-this._lastVideoCpuTime:0,audioDecodingTime:this._codec?this._codec.audioCpuTime-this._lastAudioCpuTime:0,bufferTime:this._bufferTime-this._lastBufferTime,drawingTime:this._drawingTime-this._lastDrawingTime,proxyTime:this._proxyTime-this._lastProxyTime,droppedAudio:this._droppedAudio,delayedAudio:this._delayedAudio,jitter:this._totalJitter/this._framesProcessed,lateFrames:this._lateFrames}}resetPlaybackStats(){this._framesProcessed=0,this._playTime=0,this._codec&&(this._lastDemuxerCpuTime=this._codec.demuxerCpuTime,this._lastVideoCpuTime=this._codec.videoCpuTime,this._lastAudioCpuTime=this._codec.audioCpuTime,this._codec.videoBytes=0,this._codec.audioBytes=0),this._lastBufferTime=this._bufferTime,this._lastDrawingTime=this._drawingTime,this._lastProxyTime=this._proxyTime,this._totalJitter=0,this._totalFrameTime=0,this._totalFrameCount=0}getVideoFrameSink(){return this._frameSink}getCanvas(){return this._canvas}pause(){this._paused||(this._nextProcessingTimer&&(clearTimeout(this._nextProcessingTimer),this._nextProcessingTimer=null),this._stopPlayback(),this._prebufferingAudio=!1,this._paused=!0,this._fireEvent("pause"))}stop(){this._stopVideo(),this._paused=!0}fastSeek(e){this._seek(+e,y.FAST)}}if((0,h.default)(OGVPlayer,_),OGVPlayer.instanceCount=0,OGVPlayer.styleManager=new function StyleManager(){var e=document.createElement("style");e.type="text/css",e.textContent="ogvjs { display: inline-block; position: relative; -webkit-user-select: none; -webkit-tap-highlight-color: rgba(0,0,0,0); ",document.head.appendChild(e);var t=e.sheet;this.appendRule=function(e,i){var r=[];for(var n in i)i.hasOwnProperty(n)&&r.push(n+":"+i[n]);var s=e+"{"+r.join(";")+"}";t.insertRule(s,t.cssRules.length-1)}},OGVPlayer.supportsObjectFit="string"==typeof document.createElement("canvas").style.objectFit,OGVPlayer.supportsObjectFit&&navigator.userAgent.match(/iPhone|iPad|iPod Touch/)&&(OGVPlayer.supportsObjectFit=!1),OGVPlayer.supportsObjectFit&&navigator.userAgent.match(/Edge/)&&(OGVPlayer.supportsObjectFit=!1),OGVPlayer.supportsObjectFit)OGVPlayer.updatePositionOnResize=function(){};else{OGVPlayer.updatePositionOnResize=function(){function fixup(e,t,i){var r=e.offsetParent||e.parentNode,n=t/i;if(n>r.offsetWidth/r.offsetHeight){var s=r.offsetWidth/n,o=(r.offsetHeight-s)/2;e.style.width="100%",e.style.height=s+"px",e.style.marginLeft=0,e.style.marginRight=0,e.style.marginTop=o+"px",e.style.marginBottom=o+"px"}else{var a=r.offsetHeight*n,u=(r.offsetWidth-a)/2;e.style.width=a+"px",e.style.height="100%",e.style.marginLeft=u+"px",e.style.marginRight=u+"px",e.style.marginTop=0,e.style.marginBottom=0}}function queryOver(e,t){var i=document.querySelectorAll(e);Array.prototype.slice.call(i).forEach(t)}queryOver("ogvjs > canvas",function(e){fixup(e,e.width,e.height)}),queryOver("ogvjs > img",function(e){fixup(e,e.naturalWidth,e.naturalHeight)})};var v=function fullResizeVideo(){e(OGVPlayer.updatePositionOnResize)};window.addEventListener("resize",OGVPlayer.updatePositionOnResize),window.addEventListener("orientationchange",OGVPlayer.updatePositionOnResize),document.addEventListener("fullscreenchange",v),document.addEventListener("mozfullscreenchange",v),document.addEventListener("webkitfullscreenchange",v),document.addEventListener("MSFullscreenChange",v)}var T=OGVPlayer;t.default=T}).call(this,i(22).setImmediate)},function(e,t,i){(function(e){var r=void 0!==e&&e||"undefined"!=typeof self&&self||window,n=Function.prototype.apply;function Timeout(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new Timeout(n.call(setTimeout,r,arguments),clearTimeout)},t.setInterval=function(){return new Timeout(n.call(setInterval,r,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},Timeout.prototype.unref=Timeout.prototype.ref=function(){},Timeout.prototype.close=function(){this._clearFn.call(r,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function onTimeout(){e._onTimeout&&e._onTimeout()},t))},i(23),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,i(1))},function(e,t,i){(function(e,t){!function(e,i){"use strict";if(!e.setImmediate){var r,n=1,s={},o=!1,a=e.document,u=Object.getPrototypeOf&&Object.getPrototypeOf(e);u=u&&u.setTimeout?u:e,"[object process]"==={}.toString.call(e.process)?function installNextTickImplementation(){r=function(e){t.nextTick(function(){runIfPresent(e)})}}():!function canUsePostMessage(){if(e.postMessage&&!e.importScripts){var t=!0,i=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=i,t}}()?e.MessageChannel?function installMessageChannelImplementation(){var e=new MessageChannel;e.port1.onmessage=function(e){runIfPresent(e.data)},r=function(t){e.port2.postMessage(t)}}():a&&"onreadystatechange"in a.createElement("script")?function installReadyStateChangeImplementation(){var e=a.documentElement;r=function(t){var i=a.createElement("script");i.onreadystatechange=function(){runIfPresent(t),i.onreadystatechange=null,e.removeChild(i),i=null},e.appendChild(i)}}():function installSetTimeoutImplementation(){r=function(e){setTimeout(runIfPresent,0,e)}}():function installPostMessageImplementation(){var t="setImmediate$"+Math.random()+"$",i=function(i){i.source===e&&"string"==typeof i.data&&0===i.data.indexOf(t)&&runIfPresent(+i.data.slice(t.length))};e.addEventListener?e.addEventListener("message",i,!1):e.attachEvent("onmessage",i),r=function(i){e.postMessage(t+i,"*")}}(),u.setImmediate=function setImmediate(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),i=0;i<t.length;i++)t[i]=arguments[i+1];var o={callback:e,args:t};return s[n]=o,r(n),n++},u.clearImmediate=clearImmediate}function clearImmediate(e){delete s[e]}function runIfPresent(e){if(o)setTimeout(runIfPresent,0,e);else{var t=s[e];if(t){o=!0;try{!function run(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(i,r)}}(t)}finally{clearImmediate(e),o=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,i(1),i(5))},function(e,t,i){!function(){"use strict";var t=i(3),r=i(25),n=i(28),s={FrameSink:t,SoftwareFrameSink:r,WebGLFrameSink:n,attach:function(e,t){return("webGL"in(t=t||{})?t.webGL:n.isAvailable())?new n(e,t):new r(e,t)}};e.exports=s}()},function(e,t,i){!function(){"use strict";var t=i(3),r=i(26);function SoftwareFrameSink(e){var t=e.getContext("2d"),i=null,n=null,s=null;return this.drawFrame=function drawFrame(o){var a=o.format;e.width===a.displayWidth&&e.height===a.displayHeight||(e.width=a.displayWidth,e.height=a.displayHeight),null!==i&&i.width==a.width&&i.height==a.height||function initImageData(e,r){for(var n=(i=t.createImageData(e,r)).data,s=e*r*4,o=0;o<s;o+=4)n[o+3]=255}(a.width,a.height),r.convertYCbCr(o,i.data);var u,d=a.cropWidth!=a.displayWidth||a.cropHeight!=a.displayHeight;d?(n||function initResampleCanvas(e,t){(n=document.createElement("canvas")).width=e,n.height=t,s=n.getContext("2d")}(a.cropWidth,a.cropHeight),u=s):u=t,u.putImageData(i,-a.cropLeft,-a.cropTop,a.cropLeft,a.cropTop,a.cropWidth,a.cropHeight),d&&t.drawImage(n,0,0,a.displayWidth,a.displayHeight)},this.clear=function(){t.clearRect(0,0,e.width,e.height)},this}SoftwareFrameSink.prototype=Object.create(t.prototype),e.exports=SoftwareFrameSink}()},function(e,t,i){!function(){"use strict";var t=i(27);
/**
	 * Basic YCbCr->RGB conversion
	 *
	 * <AUTHOR> Vibber <<EMAIL>>
	 * @copyright 2014-2019
	 * @license MIT-style
	 *
	 * @param {YUVFrame} buffer - input frame buffer
	 * @param {Uint8ClampedArray} output - array to draw RGBA into
	 * Assumes that the output array already has alpha channel set to opaque.
	 */e.exports={convertYCbCr:function convertYCbCr(e,i){var r=0|e.format.width,n=0|e.format.height,s=0|t(e.format.width/e.format.chromaWidth),o=0|t(e.format.height/e.format.chromaHeight),a=e.y.bytes,u=e.u.bytes,d=e.v.bytes,h=0|e.y.stride,c=0|e.u.stride,l=0|e.v.stride,f=r<<2,p=0,_=0,m=0,g=0,y=0,b=0,v=0,T=0,w=0,k=0,E=0,P=0,A=0,x=0,O=0,R=0,S=0,F=0;if(1==s&&1==o)for(v=0,T=f,F=0,R=0;R<n;R+=2){for(m=(_=R*h|0)+h|0,g=F*c|0,y=F*l|0,O=0;O<r;O+=2)w=0|u[g++],P=(409*(k=0|d[y++])|0)-57088|0,A=(100*w|0)+(208*k|0)-34816|0,x=(516*w|0)-70912|0,E=298*a[_++]|0,i[v]=E+P>>8,i[v+1]=E-A>>8,i[v+2]=E+x>>8,v+=4,E=298*a[_++]|0,i[v]=E+P>>8,i[v+1]=E-A>>8,i[v+2]=E+x>>8,v+=4,E=298*a[m++]|0,i[T]=E+P>>8,i[T+1]=E-A>>8,i[T+2]=E+x>>8,T+=4,E=298*a[m++]|0,i[T]=E+P>>8,i[T+1]=E-A>>8,i[T+2]=E+x>>8,T+=4;v+=f,T+=f,F++}else for(b=0,R=0;R<n;R++)for(S=0,p=R*h|0,g=(F=R>>o)*c|0,y=F*l|0,O=0;O<r;O++)w=0|u[g+(S=O>>s)],P=(409*(k=0|d[y+S])|0)-57088|0,A=(100*w|0)+(208*k|0)-34816|0,x=(516*w|0)-70912|0,E=298*a[p++]|0,i[b]=E+P>>8,i[b+1]=E-A>>8,i[b+2]=E+x>>8,b+=4}}}()},function(e,t){!function(){"use strict";
/**
   * Convert a ratio into a bit-shift count; for instance a ratio of 2
   * becomes a bit-shift of 1, while a ratio of 1 is a bit-shift of 0.
   *
   * <AUTHOR> Vibber <<EMAIL>>
   * @copyright 2016
   * @license MIT-style
   *
   * @param {number} ratio - the integer ratio to convert.
   * @returns {number} - number of bits to shift to multiply/divide by the ratio.
   * @throws exception if given a non-power-of-two
   */e.exports=function depower(e){for(var t=0,i=e>>1;0!=i;)i>>=1,t++;if(e!==1<<t)throw"chroma plane dimensions must be power of 2 ratio to luma plane dimensions; got "+e;return t}}()},function(e,t,i){!function(){"use strict";var t=i(3),r=i(29);function WebGLFrameSink(e){var t,i,n=this,s=WebGLFrameSink.contextForCanvas(e);if(null===s)throw new Error("WebGL unavailable");function compileShader(e,t){var i=s.createShader(e);if(s.shaderSource(i,t),s.compileShader(i),!s.getShaderParameter(i,s.COMPILE_STATUS)){var r=s.getShaderInfoLog(i);throw s.deleteShader(i),new Error("GL shader compilation for "+e+" failed: "+r)}return i}var o,a,u,d,h,c,l,f,p,_,m=new Float32Array([-1,-1,1,-1,-1,1,-1,1,1,-1,1,1]),g={},y={},b={};function createOrReuseTexture(e){return g[e]||(g[e]=s.createTexture()),g[e]}function uploadTexture(e,t,i,r){var n=createOrReuseTexture(e);if(s.activeTexture(s.TEXTURE0),WebGLFrameSink.stripe){var o=!g[e+"_temp"],a=createOrReuseTexture(e+"_temp");s.bindTexture(s.TEXTURE_2D,a),o?(s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.NEAREST),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.NEAREST),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,t/4,i,0,s.RGBA,s.UNSIGNED_BYTE,r)):s.texSubImage2D(s.TEXTURE_2D,0,0,0,t/4,i,s.RGBA,s.UNSIGNED_BYTE,r);var u=g[e+"_stripe"],d=!u;d&&(u=createOrReuseTexture(e+"_stripe")),s.bindTexture(s.TEXTURE_2D,u),d&&(s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.NEAREST),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.NEAREST),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,t,1,0,s.RGBA,s.UNSIGNED_BYTE,function buildStripe(e){if(b[e])return b[e];for(var t=e,i=new Uint32Array(t),r=0;r<t;r+=4)i[r]=255,i[r+1]=65280,i[r+2]=16711680,i[r+3]=4278190080;return b[e]=new Uint8Array(i.buffer)}(t)))}else s.bindTexture(s.TEXTURE_2D,n),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.texImage2D(s.TEXTURE_2D,0,s.LUMINANCE,t,i,0,s.LUMINANCE,s.UNSIGNED_BYTE,r)}function unpackTexture(e,t,r){var n=g[e];s.useProgram(i);var l=y[e];l||(s.activeTexture(s.TEXTURE0),s.bindTexture(s.TEXTURE_2D,n),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,t,r,0,s.RGBA,s.UNSIGNED_BYTE,null),l=y[e]=s.createFramebuffer()),s.bindFramebuffer(s.FRAMEBUFFER,l),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,n,0);var f=g[e+"_temp"];s.activeTexture(s.TEXTURE1),s.bindTexture(s.TEXTURE_2D,f),s.uniform1i(c,1);var p=g[e+"_stripe"];s.activeTexture(s.TEXTURE2),s.bindTexture(s.TEXTURE_2D,p),s.uniform1i(h,2),s.bindBuffer(s.ARRAY_BUFFER,o),s.enableVertexAttribArray(a),s.vertexAttribPointer(a,2,s.FLOAT,!1,0,0),s.bindBuffer(s.ARRAY_BUFFER,u),s.enableVertexAttribArray(d),s.vertexAttribPointer(d,2,s.FLOAT,!1,0,0),s.viewport(0,0,t,r),s.drawArrays(s.TRIANGLES,0,m.length/2),s.bindFramebuffer(s.FRAMEBUFFER,null)}function attachTexture(e,i,r){s.activeTexture(i),s.bindTexture(s.TEXTURE_2D,g[e]),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.uniform1i(s.getUniformLocation(t,e),r)}function initProgram(e,t){var i=compileShader(s.VERTEX_SHADER,e),r=compileShader(s.FRAGMENT_SHADER,t),n=s.createProgram();if(s.attachShader(n,i),s.attachShader(n,r),s.linkProgram(n),!s.getProgramParameter(n,s.LINK_STATUS)){var o=s.getProgramInfoLog(n);throw s.deleteProgram(n),new Error("GL program linking failed: "+o)}return n}return n.drawFrame=function(g){var y=g.format,b=!t||e.width!==y.displayWidth||e.height!==y.displayHeight;if(b&&(e.width=y.displayWidth,e.height=y.displayHeight,n.clear()),t||function init(){if(WebGLFrameSink.stripe){i=initProgram(r.vertexStripe,r.fragmentStripe),s.getAttribLocation(i,"aPosition"),u=s.createBuffer();var e=new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]);s.bindBuffer(s.ARRAY_BUFFER,u),s.bufferData(s.ARRAY_BUFFER,e,s.STATIC_DRAW),d=s.getAttribLocation(i,"aTexturePosition"),h=s.getUniformLocation(i,"uStripe"),c=s.getUniformLocation(i,"uTexture")}t=initProgram(r.vertex,r.fragment),o=s.createBuffer(),s.bindBuffer(s.ARRAY_BUFFER,o),s.bufferData(s.ARRAY_BUFFER,m,s.STATIC_DRAW),a=s.getAttribLocation(t,"aPosition"),l=s.createBuffer(),f=s.getAttribLocation(t,"aLumaPosition"),p=s.createBuffer(),_=s.getAttribLocation(t,"aChromaPosition")}(),b){var v=function(e,t,i){var r=y.cropLeft/i,n=(y.cropLeft+y.cropWidth)/i,o=(y.cropTop+y.cropHeight)/y.height,a=y.cropTop/y.height,u=new Float32Array([r,o,n,o,r,a,r,a,n,o,n,a]);s.bindBuffer(s.ARRAY_BUFFER,e),s.bufferData(s.ARRAY_BUFFER,u,s.STATIC_DRAW)};v(l,0,g.y.stride),v(p,0,g.u.stride*y.width/y.chromaWidth)}uploadTexture("uTextureY",g.y.stride,y.height,g.y.bytes),uploadTexture("uTextureCb",g.u.stride,y.chromaHeight,g.u.bytes),uploadTexture("uTextureCr",g.v.stride,y.chromaHeight,g.v.bytes),WebGLFrameSink.stripe&&(unpackTexture("uTextureY",g.y.stride,y.height),unpackTexture("uTextureCb",g.u.stride,y.chromaHeight),unpackTexture("uTextureCr",g.v.stride,y.chromaHeight)),s.useProgram(t),s.viewport(0,0,e.width,e.height),attachTexture("uTextureY",s.TEXTURE0,0),attachTexture("uTextureCb",s.TEXTURE1,1),attachTexture("uTextureCr",s.TEXTURE2,2),s.bindBuffer(s.ARRAY_BUFFER,o),s.enableVertexAttribArray(a),s.vertexAttribPointer(a,2,s.FLOAT,!1,0,0),s.bindBuffer(s.ARRAY_BUFFER,l),s.enableVertexAttribArray(f),s.vertexAttribPointer(f,2,s.FLOAT,!1,0,0),s.bindBuffer(s.ARRAY_BUFFER,p),s.enableVertexAttribArray(_),s.vertexAttribPointer(_,2,s.FLOAT,!1,0,0),s.drawArrays(s.TRIANGLES,0,m.length/2)},n.clear=function(){s.viewport(0,0,e.width,e.height),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT)},n.clear(),n}WebGLFrameSink.stripe=-1!==navigator.userAgent.indexOf("Windows"),WebGLFrameSink.contextForCanvas=function(e){var t={alpha:!1,depth:!1,stencil:!1,antialias:!1,preferLowPowerToHighPerformance:!0,failIfMajorPerformanceCaveat:!0};return e.getContext("webgl",t)||e.getContext("experimental-webgl",t)},WebGLFrameSink.isAvailable=function(){var e,t=document.createElement("canvas");t.width=1,t.height=1;try{e=WebGLFrameSink.contextForCanvas(t)}catch(e){return!1}if(e){var i=e.TEXTURE0,r=e.createTexture(),n=new Uint8Array(16),s=WebGLFrameSink.stripe?1:4,o=WebGLFrameSink.stripe?e.RGBA:e.LUMINANCE,a=WebGLFrameSink.stripe?e.NEAREST:e.LINEAR;return e.activeTexture(i),e.bindTexture(e.TEXTURE_2D,r),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,a),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,a),e.texImage2D(e.TEXTURE_2D,0,o,s,4,0,o,e.UNSIGNED_BYTE,n),!e.getError()}return!1},WebGLFrameSink.prototype=Object.create(t.prototype),e.exports=WebGLFrameSink}()},function(e,t){e.exports={vertex:"precision lowp float;\n\nattribute vec2 aPosition;\nattribute vec2 aLumaPosition;\nattribute vec2 aChromaPosition;\nvarying vec2 vLumaPosition;\nvarying vec2 vChromaPosition;\nvoid main() {\n    gl_Position = vec4(aPosition, 0, 1);\n    vLumaPosition = aLumaPosition;\n    vChromaPosition = aChromaPosition;\n}\n",fragment:"// inspired by https://github.com/mbebenita/Broadway/blob/master/Player/canvas.js\n\nprecision lowp float;\n\nuniform sampler2D uTextureY;\nuniform sampler2D uTextureCb;\nuniform sampler2D uTextureCr;\nvarying vec2 vLumaPosition;\nvarying vec2 vChromaPosition;\nvoid main() {\n   // Y, Cb, and Cr planes are uploaded as LUMINANCE textures.\n   float fY = texture2D(uTextureY, vLumaPosition).x;\n   float fCb = texture2D(uTextureCb, vChromaPosition).x;\n   float fCr = texture2D(uTextureCr, vChromaPosition).x;\n\n   // Premultipy the Y...\n   float fYmul = fY * 1.1643828125;\n\n   // And convert that to RGB!\n   gl_FragColor = vec4(\n     fYmul + 1.59602734375 * fCr - 0.87078515625,\n     fYmul - 0.39176171875 * fCb - 0.81296875 * fCr + 0.52959375,\n     fYmul + 2.017234375   * fCb - 1.081390625,\n     1\n   );\n}\n",vertexStripe:"precision lowp float;\n\nattribute vec2 aPosition;\nattribute vec2 aTexturePosition;\nvarying vec2 vTexturePosition;\n\nvoid main() {\n    gl_Position = vec4(aPosition, 0, 1);\n    vTexturePosition = aTexturePosition;\n}\n",fragmentStripe:"// extra 'stripe' texture fiddling to work around IE 11's poor performance on gl.LUMINANCE and gl.ALPHA textures\n\nprecision lowp float;\n\nuniform sampler2D uStripe;\nuniform sampler2D uTexture;\nvarying vec2 vTexturePosition;\nvoid main() {\n   // Y, Cb, and Cr planes are mapped into a pseudo-RGBA texture\n   // so we can upload them without expanding the bytes on IE 11\n   // which doesn't allow LUMINANCE or ALPHA textures\n   // The stripe textures mark which channel to keep for each pixel.\n   // Each texture extraction will contain the relevant value in one\n   // channel only.\n\n   float fLuminance = dot(\n      texture2D(uStripe, vTexturePosition),\n      texture2D(uTexture, vTexturePosition)\n   );\n\n   gl_FragColor = vec4(fLuminance, fLuminance, fLuminance, 1);\n}\n"}},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();i(10);var n=i(31),s=i(34),o=function(){function StreamFile(e){var t=e.url,i=void 0===t?"":t,r=e.chunkSize,s=void 0===r?1048576:r,o=e.cacheSize,a=void 0===o?0:o,u=e.progressive,d=void 0===u||u;!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,StreamFile),this.length=-1,this.loaded=!1,this.loading=!1,this.seekable=!1,this.buffering=!1,this.seeking=!1,this.progressive=d,Object.defineProperties(this,{offset:{get:function get(){return this._cache.readOffset}},eof:{get:function get(){return this.length===this._cache.readOffset}}}),this.url=i,this.headers={},this._cache=new n({cacheSize:a}),this._backend=null,this._cachever=0,this._chunkSize=s}return r(StreamFile,[{key:"load",value:function load(){var e=this;return new Promise(function(t,i){if(e.loading)throw new Error("cannot load when loading");if(e.loaded)throw new Error("cannot load when loaded");e.loading=!0,e._openBackend().then(function(i){e.seekable=i.seekable,e.headers=i.headers,e.length=i.length,e.loaded=!0,e.loading=!1,t()}).catch(function(t){"AbortError"!==t.name&&(e.loading=!1),i(t)})})}},{key:"_openBackend",value:function _openBackend(){var e=this;return new Promise(function(t,i){if(e._backend)t(e._backend);else if(e.eof)i(new Error("cannot open at end of file"));else{var r=e._cache,n=e._chunkSize,o=r.bytesReadable(n),a=r.readOffset+o;if(r.seekWrite(a),e.length>=0&&a>=e.length)return void t(null);var u=e._clampToLength(r.writeOffset+r.bytesWritable(n))-r.writeOffset;0===u?t(null):(d=e._backend=new s({url:e.url,offset:e._cache.writeOffset,length:u,cachever:e._cachever,progressive:e.progressive}),h=null,c=function checkOpen(){d!==e._backend?(h(),i(new Error("invalid state"))):(d.on("buffer",function(t){d===e._backend&&e._cache.write(t)}),d.on("done",function(){d===e._backend&&(-1===e.length&&(e.length=e._backend.offset+e._backend.bytesRead),e._backend=null)}),t(d))},l=function checkError(t){d!==e._backend?i(new Error("invalid state")):(e._backend=null,i(t))},h=function oncomplete(){d.off("open",c),d.off("error",l)},d.on("open",c),d.on("error",l),d.on("cachever",function(){e._cachever++}),d.load())}var d,h,c,l})}},{key:"_readAhead",value:function _readAhead(){var e=this;return new Promise(function(t,i){e._backend||e.eof?t():e._openBackend().then(function(){t()}).catch(function(e){i(e)})})}},{key:"seek",value:function seek(e){var t=this;return new Promise(function(i,r){if(!t.loaded||t.buffering||t.seeking)throw new Error("invalid state");if(e!==(0|e)||e<0)throw new Error("invalid input");if(t.length>=0&&e>t.length)throw new Error("seek past end of file");if(!t.seekable)throw new Error("seek on non-seekable stream");t._backend&&t.abort(),t._cache.seekRead(e),t._cache.seekWrite(e),t._readAhead().then(i).catch(r)})}},{key:"read",value:function read(e){var t=this;return this.buffer(e).then(function(e){return t.readSync(e)})}},{key:"readSync",value:function readSync(e){var t=this.bytesAvailable(e),i=new Uint8Array(t);if(this.readBytes(i)!==t)throw new Error("failed to read expected data");return i.buffer}},{key:"readBytes",value:function readBytes(e){if(!this.loaded||this.buffering||this.seeking)throw new Error("invalid state");if(!(e instanceof Uint8Array))throw new Error("invalid input");var t=this._cache.readBytes(e);return this._readAhead(),t}},{key:"buffer",value:function buffer(e){var t=this;return new Promise(function(i,r){if(!t.loaded||t.buffering||t.seeking)throw new Error("invalid state");if(e!==(0|e)||e<0)throw new Error("invalid input");var n=t._clampToLength(t.offset+e),s=n-t.offset,o=t.bytesAvailable(s);o>=s?i(o):(t.buffering=!0,t._openBackend().then(function(i){return i?i.bufferToOffset(n).then(function(){return t.buffering=!1,t.buffer(e)}):Promise.resolve(o)}).then(function(e){t.buffering=!1,i(e)}).catch(function(e){"AbortError"!==e.name&&(t.buffering=!1),r(e)}))})}},{key:"bytesAvailable",value:function bytesAvailable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0;return this._cache.bytesReadable(e)}},{key:"abort",value:function abort(){this.loading&&(this.loading=!1),this.buffering&&(this.buffering=!1),this.seeking&&(this.seeking=!1),this._backend&&(this._backend.abort(),this._backend=null)}},{key:"getBufferedRanges",value:function getBufferedRanges(){return this._cache.ranges()}},{key:"_clampToLength",value:function _clampToLength(e){return this.length<0?e:Math.min(this.length,e)}}]),StreamFile}();e.exports=o},function(e,t,i){"use strict";e.exports=i(32)},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var n=i(33),s=function(){function CachePool(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).cacheSize,t=void 0===e?0:e;!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,CachePool);var i=new n({eof:!0});this.head=i,this.tail=i,this.readOffset=0,this.readCursor=i,this.writeOffset=0,this.writeCursor=i,this.cacheSize=t}return r(CachePool,[{key:"bytesReadable",value:function bytesReadable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0,t=this.readOffset,i=this.readCursor.last(function(i){return!i.empty&&i.start<=t+e});return i?Math.min(e,i.end-t):0}},{key:"bytesWritable",value:function bytesWritable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0,t=this.writeOffset,i=this.writeCursor;if(i.eof)return e;var r=i.last(function(i){return i.empty&&i.start<=t+e});return r?Math.min(e,r.end-t):0}},{key:"seekRead",value:function seekRead(e){var t=this.head.first(function(t){return t.contains(e)});if(!t)throw new Error("read seek out of range");this.readOffset=e,this.readCursor=t}},{key:"seekWrite",value:function seekWrite(e){var t=this.head.first(function(t){return t.contains(e)});if(!t)throw new Error("write seek out of range");this.writeOffset=e,this.writeCursor=t}},{key:"readBytes",value:function readBytes(e){for(var t=e.byteLength,i=this.bytesReadable(t),r=this.readOffset,n=r+i,s=r,o=this.readCursor;o&&!o.empty&&!(o.start>=n);o=o.next){var a=Math.min(n,o.end),u=e.subarray(s-r,a-r);o.readBytes(u,s,a),s=a}return this.readOffset=s,this.readCursor=this.readCursor.first(function(e){return e.contains(s)}),i}},{key:"write",value:function write(e){var t=this.bufferItem(e),i=this.writeCursor;if(!i.empty)throw new Error("write cursor not empty");if(!i.contains(t.end)&&i.end!==t.end)throw new Error("write cursor too small");i.start<t.start&&(this.split(i,t.start),i=this.writeCursor),(t.end<i.end||i.eof)&&(this.split(i,t.end),i=this.writeCursor),this.splice(i,i,t,t),this.writeOffset=t.end,this.writeCursor=t.next,this.gc()}},{key:"bufferItem",value:function bufferItem(e){if(e instanceof ArrayBuffer)return new n({start:this.writeOffset,end:this.writeOffset+e.byteLength,buffer:e});if("string"==typeof e)return new n({start:this.writeOffset,end:this.writeOffset+e.length,string:e});throw new Error("invalid input to write")}},{key:"split",value:function split(e,t){var i=e.split(t);this.splice(e,e,i[0],i[1])}},{key:"ranges",value:function ranges(){for(var ranges=[],e=this.head;e;e=e.next)if(!e.empty){var t=e;e=e.last(function(e){return!e.empty}),ranges.push([t.start,e.end])}return ranges}},{key:"gc",value:function gc(){for(var e=0,t=[],i=this.head;i;i=i.next)i.empty||(e+=i.length,(i.end<this.readOffset||i.start>this.readOffset+this.chunkSize)&&t.push(i));if(e>this.cacheSize){t.sort(function(e,t){return e.timestamp-t.timestamp});for(var r=0;r<t.length;r++){var n=t[r];if(e<=this.cacheSize)break;this.remove(n),e-=n.length}}}},{key:"remove",value:function remove(e){var t=new n({start:e.start,end:e.end});this.splice(e,e,t,t),(e=t).prev&&e.prev.empty&&(e=this.consolidate(e.prev)),e.next&&e.next.empty&&!e.next.eof&&(e=this.consolidate(e)),0===e.start&&(this.head=e)}},{key:"consolidate",value:function consolidate(e){var t=e.last(function(e){return e.empty&&!e.eof}),i=new n({start:e.start,end:t.end});return this.splice(e,t,i,i),i}},{key:"splice",value:function splice(e,t,i,r){var n=this;if(e.start!==i.start)throw new Error("invalid splice head");if(!(t.end===r.end||t.eof&&r.eof))throw new Error("invalid splice tail");var s=e.prev,o=t.next;e.prev=null,t.next=null,s&&(s.next=i,i.prev=s),o&&(o.prev=r,r.next=o),e===this.head&&(this.head=i),t===this.tail&&(this.tail=r),this.readCursor=this.head.first(function(e){return e.contains(n.readOffset)}),this.writeCursor=this.head.first(function(e){return e.contains(n.writeOffset)})}},{key:"eof",get:function get(){return this.readCursor.eof}}]),CachePool}();e.exports=s},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var n=function(){function CacheItem(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.buffer,i=void 0===t?void 0:t,r=e.string,n=void 0===r?void 0:r,s=e.start,o=void 0===s?0:s,a=e.end,u=void 0===a?o+(i?i.byteLength:n?n.length:0):a,d=e.prev,h=void 0===d?null:d,c=e.next,l=void 0===c?null:c,f=e.eof,p=void 0!==f&&f,_=e.empty,m=void 0===_?!(i||n):_,g=e.timestamp,y=void 0===g?Date.now():g;!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,CacheItem),this.start=o,this.end=u,this.prev=h,this.next=l,this.eof=p,this.empty=m,this.timestamp=y,this.buffer=i,this.string=n,Object.defineProperty(this,"length",{get:function get(){return this.end-this.start}})}return r(CacheItem,[{key:"contains",value:function contains(e){return e>=this.start&&(e<this.end||this.eof)}},{key:"readBytes",value:function readBytes(e,t,i){var r=t-this.start,n=i-t;if(this.buffer){var s=new Uint8Array(this.buffer,r,n);e.set(s)}else{if(!this.string)throw new Error("invalid state");for(var o=this.string,a=0;a<n;a++)e[a]=o.charCodeAt(r+a)}this.timestamp=Date.now()}},{key:"split",value:function split(e){if(!this.empty||!this.contains(e))throw new Error("invalid split");var t=new CacheItem({start:this.start,end:e}),i=new CacheItem({start:e,end:this.eof?e:this.end,eof:this.eof});return t.next=i,i.prev=t,[t,i]}},{key:"first",value:function first(e){for(var t=this;t;t=t.next)if(e(t))return t;return null}},{key:"last",value:function last(e){for(var last=null,t=this;t&&e(t);t=t.next)last=t;return last}}]),CacheItem}();e.exports=n},function(e,t,i){"use strict";var r=i(35),n=i(36),s=i(37),o=i(38);var a=null;e.exports=function instantiate(e){if(!1===e.progressive)return new o(e);if(a||(a=function autoselect(){return r.supported()?r:s.supported()?s:n.supported()?n:null}()),!a)throw new Error("No supported backend class");return new a(e)}},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var n=i(4),s="moz-chunked-arraybuffer",o=function(e){function MozChunkedBackend(){return function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,MozChunkedBackend),function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(MozChunkedBackend.__proto__||Object.getPrototypeOf(MozChunkedBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(MozChunkedBackend,n),r(MozChunkedBackend,[{key:"initXHR",value:function initXHR(){(function get(e,t,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,t);if(void 0===r){var n=Object.getPrototypeOf(e);return null===n?void 0:get(n,t,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0})(MozChunkedBackend.prototype.__proto__||Object.getPrototypeOf(MozChunkedBackend.prototype),"initXHR",this).call(this),this.xhr.responseType=s}},{key:"onXHRProgress",value:function onXHRProgress(){var e=this.xhr.response;this.bytesRead+=e.byteLength,this.emit("buffer",e)}}]),MozChunkedBackend}();o.supported=function(){try{var e=new XMLHttpRequest;return e.responseType=s,e.responseType===s}catch(e){return!1}},e.exports=o},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),n=function get(e,t,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,t);if(void 0===r){var n=Object.getPrototypeOf(e);return null===n?void 0:get(n,t,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0};var s=i(11),o="ms-stream",a=function(e){function MSStreamBackend(e){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,MSStreamBackend);var t=function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(MSStreamBackend.__proto__||Object.getPrototypeOf(MSStreamBackend)).call(this,e));return t.stream=null,t.streamReader=null,t}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(MSStreamBackend,s),r(MSStreamBackend,[{key:"initXHR",value:function initXHR(){n(MSStreamBackend.prototype.__proto__||Object.getPrototypeOf(MSStreamBackend.prototype),"initXHR",this).call(this),this.xhr.responseType=o}},{key:"onXHRStart",value:function onXHRStart(){var e=this;this.xhr.addEventListener("readystatechange",function checkProgress(){3===e.xhr.readyState&&(e.stream=e.xhr.response,e.xhr.removeEventListener("readystatechange",checkProgress),e.emit("open"))})}},{key:"waitForStream",value:function waitForStream(){var e=this;return new Promise(function(t,i){e.stream?t(e.stream):function(){var r=null;e._onAbort=function(e){r(),i(e)};var n=function checkStart(){t(e.stream)};r=function oncomplete(){e.off("open",n),e._onAbort=null},e.on("open",n)}()})}},{key:"bufferToOffset",value:function bufferToOffset(e){var t=this;return this.waitForStream().then(function(i){return new Promise(function(r,n){if(t.streamReader)throw new Error("cannot trigger read when reading");if(t.offset>=e||t.eof)r();else{var s=e-t.offset;t.streamReader=new MSStreamReader,t.streamReader.onload=function(e){t.streamReader=null;var i=e.target.result;i.byteLength>0?(t.bytesRead+=i.byteLength,t.emit("buffer",i)):(t.eof=!0,t.emit("done")),r()},t.streamReader.onerror=function(){t.streamReader=null,t.stream=null,t.emit("error"),n(new Error("mystery error streaming"))},t._onAbort=function(e){t.streamReader.abort(),t.streamReader=null,t.stream=null,t.emit("error"),n(e)},t.streamReader.readAsArrayBuffer(i,s)}})})}},{key:"abort",value:function abort(){this.streamReader&&(this.streamReader.abort(),this.streamReader=null),this.stream&&(this.stream.msClose(),this.stream=null),n(MSStreamBackend.prototype.__proto__||Object.getPrototypeOf(MSStreamBackend.prototype),"abort",this).call(this)}}]),MSStreamBackend}();a.supported=function(){try{var e=new XMLHttpRequest;return e.open("GET","/robots.txt"),e.responseType=o,e.responseType===o}catch(e){return!1}},e.exports=a},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),n=function get(e,t,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,t);if(void 0===r){var n=Object.getPrototypeOf(e);return null===n?void 0:get(n,t,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0};var s=i(4),o=function(e){function BinaryStringBackend(){return function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,BinaryStringBackend),function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(BinaryStringBackend.__proto__||Object.getPrototypeOf(BinaryStringBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(BinaryStringBackend,s),r(BinaryStringBackend,[{key:"initXHR",value:function initXHR(){n(BinaryStringBackend.prototype.__proto__||Object.getPrototypeOf(BinaryStringBackend.prototype),"initXHR",this).call(this),this.xhr.responseType="text",this.xhr.overrideMimeType("text/plain; charset=x-user-defined")}},{key:"onXHRProgress",value:function onXHRProgress(){var e=this.xhr.responseText.slice(this.bytesRead);e.length>0&&(this.bytesRead+=e.length,this.emit("buffer",e))}},{key:"onXHRLoad",value:function onXHRLoad(){this.onXHRProgress(),n(BinaryStringBackend.prototype.__proto__||Object.getPrototypeOf(BinaryStringBackend.prototype),"onXHRLoad",this).call(this)}}]),BinaryStringBackend}();o.supported=function(){try{return!!(new XMLHttpRequest).overrideMimeType}catch(e){return!1}},e.exports=o},function(e,t,i){"use strict";var r=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),n=function get(e,t,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,t);if(void 0===r){var n=Object.getPrototypeOf(e);return null===n?void 0:get(n,t,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0};var s=i(4),o="arraybuffer",a=function(e){function ArrayBufferBackend(){return function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,ArrayBufferBackend),function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(ArrayBufferBackend.__proto__||Object.getPrototypeOf(ArrayBufferBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(ArrayBufferBackend,s),r(ArrayBufferBackend,[{key:"initXHR",value:function initXHR(){n(ArrayBufferBackend.prototype.__proto__||Object.getPrototypeOf(ArrayBufferBackend.prototype),"initXHR",this).call(this),this.xhr.responseType=o}},{key:"onXHRProgress",value:function onXHRProgress(){}},{key:"onXHRLoad",value:function onXHRLoad(){var e=this.xhr.response;this.bytesRead+=e.byteLength,this.emit("buffer",e),n(ArrayBufferBackend.prototype.__proto__||Object.getPrototypeOf(ArrayBufferBackend.prototype),"onXHRLoad",this).call(this)}}]),ArrayBufferBackend}();a.supported=function(){try{var e=new XMLHttpRequest;return e.responseType=o,e.responseType===o}catch(e){return!1}},e.exports=a},function(e,t,i){!function webpackUniversalModuleDefinition(t,i){e.exports=i()}(0,function(){return function(e){var t={};function __webpack_require__(i){if(t[i])return t[i].exports;var r=t[i]={exports:{},id:i,loaded:!1};return e[i].call(r.exports,r,r.exports,__webpack_require__),r.loaded=!0,r.exports}return __webpack_require__.m=e,__webpack_require__.c=t,__webpack_require__.p="",__webpack_require__(0)}([function(e,t,i){!function(){i(1);var t=i(2),r=i(4);function AudioFeeder(e){this._options=e||{},this._backend=null,this._resampleFractional=0}AudioFeeder.prototype.rate=0,AudioFeeder.prototype.targetRate=0,AudioFeeder.prototype.channels=0,AudioFeeder.prototype.bufferSize=0,Object.defineProperty(AudioFeeder.prototype,"bufferDuration",{get:function getBufferDuration(){return this.targetRate?this.bufferSize/this.targetRate:0}}),Object.defineProperty(AudioFeeder.prototype,"bufferThreshold",{get:function getBufferThreshold(){return this._backend?this._backend.bufferThreshold/this.targetRate:0},set:function setBufferThreshold(e){if(!this._backend)throw"Invalid state: AudioFeeder cannot set bufferThreshold before init";this._backend.bufferThreshold=Math.round(e*this.targetRate)}}),Object.defineProperty(AudioFeeder.prototype,"playbackPosition",{get:function getPlaybackPosition(){return this._backend?this.getPlaybackState().playbackPosition:0}}),Object.defineProperty(AudioFeeder.prototype,"durationBuffered",{get:function getDurationBuffered(){return this._backend?this.getPlaybackState().samplesQueued/this.targetRate:0}}),Object.defineProperty(AudioFeeder.prototype,"muted",{get:function getMuted(){if(this._backend)return this._backend.muted;throw"Invalid state: cannot get mute before init"},set:function setMuted(e){if(!this._backend)throw"Invalid state: cannot set mute before init";this._backend.muted=e}}),AudioFeeder.prototype.mute=function(){this.muted=!0},AudioFeeder.prototype.unmute=function(){this.muted=!1},Object.defineProperty(AudioFeeder.prototype,"volume",{get:function getVolume(){if(this._backend)return this._backend.volume;throw"Invalid state: cannot get volume before init"},set:function setVolume(e){if(!this._backend)throw"Invalid state: cannot set volume before init";this._backend.volume=e}}),AudioFeeder.prototype.init=function(e,i){if(this.channels=e,this.rate=i,t.isSupported())this._backend=new t(e,i,this._options);else{if(!r.isSupported())throw"No supported backend";this._backend=new r(e,i,this._options)}this.targetRate=this._backend.rate,this.bufferSize=this._backend.bufferSize,this._backend.onstarved=function(){this.onstarved&&this.onstarved()}.bind(this),this._backend.onbufferlow=function(){this.onbufferlow&&this.onbufferlow()}.bind(this)},AudioFeeder.prototype._resample=function(e){var t=this.rate,i=this.channels,r=this._backend.rate,n=this._backend.channels;if(t==r&&i==n)return e;var s=[],o=Math.trunc(this._resampleFractional),a=e[0].length,u=a*r/t+o,d=Math.round(u);this._resampleFractional-=o,this._resampleFractional+=u-d;for(var h=0;h<n;h++){var c=h;h>=i&&(c=0);for(var l=e[c],f=new Float32Array(d),p=0;p<f.length;p++)f[p]=l[p*a/u|0];s.push(f)}return s},AudioFeeder.prototype.bufferData=function(e){if(!this._backend)throw"Invalid state: AudioFeeder cannot bufferData before init";var t=this._resample(e);this._backend.appendBuffer(t)},AudioFeeder.prototype.getPlaybackState=function(){if(this._backend)return this._backend.getPlaybackState();throw"Invalid state: AudioFeeder cannot getPlaybackState before init"},AudioFeeder.prototype.waitUntilReady=function(e){if(!this._backend)throw"Invalid state: AudioFeeder cannot waitUntilReady before init";this._backend.waitUntilReady(e)},AudioFeeder.prototype.start=function(){if(!this._backend)throw"Invalid state: AudioFeeder cannot start before init";this._backend.start()},AudioFeeder.prototype.stop=function(){if(!this._backend)throw"Invalid state: AudioFeeder cannot stop before init";this._backend.stop()},AudioFeeder.prototype.flush=function(){if(!this._backend)throw"Invalid state: AudioFeeder cannot flush before init";this._backend.flush()},AudioFeeder.prototype.close=function(){this._backend&&(this._backend.close(),this._backend=null)},AudioFeeder.prototype.onstarved=null,AudioFeeder.prototype.onbufferlow=null,AudioFeeder.isSupported=function(){return!!Float32Array&&(t.isSupported()||r.isSupported())},AudioFeeder.initSharedAudioContext=function(){return t.isSupported()?t.initSharedAudioContext():null},e.exports=AudioFeeder}()},function(e,t){
/**
	 * @file Abstraction around a queue of audio buffers.
	 *
	 * <AUTHOR> Vibber <<EMAIL>>
	 * @copyright (c) 2013-2016 Brion Vibber
	 * @license MIT
	 */
function BufferQueue(e,t){if(e<1||e!==Math.round(e))throw"Invalid channel count for BufferQueue";this.channels=e,this.bufferSize=t,this.flush()}BufferQueue.prototype.flush=function(){this._buffers=[],this._pendingBuffer=this.createBuffer(this.bufferSize),this._pendingPos=0},BufferQueue.prototype.sampleCount=function(){var e=0;return this._buffers.forEach(function(t){e+=t[0].length}),e},BufferQueue.prototype.createBuffer=function(e){for(var t=[],i=0;i<this.channels;i++)t[i]=new Float32Array(e);return t},BufferQueue.prototype.validate=function(e){if(e.length!==this.channels)return!1;for(var t,i=0;i<e.length;i++){var r=e[i];if(!(r instanceof Float32Array))return!1;if(0==i)t=r.length;else if(r.length!==t)return!1}return!0},BufferQueue.prototype.appendBuffer=function(e){if(!this.validate(e))throw"Invalid audio buffer passed to BufferQueue.appendBuffer";for(var t=e[0].length,i=this.channels,r=this._pendingPos,n=this._pendingBuffer,s=this.bufferSize,o=0;o<t;o++){for(var a=0;a<i;a++)n[a][r]=e[a][o];++r==s&&(this._buffers.push(n),r=this._pendingPos=0,n=this._pendingBuffer=this.createBuffer(s))}this._pendingPos=r},BufferQueue.prototype.prependBuffer=function(e){if(!this.validate(e))throw"Invalid audio buffer passed to BufferQueue.prependBuffer";var t=this._buffers.slice(0);t.push(this.trimBuffer(this._pendingBuffer,0,this._pendingPos)),this.flush(),this.appendBuffer(e);for(var i=0;i<t.length;i++)this.appendBuffer(t[i])},BufferQueue.prototype.nextBuffer=function(){if(this._buffers.length)return this._buffers.shift();var e=this.trimBuffer(this._pendingBuffer,0,this._pendingPos);return this._pendingBuffer=this.createBuffer(this.bufferSize),this._pendingPos=0,e},BufferQueue.prototype.trimBuffer=function(e,t,i){var r=e[0].length,n=t+Math.min(i,r);if(0==t&&n>=r)return e;for(var s=[],o=0;o<this.channels;o++)s[o]=e[o].subarray(t,n);return s},e.exports=BufferQueue},function(e,t,i){
/**
	 * @file Web Audio API backend for AudioFeeder
	 * <AUTHOR> Vibber <<EMAIL>>
	 * @copyright (c) 2013-2016 Brion Vibber
	 * @license MIT
	 */
!function(){var t=window.AudioContext||window.webkitAudioContext,r=i(1),n=i(3);function WebAudioBackend(e,t,i){var n=i.audioContext||WebAudioBackend.initSharedAudioContext();if(this._context=n,this.output=i.output||n.destination,this.rate=n.sampleRate,this.channels=Math.min(e,2),i.bufferSize&&(this.bufferSize=0|i.bufferSize),this.bufferThreshold=2*this.bufferSize,this._bufferQueue=new r(this.channels,this.bufferSize),this._playbackTimeAtBufferTail=n.currentTime,this._queuedTime=0,this._delayedTime=0,this._dropped=0,this._liveBuffer=this._bufferQueue.createBuffer(this.bufferSize),n.createScriptProcessor)this._node=n.createScriptProcessor(this.bufferSize,0,this.channels);else{if(!n.createJavaScriptNode)throw new Error("Bad version of web audio API?");this._node=n.createJavaScriptNode(this.bufferSize,0,this.channels)}}WebAudioBackend.prototype.bufferSize=4096,WebAudioBackend.prototype.bufferThreshold=8192,WebAudioBackend.prototype._volume=1,Object.defineProperty(WebAudioBackend.prototype,"volume",{get:function getVolume(){return this._volume},set:function setVolume(e){this._volume=+e}}),WebAudioBackend.prototype._muted=!1,Object.defineProperty(WebAudioBackend.prototype,"muted",{get:function getMuted(){return this._muted},set:function setMuted(e){this._muted=!!e}}),WebAudioBackend.prototype._audioProcess=function(e){var t,i,r,s,o;o="number"==typeof e.playbackTime?e.playbackTime:this._context.currentTime+this.bufferSize/this.rate;var a=this._playbackTimeAtBufferTail;if(a<o&&(this._delayedTime+=o-a),this._bufferQueue.sampleCount()<this.bufferSize&&this.onstarved&&this.onstarved(),this._bufferQueue.sampleCount()<this.bufferSize){for(t=0;t<this.channels;t++)for(r=e.outputBuffer.getChannelData(t),s=0;s<this.bufferSize;s++)r[s]=0;this._dropped++}else{var u=this.muted?0:this.volume,d=this._bufferQueue.nextBuffer();if(d[0].length<this.bufferSize)throw"Audio buffer not expected length.";for(t=0;t<this.channels;t++)for(i=d[t],this._liveBuffer[t].set(d[t]),r=e.outputBuffer.getChannelData(t),s=0;s<i.length;s++)r[s]=i[s]*u;this._queuedTime+=this.bufferSize/this.rate,this._playbackTimeAtBufferTail=o+this.bufferSize/this.rate,this._bufferQueue.sampleCount()<Math.max(this.bufferSize,this.bufferThreshold)&&this.onbufferlow&&n(this.onbufferlow.bind(this))}},WebAudioBackend.prototype._samplesQueued=function(){return this._bufferQueue.sampleCount()+Math.floor(this._timeAwaitingPlayback()*this.rate)},WebAudioBackend.prototype._timeAwaitingPlayback=function(){return Math.max(0,this._playbackTimeAtBufferTail-this._context.currentTime)},WebAudioBackend.prototype.getPlaybackState=function(){return{playbackPosition:this._queuedTime-this._timeAwaitingPlayback(),samplesQueued:this._samplesQueued(),dropped:this._dropped,delayed:this._delayedTime}},WebAudioBackend.prototype.waitUntilReady=function(e){e()},WebAudioBackend.prototype.appendBuffer=function(e){this._bufferQueue.appendBuffer(e)},WebAudioBackend.prototype.start=function(){this._node.onaudioprocess=this._audioProcess.bind(this),this._node.connect(this.output),this._playbackTimeAtBufferTail=this._context.currentTime},WebAudioBackend.prototype.stop=function(){if(this._node){var e=this._timeAwaitingPlayback();if(e>0){var t=Math.round(e*this.rate),i=this._liveBuffer?this._liveBuffer[0].length:0;t>i?(this._bufferQueue.prependBuffer(this._liveBuffer),this._bufferQueue.prependBuffer(this._bufferQueue.createBuffer(t-i))):this._bufferQueue.prependBuffer(this._bufferQueue.trimBuffer(this._liveBuffer,i-t,t)),this._playbackTimeAtBufferTail-=e}this._node.onaudioprocess=null,this._node.disconnect()}},WebAudioBackend.prototype.flush=function(){this._bufferQueue.flush()},WebAudioBackend.prototype.close=function(){this.stop(),this._context=null},WebAudioBackend.prototype.onstarved=null,WebAudioBackend.prototype.onbufferlow=null,WebAudioBackend.isSupported=function(){return!!t},WebAudioBackend.sharedAudioContext=null,WebAudioBackend.initSharedAudioContext=function(){if(!WebAudioBackend.sharedAudioContext&&WebAudioBackend.isSupported()){var e,i=new t;if(i.createScriptProcessor)e=i.createScriptProcessor(1024,0,2);else{if(!i.createJavaScriptNode)throw new Error("Bad version of web audio API?");e=i.createJavaScriptNode(1024,0,2)}e.connect(i.destination),e.disconnect(),WebAudioBackend.sharedAudioContext=i}return WebAudioBackend.sharedAudioContext},e.exports=WebAudioBackend}()},function(e,t){e.exports=function(){if(void 0!==window.setImmediate)return window.setImmediate;if(window&&window.postMessage){var e=[];return window.addEventListener("message",function(t){if(t.source===window){var i=t.data;if("object"==typeof i&&i.nextTickBrowserPingMessage){var r=e.pop();r&&r()}}}),function(t){e.push(t),window.postMessage({nextTickBrowserPingMessage:!0},document.location.toString())}}return function(e){setTimeout(e,0)}}()},function(e,t,i){!function(){var t=i(5),r=i(3),n=function(e,i,n){var s={};"string"==typeof(n=n||{}).base&&(s.swf=n.base+"/"+t),n.bufferSize&&(this.bufferSize=0|n.bufferSize),this._flashaudio=new DynamicAudio(s),this._flashBuffer="",this._cachedFlashState=null,this._cachedFlashTime=0,this._cachedFlashInterval=185,this._waitUntilReadyQueue=[],this.onready=function(){for(this._flashaudio.flashElement.setBufferSize(this.bufferSize),this._flashaudio.flashElement.setBufferThreshold(this.bufferThreshold);this._waitUntilReadyQueue.length;){this._waitUntilReadyQueue.shift().apply(this)}},this.onlog=function(e){console.log("AudioFeeder FlashBackend: "+e)},this.bufferThreshold=2*this.bufferSize;var o={ready:"sync",log:"sync",starved:"sync",bufferlow:"async"};this._callbackName="AudioFeederFlashBackendCallback"+this._flashaudio.id;window[this._callbackName]=function(e){var t=o[e],i=this["on"+e];t&&i&&("async"===t?r(i.bind(this)):(i.apply(this,Array.prototype.slice.call(arguments,1)),this._flushFlashBuffer()))}.bind(this)};n.prototype.rate=44100,n.prototype.channels=2,n.prototype.bufferSize=4096,n.prototype._bufferThreshold=8192,Object.defineProperty(n.prototype,"bufferThreshold",{get:function getBufferThreshold(){return this._bufferThreshold},set:function setBufferThreshold(e){this._bufferThreshold=0|e,this.waitUntilReady(function(){this._flashaudio.flashElement.setBufferThreshold(this._bufferThreshold)}.bind(this))}}),n.prototype._volume=1,Object.defineProperty(n.prototype,"volume",{get:function getVolume(){return this._volume},set:function setVolume(e){this._volume=+e,this.waitUntilReady(this._flashVolumeUpdate.bind(this))}}),n.prototype._muted=!1,Object.defineProperty(n.prototype,"muted",{get:function getMuted(){return this._muted},set:function setMuted(e){this._muted=!!e,this.waitUntilReady(this._flashVolumeUpdate.bind(this))}}),n.prototype._paused=!0,n.prototype._flashVolumeUpdate=function(){this._flashaudio&&this._flashaudio.flashElement&&this._flashaudio.flashElement.setVolume&&this._flashaudio.flashElement.setVolume(this.muted?0:this.volume)},n.prototype._resampleFlash=function(e){for(var t=e[0].length,i=new Float32Array(2*t),r=e[0],n=this.channels>1?e[1]:r,s=0;s<t;s++){var o=s,a=2*s;i[a]=r[o],i[a+1]=n[o]}return i};for(var s=[],o=0;o<256;o++)s[o]=String.fromCharCode(o+57344);function DynamicAudio(e){if(!(this instanceof arguments.callee))return new arguments.callee(arguments);"function"==typeof this.init&&this.init.apply(this,e&&e.callee?e:arguments)}n.prototype._flushFlashBuffer=function(){var e=this._flashBuffer,t=this._flashaudio.flashElement;this._flashBuffer="",e.length>0&&(this._cachedFlashState=t.write(e),this._cachedFlashTime=Date.now())},n.prototype.appendBuffer=function(e){var t=this._resampleFlash(e);if(t.length>0){var i=function binaryString(e){for(var t=new Uint8Array(e),i=t.length,r="",n=0;n<i;n+=8)r+=s[t[n]],r+=s[t[n+1]],r+=s[t[n+2]],r+=s[t[n+3]],r+=s[t[n+4]],r+=s[t[n+5]],r+=s[t[n+6]],r+=s[t[n+7]];return r}(t.buffer);this._flashBuffer+=i,this._flashBuffer.length>=8*this.bufferSize&&this._flushFlashBuffer()}},n.prototype.getPlaybackState=function(){if(this._flashaudio&&this._flashaudio.flashElement&&this._flashaudio.flashElement.write){var e,t=Date.now(),i=this._paused?0:t-this._cachedFlashTime;if(this._cachedFlashState&&i<this._cachedFlashInterval){var r=this._cachedFlashState;e={playbackPosition:r.playbackPosition+i/1e3,samplesQueued:r.samplesQueued-Math.max(0,Math.round(i*this.rate/1e3)),dropped:r.dropped,delayed:r.delayed}}else e=this._flashaudio.flashElement.getPlaybackState(),this._cachedFlashState=e,this._cachedFlashTime=t;return e.samplesQueued+=this._flashBuffer.length/8,e}return{playbackPosition:0,samplesQueued:0,dropped:0,delayed:0}},n.prototype.waitUntilReady=function(e){this._flashaudio&&this._flashaudio.flashElement.write?e.apply(this):this._waitUntilReadyQueue.push(e)},n.prototype.start=function(){this._flushFlashBuffer(),this._flashaudio.flashElement.start(),this._paused=!1,this._cachedFlashState=null},n.prototype.stop=function(){this._flashaudio.flashElement.stop(),this._paused=!0,this._cachedFlashState=null},n.prototype.flush=function(){this._flashBuffer="",this._flashaudio.flashElement.flush(),this._cachedFlashState=null},n.prototype.close=function(){this.stop();var e=this._flashaudio.flashWrapper;e.parentNode.removeChild(e),this._flashaudio=null,delete window[this._callbackName]},n.prototype.onstarved=null,n.prototype.onbufferlow=null,n.isSupported=function(){if(-1!==navigator.userAgent.indexOf("Trident"))try{new ActiveXObject("ShockwaveFlash.ShockwaveFlash");return!0}catch(e){return!1}return!1},DynamicAudio.nextId=1,DynamicAudio.prototype={nextId:null,swf:t,flashWrapper:null,flashElement:null,init:function(e){this.id=DynamicAudio.nextId++,e&&void 0!==e.swf&&(this.swf=e.swf),this.flashWrapper=document.createElement("div"),this.flashWrapper.id="dynamicaudio-flashwrapper-"+this.id;var t=this.flashWrapper.style;t.position="fixed",t.width="11px",t.height="11px",t.bottom=t.left="0px",t.overflow="hidden",this.flashElement=document.createElement("div"),this.flashElement.id="dynamicaudio-flashelement-"+this.id,this.flashWrapper.appendChild(this.flashElement),document.body.appendChild(this.flashWrapper);var i=this.flashElement.id,r='<param name="FlashVars" value="objectId='+this.id+'">';this.flashWrapper.innerHTML="<object id='"+i+"' width='10' height='10' type='application/x-shockwave-flash' data='"+this.swf+"' style='visibility: visible;'><param name='allowscriptaccess' value='always'>"+r+"</object>",this.flashElement=document.getElementById(i)}},e.exports=n}()},function(e,t,i){e.exports=i.p+"dynamicaudio.swf?version=2c1ce3bfb7e6fa65c26d726a00017a94"}])})},function(e,t,i){e.exports=i.p+"dynamicaudio.swf?version=2c1ce3bfb7e6fa65c26d726a00017a94"},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=class Bisector{constructor(e){this.lower=e.start,this.upper=e.end,this.onprocess=e.process,this.position=0,this.n=0}iterate(){return this.n++,this.position=Math.floor((this.lower+this.upper)/2),this.onprocess(this.lower,this.upper,this.position)}start(){return this.iterate(),this}left(){return this.upper=this.position,this.iterate()}right(){return this.lower=this.position,this.iterate()}};t.default=r},function(e,t,i){"use strict";var r=i(0);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(i(2));
/**
 * Proxy object for web worker interface for codec classes.
 *
 * Used by the high-level player interface.
 *
 * <AUTHOR> Vibber <<EMAIL>>
 * @copyright 2015-2019 Brion Vibber
 * @license MIT-style
 */var s=class OGVWrapperCodec{constructor(e){return this.options=e||{},this.demuxer=null,this.videoDecoder=null,this.audioDecoder=null,this.flushIter=0,this.loadedMetadata=!1,this.processing=!1,Object.defineProperties(this,{duration:{get:function get(){return this.loadedMetadata?this.demuxer.duration:NaN}},hasAudio:{get:function get(){return this.loadedMetadata&&!!this.audioDecoder}},audioReady:{get:function get(){return this.hasAudio&&this.demuxer.audioReady}},audioTimestamp:{get:function get(){return this.demuxer.audioTimestamp}},audioFormat:{get:function get(){return this.hasAudio?this.audioDecoder.audioFormat:null}},audioBuffer:{get:function get(){return this.hasAudio?this.audioDecoder.audioBuffer:null}},hasVideo:{get:function get(){return this.loadedMetadata&&!!this.videoDecoder}},frameReady:{get:function get(){return this.hasVideo&&this.demuxer.frameReady}},frameTimestamp:{get:function get(){return this.demuxer.frameTimestamp}},keyframeTimestamp:{get:function get(){return this.demuxer.keyframeTimestamp}},nextKeyframeTimestamp:{get:function get(){return this.demuxer.nextKeyframeTimestamp}},videoFormat:{get:function get(){return this.hasVideo?this.videoDecoder.videoFormat:null}},frameBuffer:{get:function get(){return this.hasVideo?this.videoDecoder.frameBuffer:null}},seekable:{get:function get(){return this.demuxer.seekable}},demuxerCpuTime:{get:function get(){return this.demuxer?this.demuxer.cpuTime:0}},audioCpuTime:{get:function get(){return this.audioDecoder?this.audioDecoder.cpuTime:0}},videoCpuTime:{get:function get(){return this.videoDecoder?this.videoDecoder.cpuTime:0}}}),this.loadedDemuxerMetadata=!1,this.loadedAudioMetadata=!1,this.loadedVideoMetadata=!1,this.loadedAllMetadata=!1,this.onseek=null,this.videoBytes=0,this.audioBytes=0,this}flushSafe(e){let t=this.flushIter;return i=>{this.flushIter<=t&&e(i)}}init(e){let t;this.processing=!0,t="video/webm"===this.options.type||"audio/webm"===this.options.type?this.options.wasm?"OGVDemuxerWebMW":"OGVDemuxerWebM":this.options.wasm?"OGVDemuxerOggW":"OGVDemuxerOgg",n.default.loadClass(t,t=>{t().then(t=>{this.demuxer=t,t.onseek=(e=>{this.onseek&&this.onseek(e)}),t.init(()=>{this.processing=!1,e()})})})}close(){this.demuxer&&(this.demuxer.close(),this.demuxer=null),this.videoDecoder&&(this.videoDecoder.close(),this.videoDecoder=null),this.audioDecoder&&(this.audioDecoder.close(),this.audioDecoder=null)}receiveInput(e,t){this.demuxer.receiveInput(e,t)}process(e){if(this.processing)throw new Error("reentrancy fail on OGVWrapperCodec.process");this.processing=!0;let t=t=>{this.processing=!1,e(t)},i=()=>{this.demuxer.process(t)};this.demuxer.loadedMetadata&&!this.loadedDemuxerMetadata?this.loadAudioCodec(()=>{this.loadVideoCodec(()=>{this.loadedDemuxerMetadata=!0,this.loadedAudioMetadata=!this.audioDecoder,this.loadedVideoMetadata=!this.videoDecoder,this.loadedAllMetadata=this.loadedAudioMetadata&&this.loadedVideoMetadata,t(!0)})}):this.loadedDemuxerMetadata&&!this.loadedAudioMetadata?this.audioDecoder.loadedMetadata?(this.loadedAudioMetadata=!0,this.loadedAllMetadata=this.loadedAudioMetadata&&this.loadedVideoMetadata,t(!0)):this.demuxer.audioReady?this.demuxer.dequeueAudioPacket((e,i)=>{this.audioBytes+=e.byteLength,this.audioDecoder.processHeader(e,e=>{t(!0)})}):i():this.loadedAudioMetadata&&!this.loadedVideoMetadata?this.videoDecoder.loadedMetadata?(this.loadedVideoMetadata=!0,this.loadedAllMetadata=this.loadedAudioMetadata&&this.loadedVideoMetadata,t(!0)):this.demuxer.frameReady?(this.processing=!0,this.demuxer.dequeueVideoPacket(e=>{this.videoBytes+=e.byteLength,this.videoDecoder.processHeader(e,()=>{t(!0)})})):i():this.loadedVideoMetadata&&!this.loadedMetadata&&this.loadedAllMetadata?(this.loadedMetadata=!0,t(!0)):!this.loadedMetadata||this.hasAudio&&!this.demuxer.audioReady||this.hasVideo&&!this.demuxer.frameReady?i():t(!0)}decodeFrame(e){let t=this.flushSafe(e),i=this.frameTimestamp,r=this.keyframeTimestamp;this.demuxer.dequeueVideoPacket(e=>{this.videoBytes+=e.byteLength,this.videoDecoder.processFrame(e,e=>{let n=this.videoDecoder.frameBuffer;n&&(n.timestamp=i,n.keyframeTimestamp=r),t(e)})})}decodeAudio(e){let t=this.flushSafe(e);this.demuxer.dequeueAudioPacket((e,i)=>{this.audioBytes+=e.byteLength,this.audioDecoder.processAudio(e,e=>{if(i){let e=this.audioDecoder.audioBuffer,t=[];for(let r of e){let e=Math.round(i*this.audioFormat.rate/1e9);e>0?t.push(r.subarray(0,r.length-Math.min(e,r.length))):t.push(r.subarray(Math.min(Math.abs(e),r.length),r.length))}this.audioDecoder.audioBuffer=t}return t(e)})})}discardFrame(e){this.demuxer.dequeueVideoPacket(t=>{this.videoBytes+=t.byteLength,e()})}discardAudio(e){this.demuxer.dequeueAudioPacket((t,i)=>{this.audioBytes+=t.byteLength,e()})}flush(e){this.flushIter++,this.demuxer.flush(e)}sync(){this.videoDecoder&&this.videoDecoder.sync()}getKeypointOffset(e,t){this.demuxer.getKeypointOffset(e,t)}seekToKeypoint(e,t){this.demuxer.seekToKeypoint(e,this.flushSafe(t))}loadAudioCodec(e){if(this.demuxer.audioCodec){let t=!!this.options.wasm,i={vorbis:t?"OGVDecoderAudioVorbisW":"OGVDecoderAudioVorbis",opus:t?"OGVDecoderAudioOpusW":"OGVDecoderAudioOpus"}[this.demuxer.audioCodec];this.processing=!0,n.default.loadClass(i,t=>{let i={};this.demuxer.audioFormat&&(i.audioFormat=this.demuxer.audioFormat),t(i).then(t=>{this.audioDecoder=t,t.init(()=>{this.loadedAudioMetadata=t.loadedMetadata,this.processing=!1,e()})})},{worker:this.options.worker})}else e()}loadVideoCodec(e){if(this.demuxer.videoCodec){let t=!!this.options.wasm,i=!!this.options.threading,r={theora:t?"OGVDecoderVideoTheoraW":"OGVDecoderVideoTheora",vp8:t?i?"OGVDecoderVideoVP8MTW":"OGVDecoderVideoVP8W":"OGVDecoderVideoVP8",vp9:t?i?"OGVDecoderVideoVP9MTW":"OGVDecoderVideoVP9W":"OGVDecoderVideoVP9",av1:t?i?"OGVDecoderVideoAV1MTW":"OGVDecoderVideoAV1W":"OGVDecoderVideoAV1"}[this.demuxer.videoCodec];this.processing=!0,n.default.loadClass(r,t=>{let r={};this.demuxer.videoFormat&&(r.videoFormat=this.demuxer.videoFormat),this.options.memoryLimit&&(r.memoryLimit=this.options.memoryLimit),i&&delete window.ENVIRONMENT_IS_PTHREAD,t(r).then(t=>{this.videoDecoder=t,t.init(()=>{this.loadedVideoMetadata=t.loadedMetadata,this.processing=!1,e()})})},{worker:this.options.worker&&!this.options.threading})}else e()}};t.default=s}])});