
var OGVDecoderVideoVP9 = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  return (
function(OGVDecoderVideoVP9) {
  OGVDecoderVideoVP9 = OGVDecoderVideoVP9 || {};

var b;b||(b=typeof OGVDecoderVideoVP9 !== 'undefined' ? OGVDecoderVideoVP9 : {});var f=b;b.memoryLimit&&(b.TOTAL_MEMORY=f.memoryLimit);var p={},q;for(q in b)b.hasOwnProperty(q)&&(p[q]=b[q]);b.arguments=[];b.thisProgram="./this.program";b.quit=function(a,c){throw c;};b.preRun=[];b.postRun=[];var r=!1,t=!1,u=!1,v=!1;r="object"===typeof window;t="function"===typeof importScripts;u="object"===typeof process&&"function"===typeof require&&!r&&!t;v=!r&&!u&&!t;var w="";
if(u){w=__dirname+"/";var y,z;b.read=function(a,c){var d=A(a);d||(y||(y=require("fs")),z||(z=require("path")),a=z.normalize(a),d=y.readFileSync(a));return c?d:d.toString()};b.readBinary=function(a){a=b.read(a,!0);a.buffer||(a=new Uint8Array(a));assert(a.buffer);return a};1<process.argv.length&&(b.thisProgram=process.argv[1].replace(/\\/g,"/"));b.arguments=process.argv.slice(2);process.on("unhandledRejection",B);b.quit=function(a){process.exit(a)};b.inspect=function(){return"[Emscripten Module object]"}}else if(v)"undefined"!=
typeof read&&(b.read=function(a){var c=A(a);return c?aa(c):read(a)}),b.readBinary=function(a){var c;if(c=A(a))return c;if("function"===typeof readbuffer)return new Uint8Array(readbuffer(a));c=read(a,"binary");assert("object"===typeof c);return c},"undefined"!=typeof scriptArgs?b.arguments=scriptArgs:"undefined"!=typeof arguments&&(b.arguments=arguments),"function"===typeof quit&&(b.quit=function(a){quit(a)});else if(r||t)t?w=self.location.href:document.currentScript&&(w=document.currentScript.src),
_scriptDir&&(w=_scriptDir),0!==w.indexOf("blob:")?w=w.substr(0,w.lastIndexOf("/")+1):w="",b.read=function(a){try{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText}catch(d){if(a=A(a))return aa(a);throw d;}},t&&(b.readBinary=function(a){try{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}catch(d){if(a=A(a))return a;throw d;}}),b.readAsync=function(a,c,d){var e=new XMLHttpRequest;e.open("GET",a,!0);e.responseType=
"arraybuffer";e.onload=function(){if(200==e.status||0==e.status&&e.response)c(e.response);else{var g=A(a);g?c(g.buffer):d()}};e.onerror=d;e.send(null)},b.setWindowTitle=function(a){document.title=a};var ba=b.print||("undefined"!==typeof console?console.log.bind(console):"undefined"!==typeof print?print:null),ca=b.printErr||("undefined"!==typeof printErr?printErr:"undefined"!==typeof console&&console.warn.bind(console)||ba);for(q in p)p.hasOwnProperty(q)&&(b[q]=p[q]);p=void 0;var da=0,ea=!1;
function assert(a,c){a||B("Assertion failed: "+c)}"undefined"!==typeof TextDecoder&&new TextDecoder("utf8");"undefined"!==typeof TextDecoder&&new TextDecoder("utf-16le");var buffer,C,D,E=b.TOTAL_MEMORY||33554432;5242880>E&&ca("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+E+"! (TOTAL_STACK=5242880)");b.buffer?buffer=b.buffer:(buffer=new ArrayBuffer(E),b.buffer=buffer);b.HEAP8=new Int8Array(buffer);b.HEAP16=new Int16Array(buffer);b.HEAP32=D=new Int32Array(buffer);b.HEAPU8=C=new Uint8Array(buffer);
b.HEAPU16=new Uint16Array(buffer);b.HEAPU32=new Uint32Array(buffer);b.HEAPF32=new Float32Array(buffer);b.HEAPF64=new Float64Array(buffer);D[8280]=5276256;function F(a){for(;0<a.length;){var c=a.shift();if("function"==typeof c)c();else{var d=c.B;"number"===typeof d?void 0===c.w?b.dynCall_v(d):b.dynCall_vi(d,c.w):d(void 0===c.w?null:c.w)}}}var fa=[],ha=[],ia=[],ja=[],ka=!1;function la(){var a=b.preRun.shift();fa.unshift(a)}
Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(a,c){var d=a&65535,e=c&65535;return d*e+((a>>>16)*e+d*(c>>>16)<<16)|0});Math.clz32||(Math.clz32=function(a){var c=32,d=a>>16;d&&(c-=16,a=d);if(d=a>>8)c-=8,a=d;if(d=a>>4)c-=4,a=d;if(d=a>>2)c-=2,a=d;return a>>1?c-2:c-a});Math.trunc||(Math.trunc=function(a){return 0>a?Math.ceil(a):Math.floor(a)});var H=0,I=null,J=null;b.preloadedImages={};b.preloadedAudios={};var K=null,O="data:application/octet-stream;base64,";K="data:application/octet-stream;base64,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";
function ma(){B("OOM")}var ta=!1;function aa(a){for(var c=[],d=0;d<a.length;d++){var e=a[d];255<e&&(ta&&assert(!1,"Character code "+e+" ("+String.fromCharCode(e)+")  at offset "+d+" not in 0x00-0xFF."),e&=255);c.push(String.fromCharCode(e))}return c.join("")}
var ua="function"===typeof atob?atob:function(a){var c="",d=0;a=a.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));var h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));var k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));e=e<<2|g>>4;
g=(g&15)<<4|h>>2;var l=(h&3)<<6|k;c+=String.fromCharCode(e);64!==h&&(c+=String.fromCharCode(g));64!==k&&(c+=String.fromCharCode(l))}while(d<a.length);return c};
function A(a){if(String.prototype.startsWith?a.startsWith(O):0===a.indexOf(O)){a=a.slice(O.length);if("boolean"===typeof u&&u){try{var c=Buffer.from(a,"base64")}catch(h){c=new Buffer(a,"base64")}var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength)}else try{var e=ua(a),g=new Uint8Array(e.length);for(c=0;c<e.length;++c)g[c]=e.charCodeAt(c);d=g}catch(h){throw Error("Converting base64 string to bytes failed.");}return d}}
var va={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Float32Array:Float32Array,Float64Array:Float64Array},Ea={a:B,b:function(a){da=a},c:function(){return da},d:function(a){var c=P();try{return wa(a)}catch(d){Q(c);if(d!==d+0&&"longjmp"!==d)throw d;R(1,0)}},e:function(a,c){var d=P();try{return xa(a,c)}catch(e){Q(d);if(e!==e+0&&"longjmp"!==e)throw e;R(1,0)}},f:function(a,c,d){var e=P();try{return ya(a,c,d)}catch(g){Q(e);if(g!==
g+0&&"longjmp"!==g)throw g;R(1,0)}},g:function(a,c,d,e,g,h){var k=P();try{return za(a,c,d,e,g,h)}catch(l){Q(k);if(l!==l+0&&"longjmp"!==l)throw l;R(1,0)}},h:function(a){var c=P();try{Aa(a)}catch(d){Q(c);if(d!==d+0&&"longjmp"!==d)throw d;R(1,0)}},i:function(a,c){var d=P();try{Ba(a,c)}catch(e){Q(d);if(e!==e+0&&"longjmp"!==e)throw e;R(1,0)}},j:function(a,c,d,e,g){var h=P();try{Ca(a,c,d,e,g)}catch(k){Q(h);if(k!==k+0&&"longjmp"!==k)throw k;R(1,0)}},k:function(a,c,d,e,g,h,k){var l=P();try{Da(a,c,d,e,g,h,
k)}catch(n){Q(l);if(n!==n+0&&"longjmp"!==n)throw n;R(1,0)}},l:function(a){b.___errno_location&&(D[b.___errno_location()>>2]=a);return a},m:function(){return E},n:function(a,c,d){C.set(C.subarray(c,c+d),a)},o:function(a){ma(a)},p:function(a,c){R(a,c||1);throw"longjmp";},q:function(a,c,d,e,g,h,k,l,n,G,L,M,V,W,na,oa){function X(a,c,d,e,g,h,k,l){a=La.subarray(a,a+c*d);var m=a.buffer;"function"===typeof m.slice?(a=m.slice(a.byteOffset,a.byteOffset+a.byteLength),a=new Uint8Array(a)):a=new Uint8Array(a);
var n,x;for(n=x=0;n<g;n++,x+=c)for(m=0;m<c;m++)a[x+m]=l;for(;n<g+k;n++,x+=c){for(m=0;m<e;m++)a[x+m]=l;for(m=e+h;m<c;m++)a[x+m]=l}for(;n<d;n++,x+=c)for(m=0;m<c;m++)a[x+m]=l;return a}var La=b.HEAPU8,N=b.videoFormat,pa=(V&-2)*n/k,qa=(W&-2)*G/l,ra=L*n/k,sa=M*G/l;L===N.cropWidth&&M===N.cropHeight&&(na=N.displayWidth,oa=N.displayHeight);b.frameBuffer={format:{width:k,height:l,chromaWidth:n,chromaHeight:G,cropLeft:V,cropTop:W,cropWidth:L,cropHeight:M,displayWidth:na,displayHeight:oa},y:{bytes:X(a,c,l,V,
W,L,M,0),stride:c},u:{bytes:X(d,e,G,pa,qa,ra,sa,128),stride:e},v:{bytes:X(g,h,G,pa,qa,ra,sa,128),stride:h}}},r:ma,s:33360,t:33120};// EMSCRIPTEN_START_ASM
var S=(/** @suppress {uselessCode} */ function(global,env,buffer) {
"use asm";var a=new global.Int8Array(buffer),b=new global.Int16Array(buffer),c=new global.Int32Array(buffer),d=new global.Uint8Array(buffer),e=new global.Uint16Array(buffer),f=new global.Float32Array(buffer),g=new global.Float64Array(buffer),h=env.s|0,i=env.t|0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=global.Math.imul,s=global.Math.clz32,t=env.a,u=env.b,v=env.c,w=env.d,x=env.e,y=env.f,z=env.g,A=env.h,B=env.i,C=env.j,D=env.k,E=env.l,F=env.m,G=env.n,H=env.o,I=env.p,J=env.q,K=env.r,L=33376,M=5276256,N=0.0;
// EMSCRIPTEN_START_FUNCS
function gd(e,f,g,h,i,j,k,l,m,n){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;if(!(c[e+748>>2]|0))L=0;else L=(c[(c[e+292>>2]|0)+4>>2]|0)+(((c[e+608>>2]|0)*k|0)*12|0)+(l*12|0)|0;O=g<<24>>24!=11;C=j;c[C>>2]=0;c[C+4>>2]=0;C=e+600|0;D=f+280|0;E=f+284|0;F=f+292|0;G=f+288|0;a:do if(n){n=c[i>>2]|0;o=n+k|0;p=(o|0)<0;do if((m|0)>-1){do if((!p?(t=c[i+4>>2]|0,q=t+l|0,(o|0)<(c[C>>2]|0)?(q|0)>=(c[D>>2]|0):0):0)?(q|0)<(c[E>>2]|0):0){n=c[(c[F>>2]|0)+(((c[G>>2]|0)*n|0)+t<<2)>>2]|0;if((a[n+8>>0]|0)==h<<24>>24){if((d[n>>0]|0)<3)n=n+20+((c[29152+(m<<3)+(((t|0)==0&1)<<2)>>2]|0)*12|0)+4|0;else n=n+12|0;n=c[n>>2]|0;c[j>>2]=n;if(O){n=1;break a}else{s=1;o=1;t=n;break}}if((a[n+9>>0]|0)==h<<24>>24){if((d[n>>0]|0)<3)n=n+20+((c[29152+(m<<3)+(((t|0)==0&1)<<2)>>2]|0)*12|0)+8|0;else n=n+16|0;n=c[n>>2]|0;c[j>>2]=n;if(O){n=1;break a}else{s=1;o=1;t=n}}else{s=1;o=0;t=0}}else{s=0;o=0;t=0}while(0);p=c[i+8>>2]|0;q=p+k|0;if((q|0)<0){r=2;n=s;u=j;v=t;A=36;break a}r=c[i+12>>2]|0;n=r+l|0;if(!((q|0)<(c[C>>2]|0)?(n|0)>=(c[D>>2]|0):0)){r=2;n=s;u=j;v=t;A=36;break a}if((n|0)>=(c[E>>2]|0)){r=2;n=s;u=j;v=t;A=36;break a}n=c[(c[F>>2]|0)+(((c[G>>2]|0)*p|0)+r<<2)>>2]|0;if((a[n+8>>0]|0)==h<<24>>24){p=(d[n>>0]|0)<3;if(!o){if(p)n=n+20+((c[29152+(m<<3)+(((r|0)==0&1)<<2)>>2]|0)*12|0)+4|0;else n=n+12|0;n=c[n>>2]|0;c[j>>2]=n;if(O){n=1;break a}else{r=2;q=1;o=1;u=j;A=50;break a}}if(p)o=n+20+((c[29152+(m<<3)+(((r|0)==0&1)<<2)>>2]|0)*12|0)+4|0;else o=n+12|0;if((c[o>>2]|0)==(t|0)){r=2;n=1;o=1;u=j;v=t;A=36;break a}if((d[n>>0]|0)>=3){A=26;break}n=n+20+((c[29152+(m<<3)+(((r|0)==0&1)<<2)>>2]|0)*12|0)+4|0;A=27;break}if((a[n+9>>0]|0)!=h<<24>>24){r=2;n=1;u=j;v=t;A=36;break a}p=(d[n>>0]|0)<3;if(!o){if(p)n=n+20+((c[29152+(m<<3)+(((r|0)==0&1)<<2)>>2]|0)*12|0)+8|0;else n=n+16|0;n=c[n>>2]|0;c[j>>2]=n;if(O){n=1;break a}else{r=2;q=1;o=1;u=j;A=50;break a}}if(p)o=n+20+((c[29152+(m<<3)+(((r|0)==0&1)<<2)>>2]|0)*12|0)+8|0;else o=n+16|0;if((c[o>>2]|0)==(t|0)){r=2;n=1;o=1;u=j;v=t;A=36;break a}if((d[n>>0]|0)<3){n=n+20+((c[29152+(m<<3)+(((r|0)==0&1)<<2)>>2]|0)*12|0)+8|0;A=33}else A=32}else{do if((!p?(r=c[i+4>>2]|0,s=r+l|0,(o|0)<(c[C>>2]|0)?(s|0)>=(c[D>>2]|0):0):0)?(s|0)<(c[E>>2]|0):0){n=c[(c[F>>2]|0)+(((c[G>>2]|0)*n|0)+r<<2)>>2]|0;if((a[n+8>>0]|0)==h<<24>>24){n=c[n+12>>2]|0;c[j>>2]=n;if(O){n=1;break a}else{s=1;o=1;t=n;break}}if((a[n+9>>0]|0)==h<<24>>24){n=c[n+16>>2]|0;c[j>>2]=n;if(O){n=1;break a}else{s=1;o=1;t=n}}else{s=1;o=0;t=0}}else{s=0;o=0;t=0}while(0);n=c[i+8>>2]|0;p=n+k|0;if((p|0)<0){r=2;n=s;u=j;v=t;A=36;break a}q=c[i+12>>2]|0;r=q+l|0;if(!((p|0)<(c[C>>2]|0)?(r|0)>=(c[D>>2]|0):0)){r=2;n=s;u=j;v=t;A=36;break a}if((r|0)>=(c[E>>2]|0)){r=2;n=s;u=j;v=t;A=36;break a}n=c[(c[F>>2]|0)+(((c[G>>2]|0)*n|0)+q<<2)>>2]|0;if((a[n+8>>0]|0)==h<<24>>24){p=c[n+12>>2]|0;if(!o){c[j>>2]=p;if(O){n=1;break a}else{r=2;q=1;o=1;u=j;n=p;A=50;break a}}else if((p|0)==(t|0)){r=2;n=1;o=1;u=j;v=t;A=36;break a}else{A=26;break}}if((a[n+9>>0]|0)!=h<<24>>24){r=2;n=1;u=j;v=t;A=36;break a}p=c[n+16>>2]|0;if(!o){c[j>>2]=p;if(O){n=1;break a}else{r=2;q=1;o=1;u=j;n=p;A=50;break a}}else if((p|0)==(t|0)){r=2;n=1;o=1;u=j;v=t;A=36;break a}else{A=32;break}}while(0);if((A|0)==26){n=n+12|0;A=27}else if((A|0)==32){n=n+16|0;A=33}if((A|0)==27){c[j+4>>2]=c[n>>2];n=2;break}else if((A|0)==33){c[j+4>>2]=c[n>>2];n=2;break}}else{r=0;n=0;o=0;u=j;v=0;A=36}while(0);b:do if((A|0)==36)if(O){t=(o|0)==0;m=(o|0)==0;s=r;p=v;c:while(1){q=c[i+(s<<3)>>2]|0;r=q+k|0;do if(((r|0)>=0?(w=c[i+(s<<3)+4>>2]|0,x=w+l|0,(r|0)<(c[C>>2]|0)?(x|0)>=(c[D>>2]|0):0):0)?(x|0)<(c[E>>2]|0):0){n=c[(c[F>>2]|0)+(((c[G>>2]|0)*q|0)+w<<2)>>2]|0;if((a[n+8>>0]|0)==h<<24>>24){n=c[n+12>>2]|0;if(t){A=48;break c}if((n|0)==(v|0)){n=1;p=v;break}else{A=57;break b}}if((a[n+9>>0]|0)==h<<24>>24){n=c[n+16>>2]|0;if(m){A=45;break c}if((n|0)!=(p|0)){A=62;break b}else n=1}else n=1}while(0);s=s+1|0;if(s>>>0>=8){p=n;w=u;n=v;A=65;break b}}if((A|0)==45){c[u>>2]=n;n=1;break}else if((A|0)==48){c[u>>2]=n;n=1;break}}else{q=n;n=v;A=50}while(0);d:do if((A|0)==50){s=n;t=n;while(1){n=c[i+(r<<3)>>2]|0;p=n+k|0;do if(((p|0)>=0?(y=c[i+(r<<3)+4>>2]|0,z=y+l|0,(p|0)<(c[C>>2]|0)?(z|0)>=(c[D>>2]|0):0):0)?(z|0)<(c[E>>2]|0):0){n=c[(c[F>>2]|0)+(((c[G>>2]|0)*n|0)+y<<2)>>2]|0;if((a[n+8>>0]|0)==h<<24>>24){n=c[n+12>>2]|0;if(o)if((n|0)==(s|0)){p=1;m=t;n=s;break}else{A=57;break d}else{c[u>>2]=n;o=1;p=1;m=n;break}}if((a[n+9>>0]|0)==h<<24>>24){n=c[n+16>>2]|0;if(o)if((n|0)==(t|0)){p=1;m=t;n=t;break}else{A=62;break d}else{c[u>>2]=n;o=1;p=1;m=n;break}}else{p=1;m=t;n=s}}else{p=q;m=t;n=s}while(0);r=r+1|0;if(r>>>0>=8){w=u;n=m;A=65;break}else{q=p;s=n;t=m}}}while(0);e:do if((A|0)==57){c[j+(o<<2)>>2]=n;A=107}else if((A|0)==62){c[j+(o<<2)>>2]=n;A=107}else if((A|0)==65){u=(L|0)!=0;do if(u){if((a[L+8>>0]|0)==h<<24>>24){q=c[L>>2]|0;if(!o){c[w>>2]=q;if(O){n=1;break e}else{o=1;break}}if((q|0)==(n|0)){q=n;break}c[j+(o<<2)>>2]=q;A=107;break e}if((a[L+9>>0]|0)==h<<24>>24){q=c[L+4>>2]|0;if(!o){c[w>>2]=q;if(O){n=1;break e}else{o=1;break}}if((q|0)!=(n|0)){c[j+(o<<2)>>2]=q;A=107;break e}else q=n}else q=n}else q=n;while(0);f:do if(p){m=e+3924+(h<<24>>24<<2)|0;t=0;s=q;r=q;g:while(1){n=c[i+(t<<3)>>2]|0;p=n+k|0;do if((((p|0)>=0?(H=c[i+(t<<3)+4>>2]|0,I=H+l|0,(p|0)<(c[C>>2]|0)?(I|0)>=(c[D>>2]|0):0):0)?(I|0)<(c[E>>2]|0):0)?(J=c[(c[F>>2]|0)+(((c[G>>2]|0)*n|0)+H<<2)>>2]|0,B=a[J+8>>0]|0,B<<24>>24>=1):0){do if(B<<24>>24!=h<<24>>24){y=J+12|0;n=b[y>>1]|0;y=b[y+2>>1]|0;z=(c[e+3924+(B<<24>>24<<2)>>2]|0)==(c[m>>2]|0);n=((z?y:0-(y&65535)&65535)&65535)<<16|(z?n:0-(n&65535)&65535)&65535;if(!o){c[w>>2]=n;if(O){n=1;break e}else{o=1;r=n;q=n;break}}else if((n|0)==(s|0)){r=s;q=s;break}else{A=85;break g}}else q=s;while(0);n=a[J+9>>0]|0;if(!(n<<24>>24<1?1:n<<24>>24==h<<24>>24)?(K=c[J+16>>2]|0,(K|0)!=(c[J+12>>2]|0)):0){z=b[J+16+2>>1]|0;n=(c[e+3924+(n<<24>>24<<2)>>2]|0)==(c[m>>2]|0);n=((n?z:0-(z&65535)&65535)&65535)<<16|(n?K:0-K|0)&65535;if(!o){c[w>>2]=n;if(O){n=1;break e}else{o=1;q=n;break}}else if((n|0)==(r|0)){n=r;break}else{A=91;break g}}else n=r}else{q=s;n=r}while(0);t=t+1|0;if(t>>>0>=8)break f;else{s=q;r=n}}if((A|0)==85){c[j+(o<<2)>>2]=n;A=107;break e}else if((A|0)==91){c[j+(o<<2)>>2]=n;A=107;break e}}while(0);do if(u){n=a[L+8>>0]|0;r=h<<24>>24;do if(n<<24>>24>0?n<<24>>24!=h<<24>>24:0){l=b[L>>1]|0;p=b[L+2>>1]|0;n=(c[e+3924+(n<<24>>24<<2)>>2]|0)==(c[e+3924+(r<<2)>>2]|0);p=n?p:0-(p&65535)&65535;n=n?l:0-(l&65535)&65535;if(!o){b[j>>1]=n;b[j+2>>1]=p;if(O){n=1;break e}else{o=1;break}}if(((p&65535)<<16|n&65535|0)!=(q|0)){A=j+(o<<2)|0;b[A>>1]=n;b[A+2>>1]=p;A=107;break e}}while(0);l=a[L+9>>0]|0;n=l<<24>>24;if(!(l<<24>>24<1?1:l<<24>>24==h<<24>>24)?(M=L+4|0,N=c[M>>2]|0,(N|0)!=(c[L>>2]|0)):0){q=b[M+2>>1]|0;p=(c[e+3924+(n<<2)>>2]|0)==(c[e+3924+(r<<2)>>2]|0);q=p?q:0-(q&65535)&65535;p=p?N:0-N|0;n=p&65535;if(!o){b[j>>1]=n;b[j+2>>1]=q;if(O){n=1;break e}else break}if(((q&65535)<<16|p&65535|0)!=(c[w>>2]|0)){A=j+(o<<2)|0;b[A>>1]=n;b[A+2>>1]=q;A=107;break e}}}while(0);n=g<<24>>24==11?2:1}while(0);if((A|0)==107){n=o+1|0;if((o|0)<=-1){j=n;return j|0}}q=(c[f+316>>2]|0)+-128|0;r=(c[f+320>>2]|0)+128|0;s=(c[f+324>>2]|0)+-128|0;p=(c[f+328>>2]|0)+128|0;o=0;do{f=j+(o<<2)|0;O=f+2|0;g=b[O>>1]|0;b[O>>1]=(q|0)>(g|0)?q:(r|0)<(g|0)?r:g;O=b[f>>1]|0;b[f>>1]=(s|0)>(O|0)?s:(p|0)<(O|0)?p:O;o=o+1|0}while((o|0)!=(n|0));return n|0}function hd(f,g,h,i,j,k,l,m,n){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0;B=L;L=L+16|0;A=B;switch(h<<24>>24){case 13:{z=c[g+268>>2]|0;z=(z|0)==0?0:z+12800|0;if((l|0)<0){A=1;L=B;return A|0}w=f+4104|0;x=n+4|0;y=n+8|0;u=(m|0)==0;v=A+2|0;t=0;g=1;while(1){s=i+(t<<2)|0;r=j+(t<<2)|0;q=c[w>>2]|0;h=0;f=c[x>>2]|0;m=c[y>>2]|0;do{p=h<<24>>24;h=d[(p>>1)+(q+1970)>>0]|0;h=(256-h+(f*h|0)|0)>>>8;if((m|0)<0){zb(n);m=c[y>>2]|0}k=c[n>>2]|0;f=h<<24;if(k>>>0<f>>>0)o=0;else{o=1;k=k-f|0;h=(c[x>>2]|0)-h|0}C=d[1664+h>>0]|0;f=h<<C;m=m-C|0;c[n>>2]=k<<C;c[y>>2]=m;c[x>>2]=f;h=a[31718+(o+p)>>0]|0}while(h<<24>>24>0);h=0-(h<<24>>24)|0;if(!u?(C=b[r>>1]|0,(((C|0)>-1?C:0-C|0)|0)<64):0){k=b[r+2>>1]|0;k=(((k|0)>-1?k:0-k|0)|0)<64&1}else k=0;c[A>>2]=0;if((h|1|0)==3)b[A>>1]=id(n,q+1973|0,k)|0;if((h|2|0)==3)b[v>>1]=id(n,q+2006|0,k)|0;_b(A,z);k=(e[A>>1]|0)+(e[r>>1]|0)&65535;b[s>>1]=k;h=(e[v>>1]|0)+(e[r+2>>1]|0)&65535;b[s+2>>1]=h;if(!g)g=0;else g=(k+16383&65535)<32766&(h+16383&65535)<32766;g=g&1;if((t|0)==(l|0))break;else t=t+1|0}L=B;return g|0}case 10:case 11:{n=k;A=c[n+4>>2]|0;C=i;c[C>>2]=c[n>>2];c[C+4>>2]=A;C=1;L=B;return C|0}case 12:{C=i;c[C>>2]=0;c[C+4>>2]=0;C=1;L=B;return C|0}default:{C=0;L=B;return C|0}}return 0}function id(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;h=d[e>>0]|0;s=b+4|0;h=(256-h+((c[s>>2]|0)*h|0)|0)>>>8;r=b+8|0;g=c[r>>2]|0;if((g|0)<0){zb(b);g=c[r>>2]|0}i=c[b>>2]|0;j=h<<24;if(i>>>0<j>>>0)q=0;else{q=1;i=i-j|0;h=(c[s>>2]|0)-h|0}k=d[1664+h>>0]|0;p=h<<k;h=i<<k;k=g-k|0;c[b>>2]=h;c[r>>2]=k;c[s>>2]=p;g=0;i=p;do{l=g<<24>>24;g=d[(l>>1)+(e+1)>>0]|0;g=(256-g+(i*g|0)|0)>>>8;if((k|0)<0){zb(b);h=c[b>>2]|0;k=c[r>>2]|0}i=g<<24;if(h>>>0<i>>>0)j=0;else{j=1;h=h-i|0;g=(c[s>>2]|0)-g|0}p=d[1664+g>>0]|0;i=g<<p;h=h<<p;k=k-p|0;c[b>>2]=h;c[r>>2]=k;c[s>>2]=i;g=a[8144+(j+l)>>0]|0}while(g<<24>>24>0);n=g<<24>>24;o=0-n|0;p=g<<24>>24==0;if(p){g=d[e+11>>0]|0;g=(256-g+(i*g|0)|0)>>>8;if((k|0)<0){zb(b);h=c[b>>2]|0;k=c[r>>2]|0}i=g<<24;if(h>>>0<i>>>0)j=0;else{j=1;h=h-i|0;g=(c[s>>2]|0)-g|0}o=d[1664+g>>0]|0;g=g<<o;h=h<<o;k=k-o|0;c[b>>2]=h;c[r>>2]=k;c[s>>2]=g;o=1;m=j}else{l=0;m=0;g=i;do{j=d[e+12+l>>0]|0;g=(256-j+(g*j|0)|0)>>>8;if((k|0)<0){zb(b);k=c[r>>2]|0;h=c[b>>2]|0}i=g<<24;if(h>>>0<i>>>0)j=0;else{j=1;h=h-i|0;g=(c[s>>2]|0)-g|0}i=d[1664+g>>0]|0;g=g<<i;h=h<<i;k=k-i|0;c[b>>2]=h;c[r>>2]=k;c[s>>2]=g;m=j<<l|m;l=l+1|0}while((l|0)<(o|0));o=(2<<2-n)+1|0}n=p?e+22+(m*3|0)|0:e+28|0;i=0;do{l=i<<24>>24;j=d[n+(l>>1)>>0]|0;g=(256-j+(g*j|0)|0)>>>8;if((k|0)<0){zb(b);h=c[b>>2]|0;k=c[r>>2]|0}i=g<<24;if(h>>>0<i>>>0)j=0;else{j=1;h=h-i|0;g=(c[s>>2]|0)-g|0}i=d[1664+g>>0]|0;g=g<<i;h=h<<i;k=k-i|0;c[b>>2]=h;c[r>>2]=k;c[s>>2]=g;i=a[31726+(j+l)>>0]|0}while(i<<24>>24>0);l=i<<24>>24;if(!f){r=1;s=m<<3;b=l<<1;b=0-b|0;b=s|b;r=b|r;r=o+r|0;b=(q|0)==0;s=0-r|0;s=b?r:s;return s|0}f=d[(p?e+31|0:e+32|0)>>0]|0;g=(256-f+(g*f|0)|0)>>>8;if((k|0)<0){zb(b);h=c[b>>2]|0;k=c[r>>2]|0}i=g<<24;if(h>>>0<i>>>0)j=0;else{j=1;h=h-i|0;g=(c[s>>2]|0)-g|0}f=d[1664+g>>0]|0;c[b>>2]=h<<f;c[r>>2]=k-f;c[s>>2]=g<<f;r=j;s=m<<3;b=l<<1;b=0-b|0;b=s|b;r=b|r;r=o+r|0;b=(q|0)==0;s=0-r|0;s=b?r:s;return s|0}function jd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;k=d[f>>0]|0;j=d[f+1>>0]|0;i=d[f+2>>0]|0;e=a[f+3>>0]|0;g=e&255;h=j+1|0;a[b>>0]=(h+k|0)>>>1;h=(h+i|0)>>>1&255;a[b+c>>0]=h;a[b+2>>0]=h;h=(i+1+g|0)>>>1&255;f=c<<1;a[b+f>>0]=h;a[b+(c+2)>>0]=h;h=i+2|0;a[b+1>>0]=(h+k+(j<<1)|0)>>>2;i=(g+2+j+(i<<1)|0)>>>2&255;a[b+(c+1)>>0]=i;a[b+3>>0]=i;g=(h+g+(g<<1)|0)>>>2&255;a[b+(f|1)>>0]=g;a[b+(c+3)>>0]=g;c=c*3|0;a[b+(c+3)>>0]=e;a[b+(c+2)>>0]=e;a[b+(c+1)>>0]=e;a[b+c>>0]=e;a[b+(f+2)>>0]=e;a[b+(f+3)>>0]=e;return}function kd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;i=d[e>>0]|0;f=d[e+1>>0]|0;l=d[e+2>>0]|0;j=d[e+3>>0]|0;h=d[e+4>>0]|0;g=d[e+5>>0]|0;e=d[e+6>>0]|0;m=f+1|0;a[b>>0]=(m+i|0)>>>1;m=(m+l|0)>>>1&255;k=c<<1;a[b+k>>0]=m;a[b+1>>0]=m;m=(l+1+j|0)>>>1&255;a[b+(k|1)>>0]=m;a[b+2>>0]=m;m=(j+1+h|0)>>>1&255;a[b+(k+2)>>0]=m;a[b+3>>0]=m;a[b+(k+3)>>0]=(h+1+g|0)>>>1;k=l+2|0;a[b+c>>0]=(k+i+(f<<1)|0)>>>2;i=j+2|0;l=(i+f+(l<<1)|0)>>>2&255;f=c*3|0;a[b+f>>0]=l;a[b+(c+1)>>0]=l;j=(k+(j<<1)+h|0)>>>2&255;a[b+(f+1)>>0]=j;a[b+(c+2)>>0]=j;i=(i+(h<<1)+g|0)>>>2&255;a[b+(f+2)>>0]=i;a[b+(c+3)>>0]=i;a[b+(f+3)>>0]=(h+2+(g<<1)+e|0)>>>2;return}function ld(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;n=d[e+1>>0]|0;m=d[e+2>>0]|0;f=d[e+3>>0]|0;j=d[e+4>>0]|0;i=d[e+5>>0]|0;h=d[e+6>>0]|0;g=a[e+7>>0]|0;l=m+2|0;a[b>>0]=(l+(d[e>>0]|0)+(n<<1)|0)>>>2;k=f+2|0;e=(k+n+(m<<1)|0)>>>2&255;a[b+c>>0]=e;a[b+1>>0]=e;f=(l+(f<<1)+j|0)>>>2&255;e=c<<1;a[b+e>>0]=f;a[b+(c+1)>>0]=f;a[b+2>>0]=f;k=(k+(j<<1)+i|0)>>>2&255;f=c*3|0;a[b+f>>0]=k;a[b+(e|1)>>0]=k;a[b+(c+2)>>0]=k;a[b+3>>0]=k;j=(j+2+(i<<1)+h|0)>>>2&255;a[b+(f+1)>>0]=j;a[b+(e+2)>>0]=j;a[b+(c+3)>>0]=j;c=(i+2+(h<<1)+(g&255)|0)>>>2&255;a[b+(f+2)>>0]=c;a[b+(e+3)>>0]=c;a[b+(f+3)>>0]=g;return}function md(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;n=d[f>>0]|0;o=d[f+1>>0]|0;p=d[f+2>>0]|0;k=d[e+-1>>0]|0;j=d[e>>0]|0;h=d[e+1>>0]|0;g=d[e+2>>0]|0;f=d[e+3>>0]|0;l=j+1|0;i=(l+k|0)>>>1&255;m=c<<1;a[b+(m|1)>>0]=i;a[b>>0]=i;l=(l+h|0)>>>1&255;a[b+(m+2)>>0]=l;a[b+1>>0]=l;l=(h+1+g|0)>>>1&255;a[b+(m+3)>>0]=l;a[b+2>>0]=l;a[b+3>>0]=(g+1+f|0)>>>1;l=n+2|0;i=c*3|0;a[b+i>>0]=(l+p+(o<<1)|0)>>>2;e=k+2|0;a[b+m>>0]=(e+o+(n<<1)|0)>>>2;k=(l+(k<<1)+j|0)>>>2&255;a[b+(i+1)>>0]=k;a[b+c>>0]=k;e=(e+(j<<1)+h|0)>>>2&255;a[b+(i+2)>>0]=e;a[b+(c+1)>>0]=e;e=(j+2+(h<<1)+g|0)>>>2&255;a[b+(i+3)>>0]=e;a[b+(c+2)>>0]=e;a[b+(c+3)>>0]=(h+2+(g<<1)+f|0)>>>2;return}function nd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;m=d[f>>0]|0;p=d[f+1>>0]|0;o=d[f+2>>0]|0;j=d[e+-1>>0]|0;i=d[e>>0]|0;h=d[e+1>>0]|0;g=d[e+2>>0]|0;e=d[e+3>>0]|0;n=p+2|0;k=c*3|0;a[b+k>>0]=(n+(o<<1)+(d[f+3>>0]|0)|0)>>>2;l=m+2|0;o=(l+(p<<1)+o|0)>>>2&255;f=c<<1;a[b+f>>0]=o;a[b+(k+1)>>0]=o;m=(n+(m<<1)+j|0)>>>2&255;a[b+c>>0]=m;a[b+(f|1)>>0]=m;a[b+(k+2)>>0]=m;l=(l+i+(j<<1)|0)>>>2&255;a[b>>0]=l;a[b+(c+1)>>0]=l;a[b+(f+2)>>0]=l;a[b+(k+3)>>0]=l;j=(j+2+h+(i<<1)|0)>>>2&255;a[b+1>>0]=j;a[b+(c+2)>>0]=j;a[b+(f+3)>>0]=j;f=(i+2+g+(h<<1)|0)>>>2&255;a[b+2>>0]=f;a[b+(c+3)>>0]=f;a[b+3>>0]=(h+2+e+(g<<1)|0)>>>2;return}function od(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;m=d[f>>0]|0;j=d[f+1>>0]|0;g=d[f+2>>0]|0;h=d[f+3>>0]|0;l=d[e+-1>>0]|0;n=d[e>>0]|0;k=d[e+1>>0]|0;e=d[e+2>>0]|0;o=m+1|0;i=(o+l|0)>>>1&255;a[b+(c+2)>>0]=i;a[b>>0]=i;o=(o+j|0)>>>1&255;i=c<<1;a[b+(i+2)>>0]=o;a[b+c>>0]=o;o=(j+1+g|0)>>>1&255;f=c*3|0;a[b+(f+2)>>0]=o;a[b+i>>0]=o;a[b+f>>0]=(g+1+h|0)>>>1;a[b+3>>0]=(n+2+(k<<1)+e|0)>>>2;a[b+2>>0]=(l+2+(n<<1)+k|0)>>>2;k=m+2|0;e=(k+(l<<1)+n|0)>>>2&255;a[b+(c+3)>>0]=e;a[b+1>>0]=e;e=j+2|0;l=(e+(m<<1)+l|0)>>>2&255;a[b+(i+3)>>0]=l;a[b+(c+1)>>0]=l;c=(k+g+(j<<1)|0)>>>2&255;a[b+(f+3)>>0]=c;a[b+(i|1)>>0]=c;a[b+(f+1)>>0]=(e+h+(g<<1)|0)>>>2;return}function pd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;z=f+1|0;a[b>>0]=((d[f>>0]|0)+1+(d[z>>0]|0)|0)>>>1;y=f+2|0;k=b+c|0;a[k>>0]=((d[z>>0]|0)+1+(d[y>>0]|0)|0)>>>1;v=f+3|0;l=c<<1;i=b+l|0;a[i>>0]=((d[y>>0]|0)+1+(d[v>>0]|0)|0)>>>1;t=f+4|0;m=c*3|0;h=b+m|0;a[h>>0]=((d[v>>0]|0)+1+(d[t>>0]|0)|0)>>>1;p=f+5|0;o=c<<2;g=b+o|0;a[g>>0]=((d[t>>0]|0)+1+(d[p>>0]|0)|0)>>>1;r=f+6|0;q=c*5|0;e=b+q|0;a[e>>0]=((d[p>>0]|0)+1+(d[r>>0]|0)|0)>>>1;w=f+7|0;s=c*6|0;u=b+s|0;a[u>>0]=((d[r>>0]|0)+1+(d[w>>0]|0)|0)>>>1;x=c*7|0;n=b+x|0;a[n>>0]=a[w>>0]|0;j=b+1|0;a[j>>0]=((d[f>>0]|0)+2+((d[z>>0]|0)<<1)+(d[y>>0]|0)|0)>>>2;a[j+c>>0]=((d[z>>0]|0)+2+((d[y>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;a[j+l>>0]=((d[y>>0]|0)+2+((d[v>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;a[j+m>>0]=((d[v>>0]|0)+2+((d[t>>0]|0)<<1)+(d[p>>0]|0)|0)>>>2;a[j+o>>0]=((d[t>>0]|0)+2+((d[p>>0]|0)<<1)+(d[r>>0]|0)|0)>>>2;a[j+q>>0]=((d[p>>0]|0)+2+((d[r>>0]|0)<<1)+(d[w>>0]|0)|0)>>>2;p=d[w>>0]|0;a[j+s>>0]=((d[r>>0]|0)+2+p+(p<<1)|0)>>>2;a[j+x>>0]=a[w>>0]|0;j=b+2|0;a[j+x>>0]=a[w>>0]|0;p=x+1|0;a[j+p>>0]=a[w>>0]|0;r=x+2|0;a[j+r>>0]=a[w>>0]|0;t=x+3|0;a[j+t>>0]=a[w>>0]|0;v=x+4|0;a[j+v>>0]=a[w>>0]|0;f=x+5|0;a[j+f>>0]=a[w>>0]|0;a[j+s>>0]=a[n>>0]|0;n=s|1;a[j+n>>0]=a[b+p>>0]|0;p=s+2|0;a[j+p>>0]=a[b+r>>0]|0;r=s+3|0;a[j+r>>0]=a[b+t>>0]|0;t=s+4|0;a[j+t>>0]=a[b+v>>0]|0;s=s+5|0;a[j+s>>0]=a[b+f>>0]|0;a[j+q>>0]=a[u>>0]|0;f=q+1|0;a[j+f>>0]=a[b+n>>0]|0;n=q+2|0;a[j+n>>0]=a[b+p>>0]|0;p=q+3|0;a[j+p>>0]=a[b+r>>0]|0;r=q+4|0;a[j+r>>0]=a[b+t>>0]|0;q=q+5|0;a[j+q>>0]=a[b+s>>0]|0;a[j+o>>0]=a[e>>0]|0;e=o|1;a[j+e>>0]=a[b+f>>0]|0;f=o|2;a[j+f>>0]=a[b+n>>0]|0;n=o|3;a[j+n>>0]=a[b+p>>0]|0;p=o+4|0;a[j+p>>0]=a[b+r>>0]|0;o=o+5|0;a[j+o>>0]=a[b+q>>0]|0;a[j+m>>0]=a[g>>0]|0;g=m+1|0;a[j+g>>0]=a[b+e>>0]|0;e=m+2|0;a[j+e>>0]=a[b+f>>0]|0;f=m+3|0;a[j+f>>0]=a[b+n>>0]|0;n=m+4|0;a[j+n>>0]=a[b+p>>0]|0;m=m+5|0;a[j+m>>0]=a[b+o>>0]|0;a[j+l>>0]=a[h>>0]|0;h=l|1;a[j+h>>0]=a[b+g>>0]|0;g=l+2|0;a[j+g>>0]=a[b+e>>0]|0;e=l+3|0;a[j+e>>0]=a[b+f>>0]|0;f=l+4|0;a[j+f>>0]=a[b+n>>0]|0;l=l+5|0;a[j+l>>0]=a[b+m>>0]|0;a[j+c>>0]=a[i>>0]|0;i=c+1|0;a[j+i>>0]=a[b+h>>0]|0;h=c+2|0;a[j+h>>0]=a[b+g>>0]|0;g=c+3|0;a[j+g>>0]=a[b+e>>0]|0;e=c+4|0;a[j+e>>0]=a[b+f>>0]|0;f=c+5|0;a[j+f>>0]=a[b+l>>0]|0;a[j>>0]=a[k>>0]|0;a[b+3>>0]=a[b+i>>0]|0;a[b+4>>0]=a[b+h>>0]|0;a[b+5>>0]=a[b+g>>0]|0;a[b+6>>0]=a[b+e>>0]|0;a[b+7>>0]=a[b+f>>0]|0;return}function qd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;I=f+1|0;a[b>>0]=((d[f>>0]|0)+1+(d[I>>0]|0)|0)>>>1;H=f+2|0;a[b+c>>0]=((d[I>>0]|0)+1+(d[H>>0]|0)|0)>>>1;F=f+3|0;G=c<<1;a[b+G>>0]=((d[H>>0]|0)+1+(d[F>>0]|0)|0)>>>1;D=f+4|0;E=c*3|0;a[b+E>>0]=((d[F>>0]|0)+1+(d[D>>0]|0)|0)>>>1;B=f+5|0;C=c<<2;a[b+C>>0]=((d[D>>0]|0)+1+(d[B>>0]|0)|0)>>>1;z=f+6|0;A=c*5|0;a[b+A>>0]=((d[B>>0]|0)+1+(d[z>>0]|0)|0)>>>1;x=f+7|0;y=c*6|0;a[b+y>>0]=((d[z>>0]|0)+1+(d[x>>0]|0)|0)>>>1;v=f+8|0;w=c*7|0;a[b+w>>0]=((d[x>>0]|0)+1+(d[v>>0]|0)|0)>>>1;t=f+9|0;u=c<<3;a[b+u>>0]=((d[v>>0]|0)+1+(d[t>>0]|0)|0)>>>1;r=f+10|0;s=c*9|0;a[b+s>>0]=((d[t>>0]|0)+1+(d[r>>0]|0)|0)>>>1;p=f+11|0;q=c*10|0;a[b+q>>0]=((d[r>>0]|0)+1+(d[p>>0]|0)|0)>>>1;n=f+12|0;o=c*11|0;a[b+o>>0]=((d[p>>0]|0)+1+(d[n>>0]|0)|0)>>>1;l=f+13|0;m=c*12|0;a[b+m>>0]=((d[n>>0]|0)+1+(d[l>>0]|0)|0)>>>1;j=f+14|0;k=c*13|0;a[b+k>>0]=((d[l>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+15|0;i=c*14|0;a[b+i>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;e=c*15|0;a[b+e>>0]=a[g>>0]|0;h=b+1|0;a[h>>0]=((d[f>>0]|0)+2+((d[I>>0]|0)<<1)+(d[H>>0]|0)|0)>>>2;a[h+c>>0]=((d[I>>0]|0)+2+((d[H>>0]|0)<<1)+(d[F>>0]|0)|0)>>>2;a[h+G>>0]=((d[H>>0]|0)+2+((d[F>>0]|0)<<1)+(d[D>>0]|0)|0)>>>2;a[h+E>>0]=((d[F>>0]|0)+2+((d[D>>0]|0)<<1)+(d[B>>0]|0)|0)>>>2;a[h+C>>0]=((d[D>>0]|0)+2+((d[B>>0]|0)<<1)+(d[z>>0]|0)|0)>>>2;a[h+A>>0]=((d[B>>0]|0)+2+((d[z>>0]|0)<<1)+(d[x>>0]|0)|0)>>>2;a[h+y>>0]=((d[z>>0]|0)+2+((d[x>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;a[h+w>>0]=((d[x>>0]|0)+2+((d[v>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;a[h+u>>0]=((d[v>>0]|0)+2+((d[t>>0]|0)<<1)+(d[r>>0]|0)|0)>>>2;a[h+s>>0]=((d[t>>0]|0)+2+((d[r>>0]|0)<<1)+(d[p>>0]|0)|0)>>>2;a[h+q>>0]=((d[r>>0]|0)+2+((d[p>>0]|0)<<1)+(d[n>>0]|0)|0)>>>2;a[h+o>>0]=((d[p>>0]|0)+2+((d[n>>0]|0)<<1)+(d[l>>0]|0)|0)>>>2;a[h+m>>0]=((d[n>>0]|0)+2+((d[l>>0]|0)<<1)+(d[j>>0]|0)|0)>>>2;a[h+k>>0]=((d[l>>0]|0)+2+((d[j>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;f=d[g>>0]|0;a[h+i>>0]=((d[j>>0]|0)+2+f+(f<<1)|0)>>>2;a[h+e>>0]=a[g>>0]|0;f=b+2|0;a[f+e>>0]=a[g>>0]|0;a[f+(e+1)>>0]=a[g>>0]|0;a[f+(e+2)>>0]=a[g>>0]|0;a[f+(e+3)>>0]=a[g>>0]|0;a[f+(e+4)>>0]=a[g>>0]|0;a[f+(e+5)>>0]=a[g>>0]|0;a[f+(e+6)>>0]=a[g>>0]|0;a[f+(e+7)>>0]=a[g>>0]|0;a[f+(e+8)>>0]=a[g>>0]|0;a[f+(e+9)>>0]=a[g>>0]|0;a[f+(e+10)>>0]=a[g>>0]|0;a[f+(e+11)>>0]=a[g>>0]|0;a[f+(e+12)>>0]=a[g>>0]|0;a[f+(e+13)>>0]=a[g>>0]|0;e=14;g=0;while(1){H=(e+1|0)*c|0;I=e*c|0;a[f+I>>0]=a[b+H>>0]|0;a[f+(I+1)>>0]=a[b+(H+1)>>0]|0;a[f+(I+2)>>0]=a[b+(H+2)>>0]|0;a[f+(I+3)>>0]=a[b+(H+3)>>0]|0;a[f+(I+4)>>0]=a[b+(H+4)>>0]|0;a[f+(I+5)>>0]=a[b+(H+5)>>0]|0;a[f+(I+6)>>0]=a[b+(H+6)>>0]|0;a[f+(I+7)>>0]=a[b+(H+7)>>0]|0;a[f+(I+8)>>0]=a[b+(H+8)>>0]|0;a[f+(I+9)>>0]=a[b+(H+9)>>0]|0;a[f+(I+10)>>0]=a[b+(H+10)>>0]|0;a[f+(I+11)>>0]=a[b+(H+11)>>0]|0;a[f+(I+12)>>0]=a[b+(H+12)>>0]|0;a[f+(I+13)>>0]=a[b+(H+13)>>0]|0;g=g+1|0;if((g|0)==15)break;else e=e+-1|0}return}function rd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;j=f+1|0;a[b>>0]=((d[f>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+2|0;a[b+c>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+3|0;a[b+(c<<1)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+4|0;a[b+(c*3|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+5|0;a[b+(c<<2)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+6|0;a[b+(c*5|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+7|0;a[b+(c*6|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+8|0;a[b+(c*7|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+9|0;a[b+(c<<3)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+10|0;a[b+(c*9|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+11|0;a[b+(c*10|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+12|0;a[b+(c*11|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+13|0;a[b+(c*12|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+14|0;a[b+(c*13|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+15|0;a[b+(c*14|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+16|0;a[b+(c*15|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+17|0;a[b+(c<<4)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+18|0;a[b+(c*17|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+19|0;a[b+(c*18|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+20|0;a[b+(c*19|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+21|0;a[b+(c*20|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+22|0;a[b+(c*21|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+23|0;a[b+(c*22|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+24|0;a[b+(c*23|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+25|0;a[b+(c*24|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+26|0;a[b+(c*25|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+27|0;a[b+(c*26|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+28|0;a[b+(c*27|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+29|0;a[b+(c*28|0)>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;g=f+30|0;a[b+(c*29|0)>>0]=((d[j>>0]|0)+1+(d[g>>0]|0)|0)>>>1;j=f+31|0;h=c*30|0;a[b+h>>0]=((d[g>>0]|0)+1+(d[j>>0]|0)|0)>>>1;k=c*31|0;a[b+k>>0]=a[j>>0]|0;i=b+1|0;e=0;do{l=e;e=e+1|0;a[i+(l*c|0)>>0]=((d[f+l>>0]|0)+2+((d[f+e>>0]|0)<<1)+(d[f+(l+2)>>0]|0)|0)>>>2}while((e|0)!=30);e=d[j>>0]|0;a[i+h>>0]=((d[g>>0]|0)+2+e+(e<<1)|0)>>>2;a[i+k>>0]=a[j>>0]|0;h=b+2|0;a[h+k>>0]=a[j>>0]|0;a[h+(k+1)>>0]=a[j>>0]|0;a[h+(k+2)>>0]=a[j>>0]|0;a[h+(k+3)>>0]=a[j>>0]|0;a[h+(k+4)>>0]=a[j>>0]|0;a[h+(k+5)>>0]=a[j>>0]|0;a[h+(k+6)>>0]=a[j>>0]|0;a[h+(k+7)>>0]=a[j>>0]|0;a[h+(k+8)>>0]=a[j>>0]|0;a[h+(k+9)>>0]=a[j>>0]|0;a[h+(k+10)>>0]=a[j>>0]|0;a[h+(k+11)>>0]=a[j>>0]|0;a[h+(k+12)>>0]=a[j>>0]|0;a[h+(k+13)>>0]=a[j>>0]|0;a[h+(k+14)>>0]=a[j>>0]|0;a[h+(k+15)>>0]=a[j>>0]|0;a[h+(k+16)>>0]=a[j>>0]|0;a[h+(k+17)>>0]=a[j>>0]|0;a[h+(k+18)>>0]=a[j>>0]|0;a[h+(k+19)>>0]=a[j>>0]|0;a[h+(k+20)>>0]=a[j>>0]|0;a[h+(k+21)>>0]=a[j>>0]|0;a[h+(k+22)>>0]=a[j>>0]|0;a[h+(k+23)>>0]=a[j>>0]|0;a[h+(k+24)>>0]=a[j>>0]|0;a[h+(k+25)>>0]=a[j>>0]|0;a[h+(k+26)>>0]=a[j>>0]|0;a[h+(k+27)>>0]=a[j>>0]|0;a[h+(k+28)>>0]=a[j>>0]|0;a[h+(k+29)>>0]=a[j>>0]|0;e=30;g=0;while(1){k=(e+1|0)*c|0;l=e*c|0;a[h+l>>0]=a[b+k>>0]|0;a[h+(l+1)>>0]=a[b+(k+1)>>0]|0;a[h+(l+2)>>0]=a[b+(k+2)>>0]|0;a[h+(l+3)>>0]=a[b+(k+3)>>0]|0;a[h+(l+4)>>0]=a[b+(k+4)>>0]|0;a[h+(l+5)>>0]=a[b+(k+5)>>0]|0;a[h+(l+6)>>0]=a[b+(k+6)>>0]|0;a[h+(l+7)>>0]=a[b+(k+7)>>0]|0;a[h+(l+8)>>0]=a[b+(k+8)>>0]|0;a[h+(l+9)>>0]=a[b+(k+9)>>0]|0;a[h+(l+10)>>0]=a[b+(k+10)>>0]|0;a[h+(l+11)>>0]=a[b+(k+11)>>0]|0;a[h+(l+12)>>0]=a[b+(k+12)>>0]|0;a[h+(l+13)>>0]=a[b+(k+13)>>0]|0;a[h+(l+14)>>0]=a[b+(k+14)>>0]|0;a[h+(l+15)>>0]=a[b+(k+15)>>0]|0;a[h+(l+16)>>0]=a[b+(k+16)>>0]|0;a[h+(l+17)>>0]=a[b+(k+17)>>0]|0;a[h+(l+18)>>0]=a[b+(k+18)>>0]|0;a[h+(l+19)>>0]=a[b+(k+19)>>0]|0;a[h+(l+20)>>0]=a[b+(k+20)>>0]|0;a[h+(l+21)>>0]=a[b+(k+21)>>0]|0;a[h+(l+22)>>0]=a[b+(k+22)>>0]|0;a[h+(l+23)>>0]=a[b+(k+23)>>0]|0;a[h+(l+24)>>0]=a[b+(k+24)>>0]|0;a[h+(l+25)>>0]=a[b+(k+25)>>0]|0;a[h+(l+26)>>0]=a[b+(k+26)>>0]|0;a[h+(l+27)>>0]=a[b+(k+27)>>0]|0;a[h+(l+28)>>0]=a[b+(k+28)>>0]|0;a[h+(l+29)>>0]=a[b+(k+29)>>0]|0;g=g+1|0;if((g|0)==31)break;else e=e+-1|0}return}function sd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;i=e+1|0;a[b>>0]=((d[e>>0]|0)+1+(d[i>>0]|0)|0)>>>1;h=e+2|0;g=b+c|0;a[g>>0]=((d[e>>0]|0)+2+((d[i>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;j=b+1|0;a[j>>0]=((d[i>>0]|0)+1+(d[h>>0]|0)|0)>>>1;l=e+3|0;a[b+(c+1)>>0]=((d[i>>0]|0)+2+((d[h>>0]|0)<<1)+(d[l>>0]|0)|0)>>>2;i=b+2|0;a[i>>0]=((d[h>>0]|0)+1+(d[l>>0]|0)|0)>>>1;f=e+4|0;a[b+(c+2)>>0]=((d[h>>0]|0)+2+((d[l>>0]|0)<<1)+(d[f>>0]|0)|0)>>>2;h=b+3|0;a[h>>0]=((d[l>>0]|0)+1+(d[f>>0]|0)|0)>>>1;k=e+5|0;a[b+(c+3)>>0]=((d[l>>0]|0)+2+((d[f>>0]|0)<<1)+(d[k>>0]|0)|0)>>>2;a[b+4>>0]=((d[f>>0]|0)+1+(d[k>>0]|0)|0)>>>1;l=e+6|0;a[b+(c+4)>>0]=((d[f>>0]|0)+2+((d[k>>0]|0)<<1)+(d[l>>0]|0)|0)>>>2;a[b+5>>0]=((d[k>>0]|0)+1+(d[l>>0]|0)|0)>>>1;f=e+7|0;a[b+(c+5)>>0]=((d[k>>0]|0)+2+((d[l>>0]|0)<<1)+(d[f>>0]|0)|0)>>>2;a[b+6>>0]=((d[l>>0]|0)+1+(d[f>>0]|0)|0)>>>1;k=e+8|0;a[b+(c+6)>>0]=((d[l>>0]|0)+2+((d[f>>0]|0)<<1)+(d[k>>0]|0)|0)>>>2;a[b+7>>0]=((d[f>>0]|0)+1+(d[k>>0]|0)|0)>>>1;a[b+(c+7)>>0]=((d[f>>0]|0)+2+((d[k>>0]|0)<<1)+(d[e+9>>0]|0)|0)>>>2;e=b+(c<<1)|0;a[e>>0]=a[j>>0]|0;a[e+1>>0]=a[j+1>>0]|0;a[e+2>>0]=a[j+2>>0]|0;a[e+3>>0]=a[j+3>>0]|0;a[e+4>>0]=a[j+4>>0]|0;a[e+5>>0]=a[j+5>>0]|0;kg(e+6|0,a[f>>0]|0,2)|0;e=b+(c*3|0)|0;j=g+1|0;a[e>>0]=a[j>>0]|0;a[e+1>>0]=a[j+1>>0]|0;a[e+2>>0]=a[j+2>>0]|0;a[e+3>>0]=a[j+3>>0]|0;a[e+4>>0]=a[j+4>>0]|0;a[e+5>>0]=a[j+5>>0]|0;kg(e+6|0,a[f>>0]|0,2)|0;e=b+(c<<2)|0;a[e>>0]=a[i>>0]|0;a[e+1>>0]=a[i+1>>0]|0;a[e+2>>0]=a[i+2>>0]|0;a[e+3>>0]=a[i+3>>0]|0;a[e+4>>0]=a[i+4>>0]|0;kg(e+5|0,a[f>>0]|0,3)|0;e=b+(c*5|0)|0;i=g+2|0;a[e>>0]=a[i>>0]|0;a[e+1>>0]=a[i+1>>0]|0;a[e+2>>0]=a[i+2>>0]|0;a[e+3>>0]=a[i+3>>0]|0;a[e+4>>0]=a[i+4>>0]|0;kg(e+5|0,a[f>>0]|0,3)|0;e=b+(c*6|0)|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;a[e>>0]=h;a[e+1>>0]=h>>8;a[e+2>>0]=h>>16;a[e+3>>0]=h>>24;kg(e+4|0,a[f>>0]|0,4)|0;e=b+(c*7|0)|0;c=g+3|0;c=d[c>>0]|d[c+1>>0]<<8|d[c+2>>0]<<16|d[c+3>>0]<<24;a[e>>0]=c;a[e+1>>0]=c>>8;a[e+2>>0]=c>>16;a[e+3>>0]=c>>24;kg(e+4|0,a[f>>0]|0,4)|0;return}function td(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;f=0;do{h=e+f|0;j=f;f=f+1|0;i=e+f|0;a[b+j>>0]=((d[h>>0]|0)+1+(d[i>>0]|0)|0)>>>1;a[b+(j+c)>>0]=((d[h>>0]|0)+2+((d[i>>0]|0)<<1)+(d[e+(j+2)>>0]|0)|0)>>>2}while((f|0)!=16);e=e+15|0;g=b+c|0;f=b+(c<<1)|0;h=f;i=b+1|0;j=h+14|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+14|0,a[e>>0]|0,2)|0;f=b+(c*3|0)|0;h=f;i=g+1|0;j=h+14|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+14|0,a[e>>0]|0,2)|0;f=b+(c<<2)|0;h=f;i=b+2|0;j=h+13|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+13|0,a[e>>0]|0,3)|0;f=b+(c*5|0)|0;h=f;i=g+2|0;j=h+13|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+13|0,a[e>>0]|0,3)|0;f=b+(c*6|0)|0;h=f;i=b+3|0;j=h+12|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+12|0,a[e>>0]|0,4)|0;f=b+(c*7|0)|0;h=f;i=g+3|0;j=h+12|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+12|0,a[e>>0]|0,4)|0;f=b+(c<<3)|0;h=f;i=b+4|0;j=h+11|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+11|0,a[e>>0]|0,5)|0;f=b+(c*9|0)|0;h=f;i=g+4|0;j=h+11|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+11|0,a[e>>0]|0,5)|0;f=b+(c*10|0)|0;h=f;i=b+5|0;j=h+10|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+10|0,a[e>>0]|0,6)|0;f=b+(c*11|0)|0;h=f;i=g+5|0;j=h+10|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+10|0,a[e>>0]|0,6)|0;f=b+(c*12|0)|0;h=f;i=b+6|0;j=h+9|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+9|0,a[e>>0]|0,7)|0;f=b+(c*13|0)|0;h=f;i=g+6|0;j=h+9|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+9|0,a[e>>0]|0,7)|0;j=b+(c*14|0)|0;i=b+7|0;k=i;k=d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;h=j;f=h;a[f>>0]=k;a[f+1>>0]=k>>8;a[f+2>>0]=k>>16;a[f+3>>0]=k>>24;h=h+4|0;a[h>>0]=i;a[h+1>>0]=i>>8;a[h+2>>0]=i>>16;a[h+3>>0]=i>>24;kg(j+8|0,a[e>>0]|0,8)|0;c=b+(c*15|0)|0;j=g+7|0;h=j;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;j=j+4|0;j=d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24;b=c;i=b;a[i>>0]=h;a[i+1>>0]=h>>8;a[i+2>>0]=h>>16;a[i+3>>0]=h>>24;b=b+4|0;a[b>>0]=j;a[b+1>>0]=j>>8;a[b+2>>0]=j>>16;a[b+3>>0]=j>>24;kg(c+8|0,a[e>>0]|0,8)|0;return}function ud(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0;f=0;do{h=e+f|0;j=f;f=f+1|0;i=e+f|0;a[b+j>>0]=((d[h>>0]|0)+1+(d[i>>0]|0)|0)>>>1;a[b+(j+c)>>0]=((d[h>>0]|0)+2+((d[i>>0]|0)<<1)+(d[e+(j+2)>>0]|0)|0)>>>2}while((f|0)!=32);e=e+31|0;g=b+c|0;f=b+(c<<1)|0;h=f;i=b+1|0;j=h+30|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+30|0,a[e>>0]|0,2)|0;f=b+(c*3|0)|0;h=f;i=g+1|0;j=h+30|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+30|0,a[e>>0]|0,2)|0;f=b+(c<<2)|0;h=f;i=b+2|0;j=h+29|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+29|0,a[e>>0]|0,3)|0;f=b+(c*5|0)|0;h=f;i=g+2|0;j=h+29|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+29|0,a[e>>0]|0,3)|0;f=b+(c*6|0)|0;h=f;i=b+3|0;j=h+28|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+28|0,a[e>>0]|0,4)|0;f=b+(c*7|0)|0;h=f;i=g+3|0;j=h+28|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+28|0,a[e>>0]|0,4)|0;f=b+(c<<3)|0;h=f;i=b+4|0;j=h+27|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+27|0,a[e>>0]|0,5)|0;f=b+(c*9|0)|0;h=f;i=g+4|0;j=h+27|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+27|0,a[e>>0]|0,5)|0;f=b+(c*10|0)|0;h=f;i=b+5|0;j=h+26|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+26|0,a[e>>0]|0,6)|0;f=b+(c*11|0)|0;h=f;i=g+5|0;j=h+26|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+26|0,a[e>>0]|0,6)|0;f=b+(c*12|0)|0;h=f;i=b+6|0;j=h+25|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+25|0,a[e>>0]|0,7)|0;f=b+(c*13|0)|0;h=f;i=g+6|0;j=h+25|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+25|0,a[e>>0]|0,7)|0;f=b+(c*14|0)|0;h=f;i=b+7|0;j=h+24|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+24|0,a[e>>0]|0,8)|0;f=b+(c*15|0)|0;h=f;i=g+7|0;j=h+24|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+24|0,a[e>>0]|0,8)|0;f=b+(c<<4)|0;h=f;i=b+8|0;j=h+23|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+23|0,a[e>>0]|0,9)|0;f=b+(c*17|0)|0;h=f;i=g+8|0;j=h+23|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+23|0,a[e>>0]|0,9)|0;f=b+(c*18|0)|0;h=f;i=b+9|0;j=h+22|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+22|0,a[e>>0]|0,10)|0;f=b+(c*19|0)|0;h=f;i=g+9|0;j=h+22|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+22|0,a[e>>0]|0,10)|0;f=b+(c*20|0)|0;h=f;i=b+10|0;j=h+21|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+21|0,a[e>>0]|0,11)|0;f=b+(c*21|0)|0;h=f;i=g+10|0;j=h+21|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+21|0,a[e>>0]|0,11)|0;f=b+(c*22|0)|0;h=f;i=b+11|0;j=h+20|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+20|0,a[e>>0]|0,12)|0;f=b+(c*23|0)|0;h=f;i=g+11|0;j=h+20|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+20|0,a[e>>0]|0,12)|0;f=b+(c*24|0)|0;h=f;i=b+12|0;j=h+19|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+19|0,a[e>>0]|0,13)|0;f=b+(c*25|0)|0;h=f;i=g+12|0;j=h+19|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+19|0,a[e>>0]|0,13)|0;f=b+(c*26|0)|0;h=f;i=b+13|0;j=h+18|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+18|0,a[e>>0]|0,14)|0;f=b+(c*27|0)|0;h=f;i=g+13|0;j=h+18|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+18|0,a[e>>0]|0,14)|0;f=b+(c*28|0)|0;h=f;i=b+14|0;j=h+17|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+17|0,a[e>>0]|0,15)|0;f=b+(c*29|0)|0;h=f;i=g+14|0;j=h+17|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+17|0,a[e>>0]|0,15)|0;f=b+(c*30|0)|0;h=f;i=b+15|0;j=h+16|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+16|0,a[e>>0]|0,16)|0;f=b+(c*31|0)|0;h=f;i=g+15|0;j=h+16|0;do{a[h>>0]=a[i>>0]|0;h=h+1|0;i=i+1|0}while((h|0)<(j|0));kg(f+16|0,a[e>>0]|0,16)|0;return}function vd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=e+7|0;f=a[m>>0]|0;h=e+1|0;g=e+2|0;a[b>>0]=((d[e>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;n=e+3|0;l=b+1|0;a[l>>0]=((d[h>>0]|0)+2+((d[g>>0]|0)<<1)+(d[n>>0]|0)|0)>>>2;h=e+4|0;k=b+2|0;a[k>>0]=((d[g>>0]|0)+2+((d[n>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;g=e+5|0;j=b+3|0;a[j>>0]=((d[n>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;n=e+6|0;i=b+4|0;a[i>>0]=((d[h>>0]|0)+2+((d[g>>0]|0)<<1)+(d[n>>0]|0)|0)>>>2;h=b+5|0;a[h>>0]=((d[g>>0]|0)+2+((d[n>>0]|0)<<1)+(d[m>>0]|0)|0)>>>2;g=b+6|0;a[g>>0]=((d[n>>0]|0)+2+((d[m>>0]|0)<<1)+(d[e+8>>0]|0)|0)>>>2;a[b+7>>0]=f;e=b+c|0;a[e>>0]=a[l>>0]|0;a[e+1>>0]=a[l+1>>0]|0;a[e+2>>0]=a[l+2>>0]|0;a[e+3>>0]=a[l+3>>0]|0;a[e+4>>0]=a[l+4>>0]|0;a[e+5>>0]=a[l+5>>0]|0;kg(e+6|0,f|0,2)|0;e=e+c|0;a[e>>0]=a[k>>0]|0;a[e+1>>0]=a[k+1>>0]|0;a[e+2>>0]=a[k+2>>0]|0;a[e+3>>0]=a[k+3>>0]|0;a[e+4>>0]=a[k+4>>0]|0;kg(e+5|0,f|0,3)|0;e=e+c|0;b=d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24;a[e>>0]=b;a[e+1>>0]=b>>8;a[e+2>>0]=b>>16;a[e+3>>0]=b>>24;kg(e+4|0,f|0,4)|0;e=e+c|0;a[e>>0]=a[i>>0]|0;a[e+1>>0]=a[i+1>>0]|0;a[e+2>>0]=a[i+2>>0]|0;kg(e+3|0,f|0,5)|0;e=e+c|0;b=d[h>>0]|d[h+1>>0]<<8;a[e>>0]=b;a[e+1>>0]=b>>8;kg(e+2|0,f|0,6)|0;e=e+c|0;a[e>>0]=a[g>>0]|0;kg(e+1|0,f|0,7)|0;kg(e+c|0,f|0,8)|0;return}function wd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;f=e+15|0;u=a[f>>0]|0;t=e+1|0;v=e+2|0;a[b>>0]=((d[e>>0]|0)+2+((d[t>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;s=e+3|0;j=b+1|0;a[j>>0]=((d[t>>0]|0)+2+((d[v>>0]|0)<<1)+(d[s>>0]|0)|0)>>>2;t=e+4|0;g=b+2|0;a[g>>0]=((d[v>>0]|0)+2+((d[s>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;v=e+5|0;h=b+3|0;a[h>>0]=((d[s>>0]|0)+2+((d[t>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;s=e+6|0;i=b+4|0;a[i>>0]=((d[t>>0]|0)+2+((d[v>>0]|0)<<1)+(d[s>>0]|0)|0)>>>2;t=e+7|0;k=b+5|0;a[k>>0]=((d[v>>0]|0)+2+((d[s>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;v=e+8|0;l=b+6|0;a[l>>0]=((d[s>>0]|0)+2+((d[t>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;s=e+9|0;m=b+7|0;a[m>>0]=((d[t>>0]|0)+2+((d[v>>0]|0)<<1)+(d[s>>0]|0)|0)>>>2;t=e+10|0;n=b+8|0;a[n>>0]=((d[v>>0]|0)+2+((d[s>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;v=e+11|0;o=b+9|0;a[o>>0]=((d[s>>0]|0)+2+((d[t>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;s=e+12|0;p=b+10|0;a[p>>0]=((d[t>>0]|0)+2+((d[v>>0]|0)<<1)+(d[s>>0]|0)|0)>>>2;t=e+13|0;q=b+11|0;a[q>>0]=((d[v>>0]|0)+2+((d[s>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;v=e+14|0;r=b+12|0;a[r>>0]=((d[s>>0]|0)+2+((d[t>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;s=b+13|0;a[s>>0]=((d[t>>0]|0)+2+((d[v>>0]|0)<<1)+(d[f>>0]|0)|0)>>>2;t=b+14|0;a[t>>0]=((d[v>>0]|0)+2+((d[f>>0]|0)<<1)+(d[e+16>>0]|0)|0)>>>2;a[b+15>>0]=u;f=b+c|0;b=f;e=j;j=b+14|0;do{a[b>>0]=a[e>>0]|0;b=b+1|0;e=e+1|0}while((b|0)<(j|0));kg(f+14|0,u|0,2)|0;f=f+c|0;b=f;e=g;j=b+13|0;do{a[b>>0]=a[e>>0]|0;b=b+1|0;e=e+1|0}while((b|0)<(j|0));kg(f+13|0,u|0,3)|0;f=f+c|0;b=f;e=h;j=b+12|0;do{a[b>>0]=a[e>>0]|0;b=b+1|0;e=e+1|0}while((b|0)<(j|0));kg(f+12|0,u|0,4)|0;f=f+c|0;b=f;e=i;j=b+11|0;do{a[b>>0]=a[e>>0]|0;b=b+1|0;e=e+1|0}while((b|0)<(j|0));kg(f+11|0,u|0,5)|0;f=f+c|0;b=f;e=k;j=b+10|0;do{a[b>>0]=a[e>>0]|0;b=b+1|0;e=e+1|0}while((b|0)<(j|0));kg(f+10|0,u|0,6)|0;f=f+c|0;b=f;e=l;j=b+9|0;do{a[b>>0]=a[e>>0]|0;b=b+1|0;e=e+1|0}while((b|0)<(j|0));kg(f+9|0,u|0,7)|0;v=f+c|0;l=m;j=l;j=d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24;l=l+4|0;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;m=v;k=m;a[k>>0]=j;a[k+1>>0]=j>>8;a[k+2>>0]=j>>16;a[k+3>>0]=j>>24;m=m+4|0;a[m>>0]=l;a[m+1>>0]=l>>8;a[m+2>>0]=l>>16;a[m+3>>0]=l>>24;kg(v+8|0,u|0,8)|0;v=v+c|0;a[v>>0]=a[n>>0]|0;a[v+1>>0]=a[n+1>>0]|0;a[v+2>>0]=a[n+2>>0]|0;a[v+3>>0]=a[n+3>>0]|0;a[v+4>>0]=a[n+4>>0]|0;a[v+5>>0]=a[n+5>>0]|0;a[v+6>>0]=a[n+6>>0]|0;kg(v+7|0,u|0,9)|0;v=v+c|0;a[v>>0]=a[o>>0]|0;a[v+1>>0]=a[o+1>>0]|0;a[v+2>>0]=a[o+2>>0]|0;a[v+3>>0]=a[o+3>>0]|0;a[v+4>>0]=a[o+4>>0]|0;a[v+5>>0]=a[o+5>>0]|0;kg(v+6|0,u|0,10)|0;v=v+c|0;a[v>>0]=a[p>>0]|0;a[v+1>>0]=a[p+1>>0]|0;a[v+2>>0]=a[p+2>>0]|0;a[v+3>>0]=a[p+3>>0]|0;a[v+4>>0]=a[p+4>>0]|0;kg(v+5|0,u|0,11)|0;v=v+c|0;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;a[v>>0]=q;a[v+1>>0]=q>>8;a[v+2>>0]=q>>16;a[v+3>>0]=q>>24;kg(v+4|0,u|0,12)|0;v=v+c|0;a[v>>0]=a[r>>0]|0;a[v+1>>0]=a[r+1>>0]|0;a[v+2>>0]=a[r+2>>0]|0;kg(v+3|0,u|0,13)|0;v=v+c|0;s=d[s>>0]|d[s+1>>0]<<8;a[v>>0]=s;a[v+1>>0]=s>>8;kg(v+2|0,u|0,14)|0;v=v+c|0;a[v>>0]=a[t>>0]|0;kg(v+1|0,u|0,15)|0;kg(v+c|0,u|0,16)|0;return}function xd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0;i=a[e+31>>0]|0;f=0;do{h=f;f=f+1|0;a[b+h>>0]=((d[e+h>>0]|0)+2+((d[e+f>>0]|0)<<1)+(d[e+(h+2)>>0]|0)|0)>>>2}while((f|0)!=31);a[b+31>>0]=i;f=b+c|0;e=f;g=b+1|0;h=e+30|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+30|0,i|0,2)|0;f=f+c|0;e=f;g=b+2|0;h=e+29|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+29|0,i|0,3)|0;f=f+c|0;e=f;g=b+3|0;h=e+28|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+28|0,i|0,4)|0;f=f+c|0;e=f;g=b+4|0;h=e+27|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+27|0,i|0,5)|0;f=f+c|0;e=f;g=b+5|0;h=e+26|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+26|0,i|0,6)|0;f=f+c|0;e=f;g=b+6|0;h=e+25|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+25|0,i|0,7)|0;f=f+c|0;e=f;g=b+7|0;h=e+24|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+24|0,i|0,8)|0;f=f+c|0;e=f;g=b+8|0;h=e+23|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+23|0,i|0,9)|0;f=f+c|0;e=f;g=b+9|0;h=e+22|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+22|0,i|0,10)|0;f=f+c|0;e=f;g=b+10|0;h=e+21|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+21|0,i|0,11)|0;f=f+c|0;e=f;g=b+11|0;h=e+20|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+20|0,i|0,12)|0;f=f+c|0;e=f;g=b+12|0;h=e+19|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+19|0,i|0,13)|0;f=f+c|0;e=f;g=b+13|0;h=e+18|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+18|0,i|0,14)|0;f=f+c|0;e=f;g=b+14|0;h=e+17|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+17|0,i|0,15)|0;f=f+c|0;e=f;g=b+15|0;h=e+16|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+16|0,i|0,16)|0;f=f+c|0;e=f;g=b+16|0;h=e+15|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+15|0,i|0,17)|0;f=f+c|0;e=f;g=b+17|0;h=e+14|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+14|0,i|0,18)|0;f=f+c|0;e=f;g=b+18|0;h=e+13|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+13|0,i|0,19)|0;f=f+c|0;e=f;g=b+19|0;h=e+12|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+12|0,i|0,20)|0;f=f+c|0;e=f;g=b+20|0;h=e+11|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+11|0,i|0,21)|0;f=f+c|0;e=f;g=b+21|0;h=e+10|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+10|0,i|0,22)|0;f=f+c|0;e=f;g=b+22|0;h=e+9|0;do{a[e>>0]=a[g>>0]|0;e=e+1|0;g=g+1|0}while((e|0)<(h|0));kg(f+9|0,i|0,23)|0;h=f+c|0;e=b+23|0;j=e;j=d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24;e=e+4|0;e=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;g=h;f=g;a[f>>0]=j;a[f+1>>0]=j>>8;a[f+2>>0]=j>>16;a[f+3>>0]=j>>24;g=g+4|0;a[g>>0]=e;a[g+1>>0]=e>>8;a[g+2>>0]=e>>16;a[g+3>>0]=e>>24;kg(h+8|0,i|0,24)|0;h=h+c|0;g=b+24|0;a[h>>0]=a[g>>0]|0;a[h+1>>0]=a[g+1>>0]|0;a[h+2>>0]=a[g+2>>0]|0;a[h+3>>0]=a[g+3>>0]|0;a[h+4>>0]=a[g+4>>0]|0;a[h+5>>0]=a[g+5>>0]|0;a[h+6>>0]=a[g+6>>0]|0;kg(h+7|0,i|0,25)|0;h=h+c|0;g=b+25|0;a[h>>0]=a[g>>0]|0;a[h+1>>0]=a[g+1>>0]|0;a[h+2>>0]=a[g+2>>0]|0;a[h+3>>0]=a[g+3>>0]|0;a[h+4>>0]=a[g+4>>0]|0;a[h+5>>0]=a[g+5>>0]|0;kg(h+6|0,i|0,26)|0;h=h+c|0;g=b+26|0;a[h>>0]=a[g>>0]|0;a[h+1>>0]=a[g+1>>0]|0;a[h+2>>0]=a[g+2>>0]|0;a[h+3>>0]=a[g+3>>0]|0;a[h+4>>0]=a[g+4>>0]|0;kg(h+5|0,i|0,27)|0;h=h+c|0;g=b+27|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;a[h>>0]=g;a[h+1>>0]=g>>8;a[h+2>>0]=g>>16;a[h+3>>0]=g>>24;kg(h+4|0,i|0,28)|0;h=h+c|0;g=b+28|0;a[h>>0]=a[g>>0]|0;a[h+1>>0]=a[g+1>>0]|0;a[h+2>>0]=a[g+2>>0]|0;kg(h+3|0,i|0,29)|0;h=h+c|0;g=b+29|0;g=d[g>>0]|d[g+1>>0]<<8;a[h>>0]=g;a[h+1>>0]=g>>8;kg(h+2|0,i|0,30)|0;h=h+c|0;a[h>>0]=a[b+30>>0]|0;kg(h+1|0,i|0,31)|0;kg(h+c|0,i|0,32)|0;return}function yd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;k=e+-1|0;a[b>>0]=((d[k>>0]|0)+1+(d[e>>0]|0)|0)>>>1;o=e+1|0;a[b+1>>0]=((d[e>>0]|0)+1+(d[o>>0]|0)|0)>>>1;n=e+2|0;a[b+2>>0]=((d[o>>0]|0)+1+(d[n>>0]|0)|0)>>>1;g=e+3|0;a[b+3>>0]=((d[n>>0]|0)+1+(d[g>>0]|0)|0)>>>1;h=e+4|0;a[b+4>>0]=((d[g>>0]|0)+1+(d[h>>0]|0)|0)>>>1;i=e+5|0;a[b+5>>0]=((d[h>>0]|0)+1+(d[i>>0]|0)|0)>>>1;l=e+6|0;a[b+6>>0]=((d[i>>0]|0)+1+(d[l>>0]|0)|0)>>>1;j=e+7|0;a[b+7>>0]=((d[l>>0]|0)+1+(d[j>>0]|0)|0)>>>1;m=b+c|0;a[m>>0]=((d[f>>0]|0)+2+((d[k>>0]|0)<<1)+(d[e>>0]|0)|0)>>>2;a[m+1>>0]=((d[k>>0]|0)+2+((d[e>>0]|0)<<1)+(d[o>>0]|0)|0)>>>2;a[m+2>>0]=((d[e>>0]|0)+2+((d[o>>0]|0)<<1)+(d[n>>0]|0)|0)>>>2;a[m+3>>0]=((d[o>>0]|0)+2+((d[n>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;a[m+4>>0]=((d[n>>0]|0)+2+((d[g>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;a[m+5>>0]=((d[g>>0]|0)+2+((d[h>>0]|0)<<1)+(d[i>>0]|0)|0)>>>2;a[m+6>>0]=((d[h>>0]|0)+2+((d[i>>0]|0)<<1)+(d[l>>0]|0)|0)>>>2;a[m+7>>0]=((d[i>>0]|0)+2+((d[l>>0]|0)<<1)+(d[j>>0]|0)|0)>>>2;m=m+c|0;j=f+1|0;a[m>>0]=((d[k>>0]|0)+2+((d[f>>0]|0)<<1)+(d[j>>0]|0)|0)>>>2;k=f+2|0;l=m+c|0;a[l>>0]=((d[f>>0]|0)+2+((d[j>>0]|0)<<1)+(d[k>>0]|0)|0)>>>2;i=f+3|0;a[m+(c<<1)>>0]=((d[j>>0]|0)+2+((d[k>>0]|0)<<1)+(d[i>>0]|0)|0)>>>2;j=f+4|0;a[m+(c*3|0)>>0]=((d[k>>0]|0)+2+((d[i>>0]|0)<<1)+(d[j>>0]|0)|0)>>>2;k=f+5|0;a[m+(c<<2)>>0]=((d[i>>0]|0)+2+((d[j>>0]|0)<<1)+(d[k>>0]|0)|0)>>>2;a[m+(c*5|0)>>0]=((d[j>>0]|0)+2+((d[k>>0]|0)<<1)+(d[f+6>>0]|0)|0)>>>2;k=c*-2|0;a[m+1>>0]=a[m+k>>0]|0;j=k|1;a[m+2>>0]=a[m+j>>0]|0;i=k+2|0;a[m+3>>0]=a[m+i>>0]|0;h=k+3|0;a[m+4>>0]=a[m+h>>0]|0;g=k+4|0;a[m+5>>0]=a[m+g>>0]|0;b=k+5|0;a[m+6>>0]=a[m+b>>0]|0;e=k+6|0;a[m+7>>0]=a[m+e>>0]|0;a[l+1>>0]=a[l+k>>0]|0;a[l+2>>0]=a[l+j>>0]|0;a[l+3>>0]=a[l+i>>0]|0;a[l+4>>0]=a[l+h>>0]|0;a[l+5>>0]=a[l+g>>0]|0;a[l+6>>0]=a[l+b>>0]|0;a[l+7>>0]=a[l+e>>0]|0;f=l+c|0;a[f+1>>0]=a[f+k>>0]|0;a[f+2>>0]=a[f+j>>0]|0;a[f+3>>0]=a[f+i>>0]|0;a[f+4>>0]=a[f+h>>0]|0;a[f+5>>0]=a[f+g>>0]|0;a[f+6>>0]=a[f+b>>0]|0;a[f+7>>0]=a[f+e>>0]|0;f=f+c|0;a[f+1>>0]=a[f+k>>0]|0;a[f+2>>0]=a[f+j>>0]|0;a[f+3>>0]=a[f+i>>0]|0;a[f+4>>0]=a[f+h>>0]|0;a[f+5>>0]=a[f+g>>0]|0;a[f+6>>0]=a[f+b>>0]|0;a[f+7>>0]=a[f+e>>0]|0;f=f+c|0;a[f+1>>0]=a[f+k>>0]|0;a[f+2>>0]=a[f+j>>0]|0;a[f+3>>0]=a[f+i>>0]|0;a[f+4>>0]=a[f+h>>0]|0;a[f+5>>0]=a[f+g>>0]|0;a[f+6>>0]=a[f+b>>0]|0;a[f+7>>0]=a[f+e>>0]|0;f=f+c|0;a[f+1>>0]=a[f+k>>0]|0;a[f+2>>0]=a[f+j>>0]|0;a[f+3>>0]=a[f+i>>0]|0;a[f+4>>0]=a[f+h>>0]|0;a[f+5>>0]=a[f+g>>0]|0;a[f+6>>0]=a[f+b>>0]|0;a[f+7>>0]=a[f+e>>0]|0;return}function zd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;i=e+-1|0;a[b>>0]=((d[i>>0]|0)+1+(d[e>>0]|0)|0)>>>1;v=e+1|0;a[b+1>>0]=((d[e>>0]|0)+1+(d[v>>0]|0)|0)>>>1;u=e+2|0;a[b+2>>0]=((d[v>>0]|0)+1+(d[u>>0]|0)|0)>>>1;t=e+3|0;a[b+3>>0]=((d[u>>0]|0)+1+(d[t>>0]|0)|0)>>>1;s=e+4|0;a[b+4>>0]=((d[t>>0]|0)+1+(d[s>>0]|0)|0)>>>1;r=e+5|0;a[b+5>>0]=((d[s>>0]|0)+1+(d[r>>0]|0)|0)>>>1;q=e+6|0;a[b+6>>0]=((d[r>>0]|0)+1+(d[q>>0]|0)|0)>>>1;p=e+7|0;a[b+7>>0]=((d[q>>0]|0)+1+(d[p>>0]|0)|0)>>>1;o=e+8|0;a[b+8>>0]=((d[p>>0]|0)+1+(d[o>>0]|0)|0)>>>1;n=e+9|0;a[b+9>>0]=((d[o>>0]|0)+1+(d[n>>0]|0)|0)>>>1;m=e+10|0;a[b+10>>0]=((d[n>>0]|0)+1+(d[m>>0]|0)|0)>>>1;l=e+11|0;a[b+11>>0]=((d[m>>0]|0)+1+(d[l>>0]|0)|0)>>>1;k=e+12|0;a[b+12>>0]=((d[l>>0]|0)+1+(d[k>>0]|0)|0)>>>1;j=e+13|0;a[b+13>>0]=((d[k>>0]|0)+1+(d[j>>0]|0)|0)>>>1;h=e+14|0;a[b+14>>0]=((d[j>>0]|0)+1+(d[h>>0]|0)|0)>>>1;g=e+15|0;a[b+15>>0]=((d[h>>0]|0)+1+(d[g>>0]|0)|0)>>>1;b=b+c|0;a[b>>0]=((d[f>>0]|0)+2+((d[i>>0]|0)<<1)+(d[e>>0]|0)|0)>>>2;a[b+1>>0]=((d[i>>0]|0)+2+((d[e>>0]|0)<<1)+(d[v>>0]|0)|0)>>>2;a[b+2>>0]=((d[e>>0]|0)+2+((d[v>>0]|0)<<1)+(d[u>>0]|0)|0)>>>2;a[b+3>>0]=((d[v>>0]|0)+2+((d[u>>0]|0)<<1)+(d[t>>0]|0)|0)>>>2;a[b+4>>0]=((d[u>>0]|0)+2+((d[t>>0]|0)<<1)+(d[s>>0]|0)|0)>>>2;a[b+5>>0]=((d[t>>0]|0)+2+((d[s>>0]|0)<<1)+(d[r>>0]|0)|0)>>>2;a[b+6>>0]=((d[s>>0]|0)+2+((d[r>>0]|0)<<1)+(d[q>>0]|0)|0)>>>2;a[b+7>>0]=((d[r>>0]|0)+2+((d[q>>0]|0)<<1)+(d[p>>0]|0)|0)>>>2;a[b+8>>0]=((d[q>>0]|0)+2+((d[p>>0]|0)<<1)+(d[o>>0]|0)|0)>>>2;a[b+9>>0]=((d[p>>0]|0)+2+((d[o>>0]|0)<<1)+(d[n>>0]|0)|0)>>>2;a[b+10>>0]=((d[o>>0]|0)+2+((d[n>>0]|0)<<1)+(d[m>>0]|0)|0)>>>2;a[b+11>>0]=((d[n>>0]|0)+2+((d[m>>0]|0)<<1)+(d[l>>0]|0)|0)>>>2;a[b+12>>0]=((d[m>>0]|0)+2+((d[l>>0]|0)<<1)+(d[k>>0]|0)|0)>>>2;a[b+13>>0]=((d[l>>0]|0)+2+((d[k>>0]|0)<<1)+(d[j>>0]|0)|0)>>>2;a[b+14>>0]=((d[k>>0]|0)+2+((d[j>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;a[b+15>>0]=((d[j>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;b=b+c|0;g=f+1|0;a[b>>0]=((d[i>>0]|0)+2+((d[f>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;i=f+2|0;a[b+c>>0]=((d[f>>0]|0)+2+((d[g>>0]|0)<<1)+(d[i>>0]|0)|0)>>>2;h=f+3|0;a[b+(c<<1)>>0]=((d[g>>0]|0)+2+((d[i>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;g=f+4|0;a[b+(c*3|0)>>0]=((d[i>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;i=f+5|0;a[b+(c<<2)>>0]=((d[h>>0]|0)+2+((d[g>>0]|0)<<1)+(d[i>>0]|0)|0)>>>2;h=f+6|0;a[b+(c*5|0)>>0]=((d[g>>0]|0)+2+((d[i>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;g=f+7|0;a[b+(c*6|0)>>0]=((d[i>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;i=f+8|0;a[b+(c*7|0)>>0]=((d[h>>0]|0)+2+((d[g>>0]|0)<<1)+(d[i>>0]|0)|0)>>>2;h=f+9|0;a[b+(c<<3)>>0]=((d[g>>0]|0)+2+((d[i>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;g=f+10|0;a[b+(c*9|0)>>0]=((d[i>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;i=f+11|0;a[b+(c*10|0)>>0]=((d[h>>0]|0)+2+((d[g>>0]|0)<<1)+(d[i>>0]|0)|0)>>>2;h=f+12|0;a[b+(c*11|0)>>0]=((d[g>>0]|0)+2+((d[i>>0]|0)<<1)+(d[h>>0]|0)|0)>>>2;g=f+13|0;a[b+(c*12|0)>>0]=((d[i>>0]|0)+2+((d[h>>0]|0)<<1)+(d[g>>0]|0)|0)>>>2;a[b+(c*13|0)>>0]=((d[h>>0]|0)+2+((d[g>>0]|0)<<1)+(d[f+14>>0]|0)|0)>>>2;f=c*-2|0;g=f|1;h=f+2|0;i=f+3|0;j=f+4|0;k=f+5|0;l=f+6|0;m=f+7|0;n=f+8|0;o=f+9|0;p=f+10|0;q=f+11|0;r=f+12|0;s=f+13|0;t=f+14|0;e=2;while(1){a[b+1>>0]=a[b+f>>0]|0;a[b+2>>0]=a[b+g>>0]|0;a[b+3>>0]=a[b+h>>0]|0;a[b+4>>0]=a[b+i>>0]|0;a[b+5>>0]=a[b+j>>0]|0;a[b+6>>0]=a[b+k>>0]|0;a[b+7>>0]=a[b+l>>0]|0;a[b+8>>0]=a[b+m>>0]|0;a[b+9>>0]=a[b+n>>0]|0;a[b+10>>0]=a[b+o>>0]|0;a[b+11>>0]=a[b+p>>0]|0;a[b+12>>0]=a[b+q>>0]|0;a[b+13>>0]=a[b+r>>0]|0;a[b+14>>0]=a[b+s>>0]|0;a[b+15>>0]=a[b+t>>0]|0;e=e+1|0;if((e|0)==16)break;else b=b+c|0}return}function Ad(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0;h=e+-1|0;a[b>>0]=((d[h>>0]|0)+1+(d[e>>0]|0)|0)>>>1;J=e+1|0;a[b+1>>0]=((d[e>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+2|0;a[b+2>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+3|0;a[b+3>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+4|0;a[b+4>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+5|0;a[b+5>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+6|0;a[b+6>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+7|0;a[b+7>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+8|0;a[b+8>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+9|0;a[b+9>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+10|0;a[b+10>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+11|0;a[b+11>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+12|0;a[b+12>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+13|0;a[b+13>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+14|0;a[b+14>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+15|0;a[b+15>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+16|0;a[b+16>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+17|0;a[b+17>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+18|0;a[b+18>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+19|0;a[b+19>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+20|0;a[b+20>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+21|0;a[b+21>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+22|0;a[b+22>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+23|0;a[b+23>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+24|0;a[b+24>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+25|0;a[b+25>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+26|0;a[b+26>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+27|0;a[b+27>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+28|0;a[b+28>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;J=e+29|0;a[b+29>>0]=((d[g>>0]|0)+1+(d[J>>0]|0)|0)>>>1;g=e+30|0;a[b+30>>0]=((d[J>>0]|0)+1+(d[g>>0]|0)|0)>>>1;a[b+31>>0]=((d[g>>0]|0)+1+(d[e+31>>0]|0)|0)>>>1;g=b+c|0;a[g>>0]=((d[f>>0]|0)+2+((d[h>>0]|0)<<1)+(d[e>>0]|0)|0)>>>2;b=1;do{a[g+b>>0]=((d[e+(b+-2)>>0]|0)+2+((d[e+(b+-1)>>0]|0)<<1)+(d[e+b>>0]|0)|0)>>>2;b=b+1|0}while((b|0)!=32);g=g+c|0;a[g>>0]=((d[h>>0]|0)+2+((d[f>>0]|0)<<1)+(d[f+1>>0]|0)|0)>>>2;b=3;do{J=b+-2|0;a[g+(J*c|0)>>0]=((d[f+(b+-3)>>0]|0)+2+((d[f+J>>0]|0)<<1)+(d[f+(b+-1)>>0]|0)|0)>>>2;b=b+1|0}while((b|0)!=32);e=c*-2|0;h=e|1;f=e+2|0;i=e+3|0;j=e+4|0;k=e+5|0;l=e+6|0;m=e+7|0;n=e+8|0;o=e+9|0;p=e+10|0;q=e+11|0;r=e+12|0;s=e+13|0;t=e+14|0;u=e+15|0;v=e+16|0;w=e+17|0;x=e+18|0;y=e+19|0;z=e+20|0;A=e+21|0;B=e+22|0;C=e+23|0;D=e+24|0;E=e+25|0;F=e+26|0;G=e+27|0;H=e+28|0;I=e+29|0;J=e+30|0;b=g;g=2;while(1){a[b+1>>0]=a[b+e>>0]|0;a[b+2>>0]=a[b+h>>0]|0;a[b+3>>0]=a[b+f>>0]|0;a[b+4>>0]=a[b+i>>0]|0;a[b+5>>0]=a[b+j>>0]|0;a[b+6>>0]=a[b+k>>0]|0;a[b+7>>0]=a[b+l>>0]|0;a[b+8>>0]=a[b+m>>0]|0;a[b+9>>0]=a[b+n>>0]|0;a[b+10>>0]=a[b+o>>0]|0;a[b+11>>0]=a[b+p>>0]|0;a[b+12>>0]=a[b+q>>0]|0;a[b+13>>0]=a[b+r>>0]|0;a[b+14>>0]=a[b+s>>0]|0;a[b+15>>0]=a[b+t>>0]|0;a[b+16>>0]=a[b+u>>0]|0;a[b+17>>0]=a[b+v>>0]|0;a[b+18>>0]=a[b+w>>0]|0;a[b+19>>0]=a[b+x>>0]|0;a[b+20>>0]=a[b+y>>0]|0;a[b+21>>0]=a[b+z>>0]|0;a[b+22>>0]=a[b+A>>0]|0;a[b+23>>0]=a[b+B>>0]|0;a[b+24>>0]=a[b+C>>0]|0;a[b+25>>0]=a[b+D>>0]|0;a[b+26>>0]=a[b+E>>0]|0;a[b+27>>0]=a[b+F>>0]|0;a[b+28>>0]=a[b+G>>0]|0;a[b+29>>0]=a[b+H>>0]|0;a[b+30>>0]=a[b+I>>0]|0;a[b+31>>0]=a[b+J>>0]|0;g=g+1|0;if((g|0)==32)break;else b=b+c|0}return}function Bd(b,f,g,h){b=b|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0;i=L;L=L+64|0;j=i;m=d[h+5>>0]|0;k=d[h+6>>0]|0;a[j>>0]=(m+2+(k<<1)+(d[h+7>>0]|0)|0)>>>2;l=d[h+4>>0]|0;a[j+1>>0]=(l+2+(m<<1)+k|0)>>>2;k=d[h+3>>0]|0;a[j+2>>0]=(k+2+(l<<1)+m|0)>>>2;m=d[h+2>>0]|0;a[j+3>>0]=(m+2+(k<<1)+l|0)>>>2;l=d[h+1>>0]|0;a[j+4>>0]=(l+2+(m<<1)+k|0)>>>2;k=d[h>>0]|0;n=k+2|0;a[j+5>>0]=(n+(l<<1)+m|0)>>>2;m=d[g+-1>>0]|0;h=m+2|0;a[j+6>>0]=((k<<1)+h+l|0)>>>2;l=d[g>>0]|0;k=j+7|0;a[k>>0]=(n+(m<<1)+l|0)>>>2;m=d[g+1>>0]|0;a[j+8>>0]=((l<<1)+h+m|0)>>>2;h=d[g+2>>0]|0;a[j+9>>0]=(l+2+(m<<1)+h|0)>>>2;l=d[g+3>>0]|0;a[j+10>>0]=(m+2+(h<<1)+l|0)>>>2;m=d[g+4>>0]|0;a[j+11>>0]=(h+2+(l<<1)+m|0)>>>2;h=d[g+5>>0]|0;a[j+12>>0]=(l+2+(m<<1)+h|0)>>>2;l=d[g+6>>0]|0;a[j+13>>0]=(m+2+(h<<1)+l|0)>>>2;a[j+14>>0]=(h+2+(l<<1)+(d[g+7>>0]|0)|0)>>>2;g=k;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;k=k+4|0;k=d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24;l=b;h=l;a[h>>0]=g;a[h+1>>0]=g>>8;a[h+2>>0]=g>>16;a[h+3>>0]=g>>24;l=l+4|0;a[l>>0]=k;a[l+1>>0]=k>>8;a[l+2>>0]=k>>16;a[l+3>>0]=k>>24;l=j+6|0;k=l;k=e[k>>1]|e[k+2>>1]<<16;l=l+4|0;l=e[l>>1]|e[l+2>>1]<<16;h=b+f|0;g=h;a[g>>0]=k;a[g+1>>0]=k>>8;a[g+2>>0]=k>>16;a[g+3>>0]=k>>24;h=h+4|0;a[h>>0]=l;a[h+1>>0]=l>>8;a[h+2>>0]=l>>16;a[h+3>>0]=l>>24;h=j+5|0;l=h;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;h=h+4|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;g=b+(f<<1)|0;k=g;a[k>>0]=l;a[k+1>>0]=l>>8;a[k+2>>0]=l>>16;a[k+3>>0]=l>>24;g=g+4|0;a[g>>0]=h;a[g+1>>0]=h>>8;a[g+2>>0]=h>>16;a[g+3>>0]=h>>24;g=j+4|0;h=c[g>>2]|0;g=c[g+4>>2]|0;k=b+(f*3|0)|0;l=k;a[l>>0]=h;a[l+1>>0]=h>>8;a[l+2>>0]=h>>16;a[l+3>>0]=h>>24;k=k+4|0;a[k>>0]=g;a[k+1>>0]=g>>8;a[k+2>>0]=g>>16;a[k+3>>0]=g>>24;k=j+3|0;g=k;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;k=k+4|0;k=d[k>>0]|d[k+1>>0]<<8|d[k+2>>0]<<16|d[k+3>>0]<<24;l=b+(f<<2)|0;h=l;a[h>>0]=g;a[h+1>>0]=g>>8;a[h+2>>0]=g>>16;a[h+3>>0]=g>>24;l=l+4|0;a[l>>0]=k;a[l+1>>0]=k>>8;a[l+2>>0]=k>>16;a[l+3>>0]=k>>24;l=j+2|0;k=l;k=e[k>>1]|e[k+2>>1]<<16;l=l+4|0;l=e[l>>1]|e[l+2>>1]<<16;h=b+(f*5|0)|0;g=h;a[g>>0]=k;a[g+1>>0]=k>>8;a[g+2>>0]=k>>16;a[g+3>>0]=k>>24;h=h+4|0;a[h>>0]=l;a[h+1>>0]=l>>8;a[h+2>>0]=l>>16;a[h+3>>0]=l>>24;h=j+1|0;l=h;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;h=h+4|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;g=b+(f*6|0)|0;k=g;a[k>>0]=l;a[k+1>>0]=l>>8;a[k+2>>0]=l>>16;a[k+3>>0]=l>>24;g=g+4|0;a[g>>0]=h;a[g+1>>0]=h>>8;a[g+2>>0]=h>>16;a[g+3>>0]=h>>24;g=j;j=c[g>>2]|0;g=c[g+4>>2]|0;h=b+(f*7|0)|0;f=h;a[f>>0]=j;a[f+1>>0]=j>>8;a[f+2>>0]=j>>16;a[f+3>>0]=j>>24;h=h+4|0;a[h>>0]=g;a[h+1>>0]=g>>8;a[h+2>>0]=g>>16;a[h+3>>0]=g>>24;L=i;return}function Cd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;i=L;L=L+64|0;h=i;k=d[f+13>>0]|0;j=d[f+14>>0]|0;a[h>>0]=(k+2+(j<<1)+(d[f+15>>0]|0)|0)>>>2;l=d[f+12>>0]|0;a[h+1>>0]=(l+2+(k<<1)+j|0)>>>2;j=d[f+11>>0]|0;a[h+2>>0]=(j+2+(l<<1)+k|0)>>>2;k=d[f+10>>0]|0;a[h+3>>0]=(k+2+(j<<1)+l|0)>>>2;l=d[f+9>>0]|0;a[h+4>>0]=(l+2+(k<<1)+j|0)>>>2;j=d[f+8>>0]|0;a[h+5>>0]=(j+2+(l<<1)+k|0)>>>2;k=d[f+7>>0]|0;a[h+6>>0]=(k+2+(j<<1)+l|0)>>>2;l=d[f+6>>0]|0;a[h+7>>0]=(l+2+(k<<1)+j|0)>>>2;j=d[f+5>>0]|0;a[h+8>>0]=(j+2+(l<<1)+k|0)>>>2;k=d[f+4>>0]|0;a[h+9>>0]=(k+2+(j<<1)+l|0)>>>2;l=d[f+3>>0]|0;a[h+10>>0]=(l+2+(k<<1)+j|0)>>>2;j=d[f+2>>0]|0;a[h+11>>0]=(j+2+(l<<1)+k|0)>>>2;k=d[f+1>>0]|0;a[h+12>>0]=(k+2+(j<<1)+l|0)>>>2;f=d[f>>0]|0;l=f+2|0;a[h+13>>0]=(l+(k<<1)+j|0)>>>2;j=d[e+-1>>0]|0;g=j+2|0;a[h+14>>0]=((f<<1)+g+k|0)>>>2;k=d[e>>0]|0;f=h+15|0;a[f>>0]=(l+(j<<1)+k|0)>>>2;j=d[e+1>>0]|0;a[h+16>>0]=((k<<1)+g+j|0)>>>2;g=d[e+2>>0]|0;a[h+17>>0]=(k+2+(j<<1)+g|0)>>>2;k=d[e+3>>0]|0;a[h+18>>0]=(j+2+(g<<1)+k|0)>>>2;j=d[e+4>>0]|0;a[h+19>>0]=(g+2+(k<<1)+j|0)>>>2;g=d[e+5>>0]|0;a[h+20>>0]=(k+2+(j<<1)+g|0)>>>2;k=d[e+6>>0]|0;a[h+21>>0]=(j+2+(g<<1)+k|0)>>>2;j=d[e+7>>0]|0;a[h+22>>0]=(g+2+(k<<1)+j|0)>>>2;g=d[e+8>>0]|0;a[h+23>>0]=(k+2+(j<<1)+g|0)>>>2;k=d[e+9>>0]|0;a[h+24>>0]=(j+2+(g<<1)+k|0)>>>2;j=d[e+10>>0]|0;a[h+25>>0]=(g+2+(k<<1)+j|0)>>>2;g=d[e+11>>0]|0;a[h+26>>0]=(k+2+(j<<1)+g|0)>>>2;k=d[e+12>>0]|0;a[h+27>>0]=(j+2+(g<<1)+k|0)>>>2;j=d[e+13>>0]|0;a[h+28>>0]=(g+2+(k<<1)+j|0)>>>2;g=d[e+14>>0]|0;a[h+29>>0]=(k+2+(j<<1)+g|0)>>>2;a[h+30>>0]=(j+2+(g<<1)+(d[e+15>>0]|0)|0)>>>2;e=b;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+c|0;f=h+14|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c<<1)|0;f=h+13|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*3|0)|0;f=h+12|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c<<2)|0;f=h+11|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*5|0)|0;f=h+10|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*6|0)|0;f=h+9|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*7|0)|0;f=h+8|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c<<3)|0;f=h+7|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*9|0)|0;f=h+6|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*10|0)|0;f=h+5|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*11|0)|0;f=h+4|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*12|0)|0;f=h+3|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*13|0)|0;f=h+2|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*14|0)|0;f=h+1|0;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));e=b+(c*15|0)|0;f=h;g=e+16|0;do{a[e>>0]=a[f>>0]|0;e=e+1|0;f=f+1|0}while((e|0)<(g|0));L=i;return}function Dd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;l=L;L=L+64|0;k=l;g=0;h=0;while(1){a[k+h>>0]=((d[f+(g+29)>>0]|0)+2+((d[f+(30-h)>>0]|0)<<1)+(d[f+(g+31)>>0]|0)|0)>>>2;i=h+1|0;if((i|0)==30)break;else{g=~h;h=i}}m=d[e+-1>>0]|0;n=d[f>>0]|0;h=m+2|0;a[k+30>>0]=((n<<1)+h+(d[f+1>>0]|0)|0)>>>2;i=a[e>>0]|0;j=i&255;g=k+31|0;a[g>>0]=(n+2+(m<<1)+j|0)>>>2;f=a[e+1>>0]|0;a[k+32>>0]=((j<<1)+h+(f&255)|0)>>>2;h=0;while(1){j=a[e+(h+2)>>0]|0;a[k+(h+33)>>0]=((i&255)+2+((f&255)<<1)+(j&255)|0)>>>2;h=h+1|0;if((h|0)==30)break;else{i=f;f=j}}i=b;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+c|0;g=k+30|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c<<1)|0;g=k+29|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*3|0)|0;g=k+28|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c<<2)|0;g=k+27|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*5|0)|0;g=k+26|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*6|0)|0;g=k+25|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*7|0)|0;g=k+24|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c<<3)|0;g=k+23|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*9|0)|0;g=k+22|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*10|0)|0;g=k+21|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*11|0)|0;g=k+20|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*12|0)|0;g=k+19|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*13|0)|0;g=k+18|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*14|0)|0;g=k+17|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*15|0)|0;g=k+16|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c<<4)|0;g=k+15|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*17|0)|0;g=k+14|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*18|0)|0;g=k+13|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*19|0)|0;g=k+12|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*20|0)|0;g=k+11|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*21|0)|0;g=k+10|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*22|0)|0;g=k+9|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*23|0)|0;g=k+8|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*24|0)|0;g=k+7|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*25|0)|0;g=k+6|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*26|0)|0;g=k+5|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*27|0)|0;g=k+4|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*28|0)|0;g=k+3|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*29|0)|0;g=k+2|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*30|0)|0;g=k+1|0;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));i=b+(c*31|0)|0;g=k;h=i+32|0;do{a[i>>0]=a[g>>0]|0;i=i+1|0;g=g+1|0}while((i|0)<(h|0));L=l;return}function Ed(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;Fd(a,b,8,c,d);return}function Fd(b,c,e,f,g){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0;i=f+-1|0;a[b>>0]=((d[i>>0]|0)+1+(d[g>>0]|0)|0)>>>1;l=(e|0)>1;if(l){h=1;do{a[b+(h*c|0)>>0]=((d[g+(h+-1)>>0]|0)+1+(d[g+h>>0]|0)|0)>>>1;h=h+1|0}while((h|0)!=(e|0))}j=b+1|0;a[j>>0]=((d[g>>0]|0)+2+((d[i>>0]|0)<<1)+(d[f>>0]|0)|0)>>>2;a[j+c>>0]=((d[i>>0]|0)+2+((d[g>>0]|0)<<1)+(d[g+1>>0]|0)|0)>>>2;k=(e|0)>2;if(k){h=2;do{a[j+(h*c|0)>>0]=((d[g+(h+-2)>>0]|0)+2+((d[g+(h+-1)>>0]|0)<<1)+(d[g+h>>0]|0)|0)>>>2;h=h+1|0}while((h|0)!=(e|0));i=b+2|0;j=e+-2|0;if(k){h=0;do{b=h;h=h+1|0;a[i+b>>0]=((d[f+(b+-1)>>0]|0)+2+((d[f+b>>0]|0)<<1)+(d[f+h>>0]|0)|0)>>>2}while((h|0)!=(j|0));k=j}else k=j}else{k=e+-2|0;i=b+2|0}if(!l)return;g=-2-c|0;if((e|0)==2)return;j=1;do{i=i+c|0;h=0;do{a[i+h>>0]=a[i+(g+h)>>0]|0;h=h+1|0}while((h|0)<(k|0));j=j+1|0}while((j|0)!=(e|0));return}function Gd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;Fd(a,b,16,c,d);return}function Hd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;Fd(a,b,32,c,d);return}function Id(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;f=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;a[b>>0]=f;a[b+1>>0]=f>>8;a[b+2>>0]=f>>16;a[b+3>>0]=f>>24;f=b+c|0;b=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;a[f>>0]=b;a[f+1>>0]=b>>8;a[f+2>>0]=b>>16;a[f+3>>0]=b>>24;f=f+c|0;b=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;a[f>>0]=b;a[f+1>>0]=b>>8;a[f+2>>0]=b>>16;a[f+3>>0]=b>>24;f=f+c|0;e=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;return}function Jd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0;i=e;g=i;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;f=b;h=f;a[h>>0]=g;a[h+1>>0]=g>>8;a[h+2>>0]=g>>16;a[h+3>>0]=g>>24;f=f+4|0;a[f>>0]=i;a[f+1>>0]=i>>8;a[f+2>>0]=i>>16;a[f+3>>0]=i>>24;f=b+c|0;i=e;h=i;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=f;b=g;a[b>>0]=h;a[b+1>>0]=h>>8;a[b+2>>0]=h>>16;a[b+3>>0]=h>>24;g=g+4|0;a[g>>0]=i;a[g+1>>0]=i>>8;a[g+2>>0]=i>>16;a[g+3>>0]=i>>24;f=f+c|0;g=e;i=g;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;b=f;h=b;a[h>>0]=i;a[h+1>>0]=i>>8;a[h+2>>0]=i>>16;a[h+3>>0]=i>>24;b=b+4|0;a[b>>0]=g;a[b+1>>0]=g>>8;a[b+2>>0]=g>>16;a[b+3>>0]=g>>24;f=f+c|0;b=e;g=b;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;b=b+4|0;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;h=f;i=h;a[i>>0]=g;a[i+1>>0]=g>>8;a[i+2>>0]=g>>16;a[i+3>>0]=g>>24;h=h+4|0;a[h>>0]=b;a[h+1>>0]=b>>8;a[h+2>>0]=b>>16;a[h+3>>0]=b>>24;f=f+c|0;h=e;b=h;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;h=h+4|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;i=f;g=i;a[g>>0]=b;a[g+1>>0]=b>>8;a[g+2>>0]=b>>16;a[g+3>>0]=b>>24;i=i+4|0;a[i>>0]=h;a[i+1>>0]=h>>8;a[i+2>>0]=h>>16;a[i+3>>0]=h>>24;f=f+c|0;i=e;h=i;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=f;b=g;a[b>>0]=h;a[b+1>>0]=h>>8;a[b+2>>0]=h>>16;a[b+3>>0]=h>>24;g=g+4|0;a[g>>0]=i;a[g+1>>0]=i>>8;a[g+2>>0]=i>>16;a[g+3>>0]=i>>24;f=f+c|0;g=e;i=g;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;b=f;h=b;a[h>>0]=i;a[h+1>>0]=i>>8;a[h+2>>0]=i>>16;a[h+3>>0]=i>>24;b=b+4|0;a[b>>0]=g;a[b+1>>0]=g>>8;a[b+2>>0]=g>>16;a[b+3>>0]=g>>24;b=e;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;e=e+4|0;e=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;f=f+c|0;c=f;a[c>>0]=b;a[c+1>>0]=b>>8;a[c+2>>0]=b>>16;a[c+3>>0]=b>>24;f=f+4|0;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;return}function Kd(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0;f=b;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=b+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));f=e+c|0;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}function Ld(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0;f=b;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=b+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));f=e+c|0;g=d;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}function Md(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;kg(b|0,a[e>>0]|0,4)|0;d=b+c|0;kg(d|0,a[e+1>>0]|0,4)|0;d=d+c|0;kg(d|0,a[e+2>>0]|0,4)|0;kg(d+c|0,a[e+3>>0]|0,4)|0;return}function Nd(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;kg(b|0,a[e>>0]|0,8)|0;d=b+c|0;kg(d|0,a[e+1>>0]|0,8)|0;d=d+c|0;kg(d|0,a[e+2>>0]|0,8)|0;d=d+c|0;kg(d|0,a[e+3>>0]|0,8)|0;d=d+c|0;kg(d|0,a[e+4>>0]|0,8)|0;d=d+c|0;kg(d|0,a[e+5>>0]|0,8)|0;d=d+c|0;kg(d|0,a[e+6>>0]|0,8)|0;kg(d+c|0,a[e+7>>0]|0,8)|0;return}function Od(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;kg(b|0,a[e>>0]|0,16)|0;d=b+c|0;kg(d|0,a[e+1>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+2>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+3>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+4>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+5>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+6>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+7>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+8>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+9>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+10>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+11>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+12>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+13>>0]|0,16)|0;d=d+c|0;kg(d|0,a[e+14>>0]|0,16)|0;kg(d+c|0,a[e+15>>0]|0,16)|0;return}function Pd(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;kg(b|0,a[e>>0]|0,32)|0;d=b+c|0;kg(d|0,a[e+1>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+2>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+3>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+4>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+5>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+6>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+7>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+8>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+9>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+10>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+11>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+12>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+13>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+14>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+15>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+16>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+17>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+18>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+19>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+20>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+21>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+22>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+23>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+24>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+25>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+26>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+27>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+28>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+29>>0]|0,32)|0;d=d+c|0;kg(d|0,a[e+30>>0]|0,32)|0;kg(d+c|0,a[e+31>>0]|0,32)|0;return}function Qd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;h=d[e+-1>>0]|0;j=(d[f>>0]|0)-h+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[b>>0]=(j|0)<255?j:255;j=e+1|0;i=(d[f>>0]|0)-h+(d[j>>0]|0)|0;i=(i|0)>0?i:0;a[b+1>>0]=(i|0)<255?i:255;i=e+2|0;g=(d[f>>0]|0)-h+(d[i>>0]|0)|0;g=(g|0)>0?g:0;a[b+2>>0]=(g|0)<255?g:255;g=e+3|0;k=(d[f>>0]|0)-h+(d[g>>0]|0)|0;k=(k|0)>0?k:0;a[b+3>>0]=(k|0)<255?k:255;b=b+c|0;k=f+1|0;l=(d[k>>0]|0)-h+(d[e>>0]|0)|0;l=(l|0)>0?l:0;a[b>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[j>>0]|0)|0;l=(l|0)>0?l:0;a[b+1>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[b+2>>0]=(l|0)<255?l:255;k=(d[k>>0]|0)-h+(d[g>>0]|0)|0;k=(k|0)>0?k:0;a[b+3>>0]=(k|0)<255?k:255;b=b+c|0;k=f+2|0;l=(d[k>>0]|0)-h+(d[e>>0]|0)|0;l=(l|0)>0?l:0;a[b>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[j>>0]|0)|0;l=(l|0)>0?l:0;a[b+1>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[b+2>>0]=(l|0)<255?l:255;k=(d[k>>0]|0)-h+(d[g>>0]|0)|0;k=(k|0)>0?k:0;a[b+3>>0]=(k|0)<255?k:255;c=b+c|0;f=f+3|0;e=(d[f>>0]|0)-h+(d[e>>0]|0)|0;e=(e|0)>0?e:0;a[c>>0]=(e|0)<255?e:255;e=(d[f>>0]|0)-h+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[c+1>>0]=(e|0)<255?e:255;e=(d[f>>0]|0)-h+(d[i>>0]|0)|0;e=(e|0)>0?e:0;a[c+2>>0]=(e|0)<255?e:255;f=(d[f>>0]|0)-h+(d[g>>0]|0)|0;f=(f|0)>0?f:0;a[c+3>>0]=(f|0)<255?f:255;return}function Rd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;l=d[e+-1>>0]|0;m=e+1|0;n=e+2|0;o=e+3|0;h=e+4|0;i=e+5|0;j=e+6|0;k=e+7|0;g=0;while(1){p=f+g|0;q=(d[p>>0]|0)-l+(d[e>>0]|0)|0;q=(q|0)>0?q:0;a[b>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[m>>0]|0)|0;q=(q|0)>0?q:0;a[b+1>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[n>>0]|0)|0;q=(q|0)>0?q:0;a[b+2>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[o>>0]|0)|0;q=(q|0)>0?q:0;a[b+3>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[h>>0]|0)|0;q=(q|0)>0?q:0;a[b+4>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[i>>0]|0)|0;q=(q|0)>0?q:0;a[b+5>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[j>>0]|0)|0;q=(q|0)>0?q:0;a[b+6>>0]=(q|0)<255?q:255;p=(d[p>>0]|0)-l+(d[k>>0]|0)|0;p=(p|0)>0?p:0;a[b+7>>0]=(p|0)<255?p:255;g=g+1|0;if((g|0)==8)break;else b=b+c|0}return}function Sd(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;t=d[e+-1>>0]|0;u=e+1|0;v=e+2|0;w=e+3|0;h=e+4|0;i=e+5|0;j=e+6|0;k=e+7|0;l=e+8|0;m=e+9|0;n=e+10|0;o=e+11|0;p=e+12|0;q=e+13|0;r=e+14|0;s=e+15|0;g=0;while(1){x=f+g|0;y=(d[x>>0]|0)-t+(d[e>>0]|0)|0;y=(y|0)>0?y:0;a[b>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[u>>0]|0)|0;y=(y|0)>0?y:0;a[b+1>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[v>>0]|0)|0;y=(y|0)>0?y:0;a[b+2>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[w>>0]|0)|0;y=(y|0)>0?y:0;a[b+3>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[h>>0]|0)|0;y=(y|0)>0?y:0;a[b+4>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[i>>0]|0)|0;y=(y|0)>0?y:0;a[b+5>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[j>>0]|0)|0;y=(y|0)>0?y:0;a[b+6>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[k>>0]|0)|0;y=(y|0)>0?y:0;a[b+7>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[l>>0]|0)|0;y=(y|0)>0?y:0;a[b+8>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[m>>0]|0)|0;y=(y|0)>0?y:0;a[b+9>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[n>>0]|0)|0;y=(y|0)>0?y:0;a[b+10>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[o>>0]|0)|0;y=(y|0)>0?y:0;a[b+11>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[p>>0]|0)|0;y=(y|0)>0?y:0;a[b+12>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[q>>0]|0)|0;y=(y|0)>0?y:0;a[b+13>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[r>>0]|0)|0;y=(y|0)>0?y:0;a[b+14>>0]=(y|0)<255?y:255;x=(d[x>>0]|0)-t+(d[s>>0]|0)|0;x=(x|0)>0?x:0;a[b+15>>0]=(x|0)<255?x:255;g=g+1|0;if((g|0)==16)break;else b=b+c|0}return}function Td(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;j=d[e+-1>>0]|0;i=0;while(1){h=f+i|0;g=0;do{k=(d[h>>0]|0)-j+(d[e+g>>0]|0)|0;k=(k|0)>0?k:0;a[b+g>>0]=(k|0)<255?k:255;g=g+1|0}while((g|0)!=32);i=i+1|0;if((i|0)==32)break;else b=b+c|0}return}function Ud(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;e=b+c|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;e=e+c|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;e=e+c|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;return}function Vd(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;e=b;d=e;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+4|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;e=b+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+4|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;return}function Wd(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0;e=b;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=b+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));e=d+c|0;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));return}function Xd(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0;e=b;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=b+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));e=d+c|0;f=e+32|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));return}function Yd(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[e>>0]|0)+(d[e+1>>0]|0)+(d[e+2>>0]|0)+(d[e+3>>0]|0)+2|0)>>>2&255;kg(a|0,e|0,4)|0;c=a+b|0;kg(c|0,e|0,4)|0;c=c+b|0;kg(c|0,e|0,4)|0;kg(c+b|0,e|0,4)|0;return}function Zd(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[e>>0]|0)+(d[e+1>>0]|0)+(d[e+2>>0]|0)+(d[e+3>>0]|0)+(d[e+4>>0]|0)+(d[e+5>>0]|0)+(d[e+6>>0]|0)+(d[e+7>>0]|0)+4|0)>>>3&255;kg(a|0,e|0,8)|0;c=a+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;kg(c+b|0,e|0,8)|0;return}function _d(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[e>>0]|0)+(d[e+1>>0]|0)+(d[e+2>>0]|0)+(d[e+3>>0]|0)+(d[e+4>>0]|0)+(d[e+5>>0]|0)+(d[e+6>>0]|0)+(d[e+7>>0]|0)+(d[e+8>>0]|0)+(d[e+9>>0]|0)+(d[e+10>>0]|0)+(d[e+11>>0]|0)+(d[e+12>>0]|0)+(d[e+13>>0]|0)+(d[e+14>>0]|0)+(d[e+15>>0]|0)+8|0)>>>4&255;kg(a|0,e|0,16)|0;c=a+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;kg(c+b|0,e|0,16)|0;return}function $d(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[e>>0]|0)+(d[e+1>>0]|0)+(d[e+2>>0]|0)+(d[e+3>>0]|0)+(d[e+4>>0]|0)+(d[e+5>>0]|0)+(d[e+6>>0]|0)+(d[e+7>>0]|0)+(d[e+8>>0]|0)+(d[e+9>>0]|0)+(d[e+10>>0]|0)+(d[e+11>>0]|0)+(d[e+12>>0]|0)+(d[e+13>>0]|0)+(d[e+14>>0]|0)+(d[e+15>>0]|0)+(d[e+16>>0]|0)+(d[e+17>>0]|0)+(d[e+18>>0]|0)+(d[e+19>>0]|0)+(d[e+20>>0]|0)+(d[e+21>>0]|0)+(d[e+22>>0]|0)+(d[e+23>>0]|0)+(d[e+24>>0]|0)+(d[e+25>>0]|0)+(d[e+26>>0]|0)+(d[e+27>>0]|0)+(d[e+28>>0]|0)+(d[e+29>>0]|0)+(d[e+30>>0]|0)+(d[e+31>>0]|0)+16|0)>>>5&255;kg(a|0,e|0,32)|0;c=a+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;kg(c+b|0,e|0,32)|0;return}function ae(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[c>>0]|0)+(d[c+1>>0]|0)+(d[c+2>>0]|0)+(d[c+3>>0]|0)+2|0)>>>2&255;kg(a|0,e|0,4)|0;c=a+b|0;kg(c|0,e|0,4)|0;c=c+b|0;kg(c|0,e|0,4)|0;kg(c+b|0,e|0,4)|0;return}function be(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[c>>0]|0)+(d[c+1>>0]|0)+(d[c+2>>0]|0)+(d[c+3>>0]|0)+(d[c+4>>0]|0)+(d[c+5>>0]|0)+(d[c+6>>0]|0)+(d[c+7>>0]|0)+4|0)>>>3&255;kg(a|0,e|0,8)|0;c=a+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;kg(c+b|0,e|0,8)|0;return}function ce(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[c>>0]|0)+(d[c+1>>0]|0)+(d[c+2>>0]|0)+(d[c+3>>0]|0)+(d[c+4>>0]|0)+(d[c+5>>0]|0)+(d[c+6>>0]|0)+(d[c+7>>0]|0)+(d[c+8>>0]|0)+(d[c+9>>0]|0)+(d[c+10>>0]|0)+(d[c+11>>0]|0)+(d[c+12>>0]|0)+(d[c+13>>0]|0)+(d[c+14>>0]|0)+(d[c+15>>0]|0)+8|0)>>>4&255;kg(a|0,e|0,16)|0;c=a+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;kg(c+b|0,e|0,16)|0;return}function de(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[c>>0]|0)+(d[c+1>>0]|0)+(d[c+2>>0]|0)+(d[c+3>>0]|0)+(d[c+4>>0]|0)+(d[c+5>>0]|0)+(d[c+6>>0]|0)+(d[c+7>>0]|0)+(d[c+8>>0]|0)+(d[c+9>>0]|0)+(d[c+10>>0]|0)+(d[c+11>>0]|0)+(d[c+12>>0]|0)+(d[c+13>>0]|0)+(d[c+14>>0]|0)+(d[c+15>>0]|0)+(d[c+16>>0]|0)+(d[c+17>>0]|0)+(d[c+18>>0]|0)+(d[c+19>>0]|0)+(d[c+20>>0]|0)+(d[c+21>>0]|0)+(d[c+22>>0]|0)+(d[c+23>>0]|0)+(d[c+24>>0]|0)+(d[c+25>>0]|0)+(d[c+26>>0]|0)+(d[c+27>>0]|0)+(d[c+28>>0]|0)+(d[c+29>>0]|0)+(d[c+30>>0]|0)+(d[c+31>>0]|0)+16|0)>>>5&255;kg(a|0,e|0,32)|0;c=a+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;kg(c+b|0,e|0,32)|0;return}function ee(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+4|0)/8|0)&255;kg(a|0,e|0,4)|0;c=a+b|0;kg(c|0,e|0,4)|0;c=c+b|0;kg(c|0,e|0,4)|0;kg(c+b|0,e|0,4)|0;return}function fe(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+(d[c+4>>0]|0)+(d[e+4>>0]|0)+(d[c+5>>0]|0)+(d[e+5>>0]|0)+(d[c+6>>0]|0)+(d[e+6>>0]|0)+(d[c+7>>0]|0)+(d[e+7>>0]|0)+8|0)/16|0)&255;kg(a|0,e|0,8)|0;c=a+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;c=c+b|0;kg(c|0,e|0,8)|0;kg(c+b|0,e|0,8)|0;return}function ge(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+(d[c+4>>0]|0)+(d[e+4>>0]|0)+(d[c+5>>0]|0)+(d[e+5>>0]|0)+(d[c+6>>0]|0)+(d[e+6>>0]|0)+(d[c+7>>0]|0)+(d[e+7>>0]|0)+(d[c+8>>0]|0)+(d[e+8>>0]|0)+(d[c+9>>0]|0)+(d[e+9>>0]|0)+(d[c+10>>0]|0)+(d[e+10>>0]|0)+(d[c+11>>0]|0)+(d[e+11>>0]|0)+(d[c+12>>0]|0)+(d[e+12>>0]|0)+(d[c+13>>0]|0)+(d[e+13>>0]|0)+(d[c+14>>0]|0)+(d[e+14>>0]|0)+(d[c+15>>0]|0)+(d[e+15>>0]|0)+16|0)/32|0)&255;kg(a|0,e|0,16)|0;c=a+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;c=c+b|0;kg(c|0,e|0,16)|0;kg(c+b|0,e|0,16)|0;return}function he(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+(d[c+4>>0]|0)+(d[e+4>>0]|0)+(d[c+5>>0]|0)+(d[e+5>>0]|0)+(d[c+6>>0]|0)+(d[e+6>>0]|0)+(d[c+7>>0]|0)+(d[e+7>>0]|0)+(d[c+8>>0]|0)+(d[e+8>>0]|0)+(d[c+9>>0]|0)+(d[e+9>>0]|0)+(d[c+10>>0]|0)+(d[e+10>>0]|0)+(d[c+11>>0]|0)+(d[e+11>>0]|0)+(d[c+12>>0]|0)+(d[e+12>>0]|0)+(d[c+13>>0]|0)+(d[e+13>>0]|0)+(d[c+14>>0]|0)+(d[e+14>>0]|0)+(d[c+15>>0]|0)+(d[e+15>>0]|0)+(d[c+16>>0]|0)+(d[e+16>>0]|0)+(d[c+17>>0]|0)+(d[e+17>>0]|0)+(d[c+18>>0]|0)+(d[e+18>>0]|0)+(d[c+19>>0]|0)+(d[e+19>>0]|0)+(d[c+20>>0]|0)+(d[e+20>>0]|0)+(d[c+21>>0]|0)+(d[e+21>>0]|0)+(d[c+22>>0]|0)+(d[e+22>>0]|0)+(d[c+23>>0]|0)+(d[e+23>>0]|0)+(d[c+24>>0]|0)+(d[e+24>>0]|0)+(d[c+25>>0]|0)+(d[e+25>>0]|0)+(d[c+26>>0]|0)+(d[e+26>>0]|0)+(d[c+27>>0]|0)+(d[e+27>>0]|0)+(d[c+28>>0]|0)+(d[e+28>>0]|0)+(d[c+29>>0]|0)+(d[e+29>>0]|0)+(d[c+30>>0]|0)+(d[e+30>>0]|0)+(d[c+31>>0]|0)+(d[e+31>>0]|0)+32|0)/64|0)&255;kg(a|0,e|0,32)|0;c=a+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;c=c+b|0;kg(c|0,e|0,32)|0;kg(c+b|0,e|0,32)|0;return}function ie(c,e,f,g,h,i,j,k,l,m,n){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0;if(!((m|0)>0&(n|0)>0))return;o=c+-3|0;c=f;l=0;while(1){k=i;f=0;while(1){p=o+(k>>4)|0;q=k&15;p=((b[h+(q<<4)>>1]|0)*(d[p>>0]|0)|0)+64+((b[h+(q<<4)+2>>1]|0)*(d[p+1>>0]|0)|0)+((b[h+(q<<4)+4>>1]|0)*(d[p+2>>0]|0)|0)+((b[h+(q<<4)+6>>1]|0)*(d[p+3>>0]|0)|0)+((b[h+(q<<4)+8>>1]|0)*(d[p+4>>0]|0)|0)+((b[h+(q<<4)+10>>1]|0)*(d[p+5>>0]|0)|0)+((b[h+(q<<4)+12>>1]|0)*(d[p+6>>0]|0)|0)+((b[h+(q<<4)+14>>1]|0)*(d[p+7>>0]|0)|0)>>7;p=(p|0)>0?p:0;a[c+f>>0]=(p|0)<255?p:255;f=f+1|0;if((f|0)==(m|0))break;else k=k+j|0}l=l+1|0;if((l|0)==(n|0))break;else{o=o+e|0;c=c+g|0}}return}function je(c,e,f,g,h,i,j,k,l,m,n){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0;if(!((m|0)>0&(n|0)>0))return;o=c+-3|0;c=f;l=0;while(1){k=i;f=0;while(1){q=o+(k>>4)|0;r=k&15;p=c+f|0;q=((b[h+(r<<4)>>1]|0)*(d[q>>0]|0)|0)+64+((b[h+(r<<4)+2>>1]|0)*(d[q+1>>0]|0)|0)+((b[h+(r<<4)+4>>1]|0)*(d[q+2>>0]|0)|0)+((b[h+(r<<4)+6>>1]|0)*(d[q+3>>0]|0)|0)+((b[h+(r<<4)+8>>1]|0)*(d[q+4>>0]|0)|0)+((b[h+(r<<4)+10>>1]|0)*(d[q+5>>0]|0)|0)+((b[h+(r<<4)+12>>1]|0)*(d[q+6>>0]|0)|0)+((b[h+(r<<4)+14>>1]|0)*(d[q+7>>0]|0)|0)>>7;q=(q|0)>0?q:0;a[p>>0]=((d[p>>0]|0)+1+((q|0)<255?q:255)|0)>>>1;f=f+1|0;if((f|0)==(m|0))break;else k=k+j|0}l=l+1|0;if((l|0)==(n|0))break;else{o=o+e|0;c=c+g|0}}return}function ke(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;le(a,b,c,d,e,h,i,j,k);return}function le(c,e,f,g,h,i,j,k,l){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;if(!((k|0)>0&(l|0)>0))return;p=e<<1;q=e*3|0;r=e<<2;s=e*5|0;t=e*6|0;u=e*7|0;o=c+(e*-3|0)|0;n=0;while(1){c=i;m=0;while(1){v=o+((c>>4)*e|0)|0;w=c&15;v=((b[h+(w<<4)+14>>1]|0)*(d[v+u>>0]|0)|0)+(((b[h+(w<<4)+12>>1]|0)*(d[v+t>>0]|0)|0)+(((b[h+(w<<4)+10>>1]|0)*(d[v+s>>0]|0)|0)+(((b[h+(w<<4)+8>>1]|0)*(d[v+r>>0]|0)|0)+(((b[h+(w<<4)+6>>1]|0)*(d[v+q>>0]|0)|0)+(((b[h+(w<<4)+4>>1]|0)*(d[v+p>>0]|0)|0)+(((b[h+(w<<4)+2>>1]|0)*(d[v+e>>0]|0)|0)+((b[h+(w<<4)>>1]|0)*(d[v>>0]|0)|0)))))))+64>>7;v=(v|0)>0?v:0;a[f+(m*g|0)>>0]=(v|0)<255?v:255;m=m+1|0;if((m|0)==(l|0))break;else c=c+j|0}n=n+1|0;if((n|0)==(k|0))break;else{o=o+1|0;f=f+1|0}}return}function me(c,e,f,g,h,i,j,k,l,m,n){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;if(!((m|0)>0&(n|0)>0))return;p=e<<1;q=e*3|0;r=e<<2;s=e*5|0;t=e*6|0;u=e*7|0;o=c+(e*-3|0)|0;i=f;f=0;while(1){j=k;c=0;while(1){w=o+((j>>4)*e|0)|0;x=j&15;v=i+(c*g|0)|0;w=((b[h+(x<<4)>>1]|0)*(d[w>>0]|0)|0)+64+((b[h+(x<<4)+2>>1]|0)*(d[w+e>>0]|0)|0)+((b[h+(x<<4)+4>>1]|0)*(d[w+p>>0]|0)|0)+((b[h+(x<<4)+6>>1]|0)*(d[w+q>>0]|0)|0)+((b[h+(x<<4)+8>>1]|0)*(d[w+r>>0]|0)|0)+((b[h+(x<<4)+10>>1]|0)*(d[w+s>>0]|0)|0)+((b[h+(x<<4)+12>>1]|0)*(d[w+t>>0]|0)|0)+((b[h+(x<<4)+14>>1]|0)*(d[w+u>>0]|0)|0)>>7;w=(w|0)>0?w:0;a[v>>0]=((d[v>>0]|0)+1+((w|0)<255?w:255)|0)>>>1;c=c+1|0;if((c|0)==(n|0))break;else j=j+l|0}f=f+1|0;if((f|0)==(m|0))break;else{o=o+1|0;i=i+1|0}}return}function ne(c,e,f,g,h,i,j,k,l,m,n){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;v=L;L=L+8640|0;q=v;o=((n+-1|0)*l|0)+k|0;u=(m|0)>0;if(u&(o|0)>-113){t=(o>>4)+7|0;o=c+(e*-3|0)+-3|0;c=q;r=0;while(1){p=i;s=0;while(1){w=o+(p>>4)|0;x=p&15;w=((b[h+(x<<4)>>1]|0)*(d[w>>0]|0)|0)+64+((b[h+(x<<4)+2>>1]|0)*(d[w+1>>0]|0)|0)+((b[h+(x<<4)+4>>1]|0)*(d[w+2>>0]|0)|0)+((b[h+(x<<4)+6>>1]|0)*(d[w+3>>0]|0)|0)+((b[h+(x<<4)+8>>1]|0)*(d[w+4>>0]|0)|0)+((b[h+(x<<4)+10>>1]|0)*(d[w+5>>0]|0)|0)+((b[h+(x<<4)+12>>1]|0)*(d[w+6>>0]|0)|0)+((b[h+(x<<4)+14>>1]|0)*(d[w+7>>0]|0)|0)>>7;w=(w|0)>0?w:0;a[c+s>>0]=(w|0)<255?w:255;s=s+1|0;if((s|0)==(m|0))break;else p=p+j|0}if((r|0)==(t|0))break;else{o=o+e|0;c=c+64|0;r=r+1|0}}}if(!(u&(n|0)>0)){L=v;return}p=0;while(1){o=k;c=0;while(1){x=q+(o>>4<<6)|0;w=o&15;x=((b[h+(w<<4)>>1]|0)*(d[x>>0]|0)|0)+64+((b[h+(w<<4)+2>>1]|0)*(d[x+64>>0]|0)|0)+((b[h+(w<<4)+4>>1]|0)*(d[x+128>>0]|0)|0)+((b[h+(w<<4)+6>>1]|0)*(d[x+192>>0]|0)|0)+((b[h+(w<<4)+8>>1]|0)*(d[x+256>>0]|0)|0)+((b[h+(w<<4)+10>>1]|0)*(d[x+320>>0]|0)|0)+((b[h+(w<<4)+12>>1]|0)*(d[x+384>>0]|0)|0)+((b[h+(w<<4)+14>>1]|0)*(d[x+448>>0]|0)|0)>>7;x=(x|0)>0?x:0;a[f+(c*g|0)>>0]=(x|0)<255?x:255;c=c+1|0;if((c|0)==(n|0))break;else o=o+l|0}p=p+1|0;if((p|0)==(m|0))break;else{q=q+1|0;f=f+1|0}}L=v;return}function oe(b,c,e,f,g,h,i,j,k,l,m){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0;o=L;L=L+4096|0;n=o;ne(b,c,n,64,g,h,i,j,k,l,m);if(!((l|0)>0&(m|0)>0)){L=o;return}c=0;while(1){b=0;do{k=e+b|0;a[k>>0]=((d[k>>0]|0)+1+(d[n+b>>0]|0)|0)>>>1;b=b+1|0}while((b|0)!=(l|0));c=c+1|0;if((c|0)==(m|0))break;else{n=n+64|0;e=e+f|0}}L=o;return}function pe(b,c,e,f,g,h,i,j,k,l,m){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;if(!((m|0)>0&(l|0)>0))return;h=0;while(1){g=0;do{k=e+g|0;a[k>>0]=((d[k>>0]|0)+1+(d[b+g>>0]|0)|0)>>>1;g=g+1|0}while((g|0)!=(l|0));h=h+1|0;if((h|0)==(m|0))break;else{b=b+c|0;e=e+f|0}}return}function qe(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;if((k|0)<=0)return;while(1){ig(c|0,a|0,j|0)|0;if((k|0)>1){a=a+b|0;c=c+d|0;k=k+-1|0}else break}return}function re(c,e,f,g,h,i,j,k,l,m,n){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0;if(!((m|0)>0&(n|0)>0))return;o=c+-3|0;c=f;l=0;while(1){k=i;f=0;while(1){p=o+(k>>4)|0;q=k&15;p=((b[h+(q<<4)>>1]|0)*(d[p>>0]|0)|0)+64+((b[h+(q<<4)+2>>1]|0)*(d[p+1>>0]|0)|0)+((b[h+(q<<4)+4>>1]|0)*(d[p+2>>0]|0)|0)+((b[h+(q<<4)+6>>1]|0)*(d[p+3>>0]|0)|0)+((b[h+(q<<4)+8>>1]|0)*(d[p+4>>0]|0)|0)+((b[h+(q<<4)+10>>1]|0)*(d[p+5>>0]|0)|0)+((b[h+(q<<4)+12>>1]|0)*(d[p+6>>0]|0)|0)+((b[h+(q<<4)+14>>1]|0)*(d[p+7>>0]|0)|0)>>7;p=(p|0)>0?p:0;a[c+f>>0]=(p|0)<255?p:255;f=f+1|0;if((f|0)==(m|0))break;else k=k+j|0}l=l+1|0;if((l|0)==(n|0))break;else{o=o+e|0;c=c+g|0}}return}function se(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;le(a,b,c,d,e,h,i,j,k);return}function te(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;ne(a,b,c,d,e,f,g,h,i,j,k);return}function ue(c,e,f,g,h,i,j,k,l,m,n){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;var o=0,p=0,q=0,r=0;if(!((m|0)>0&(n|0)>0))return;o=c+-3|0;c=f;l=0;while(1){k=i;f=0;while(1){q=o+(k>>4)|0;r=k&15;p=c+f|0;q=((b[h+(r<<4)>>1]|0)*(d[q>>0]|0)|0)+64+((b[h+(r<<4)+2>>1]|0)*(d[q+1>>0]|0)|0)+((b[h+(r<<4)+4>>1]|0)*(d[q+2>>0]|0)|0)+((b[h+(r<<4)+6>>1]|0)*(d[q+3>>0]|0)|0)+((b[h+(r<<4)+8>>1]|0)*(d[q+4>>0]|0)|0)+((b[h+(r<<4)+10>>1]|0)*(d[q+5>>0]|0)|0)+((b[h+(r<<4)+12>>1]|0)*(d[q+6>>0]|0)|0)+((b[h+(r<<4)+14>>1]|0)*(d[q+7>>0]|0)|0)>>7;q=(q|0)>0?q:0;a[p>>0]=((d[p>>0]|0)+1+((q|0)<255?q:255)|0)>>>1;f=f+1|0;if((f|0)==(m|0))break;else k=k+j|0}l=l+1|0;if((l|0)==(n|0))break;else{o=o+e|0;c=c+g|0}}return}function ve(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;me(a,b,c,d,e,0,0,h,i,j,k);return}function we(b,c,e,f,g,h,i,j,k,l,m){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0;o=L;L=L+4096|0;n=o;ne(b,c,n,64,g,h,i,j,k,l,m);if(!((l|0)>0&(m|0)>0)){L=o;return}c=0;while(1){b=0;do{k=e+b|0;a[k>>0]=((d[k>>0]|0)+1+(d[n+b>>0]|0)|0)>>>1;b=b+1|0}while((b|0)!=(l|0));c=c+1|0;if((c|0)==(m|0))break;else{n=n+64|0;e=e+f|0}}L=o;return}function xe(b,c,e,f,g){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;k=c*-4|0;l=c*-3|0;m=c*-2|0;n=0-c|0;o=c<<1;i=c*3|0;j=0-o|0;h=0;while(1){v=b+n|0;s=b+c|0;z=d[b+l>>0]|0;A=(d[b+k>>0]|0)-z|0;u=d[f>>0]|0;G=d[b+m>>0]|0;z=z-G|0;w=d[v>>0]|0;B=G-w|0;t=d[s>>0]|0;y=d[b>>0]|0;C=t-y|0;C=(C|0)>-1?C:0-C|0;E=d[b+o>>0]|0;D=E-t|0;E=(d[b+i>>0]|0)-E|0;F=w-y|0;G=G-t|0;p=b+j|0;q=d[p>>0]|0;x=q-w|0;r=d[g>>0]|0;r=((C|0)>(r|0)?1:(((x|0)>-1?x:0-x|0)|0)>(r|0))<<31>>31;x=q-t|0;x=(x|0)<255?x:255;x=(((x|0)>0?x:0)&r)+((y-w|0)*3|0)|0;x=(x|0)>0&(((((((G|0)>-1?G:0-G|0)|0)/2|0)+(((F|0)>-1?F:0-F|0)<<1)|0)>(d[e>>0]|0|0)|((((E|0)>-1?E:0-E|0)|0)>(u|0)|((((D|0)>-1?D:0-D|0)|0)>(u|0)|((C|0)>(u|0)|((((B|0)>-1?B:0-B|0)|0)>(u|0)|((((A|0)>-1?A:0-A|0)|0)>(u|0)?1:(((z|0)>-1?z:0-z|0)|0)>(u|0)))))))^1)?((x|0)<255?x:255):0;u=x+4|0;u=(u>>>0<255?u:255)>>>3;x=x+3|0;y=y-u|0;y=(y|0)<255?y:255;a[b>>0]=(y|0)>0?y:0;w=((x>>>0<255?x:255)>>>3)+w|0;w=w>>>0<255?w:255;a[v>>0]=(w|0)>0?w:0;r=(u+1|0)>>>1&~r;t=t-r|0;t=(t|0)<255?t:255;a[s>>0]=(t|0)>0?t:0;q=r+q|0;q=q>>>0<255?q:255;a[p>>0]=(q|0)>0?q:0;h=h+1|0;if((h|0)==8)break;else b=b+1|0}return}function ye(a,b,c,d,e,f,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;xe(a,b,c,d,e);xe(a+8|0,b,f,g,h);return}function ze(b,c,e,f,g){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0;if((c|0)==1){h=0;i=b;b=a[b>>0]|0;while(1){j=i+-2|0;p=i+-1|0;m=i+1|0;t=d[i+-3>>0]|0;u=(d[i+-4>>0]|0)-t|0;o=d[f>>0]|0;k=d[j>>0]|0;t=t-k|0;q=d[p>>0]|0;v=k-q|0;v=(v|0)>-1?v:0-v|0;n=d[m>>0]|0;s=b&255;w=n-s|0;w=(w|0)>-1?w:0-w|0;y=d[i+2>>0]|0;x=y-n|0;y=(d[i+3>>0]|0)-y|0;z=q-s|0;A=k-n|0;l=d[g>>0]|0;l=((v|0)>(l|0)|(w|0)>(l|0))<<31>>31;r=(A|0)<255?A:255;r=(((r|0)>0?r:0)&l)+((s-q|0)*3|0)|0;r=(r|0)>0&(((((((A|0)>-1?A:0-A|0)|0)/2|0)+(((z|0)>-1?z:0-z|0)<<1)|0)>(d[e>>0]|0|0)|((((y|0)>-1?y:0-y|0)|0)>(o|0)|((((x|0)>-1?x:0-x|0)|0)>(o|0)|((w|0)>(o|0)|((v|0)>(o|0)|((((u|0)>-1?u:0-u|0)|0)>(o|0)?1:(((t|0)>-1?t:0-t|0)|0)>(o|0)))))))^1)?((r|0)<255?r:255):0;o=r+4|0;o=(o>>>0<255?o:255)>>>3;r=r+3|0;s=s-o|0;s=(s|0)<255?s:255;a[i>>0]=(s|0)>0?s:0;q=((r>>>0<255?r:255)>>>3)+q|0;q=q>>>0<255?q:255;a[p>>0]=(q|0)>0?q:0;l=(o+1|0)>>>1&~l;n=n-l|0;n=(n|0)<255?n:255;b=((n|0)>0?n:0)&255;a[m>>0]=b;k=l+k|0;k=k>>>0<255?k:255;a[j>>0]=(k|0)>0?k:0;h=h+1|0;if((h|0)==8)break;else i=i+c|0}return}else{h=0;while(1){A=b+-2|0;u=b+-1|0;x=b+1|0;q=d[b+-3>>0]|0;p=(d[b+-4>>0]|0)-q|0;v=d[f>>0]|0;z=d[A>>0]|0;q=q-z|0;t=d[u>>0]|0;o=z-t|0;o=(o|0)>-1?o:0-o|0;w=d[x>>0]|0;r=d[b>>0]|0;n=w-r|0;n=(n|0)>-1?n:0-n|0;l=d[b+2>>0]|0;m=l-w|0;l=(d[b+3>>0]|0)-l|0;k=t-r|0;j=z-w|0;y=d[g>>0]|0;y=((o|0)>(y|0)|(n|0)>(y|0))<<31>>31;s=(j|0)<255?j:255;s=(((s|0)>0?s:0)&y)+((r-t|0)*3|0)|0;s=(s|0)>0&(((((((j|0)>-1?j:0-j|0)|0)/2|0)+(((k|0)>-1?k:0-k|0)<<1)|0)>(d[e>>0]|0|0)|((((l|0)>-1?l:0-l|0)|0)>(v|0)|((((m|0)>-1?m:0-m|0)|0)>(v|0)|((n|0)>(v|0)|((o|0)>(v|0)|((((p|0)>-1?p:0-p|0)|0)>(v|0)?1:(((q|0)>-1?q:0-q|0)|0)>(v|0)))))))^1)?((s|0)<255?s:255):0;v=s+4|0;v=(v>>>0<255?v:255)>>>3;s=s+3|0;r=r-v|0;r=(r|0)<255?r:255;a[b>>0]=(r|0)>0?r:0;t=((s>>>0<255?s:255)>>>3)+t|0;t=t>>>0<255?t:255;a[u>>0]=(t|0)>0?t:0;y=(v+1|0)>>>1&~y;w=w-y|0;w=(w|0)<255?w:255;a[x>>0]=(w|0)>0?w:0;z=y+z|0;z=z>>>0<255?z:255;a[A>>0]=(z|0)>0?z:0;h=h+1|0;if((h|0)==8)break;else b=b+c|0}return}}function Ae(a,b,c,d,e,f,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;ze(a,b,c,d,e);ze(a+(b<<3)|0,b,f,g,h);return}function Be(b,c,e,f,g){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0;y=c*-4|0;z=c*-3|0;A=c*-2|0;B=0-c|0;C=c<<1;u=c*3|0;v=0-u|0;w=0-C|0;x=0-(c<<2)|0;t=0;while(1){h=b+B|0;j=b+c|0;i=b+C|0;G=d[b+y>>0]|0;E=d[b+z>>0]|0;H=G-E|0;n=d[f>>0]|0;J=d[b+A>>0]|0;F=E-J|0;k=d[h>>0]|0;D=J-k|0;D=(D|0)>-1?D:0-D|0;q=d[j>>0]|0;l=d[b>>0]|0;m=q-l|0;m=(m|0)>-1?m:0-m|0;r=d[i>>0]|0;o=r-q|0;s=d[b+u>>0]|0;p=s-r|0;I=k-l|0;J=J-q|0;n=(((((J|0)>-1?J:0-J|0)|0)/2|0)+(((I|0)>-1?I:0-I|0)<<1)|0)>(d[e>>0]|0|0)|((((p|0)>-1?p:0-p|0)|0)>(n|0)|((((o|0)>-1?o:0-o|0)|0)>(n|0)|((m|0)>(n|0)|((D|0)>(n|0)|((((H|0)>-1?H:0-H|0)|0)>(n|0)?1:(((F|0)>-1?F:0-F|0)|0)>(n|0))))));E=E-k|0;F=r-l|0;G=G-k|0;H=s-l|0;o=b+v|0;p=b+w|0;if((((H|0)>-1?H:0-H|0)|0)>1|((((G|0)>-1?G:0-G|0)|0)>1|((((F|0)>-1?F:0-F|0)|0)>1|((((E|0)>-1?E:0-E|0)|0)>1|((D|0)>1|(m|0)>1))))|n){i=d[p>>0]|0;H=i-k|0;J=d[g>>0]|0;J=((m|0)>(J|0)?1:(((H|0)>-1?H:0-H|0)|0)>(J|0))<<31>>31;H=i-q|0;H=(H|0)<255?H:255;H=(((H|0)>0?H:0)&J)+((l-k|0)*3|0)|0;H=(H|0)>0&(n^1)?((H|0)<255?H:255):0;I=H+4|0;I=(I>>>0<255?I:255)>>>3;H=H+3|0;G=l-I|0;G=(G|0)<255?G:255;a[b>>0]=(G|0)>0?G:0;H=((H>>>0<255?H:255)>>>3)+k|0;H=H>>>0<255?H:255;a[h>>0]=(H|0)>0?H:0;h=(I+1|0)>>>1&~J;J=q-h|0;J=(J|0)<255?J:255;a[j>>0]=(J|0)>0?J:0;h=h+i|0;h=h>>>0<255?h:255;i=p;h=(h|0)>0?h:0}else{F=d[b+x>>0]|0;G=d[o>>0]|0;I=d[p>>0]|0;H=k+4|0;J=H+l|0;a[o>>0]=(J+(F*3|0)+(G<<1)+I|0)>>>3;H=H+G|0;E=q+l|0;a[p>>0]=(E+H+(I+F<<1)|0)>>>3;a[h>>0]=(E+4+(k<<1)+r+F+G+I|0)>>>3;a[b>>0]=(I+r+(l<<1)+q+s+H|0)>>>3;a[j>>0]=(J+r+(s+q<<1)+I|0)>>>3;h=(J+q+(r<<1)+(s*3|0)|0)>>>3}a[i>>0]=h;t=t+1|0;if((t|0)==8)break;else b=b+1|0}return}function Ce(a,b,c,d,e,f,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;Be(a,b,c,d,e);Be(a+8|0,b,f,g,h);return}function De(b,c,e,f,g){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0;y=0;while(1){t=b+-3|0;u=b+-2|0;h=b+-1|0;j=b+1|0;i=b+2|0;k=d[b+-4>>0]|0;l=d[t>>0]|0;A=k-l|0;s=d[f>>0]|0;m=d[u>>0]|0;z=l-m|0;n=d[h>>0]|0;o=m-n|0;o=(o|0)>-1?o:0-o|0;v=d[j>>0]|0;p=d[b>>0]|0;q=v-p|0;q=(q|0)>-1?q:0-q|0;w=d[i>>0]|0;B=w-v|0;x=d[b+3>>0]|0;C=x-w|0;D=n-p|0;r=m-v|0;s=(((((r|0)>-1?r:0-r|0)|0)/2|0)+(((D|0)>-1?D:0-D|0)<<1)|0)>(d[e>>0]|0|0)|((((C|0)>-1?C:0-C|0)|0)>(s|0)|((((B|0)>-1?B:0-B|0)|0)>(s|0)|((q|0)>(s|0)|((o|0)>(s|0)|((((A|0)>-1?A:0-A|0)|0)>(s|0)?1:(((z|0)>-1?z:0-z|0)|0)>(s|0))))));z=l-n|0;A=w-p|0;B=k-n|0;C=x-p|0;if((((C|0)>-1?C:0-C|0)|0)>1|((((B|0)>-1?B:0-B|0)|0)>1|((((A|0)>-1?A:0-A|0)|0)>1|((((z|0)>-1?z:0-z|0)|0)>1|((o|0)>1|(q|0)>1))))|s){i=d[g>>0]|0;i=((o|0)>(i|0)|(q|0)>(i|0))<<31>>31;C=(r|0)<255?r:255;C=(((C|0)>0?C:0)&i)+((p-n|0)*3|0)|0;C=(C|0)>0&(s^1)?((C|0)<255?C:255):0;D=C+4|0;D=(D>>>0<255?D:255)>>>3;C=C+3|0;B=p-D|0;B=(B|0)<255?B:255;a[b>>0]=(B|0)>0?B:0;C=((C>>>0<255?C:255)>>>3)+n|0;C=C>>>0<255?C:255;a[h>>0]=(C|0)>0?C:0;h=(D+1|0)>>>1&~i;i=v-h|0;i=(i|0)<255?i:255;a[j>>0]=(i|0)>0?i:0;h=h+m|0;h=h>>>0<255?h:255;i=u;h=(h|0)>0?h:0}else{C=n+4|0;D=C+p|0;a[t>>0]=(D+(k*3|0)+(l<<1)+m|0)>>>3;C=C+l|0;a[u>>0]=(v+p+(m+k<<1)+C|0)>>>3;a[h>>0]=(p+m+4+k+l+(n<<1)+v+w|0)>>>3;a[b>>0]=(w+m+C+(p<<1)+v+x|0)>>>3;a[j>>0]=(D+m+w+(x+v<<1)|0)>>>3;h=(D+v+(w<<1)+(x*3|0)|0)>>>3}a[i>>0]=h;y=y+1|0;if((y|0)==8)break;else b=b+c|0}return}function Ee(a,b,c,d,e,f,g,h){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;De(a,b,c,d,e);De(a+(b<<3)|0,b,f,g,h);return}function Fe(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;Ge(a,b,c,d,e,1);return}function Ge(b,c,e,f,g,h){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0;fa=h<<3;if((h|0)<=0)return;da=c*-4|0;ea=c*-3|0;M=c*-2|0;N=0-c|0;O=c<<1;P=c*3|0;Q=c*-8|0;R=c*-7|0;S=c*-6|0;T=c*-5|0;U=c<<2;V=c*5|0;W=c*6|0;X=c*7|0;Y=0-X|0;Z=0-W|0;_=0-V|0;$=0-U|0;aa=0-P|0;ba=0-O|0;ca=0-(c<<3)|0;L=0;while(1){u=b+N|0;v=b+c|0;w=b+O|0;x=b+P|0;i=d[b+da>>0]|0;j=d[b+ea>>0]|0;k=i-j|0;E=d[f>>0]|0;l=d[b+M>>0]|0;F=j-l|0;y=d[u>>0]|0;h=l-y|0;h=(h|0)>-1?h:0-h|0;I=d[v>>0]|0;z=d[b>>0]|0;A=I-z|0;B=(A|0)>-1?A:0-A|0;J=d[w>>0]|0;C=J-I|0;K=d[x>>0]|0;D=K-J|0;ha=y-z|0;l=l-I|0;E=(((((l|0)>-1?l:0-l|0)|0)/2|0)+(((ha|0)>-1?ha:0-ha|0)<<1)|0)>(d[e>>0]|0|0)|((((D|0)>-1?D:0-D|0)|0)>(E|0)|((((C|0)>-1?C:0-C|0)|0)>(E|0)|((B|0)>(E|0)|((h|0)>(E|0)|((((k|0)>-1?k:0-k|0)|0)>(E|0)?1:(((F|0)>-1?F:0-F|0)|0)>(E|0))))));F=E^1;j=j-y|0;k=J-z|0;i=i-y|0;ha=K-z|0;h=(((ha|0)>-1?ha:0-ha|0)|0)>1|((((i|0)>-1?i:0-i|0)|0)>1|((((k|0)>-1?k:0-k|0)|0)>1|((((j|0)>-1?j:0-j|0)|0)>1|((h|0)>1|(B|0)>1))));j=b+U|0;k=b+V|0;i=b+W|0;ha=(d[b+T>>0]|0)-y|0;l=d[j>>0]|0;ga=l-z|0;ia=(d[b+S>>0]|0)-y|0;m=d[k>>0]|0;ja=m-z|0;ka=(d[b+R>>0]|0)-y|0;G=d[i>>0]|0;la=G-z|0;na=(d[b+Q>>0]|0)-y|0;H=d[b+X>>0]|0;ma=H-z|0;n=a[g>>0]|0;o=b+Y|0;p=b+Z|0;q=b+_|0;r=b+$|0;s=b+aa|0;t=b+ba|0;do if(E|(h|(((((na|0)>-1?na:0-na|0)|0)>1?1:(((ma|0)>-1?ma:0-ma|0)|0)>1)|((((la|0)>-1?la:0-la|0)|0)>1|((((ka|0)>-1?ka:0-ka|0)|0)>1|((((ja|0)>-1?ja:0-ja|0)|0)>1|((((ia|0)>-1?ia:0-ia|0)|0)>1|((((ha|0)>-1?ha:0-ha|0)|0)>1?1:(((ga|0)>-1?ga:0-ga|0)|0)>1))))))))if(h|E){h=d[t>>0]|0;ma=h-y|0;i=n&255;i=((B|0)>(i|0)?1:(((ma|0)>-1?ma:0-ma|0)|0)>(i|0))<<31>>31;ma=h-I|0;ma=(ma|0)<255?ma:255;ma=(((ma|0)>0?ma:0)&i)+((z-y|0)*3|0)|0;ma=(ma|0)>0&F?((ma|0)<255?ma:255):0;na=ma+4|0;na=(na>>>0<255?na:255)>>>3;ma=ma+3|0;la=z-na|0;la=(la|0)<255?la:255;a[b>>0]=(la|0)>0?la:0;ma=((ma>>>0<255?ma:255)>>>3)+y|0;ma=ma>>>0<255?ma:255;a[u>>0]=(ma|0)>0?ma:0;i=(na+1|0)>>>1&~i;na=I-i|0;na=(na|0)<255?na:255;a[v>>0]=(na|0)>0?na:0;h=i+h|0;h=h>>>0<255?h:255;i=t;h=(h|0)>0?h:0;break}else{la=d[r>>0]|0;ma=d[s>>0]|0;i=d[t>>0]|0;na=y+4|0;h=na+z|0;a[s>>0]=(h+i+(la*3|0)+(ma<<1)|0)>>>3;na=na+ma|0;a[t>>0]=(I+z+na+(i+la<<1)|0)>>>3;i=i+J|0;a[u>>0]=(i+4+z+(y<<1)+I+la+ma|0)>>>3;a[b>>0]=(i+(z<<1)+I+K+na|0)>>>3;a[v>>0]=(i+h+(K+I<<1)|0)>>>3;i=w;h=(h+I+(J<<1)+(K*3|0)|0)>>>3;break}else{ha=d[b+ca>>0]|0;ia=d[o>>0]|0;ma=d[p>>0]|0;ja=d[q>>0]|0;ka=d[r>>0]|0;la=d[s>>0]|0;na=d[t>>0]|0;h=z+y+(ha*7|0)+(ia<<1)+ma+ja+ka+la+na|0;a[o>>0]=(h+8|0)>>>4;h=I-ha-ia+ma+h|0;a[p>>0]=(h+8|0)>>>4;h=J-ha-ma+ja+h|0;a[q>>0]=(h+8|0)>>>4;h=K-ha-ja+ka+h|0;a[r>>0]=(h+8|0)>>>4;h=l-ha-ka+la+h|0;a[s>>0]=(h+8|0)>>>4;h=m-ha-la+na+h|0;a[t>>0]=(h+8|0)>>>4;h=G+y-ha-na+h|0;a[u>>0]=(h+8|0)>>>4;h=z-y+H-ha+h|0;a[b>>0]=(h+8|0)>>>4;h=A+H-ia+h|0;a[v>>0]=(h+8|0)>>>4;h=C+H-ma+h|0;a[w>>0]=(h+8|0)>>>4;h=D+H-ja+h|0;a[x>>0]=(h+8|0)>>>4;h=l-K+H-ka+h|0;a[j>>0]=(h+8|0)>>>4;h=m-l+H-la+h|0;a[k>>0]=(h+8|0)>>>4;h=(G+8+H-ma-na+h|0)>>>4}while(0);a[i>>0]=h;L=L+1|0;if((L|0)>=(fa|0))break;else b=b+1|0}return}function He(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;Ge(a,b,c,d,e,2);return}function Ie(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;Je(a,b,c,d,e,8);return}function Je(b,c,e,f,g,h){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0;if((h|0)<=0)return;V=0;while(1){F=b+-4|0;I=b+-3|0;j=b+-2|0;n=b+-1|0;q=b+1|0;r=b+2|0;s=b+3|0;t=d[F>>0]|0;u=d[I>>0]|0;G=t-u|0;D=d[f>>0]|0;R=d[j>>0]|0;E=u-R|0;v=d[n>>0]|0;w=R-v|0;w=(w|0)>-1?w:0-w|0;S=d[q>>0]|0;x=d[b>>0]|0;y=S-x|0;z=(y|0)>-1?y:0-y|0;T=d[r>>0]|0;A=T-S|0;U=d[s>>0]|0;B=U-T|0;H=v-x|0;C=R-S|0;D=(((((C|0)>-1?C:0-C|0)|0)/2|0)+(((H|0)>-1?H:0-H|0)<<1)|0)>(d[e>>0]|0|0)|((((B|0)>-1?B:0-B|0)|0)>(D|0)|((((A|0)>-1?A:0-A|0)|0)>(D|0)|((z|0)>(D|0)|((w|0)>(D|0)|((((G|0)>-1?G:0-G|0)|0)>(D|0)?1:(((E|0)>-1?E:0-E|0)|0)>(D|0))))));E=D^1;G=u-v|0;H=T-x|0;J=t-v|0;K=U-x|0;G=(((K|0)>-1?K:0-K|0)|0)>1|((((J|0)>-1?J:0-J|0)|0)>1|((((H|0)>-1?H:0-H|0)|0)>1|((((G|0)>-1?G:0-G|0)|0)>1|((w|0)>1|(z|0)>1))));H=b+-7|0;J=b+-6|0;K=b+-5|0;L=b+4|0;M=b+5|0;N=b+6|0;i=d[K>>0]|0;X=i-v|0;k=d[L>>0]|0;W=k-x|0;O=d[J>>0]|0;Y=O-v|0;l=d[M>>0]|0;Z=l-x|0;m=d[H>>0]|0;_=m-v|0;P=d[N>>0]|0;$=P-x|0;o=d[b+-8>>0]|0;ba=o-v|0;Q=d[b+7>>0]|0;aa=Q-x|0;p=a[g>>0]|0;do if(D|(G|(((((ba|0)>-1?ba:0-ba|0)|0)>1?1:(((aa|0)>-1?aa:0-aa|0)|0)>1)|(((($|0)>-1?$:0-$|0)|0)>1|((((_|0)>-1?_:0-_|0)|0)>1|((((Z|0)>-1?Z:0-Z|0)|0)>1|((((Y|0)>-1?Y:0-Y|0)|0)>1|((((X|0)>-1?X:0-X|0)|0)>1?1:(((W|0)>-1?W:0-W|0)|0)>1))))))))if(G|D){i=p&255;i=((w|0)>(i|0)|(z|0)>(i|0))<<31>>31;aa=(C|0)<255?C:255;aa=(((aa|0)>0?aa:0)&i)+((x-v|0)*3|0)|0;aa=(aa|0)>0&E?((aa|0)<255?aa:255):0;ba=aa+4|0;ba=(ba>>>0<255?ba:255)>>>3;aa=aa+3|0;$=x-ba|0;$=($|0)<255?$:255;a[b>>0]=($|0)>0?$:0;aa=((aa>>>0<255?aa:255)>>>3)+v|0;aa=aa>>>0<255?aa:255;a[n>>0]=(aa|0)>0?aa:0;i=(ba+1|0)>>>1&~i;ba=S-i|0;ba=(ba|0)<255?ba:255;a[q>>0]=(ba|0)>0?ba:0;i=i+R|0;i=i>>>0<255?i:255;i=(i|0)>0?i:0;break}else{ba=v+4|0;i=ba+x|0;a[I>>0]=(i+R+(t*3|0)+(u<<1)|0)>>>3;aa=R+t|0;ba=ba+u|0;a[j>>0]=(ba+S+x+(aa<<1)|0)>>>3;a[n>>0]=(aa+4+u+x+(v<<1)+S+T|0)>>>3;j=T+R|0;a[b>>0]=(j+ba+(x<<1)+S+U|0)>>>3;a[q>>0]=(j+i+(U+S<<1)|0)>>>3;j=r;i=(i+S+(T<<1)+(U*3|0)|0)>>>3;break}else{aa=R+t+u+v+x+(o*7|0)+(m<<1)+O+i|0;a[H>>0]=(aa+8|0)>>>4;aa=S-o-m+O+aa|0;a[J>>0]=(aa+8|0)>>>4;aa=T-o-O+i+aa|0;a[K>>0]=(aa+8|0)>>>4;ba=U+t|0;aa=ba-o-i+aa|0;a[F>>0]=(aa+8|0)>>>4;aa=u-t-o+k+aa|0;a[I>>0]=(aa+8|0)>>>4;aa=R-u-o+l+aa|0;a[j>>0]=(aa+8|0)>>>4;j=v-R-o+P+aa|0;a[n>>0]=(j+8|0)>>>4;j=x-v-o+Q+j|0;a[b>>0]=(j+8|0)>>>4;j=y-m+Q+j|0;a[q>>0]=(j+8|0)>>>4;j=A-O+Q+j|0;a[r>>0]=(j+8|0)>>>4;i=B-i+Q+j|0;a[s>>0]=(i+8|0)>>>4;i=k-ba+Q+i|0;a[L>>0]=(i+8|0)>>>4;i=l-(k+u)+Q+i|0;a[M>>0]=(i+8|0)>>>4;j=N;i=(8-R-O+P+Q+i|0)>>>4}while(0);a[j>>0]=i;V=V+1|0;if((V|0)==(h|0))break;else b=b+c|0}return}function Ke(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;Je(a,b,c,d,e,16);return}function Le(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;g=L;L=L+32|0;m=g;n=b[c+2>>1]>>2;k=b[c+6>>1]>>2;l=n+(b[c>>1]>>2)|0;o=(b[c+4>>1]>>2)-k|0;p=l-o>>1;k=p-k|0;n=p-n|0;b[m>>1]=l-k;l=m+2|0;b[l>>1]=k;b[m+4>>1]=n;b[m+6>>1]=n+o;o=b[c+10>>1]>>2;n=b[c+14>>1]>>2;k=o+(b[c+8>>1]>>2)|0;p=(b[c+12>>1]>>2)-n|0;h=k-p>>1;n=h-n|0;o=h-o|0;k=k-n|0;b[m+8>>1]=k;b[m+10>>1]=n;b[m+12>>1]=o;b[m+14>>1]=o+p;p=b[c+18>>1]>>2;o=b[c+22>>1]>>2;n=p+(b[c+16>>1]>>2)|0;h=(b[c+20>>1]>>2)-o|0;j=n-h>>1;o=j-o|0;p=j-p|0;n=n-o|0;b[m+16>>1]=n;b[m+18>>1]=o;b[m+20>>1]=p;b[m+22>>1]=p+h;h=b[c+26>>1]>>2;p=b[c+30>>1]>>2;o=h+(b[c+24>>1]>>2)|0;j=(b[c+28>>1]>>2)-p|0;i=o-j>>1;c=i-p|0;h=i-h|0;o=o-c|0;b[m+24>>1]=o;b[m+26>>1]=c;b[m+28>>1]=h;b[m+30>>1]=h+j;j=f<<1;h=f*3|0;k=k<<16>>16;o=o<<16>>16;i=k+(b[m>>1]|0)|0;n=(n<<16>>16)-o|0;p=i-n>>1;o=p-o|0;k=p-k|0;i=i-o+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;i=e+f|0;o=o+(d[i>>0]|0)|0;o=(o|0)>0?o:0;a[i>>0]=(o|0)<255?o:255;i=e+j|0;o=k+(d[i>>0]|0)|0;o=(o|0)>0?o:0;a[i>>0]=(o|0)<255?o:255;i=e+h|0;n=k+n+(d[i>>0]|0)|0;n=(n|0)>0?n:0;a[i>>0]=(n|0)<255?n:255;i=e+1|0;n=b[m+10>>1]|0;l=n+(b[l>>1]|0)|0;k=(b[m+18>>1]|0)-c|0;o=l-k>>1;c=o-c|0;n=o-n|0;l=l-c+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[i>>0]=(l|0)<255?l:255;l=i+f|0;c=c+(d[l>>0]|0)|0;c=(c|0)>0?c:0;a[l>>0]=(c|0)<255?c:255;l=i+j|0;c=n+(d[l>>0]|0)|0;c=(c|0)>0?c:0;a[l>>0]=(c|0)<255?c:255;i=i+h|0;k=n+k+(d[i>>0]|0)|0;k=(k|0)>0?k:0;a[i>>0]=(k|0)<255?k:255;i=e+2|0;k=b[m+12>>1]|0;n=b[m+28>>1]|0;l=k+(b[m+4>>1]|0)|0;c=(b[m+20>>1]|0)-n|0;o=l-c>>1;n=o-n|0;k=o-k|0;l=l-n+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[i>>0]=(l|0)<255?l:255;l=i+f|0;n=n+(d[l>>0]|0)|0;n=(n|0)>0?n:0;a[l>>0]=(n|0)<255?n:255;l=i+j|0;n=k+(d[l>>0]|0)|0;n=(n|0)>0?n:0;a[l>>0]=(n|0)<255?n:255;i=i+h|0;c=k+c+(d[i>>0]|0)|0;c=(c|0)>0?c:0;a[i>>0]=(c|0)<255?c:255;i=e+3|0;c=b[m+14>>1]|0;k=b[m+30>>1]|0;l=c+(b[m+6>>1]|0)|0;e=(b[m+22>>1]|0)-k|0;m=l-e>>1;k=m-k|0;c=m-c|0;l=l-k+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[i>>0]=(l|0)<255?l:255;f=i+f|0;k=k+(d[f>>0]|0)|0;k=(k|0)>0?k:0;a[f>>0]=(k|0)<255?k:255;f=i+j|0;j=c+(d[f>>0]|0)|0;j=(j|0)>0?j:0;a[f>>0]=(j|0)<255?j:255;f=i+h|0;e=c+e+(d[f>>0]|0)|0;e=(e|0)>0?e:0;a[f>>0]=(e|0)<255?e:255;L=g;return}function Me(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;g=L;L=L+16|0;j=g;k=b[c>>1]|0;l=k>>3;h=(k>>2)-l|0;b[j>>1]=h;i=l&65535;b[j+6>>1]=i;b[j+4>>1]=i;b[j+2>>1]=i;i=f<<1;c=f*3|0;m=h>>1;h=h-m+(d[e>>0]|0)|0;h=(h|0)>0?h:0;a[e>>0]=(h|0)<255?h:255;h=e+f|0;n=m+(d[h>>0]|0)|0;n=(n|0)>0?n:0;a[h>>0]=(n|0)<255?n:255;h=e+i|0;n=m+(d[h>>0]|0)|0;n=(n|0)>0?n:0;a[h>>0]=(n|0)<255?n:255;h=e+c|0;m=m+(d[h>>0]|0)|0;m=(m|0)>0?m:0;a[h>>0]=(m|0)<255?m:255;h=e+1|0;k=k>>4;l=l-k|0;m=l+(d[h>>0]|0)|0;m=(m|0)>0?m:0;a[h>>0]=(m|0)<255?m:255;m=h+f|0;n=k+(d[m>>0]|0)|0;n=(n|0)>0?n:0;a[m>>0]=(n|0)<255?n:255;m=h+i|0;n=k+(d[m>>0]|0)|0;n=(n|0)>0?n:0;a[m>>0]=(n|0)<255?n:255;h=h+c|0;m=k+(d[h>>0]|0)|0;m=(m|0)>0?m:0;a[h>>0]=(m|0)<255?m:255;h=e+2|0;l=l+(d[h>>0]|0)|0;l=(l|0)>0?l:0;a[h>>0]=(l|0)<255?l:255;l=h+f|0;m=k+(d[l>>0]|0)|0;m=(m|0)>0?m:0;a[l>>0]=(m|0)<255?m:255;l=h+i|0;m=k+(d[l>>0]|0)|0;m=(m|0)>0?m:0;a[l>>0]=(m|0)<255?m:255;h=h+c|0;k=k+(d[h>>0]|0)|0;k=(k|0)>0?k:0;a[h>>0]=(k|0)<255?k:255;h=e+3|0;j=b[j+6>>1]|0;e=j>>1;j=j-e+(d[h>>0]|0)|0;j=(j|0)>0?j:0;a[h>>0]=(j|0)<255?j:255;f=h+f|0;j=e+(d[f>>0]|0)|0;j=(j|0)>0?j:0;a[f>>0]=(j|0)<255?j:255;f=h+i|0;i=e+(d[f>>0]|0)|0;i=(i|0)>0?i:0;a[f>>0]=(i|0)<255?i:255;f=h+c|0;e=e+(d[f>>0]|0)|0;e=(e|0)>0?e:0;a[f>>0]=(e|0)<255?e:255;L=g;return}function Ne(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;i=b[a>>1]|0;f=b[a+2>>1]|0;h=b[a+4>>1]|0;g=b[a+6>>1]|0;e=i<<16>>16;a=h<<16>>16;d=g<<16>>16;if(!((f|i|h|g)<<16>>16)){i=c;h=i;b[h>>1]=0;b[h+2>>1]=0>>>16;i=i+4|0;b[i>>1]=0;b[i+2>>1]=0>>>16;return}else{g=(f<<16>>16)*13377|0;i=(a*15212|0)+(e*5283|0)+(d*9929|0)|0;h=(a*-5283|0)+(e*9929|0)+(d*-15212|0)|0;f=g+8192|0;b[c>>1]=(f+i|0)>>>14;b[c+2>>1]=(f+h|0)>>>14;b[c+4>>1]=(((e-a+d|0)*13377|0)+8192|0)>>>14;b[c+6>>1]=(8192-g+h+i|0)>>>14;return}}function Oe(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;h=b[a>>1]|0;f=b[a+4>>1]|0;g=b[a+2>>1]|0;e=b[a+6>>1]|0;d=(((f+h|0)*11585|0)+8192|0)>>>14<<16>>16;a=((g*15137|0)+8192+(e*6270|0)|0)>>>14<<16>>16;b[c>>1]=a+d;f=(((h-f|0)*11585|0)+8192|0)>>>14<<16>>16;e=((g*6270|0)+8192+(e*-15137|0)|0)>>>14<<16>>16;b[c+2>>1]=e+f;b[c+4>>1]=f-e;b[c+6>>1]=d-a;return}
function _(a){a=a|0;var b=0;b=L;L=L+a|0;L=L+15&-16;return b|0}function $(){return L|0}function aa(a){a=a|0;L=a}function ba(a,b){a=a|0;b=b|0;L=a;M=b}function ca(){var a=0,b=0,d=0;a=L;L=L+16|0;b=a;d=La()|0;c[8120]=d;c[b>>2]=0;c[b+4>>2]=0;c[b+8>>2]=0;ia(32484,d,b,0,11)|0;L=a;return}function da(){return 0}function ea(){return}function fa(a,b){a=a|0;b=b|0;return 0}function ga(a,b){a=a|0;b=b|0;if(!a){ha();return 1}else{ja(32484,a,b,0,1)|0;ja(32484,0,0,0,1)|0;ha();return 1}return 0}function ha(){var a=0,b=0,d=0,e=0,f=0,g=0,h=0,i=0;i=L;L=L+16|0;h=i;c[h>>2]=0;a=ka(32484,h)|0;if(!a){L=i;return}b=1;a:while(1){if(!b){a=4;break}f=c[a+28>>2]|0;g=(f&1)+f|0;switch(c[a>>2]|0){case 258:{e=c[a+12>>2]|0;b=g>>1;d=e>>>1;break}case 261:{e=c[a+12>>2]|0;b=g;d=e>>>1;break}case 262:{e=c[a+12>>2]|0;b=g;d=e;break}default:{a=11;break a}}J(c[a+48>>2]|0,c[a+64>>2]|0,c[a+52>>2]|0,c[a+68>>2]|0,c[a+56>>2]|0,c[a+72>>2]|0,e|0,g|0,d|0,b|0,c[a+24>>2]|0,f|0,0,0,c[a+32>>2]|0,c[a+36>>2]|0);a=ka(32484,h)|0;if(!a){a=11;break}else b=0}if((a|0)==4){do{}while((ka(32484,h)|0)!=0);L=i;return}else if((a|0)==11){L=i;return}}function ia(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;do if((f|0)==11)if((a|0)!=0&(b|0)!=0)if((c[b+4>>2]|0)==5){if(e&65536|0?(c[b+8>>2]&262144|0)==0:0){f=4;break}if(e&131072|0?(c[b+8>>2]&524288|0)==0:0){f=4;break}g=c[b+8>>2]|0;if(!((e&262144|0)!=0&(g&1048576|0)==0|(g&1|0)==0)){g=a+8|0;c[g>>2]=0;c[g+4>>2]=0;c[g+8>>2]=0;c[g+12>>2]=0;c[a+4>>2]=b;c[a>>2]=c[b>>2];g=a+24|0;c[g>>2]=0;c[a+16>>2]=e;c[a+20>>2]=d;f=Q[c[b+12>>2]&31](a,0)|0;if(!f)f=0;else{b=c[g>>2]|0;if(!b)b=0;else b=c[b>>2]|0;c[a+12>>2]=b;la(a)|0}}else f=4}else f=3;else{f=8;h=13}else{f=3;h=13}while(0);if((h|0)==13)if(!a){h=f;return h|0}c[a+8>>2]=f;h=f;return h|0}function ja(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;if(!a){a=8;return a|0}if((b|0)==0^(d|0)!=0){h=c[a+4>>2]|0;if((h|0)!=0?(g=c[a+24>>2]|0,(g|0)!=0):0)b=T[c[h+32>>2]&3](g,b,d,e,f)|0;else b=1}else b=8;c[a+8>>2]=b;a=b;return a|0}function ka(a,b){a=a|0;b=b|0;var d=0;if(!((a|0)!=0&(b|0)!=0)){d=0;return d|0}d=c[a+4>>2]|0;if(!d){d=0;return d|0}a=c[a+24>>2]|0;if(!a){d=0;return d|0}d=Q[c[d+36>>2]&31](a,b)|0;return d|0}function la(a){a=a|0;var b=0,d=0,e=0,f=0;if(!a){a=8;return a|0}b=a+4|0;d=c[b>>2]|0;if((d|0)!=0?(e=a+24|0,f=c[e>>2]|0,(f|0)!=0):0){P[c[d+16>>2]&7](f)|0;c[b>>2]=0;c[a>>2]=0;c[e>>2]=0;b=0}else b=1;c[a+8>>2]=b;a=b;return a|0}function ma(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;h=L;L=L+16|0;g=h;c[b>>2]=d;d=b+4|0;c[d>>2]=0;if(e|0){c[d>>2]=1;c[g>>2]=f;Nf(b+8|0,79,e,g)|0;a[b+87>>0]=0}if(!(c[b+88>>2]|0)){L=h;return}else I(b+92|0,c[b>>2]|0)}function na(a,b){a=a|0;b=b|0;var d=0;b=a+24|0;if(c[b>>2]|0){d=0;return d|0}d=pb(1,400)|0;if(!d){d=2;return d|0}c[b>>2]=d;c[d+4>>2]=c[a+16>>2];c[d+196>>2]=16;c[d+352>>2]=0;b=a+20|0;a=c[b>>2]|0;if(!a){d=0;return d|0}d=d+184|0;c[d>>2]=c[a>>2];c[d+4>>2]=c[a+4>>2];c[d+8>>2]=c[a+8>>2];c[b>>2]=d;d=0;return d|0}function oa(a){a=a|0;var b=0,d=0;b=c[a+212>>2]|0;if(b|0)db(b);b=a+376|0;d=c[b>>2]|0;if(!d){qb(d);qb(a);return 0}else{Ob(d);dc((c[b>>2]|0)+1836|0);d=c[b>>2]|0;qb(d);qb(a);return 0}return 0}function pa(a,b,c){a=a|0;b=b|0;c=c|0;return va(a,b,c,0,0,0)|0}function qa(a,b){a=a|0;b=b|0;a=a+196|0;c[b>>2]=c[a>>2];c[b+4>>2]=c[a+4>>2];c[b+8>>2]=c[a+8>>2];c[b+12>>2]=c[a+12>>2];c[b>>2]=16;return 0}function ra(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;t=L;L=L+48|0;l=t+32|0;r=t+44|0;s=t+40|0;m=t;n=t+36|0;c[s>>2]=d;i=d+e|0;g=b+352|0;if((d|0)==0&(e|0)==0){c[g>>2]=1;f=0;L=t;return f|0}c[g>>2]=0;h=b+212|0;do if(!(c[h>>2]|0)){c[b+360>>2]=-1;c[b+372>>2]=1;c[g>>2]=0;g=pb(1,1844)|0;c[b+376>>2]=g;if(!g){f=2;L=t;return f|0}j=cb(g)|0;c[h>>2]=j;if(!j){c[b>>2]=30195;f=2;L=t;return f|0}c[j+18704>>2]=c[b+184>>2];c[j+18708>>2]=c[b+356>>2];if((c[b+220>>2]|0)==0?c[b+4>>2]&65536|0:0){c[b+224>>2]=3;c[b+228>>2]=4;c[b+232>>2]=0}h=c[j+17832>>2]|0;c[j+988>>2]=-1;c[j+17804>>2]=c[b+364>>2];c[j+17808>>2]=c[b+368>>2];g=c[b+384>>2]|0;if(g|0?(k=c[b+388>>2]|0,k|0):0){c[h+4>>2]=g;c[h+8>>2]=k;c[h>>2]=c[b+380>>2];break}c[h+4>>2]=3;c[h+8>>2]=19;g=h+1836|0;if(cc(g)|0)ma(j+432|0,2,30222,l);c[h>>2]=g}while(0);k=b+236|0;l=b+240|0;g=lb(d,e,m,n,c[k>>2]|0,c[l>>2]|0)|0;if(g|0){f=g;L=t;return f|0}if(c[b+392>>2]|0){e=c[b+396>>2]|0;g=c[n>>2]|0;h=e+1|0;if((e|0)<(g+-1|0)){c[n>>2]=h;g=h}}else g=c[n>>2]|0;if((g|0)>0){h=0;g=c[s>>2]|0;while(1){c[r>>2]=g;j=c[m+(h<<2)>>2]|0;if(g>>>0<d>>>0|j>>>0>(i-g|0)>>>0){q=24;break}g=ua(b,r,j,f)|0;if(g|0){o=g;break}g=(c[s>>2]|0)+j|0;c[s>>2]=g;h=h+1|0;if((h|0)>=(c[n>>2]|0)){p=0;q=39;break}}if((q|0)==24){c[b>>2]=30266;o=7}else if((q|0)==39){L=t;return p|0}f=o;L=t;return f|0}j=i;g=c[s>>2]|0;if(g>>>0>=i>>>0){f=0;L=t;return f|0}while(1){g=ua(b,s,j-g|0,f)|0;if(g|0){p=g;q=39;break}g=c[s>>2]|0;a:do if(g>>>0<i>>>0)do{h=c[k>>2]|0;if(!h)h=a[g>>0]|0;else{X[h&63](c[l>>2]|0,g,r,1);h=a[r>>0]|0}g=c[s>>2]|0;if(h<<24>>24)break a;g=g+1|0;c[s>>2]=g}while(g>>>0<i>>>0);while(0);if(g>>>0>=i>>>0){p=0;q=39;break}}if((q|0)==39){L=t;return p|0}return 0}function sa(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;g=L;L=L+144|0;f=g+16|0;b=g;d=a+212|0;e=c[d>>2]|0;if(!e){a=0;L=g;return a|0};c[b>>2]=0;c[b+4>>2]=0;c[b+8>>2]=0;if(c[a+4>>2]&65536|0){i=c[a+228>>2]|0;h=c[a+232>>2]|0;c[b>>2]=c[a+224>>2];c[b+4>>2]=i;c[b+8>>2]=h}if(kb(e,f,b)|0){i=0;L=g;return i|0}d=c[d>>2]|0;b=c[d+17832>>2]|0;d=d+988|0;c[a+360>>2]=c[d>>2];if(!(c[a+372>>2]|0)){i=a+244|0;h=c[a+216>>2]|0;l=c[f+88>>2]|0;j=(l|0)==0;m=c[f+84>>2]|0;e=(m|0)!=0;c[i>>2]=j?(e?261:262):e?258:263;c[a+248>>2]=c[f+96>>2];c[a+252>>2]=c[f+100>>2];c[a+264>>2]=8;k=c[f+16>>2]|0;c[a+256>>2]=k;c[a+260>>2]=(c[f+4>>2]|0)+327&-8;c[a+268>>2]=c[f+8>>2];c[a+272>>2]=c[f+12>>2];c[a+276>>2]=c[f+104>>2];c[a+280>>2]=c[f+108>>2];c[a+284>>2]=m;c[a+288>>2]=l;c[a+292>>2]=c[f+52>>2];c[a+296>>2]=c[f+56>>2];c[a+300>>2]=c[f+60>>2];c[a+304>>2]=0;c[a+308>>2]=k;l=c[f+36>>2]|0;c[a+312>>2]=l;c[a+316>>2]=l;c[a+320>>2]=k;c[a+324>>2]=j?(e?16:24):e?12:16;c[a+328>>2]=h;c[a+332>>2]=c[f+68>>2];c[a+336>>2]=0;c[a+340>>2]=0;c[a+344>>2]=c[b+12+((c[d>>2]|0)*152|0)+28>>2];b=i}else b=0;m=b;L=g;return m|0}function ta(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;if((b|0)==0|(d|0)==0){e=8;return e|0}if(c[a+212>>2]|0){e=1;return e|0}c[a+384>>2]=b;c[a+388>>2]=d;c[a+380>>2]=e;e=0;return e|0}function ua(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;k=L;L=L+16|0;j=k;do if(!(c[b+204>>2]|0)){c[j>>2]=0;g=b+236|0;h=b+240|0;i=va(c[d>>2]|0,e,b+196|0,j,c[g>>2]|0,c[h>>2]|0)|0;if(!i){if(!(c[j>>2]|c[b+208>>2]))g=1;else break;L=k;return g|0}else{b=i;L=k;return b|0}}else{h=b+240|0;g=b+236|0}while(0);c[b+216>>2]=f;j=b+212|0;f=c[j>>2]|0;c[f+18696>>2]=c[g>>2];c[f+18700>>2]=c[h>>2];f=(jb(f,e,d)|0)==0;h=c[j>>2]|0;if(!f){c[(c[h+17864>>2]|0)+144>>2]=1;c[h+18712>>2]=1;c[b+372>>2]=1;g=c[h+432>>2]|0;if(!g){b=0;L=k;return b|0}c[b>>2]=(c[h+436>>2]|0)==0?0:h+440|0;b=g;L=k;return b|0}g=b+372|0;if((c[g>>2]|0)!=1){b=0;L=k;return b|0}if(c[h+18712>>2]|0){b=0;L=k;return b|0}if((a[h+1012>>0]|0)==0?c[h+996>>2]|0:0){b=0;L=k;return b|0}c[g>>2]=0;b=0;L=k;return b|0}function va(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0;n=L;L=L+32|0;h=n+20|0;m=n;if((b|0)<1){e=8;L=n;return e|0}i=d+12|0;c[i>>2]=0;l=d+8|0;c[l>>2]=0;k=d+4|0;c[k>>2]=0;d=b>>>0<10?b:10;if(f){X[f&63](g,a,h,d);a=h;b=d}c[m>>2]=a;c[m+4>>2]=a+b;g=m+8|0;c[g>>2]=0;c[m+12>>2]=0;c[m+16>>2]=0;h=Db(m,2)|0;f=Oa(m)|0;a:do if(!((h|0)!=2|f>>>0>3)){if(Cb(m)|0){if(b>>>0<2&f>>>0>2){a=5;break}Db(m,3)|0;a=0;break}if(b>>>0>=10){c[i>>2]=(Cb(m)|0)==0&1;a=Cb(m)|0;b=Cb(m)|0;do if(!(c[i>>2]|0)){if(a|0){c[g>>2]=(c[g>>2]|0)+((b|0)==0?2:0);a=0;break}a=Cb(m)|0;c[g>>2]=(c[g>>2]|0)+((b|0)==0?2:0);if(!a)a=0;else{if(!(Ma(m)|0)){a=5;break a}switch(f|0){case 0:{b=c[g>>2]|0;break}case 1:{j=27;break}default:{c[g>>2]=(c[g>>2]|0)+1;j=27}}do if((j|0)==27)if((Db(m,3)|0)==7){if((f|2|0)!=3){a=5;break a}b=(c[g>>2]|0)+1|0;c[g>>2]=b;break}else{d=c[g>>2]|0;b=d+1|0;c[g>>2]=b;if((f|2|0)!=3)break;b=d+4|0;c[g>>2]=b;break}while(0);c[g>>2]=b+8;Na(m,k,l)}}else{if(!(Ma(m)|0)){a=5;break a}if(f>>>0>1)c[g>>2]=(c[g>>2]|0)+1;if((Db(m,3)|0)!=7){a=c[g>>2]|0;c[g>>2]=a+1;if((f|2|0)==3){a=a+4|0;j=18}}else{if((f|2|0)!=3){a=5;break a}a=(c[g>>2]|0)+1|0;j=18}if((j|0)==18)c[g>>2]=a;Na(m,k,l);a=0}while(0);if(!e){e=0;L=n;return e|0}c[e>>2]=a;e=0;L=n;return e|0}else a=5}else a=5;while(0);e=a;L=n;return e|0}function wa(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;f=L;L=L+128|0;d=f;g=(c[b>>2]|0)+(4-1)&~(4-1);e=c[g>>2]|0;c[b>>2]=g+4;if(!e){g=8;L=f;return g|0}c[d+52>>2]=c[e+52>>2];c[d+56>>2]=c[e+56>>2];c[d+60>>2]=c[e+60>>2];i=c[e+28>>2]|0;c[d+8>>2]=i;h=c[e+32>>2]|0;c[d+12>>2]=h;c[d+104>>2]=c[e+36>>2];c[d+108>>2]=c[e+40>>2];c[d>>2]=i;c[d+4>>2]=h;b=c[e+44>>2]|0;i=(b|0)==1?(i+1|0)/2|0:i;c[d+20>>2]=i;g=c[e+48>>2]|0;h=(g|0)==1?(h+1|0)/2|0:h;c[d+24>>2]=h;c[d+28>>2]=i;c[d+32>>2]=h;h=c[e+68>>2]|0;c[d+16>>2]=h;c[d+36>>2]=c[e+72>>2];c[d+96>>2]=c[e+8>>2];c[d+100>>2]=c[e+12>>2];c[d+76>>2]=(h-(c[e+16>>2]|0)|0)>>>1;c[d+84>>2]=b;c[d+88>>2]=g;g=hb(c[a+212>>2]|0,c[e>>2]|0,d)|0;L=f;return g|0}function xa(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;f=L;L=L+128|0;d=f;g=(c[b>>2]|0)+(4-1)&~(4-1);e=c[g>>2]|0;c[b>>2]=g+4;if(!e){g=8;L=f;return g|0}c[d+52>>2]=c[e+52>>2];c[d+56>>2]=c[e+56>>2];c[d+60>>2]=c[e+60>>2];i=c[e+28>>2]|0;c[d+8>>2]=i;h=c[e+32>>2]|0;c[d+12>>2]=h;c[d+104>>2]=c[e+36>>2];c[d+108>>2]=c[e+40>>2];c[d>>2]=i;c[d+4>>2]=h;b=c[e+44>>2]|0;i=(b|0)==1?(i+1|0)/2|0:i;c[d+20>>2]=i;g=c[e+48>>2]|0;h=(g|0)==1?(h+1|0)/2|0:h;c[d+24>>2]=h;c[d+28>>2]=i;c[d+32>>2]=h;h=c[e+68>>2]|0;c[d+16>>2]=h;c[d+36>>2]=c[e+72>>2];c[d+96>>2]=c[e+8>>2];c[d+100>>2]=c[e+12>>2];c[d+76>>2]=(h-(c[e+16>>2]|0)|0)>>>1;c[d+84>>2]=b;c[d+88>>2]=g;g=c[e>>2]|0;g=ib((c[a+212>>2]|0)+432|0,(g|0)==4?4:(g|0)==2?2:1,d)|0;L=f;return g|0}function ya(a,b){a=a|0;b=b|0;return 4}function za(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;c[a+356>>2]=d;return 0}function Aa(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){c[a+236>>2]=0;d=0;e=a+240|0;c[e>>2]=d;return 0}else{c[a+236>>2]=c[d>>2];d=c[d+4>>2]|0;e=a+240|0;c[e>>2]=d;return 0}return 0}function Ba(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(d|0){if((d+-32|0)>>>0>992){e=8;return e|0}if(d+-1&d|0){e=8;return e|0}}c[a+364>>2]=d;a=c[a+212>>2]|0;if(!a){e=0;return e|0}c[a+17804>>2]=d;e=0;return e|0}function Ca(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;c[a+368>>2]=d;a=c[a+212>>2]|0;if(!a)return 0;c[a+17808>>2]=d;return 0}function Da(a,b){a=a|0;b=b|0;var d=0,e=0;c[a+392>>2]=1;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;c[a+396>>2]=d;return d>>31&8|0}function Ea(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}a=c[a+212>>2]|0;if(!a){e=8;return e|0}c[d>>2]=c[a+1052>>2];e=0;return e|0}function Fa(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}a=c[a+212>>2]|0;if(!a){e=1;return e|0}c[d>>2]=c[a+17860>>2];e=0;return e|0}function Ga(a,b){a=a|0;b=b|0;var d=0,e=0;d=(c[b>>2]|0)+(4-1)&~(4-1);e=c[d>>2]|0;c[b>>2]=d+4;if(!e){e=8;return e|0}d=c[a+212>>2]|0;if(!d){e=1;return e|0}if(!(c[d+720>>2]|0)){e=1;return e|0}b=c[a+360>>2]|0;if((b|0)<=-1){e=0;return e|0}c[e>>2]=c[(c[d+17832>>2]|0)+12+(b*152|0)+144>>2];e=0;return e|0}function Ha(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}b=c[a+212>>2]|0;a=c[d>>2]|0;if(a>>>0>7){e=1;return e|0}a=c[b+732+(a<<2)>>2]|0;if((a|0)<0){e=1;return e|0}e=c[b+17832>>2]|0;i=c[e+12+(a*152|0)+120>>2]|0;f=(i|0)==0;j=c[e+12+(a*152|0)+116>>2]|0;b=(j|0)!=0;c[d+4>>2]=f?(b?261:262):b?258:263;c[d+8>>2]=c[e+12+(a*152|0)+128>>2];c[d+12>>2]=c[e+12+(a*152|0)+132>>2];c[d+24>>2]=8;g=e+12+(a*152|0)+48|0;h=c[g>>2]|0;c[d+16>>2]=h;c[d+20>>2]=(c[e+12+(a*152|0)+36>>2]|0)+327&-8;c[d+28>>2]=c[e+12+(a*152|0)+40>>2];c[d+32>>2]=c[e+12+(a*152|0)+44>>2];c[d+36>>2]=c[e+12+(a*152|0)+136>>2];c[d+40>>2]=c[e+12+(a*152|0)+140>>2];c[d+44>>2]=j;c[d+48>>2]=i;c[d+52>>2]=c[e+12+(a*152|0)+84>>2];c[d+56>>2]=c[e+12+(a*152|0)+88>>2];c[d+60>>2]=c[e+12+(a*152|0)+92>>2];c[d+64>>2]=0;c[d+68>>2]=h;h=e+12+(a*152|0)+68|0;c[d+72>>2]=c[h>>2];c[d+76>>2]=c[h>>2];c[d+80>>2]=c[g>>2];c[d+84>>2]=f?(b?16:24):b?12:16;c[d+88>>2]=0;c[d+92>>2]=c[e+12+(a*152|0)+100>>2];c[d+96>>2]=0;c[d+100>>2]=0;e=0;return e|0}function Ia(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}a=c[a+212>>2]|0;if(!a){e=1;return e|0}c[d>>2]=c[a+696>>2];c[d+4>>2]=c[a+700>>2];e=0;return e|0}function Ja(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}a=c[a+212>>2]|0;if(!a){e=1;return e|0}c[d>>2]=c[a+17780>>2];e=0;return e|0}function Ka(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}a=c[a+212>>2]|0;if(!a){e=1;return e|0}c[d>>2]=c[a+688>>2];c[d+4>>2]=c[a+692>>2];e=0;return e|0}function La(){return 29664}function Ma(a){a=a|0;if((Db(a,8)|0)!=73){a=0;return a|0}if((Db(a,8)|0)!=131){a=0;return a|0}a=(Db(a,8)|0)==66&1;return a|0}function Na(a,b,d){a=a|0;b=b|0;d=d|0;c[b>>2]=(Db(a,16)|0)+1;c[d>>2]=(Db(a,16)|0)+1;return}function Oa(a){a=a|0;var b=0;b=Cb(a)|0;b=(Cb(a)|0)<<1|b;if((b|0)<=2)return b|0;b=(Cb(a)|0)+b|0;return b|0}function Pa(d,e,f,g){d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,Q=0,R=0,S=0,T=0,U=0,W=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,na=0,oa=0,pa=0,qa=0;qa=L;L=L+3424|0;pa=qa+3384|0;ka=qa+3376|0;ga=qa+3368|0;fa=qa+3360|0;aa=qa+3352|0;$=qa+3344|0;ha=qa+3336|0;ca=qa+3328|0;ba=qa+3320|0;W=qa+3312|0;U=qa+3304|0;T=qa+3296|0;S=qa+3288|0;M=qa+3280|0;K=qa+3272|0;H=qa+3264|0;q=qa+3256|0;p=qa+3248|0;E=qa+3240|0;D=qa+3232|0;F=qa+3224|0;C=qa+3216|0;B=qa+3208|0;A=qa+3200|0;z=qa+3192|0;o=qa+3184|0;n=qa+3176|0;k=qa+3168|0;ia=qa+80|0;ja=qa+3408|0;R=qa+3388|0;h=qa;oa=d+432|0;c[R+8>>2]=0;c[R+16>>2]=5;c[R+12>>2]=oa;da=d+18696|0;i=c[da>>2]|0;ea=f;j=ea-e|0;j=(j|0)<80?j:80;if(!i){i=f;h=e}else{X[i&63](c[d+18700>>2]|0,e,h,j);i=h+j|0}c[R>>2]=h;c[R+4>>2]=i;Y=d+17832|0;I=c[Y>>2]|0;la=d+996|0;O=d+992|0;c[O>>2]=c[la>>2];na=d+1012|0;Q=d+1013|0;a[Q>>0]=a[na>>0]|0;if((Db(R,2)|0)!=2)ma(oa,5,30294,qa+3152|0);h=Cb(R)|0;h=(Cb(R)|0)<<1|h;if((h|0)>2)h=(Cb(R)|0)+h|0;N=d+17776|0;c[N>>2]=h;if(h>>>0>1)ma(oa,5,30315,qa+3160|0);G=Cb(R)|0;c[d+1008>>2]=G;do if(!G){c[la>>2]=Cb(R)|0;h=d+1e3|0;c[h>>2]=Cb(R)|0;i=Cb(R)|0;G=d+17788|0;c[G>>2]=i;do if(!(c[la>>2]|0)){if(!(((Db(R,8)|0)==73?(Db(R,8)|0)==131:0)?(Db(R,8)|0)==66:0))ma(oa,5,30388,n);Ra(oa,R);c[d+17860>>2]=255;c[d+796>>2]=-1;c[d+800>>2]=0;c[d+860>>2]=-1;c[d+864>>2]=0;c[d+924>>2]=-1;c[d+928>>2]=0;Sa(oa,R);h=d+18712|0;if(!(c[h>>2]|0))h=0;else{F=d+732|0;c[F>>2]=-1;c[F+4>>2]=-1;c[F+8>>2]=-1;c[F+12>>2]=-1;c[F+16>>2]=-1;c[F+20>>2]=-1;c[F+24>>2]=-1;c[F+28>>2]=-1;c[h>>2]=0;h=0}}else{if(!(c[h>>2]|0)){h=Cb(R)|0;i=c[G>>2]|0}else h=0;h=h&255;a[na>>0]=h;if(!i){i=Db(R,2)|0;h=a[na>>0]|0}else i=0;c[d+1020>>2]=i;if(h<<24>>24){if(!(((Db(R,8)|0)==73?(Db(R,8)|0)==131:0)?(Db(R,8)|0)==66:0))ma(oa,5,30388,o);if(!(c[N>>2]|0)){c[d+680>>2]=1;c[d+684>>2]=0;c[d+712>>2]=1;c[d+716>>2]=1;c[d+17780>>2]=8}else Ra(oa,R);c[d+17860>>2]=Db(R,8)|0;Sa(oa,R);h=d+18712|0;if(!(c[h>>2]|0)){h=0;break}F=d+732|0;c[F>>2]=-1;c[F+4>>2]=-1;c[F+8>>2]=-1;c[F+12>>2]=-1;c[F+16>>2]=-1;c[F+20>>2]=-1;c[F+24>>2]=-1;c[F+28>>2]=-1;c[h>>2]=0;h=0;break}v=d+18712|0;if((c[v>>2]|0)==1)h=1;else{c[d+17860>>2]=Db(R,8)|0;u=c[d+732+((Db(R,3)|0)<<2)>>2]|0;m=d+796|0;c[m>>2]=u;w=d+800|0;c[w>>2]=I+12+(u*152|0)+32;c[d+4360>>2]=Cb(R)|0;u=c[d+732+((Db(R,3)|0)<<2)>>2]|0;r=d+860|0;c[r>>2]=u;x=d+864|0;c[x>>2]=I+12+(u*152|0)+32;c[d+4364>>2]=Cb(R)|0;u=c[d+732+((Db(R,3)|0)<<2)>>2]|0;t=d+924|0;c[t>>2]=u;y=d+928|0;c[y>>2]=I+12+(u*152|0)+32;c[d+4368>>2]=Cb(R)|0;u=c[Y>>2]|0;if(Cb(R)|0)if((c[m>>2]|0)==-1){ma(oa,7,30412,z);J=43}else{h=w;J=41}else J=43;do if((J|0)==43){if(Cb(R)|0){if((c[r>>2]|0)!=-1){h=x;J=41;break}ma(oa,7,30412,p)}if(Cb(R)|0){if((c[t>>2]|0)!=-1){h=y;J=41;break}ma(oa,7,30412,q)}q=(Db(R,16)|0)+1|0;p=(Db(R,16)|0)+1|0}while(0);if((J|0)==41){q=c[h>>2]|0;p=c[q+12>>2]|0;q=c[q+8>>2]|0}if((q|0)<1|(p|0)<1)ma(oa,7,30440,A);i=q<<1;j=p<<1;h=c[m>>2]|0;if((h|0)==-1)l=0;else{l=c[w>>2]|0;A=c[l+8>>2]|0;l=c[l+12>>2]|0;l=(l<<4|0)>=(p|0)&(((A<<4|0)<(q|0)|((i|0)<(A|0)|(j|0)<(l|0)))^1)&1}if((c[r>>2]|0)==-1)k=0;else{k=c[x>>2]|0;A=c[k+8>>2]|0;k=c[k+12>>2]|0;k=(k<<4|0)>=(p|0)&(((A<<4|0)<(q|0)|((i|0)<(A|0)|(j|0)<(k|0)))^1)&1}if((c[t>>2]|0)==-1)i=0;else{A=c[y>>2]|0;z=c[A+8>>2]|0;A=c[A+12>>2]|0;i=(A<<4|0)>=(p|0)&(((z<<4|0)<(q|0)|((i|0)<(z|0)|(j|0)<(A|0)))^1)&1}if(!(k|l|i)){ma(oa,7,30459,B);h=c[m>>2]|0}m=d+17780|0;n=d+712|0;o=d+716|0;if(!((h|0)!=-1?(B=c[w>>2]|0,((c[B+92>>2]|0)==(c[m>>2]|0)?(c[B+84>>2]|0)==(c[n>>2]|0):0)&(c[B+88>>2]|0)==(c[o>>2]|0)):0))ma(oa,7,30493,C);if(!((c[r>>2]|0)!=-1?(C=c[x>>2]|0,((c[C+92>>2]|0)==(c[m>>2]|0)?(c[C+84>>2]|0)==(c[n>>2]|0):0)&(c[C+88>>2]|0)==(c[o>>2]|0)):0))ma(oa,7,30493,D);if(!((c[t>>2]|0)!=-1?(D=c[y>>2]|0,((c[D+92>>2]|0)==(c[m>>2]|0)?(c[D+84>>2]|0)==(c[n>>2]|0):0)&(c[D+88>>2]|0)==(c[o>>2]|0)):0))ma(oa,7,30493,E);Ta(oa,q,p);k=d+688|0;i=d+696|0;c[i>>2]=c[k>>2];l=d+692|0;j=d+700|0;c[j>>2]=c[l>>2];if(Cb(R)|0){c[i>>2]=(Db(R,16)|0)+1;c[j>>2]=(Db(R,16)|0)+1}h=d+988|0;E=c[h>>2]|0;if(sb((c[Y>>2]|0)+12+(E*152|0)+32|0,c[k>>2]|0,c[l>>2]|0,c[n>>2]|0,c[o>>2]|0,32,c[d+17804>>2]|0,u+12+(E*152|0)+20|0,c[u+4>>2]|0,c[u>>2]|0)|0)ma(oa,2,30540,F);F=c[h>>2]|0;a[u+12+(F*152|0)+16>>0]=0;c[u+12+(F*152|0)+116>>2]=c[n>>2];c[u+12+(F*152|0)+120>>2]=c[o>>2];c[u+12+(F*152|0)+124>>2]=c[m>>2];c[u+12+(F*152|0)+128>>2]=c[d+680>>2];c[u+12+(F*152|0)+132>>2]=c[d+684>>2];c[u+12+(F*152|0)+136>>2]=c[i>>2];c[u+12+(F*152|0)+140>>2]=c[j>>2];c[d+1016>>2]=Cb(R)|0;if(!(Cb(R)|0))h=a[30572+(Db(R,2)|0)>>0]|0;else h=4;a[d+1212>>0]=h;h=c[w>>2]|0;tc(d+804|0,c[h+8>>2]|0,c[h+12>>2]|0,c[k>>2]|0,c[l>>2]|0);h=c[x>>2]|0;tc(d+868|0,c[h+8>>2]|0,c[h+12>>2]|0,c[k>>2]|0,c[l>>2]|0);h=c[y>>2]|0;tc(d+932|0,c[h+8>>2]|0,c[h+12>>2]|0,c[k>>2]|0,c[l>>2]|0);h=c[v>>2]|0}}while(0);j=d+988|0;F=c[j>>2]|0;E=c[Y>>2]|0;c[E+12+(F*152|0)+128>>2]=c[d+680>>2];c[E+12+(F*152|0)+132>>2]=c[d+684>>2];c[E+12+(F*152|0)+136>>2]=c[d+696>>2];c[E+12+(F*152|0)+140>>2]=c[d+700>>2];if(h|0)ma(oa,7,30576,H);if(!(c[G>>2]|0)){c[d+4352>>2]=Cb(R)|0;H=Cb(R)|0;c[d+17792>>2]=H;if(!H)kg(d+4548|0,0,13224)|0}else{c[d+4352>>2]=0;c[d+17792>>2]=1}c[d+4544>>2]=Db(R,2)|0;h=c[d+17860>>2]|0;if(h){i=h;h=0;do{if(!(i&1)){k=c[d+732+(h<<2)>>2]|0;c[d+764+(h<<2)>>2]=k}else{c[d+764+(h<<2)>>2]=c[j>>2];k=I+12+((c[j>>2]|0)*152|0)|0;c[k>>2]=(c[k>>2]|0)+1;k=c[d+732+(h<<2)>>2]|0}l=I+12+(k*152|0)|0;if((k|0)>-1)c[l>>2]=(c[l>>2]|0)+1;h=h+1|0;i=i>>1}while((i|0)!=0);if(h>>>0<8)J=91}else{h=0;J=91}if((J|0)==91)do{H=c[d+732+(h<<2)>>2]|0;c[d+764+(h<<2)>>2]=H;i=I+12+(H*152|0)|0;if((H|0)>-1)c[i>>2]=(c[i>>2]|0)+1;h=h+1|0}while((h|0)!=8);c[d+18716>>2]=1;if(!(((c[la>>2]|0)!=0?(a[na>>0]|0)==0:0)?!(c[G>>2]|0):0))Zb(oa);c[d+4372>>2]=Db(R,6)|0;c[d+4380>>2]=Db(R,3)|0;h=d+4389|0;a[h>>0]=0;I=(Cb(R)|0)&255;a[d+4388>>0]=I;if(I<<24>>24?(I=(Cb(R)|0)&255,a[h>>0]=I,I<<24>>24):0){if(Cb(R)|0)a[d+4390>>0]=Eb(R,6)|0;if(Cb(R)|0)a[d+4391>>0]=Eb(R,6)|0;if(Cb(R)|0)a[d+4392>>0]=Eb(R,6)|0;if(Cb(R)|0)a[d+4393>>0]=Eb(R,6)|0;if(Cb(R)|0)a[d+4398>>0]=Eb(R,6)|0;if(Cb(R)|0)a[d+4399>>0]=Eb(R,6)|0}n=d+1052|0;c[n>>2]=Db(R,8)|0;if(!(Cb(R)|0))h=0;else h=Eb(R,4)|0;p=d+1056|0;c[p>>2]=h;if(!(Cb(R)|0))h=0;else h=Eb(R,4)|0;o=d+1060|0;c[o>>2]=h;if(!(Cb(R)|0))h=0;else h=Eb(R,4)|0;l=d+1064|0;c[l>>2]=h;m=d+17780|0;c[d+17784>>2]=c[m>>2];if((c[n>>2]|0)==0?(c[p>>2]|0)==0:0)h=(c[o>>2]|h|0)==0&1;else h=0;c[d+420>>2]=h;k=d+4412|0;h=d+4413|0;a[h>>0]=0;i=d+4414|0;a[i>>0]=0;I=(Cb(R)|0)&255;a[k>>0]=I;do if(!(I<<24>>24)){h=c[n>>2]|0;J=165}else{I=(Cb(R)|0)&255;a[h>>0]=I;do if(I<<24>>24){if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4417>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4418>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4419>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4420>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4421>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4422>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4423>>0]=h;I=(Cb(R)|0)&255;a[d+4416>>0]=I;if(!(I<<24>>24)){I=d+4424|0;a[I>>0]=255;a[I+1>>0]=255;a[I+2>>0]=255;break}if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4424>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4425>>0]=h;if(!(Cb(R)|0))h=-1;else h=(Db(R,8)|0)&255;a[d+4426>>0]=h}while(0);I=(Cb(R)|0)&255;a[i>>0]=I;if(I<<24>>24){a[d+4415>>0]=Cb(R)|0;xc(k);i=0;do{do if(!(Cb(R)|0))h=0;else{yc(k,i,0);I=zc(0)|0;h=((s(I|0)|0)^31)+1|0;h=Db(R,(I|0)==0?0:h)|0;h=(h|0)>(I|0)?I:h;if(!(Ac(0)|0))break;I=(Cb(R)|0)==0;h=I?h:0-h|0}while(0);Bc(k,i,0,h);do if(!(Cb(R)|0))h=0;else{yc(k,i,1);I=zc(1)|0;h=((s(I|0)|0)^31)+1|0;h=Db(R,(I|0)==0?0:h)|0;h=(h|0)>(I|0)?I:h;if(!(Ac(1)|0))break;I=(Cb(R)|0)==0;h=I?h:0-h|0}while(0);Bc(k,i,1,h);do if(!(Cb(R)|0))h=0;else{yc(k,i,2);I=zc(2)|0;h=((s(I|0)|0)^31)+1|0;h=Db(R,(I|0)==0?0:h)|0;h=(h|0)>(I|0)?I:h;if(!(Ac(2)|0))break;I=(Cb(R)|0)==0;h=I?h:0-h|0}while(0);Bc(k,i,2,h);do if(!(Cb(R)|0))h=0;else{yc(k,i,3);I=zc(3)|0;h=((s(I|0)|0)^31)+1|0;h=Db(R,(I|0)==0?0:h)|0;h=(h|0)>(I|0)?I:h;if(!(Ac(3)|0))break;I=(Cb(R)|0)==0;h=I?h:0-h|0}while(0);Bc(k,i,3,h);i=i+1|0}while((i|0)!=8)}h=c[n>>2]|0;if(!(a[k>>0]|0)){J=165;break}I=Zc(k,0,h)|0;b[d+1068>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1070>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1100>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1102>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,1,c[n>>2]|0)|0;b[d+1072>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1074>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1104>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1106>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,2,c[n>>2]|0)|0;b[d+1076>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1078>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1108>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1110>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,3,c[n>>2]|0)|0;b[d+1080>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1082>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1112>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1114>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,4,c[n>>2]|0)|0;b[d+1084>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1086>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1116>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1118>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,5,c[n>>2]|0)|0;b[d+1088>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1090>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1120>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1122>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,6,c[n>>2]|0)|0;b[d+1092>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1094>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1124>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1126>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0;I=Zc(k,7,c[n>>2]|0)|0;b[d+1096>>1]=Xc(I,c[p>>2]|0,c[m>>2]|0)|0;b[d+1098>>1]=Yc(I,0,c[m>>2]|0)|0;b[d+1128>>1]=Xc(I,c[o>>2]|0,c[m>>2]|0)|0;b[d+1130>>1]=Yc(I,c[l>>2]|0,c[m>>2]|0)|0}while(0);if((J|0)==165){b[d+1068>>1]=Xc(h,c[p>>2]|0,c[m>>2]|0)|0;b[d+1070>>1]=Yc(h,0,c[m>>2]|0)|0;b[d+1100>>1]=Xc(h,c[o>>2]|0,c[m>>2]|0)|0;b[d+1102>>1]=Yc(h,c[l>>2]|0,c[m>>2]|0)|0}Fc(c[d+1040>>2]|0,ia,ja);h=c[ia>>2]|0;i=(c[ja>>2]|0)-h|0;k=d+17796|0;c[k>>2]=h;a:do if(i)do{i=i+-1|0;J=(Cb(R)|0)==0;h=c[k>>2]|0;if(J)break a;h=h+1|0;c[k>>2]=h}while((i|0)!=0);while(0);if((h|0)>6)ma(oa,7,30636,K);K=Cb(R)|0;i=d+17800|0;c[i>>2]=K;if(K|0){K=Cb(R)|0;c[i>>2]=(c[i>>2]|0)+K}h=Db(R,16)|0;if(h|0){t=k;break}ma(oa,7,30667,M);h=0;t=k}else{i=c[d+732+((Db(R,3)|0)<<2)>>2]|0;if(!((i|0)>=0?(c[I+12+(i*152|0)>>2]|0)>=1:0)){c[k>>2]=i;ma(oa,5,30345,k)}j=d+988|0;h=c[j>>2]|0;if((h|0)>-1?(l=I+12+(h*152|0)|0,m=c[l>>2]|0,(m|0)>0):0)c[l>>2]=m+-1;c[j>>2]=i;h=I+12+(i*152|0)|0;c[h>>2]=(c[h>>2]|0)+1;c[d+17860>>2]=0;c[d+4372>>2]=0;c[d+1e3>>2]=1;h=0;t=d+17796|0;i=d+17800|0}while(0);q=c[i>>2]|0;n=1<<q;o=c[t>>2]|0;r=1<<o;l=c[j>>2]|0;m=c[Y>>2]|0;u=m+12+(l*152|0)+32|0;c[d+344>>2]=u;if(!h){c[g>>2]=e+((c[N>>2]|0)>>>0<3?1:2);L=qa;return}p=e+(Bb(R)|0)|0;if((ea-p|0)>>>0<h>>>0)ma(oa,7,30687,S);e=d+17788|0;if(((((c[e>>2]|0)==0?(c[d+688>>2]|0)==(c[d+704>>2]|0):0)?(c[d+692>>2]|0)==(c[d+708>>2]|0):0)?(a[Q>>0]|0)==0:0)?(c[d+1004>>2]|0)!=0:0)k=(c[O>>2]|0)!=0&1;else k=0;c[d+1180>>2]=k;Vb(d,c[d+712>>2]|0,c[d+716>>2]|0);M=d+4536|0;N=d+4540|0;O=d+4544|0;ig(c[M>>2]|0,(c[N>>2]|0)+((c[O>>2]|0)*2044|0)|0,2044)|0;if(!(c[(c[M>>2]|0)+2040>>2]|0))ma(oa,7,30729,T);K=d+424|0;c[K>>2]=0;T=Ua(d,p,h)|0;c[m+12+(l*152|0)+144>>2]=T;if(T|0)ma(oa,7,30760,U);J=d+4372|0;k=c[J>>2]|0;if(k|0?(c[d+17808>>2]|0)==0:0)Hc(oa,k);H=d+17896|0;m=c[H>>2]|0;k=n<<o;if((m|0)!=0?(k|0)==(c[d+18672>>2]|0):0)l=d+18704|0;else{l=d+18704|0;U=c[l>>2]|0;qb(m);U=nb(32,(((U|0)>1?U:0)+k|0)*16016|0)|0;c[H>>2]=U;if(!U)ma(oa,2,30807,W);c[d+18672>>2]=k}W=(r|0)>1&((q|0)==0&(c[l>>2]|0)>1);p=p+h|0;I=Mb()|0;B=d+1040|0;o=(c[B>>2]|0)+7&-8;E=c[t>>2]|0;G=1<<E;i=c[i>>2]|0;F=1<<i;do if(W){l=c[l>>2]|0;n=(l|0)<(G|0);r=n?l:G;t=d+18668|0;if(!(c[t>>2]|0)){ka=ob(l*24|0)|0;m=d+17892|0;c[m>>2]=ka;if(!ka)ma(oa,2,30848,ba);if((l|0)>0){i=l+-1|0;j=I+4|0;h=0;do{k=(c[m>>2]|0)+(h*24|0)|0;c[t>>2]=(c[t>>2]|0)+1;V[c[I>>2]&15](k);do if((h|0)<(i|0)){if(P[c[j>>2]&7](k)|0)break;ma(oa,1,30885,ca)}while(0);h=h+1|0}while((h|0)!=(l|0))}}m=(r|0)>0;if(m){i=d+17892|0;j=d+18672|0;k=I+8|0;l=d+17792|0;h=0;do{ka=c[i>>2]|0;ga=c[H>>2]|0;ja=(c[j>>2]|0)+h|0;P[c[k>>2]&7](ka+(h*24|0)|0)|0;ig(ga+(ja*16016|0)+13280|0,d|0,432)|0;c[ga+(ja*16016|0)+13548>>2]=(c[l>>2]|0)==0?ga+(ja*16016|0)+48|0:0;c[ka+(h*24|0)+8>>2]=20;c[ka+(h*24|0)+12>>2]=ga+(ja*16016|0);c[ka+(h*24|0)+16>>2]=d;h=h+1|0}while((h|0)<(r|0))}kg(c[d+17840>>2]|0,0,o*6|0)|0;kg(c[d+17836>>2]|0,0,o|0)|0;Rc(oa);h=d+17900|0;Wa(d,p,f,G,F,h);Pf(h,G,12,21);if(n){if((G|0)>2){h=0;i=G+-2|0;do{ja=d+17900+(h*12|0)|0;c[ia>>2]=c[ja>>2];c[ia+4>>2]=c[ja+4>>2];c[ia+8>>2]=c[ja+8>>2];ka=d+17900+(i*12|0)|0;c[ja>>2]=c[ka>>2];c[ja+4>>2]=c[ka+4>>2];c[ja+8>>2]=c[ka+8>>2];c[ka>>2]=c[ia>>2];c[ka+4>>2]=c[ia+4>>2];c[ka+8>>2]=c[ia+8>>2];h=h+2|0;i=i+-2|0}while((h|0)<(i|0))}}else{c[ia>>2]=c[h>>2];c[ia+4>>2]=c[h+4>>2];c[ia+8>>2]=c[h+8>>2];ka=G+-1|0;jg(h|0,d+17912|0,ka*12|0)|0;ka=d+17900+(ka*12|0)|0;c[ka>>2]=c[ia>>2];c[ka+4>>2]=c[ia+4>>2];c[ka+8>>2]=c[ia+8>>2]}q=d+17792|0;if(m&(c[q>>2]|0)==0){i=d+17892|0;h=0;do{kg((c[(c[i>>2]|0)+(h*24|0)+12>>2]|0)+48|0,0,13224)|0;h=h+1|0}while((h|0)<(r|0))}o=(G|0)/(r|0)|0;n=G-(o*r|0)|0;do if(m){p=d+17892|0;j=r+-1|0;k=I+16|0;l=I+12|0;h=0;i=0;do{ka=c[p>>2]|0;m=ka+(i*24|0)|0;ja=c[ka+(i*24|0)+12>>2]|0;c[ja+4>>2]=h;h=h+o+((i+n|0)/(r|0)|0)|0;c[ja+8>>2]=h+-1;c[ja>>2]=f;c[ka+(i*24|0)+20>>2]=0;if((i|0)==(j|0))V[c[k>>2]&15](m);else V[c[l>>2]&15](m);i=i+1|0}while((i|0)<(r|0));l=I+8|0;h=0;j=r;do{ka=c[p>>2]|0;k=j;j=j+-1|0;i=c[ka+(j*24|0)+12>>2]|0;ka=(P[c[l>>2]&7](ka+(j*24|0)|0)|0)==0&1;c[K>>2]=c[K>>2]|ka;if(!h)h=c[i>>2]|0}while((k|0)>1);if(c[q>>2]|0)break;j=d+4548|0;i=0;do{Wc(j,(c[(c[p>>2]|0)+(i*24|0)+12>>2]|0)+48|0,1);i=i+1|0}while((i|0)<(r|0))}else h=0;while(0);c[g>>2]=h;if(c[K>>2]|0){ma(oa,7,30921,ha);break}if(c[d+17808>>2]|0)break;Tc(u,oa,d,c[J>>2]|0,0,0,c[d+17892>>2]|0,c[t>>2]|0,d+18676|0)}else{if(c[J>>2]|0){h=d+17808|0;do if((c[h>>2]|0)==0?(Z=d+17868|0,_=d+17880|0,(c[_>>2]|0)==0):0){ha=nb(32,284)|0;c[_>>2]=ha;if(!ha)ma(oa,2,30961,$);c[d+17876>>2]=22;if((c[l>>2]|0)<=1)break;if(P[c[I+4>>2]&7](Z)|0)break;ma(oa,1,31001,aa)}while(0);if(c[J>>2]|0?(c[h>>2]|0)==0:0){ha=c[d+17880>>2]|0;P[c[I+8>>2]&7](d+17868|0)|0;Qc(ha,(c[Y>>2]|0)+12+((c[j>>2]|0)*152|0)+32|0,oa,d)}}z=d+17840|0;kg(c[z>>2]|0,0,o*6|0)|0;A=d+17836|0;kg(c[A>>2]|0,0,o|0)|0;Rc(oa);Wa(d,p,f,G,F,ia);if((i|0)!=31){D=(E|0)==31;r=d+17792|0;t=d+4548|0;u=d+18700|0;v=d+1100|0;w=d+1068|0;q=d+1044|0;if(!D){p=0;do{x=p<<E;o=0;do{h=(c[H>>2]|0)+(x*16016|0)+(o*16016|0)|0;y=h+13280|0;ig(y|0,d|0,432)|0;c[y+424>>2]=0;c[y+268>>2]=(c[r>>2]|0)==0?t:0;i=h+13712|0;kg(i|0,0,2048)|0;Ec(y+272|0,oa,p,o);j=c[ia+(p*768|0)+(o*12|0)>>2]|0;k=c[ia+(p*768|0)+(o*12|0)+4>>2]|0;m=c[da>>2]|0;n=c[u>>2]|0;if((k+-1|0)>>>0>=(ea-j|0)>>>0)ma(oa,7,31036,fa);if(yb(h+12|0,j,k,m,n)|0){c[ga>>2]=1;ma(oa,2,31076,ga)}ba=y+332|0;c[y>>2]=i;c[y+348>>2]=c[z>>2];aa=y+44|0;c[aa>>2]=c[w>>2];c[aa+4>>2]=c[w+4>>2];c[aa+8>>2]=c[w+8>>2];c[aa+12>>2]=c[w+12>>2];c[aa+16>>2]=c[w+16>>2];c[aa+20>>2]=c[w+20>>2];c[aa+24>>2]=c[w+24>>2];c[aa+28>>2]=c[w+28>>2];aa=c[z>>2]|0;ca=(c[B>>2]<<1)+14&-16;c[ba>>2]=c[M>>2];c[y+88>>2]=i;c[y+352>>2]=aa+ca;ca=y+132|0;c[ca>>2]=c[v>>2];c[ca+4>>2]=c[v+4>>2];c[ca+8>>2]=c[v+8>>2];c[ca+12>>2]=c[v+12>>2];c[ca+16>>2]=c[v+16>>2];c[ca+20>>2]=c[v+20>>2];c[ca+24>>2]=c[v+24>>2];c[ca+28>>2]=c[v+28>>2];ca=c[z>>2]|0;aa=(c[B>>2]<<2)+28&-32;c[ba>>2]=c[M>>2];c[y+176>>2]=i;c[y+356>>2]=ca+aa;aa=y+220|0;c[aa>>2]=c[v>>2];c[aa+4>>2]=c[v+4>>2];c[aa+8>>2]=c[v+8>>2];c[aa+12>>2]=c[v+12>>2];c[aa+16>>2]=c[v+16>>2];c[aa+20>>2]=c[v+20>>2];c[aa+24>>2]=c[v+24>>2];c[aa+28>>2]=c[v+28>>2];aa=c[M>>2]|0;ca=c[A>>2]|0;f=c[q>>2]|0;h=aa;ha=(c[la>>2]|0)==0;c[ba>>2]=aa;c[y+408>>2]=ca;c[y+288>>2]=f;c[y+428>>2]=oa;do if(ha)h=7840;else{if(a[na>>0]|0){h=7840;break}h=h+126|0}while(0);c[y+312>>2]=h;o=o+1|0}while((o|0)<(G|0));p=p+1|0}while((p|0)<(F|0))}q=ja+4|0;r=d+18708|0;t=G+-1|0;u=ja+8|0;v=ja+12|0;w=d+17808|0;x=d+17868|0;y=d+17880|0;z=d+1032|0;A=I+8|0;B=I+12|0;C=I+16|0;p=0;do{Cc(ja,oa,p);h=c[ja>>2]|0;b:do if((h|0)<(c[q>>2]|0)){o=p<<E;if(D)while(1){do if(c[J>>2]|0){if(c[w>>2]|0)break;i=c[y>>2]|0;if((h|0)<8)break;if((h+8|0)>=(c[z>>2]|0))break;P[c[A>>2]&7](x)|0;c[i+272>>2]=h+-8;c[i+276>>2]=h;if((c[l>>2]|0)>1){V[c[B>>2]&15](x);break}else{V[c[C>>2]&15](x);break}}while(0);h=h+8|0;if((h|0)>=(c[q>>2]|0))break b}do{k=0;do{n=(c[r>>2]|0)==0?k:t-k|0;m=(c[H>>2]|0)+(o*16016|0)+(n*16016|0)|0;Dc(ja,oa,n);n=m+13280|0;i=n+360|0;j=i+48|0;do{c[i>>2]=0;i=i+4|0}while((i|0)<(j|0));i=n+412|0;c[i>>2]=0;c[i+4>>2]=0;i=c[u>>2]|0;if((i|0)<(c[v>>2]|0))do{Ya(m,d,h,i,12,4);i=i+8|0}while((i|0)<(c[v>>2]|0));ia=c[K>>2]|c[n+424>>2];c[K>>2]=ia;if(ia|0)ma(oa,7,31111,ka);k=k+1|0}while((G|0)>(k|0));do if(c[J>>2]|0){if(c[w>>2]|0)break;i=c[y>>2]|0;if((h|0)<8)break;if((h+8|0)>=(c[z>>2]|0))break;P[c[A>>2]&7](x)|0;c[i+272>>2]=h+-8;c[i+276>>2]=h;if((c[l>>2]|0)>1){V[c[B>>2]&15](x);break}else{V[c[C>>2]&15](x);break}}while(0);h=h+8|0}while((h|0)<(c[q>>2]|0))}while(0);p=p+1|0}while((p|0)<(F|0))}if(c[J>>2]|0?(c[d+17808>>2]|0)==0:0){ka=d+17868|0;ia=c[d+17880>>2]|0;P[c[I+8>>2]&7](ka)|0;ja=ia+276|0;c[ia+272>>2]=c[ja>>2];c[ja>>2]=c[d+1032>>2];V[c[I+16>>2]&15](ka)}c[g>>2]=Ab((c[H>>2]|0)+((F<<E)*16016|0)+-16016+12|0)|0}while(0);if(!(c[K>>2]|0)){if((((c[e>>2]|0)==0?(c[d+17792>>2]|0)==0:0)?(Xb(oa),c[la>>2]|0):0)?(a[na>>0]|0)==0:0){Yb(oa);ac(oa,c[d+1016>>2]|0)}}else ma(oa,7,30921,pa);if(!(c[d+4352>>2]|0)){L=qa;return}ig((c[N>>2]|0)+((c[O>>2]|0)*2044|0)|0,c[M>>2]|0,2044)|0;L=qa;return}function Qa(a){a=a|0;var b=0;b=L;L=L+16|0;ma(a,7,31447,b);L=b;return}function Ra(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;i=L;L=L+32|0;h=i+16|0;g=i+8|0;f=i;e=a+17344|0;if((c[e>>2]|0)>>>0>1){d=(Cb(b)|0)==0;d=d?10:12}else d=8;c[a+17348>>2]=d;d=Db(b,3)|0;c[a+248>>2]=d;if((d|0)==7){c[a+252>>2]=1;switch(c[e>>2]|0){case 3:case 1:break;default:{ma(a,5,31403,i+24|0);L=i;return}}c[a+280>>2]=0;c[a+284>>2]=0;if(!(Cb(b)|0)){L=i;return}ma(a,5,31386,h);L=i;return}c[a+252>>2]=Cb(b)|0;switch(c[e>>2]|0){case 3:case 1:break;default:{c[a+280>>2]=1;c[a+284>>2]=1;L=i;return}}h=a+280|0;c[h>>2]=Cb(b)|0;e=Cb(b)|0;c[a+284>>2]=e;if((e|0)==1&(c[h>>2]|0)==1)ma(a,5,31342,f);if(!(Cb(b)|0)){L=i;return}ma(a,5,31386,g);L=i;return}function Sa(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=L;L=L+16|0;j=b+17400|0;k=c[j>>2]|0;l=(Db(d,16)|0)+1|0;Ta(b,l,(Db(d,16)|0)+1|0);l=b+256|0;g=b+264|0;c[g>>2]=c[l>>2];h=b+260|0;i=b+268|0;c[i>>2]=c[h>>2];if(Cb(d)|0){c[g>>2]=(Db(d,16)|0)+1;c[i>>2]=(Db(d,16)|0)+1}d=b+556|0;n=c[d>>2]|0;e=b+280|0;f=b+284|0;if(sb((c[j>>2]|0)+12+(n*152|0)+32|0,c[l>>2]|0,c[h>>2]|0,c[e>>2]|0,c[f>>2]|0,32,c[b+17372>>2]|0,k+12+(n*152|0)+20|0,c[k+4>>2]|0,c[k>>2]|0)|0)ma(b,2,30540,m);n=c[d>>2]|0;a[k+12+(n*152|0)+16>>0]=0;c[k+12+(n*152|0)+116>>2]=c[e>>2];c[k+12+(n*152|0)+120>>2]=c[f>>2];c[k+12+(n*152|0)+124>>2]=c[b+17348>>2];c[k+12+(n*152|0)+128>>2]=c[b+248>>2];c[k+12+(n*152|0)+132>>2]=c[b+252>>2];c[k+12+(n*152|0)+136>>2]=c[g>>2];c[k+12+(n*152|0)+140>>2]=c[i>>2];L=m;return}function Ta(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;i=L;L=L+16|0;h=i+8|0;f=i;e=a+256|0;if(!((c[e>>2]|0)==(b|0)?(c[a+260>>2]|0)==(d|0):0)){if((b+7>>3|0)<=(c[a+608>>2]|0)?(d+7>>3|0)<=(c[a+600>>2]|0):0)Nb(a,b,d);else g=5;if((g|0)==5?Pb(a,b,d)|0:0)ma(a,2,31269,f);Rb(a);c[e>>2]=b;c[a+260>>2]=d}d=a+296|0;g=c[d>>2]|0;e=c[g+4>>2]|0;b=a+600|0;if((e|0?(c[b>>2]|0)<=(c[g+8>>2]|0):0)?(c[a+608>>2]|0)<=(c[g+12>>2]|0):0){L=i;return}qb(e);g=c[b>>2]|0;e=c[d>>2]|0;c[e+8>>2]=g;f=c[a+608>>2]|0;c[e+12>>2]=f;g=pb(f*g|0,12)|0;c[(c[d>>2]|0)+4>>2]=g;if(g|0){L=i;return}ma(a,2,31304,h);L=i;return}function Ua(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;q=L;L=L+48|0;o=q+4|0;m=b+4536|0;p=c[m>>2]|0;if(yb(o,e,f,c[b+18696>>2]|0,c[b+18700>>2]|0)|0)ma(b+432|0,2,31235,q);l=o+4|0;n=o+8|0;if(!(c[b+420>>2]|0)){e=c[l>>2]|0;f=c[n>>2]|0;g=((e<<7)+128|0)>>>8;if((f|0)<0){zb(o);e=c[l>>2]|0;f=c[n>>2]|0}r=c[o>>2]|0;i=g<<24;j=r>>>0<i>>>0;g=j?g:e-g|0;h=d[1664+g>>0]|0;g=g<<h;e=r-(j?0:i)<<h;h=f-h|0;c[o>>2]=e;c[n>>2]=h;c[l>>2]=g;i=((g<<7)+128|0)>>>8;if((h|0)<0){zb(o);e=c[o>>2]|0;f=c[l>>2]|0;h=c[n>>2]|0}else f=g;g=i<<24;r=e>>>0<g>>>0;f=r?i:f-i|0;i=d[1664+f>>0]|0;f=f<<i;g=e-(r?0:g)<<i;i=h-i|0;c[o>>2]=g;c[n>>2]=i;c[l>>2]=f;e=(j?0:2)|(r^1)&1;if((e|0)==3){h=((f<<7)+128|0)>>>8;if((i|0)<0){zb(o);g=c[o>>2]|0;f=c[l>>2]|0;e=c[n>>2]|0}else e=i;s=h<<24;r=g>>>0<s>>>0;i=r?h:f-h|0;j=d[1664+i>>0]|0;c[o>>2]=g-(r?0:s)<<j;c[n>>2]=e-j;c[l>>2]=i<<j;e=b+1048|0;c[e>>2]=r?3:4;if(r)e=3;else{mb(o,p+1965|0);mb(o,p+1966|0);mb(o,p+1961|0);mb(o,p+1962|0);mb(o,p+1963|0);mb(o,p+1964|0);mb(o,p+1955|0);mb(o,p+1956|0);mb(o,p+1957|0);mb(o,p+1958|0);mb(o,p+1959|0);mb(o,p+1960|0);e=c[e>>2]|0}}else k=12}else{e=0;k=12}if((k|0)==12)c[b+1048>>2]=e;i=a[31954+e>>0]|0;h=0;do{j=h&255;f=c[l>>2]|0;g=((f<<7)+128|0)>>>8;e=c[n>>2]|0;if((e|0)<0){zb(o);e=c[n>>2]|0;f=c[l>>2]|0}u=c[o>>2]|0;t=g<<24;s=u>>>0<t>>>0;k=s?g:f-g|0;r=d[1664+k>>0]|0;c[o>>2]=u-(s?0:t)<<r;c[n>>2]=e-r;c[l>>2]=k<<r;if(!s){e=0;do{mb(o,p+174+(j*432|0)+(e*216|0)|0);mb(o,p+174+(j*432|0)+(e*216|0)+1|0);mb(o,p+174+(j*432|0)+(e*216|0)+2|0);mb(o,p+174+(j*432|0)+(e*216|0)+3|0);mb(o,p+174+(j*432|0)+(e*216|0)+4|0);mb(o,p+174+(j*432|0)+(e*216|0)+5|0);mb(o,p+174+(j*432|0)+(e*216|0)+6|0);mb(o,p+174+(j*432|0)+(e*216|0)+7|0);mb(o,p+174+(j*432|0)+(e*216|0)+8|0);mb(o,p+174+(j*432|0)+(e*216|0)+18|0);mb(o,p+174+(j*432|0)+(e*216|0)+19|0);mb(o,p+174+(j*432|0)+(e*216|0)+20|0);mb(o,p+174+(j*432|0)+(e*216|0)+21|0);mb(o,p+174+(j*432|0)+(e*216|0)+22|0);mb(o,p+174+(j*432|0)+(e*216|0)+23|0);mb(o,p+174+(j*432|0)+(e*216|0)+24|0);mb(o,p+174+(j*432|0)+(e*216|0)+25|0);mb(o,p+174+(j*432|0)+(e*216|0)+26|0);mb(o,p+174+(j*432|0)+(e*216|0)+27|0);mb(o,p+174+(j*432|0)+(e*216|0)+28|0);mb(o,p+174+(j*432|0)+(e*216|0)+29|0);mb(o,p+174+(j*432|0)+(e*216|0)+30|0);mb(o,p+174+(j*432|0)+(e*216|0)+31|0);mb(o,p+174+(j*432|0)+(e*216|0)+32|0);mb(o,p+174+(j*432|0)+(e*216|0)+33|0);mb(o,p+174+(j*432|0)+(e*216|0)+34|0);mb(o,p+174+(j*432|0)+(e*216|0)+35|0);mb(o,p+174+(j*432|0)+(e*216|0)+36|0);mb(o,p+174+(j*432|0)+(e*216|0)+37|0);mb(o,p+174+(j*432|0)+(e*216|0)+38|0);mb(o,p+174+(j*432|0)+(e*216|0)+39|0);mb(o,p+174+(j*432|0)+(e*216|0)+40|0);mb(o,p+174+(j*432|0)+(e*216|0)+41|0);mb(o,p+174+(j*432|0)+(e*216|0)+42|0);mb(o,p+174+(j*432|0)+(e*216|0)+43|0);mb(o,p+174+(j*432|0)+(e*216|0)+44|0);mb(o,p+174+(j*432|0)+(e*216|0)+45|0);mb(o,p+174+(j*432|0)+(e*216|0)+46|0);mb(o,p+174+(j*432|0)+(e*216|0)+47|0);mb(o,p+174+(j*432|0)+(e*216|0)+48|0);mb(o,p+174+(j*432|0)+(e*216|0)+49|0);mb(o,p+174+(j*432|0)+(e*216|0)+50|0);mb(o,p+174+(j*432|0)+(e*216|0)+51|0);mb(o,p+174+(j*432|0)+(e*216|0)+52|0);mb(o,p+174+(j*432|0)+(e*216|0)+53|0);mb(o,p+174+(j*432|0)+(e*216|0)+54|0);mb(o,p+174+(j*432|0)+(e*216|0)+55|0);mb(o,p+174+(j*432|0)+(e*216|0)+56|0);mb(o,p+174+(j*432|0)+(e*216|0)+57|0);mb(o,p+174+(j*432|0)+(e*216|0)+58|0);mb(o,p+174+(j*432|0)+(e*216|0)+59|0);mb(o,p+174+(j*432|0)+(e*216|0)+60|0);mb(o,p+174+(j*432|0)+(e*216|0)+61|0);mb(o,p+174+(j*432|0)+(e*216|0)+62|0);mb(o,p+174+(j*432|0)+(e*216|0)+63|0);mb(o,p+174+(j*432|0)+(e*216|0)+64|0);mb(o,p+174+(j*432|0)+(e*216|0)+65|0);mb(o,p+174+(j*432|0)+(e*216|0)+66|0);mb(o,p+174+(j*432|0)+(e*216|0)+67|0);mb(o,p+174+(j*432|0)+(e*216|0)+68|0);mb(o,p+174+(j*432|0)+(e*216|0)+69|0);mb(o,p+174+(j*432|0)+(e*216|0)+70|0);mb(o,p+174+(j*432|0)+(e*216|0)+71|0);mb(o,p+174+(j*432|0)+(e*216|0)+72|0);mb(o,p+174+(j*432|0)+(e*216|0)+73|0);mb(o,p+174+(j*432|0)+(e*216|0)+74|0);mb(o,p+174+(j*432|0)+(e*216|0)+75|0);mb(o,p+174+(j*432|0)+(e*216|0)+76|0);mb(o,p+174+(j*432|0)+(e*216|0)+77|0);mb(o,p+174+(j*432|0)+(e*216|0)+78|0);mb(o,p+174+(j*432|0)+(e*216|0)+79|0);mb(o,p+174+(j*432|0)+(e*216|0)+80|0);mb(o,p+174+(j*432|0)+(e*216|0)+81|0);mb(o,p+174+(j*432|0)+(e*216|0)+82|0);mb(o,p+174+(j*432|0)+(e*216|0)+83|0);mb(o,p+174+(j*432|0)+(e*216|0)+84|0);mb(o,p+174+(j*432|0)+(e*216|0)+85|0);mb(o,p+174+(j*432|0)+(e*216|0)+86|0);mb(o,p+174+(j*432|0)+(e*216|0)+87|0);mb(o,p+174+(j*432|0)+(e*216|0)+88|0);mb(o,p+174+(j*432|0)+(e*216|0)+89|0);mb(o,p+174+(j*432|0)+(e*216|0)+90|0);mb(o,p+174+(j*432|0)+(e*216|0)+91|0);mb(o,p+174+(j*432|0)+(e*216|0)+92|0);mb(o,p+174+(j*432|0)+(e*216|0)+93|0);mb(o,p+174+(j*432|0)+(e*216|0)+94|0);mb(o,p+174+(j*432|0)+(e*216|0)+95|0);mb(o,p+174+(j*432|0)+(e*216|0)+96|0);mb(o,p+174+(j*432|0)+(e*216|0)+97|0);mb(o,p+174+(j*432|0)+(e*216|0)+98|0);mb(o,p+174+(j*432|0)+(e*216|0)+99|0);mb(o,p+174+(j*432|0)+(e*216|0)+100|0);mb(o,p+174+(j*432|0)+(e*216|0)+101|0);mb(o,p+174+(j*432|0)+(e*216|0)+102|0);mb(o,p+174+(j*432|0)+(e*216|0)+103|0);mb(o,p+174+(j*432|0)+(e*216|0)+104|0);mb(o,p+174+(j*432|0)+(e*216|0)+105|0);mb(o,p+174+(j*432|0)+(e*216|0)+106|0);mb(o,p+174+(j*432|0)+(e*216|0)+107|0);mb(o,p+174+(j*432|0)+(e*216|0)+108|0);mb(o,p+174+(j*432|0)+(e*216|0)+109|0);mb(o,p+174+(j*432|0)+(e*216|0)+110|0);mb(o,p+174+(j*432|0)+(e*216|0)+111|0);mb(o,p+174+(j*432|0)+(e*216|0)+112|0);mb(o,p+174+(j*432|0)+(e*216|0)+113|0);mb(o,p+174+(j*432|0)+(e*216|0)+114|0);mb(o,p+174+(j*432|0)+(e*216|0)+115|0);mb(o,p+174+(j*432|0)+(e*216|0)+116|0);mb(o,p+174+(j*432|0)+(e*216|0)+126|0);mb(o,p+174+(j*432|0)+(e*216|0)+127|0);mb(o,p+174+(j*432|0)+(e*216|0)+128|0);mb(o,p+174+(j*432|0)+(e*216|0)+129|0);mb(o,p+174+(j*432|0)+(e*216|0)+130|0);mb(o,p+174+(j*432|0)+(e*216|0)+131|0);mb(o,p+174+(j*432|0)+(e*216|0)+132|0);mb(o,p+174+(j*432|0)+(e*216|0)+133|0);mb(o,p+174+(j*432|0)+(e*216|0)+134|0);mb(o,p+174+(j*432|0)+(e*216|0)+135|0);mb(o,p+174+(j*432|0)+(e*216|0)+136|0);mb(o,p+174+(j*432|0)+(e*216|0)+137|0);mb(o,p+174+(j*432|0)+(e*216|0)+138|0);mb(o,p+174+(j*432|0)+(e*216|0)+139|0);mb(o,p+174+(j*432|0)+(e*216|0)+140|0);mb(o,p+174+(j*432|0)+(e*216|0)+141|0);mb(o,p+174+(j*432|0)+(e*216|0)+142|0);mb(o,p+174+(j*432|0)+(e*216|0)+143|0);mb(o,p+174+(j*432|0)+(e*216|0)+144|0);mb(o,p+174+(j*432|0)+(e*216|0)+145|0);mb(o,p+174+(j*432|0)+(e*216|0)+146|0);mb(o,p+174+(j*432|0)+(e*216|0)+147|0);mb(o,p+174+(j*432|0)+(e*216|0)+148|0);mb(o,p+174+(j*432|0)+(e*216|0)+149|0);mb(o,p+174+(j*432|0)+(e*216|0)+150|0);mb(o,p+174+(j*432|0)+(e*216|0)+151|0);mb(o,p+174+(j*432|0)+(e*216|0)+152|0);mb(o,p+174+(j*432|0)+(e*216|0)+153|0);mb(o,p+174+(j*432|0)+(e*216|0)+154|0);mb(o,p+174+(j*432|0)+(e*216|0)+155|0);mb(o,p+174+(j*432|0)+(e*216|0)+156|0);mb(o,p+174+(j*432|0)+(e*216|0)+157|0);mb(o,p+174+(j*432|0)+(e*216|0)+158|0);mb(o,p+174+(j*432|0)+(e*216|0)+159|0);mb(o,p+174+(j*432|0)+(e*216|0)+160|0);mb(o,p+174+(j*432|0)+(e*216|0)+161|0);mb(o,p+174+(j*432|0)+(e*216|0)+162|0);mb(o,p+174+(j*432|0)+(e*216|0)+163|0);mb(o,p+174+(j*432|0)+(e*216|0)+164|0);mb(o,p+174+(j*432|0)+(e*216|0)+165|0);mb(o,p+174+(j*432|0)+(e*216|0)+166|0);mb(o,p+174+(j*432|0)+(e*216|0)+167|0);mb(o,p+174+(j*432|0)+(e*216|0)+168|0);mb(o,p+174+(j*432|0)+(e*216|0)+169|0);mb(o,p+174+(j*432|0)+(e*216|0)+170|0);mb(o,p+174+(j*432|0)+(e*216|0)+171|0);mb(o,p+174+(j*432|0)+(e*216|0)+172|0);mb(o,p+174+(j*432|0)+(e*216|0)+173|0);mb(o,p+174+(j*432|0)+(e*216|0)+174|0);mb(o,p+174+(j*432|0)+(e*216|0)+175|0);mb(o,p+174+(j*432|0)+(e*216|0)+176|0);mb(o,p+174+(j*432|0)+(e*216|0)+177|0);mb(o,p+174+(j*432|0)+(e*216|0)+178|0);mb(o,p+174+(j*432|0)+(e*216|0)+179|0);mb(o,p+174+(j*432|0)+(e*216|0)+180|0);mb(o,p+174+(j*432|0)+(e*216|0)+181|0);mb(o,p+174+(j*432|0)+(e*216|0)+182|0);mb(o,p+174+(j*432|0)+(e*216|0)+183|0);mb(o,p+174+(j*432|0)+(e*216|0)+184|0);mb(o,p+174+(j*432|0)+(e*216|0)+185|0);mb(o,p+174+(j*432|0)+(e*216|0)+186|0);mb(o,p+174+(j*432|0)+(e*216|0)+187|0);mb(o,p+174+(j*432|0)+(e*216|0)+188|0);mb(o,p+174+(j*432|0)+(e*216|0)+189|0);mb(o,p+174+(j*432|0)+(e*216|0)+190|0);mb(o,p+174+(j*432|0)+(e*216|0)+191|0);mb(o,p+174+(j*432|0)+(e*216|0)+192|0);mb(o,p+174+(j*432|0)+(e*216|0)+193|0);mb(o,p+174+(j*432|0)+(e*216|0)+194|0);mb(o,p+174+(j*432|0)+(e*216|0)+195|0);mb(o,p+174+(j*432|0)+(e*216|0)+196|0);mb(o,p+174+(j*432|0)+(e*216|0)+197|0);mb(o,p+174+(j*432|0)+(e*216|0)+198|0);mb(o,p+174+(j*432|0)+(e*216|0)+199|0);mb(o,p+174+(j*432|0)+(e*216|0)+200|0);mb(o,p+174+(j*432|0)+(e*216|0)+201|0);mb(o,p+174+(j*432|0)+(e*216|0)+202|0);mb(o,p+174+(j*432|0)+(e*216|0)+203|0);mb(o,p+174+(j*432|0)+(e*216|0)+204|0);mb(o,p+174+(j*432|0)+(e*216|0)+205|0);mb(o,p+174+(j*432|0)+(e*216|0)+206|0);mb(o,p+174+(j*432|0)+(e*216|0)+207|0);mb(o,p+174+(j*432|0)+(e*216|0)+208|0);mb(o,p+174+(j*432|0)+(e*216|0)+209|0);mb(o,p+174+(j*432|0)+(e*216|0)+210|0);mb(o,p+174+(j*432|0)+(e*216|0)+211|0);mb(o,p+174+(j*432|0)+(e*216|0)+212|0);mb(o,p+174+(j*432|0)+(e*216|0)+213|0);mb(o,p+174+(j*432|0)+(e*216|0)+214|0);mb(o,p+174+(j*432|0)+(e*216|0)+215|0);e=e+1|0}while((e|0)!=2)}h=h+1<<24>>24}while((h&255)<=(i&255));mb(o,p+1967|0);mb(o,p+1968|0);mb(o,p+1969|0);if(!(c[b+996>>2]|0)){u=c[n>>2]|0;u=u+-33|0;u=u>>>0<1073741791;u=u&1;L=q;return u|0}if(a[b+1012>>0]|0){u=c[n>>2]|0;u=u+-33|0;u=u>>>0<1073741791;u=u&1;L=q;return u|0}mb(o,p+1910|0);mb(o,p+1911|0);mb(o,p+1912|0);mb(o,p+1913|0);mb(o,p+1914|0);mb(o,p+1915|0);mb(o,p+1916|0);mb(o,p+1917|0);mb(o,p+1918|0);mb(o,p+1919|0);mb(o,p+1920|0);mb(o,p+1921|0);mb(o,p+1922|0);mb(o,p+1923|0);mb(o,p+1924|0);mb(o,p+1925|0);mb(o,p+1926|0);mb(o,p+1927|0);mb(o,p+1928|0);mb(o,p+1929|0);mb(o,p+1930|0);if((a[b+1212>>0]|0)==4){mb(o,p+1902|0);mb(o,p+1903|0);mb(o,p+1904|0);mb(o,p+1905|0);mb(o,p+1906|0);mb(o,p+1907|0);mb(o,p+1908|0);mb(o,p+1909|0)}mb(o,p+1931|0);mb(o,p+1932|0);mb(o,p+1933|0);mb(o,p+1934|0);j=b+4360|0;u=c[j>>2]|0;k=b+4364|0;if((c[k>>2]|0)==(u|0)?(c[b+4368>>2]|0)==(u|0):0)k=33;else{e=c[l>>2]|0;g=((e<<7)+128|0)>>>8;f=c[n>>2]|0;if((f|0)<0){zb(o);e=c[l>>2]|0;f=c[n>>2]|0}r=c[o>>2]|0;s=g<<24;u=r>>>0<s>>>0;g=u?g:e-g|0;t=d[1664+g>>0]|0;g=g<<t;e=r-(u?0:s)<<t;f=f-t|0;c[o>>2]=e;c[n>>2]=f;c[l>>2]=g;if(!u){h=((g<<7)+128|0)>>>8;if((f|0)<0){zb(o);e=c[o>>2]|0;g=c[l>>2]|0;f=c[n>>2]|0}t=h<<24;i=e>>>0<t>>>0;u=i?h:g-h|0;g=d[1664+u>>0]|0;c[o>>2]=e-(i?0:t)<<g;c[n>>2]=f-g;c[l>>2]=u<<g;f=b+4532|0;c[f>>2]=i?1:2;g=c[j>>2]|0;do if((g|0)!=(c[k>>2]|0)){e=b+4528|0;if((g|0)==(c[b+4368>>2]|0)){a[e>>0]=2;e=3;g=1;break}else{a[e>>0]=1;e=3;g=2;break}}else{a[b+4528>>0]=3;e=2;g=1}while(0);a[b+4529>>0]=g;a[b+4530>>0]=e;e=c[m>>2]|0;if(!i?(mb(o,e+1935|0),mb(o,e+1936|0),mb(o,e+1937|0),mb(o,e+1938|0),mb(o,e+1939|0),(c[f>>2]|0)!=1):0)k=40;else k=41}else k=33}if((k|0)==33){f=b+4532|0;c[f>>2]=0;e=c[m>>2]|0;k=40}if((k|0)==40?(mb(o,e+1940|0),mb(o,e+1941|0),mb(o,e+1942|0),mb(o,e+1943|0),mb(o,e+1944|0),mb(o,e+1945|0),mb(o,e+1946|0),mb(o,e+1947|0),mb(o,e+1948|0),mb(o,e+1949|0),c[f>>2]|0):0)k=41;if((k|0)==41){mb(o,e+1950|0);mb(o,e+1951|0);mb(o,e+1952|0);mb(o,e+1953|0);mb(o,e+1954|0)}mb(o,p);mb(o,p+1|0);mb(o,p+2|0);mb(o,p+3|0);mb(o,p+4|0);mb(o,p+5|0);mb(o,p+6|0);mb(o,p+7|0);mb(o,p+8|0);mb(o,p+9|0);mb(o,p+10|0);mb(o,p+11|0);mb(o,p+12|0);mb(o,p+13|0);mb(o,p+14|0);mb(o,p+15|0);mb(o,p+16|0);mb(o,p+17|0);mb(o,p+18|0);mb(o,p+19|0);mb(o,p+20|0);mb(o,p+21|0);mb(o,p+22|0);mb(o,p+23|0);mb(o,p+24|0);mb(o,p+25|0);mb(o,p+26|0);mb(o,p+27|0);mb(o,p+28|0);mb(o,p+29|0);mb(o,p+30|0);mb(o,p+31|0);mb(o,p+32|0);mb(o,p+33|0);mb(o,p+34|0);mb(o,p+35|0);mb(o,p+126|0);mb(o,p+127|0);mb(o,p+128|0);mb(o,p+129|0);mb(o,p+130|0);mb(o,p+131|0);mb(o,p+132|0);mb(o,p+133|0);mb(o,p+134|0);mb(o,p+135|0);mb(o,p+136|0);mb(o,p+137|0);mb(o,p+138|0);mb(o,p+139|0);mb(o,p+140|0);mb(o,p+141|0);mb(o,p+142|0);mb(o,p+143|0);mb(o,p+144|0);mb(o,p+145|0);mb(o,p+146|0);mb(o,p+147|0);mb(o,p+148|0);mb(o,p+149|0);mb(o,p+150|0);mb(o,p+151|0);mb(o,p+152|0);mb(o,p+153|0);mb(o,p+154|0);mb(o,p+155|0);mb(o,p+156|0);mb(o,p+157|0);mb(o,p+158|0);mb(o,p+159|0);mb(o,p+160|0);mb(o,p+161|0);mb(o,p+162|0);mb(o,p+163|0);mb(o,p+164|0);mb(o,p+165|0);mb(o,p+166|0);mb(o,p+167|0);mb(o,p+168|0);mb(o,p+169|0);mb(o,p+170|0);mb(o,p+171|0);mb(o,p+172|0);mb(o,p+173|0);u=c[b+1016>>2]|0;$a(p+1970|0,3,o);$a(p+1973|0,1,o);$a(p+1974|0,10,o);$a(p+1984|0,1,o);$a(p+1985|0,10,o);$a(p+2006|0,1,o);$a(p+2007|0,10,o);$a(p+2017|0,1,o);$a(p+2018|0,10,o);$a(p+1995|0,3,o);$a(p+1998|0,3,o);$a(p+2001|0,3,o);$a(p+2028|0,3,o);$a(p+2031|0,3,o);$a(p+2034|0,3,o);if(!u){u=c[n>>2]|0;u=u+-33|0;u=u>>>0<1073741791;u=u&1;L=q;return u|0}$a(p+2004|0,1,o);$a(p+2005|0,1,o);$a(p+2037|0,1,o);$a(p+2038|0,1,o);u=c[n>>2]|0;u=u+-33|0;u=u>>>0<1073741791;u=u&1;L=q;return u|0}function Va(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,w=0,y=0,A=0,B=0,E=0,F=0,G=0,H=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0;ka=L;L=L+32|0;fa=ka+8|0;ea=ka;ja=4;ga=kf(40)|0;c[ga>>2]=0;ba=ka+20|0;ia=ka+16|0;ca=ka+12|0;c[ba>>2]=b+13552;da=d+432|0;$=(1<<c[d+17796>>2])+-1|0;c[ia>>2]=0;c[ca>>2]=c[b+4>>2];aa=b+15760|0;ha=b+15848|0;c[ha>>2]=1;ja=fg(b+15852|0,1,ga|0,ja|0)|0;ga=v()|0;j=0;e=j;j=0;if((e|0)!=0&(k|0)!=0){f=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!f)I(e|0,k|0);u(k|0)}else f=-1;e=v()|0;if((f|0)!=1)e=0;a:while(1){if(e|0){f=4;break}q=b+13280|0;r=b+13704|0;c[r>>2]=0;s=d+17900|0;t=b+13712|0;w=b+12|0;y=d+18696|0;A=d+18700|0;B=d+17840|0;E=d+1040|0;F=d+4536|0;G=b+13612|0;H=d+1100|0;J=b+13628|0;K=b+13324|0;M=d+1068|0;N=b+13368|0;O=b+13632|0;P=b+13412|0;Q=b+13456|0;R=b+13636|0;S=b+13500|0;n=d+17836|0;o=d+1044|0;p=d+996|0;T=b+13688|0;U=b+13568|0;V=b+13708|0;W=b+13592|0;X=d+1012|0;Y=b+13640|0;Z=b+13692|0;_=b+8|0;while(1){g=c[ca>>2]|0;kg(t|0,0,2048)|0;m=s+(g*12|0)+8|0;j=0;C(1,c[ba>>2]|0,da|0,0,c[m>>2]|0);e=j;j=0;if((e|0)!=0&(k|0)!=0){f=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!f)I(e|0,k|0);u(k|0)}else f=-1;e=v()|0;if((f|0)==1)continue a;l=c[s+(g*12|0)>>2]|0;f=c[s+(g*12|0)+4>>2]|0;g=c[y>>2]|0;h=c[A>>2]|0;if((f+-1|0)>>>0>=((c[b>>2]|0)-l|0)>>>0){j=0;C(2,aa|0,7,31036,ea|0);e=j;j=0;if((e|0)!=0&(k|0)!=0){i=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!i)I(e|0,k|0);u(k|0)}else i=-1;e=v()|0;if((i|0)==1)continue a}j=0;f=z(2,w|0,l|0,f|0,g|0,h|0)|0;e=j;j=0;if((e|0)!=0&(k|0)!=0){g=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!g)I(e|0,k|0);u(k|0)}else g=-1;e=v()|0;if((g|0)==1)continue a;if(f|0){j=0;c[fa>>2]=1;C(2,aa|0,2,31076,fa|0);e=j;j=0;if((e|0)!=0&(k|0)!=0){f=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!f)I(e|0,k|0);u(k|0)}else f=-1;e=v()|0;if((f|0)==1)continue a}c[q>>2]=t;c[J>>2]=c[B>>2];c[K>>2]=c[M>>2];c[K+4>>2]=c[M+4>>2];c[K+8>>2]=c[M+8>>2];c[K+12>>2]=c[M+12>>2];c[K+16>>2]=c[M+16>>2];c[K+20>>2]=c[M+20>>2];c[K+24>>2]=c[M+24>>2];c[K+28>>2]=c[M+28>>2];g=c[B>>2]|0;h=(c[E>>2]<<1)+14&-16;c[G>>2]=c[F>>2];c[N>>2]=t;c[O>>2]=g+h;c[P>>2]=c[H>>2];c[P+4>>2]=c[H+4>>2];c[P+8>>2]=c[H+8>>2];c[P+12>>2]=c[H+12>>2];c[P+16>>2]=c[H+16>>2];c[P+20>>2]=c[H+20>>2];c[P+24>>2]=c[H+24>>2];c[P+28>>2]=c[H+28>>2];h=c[B>>2]|0;g=(c[E>>2]<<2)+28&-32;c[G>>2]=c[F>>2];c[Q>>2]=t;c[R>>2]=h+g;c[S>>2]=c[H>>2];c[S+4>>2]=c[H+4>>2];c[S+8>>2]=c[H+8>>2];c[S+12>>2]=c[H+12>>2];c[S+16>>2]=c[H+16>>2];c[S+20>>2]=c[H+20>>2];c[S+24>>2]=c[H+24>>2];c[S+28>>2]=c[H+28>>2];g=c[F>>2]|0;h=c[n>>2]|0;i=c[o>>2]|0;e=g;l=(c[p>>2]|0)==0;c[G>>2]=g;c[T>>2]=h;c[U>>2]=i;c[V>>2]=da;if(!l?(a[X>>0]|0)==0:0)e=e+126|0;else e=7840;c[W>>2]=e;c[V>>2]=aa;e=c[c[ba>>2]>>2]|0;if((e|0)<(c[(c[ba>>2]|0)+4>>2]|0)){h=e;while(1){e=Y;f=e+48|0;do{c[e>>2]=0;e=e+4|0}while((e|0)<(f|0));e=Z;c[e>>2]=0;c[e+4>>2]=0;e=c[(c[ba>>2]|0)+8>>2]|0;if((e|0)<(c[(c[ba>>2]|0)+12>>2]|0)){f=e;while(1){j=0;D(1,b|0,d|0,h|0,f|0,12,4);e=j;j=0;if((e|0)!=0&(k|0)!=0){g=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!g)I(e|0,k|0);u(k|0)}else g=-1;e=v()|0;if((g|0)==1)continue a;f=f+8|0;if((f|0)>=(c[(c[ba>>2]|0)+12>>2]|0))break}}h=h+8|0;if((h|0)>=(c[(c[ba>>2]|0)+4>>2]|0))break}}if((c[m>>2]|0)==($|0)){j=0;f=x(4,w|0)|0;e=j;j=0;if((e|0)!=0&(k|0)!=0){g=gg(c[e>>2]|0,ja|0,ga|0)|0;if(!g)I(e|0,k|0);u(k|0)}else g=-1;e=v()|0;if((g|0)==1)continue a;c[ia>>2]=f}if(c[r>>2]|0){e=0;f=29;break a}m=c[ca>>2]|0;c[ca>>2]=m+1;if((m|0)>=(c[_>>2]|0)){e=1;f=29;break a}}}if((f|0)==4){c[ha>>2]=0;c[b+13704>>2]=1;c[b>>2]=0;ia=0;lf(ja|0);L=ka;return ia|0}else if((f|0)==29){c[b>>2]=c[ia>>2];ia=e;lf(ja|0);L=ka;return ia|0}return 0}function Wa(a,b,e,f,g,h){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;B=L;L=L+32|0;A=B+24|0;z=B+16|0;y=B+8|0;x=B;v=B+28|0;if((g|0)<=0){L=B;return}w=(f|0)>0;r=g+-1|0;s=f+-1|0;t=a+432|0;u=a+18696|0;q=a+18700|0;n=v+1|0;o=v+2|0;p=v+3|0;m=0;a=b;do{do if(w){if((m|0)!=(r|0)){j=0;do{c[h+(m*768|0)+(j*12|0)+8>>2]=j;b=c[u>>2]|0;i=c[q>>2]|0;if((e-a|0)>>>0<4)ma(t,7,31036,x);if(!b)i=(d[a+1>>0]|0)<<16|(d[a>>0]|0)<<24|(d[a+2>>0]|0)<<8|(d[a+3>>0]|0);else{X[b&63](i,a,v,4);i=(d[n>>0]|0)<<16|(d[v>>0]|0)<<24|(d[o>>0]|0)<<8|(d[p>>0]|0)}a=a+4|0;b=a;if(i>>>0>(e-b|0)>>>0)ma(t,7,31197,y);c[h+(m*768|0)+(j*12|0)>>2]=b;c[h+(m*768|0)+(j*12|0)+4>>2]=i;a=a+i|0;j=j+1|0}while((j|0)!=(f|0));break}l=0;do{k=h+(r*768|0)+(l*12|0)|0;c[h+(r*768|0)+(l*12|0)+8>>2]=l;i=c[u>>2]|0;j=c[q>>2]|0;b=e-a|0;if((l|0)!=(s|0)){if(b>>>0<4)ma(t,7,31036,z);if(!i)b=(d[a+1>>0]|0)<<16|(d[a>>0]|0)<<24|(d[a+2>>0]|0)<<8|(d[a+3>>0]|0);else{X[i&63](j,a,v,4);b=(d[n>>0]|0)<<16|(d[v>>0]|0)<<24|(d[o>>0]|0)<<8|(d[p>>0]|0)}a=a+4|0;if(b>>>0>(e-a|0)>>>0)ma(t,7,31197,A)}c[k>>2]=a;c[h+(r*768|0)+(l*12|0)+4>>2]=b;a=a+b|0;l=l+1|0}while((l|0)!=(f|0))}while(0);m=m+1|0}while((m|0)!=(g|0));L=B;return}function Xa(a,b){a=a|0;b=b|0;return (c[b+4>>2]|0)-(c[a+4>>2]|0)|0}function Ya(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0;A=i+-1|0;B=1<<A;C=B>>1;D=C+f|0;y=c[e+1032>>2]|0;v=(D|0)<(y|0);w=C+g|0;z=c[e+1040>>2]|0;x=(w|0)<(z|0);if(!((y|0)>(f|0)&(z|0)>(g|0)))return;y=b+13688|0;z=(f&7)+(b+13692)|0;t=(a[(c[y>>2]|0)+g>>0]|0)>>>A&1|A<<2|(a[z>>0]|0)>>>A<<1&2;r=c[b+13592>>2]|0;u=c[b+13548>>2]|0;s=b+12|0;do if(!(v&x)){j=v^1;if(x&j){l=d[r+(t*3|0)+1>>0]|0;o=b+16|0;l=(256-l+((c[o>>2]|0)*l|0)|0)>>>8;p=b+20|0;j=c[p>>2]|0;if((j|0)<0){zb(s);j=c[p>>2]|0}m=c[s>>2]|0;n=l<<24;if(m>>>0<n>>>0)k=1;else{k=3;m=m-n|0;l=(c[o>>2]|0)-l|0}r=d[1664+l>>0]|0;c[s>>2]=m<<r;c[p>>2]=j-r;c[o>>2]=l<<r;break}if(x|j)k=3;else{l=d[r+(t*3|0)+2>>0]|0;o=b+16|0;l=(256-l+((c[o>>2]|0)*l|0)|0)>>>8;p=b+20|0;j=c[p>>2]|0;if((j|0)<0){zb(s);j=c[p>>2]|0}m=c[s>>2]|0;n=l<<24;if(m>>>0<n>>>0)k=2;else{k=3;m=m-n|0;l=(c[o>>2]|0)-l|0}r=d[1664+l>>0]|0;c[s>>2]=m<<r;c[p>>2]=j-r;c[o>>2]=l<<r}}else{p=b+16|0;q=b+20|0;j=0;l=c[p>>2]|0;m=c[q>>2]|0;do{o=j<<24>>24;j=d[(o>>1)+(r+(t*3|0))>>0]|0;j=(256-j+(l*j|0)|0)>>>8;if((m|0)<0){zb(s);m=c[q>>2]|0}k=c[s>>2]|0;l=j<<24;if(k>>>0<l>>>0)n=0;else{n=1;k=k-l|0;j=(c[p>>2]|0)-j|0}E=d[1664+j>>0]|0;l=j<<E;m=m-E|0;c[s>>2]=k<<E;c[q>>2]=m;c[p>>2]=l;j=a[31673+(n+o)>>0]|0}while(j<<24>>24>0);k=0-(j<<24>>24)|0}while(0);j=u+560+(t<<4)+(k<<2)|0;if(u|0)c[j>>2]=(c[j>>2]|0)+1;j=a[(h&255)+(11152+(k*13|0))>>0]|0;a:do if(!C){a[b+13544>>0]=1>>>(k>>>1&1);a[b+13545>>0]=1>>>(k&1);Za(b,e,f,g,j,1,1)}else switch(k|0){case 0:{Za(b,e,f,g,j,i,i);break a}case 1:{Za(b,e,f,g,j,i,A);if(!v)break a;Za(b,e,D,g,j,i,A);break a}case 2:{Za(b,e,f,g,j,A,i);if(!x)break a;Za(b,e,f,w,j,A,i);break a}case 3:{Ya(b,e,f,g,j,A);Ya(b,e,f,w,j,A);Ya(b,e,D,g,j,A);Ya(b,e,D,w,j,A);break a}default:break a}while(0);if((h&255)<=2)return;if(!(h<<24>>24==3|(k|0)!=3))return;E=j&255;kg((c[y>>2]|0)+g|0,a[11488+(E<<1)>>0]|0,B|0)|0;kg(z|0,a[11488+(E<<1)+1>>0]|0,B|0)|0;return}function Za(f,g,h,i,j,k,l){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0;da=L;L=L+16|0;V=da+8|0;x=da;U=da+12|0;ca=g+432|0;w=j&255;X=(j&255)>2;aa=1<<k+-1;ba=1<<l+-1;r=c[g+1040>>2]|0;s=r-i|0;s=(aa|0)<(s|0)?aa:s;q=c[g+1032>>2]|0;v=q-h|0;v=(ba|0)<(v|0)?ba:v;W=f+13280|0;o=c[g+1044>>2]|0;_=(o*h|0)+i|0;$=(c[g+1168>>2]|0)+(_<<2)|0;Q=f+13572|0;c[Q>>2]=$;c[$>>2]=(c[g+1140>>2]|0)+(_*68|0);a[c[c[Q>>2]>>2]>>0]=j;if((v|0)>0){m=0;do{j=(m|0)==0&1;if((s|0)>(j|0)){n=m*o|0;do{$=c[Q>>2]|0;c[$+(j+n<<2)>>2]=c[$>>2];j=j+1|0}while((j|0)!=(s|0))}m=m+1|0}while((m|0)!=(v|0))}$=aa<<1;Z=ba<<1;n=c[f+13284>>2]|0;K=f+13356|0;b[K>>1]=$>>n;o=c[f+13288>>2]|0;M=f+13358|0;b[M>>1]=Z>>o;a[f+13360>>0]=k-n;a[f+13361>>0]=l-o;J=c[f+13372>>2]|0;N=f+13444|0;b[N>>1]=$>>J;Y=c[f+13376>>2]|0;O=f+13446|0;b[O>>1]=Z>>Y;a[f+13448>>0]=k-J;a[f+13449>>0]=l-Y;p=c[f+13460>>2]|0;P=f+13532|0;b[P>>1]=$>>p;$=c[f+13464>>2]|0;R=f+13534|0;b[R>>1]=Z>>$;a[f+13536>>0]=k-p;a[f+13537>>0]=l-$;Z=i<<1;_=h<<1&14;m=f+13316|0;c[m>>2]=(c[f+13628>>2]|0)+(Z>>n);n=f+13320|0;c[n>>2]=(_>>>o)+(f+13640);o=f+13404|0;c[o>>2]=(c[f+13632>>2]|0)+(Z>>J);k=f+13408|0;c[k>>2]=(_>>>Y)+(f+13656);l=f+13492|0;c[l>>2]=(c[f+13636>>2]|0)+(Z>>p);p=f+13496|0;c[p>>2]=(_>>>$)+(f+13672);c[f+13604>>2]=0-(h<<6);$=f+13608|0;c[$>>2]=q-(ba+h)<<6;c[f+13596>>2]=0-(i<<6);_=f+13600|0;c[_>>2]=r-(aa+i)<<6;if(!h)j=0;else j=c[(c[Q>>2]|0)+(0-(c[f+13568>>2]|0)<<2)>>2]|0;c[f+13580>>2]=j;if((c[f+13560>>2]|0)<(i|0))j=c[(c[Q>>2]|0)+-4>>2]|0;else j=0;c[f+13576>>2]=j;J=g+17832|0;ad(W,(c[J>>2]|0)+12+((c[g+988>>2]|0)*152|0)+32|0,h,i);Z=c[c[Q>>2]>>2]|0;if((X?(t=c[g+712>>2]|0,u=c[g+716>>2]|0,t|u|0):0)?(a[11216+(w<<2)+(t<<1)+u>>0]|0)==13:0)ma(c[f+13708>>2]|0,7,31138,x);ed(f,g,h,i,s,v);Y=Z+3|0;if(a[Y>>0]|0){kg(c[m>>2]|0,0,e[K>>1]|0)|0;kg(c[n>>2]|0,0,e[M>>1]|0)|0;kg(c[o>>2]|0,0,e[N>>1]|0)|0;kg(c[k>>2]|0,0,e[O>>1]|0)|0;kg(c[l>>2]|0,0,e[P>>1]|0)|0;kg(c[p>>2]|0,0,e[R>>1]|0)|0}if((a[Z+8>>0]|0)>=1){H=i<<3;I=h<<3;v=c[c[Q>>2]>>2]|0;w=c[9408+(d[v+7>>0]<<2)>>2]|0;x=f+13708|0;y=(d[v>>0]|0)<3;z=(a[v+9>>0]|0)>0?2:1;A=f+13292|0;B=f+13380|0;C=f+13468|0;D=f+13368|0;E=f+13456|0;u=0;do{m=(a[v+8+u>>0]|0)+-1|0;k=g+796+(m<<6)|0;F=g+796+(m<<6)+8|0;G=(c[J>>2]|0)+12+((c[k>>2]|0)*152|0)|0;j=c[F>>2]|0;if((j|0)!=-1?(T=c[g+796+(m<<6)+12>>2]|0,(T|0)!=-1):0){n=T;o=58}else o=56;do if((o|0)==56){ma(c[x>>2]|0,5,31158,V);j=c[F>>2]|0;if((j|0)!=-1?(S=c[g+796+(m<<6)+12>>2]|0,(S|0)!=-1):0){n=S;o=58;break}j=c[g+796+(m<<6)+4>>2]|0;o=60}while(0);if((o|0)==58){o=0;m=c[g+796+(m<<6)+4>>2]|0;if((j|0)==16384&(n|0)==16384){j=m;o=60}else{j=m;m=F;t=1}}if((o|0)==60){m=0;t=0}bd(W,u,j,h,i,m);c[f+13616+(u<<2)>>2]=k;if(y){s=b[K>>1]|0;k=s&65535;Q=b[M>>1]|0;l=Q&65535;p=k<<2;q=l<<2;r=f+13300+(u<<3)|0;if(!(s<<16>>16==0|Q<<16>>16==0)){j=0;n=0;while(1){s=n<<2;m=0;o=j;while(1){$c(U,W,v,u,o);_a(W,0,p,q,m<<2,s,4,4,H,I,w,F,r,A,U,G,t,u);m=m+1|0;if((m|0)==(k|0))break;else o=o+1|0}n=n+1|0;if((n|0)==(l|0))break;else j=j+k|0}}s=b[N>>1]|0;k=s&65535;Q=b[O>>1]|0;l=Q&65535;p=k<<2;q=l<<2;r=f+13388+(u<<3)|0;if(!(s<<16>>16==0|Q<<16>>16==0)){j=0;n=0;while(1){s=n<<2;m=0;o=j;while(1){$c(U,D,v,u,o);_a(W,1,p,q,m<<2,s,4,4,H,I,w,F,r,B,U,G,t,u);m=m+1|0;if((m|0)==(k|0))break;else o=o+1|0}n=n+1|0;if((n|0)==(l|0))break;else j=j+k|0}}s=b[P>>1]|0;k=s&65535;Q=b[R>>1]|0;l=Q&65535;p=k<<2;q=l<<2;r=f+13476+(u<<3)|0;if(!(s<<16>>16==0|Q<<16>>16==0)){j=0;n=0;while(1){s=n<<2;m=0;o=j;while(1){$c(U,E,v,u,o);_a(W,2,p,q,m<<2,s,4,4,H,I,w,F,r,C,U,G,t,u);m=m+1|0;if((m|0)==(k|0))break;else o=o+1|0}n=n+1|0;if((n|0)==(l|0))break;else j=j+k|0}}}else{s=v+12+(u<<2)|0;c[U>>2]=e[s>>1]|e[s+2>>1]<<16;s=e[K>>1]<<2;Q=e[M>>1]<<2;_a(W,0,s,Q,0,0,s,Q,H,I,w,F,f+13300+(u<<3)|0,A,U,G,t,u);Q=e[N>>1]<<2;s=e[O>>1]<<2;_a(W,1,Q,s,0,0,Q,s,H,I,w,F,f+13388+(u<<3)|0,B,U,G,t,u);s=e[P>>1]<<2;Q=e[R>>1]<<2;_a(W,2,s,Q,0,0,s,Q,H,I,w,F,f+13476+(u<<3)|0,C,U,G,t,u)}u=u+1|0}while((u|0)!=(z|0));if(!(a[Y>>0]|0)){G=Z+2|0;H=f+13584|0;I=f+13588|0;F=Z+4|0;J=f+13700|0;j=0;E=0;do{if(!E)m=G;else m=(c[f+13280+(E*88|0)+8>>2]|0)+(11280+(d[Z>>0]<<4)+(d[G>>0]<<2)+(c[f+13280+(E*88|0)+4>>2]<<1))|0;C=a[m>>0]|0;k=C&255;D=1<<k;m=c[_>>2]|0;o=(m|0)>-1;if(o)m=0;else m=m>>(c[f+13280+(E*88|0)+4>>2]|0)+5;B=m+(e[f+13280+(E*88|0)+76>>1]|0)|0;m=c[$>>2]|0;n=(m|0)>-1;if(n)m=0;else m=m>>(c[f+13280+(E*88|0)+8>>2]|0)+5;A=m+(e[f+13280+(E*88|0)+78>>1]|0)|0;c[H>>2]=o?0:B;c[I>>2]=n?0:A;if((A|0)>0){s=(B|0)>0;t=11520+(k*12|0)|0;u=f+13280+(E*88|0)+12|0;v=f+13280+(E*88|0)+16|0;w=f+13280+(E*88|0)|0;x=(C&255)<3;y=32<<k;z=C<<24>>24==3;q=32<<(k<<1);r=C<<24>>24==0;p=0;do{a:do if(s){if(r){l=0;while(1){m=ab(f,E,t,l,p,0,a[F>>0]|0)|0;do if((m|0)>0){n=c[v>>2]|0;o=(c[u>>2]|0)+((n*p|0)+l<<2)|0;k=c[w>>2]|0;if(!(c[J>>2]|0))jc(k,o,n,m);else kc(k,o,n,m);if((m|0)==1){b[k>>1]=0;break}if(x&(m|0)<11){kg(k|0,0,y|0)|0;break}if(z&(m|0)<35){kg(k|0,0,512)|0;break}else{kg(k|0,0,q|0)|0;break}}while(0);j=m+j|0;l=l+D|0;if((l|0)>=(B|0))break a}}l=0;do{m=ab(f,E,t,l,p,C,a[F>>0]|0)|0;b:do if((m|0)>0){n=c[v>>2]|0;o=(c[u>>2]|0)+((n*p|0)+l<<2)|0;k=c[w>>2]|0;c:do if(!(c[J>>2]|0))switch(C<<24>>24){case 3:{nc(k,o,n,m);break c}case 1:{lc(k,o,n,m);break c}case 2:{mc(k,o,n,m);break c}default:break b}else kc(k,o,n,m);while(0);if((m|0)==1){b[k>>1]=0;break}if(x&(m|0)<11){kg(k|0,0,y|0)|0;break}if(z&(m|0)<35){kg(k|0,0,512)|0;break}else{kg(k|0,0,q|0)|0;break}}while(0);j=m+j|0;l=l+D|0}while((l|0)<(B|0))}while(0);p=p+D|0}while((p|0)<(A|0))}E=E+1|0}while((E|0)!=3);if(X&(j|0)==0)a[Y>>0]=1}}else{H=Z+2|0;I=f+13584|0;J=f+13588|0;K=Z+1|0;M=Z+6|0;N=f+13700|0;O=Z+4|0;G=0;do{P=(G|0)==0;if(P)j=H;else j=(c[f+13280+(G*88|0)+8>>2]|0)+(11280+(d[Z>>0]<<4)+(d[H>>0]<<2)+(c[f+13280+(G*88|0)+4>>2]<<1))|0;D=a[j>>0]|0;E=D&255;F=1<<E;j=c[_>>2]|0;n=(j|0)>-1;if(n)j=0;else j=j>>(c[f+13280+(G*88|0)+4>>2]|0)+5;C=j+(e[f+13280+(G*88|0)+76>>1]|0)|0;j=c[$>>2]|0;m=(j|0)>-1;if(m)j=0;else j=j>>(c[f+13280+(G*88|0)+8>>2]|0)+5;B=j+(e[f+13280+(G*88|0)+78>>1]|0)|0;c[I>>2]=n?0:C;c[J>>2]=m?0:B;if((B|0)>0){p=(C|0)>0;q=f+13280+(G*88|0)+12|0;r=f+13280+(G*88|0)+16|0;s=f+13280+(G*88|0)+80|0;t=11520+(E*12|0)|0;u=f+13280+(G*88|0)|0;v=(D&255)<3;w=D<<24>>24==3;x=32<<E;y=32<<(E<<1);k=0;do{if(p){z=k<<1;l=0;do{m=c[r>>2]|0;A=(c[q>>2]|0)+((m*k|0)+l<<2)|0;if(P&(d[Z>>0]|0)<3)j=(c[c[Q>>2]>>2]|0)+20+((l+z|0)*12|0)|0;else j=P?K:M;j=a[j>>0]|0;cd(W,d[s>>0]|0,D,j,A,m,A,m,l,k,G);d:do if(!(a[Y>>0]|0)){do if(P){if(c[N>>2]|0){j=t;o=0;break}o=c[11104+((j&255)<<2)>>2]|0;j=22512+(E*48|0)+(o*12|0)|0}else{j=t;o=0}while(0);j=ab(f,G,j,l,k,D,a[O>>0]|0)|0;if((j|0)<=0)break;m=c[r>>2]|0;n=c[u>>2]|0;e:do if(!(c[N>>2]|0))switch(D<<24>>24){case 0:{oc(o,n,A,m,j);break e}case 1:{pc(o,n,A,m,j);break e}case 2:{qc(o,n,A,m,j);break e}case 3:{nc(n,A,m,j);break e}default:break d}else kc(n,A,m,j);while(0);if((j|0)==1){b[n>>1]=0;break}if((o|0)==0&(v&(j|0)<11)){kg(n|0,0,x|0)|0;break}if(w&(j|0)<35){kg(n|0,0,512)|0;break}else{kg(n|0,0,y|0)|0;break}}while(0);l=l+F|0}while((l|0)<(C|0))}k=k+F|0}while((k|0)<(B|0))}G=G+1|0}while((G|0)!=3)}$=f+13704|0;c[$>>2]=c[$>>2]|((c[f+20>>2]|0)+-33|0)>>>0<1073741791;if(!(c[g+4372>>2]|0)){L=da;return}Pc(ca,Z,h,i,aa,ba);L=da;return}function _a(d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u){d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;t=t|0;u=u|0;var v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0;H=L;L=L+25616|0;G=H;v=H+25600|0;y=q+4|0;F=(c[q>>2]|0)+((c[y>>2]|0)*i|0)+h|0;x=(e|0)==0;z=c[(x?s+40|0:s+60|0)>>2]|0;E=c[(x?s+44|0:s+64|0)>>2]|0;x=c[(x?s+84|0:(e|0)==1?s+88|0:s+92|0)>>2]|0;w=(t|0)!=0;if(w){q=d+(e*88|0)+4|0;s=d+(e*88|0)+8|0;_c(G,d,r,f,g,c[q>>2]|0,c[s>>2]|0);q=(0-(c[d+316>>2]|0)>>(c[q>>2]|0)+3)+h|0;s=(0-(c[d+324>>2]|0)>>(c[s>>2]|0)+3)+i|0;D=o+16|0;g=Q[c[D>>2]&31](q<<4,o)|0;C=o+20|0;d=Q[c[C>>2]&31](s<<4,o)|0;q=Q[c[D>>2]&31](q,o)|0;s=Q[c[C>>2]&31](s,o)|0;sc(v,G,l+h|0,m+i|0,o);C=c[o+8>>2]|0;D=c[o+12>>2]|0;e=c[v>>2]|0;f=c[v+4>>2]|0}else{f=c[d+(e*88|0)+4>>2]|0;g=(0-(c[d+316>>2]|0)>>f+3)+h|0;e=c[d+(e*88|0)+8>>2]|0;d=(0-(c[d+324>>2]|0)>>e+3)+i|0;C=16;D=16;q=g;s=d;g=g<<4;d=d<<4;e=b[r>>1]<<1-e;f=b[r+2>>1]<<1-f}B=f&15;A=e&15;h=(f>>4)+q|0;t=(e>>4)+s|0;p=c[p+4>>2]|0;i=x+(t*p|0)+h|0;do if(!(((E|z)&7|e|f|0)==0&(w^1))){s=(D*(k+-1|0)|0)+d+e>>4;d=s+1|0;q=(C*(j+-1|0)|0)+g+f>>4;if((B|0)==0?(c[o+8>>2]|0)==16:0){f=0;g=q+1|0}else{f=3;g=q+5|0;h=h+-3|0}if((A|0)==0?(c[o+12>>2]|0)==16:0){e=0;q=d}else{e=3;q=s+5|0;t=t+-3|0}d=(h|0)<0;if(((!d?!((h|0)>=(z|0)|(g|0)<0):0)?!((g|0)>=(z|0)|(t|0)<0):0)?(q|0)<(E|0)&((t|0)<(E|0)&(q|0)>-1):0)break;i=t*p|0;v=g-h+1|0;s=q-t+1|0;r=(e*v|0)+f|0;m=c[y>>2]|0;l=0-h|0;q=x+i+h+l+(0-i)|0;q=(t|0)<(E|0)?((t|0)>0?q+i|0:q):q+(p*(E+-1|0)|0)|0;l=d?l:0;l=(l|0)>(v|0)?v:l;i=g+1|0;i=(i|0)>(z|0)?i-z|0:0;i=(i|0)>(v|0)?v:i;g=v-l-i|0;d=(g|0)==0;e=(i|0)==0;f=z+-1|0;a:do if(!l){if(d){if(e)break;d=G;while(1){kg(d|0,a[q+f>>0]|0,i|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break a;else d=d+v|0}}if(e){d=G;while(1){ig(d|0,q+h|0,g|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break;else d=d+v|0}}else{d=G;while(1){ig(d|0,q+h|0,g|0)|0;kg(d+g|0,a[q+f>>0]|0,i|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break;else d=d+v|0}}}else if(d)if(e){d=G;while(1){kg(d|0,a[q>>0]|0,l|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break;else d=d+v|0}}else{d=G;while(1){kg(d|0,a[q>>0]|0,l|0)|0;kg(d+l|0,a[q+f>>0]|0,i|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break;else d=d+v|0}}else if(e){d=G;while(1){kg(d|0,a[q>>0]|0,l|0)|0;ig(d+l|0,q+h+l|0,g|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break;else d=d+v|0}}else{d=G;while(1){kg(d|0,a[q>>0]|0,l|0)|0;z=d+l|0;ig(z|0,q+h+l|0,g|0)|0;kg(z+g|0,a[q+f>>0]|0,i|0)|0;z=t;t=t+1|0;q=(z|0)>-1&(t|0)<(E|0)?q+p|0:q;s=s+-1|0;if(!s)break;else d=d+v|0}}while(0);Z[c[o+24+(((B|0)!=0&1)<<4)+(((A|0)!=0&1)<<3)+(u<<2)>>2]&15](G+r|0,v,F,m,n,B,C,A,D,j,k);L=H;return}while(0);Z[c[o+24+(((B|0)!=0&1)<<4)+(((A|0)!=0&1)<<3)+(u<<2)>>2]&15](i,p,F,c[y>>2]|0,n,B,C,A,D,j,k);L=H;return}function $a(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;if((e|0)<=0)return;s=f+4|0;t=f+8|0;r=0;do{h=(((c[s>>2]|0)*252|0)+4|0)>>>8;g=c[t>>2]|0;if((g|0)<0){zb(f);g=c[t>>2]|0}i=c[f>>2]|0;j=h<<24;if(i>>>0<j>>>0)k=1;else{k=0;i=i-j|0;h=(c[s>>2]|0)-h|0}q=d[1664+h>>0]|0;j=h<<q;h=i<<q;i=g-q|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=j;if(!k){g=((j<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;j=c[t>>2]|0}else j=i;i=g<<24;if(h>>>0<i>>>0)q=0;else{q=128;h=h-i|0;g=(c[s>>2]|0)-g|0}i=d[1664+g>>0]|0;g=g<<i;h=h<<i;i=j-i|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=g;g=((g<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;i=c[t>>2]|0}j=g<<24;if(h>>>0<j>>>0)p=0;else{p=64;h=h-j|0;g=(c[s>>2]|0)-g|0}o=d[1664+g>>0]|0;g=g<<o;h=h<<o;i=i-o|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=g;g=((g<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;i=c[t>>2]|0}j=g<<24;if(h>>>0<j>>>0)o=0;else{o=32;h=h-j|0;g=(c[s>>2]|0)-g|0}n=d[1664+g>>0]|0;g=g<<n;h=h<<n;i=i-n|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=g;g=((g<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;i=c[t>>2]|0}j=g<<24;if(h>>>0<j>>>0)n=0;else{n=16;h=h-j|0;g=(c[s>>2]|0)-g|0}m=d[1664+g>>0]|0;g=g<<m;h=h<<m;i=i-m|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=g;g=((g<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;i=c[t>>2]|0}j=g<<24;if(h>>>0<j>>>0)m=0;else{m=8;h=h-j|0;g=(c[s>>2]|0)-g|0}l=d[1664+g>>0]|0;g=g<<l;h=h<<l;i=i-l|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=g;g=((g<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;i=c[t>>2]|0}j=g<<24;if(h>>>0<j>>>0)l=0;else{l=4;h=h-j|0;g=(c[s>>2]|0)-g|0}k=d[1664+g>>0]|0;g=g<<k;h=h<<k;i=i-k|0;c[f>>2]=h;c[t>>2]=i;c[s>>2]=g;g=((g<<7)+128|0)>>>8;if((i|0)<0){zb(f);h=c[f>>2]|0;i=c[t>>2]|0}j=g<<24;if(h>>>0<j>>>0)k=0;else{k=2;h=h-j|0;g=(c[s>>2]|0)-g|0}j=d[1664+g>>0]|0;c[f>>2]=h<<j;c[t>>2]=i-j;c[s>>2]=g<<j;a[b+r>>0]=k|(l|(m|(n|(o|(p|q)))))|1}r=r+1|0}while((r|0)!=(e|0));return}function ab(d,e,f,g,h,i,j){d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0;m=d+12|0;n=d+13280+(e*88|0)|0;l=d+13280+(e*88|0)+44+(j<<2)|0;j=(c[d+13280+(e*88|0)+36>>2]|0)+g|0;k=(c[d+13280+(e*88|0)+40>>2]|0)+h|0;switch(i<<24>>24){case 0:{n=bb(c[d+13548>>2]|0,a[(c[c[d+13572>>2]>>2]|0)+8>>0]|0,c[d+13612>>2]|0,(e|0)>0&1,c[n>>2]|0,0,l,((a[k>>0]|0)!=0&1)+((a[j>>0]|0)!=0&1)|0,c[f>>2]|0,c[f+8>>2]|0,m)|0;m=(n|0)>0&1;a[k>>0]=m;a[j>>0]=m;return n|0}case 1:{o=c[d+13584>>2]|0;g=g+2|0;i=c[d+13588>>2]|0;h=h+2|0;n=bb(c[d+13548>>2]|0,a[(c[c[d+13572>>2]>>2]|0)+8>>0]|0,c[d+13612>>2]|0,(e|0)>0&1,c[n>>2]|0,1,l,((b[k>>1]|0)!=0&1)+((b[j>>1]|0)!=0&1)|0,c[f>>2]|0,c[f+8>>2]|0,m)|0;m=(n|0)>0?257:0;b[j>>1]=m>>>((o|0)!=0&g>>>0>o>>>0?g-o<<3:0);b[k>>1]=m>>>((i|0)!=0&h>>>0>i>>>0?h-i<<3:0);return n|0}case 2:{p=c[d+13584>>2]|0;g=g+4|0;i=c[d+13588>>2]|0;h=h+4|0;o=bb(c[d+13548>>2]|0,a[(c[c[d+13572>>2]>>2]|0)+8>>0]|0,c[d+13612>>2]|0,(e|0)>0&1,c[n>>2]|0,2,l,((c[k>>2]|0)!=0&1)+((c[j>>2]|0)!=0&1)|0,c[f>>2]|0,c[f+8>>2]|0,m)|0;n=(o|0)>0?16843009:0;c[j>>2]=n>>>((p|0)!=0&g>>>0>p>>>0?g-p<<3:0);c[k>>2]=n>>>((i|0)!=0&h>>>0>i>>>0?h-i<<3:0);return o|0}case 3:{q=c[d+13584>>2]|0;g=g+8|0;o=c[d+13588>>2]|0;i=h+8|0;p=j;h=k;p=bb(c[d+13548>>2]|0,a[(c[c[d+13572>>2]>>2]|0)+8>>0]|0,c[d+13612>>2]|0,(e|0)>0&1,c[n>>2]|0,3,l,(((c[h>>2]|0)!=0|(c[h+4>>2]|0)!=0)&1)+(((c[p>>2]|0)!=0|(c[p+4>>2]|0)!=0)&1)|0,c[f>>2]|0,c[f+8>>2]|0,m)|0;m=(p|0)>0;n=m?16843009:0;m=m?16843009:0;f=cg(n|0,m|0,((q|0)!=0&g>>>0>q>>>0?g-q<<3:0)|0)|0;g=v()|0;h=j;c[h>>2]=f;c[h+4>>2]=g;m=cg(n|0,m|0,((o|0)!=0&i>>>0>o>>>0?i-o<<3:0)|0)|0;n=v()|0;o=k;c[o>>2]=m;c[o+4>>2]=n;return p|0}default:{q=0;return q|0}}return 0}function bb(e,f,g,h,i,j,k,l,m,n,o){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;var p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0;la=L;L=L+1024|0;fa=la;ia=j&255;ea=16<<(ia<<1);ga=f<<24>>24>0&1;z=j<<24>>24==3&1;f=c[o>>2]|0;ja=o+4|0;p=c[ja>>2]|0;ka=o+8|0;q=c[ka>>2]|0;ha=(e|0)!=0;if((ea|0)<=0){ia=0;ha=q;h=p;e=f;c[o>>2]=e;c[ja>>2]=h;c[ka>>2]=ha;L=la;return ia|0}A=k+2|0;B=d[1920]|0;C=256-B|0;D=d[1936]|0;E=256-D|0;F=d[1937]|0;G=256-F|0;H=d[1952]|0;I=256-H|0;J=d[1968]|0;K=256-J|0;M=d[1984]|0;N=256-M|0;O=d[1953]|0;P=256-O|0;Q=d[1969]|0;R=256-Q|0;S=d[1985]|0;T=256-S|0;U=d[1954]|0;V=256-U|0;W=d[1970]|0;X=256-W|0;Y=d[1986]|0;Z=256-Y|0;_=d[1971]|0;$=256-_|0;aa=d[1987]|0;ba=256-aa|0;ca=d[1988]|0;da=256-ca|0;v=l;w=0;t=j<<24>>24==0?3040:2016;a:while(1){k=b[k>>1]|0;s=a[t>>0]|0;r=s&255;u=g+174+(ia*432|0)+(h*216|0)+(ga*108|0)+(r*18|0)+(v*3|0)|0;l=e+10032+(ia*576|0)+(h*288|0)+(ga*144|0)+(r*24|0)+(v<<2)|0;if(ha)c[l>>2]=(c[l>>2]|0)+1;l=d[u>>0]|0;l=(256-l+(p*l|0)|0)>>>8;j=l<<24;if((q|0)<0){c[o>>2]=f;c[ka>>2]=q;zb(o);q=c[ka>>2]|0;f=c[o>>2]|0}if(f>>>0<j>>>0){k=8;break}p=p-l|0;y=d[1664+p>>0]|0;p=p<<y;f=f-j<<y;q=q-y|0;b:do if(ha){j=t;l=w;while(1){t=j+1|0;s=d[u+1>>0]|0;s=(256-s+(p*s|0)|0)>>>8;j=s<<24;if((q|0)<0){c[o>>2]=f;c[ka>>2]=q;zb(o);q=c[ka>>2]|0;f=c[o>>2]|0}if(f>>>0>=j>>>0){w=r;x=k;y=t;k=s;break b}k=d[1664+s>>0]|0;p=s<<k;f=f<<k;q=q-k|0;k=e+816+(ia*2304|0)+(h*1152|0)+(ga*576|0)+(r*96|0)+(v<<4)|0;c[k>>2]=(c[k>>2]|0)+1;k=b[A>>1]|0;a[fa+(b[m+(l<<1)>>1]|0)>>0]=0;l=l+1|0;if((l|0)>=(ea|0)){k=104;break a}v=l<<1;v=((d[fa+(b[n+(v<<1)>>1]|0)>>0]|0)+1+(d[fa+(b[n+((v|1)<<1)>>1]|0)>>0]|0)|0)>>>1;u=d[t>>0]|0;r=u;j=t;u=g+174+(ia*432|0)+(h*216|0)+(ga*108|0)+(u*18|0)+(v*3|0)|0}}else{j=t;l=w;while(1){t=j+1|0;s=d[u+1>>0]|0;s=(256-s+(p*s|0)|0)>>>8;j=s<<24;if((q|0)<0){c[o>>2]=f;c[ka>>2]=q;zb(o);q=c[ka>>2]|0;f=c[o>>2]|0}if(f>>>0>=j>>>0){w=r;x=k;y=t;k=s;break b}k=d[1664+s>>0]|0;p=s<<k;f=f<<k;q=q-k|0;k=b[A>>1]|0;a[fa+(b[m+(l<<1)>>1]|0)>>0]=0;l=l+1|0;if((l|0)>=(ea|0)){k=104;break a}v=l<<1;v=((d[fa+(b[n+(v<<1)>>1]|0)>>0]|0)+1+(d[fa+(b[n+((v|1)<<1)>>1]|0)>>0]|0)|0)>>>1;u=d[t>>0]|0;r=u;j=t;u=g+174+(ia*432|0)+(h*216|0)+(ga*108|0)+(u*18|0)+(v*3|0)|0}}while(0);s=p-k|0;t=d[1664+s>>0]|0;s=s<<t;p=f-j<<t;f=q-t|0;j=u+2|0;t=d[j>>0]|0;t=(256-t+(s*t|0)|0)>>>8;q=t<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;k=c[o>>2]|0}else{r=f;k=p}do if(k>>>0<q>>>0){p=d[1664+t>>0]|0;f=e+816+(ia*2304|0)+(h*1152|0)+(ga*576|0)+(w*96|0)+(v<<4)+4|0;if(ha)c[f>>2]=(c[f>>2]|0)+1;a[fa+(b[m+(l<<1)>>1]|0)>>0]=1;u=k<<p;s=r-p|0;q=t<<p;f=x<<16>>16}else{s=s-t|0;f=d[1664+s>>0]|0;s=s<<f;p=k-q<<f;k=r-f|0;r=(d[j>>0]|0)+-1|0;f=e+816+(ia*2304|0)+(h*1152|0)+(ga*576|0)+(w*96|0)+(v<<4)+8|0;if(ha)c[f>>2]=(c[f>>2]|0)+1;f=d[3056+(r<<3)>>0]|0;f=(256-f+(s*f|0)|0)>>>8;q=f<<24;if((k|0)<0){c[o>>2]=p;c[ka>>2]=k;zb(o);k=c[ka>>2]|0;p=c[o>>2]|0}if(p>>>0<q>>>0){q=d[1664+f>>0]|0;j=f<<q;p=p<<q;f=k-q|0;k=d[3056+(r<<3)+1>>0]|0;k=(256-k+(j*k|0)|0)>>>8;q=k<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;p=c[o>>2]|0}if(p>>>0<q>>>0){q=d[1664+k>>0]|0;a[fa+(b[m+(l<<1)>>1]|0)>>0]=2;u=p<<q;s=f-q|0;q=k<<q;f=x<<16>>16<<1;break}j=j-k|0;k=d[1664+j>>0]|0;j=j<<k;p=p-q<<k;f=f-k|0;a[fa+(b[m+(l<<1)>>1]|0)>>0]=3;k=d[3056+(r<<3)+2>>0]|0;k=(256-k+(j*k|0)|0)>>>8;q=k<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=p}if(f>>>0<q>>>0){p=d[1664+k>>0]|0;j=3;f=f<<p;q=k<<p}else{w=j-k|0;p=d[1664+w>>0]|0;j=4;f=f-q<<p;q=w<<p}u=f;s=r-p|0;f=j*(x<<16>>16)|0;break}j=s-f|0;f=d[1664+j>>0]|0;j=j<<f;p=p-q<<f;f=k-f|0;q=d[3056+(r<<3)+3>>0]|0;q=(256-q+(j*q|0)|0)>>>8;k=q<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;p=c[o>>2]|0}do if(p>>>0<k>>>0){k=d[1664+q>>0]|0;j=q<<k;q=p<<k;f=f-k|0;a[fa+(b[m+(l<<1)>>1]|0)>>0]=4;p=d[3056+(r<<3)+4>>0]|0;p=(256-p+(j*p|0)|0)>>>8;k=p<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;q=c[o>>2]|0}if(q>>>0<k>>>0){j=d[1664+p>>0]|0;k=p<<j;p=q<<j;f=f-j|0;j=(C+(k*B|0)|0)>>>8;q=j<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;p=c[o>>2]|0}if(p>>>0<q>>>0){q=d[1664+j>>0]|0;k=5;p=p<<q;j=j<<q}else{j=k-j|0;w=d[1664+j>>0]|0;k=6;p=p-q<<w;j=j<<w;q=w}s=f-q|0;f=j;break}r=j-p|0;w=d[1664+r>>0]|0;r=r<<w;p=q-k<<w;f=f-w|0;k=(E+(r*D|0)|0)>>>8;q=k<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);j=c[ka>>2]|0;f=c[o>>2]|0}else{j=f;f=p}if(f>>>0<q>>>0){w=d[1664+k>>0]|0;s=0;q=f<<w;k=k<<w;f=w}else{k=r-k|0;w=d[1664+k>>0]|0;s=2;q=f-q<<w;k=k<<w;f=w}f=j-f|0;j=(G+(k*F|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){q=d[1664+j>>0]|0;k=0;p=f<<q;f=j<<q}else{w=k-j|0;q=d[1664+w>>0]|0;k=1;p=f-p<<q;f=w<<q}k=(k|s)+7|0;s=r-q|0}else{j=j-q|0;q=d[1664+j>>0]|0;j=j<<q;p=p-k<<q;f=f-q|0;a[fa+(b[m+(l<<1)>>1]|0)>>0]=5;q=d[3056+(r<<3)+5>>0]|0;q=(256-q+(j*q|0)|0)>>>8;k=q<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;p=c[o>>2]|0}if(p>>>0>=k>>>0){j=j-q|0;w=d[1664+j>>0]|0;j=j<<w;q=p-k<<w;f=f-w|0;p=d[3056+(r<<3)+7>>0]|0;p=(256-p+(j*p|0)|0)>>>8;k=p<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;q=c[o>>2]|0}if(q>>>0>=k>>>0){w=j-p|0;p=d[1664+w>>0]|0;r=0;t=0;s=f-p|0;f=w<<p;p=q-k<<p;do{q=d[2e3+r>>0]|0;q=(256-q+(f*q|0)|0)>>>8;j=q<<24;if((s|0)<0){c[o>>2]=p;c[ka>>2]=s;zb(o);s=c[ka>>2]|0;p=c[o>>2]|0}if(p>>>0<j>>>0){w=d[1664+q>>0]|0;k=0;f=q<<w;p=p<<w;q=w}else{f=f-q|0;q=d[1664+f>>0]|0;k=1;f=f<<q;p=p-j<<q}s=s-q|0;t=k|t<<1;r=r+1|0}while((r|0)!=14);k=t+67|0;break}j=d[1664+p>>0]|0;k=p<<j;p=q<<j;f=f-j|0;j=(N+(k*M|0)|0)>>>8;q=j<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=p}if(f>>>0<q>>>0){w=d[1664+j>>0]|0;v=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;v=16;q=f-q<<w;k=k<<w;f=w}f=r-f|0;j=(T+(k*S|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){w=d[1664+j>>0]|0;u=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;u=8;q=f-p<<w;k=k<<w;f=w}f=r-f|0;j=(Z+(k*Y|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){w=d[1664+j>>0]|0;t=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;t=4;q=f-p<<w;k=k<<w;f=w}f=r-f|0;j=(ba+(k*aa|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){w=d[1664+j>>0]|0;s=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;s=2;q=f-p<<w;k=k<<w;f=w}f=r-f|0;j=(da+(k*ca|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){q=d[1664+j>>0]|0;k=0;p=f<<q;f=j<<q}else{w=k-j|0;q=d[1664+w>>0]|0;k=1;p=f-p<<q;f=w<<q}k=(k|(s|(t|(u|v))))+35|0;s=r-q|0;break}k=d[1664+q>>0]|0;j=q<<k;q=p<<k;f=f-k|0;p=d[3056+(r<<3)+6>>0]|0;p=(256-p+(j*p|0)|0)>>>8;k=p<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);f=c[ka>>2]|0;q=c[o>>2]|0}if(q>>>0<k>>>0){j=d[1664+p>>0]|0;k=p<<j;p=q<<j;f=f-j|0;j=(I+(k*H|0)|0)>>>8;q=j<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=p}if(f>>>0<q>>>0){w=d[1664+j>>0]|0;t=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;t=4;q=f-q<<w;k=k<<w;f=w}f=r-f|0;j=(P+(k*O|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){w=d[1664+j>>0]|0;s=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;s=2;q=f-p<<w;k=k<<w;f=w}f=r-f|0;j=(V+(k*U|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){q=d[1664+j>>0]|0;k=0;p=f<<q;f=j<<q}else{w=k-j|0;q=d[1664+w>>0]|0;k=1;p=f-p<<q;f=w<<q}k=(k|(s|t))+11|0;s=r-q|0;break}r=j-p|0;w=d[1664+r>>0]|0;r=r<<w;p=q-k<<w;f=f-w|0;k=(K+(r*J|0)|0)>>>8;q=k<<24;if((f|0)<0){c[o>>2]=p;c[ka>>2]=f;zb(o);j=c[ka>>2]|0;f=c[o>>2]|0}else{j=f;f=p}if(f>>>0<q>>>0){w=d[1664+k>>0]|0;u=0;q=f<<w;k=k<<w;f=w}else{k=r-k|0;w=d[1664+k>>0]|0;u=8;q=f-q<<w;k=k<<w;f=w}f=j-f|0;j=(R+(k*Q|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){w=d[1664+j>>0]|0;t=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;t=4;q=f-p<<w;k=k<<w;f=w}f=r-f|0;j=(X+(k*W|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){w=d[1664+j>>0]|0;s=0;q=f<<w;k=j<<w;f=w}else{k=k-j|0;w=d[1664+k>>0]|0;s=2;q=f-p<<w;k=k<<w;f=w}f=r-f|0;j=($+(k*_|0)|0)>>>8;p=j<<24;if((f|0)<0){c[o>>2]=q;c[ka>>2]=f;zb(o);r=c[ka>>2]|0;f=c[o>>2]|0}else{r=f;f=q}if(f>>>0<p>>>0){q=d[1664+j>>0]|0;k=0;p=f<<q;f=j<<q}else{w=k-j|0;q=d[1664+w>>0]|0;k=1;p=f-p<<q;f=w<<q}k=(k|(s|(t|u)))+19|0;s=r-q|0}while(0);u=p;q=f;f=k*(x<<16>>16)|0}while(0);p=f>>z;r=((q<<7)+128|0)>>>8;j=r<<24;if((s|0)<0){c[o>>2]=u;c[ka>>2]=s;zb(o);s=c[ka>>2]|0;f=c[o>>2]|0}else f=u;if(f>>>0<j>>>0){q=d[1664+r>>0]|0;k=p;f=f<<q;p=r<<q}else{x=q-r|0;q=d[1664+x>>0]|0;k=0-p|0;f=f-j<<q;p=x<<q}b[i+(b[m+(l<<1)>>1]<<1)>>1]=k;q=s-q|0;l=l+1|0;k=l<<1;if((l|0)<(ea|0)){v=((d[fa+(b[n+(k<<1)>>1]|0)>>0]|0)+1+(d[fa+(b[n+((k|1)<<1)>>1]|0)>>0]|0)|0)>>>1;k=A;w=l;t=y}else{k=104;break}}if((k|0)==8){p=d[1664+l>>0]|0;k=l<<p;l=f<<p;p=q-p|0;f=e+816+(ia*2304|0)+(h*1152|0)+(ga*576|0)+((s&255)*96|0)+(v<<4)+12|0;if(!ha){ia=w;ha=p;h=k;e=l;c[o>>2]=e;c[ja>>2]=h;c[ka>>2]=ha;L=la;return ia|0}c[f>>2]=(c[f>>2]|0)+1;ia=w;ha=p;h=k;e=l;c[o>>2]=e;c[ja>>2]=h;c[ka>>2]=ha;L=la;return ia|0}else if((k|0)==104){c[o>>2]=f;c[ja>>2]=p;c[ka>>2]=q;L=la;return l|0}return 0}function cb(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,l=0,m=0,n=0,o=0;o=L;L=L+32|0;i=o+8|0;h=o;l=4;n=kf(40)|0;c[n>>2]=0;m=o+16|0;g=o+12|0;j=0;d=y(23,32,18720)|0;b=j;j=0;if((b|0)!=0&(k|0)!=0){e=gg(c[b>>2]|0,n|0,l|0)|0;if(!e)I(b|0,k|0);u(k|0)}else e=-1;b=v()|0;if((e|0)!=1){c[m>>2]=d;if(!(c[m>>2]|0))b=0;else b=(c[m>>2]|0)+432|0;c[g>>2]=b;if(!(c[g>>2]|0)){m=0;lf(n|0);L=o;return m|0}kg(c[m>>2]|0,0,18720)|0;n=fg((c[g>>2]|0)+92|0,1,n|0,l|0)|0;l=v()|0;j=0;b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1)f=g;else{f=g;b=0}}else f=0;while(1){d=(c[g>>2]|0)+88|0;if(b|0){c[d>>2]=0;j=0;B(6,c[m>>2]|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}else{b=9;break}}c[d>>2]=1;j=0;d=y(24,1,2044)|0;b=j;j=0;if((b|0)!=0&(k|0)!=0){e=gg(c[b>>2]|0,n|0,l|0)|0;if(!e)I(b|0,k|0);u(k|0)}else e=-1;b=v()|0;if((e|0)==1){e=f;f=e;continue}c[(c[g>>2]|0)+4104>>2]=d;if(!(c[(c[g>>2]|0)+4104>>2]|0)){j=0;C(2,c[g>>2]|0,2,31464,h|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}}j=0;d=y(24,4,2044)|0;b=j;j=0;if((b|0)!=0&(k|0)!=0){e=gg(c[b>>2]|0,n|0,l|0)|0;if(!e)I(b|0,k|0);u(k|0)}else e=-1;b=v()|0;if((e|0)==1){e=f;f=e;continue}c[(c[g>>2]|0)+4108>>2]=d;if(!(c[(c[g>>2]|0)+4108>>2]|0)){j=0;C(2,c[g>>2]|0,2,31490,i|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}}c[(c[m>>2]|0)+18712>>2]=1;if(!(c[8128]|0)){if(!(c[8129]|0)){j=0;A(1);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}j=0;A(2);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}j=0;A(3);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}j=0;A(4);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}c[8129]=1}c[8128]=1}b=(c[g>>2]|0)+300|0;c[b>>2]=-1;c[b+4>>2]=-1;c[b+8>>2]=-1;c[b+12>>2]=-1;c[b+16>>2]=-1;c[b+20>>2]=-1;c[b+24>>2]=-1;c[b+28>>2]=-1;b=(c[g>>2]|0)+332|0;c[b>>2]=-1;c[b+4>>2]=-1;c[b+8>>2]=-1;c[b+12>>2]=-1;c[b+16>>2]=-1;c[b+20>>2]=-1;c[b+24>>2]=-1;c[b+28>>2]=-1;c[(c[g>>2]|0)+17340>>2]=0;c[(c[m>>2]|0)+17856>>2]=1;c[(c[m>>2]|0)+17832>>2]=a;c[(c[g>>2]|0)+17348>>2]=8;c[(c[g>>2]|0)+17352>>2]=8;c[(c[g>>2]|0)+720>>2]=25;c[(c[g>>2]|0)+724>>2]=7;c[(c[g>>2]|0)+728>>2]=8;j=0;B(9,c[g>>2]|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){e=f;f=e;continue}c[(c[g>>2]|0)+88>>2]=0;j=0;d=w(1)|0;b=j;j=0;if((b|0)!=0&(k|0)!=0){e=gg(c[b>>2]|0,n|0,l|0)|0;if(!e)I(b|0,k|0);u(k|0)}else e=-1;b=v()|0;if((e|0)==1){e=f;f=e;continue}j=0;B(c[d>>2]|0,(c[m>>2]|0)+17868|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=gg(c[b>>2]|0,n|0,l|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)!=1){b=29;break}else{e=f;f=e}}if((b|0)==9){m=0;lf(n|0);L=o;return m|0}else if((b|0)==29){m=c[m>>2]|0;lf(n|0);L=o;return m|0}return 0}function db(a){a=a|0;var b=0,d=0,e=0,f=0;if(!a)return;V[c[(Mb()|0)+20>>2]&15](a+17868|0);qb(c[a+17880>>2]|0);e=a+18668|0;d=a+17892|0;if((c[e>>2]|0)>0){b=0;do{f=(c[d>>2]|0)+(b*24|0)|0;V[c[(Mb()|0)+20>>2]&15](f);b=b+1|0}while((b|0)<(c[e>>2]|0))}qb(c[a+17896>>2]|0);qb(c[d>>2]|0);if((c[e>>2]|0)>0)Vc(a+18676|0);Qb(a+432|0);qb(a);return}function eb(a,b){a=a|0;b=b|0;var d=0;d=pb(b,68)|0;c[a+704>>2]=d;if(!d){d=1;return d|0}c[a+700>>2]=b;d=pb(b,4)|0;c[a+732>>2]=d;d=(d|0)==0&1;return d|0}function fb(a){a=a|0;var b=0;b=a+704|0;qb(c[b>>2]|0);c[b>>2]=0;a=a+732|0;qb(c[a>>2]|0);c[a>>2]=0;return}function gb(a){a=a|0;var b=0,d=0;b=c[a+612>>2]|0;c[a+708>>2]=(c[a+704>>2]|0)+(b*68|0)+68;d=c[a+732>>2]|0;c[a+736>>2]=d+(b<<2)+4;kg(d|0,0,(b<<2)*((c[a+600>>2]|0)+1|0)|0)|0;return}function hb(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;i=L;L=L+32|0;e=i+16|0;h=i+8|0;f=i;g=a+432|0;do if((b|0)==1){e=c[a+732>>2]|0;if((e|0)<0){ma(g,1,31528,f);h=1;L=i;return h|0}f=c[a+17832>>2]|0;b=f+12+(e*152|0)+32|0;if((((c[f+12+(e*152|0)+36>>2]|0)==(c[d+4>>2]|0)?(c[b>>2]|0)==(c[d>>2]|0):0)?(c[f+12+(e*152|0)+56>>2]|0)==(c[d+24>>2]|0):0)?(c[f+12+(e*152|0)+52>>2]|0)==(c[d+20>>2]|0):0){ub(b,d);break}ma(g,1,31554,h)}else ma(g,1,31582,e);while(0);h=c[g>>2]|0;L=i;return h|0}function ib(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0;h=L;L=L+32|0;g=h+16|0;f=h+8|0;switch(b|0){case 1:{b=a+300|0;break}case 2:{b=a+304|0;break}case 4:{b=a+308|0;break}default:{ma(a,1,31582,h);g=c[a>>2]|0;L=h;return g|0}}e=c[b>>2]|0;if(e>>>0>11){ma(a,1,31606,f);g=c[a>>2]|0;L=h;return g|0}f=c[a+17400>>2]|0;b=f+12+(e*152|0)+32|0;if((((c[f+12+(e*152|0)+36>>2]|0)==(c[d+4>>2]|0)?(c[b>>2]|0)==(c[d>>2]|0):0)?(c[f+12+(e*152|0)+56>>2]|0)==(c[d+24>>2]|0):0)?(c[f+12+(e*152|0)+52>>2]|0)==(c[d+20>>2]|0):0){ub(d,b);g=c[a>>2]|0;L=h;return g|0}ma(a,1,31554,g);g=c[a>>2]|0;L=h;return g|0}function jb(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,z=0,A=0,D=0,E=0,F=0,G=0,H=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0;_=L;L=L+16|0;h=_;X=4;Y=kf(40)|0;c[Y>>2]=0;V=_+12|0;t=_+8|0;W=_+4|0;c[V>>2]=b+432;c[t>>2]=c[(c[V>>2]|0)+17400>>2];c[W>>2]=(c[(c[V>>2]|0)+17400>>2]|0)+12;z=c[e>>2]|0;c[c[V>>2]>>2]=0;if((d|0)==0?(c[(c[V>>2]|0)+364>>2]|0)>0:0)c[(c[(c[V>>2]|0)+368>>2]|0)+112>>2]=1;M=b+17856|0;c[M>>2]=0;if(((c[(c[V>>2]|0)+556>>2]|0)>-1?(c[(c[W>>2]|0)+((c[(c[V>>2]|0)+556>>2]|0)*152|0)>>2]|0)==0:0)?(a[(c[W>>2]|0)+((c[(c[V>>2]|0)+556>>2]|0)*152|0)+16>>0]|0)==0:0){j=0;y(c[(c[t>>2]|0)+8>>2]|0,c[c[t>>2]>>2]|0,(c[W>>2]|0)+((c[(c[V>>2]|0)+556>>2]|0)*152|0)+20|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1)h=0;else{a[(c[W>>2]|0)+((c[(c[V>>2]|0)+556>>2]|0)*152|0)+16>>0]=1;Z=9}}else Z=9;do if((Z|0)==9){f=c[(c[V>>2]|0)+17400>>2]|0;g=f+12|0;if(c[g>>2]|0){g=f+164|0;if(c[g>>2]|0){g=f+316|0;if(c[g>>2]|0){g=f+468|0;if(c[g>>2]|0){g=f+620|0;if(c[g>>2]|0){g=f+772|0;if(c[g>>2]|0){g=f+924|0;if(c[g>>2]|0){g=f+1076|0;if(c[g>>2]|0){g=f+1228|0;if(c[g>>2]|0){g=f+1380|0;if(c[g>>2]|0){g=f+1532|0;if(c[g>>2]|0){g=f+1684|0;if(!(c[g>>2]|0)){f=11;Z=11}else f=-1}else{f=10;Z=11}}else{f=9;Z=11}}else{f=8;Z=11}}else{f=7;Z=11}}else{f=6;Z=11}}else{f=5;Z=11}}else{f=4;Z=11}}else{f=3;Z=11}}else{f=2;Z=11}}else{f=1;Z=11}}else{f=0;Z=11}if((Z|0)==11)c[g>>2]=1;c[(c[V>>2]|0)+556>>2]=f;if((c[(c[V>>2]|0)+556>>2]|0)!=-1){c[(c[V>>2]|0)+296>>2]=(c[t>>2]|0)+12+((c[(c[V>>2]|0)+556>>2]|0)*152|0);h=b+18716|0;c[h>>2]=0;c[b+17864>>2]=(c[W>>2]|0)+((c[(c[V>>2]|0)+556>>2]|0)*152|0);Y=fg((c[V>>2]|0)+92|0,1,Y|0,X|0)|0;X=v()|0;j=0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1)break;f=0;break}j=0;C(2,c[V>>2]|0,2,31634,h|0);f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1)h=0;else{Z=c[c[V>>2]>>2]|0;lf(Y|0);L=_;return Z|0}}while(0);a:while(1){if(!f){c[(c[V>>2]|0)+88>>2]=1;j=0;C(3,b|0,z|0,z+d|0,e|0);f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1){r=h;h=r;continue}q=b+17832|0;r=c[q>>2]|0;f=c[b+17860>>2]|0;p=r+8|0;if(f){m=f;n=0;while(1){o=b+732+(n<<2)|0;i=c[o>>2]|0;if((i|0)>-1){l=r+12+(i*152|0)|0;f=c[l>>2]|0;if(((f|0)>0?(g=f+-1|0,c[l>>2]=g,P=r+12+(i*152|0)+16|0,(g|0)==0&(a[P>>0]|0)==0):0)?c[r+12+(i*152|0)+28>>2]|0:0){j=0;y(c[p>>2]|0,c[r>>2]|0,r+12+(i*152|0)+20|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1){r=h;h=r;continue a}a[P>>0]=1}if(((m&1|0?(Q=c[l>>2]|0,(Q|0)>0):0)?(g=Q+-1|0,c[l>>2]=g,R=r+12+(i*152|0)+16|0,(g|0)==0&(a[R>>0]|0)==0):0)?c[r+12+(i*152|0)+28>>2]|0:0){j=0;y(c[p>>2]|0,c[r>>2]|0,r+12+(i*152|0)+20|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1){r=h;h=r;continue a}a[R>>0]=1}}c[o>>2]=c[b+764+(n<<2)>>2];n=n+1|0;m=m>>1;if(!m)break}if(n>>>0<8){f=n;Z=85}}else{f=0;Z=85}b:do if((Z|0)==85){Z=0;m=b+1008|0;i=f;while(1){if(c[m>>2]|0)break b;l=b+732+(i<<2)|0;f=c[l>>2]|0;if((((f|0)>-1?(S=r+12+(f*152|0)|0,T=c[S>>2]|0,(T|0)>0):0)?(o=T+-1|0,c[S>>2]=o,U=r+12+(f*152|0)+16|0,(o|0)==0&(a[U>>0]|0)==0):0)?c[r+12+(f*152|0)+28>>2]|0:0){j=0;y(c[p>>2]|0,c[r>>2]|0,r+12+(f*152|0)+20|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1){r=h;h=r;continue a}a[U>>0]=1}c[l>>2]=c[b+764+(i<<2)>>2];i=i+1|0;if(i>>>0>=8)break}}while(0);c[h>>2]=0;p=c[b+988>>2]|0;c[b+720>>2]=(c[q>>2]|0)+12+(p*152|0)+32;r=r+12+(p*152|0)|0;c[r>>2]=(c[r>>2]|0)+-1;c[b+796>>2]=-1;c[b+860>>2]=-1;c[b+924>>2]=-1;if(c[(c[V>>2]|0)+576>>2]|0)break;c[(c[V>>2]|0)+572>>2]=c[(c[V>>2]|0)+568>>2];c[(c[V>>2]|0)+292>>2]=c[(c[V>>2]|0)+296>>2];if(!(a[(c[V>>2]|0)+3980>>0]|0))break;j=0;B(10,c[V>>2]|0);f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1){r=h;h=r;continue}else{Z=97;break}}j=0;g=w(1)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){i=gg(c[f>>2]|0,Y|0,X|0)|0;if(!i)I(f|0,k|0);u(k|0)}else i=-1;f=v()|0;if((i|0)==1){r=h;h=r;continue}c[(c[V>>2]|0)+88>>2]=0;c[M>>2]=1;n=g+8|0;j=0;x(c[n>>2]|0,b+17868|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)==1){r=h;h=r;continue}i=b+18668|0;if((c[i>>2]|0)>0){l=b+17892|0;g=0;while(1){j=0;x(c[n>>2]|0,(c[l>>2]|0)+(g*24|0)|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){m=gg(c[f>>2]|0,Y|0,X|0)|0;if(!m)I(f|0,k|0);u(k|0)}else m=-1;f=v()|0;if((m|0)==1){r=h;h=r;continue a}g=g+1|0;if((g|0)>=(c[i>>2]|0))break}}if((c[h>>2]|0)==1){f=c[b+17860>>2]|0;if(f){n=f;f=0;while(1){l=c[(c[V>>2]|0)+300+(f<<2)>>2]|0;g=c[W>>2]|0;i=c[t>>2]|0;m=(l|0)>-1;if(((m?(A=g+(l*152|0)|0,D=c[A>>2]|0,(D|0)>0):0)?(r=D+-1|0,c[A>>2]=r,E=g+(l*152|0)+16|0,(r|0)==0&(a[E>>0]|0)==0):0)?c[g+(l*152|0)+28>>2]|0:0){j=0;y(c[i+8>>2]|0,c[i>>2]|0,g+(l*152|0)+20|0)|0;g=j;j=0;if((g|0)!=0&(k|0)!=0){i=gg(c[g>>2]|0,Y|0,X|0)|0;if(!i)I(g|0,k|0);u(k|0)}else i=-1;g=v()|0;if((i|0)==1){r=h;f=g;h=r;continue a}a[E>>0]=1}if((((n&1|0?(s=c[W>>2]|0,F=c[t>>2]|0,m):0)?(G=s+(l*152|0)|0,H=c[G>>2]|0,(H|0)>0):0)?(r=H+-1|0,c[G>>2]=r,J=s+(l*152|0)+16|0,(r|0)==0&(a[J>>0]|0)==0):0)?c[s+(l*152|0)+28>>2]|0:0){j=0;y(c[F+8>>2]|0,c[F>>2]|0,s+(l*152|0)+20|0)|0;g=j;j=0;if((g|0)!=0&(k|0)!=0){i=gg(c[g>>2]|0,Y|0,X|0)|0;if(!i)I(g|0,k|0);u(k|0)}else i=-1;g=v()|0;if((i|0)==1){r=h;f=g;h=r;continue a}a[J>>0]=1}f=f+1|0;n=n>>1;if(!n)break}if(f>>>0<8)Z=52}else{f=0;Z=52}c:do if((Z|0)==52){Z=0;while(1){if(c[(c[V>>2]|0)+576>>2]|0)break c;i=c[(c[V>>2]|0)+300+(f<<2)>>2]|0;g=c[W>>2]|0;l=c[t>>2]|0;if((((i|0)>-1?(K=g+(i*152|0)|0,N=c[K>>2]|0,(N|0)>0):0)?(r=N+-1|0,c[K>>2]=r,O=g+(i*152|0)+16|0,(r|0)==0&(a[O>>0]|0)==0):0)?c[g+(i*152|0)+28>>2]|0:0){j=0;y(c[l+8>>2]|0,c[l>>2]|0,g+(i*152|0)+20|0)|0;g=j;j=0;if((g|0)!=0&(k|0)!=0){i=gg(c[g>>2]|0,Y|0,X|0)|0;if(!i)I(g|0,k|0);u(k|0)}else i=-1;g=v()|0;if((i|0)==1){r=h;f=g;h=r;continue a}a[O>>0]=1}f=f+1|0;if(f>>>0>=8)break}}while(0);c[h>>2]=0}l=c[(c[V>>2]|0)+556>>2]|0;i=c[W>>2]|0;m=c[t>>2]|0;if((l|0)<=-1){f=-1;Z=101;break}f=i+(l*152|0)|0;g=c[f>>2]|0;if((g|0)<=0){f=-1;Z=101;break}r=g+-1|0;c[f>>2]=r;n=i+(l*152|0)+16|0;if(!((r|0)==0&(a[n>>0]|0)==0)){f=-1;Z=101;break}if(!(c[i+(l*152|0)+28>>2]|0)){f=-1;Z=101;break}j=0;y(c[m+8>>2]|0,c[m>>2]|0,i+(l*152|0)+20|0)|0;f=j;j=0;if((f|0)!=0&(k|0)!=0){g=gg(c[f>>2]|0,Y|0,X|0)|0;if(!g)I(f|0,k|0);u(k|0)}else g=-1;f=v()|0;if((g|0)!=1){Z=67;break}else{r=h;h=r}}if((Z|0)==67){a[n>>0]=1;Z=-1;lf(Y|0);L=_;return Z|0}else if((Z|0)!=97)if((Z|0)==101){lf(Y|0);L=_;return f|0}c[(c[V>>2]|0)+272>>2]=c[(c[V>>2]|0)+256>>2];c[(c[V>>2]|0)+276>>2]=c[(c[V>>2]|0)+260>>2];if(c[(c[V>>2]|0)+568>>2]|0){Z=(c[V>>2]|0)+17340|0;c[Z>>2]=(c[Z>>2]|0)+1}c[(c[V>>2]|0)+88>>2]=0;Z=0;lf(Y|0);L=_;return Z|0}function kb(a,b,d){a=a|0;b=b|0;d=d|0;var e=0;d=a+17856|0;if((c[d>>2]|0)==1){a=-1;return a|0}c[d>>2]=1;if(!(c[a+1e3>>2]|0)){a=-1;return a|0}c[d>>2]=1;e=b;d=c[a+720>>2]|0;b=e+120|0;do{c[e>>2]=c[d>>2];e=e+4|0;d=d+4|0}while((e|0)<(b|0));a=0;return a|0}function lb(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;s=L;L=L+32|0;o=s;l=b+e|0;j=l+-1|0;p=(h|0)==0;if(p)m=a[j>>0]|0;else{X[h&63](i,j,o,1);m=a[o>>0]|0}c[g>>2]=0;j=m&255;if((j&224|0)!=192){g=0;L=s;return g|0}r=(j&7)+1|0;q=(j>>>3&3)+1|0;n=q*r|0;if((n+2|0)>>>0>e>>>0){g=7;L=s;return g|0}k=-2-n|0;j=l+k|0;if(p)j=a[j>>0]|0;else{X[h&63](i,j,o,1);j=a[o>>0]|0}if(m<<24>>24!=j<<24>>24){g=7;L=s;return g|0}j=b+(e+1+k)|0;if(!p){X[h&63](i,j,o,n);j=o}n=0;while(1){k=0;l=0;m=j;while(1){l=(d[m>>0]|0)<<(k<<3)|l;k=k+1|0;if((k|0)==(q|0))break;else m=m+1|0}c[f+(n<<2)>>2]=l;n=n+1|0;if((n|0)==(r|0))break;else j=j+q|0}c[g>>2]=r;g=0;L=s;return g|0}function mb(b,e){b=b|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;s=b+4|0;g=(((c[s>>2]|0)*252|0)+4|0)>>>8;t=b+8|0;f=c[t>>2]|0;if((f|0)<0){zb(b);f=c[t>>2]|0}h=c[b>>2]|0;i=g<<24;if(h>>>0<i>>>0)j=1;else{j=0;h=h-i|0;g=(c[s>>2]|0)-g|0}r=d[1664+g>>0]|0;i=g<<r;g=h<<r;h=f-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=i;if(j)return;f=((i<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;i=c[t>>2]|0}else i=h;h=f<<24;if(g>>>0<h>>>0)j=1;else{j=0;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;i=i-h|0;c[b>>2]=g;c[t>>2]=i;c[s>>2]=f;f=((f<<7)+128|0)>>>8;h=(i|0)<0;do if(j){if(h){zb(b);g=c[b>>2]|0;i=c[t>>2]|0}h=f<<24;if(g>>>0<h>>>0)m=0;else{m=8;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;h=i-h|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)l=0;else{l=4;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)k=0;else{k=2;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)j=0;else{j=1;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;c[b>>2]=g<<r;c[t>>2]=h-r;c[s>>2]=f<<r;f=l|m|k|j}else{if(h){zb(b);g=c[b>>2]|0;i=c[t>>2]|0}h=f<<24;if(g>>>0<h>>>0)j=1;else{j=0;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;i=i-h|0;c[b>>2]=g;c[t>>2]=i;c[s>>2]=f;f=((f<<7)+128|0)>>>8;h=(i|0)<0;if(j){if(h){zb(b);g=c[b>>2]|0;i=c[t>>2]|0}h=f<<24;if(g>>>0<h>>>0)m=16;else{m=24;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;h=i-h|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)l=0;else{l=4;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)k=0;else{k=2;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)j=0;else{j=1;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;c[b>>2]=g<<r;c[t>>2]=h-r;c[s>>2]=f<<r;f=m|l|k|j;break}if(h){zb(b);i=c[t>>2]|0;g=c[b>>2]|0}h=f<<24;if(g>>>0<h>>>0)j=1;else{j=0;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;i=i-h|0;c[b>>2]=g;c[t>>2]=i;c[s>>2]=f;f=((f<<7)+128|0)>>>8;h=(i|0)<0;if(j){if(h){zb(b);g=c[b>>2]|0;i=c[t>>2]|0}h=f<<24;if(g>>>0<h>>>0)n=32;else{n=48;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;h=i-h|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)m=0;else{m=8;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)l=0;else{l=4;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)k=0;else{k=2;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;f=f<<r;g=g<<r;h=h-r|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)j=0;else{j=1;g=g-i|0;f=(c[s>>2]|0)-f|0}r=d[1664+f>>0]|0;c[b>>2]=g<<r;c[t>>2]=h-r;c[s>>2]=f<<r;f=n|m|l|k|j;break}if(h){zb(b);g=c[b>>2]|0;i=c[t>>2]|0}h=f<<24;if(g>>>0<h>>>0)r=0;else{r=64;g=g-h|0;f=(c[s>>2]|0)-f|0}h=d[1664+f>>0]|0;f=f<<h;g=g<<h;h=i-h|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)q=0;else{q=32;g=g-i|0;f=(c[s>>2]|0)-f|0}p=d[1664+f>>0]|0;f=f<<p;g=g<<p;h=h-p|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)p=0;else{p=16;g=g-i|0;f=(c[s>>2]|0)-f|0}o=d[1664+f>>0]|0;f=f<<o;g=g<<o;h=h-o|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)o=0;else{o=8;g=g-i|0;f=(c[s>>2]|0)-f|0}n=d[1664+f>>0]|0;f=f<<n;g=g<<n;h=h-n|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)n=0;else{n=4;g=g-i|0;f=(c[s>>2]|0)-f|0}m=d[1664+f>>0]|0;f=f<<m;g=g<<m;h=h-m|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;h=c[t>>2]|0}i=f<<24;if(g>>>0<i>>>0)m=0;else{m=2;g=g-i|0;f=(c[s>>2]|0)-f|0}l=d[1664+f>>0]|0;f=f<<l;g=g<<l;h=h-l|0;c[b>>2]=g;c[t>>2]=h;c[s>>2]=f;f=((f<<7)+128|0)>>>8;if((h|0)<0){zb(b);g=c[b>>2]|0;j=c[t>>2]|0}else j=h;h=f<<24;if(g>>>0<h>>>0)l=0;else{l=1;g=g-h|0;f=(c[s>>2]|0)-f|0}k=d[1664+f>>0]|0;i=f<<k;h=g<<k;k=j-k|0;c[b>>2]=h;c[t>>2]=k;c[s>>2]=i;f=q|r|p|o|n|m|l;if((f|0)>=65){g=((i<<7)+128|0)>>>8;if((k|0)<0){zb(b);h=c[b>>2]|0;k=c[t>>2]|0}i=g<<24;if(h>>>0<i>>>0)j=0;else{j=1;h=h-i|0;g=(c[s>>2]|0)-g|0}r=d[1664+g>>0]|0;c[b>>2]=h<<r;c[t>>2]=k-r;c[s>>2]=g<<r;f=(f<<1)+-65+j|0}f=f+64|0}while(0);t=a[e>>0]|0;h=t&255;f=d[1408+f>>0]|0;g=h+-1|0;if((t&255)<129){do if((g<<1|0)>=(f|0))if(!(f&1)){f=(f>>>1)+g|0;break}else{f=g-((f+1|0)>>>1)|0;break}while(0);f=f+1|0}else{g=h^255;do if(g<<1>>>0>=f>>>0)if(!(f&1)){f=(f>>>1)+g|0;break}else{f=g-((f+1|0)>>>1)|0;break}while(0);f=255-f|0}a[e>>0]=f;return}function nb(a,b){a=a|0;b=b|0;var d=0;d=Zf(a|0,0,3,0)|0;b=Zf(d|0,v()|0,b|0,0)|0;d=v()|0;if(d>>>0>0|(d|0)==0&b>>>0>2147418112|((b|0)!=(b|0)|(d|0)!=0)){a=0;return a|0}d=kf(b)|0;b=a+-1+(d+4)&0-a;if(!d){a=0;return a|0}c[b+-4>>2]=d;a=b;return a|0}function ob(a){a=a|0;var b=0,d=0;b=Zf(a|0,0,11,0)|0;d=v()|0;if(d>>>0>0|(d|0)==0&b>>>0>2147418112|((b|0)!=(a+11|0)|(d|0)!=0)){d=0;return d|0}a=kf(b)|0;b=a+4+7&-8;if(!a){d=0;return d|0}c[b+-4>>2]=a;d=b;return d|0}function pb(a,b){a=a|0;b=b|0;var d=0,e=0;if(a|0?(e=(a|0)==0,(2147418112/(a>>>0)|0)>>>0<b>>>0?1:e?0:(((b*a|0)>>>0)/((e?1:a)>>>0)|0|0)!=(b|0)):0){e=0;return e|0}e=b*a|0;a=Zf(e|0,0,11,0)|0;d=v()|0;if(d>>>0>0|(d|0)==0&a>>>0>2147418112|((a|0)!=(e+11|0)|(d|0)!=0)){e=0;return e|0}a=kf(a)|0;b=a+4+7&-8;d=b;if(!a){e=0;return e|0}c[d+-4>>2]=a;if(!b){e=0;return e|0}kg(d|0,0,e|0)|0;e=d;return e|0}function qb(a){a=a|0;if(!a)return;lf(c[a+-4>>2]|0);return}function rb(a){a=a|0;var b=0;if(!a){b=-1;return b|0}if((c[a+72>>2]|0)>0)qb(c[a+68>>2]|0);b=a+120|0;do{c[a>>2]=0;a=a+4|0}while((a|0)<(b|0));b=0;return b|0}function sb(a,b,d,e,f,g,h,i,j,k){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0;if(!a){g=-2;return g|0}p=(h|0)==0?1:h;q=b+7&-8;r=d+7&-8;t=g<<1;s=q+31+t&-32;t=t+r|0;t=Yf(s|0,((s|0)<0)<<31>>31|0,t|0,((t|0)<0)<<31>>31|0)|0;n=((h|0)<0)<<31>>31;t=Zf(t|0,v()|0,h|0,n|0)|0;o=v()|0;u=q>>e;w=r>>f;x=s>>e;y=g>>e;z=g>>f;m=(z<<1)+w|0;m=Yf(x|0,((x|0)<0)<<31>>31|0,m|0,((m|0)<0)<<31>>31|0)|0;n=Zf(m|0,v()|0,h|0,n|0)|0;h=dg(n|0,v()|0,1)|0;o=Zf(h|0,v()|0,t|0,o|0)|0;h=v()|0;if(h>>>0>0|(h|0)==0&o>>>0>2147483647){g=-1;return g|0}do if(!j){l=a+72|0;if(h>>>0>0|((h|0)==0?o>>>0>(c[l>>2]|0)>>>0:0)){i=a+68|0;qb(c[i>>2]|0);c[i>>2]=0;h=nb(32,o)|0;c[i>>2]=h;if(!h){g=-1;return g|0}else{c[l>>2]=o;kg(h|0,0,o|0)|0;break}}}else{l=Zf(o|0,h|0,31,0)|0;m=v()|0;if(!((l|0)==(l|0)&(m|0)==0)){g=-1;return g|0}if((R[j&3](k,l,i)|0)<0){g=-1;return g|0}h=c[i>>2]|0;if(!h){g=-1;return g|0}if(m>>>0>0|((m|0)==0?l>>>0>(c[i+4>>2]|0)>>>0:0)){g=-1;return g|0}else{c[a+68>>2]=h+31&-32;break}}while(0);if(g&31|0){g=-3;return g|0}c[a+8>>2]=b;c[a+12>>2]=d;c[a>>2]=q;c[a+4>>2]=r;c[a+16>>2]=s;c[a+28>>2]=e+b>>e;c[a+32>>2]=f+d>>f;c[a+20>>2]=u;c[a+24>>2]=w;c[a+36>>2]=x;c[a+76>>2]=g;c[a+80>>2]=o;c[a+84>>2]=e;c[a+88>>2]=f;w=c[a+68>>2]|0;u=p+-1|0;f=0-p|0;c[a+52>>2]=u+(w+(s*g|0)+g)&f;w=w+t|0;g=x*z|0;c[a+56>>2]=u+(w+g+y)&f;c[a+60>>2]=u+(w+n+g+y)&f;c[a+112>>2]=0;g=0;return g|0}function tb(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;s=c[b+28>>2]|0;v=c[b+32>>2]|0;r=c[b+20>>2]|0;q=c[b>>2]|0;x=c[b+24>>2]|0;p=c[b+4>>2]|0;w=d>>((x|0)<(p|0)&1);t=d>>((r|0)<(q|0)&1);x=x-v+w|0;r=t+r|0;u=r-s|0;l=c[b+52>>2]|0;o=c[b+16>>2]|0;e=c[b+8>>2]|0;k=c[b+12>>2]|0;p=p+d-k|0;q=q+d|0;j=q-e|0;q=q+d|0;e=l+e|0;m=0-d|0;n=l+m|0;if((k|0)>0){f=n;g=e+-1|0;h=l;i=0;while(1){kg(f|0,a[h>>0]|0,d|0)|0;kg(e|0,a[g>>0]|0,j|0)|0;i=i+1|0;if((i|0)==(k|0))break;else{f=f+o|0;g=g+o|0;h=h+o|0;e=e+o|0}}}h=l+((k+-1|0)*o|0)+m|0;e=l+(k*o|0)+m|0;if((d|0)>0){f=l+(o*m|0)+m|0;g=0;while(1){ig(f|0,n|0,q|0)|0;g=g+1|0;if((g|0)==(d|0))break;else f=f+o|0}}if((p|0)>0){f=0;while(1){ig(e|0,h|0,q|0)|0;f=f+1|0;if((f|0)==(p|0))break;else e=e+o|0}}j=c[b+56>>2]|0;m=b+36|0;l=c[m>>2]|0;r=r+t|0;e=j+s|0;q=0-t|0;k=j+q|0;o=(v|0)>0;if(o){f=k;g=e+-1|0;h=j;i=0;while(1){kg(f|0,a[h>>0]|0,t|0)|0;kg(e|0,a[g>>0]|0,u|0)|0;i=i+1|0;if((i|0)==(v|0))break;else{f=f+l|0;g=g+l|0;h=h+l|0;e=e+l|0}}}d=v+-1|0;h=j+(l*d|0)+q|0;e=j+(l*v|0)+q|0;p=(w|0)>0;if(p){f=j+(l*(0-w|0)|0)+q|0;g=0;while(1){ig(f|0,k|0,r|0)|0;g=g+1|0;if((g|0)==(w|0))break;else f=f+l|0}}n=(x|0)>0;if(n){f=0;while(1){ig(e|0,h|0,r|0)|0;f=f+1|0;if((f|0)==(x|0))break;else e=e+l|0}}k=c[b+60>>2]|0;l=c[m>>2]|0;e=k+s|0;j=k+q|0;if(o){f=j;g=e+-1|0;h=k;i=0;while(1){kg(f|0,a[h>>0]|0,t|0)|0;kg(e|0,a[g>>0]|0,u|0)|0;i=i+1|0;if((i|0)==(v|0))break;else{f=f+l|0;g=g+l|0;h=h+l|0;e=e+l|0}}}h=k+(l*d|0)+q|0;e=k+(l*v|0)+q|0;if(p){f=k+(l*(0-w|0)|0)+q|0;g=0;while(1){ig(f|0,j|0,r|0)|0;g=g+1|0;if((g|0)==(w|0))break;else f=f+l|0}}if(!n)return;f=0;while(1){ig(e|0,h|0,r|0)|0;f=f+1|0;if((f|0)==(x|0))break;else e=e+l|0}return}function ub(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;h=a+4|0;if((c[h>>2]|0)>0){i=a+16|0;g=b+16|0;d=c[a+52>>2]|0;e=0;f=c[b+52>>2]|0;while(1){ig(f|0,d|0,c[a>>2]|0)|0;e=e+1|0;if((e|0)>=(c[h>>2]|0))break;else{d=d+(c[i>>2]|0)|0;f=f+(c[g>>2]|0)|0}}}k=a+24|0;if((c[k>>2]|0)<=0){a=b+76|0;a=c[a>>2]|0;tb(b,a);return}g=a+20|0;h=a+36|0;i=b+36|0;d=c[a+56>>2]|0;e=0;f=c[b+56>>2]|0;while(1){ig(f|0,d|0,c[g>>2]|0)|0;e=e+1|0;j=c[k>>2]|0;if((e|0)>=(j|0))break;else{d=d+(c[h>>2]|0)|0;f=f+(c[i>>2]|0)|0}}if((j|0)<=0){a=b+76|0;a=c[a>>2]|0;tb(b,a);return}g=a+20|0;h=a+36|0;i=b+36|0;d=c[a+60>>2]|0;e=0;f=c[b+60>>2]|0;while(1){ig(f|0,d|0,c[g>>2]|0)|0;e=e+1|0;if((e|0)>=(c[k>>2]|0))break;else{d=d+(c[h>>2]|0)|0;f=f+(c[i>>2]|0)|0}}a=b+76|0;a=c[a>>2]|0;tb(b,a);return}function vb(){if(c[8130]|0)return;c[8130]=1;return}function wb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;xb(0,a,b,c,d)|0;return}function xb(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0;i=a[d+b>>0]|0;h=i<<24>>24;if(i<<24>>24<1)i=c[f+(0-h<<2)>>2]|0;else i=xb(h,d,e,f,g)|0;j=a[d+(b+1)>>0]|0;h=j<<24>>24;if(j<<24>>24<1)h=c[f+(0-h<<2)>>2]|0;else h=xb(h,d,e,f,g)|0;f=b>>>1;d=a[e+f>>0]|0;h=h+i|0;if(!h){i=d;j=g+f|0;a[j>>0]=i;return h|0}j=c[9216+((h>>>0<20?h:20)<<2)>>2]|0;i=dg(i|0,0,8)|0;i=Zf(i|0,v()|0,h>>>1|0,0)|0;i=bg(i|0,v()|0,h|0,0)|0;v()|0;i=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255;j=g+f|0;a[j>>0]=i;return h|0}function yb(a,b,e,f,g){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;if(!((b|0)!=0|(e|0)==0)){r=1;return r|0}k=a+12|0;c[k>>2]=b+e;p=a+16|0;c[p>>2]=b;c[a>>2]=0;q=a+8|0;c[q>>2]=-8;r=a+4|0;c[r>>2]=255;m=a+20|0;c[m>>2]=f;n=a+24|0;c[n>>2]=g;h=e<<3;o=a+28|0;if(f){X[f&63](g,b,o,e>>>0<5?e:5);b=o}if(h>>>0<=32){j=32-h|0;l=(j|0)>-1;g=j>>31&-1073741824;j=l?j:0;if((h|0)==0&l|(j|0)>24){e=0;f=b;h=g|1073741816}else{h=24-j|0;i=g+h|0;h=(h>>>3)+1|0;e=0;f=24;g=b;while(1){e=(d[g>>0]|0)<<f|e;f=f+-8|0;if((f|0)<(j|0))break;else g=g+1|0}f=b+h|0;h=i+1073741824|0}}else{e=eg(d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24|0)|0;e=e|c[a>>2];f=b+4|0;h=24}j=(c[p>>2]|0)+(f-b)|0;c[p>>2]=j;c[a>>2]=e;c[q>>2]=h;b=((c[r>>2]<<7)+128|0)>>>8;if((h|0)<0){g=(c[k>>2]|0)-j|0;l=g<<3;i=16-h|0;f=c[m>>2]|0;if(f){X[f&63](c[n>>2]|0,j,o,g>>>0<5?g:5);j=o}f=i+8|0;if(l>>>0<=32){k=f-l|0;o=(k|0)>-1;f=o?h+1073741824|0:h;k=o?k:0;if((l|0)==0&o|(i|0)<(k|0))g=j;else{h=j;while(1){f=f+8|0;g=h+1|0;e=(d[h>>0]|0)<<i|e;i=i+-8|0;if((i|0)<(k|0))break;else h=g}}}else{o=f&-8;e=(eg(d[j>>0]|d[j+1>>0]<<8|d[j+2>>0]<<16|d[j+3>>0]<<24|0)|0)>>>(32-o|0);e=e<<(i&7)|c[a>>2];g=j+(f>>3)|0;f=o+h|0}c[p>>2]=(c[p>>2]|0)+(g-j);c[a>>2]=e;c[q>>2]=f;h=f}f=b<<24;if(e>>>0<f>>>0)g=0;else{g=1;e=e-f|0;b=(c[r>>2]|0)-b|0}p=d[1664+b>>0]|0;c[a>>2]=e<<p;c[q>>2]=h-p;c[r>>2]=b<<p;r=g;return r|0}function zb(a){a=a|0;var b=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=a+16|0;g=c[m>>2]|0;e=c[a>>2]|0;n=a+8|0;j=c[n>>2]|0;h=(c[a+12>>2]|0)-g|0;i=h<<3;k=16-j|0;b=c[a+20>>2]|0;f=a+28|0;if(!b)l=g;else{X[b&63](c[a+24>>2]|0,g,f,h>>>0<5?h:5);l=f}b=k+8|0;if(i>>>0<=32){g=b-i|0;h=(g|0)>-1;b=h?j+1073741824|0:j;j=h?g:0;if(h&(i|0)==0|(k|0)<(j|0))f=l;else{h=k;g=l;while(1){b=b+8|0;f=g+1|0;e=(d[g>>0]|0)<<h|e;h=h+-8|0;if((h|0)<(j|0))break;else g=f}}}else{i=b&-8;e=(eg(d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24|0)|0)>>>(32-i|0);e=e<<(k&7)|c[a>>2];f=l+(b>>3)|0;b=i+j|0}c[m>>2]=(c[m>>2]|0)+(f-l);c[a>>2]=e;c[n>>2]=b;return}function Ab(a){a=a|0;var b=0,d=0,e=0,f=0;e=a+8|0;b=c[e>>2]|0;d=a+16|0;a=c[d>>2]|0;if((b+-9|0)>>>0>=23){e=a;return e|0}do{f=b;b=b+-8|0;a=a+-1|0}while((f+-17|0)>>>0<23);c[e>>2]=b;c[d>>2]=a;f=a;return f|0}function Bb(a){a=a|0;return ((c[a+8>>2]|0)+7|0)>>>3|0}function Cb(a){a=a|0;var b=0,e=0,f=0;b=a+8|0;e=c[b>>2]|0;f=(c[a>>2]|0)+(e>>>3)|0;if(f>>>0<(c[a+4>>2]|0)>>>0){f=(d[f>>0]|0)>>>(e&7^7)&1;c[b>>2]=e+1;return f|0}else{V[c[a+16>>2]&15](c[a+12>>2]|0);f=0;return f|0}return 0}function Db(a,b){a=a|0;b=b|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if((b|0)<=0){l=0;return l|0}i=a+8|0;j=a+4|0;k=a+16|0;l=a+12|0;g=b;b=0;do{h=g;g=g+-1|0;f=c[i>>2]|0;e=(c[a>>2]|0)+(f>>>3)|0;if(e>>>0<(c[j>>2]|0)>>>0){e=(d[e>>0]|0)>>>(f&7^7)&1;c[i>>2]=f+1}else{V[c[k>>2]&15](c[l>>2]|0);e=0}b=e<<g|b}while((h|0)>1);return b|0}function Eb(a,b){a=a|0;b=b|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;k=a+8|0;l=a+4|0;if((b|0)>0){i=a+16|0;j=a+12|0;g=b;b=0;do{h=g;g=g+-1|0;f=c[k>>2]|0;e=(c[a>>2]|0)+(f>>>3)|0;if(e>>>0<(c[l>>2]|0)>>>0){e=(d[e>>0]|0)>>>(f&7^7)&1;c[k>>2]=f+1}else{V[c[i>>2]&15](c[j>>2]|0);e=0}b=e<<g|b}while((h|0)>1)}else b=0;e=c[k>>2]|0;f=(c[a>>2]|0)+(e>>>3)|0;if(f>>>0<(c[l>>2]|0)>>>0){l=d[f>>0]|0;c[k>>2]=e+1;if(1<<(e&7^7)&l|0){l=0-b|0;return l|0}}else V[c[a+16>>2]&15](c[a+12>>2]|0);l=b;return l|0}function Fb(){if(c[8131]|0)return;c[8131]=1;return}function Gb(a){a=a|0;c[a>>2]=0;c[a+4>>2]=0;c[a+8>>2]=0;c[a+12>>2]=0;c[a+16>>2]=0;c[a+20>>2]=0;return}function Hb(a){a=a|0;c[a+20>>2]=0;a=a+4|0;if(c[a>>2]|0)return 1;c[a>>2]=1;return 1}function Ib(a){a=a|0;return (c[a+20>>2]|0)==0|0}function Jb(a){a=a|0;var b=0,d=0;b=c[a+8>>2]|0;if(!b)return;d=(Q[b&31](c[a+12>>2]|0,c[a+16>>2]|0)|0)==0&1;b=a+20|0;c[b>>2]=c[b>>2]|d;return}function Kb(a){a=a|0;var b=0,d=0;b=c[a+8>>2]|0;if(!b)return;d=(Q[b&31](c[a+12>>2]|0,c[a+16>>2]|0)|0)==0&1;b=a+20|0;c[b>>2]=c[b>>2]|d;return}function Lb(a){a=a|0;c[a+4>>2]=0;return}function Mb(){return 29740}function Nb(a,b,d){a=a|0;b=b|0;d=d|0;var e=0;e=b+7>>3;c[a+608>>2]=e;b=d+7>>3;c[a+600>>2]=b;c[a+612>>2]=e+8;d=e+1>>1;c[a+604>>2]=d;b=b+1>>1;c[a+596>>2]=b;c[a+592>>2]=b*d;return}function Ob(b){b=b|0;var d=0,e=0,f=0,g=0;e=b+8|0;d=0;do{f=b+12+(d*152|0)+16|0;if((a[f>>0]|0)==0?(g=b+12+(d*152|0)+20|0,c[g>>2]|0):0){Q[c[e>>2]&31](c[b>>2]|0,g)|0;c[b+12+(d*152|0)>>2]=0;a[f>>0]=1}f=b+12+(d*152|0)+4|0;qb(c[f>>2]|0);c[f>>2]=0;rb(b+12+(d*152|0)+32|0)|0;d=d+1|0}while((d|0)!=12);return}function Pb(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;e=b+7>>3;n=a+608|0;c[n>>2]=e;f=d+7>>3;m=a+600|0;c[m>>2]=f;b=e+8|0;i=a+612|0;c[i>>2]=b;h=e+1>>1;j=a+604|0;c[j>>2]=h;d=f+1>>1;k=a+596|0;c[k>>2]=d;l=a+592|0;c[l>>2]=d*h;b=(f+8|0)*b|0;if((c[a+700>>2]|0)<(b|0)){V[c[a+724>>2]&15](a);if(!(Q[c[a+720>>2]&31](a,b)|0)){d=c[n>>2]|0;b=c[m>>2]|0;g=4}}else{d=e;b=f;g=4}do if((g|0)==4){h=a+776|0;if((c[h>>2]|0)<(d*b|0)){o=a+760|0;qb(c[o>>2]|0);c[o>>2]=0;b=a+764|0;qb(c[b>>2]|0);c[b>>2]=0;d=a+772|0;c[d>>2]=0;e=a+768|0;c[e>>2]=0;f=(c[n>>2]|0)*(c[m>>2]|0)|0;g=pb(f,1)|0;c[o>>2]=g;if(!g)break;g=pb(f,1)|0;c[b>>2]=g;if(!g)break;c[h>>2]=f;c[a+752>>2]=0;c[a+756>>2]=1;c[d>>2]=c[a+760>>2];c[e>>2]=g;d=c[n>>2]|0}b=a+17412|0;if((c[b>>2]|0)<(d|0)){h=a+17408|0;qb(c[h>>2]|0);o=pb(((c[n>>2]|0)+7&-8)*6|0,1)|0;c[h>>2]=o;if(!o)break;h=a+17404|0;qb(c[h>>2]|0);o=pb((c[n>>2]|0)+7&-8,1)|0;c[h>>2]=o;if(!o)break;c[b>>2]=c[n>>2]}h=a+3972|0;qb(c[h>>2]|0);o=(c[n>>2]|0)+7>>3;c[a+3976>>2]=o;o=pb(((c[m>>2]|0)+7>>3)*o|0,160)|0;c[h>>2]=o;if(o|0){o=0;return o|0}}while(0);c[n>>2]=0;c[m>>2]=0;c[i>>2]=8;c[j>>2]=0;c[k>>2]=0;c[l>>2]=0;V[c[a+724>>2]&15](a);o=a+760|0;qb(c[o>>2]|0);c[o>>2]=0;o=a+764|0;qb(c[o>>2]|0);c[o>>2]=0;c[a+772>>2]=0;c[a+768>>2]=0;o=a+17408|0;qb(c[o>>2]|0);c[o>>2]=0;o=a+17404|0;qb(c[o>>2]|0);c[o>>2]=0;o=a+3972|0;qb(c[o>>2]|0);c[o>>2]=0;o=1;return o|0}function Qb(a){a=a|0;var b=0;V[c[a+724>>2]&15](a);b=a+760|0;qb(c[b>>2]|0);c[b>>2]=0;b=a+764|0;qb(c[b>>2]|0);c[b>>2]=0;c[a+772>>2]=0;c[a+768>>2]=0;b=a+17408|0;qb(c[b>>2]|0);c[b>>2]=0;b=a+17404|0;qb(c[b>>2]|0);c[b>>2]=0;b=a+3972|0;qb(c[b>>2]|0);c[b>>2]=0;b=a+4104|0;qb(c[b>>2]|0);c[b>>2]=0;a=a+4108|0;qb(c[a>>2]|0);c[a>>2]=0;return}function Rb(a){a=a|0;var b=0;V[c[a+728>>2]&15](a);b=c[a+768>>2]|0;if(!b)return;kg(b|0,0,(c[a+608>>2]|0)*(c[a+600>>2]|0)|0)|0;return}function Sb(a){a=a|0;var b=0,d=0,e=0,f=0;f=a+752|0;b=c[f>>2]|0;e=a+756|0;d=c[e>>2]|0;c[f>>2]=d;c[e>>2]=b;c[a+772>>2]=c[a+760+(d<<2)>>2];c[a+768>>2]=c[a+760+(b<<2)>>2];return}function Tb(b,c,e){b=b|0;c=c|0;e=e|0;if((e|2|0)!=2){e=a[b+20+((e+-1|0)*12|0)>>0]|0;return e|0}if(!c){e=0;return e|0}if((a[c+8>>0]|0)>=1){e=0;return e|0}e=a[((d[c>>0]|0)<3?c+20+((e+1|0)*12|0)|0:c+1|0)>>0]|0;return e|0}function Ub(b,c,e){b=b|0;c=c|0;e=e|0;if(e>>>0>=2){e=a[b+20+((e+-2|0)*12|0)>>0]|0;return e|0}if(!c){e=0;return e|0}if((a[c+8>>0]|0)>=1){e=0;return e|0}e=a[((d[c>>0]|0)<3?c+20+((e+2|0)*12|0)|0:c+1|0)>>0]|0;return e|0}function Vb(a,b,d){a=a|0;b=b|0;d=d|0;c[a+4>>2]=0;c[a+8>>2]=0;c[a+92>>2]=b;c[a+96>>2]=d;c[a+180>>2]=b;c[a+184>>2]=d;return}function Wb(a){a=a|0;a=a+4104|0;ig((c[a>>2]|0)+174|0,5104,432)|0;ig((c[a>>2]|0)+606|0,5536,432)|0;ig((c[a>>2]|0)+1038|0,5968,432)|0;ig((c[a>>2]|0)+1470|0,6400,432)|0;return}function Xb(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;if((c[b+564>>2]|0)!=0?(a[b+580>>0]|0)==0:0)m=(c[b+560>>2]|0)==0?128:112;else m=112;o=b+4108|0;p=b+4112|0;q=b+4104|0;u=0;do{r=c[o>>2]|0;s=c[p>>2]|0;t=c[q>>2]|0;n=0;do{i=0;do{k=(i|0)==0?3:6;j=0;do{g=c[b+4932+(u*2304|0)+(n*1152|0)+(i*96|0)+(j<<4)>>2]|0;h=c[b+4932+(u*2304|0)+(n*1152|0)+(i*96|0)+(j<<4)+4>>2]|0;e=c[b+14148+(u*576|0)+(n*288|0)+(i*24|0)+(j<<2)>>2]|0;l=(c[b+4932+(u*2304|0)+(n*1152|0)+(i*96|0)+(j<<4)+8>>2]|0)+h|0;f=a[r+(s*2044|0)+174+(u*432|0)+(n*216|0)+(i*18|0)+(j*3|0)>>0]|0;if(!e)d=128;else{d=dg(c[b+4932+(u*2304|0)+(n*1152|0)+(i*96|0)+(j<<4)+12>>2]|0,0,8)|0;d=Zf(d|0,v()|0,e>>>1|0,0)|0;d=bg(d|0,v()|0,e|0,0)|0;v()|0;d=((255-d|0)>>>23|d)&255|(d|0)==0}e=(((e>>>0<24?e:24)*m|0)>>>0)/24|0;a[t+174+(u*432|0)+(n*216|0)+(i*18|0)+(j*3|0)>>0]=(((256-e|0)*(f&255)|0)+128+(d*e|0)|0)>>>8;e=a[r+(s*2044|0)+174+(u*432|0)+(n*216|0)+(i*18|0)+(j*3|0)+1>>0]|0;f=l+g|0;if(!f)d=128;else{d=dg(g|0,0,8)|0;d=Zf(d|0,v()|0,f>>>1|0,0)|0;d=bg(d|0,v()|0,f|0,0)|0;v()|0;d=((255-d|0)>>>23|d)&255|(d|0)==0}g=(((f>>>0<24?f:24)*m|0)>>>0)/24|0;a[t+174+(u*432|0)+(n*216|0)+(i*18|0)+(j*3|0)+1>>0]=(((256-g|0)*(e&255)|0)+128+(d*g|0)|0)>>>8;e=a[r+(s*2044|0)+174+(u*432|0)+(n*216|0)+(i*18|0)+(j*3|0)+2>>0]|0;if(!l)d=128;else{d=dg(h|0,0,8)|0;d=Zf(d|0,v()|0,l>>>1|0,0)|0;d=bg(d|0,v()|0,l|0,0)|0;v()|0;d=((255-d|0)>>>23|d)&255|(d|0)==0}l=(((l>>>0<24?l:24)*m|0)>>>0)/24|0;a[t+174+(u*432|0)+(n*216|0)+(i*18|0)+(j*3|0)+2>>0]=(((256-l|0)*(e&255)|0)+128+(d*l|0)|0)>>>8;j=j+1|0}while((j|0)!=(k|0));i=i+1|0}while((i|0)!=6);i=0;do{k=(i|0)==0?3:6;j=0;do{g=c[b+4932+(u*2304|0)+(n*1152|0)+576+(i*96|0)+(j<<4)>>2]|0;h=c[b+4932+(u*2304|0)+(n*1152|0)+576+(i*96|0)+(j<<4)+4>>2]|0;e=c[b+14148+(u*576|0)+(n*288|0)+144+(i*24|0)+(j<<2)>>2]|0;l=(c[b+4932+(u*2304|0)+(n*1152|0)+576+(i*96|0)+(j<<4)+8>>2]|0)+h|0;f=a[r+(s*2044|0)+174+(u*432|0)+(n*216|0)+108+(i*18|0)+(j*3|0)>>0]|0;if(!e)d=128;else{d=dg(c[b+4932+(u*2304|0)+(n*1152|0)+576+(i*96|0)+(j<<4)+12>>2]|0,0,8)|0;d=Zf(d|0,v()|0,e>>>1|0,0)|0;d=bg(d|0,v()|0,e|0,0)|0;v()|0;d=((255-d|0)>>>23|d)&255|(d|0)==0}e=(((e>>>0<24?e:24)*m|0)>>>0)/24|0;a[t+174+(u*432|0)+(n*216|0)+108+(i*18|0)+(j*3|0)>>0]=(((256-e|0)*(f&255)|0)+128+(d*e|0)|0)>>>8;e=a[r+(s*2044|0)+174+(u*432|0)+(n*216|0)+108+(i*18|0)+(j*3|0)+1>>0]|0;f=l+g|0;if(!f)d=128;else{d=dg(g|0,0,8)|0;d=Zf(d|0,v()|0,f>>>1|0,0)|0;d=bg(d|0,v()|0,f|0,0)|0;v()|0;d=((255-d|0)>>>23|d)&255|(d|0)==0}g=(((f>>>0<24?f:24)*m|0)>>>0)/24|0;a[t+174+(u*432|0)+(n*216|0)+108+(i*18|0)+(j*3|0)+1>>0]=(((256-g|0)*(e&255)|0)+128+(d*g|0)|0)>>>8;e=a[r+(s*2044|0)+174+(u*432|0)+(n*216|0)+108+(i*18|0)+(j*3|0)+2>>0]|0;if(!l)d=128;else{d=dg(h|0,0,8)|0;d=Zf(d|0,v()|0,l>>>1|0,0)|0;d=bg(d|0,v()|0,l|0,0)|0;v()|0;d=((255-d|0)>>>23|d)&255|(d|0)==0}l=(((l>>>0<24?l:24)*m|0)>>>0)/24|0;a[t+174+(u*432|0)+(n*216|0)+108+(i*18|0)+(j*3|0)+2>>0]=(((256-l|0)*(e&255)|0)+128+(d*l|0)|0)>>>8;j=j+1|0}while((j|0)!=(k|0));i=i+1|0}while((i|0)!=6);n=n+1|0}while((n|0)!=2);u=u+1|0}while((u|0)!=4);return}
function Yb(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=c[b+4104>>2]|0;k=c[b+4108>>2]|0;l=c[b+4112>>2]|0;d=a[k+(l*2044|0)+1931>>0]|0;e=c[b+16612>>2]|0;f=(c[b+16616>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1931>>0]=d;d=a[k+(l*2044|0)+1932>>0]|0;e=c[b+16620>>2]|0;f=(c[b+16624>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1932>>0]=d;d=a[k+(l*2044|0)+1933>>0]|0;e=c[b+16628>>2]|0;f=(c[b+16632>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1933>>0]=d;d=a[k+(l*2044|0)+1934>>0]|0;e=c[b+16636>>2]|0;f=(c[b+16640>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1934>>0]=d;d=a[k+(l*2044|0)+1935>>0]|0;e=c[b+16644>>2]|0;f=(c[b+16648>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1935>>0]=d;d=a[k+(l*2044|0)+1936>>0]|0;e=c[b+16652>>2]|0;f=(c[b+16656>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1936>>0]=d;d=a[k+(l*2044|0)+1937>>0]|0;e=c[b+16660>>2]|0;f=(c[b+16664>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1937>>0]=d;d=a[k+(l*2044|0)+1938>>0]|0;e=c[b+16668>>2]|0;f=(c[b+16672>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1938>>0]=d;d=a[k+(l*2044|0)+1939>>0]|0;e=c[b+16676>>2]|0;f=(c[b+16680>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1939>>0]=d;d=a[k+(l*2044|0)+1950>>0]|0;e=c[b+16764>>2]|0;f=(c[b+16768>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1950>>0]=d;d=a[k+(l*2044|0)+1951>>0]|0;e=c[b+16772>>2]|0;f=(c[b+16776>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1951>>0]=d;d=a[k+(l*2044|0)+1952>>0]|0;e=c[b+16780>>2]|0;f=(c[b+16784>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1952>>0]=d;d=a[k+(l*2044|0)+1953>>0]|0;e=c[b+16788>>2]|0;f=(c[b+16792>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1953>>0]=d;d=a[k+(l*2044|0)+1954>>0]|0;e=c[b+16796>>2]|0;f=(c[b+16800>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1954>>0]=d;d=a[k+(l*2044|0)+1940>>0]|0;e=c[b+16684>>2]|0;f=(c[b+16688>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1940>>0]=d;d=a[k+(l*2044|0)+1941>>0]|0;e=c[b+16692>>2]|0;f=(c[b+16696>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1941>>0]=d;d=a[k+(l*2044|0)+1942>>0]|0;e=c[b+16700>>2]|0;f=(c[b+16704>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1942>>0]=d;d=a[k+(l*2044|0)+1943>>0]|0;e=c[b+16708>>2]|0;f=(c[b+16712>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1943>>0]=d;d=a[k+(l*2044|0)+1944>>0]|0;e=c[b+16716>>2]|0;f=(c[b+16720>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1944>>0]=d;d=a[k+(l*2044|0)+1945>>0]|0;e=c[b+16724>>2]|0;f=(c[b+16728>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1945>>0]=d;d=a[k+(l*2044|0)+1946>>0]|0;e=c[b+16732>>2]|0;f=(c[b+16736>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1946>>0]=d;d=a[k+(l*2044|0)+1947>>0]|0;e=c[b+16740>>2]|0;f=(c[b+16744>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1947>>0]=d;d=a[k+(l*2044|0)+1948>>0]|0;e=c[b+16748>>2]|0;f=(c[b+16752>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1948>>0]=d;d=a[k+(l*2044|0)+1949>>0]|0;e=c[b+16756>>2]|0;f=(c[b+16760>>2]|0)+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1949>>0]=d;wb(31667,k+(l*2044|0)+1910|0,b+16500|0,m+1910|0);wb(31667,k+(l*2044|0)+1913|0,b+16516|0,m+1913|0);wb(31667,k+(l*2044|0)+1916|0,b+16532|0,m+1916|0);wb(31667,k+(l*2044|0)+1919|0,b+16548|0,m+1919|0);wb(31667,k+(l*2044|0)+1922|0,b+16564|0,m+1922|0);wb(31667,k+(l*2044|0)+1925|0,b+16580|0,m+1925|0);wb(31667,k+(l*2044|0)+1928|0,b+16596|0,m+1928|0);wb(7888,k+(l*2044|0)|0,b+4116|0,m);wb(7888,k+(l*2044|0)+9|0,b+4156|0,m+9|0);wb(7888,k+(l*2044|0)+18|0,b+4196|0,m+18|0);wb(7888,k+(l*2044|0)+27|0,b+4236|0,m+27|0);wb(7888,k+(l*2044|0)+36|0,b+4276|0,m+36|0);wb(7888,k+(l*2044|0)+45|0,b+4316|0,m+45|0);wb(7888,k+(l*2044|0)+54|0,b+4356|0,m+54|0);wb(7888,k+(l*2044|0)+63|0,b+4396|0,m+63|0);wb(7888,k+(l*2044|0)+72|0,b+4436|0,m+72|0);wb(7888,k+(l*2044|0)+81|0,b+4476|0,m+81|0);wb(7888,k+(l*2044|0)+90|0,b+4516|0,m+90|0);wb(7888,k+(l*2044|0)+99|0,b+4556|0,m+99|0);wb(7888,k+(l*2044|0)+108|0,b+4596|0,m+108|0);wb(7888,k+(l*2044|0)+117|0,b+4636|0,m+117|0);wb(31673,k+(l*2044|0)+126|0,b+4676|0,m+126|0);wb(31673,k+(l*2044|0)+129|0,b+4692|0,m+129|0);wb(31673,k+(l*2044|0)+132|0,b+4708|0,m+132|0);wb(31673,k+(l*2044|0)+135|0,b+4724|0,m+135|0);wb(31673,k+(l*2044|0)+138|0,b+4740|0,m+138|0);wb(31673,k+(l*2044|0)+141|0,b+4756|0,m+141|0);wb(31673,k+(l*2044|0)+144|0,b+4772|0,m+144|0);wb(31673,k+(l*2044|0)+147|0,b+4788|0,m+147|0);wb(31673,k+(l*2044|0)+150|0,b+4804|0,m+150|0);wb(31673,k+(l*2044|0)+153|0,b+4820|0,m+153|0);wb(31673,k+(l*2044|0)+156|0,b+4836|0,m+156|0);wb(31673,k+(l*2044|0)+159|0,b+4852|0,m+159|0);wb(31673,k+(l*2044|0)+162|0,b+4868|0,m+162|0);wb(31673,k+(l*2044|0)+165|0,b+4884|0,m+165|0);wb(31673,k+(l*2044|0)+168|0,b+4900|0,m+168|0);wb(31673,k+(l*2044|0)+171|0,b+4916|0,m+171|0);if((a[b+780>>0]|0)==4){wb(31679,k+(l*2044|0)+1902|0,b+16452|0,m+1902|0);wb(31679,k+(l*2044|0)+1904|0,b+16464|0,m+1904|0);wb(31679,k+(l*2044|0)+1906|0,b+16476|0,m+1906|0);wb(31679,k+(l*2044|0)+1908|0,b+16488|0,m+1908|0)}if((c[b+616>>2]|0)==4){e=c[b+16860>>2]|0;d=a[k+(l*2044|0)+1965>>0]|0;f=(c[b+16864>>2]|0)+e|0;if(f){h=dg(e|0,0,8)|0;i=v()|0;j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=Zf(h|0,i|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1965>>0]=d;e=c[b+16836>>2]|0;g=c[b+16840>>2]|0;h=(c[b+16844>>2]|0)+g|0;d=a[k+(l*2044|0)+1961>>0]|0;f=h+e|0;if(f){j=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;i=dg(e|0,0,8)|0;i=Zf(i|0,v()|0,f>>>1|0,0)|0;i=bg(i|0,v()|0,f|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1961>>0]=d;d=a[k+(l*2044|0)+1962>>0]|0;if(h){j=c[9216+((h>>>0<20?h:20)<<2)>>2]|0;i=dg(g|0,0,8)|0;i=Zf(i|0,v()|0,h>>>1|0,0)|0;i=bg(i|0,v()|0,h|0,0)|0;v()|0;d=(((256-j|0)*(d&255)|0)+128+((((255-i|0)>>>23|i)&255|(i|0)==0)*j|0)|0)>>>8&255}a[m+1962>>0]=d;f=c[b+16804>>2]|0;h=c[b+16808>>2]|0;i=c[b+16812>>2]|0;j=(c[b+16816>>2]|0)+i|0;g=j+h|0;d=a[k+(l*2044|0)+1955>>0]|0;e=g+f|0;if(e){n=c[9216+((e>>>0<20?e:20)<<2)>>2]|0;f=dg(f|0,0,8)|0;f=Zf(f|0,v()|0,e>>>1|0,0)|0;f=bg(f|0,v()|0,e|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*n|0)|0)>>>8&255}a[m+1955>>0]=d;d=a[k+(l*2044|0)+1956>>0]|0;if(g){n=c[9216+((g>>>0<20?g:20)<<2)>>2]|0;h=dg(h|0,0,8)|0;h=Zf(h|0,v()|0,g>>>1|0,0)|0;h=bg(h|0,v()|0,g|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-h|0)>>>23|h)&255|(h|0)==0)*n|0)|0)>>>8&255}a[m+1956>>0]=d;d=a[k+(l*2044|0)+1957>>0]|0;if(j){n=c[9216+((j>>>0<20?j:20)<<2)>>2]|0;i=dg(i|0,0,8)|0;i=Zf(i|0,v()|0,j>>>1|0,0)|0;j=bg(i|0,v()|0,j|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1957>>0]=d;e=c[b+16868>>2]|0;d=a[k+(l*2044|0)+1966>>0]|0;f=(c[b+16872>>2]|0)+e|0;if(f){i=dg(e|0,0,8)|0;j=v()|0;n=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;j=Zf(i|0,j|0,f>>>1|0,0)|0;j=bg(j|0,v()|0,f|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1966>>0]=d;e=c[b+16848>>2]|0;g=c[b+16852>>2]|0;h=(c[b+16856>>2]|0)+g|0;d=a[k+(l*2044|0)+1963>>0]|0;f=h+e|0;if(f){n=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;j=dg(e|0,0,8)|0;j=Zf(j|0,v()|0,f>>>1|0,0)|0;j=bg(j|0,v()|0,f|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1963>>0]=d;d=a[k+(l*2044|0)+1964>>0]|0;if(h){n=c[9216+((h>>>0<20?h:20)<<2)>>2]|0;j=dg(g|0,0,8)|0;j=Zf(j|0,v()|0,h>>>1|0,0)|0;j=bg(j|0,v()|0,h|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1964>>0]=d;e=c[b+16820>>2]|0;g=c[b+16824>>2]|0;i=c[b+16828>>2]|0;j=(c[b+16832>>2]|0)+i|0;h=j+g|0;d=a[k+(l*2044|0)+1958>>0]|0;f=h+e|0;if(f){n=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*n|0)|0)>>>8&255}a[m+1958>>0]=d;d=a[k+(l*2044|0)+1959>>0]|0;if(h){n=c[9216+((h>>>0<20?h:20)<<2)>>2]|0;g=dg(g|0,0,8)|0;g=Zf(g|0,v()|0,h>>>1|0,0)|0;h=bg(g|0,v()|0,h|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-h|0)>>>23|h)&255|(h|0)==0)*n|0)|0)>>>8&255}a[m+1959>>0]=d;d=a[k+(l*2044|0)+1960>>0]|0;if(j){n=c[9216+((j>>>0<20?j:20)<<2)>>2]|0;i=dg(i|0,0,8)|0;i=Zf(i|0,v()|0,j>>>1|0,0)|0;j=bg(i|0,v()|0,j|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1960>>0]=d}d=a[k+(l*2044|0)+1967>>0]|0;e=c[b+16892>>2]|0;f=(c[b+16896>>2]|0)+e|0;if(f){n=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;j=dg(e|0,0,8)|0;j=Zf(j|0,v()|0,f>>>1|0,0)|0;j=bg(j|0,v()|0,f|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1967>>0]=d;d=a[k+(l*2044|0)+1968>>0]|0;e=c[b+16900>>2]|0;f=(c[b+16904>>2]|0)+e|0;if(f){n=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;j=dg(e|0,0,8)|0;j=Zf(j|0,v()|0,f>>>1|0,0)|0;j=bg(j|0,v()|0,f|0,0)|0;v()|0;d=(((256-n|0)*(d&255)|0)+128+((((255-j|0)>>>23|j)&255|(j|0)==0)*n|0)|0)>>>8&255}a[m+1968>>0]=d;f=a[k+(l*2044|0)+1969>>0]|0;e=c[b+16908>>2]|0;d=(c[b+16912>>2]|0)+e|0;if(!d){b=f;n=m+1969|0;a[n>>0]=b;return}b=c[9216+((d>>>0<20?d:20)<<2)>>2]|0;n=dg(e|0,0,8)|0;n=Zf(n|0,v()|0,d>>>1|0,0)|0;n=bg(n|0,v()|0,d|0,0)|0;v()|0;b=(((256-b|0)*(f&255)|0)+128+((((255-n|0)>>>23|n)&255|(n|0)==0)*b|0)|0)>>>8&255;n=m+1969|0;a[n>>0]=b;return}function Zb(d){d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;xc(d+3980|0);a[d+3983>>0]=0;e=c[d+768>>2]|0;if(e|0)kg(e|0,0,(c[d+608>>2]|0)*(c[d+600>>2]|0)|0)|0;e=c[d+772>>2]|0;if(e|0)kg(e|0,0,(c[d+608>>2]|0)*(c[d+600>>2]|0)|0)|0;j=d+3962|0;b[j>>1]=0;b[j+2>>1]=0>>>16;b[d+3968>>1]=0;a[d+3956>>0]=1;a[d+3957>>0]=1;a[d+3958>>0]=1;a[d+3959>>0]=0;a[d+3960>>0]=-1;a[d+3961>>0]=-1;a[d+3966>>0]=0;a[d+3967>>0]=0;c[d+3952>>2]=-1;Wb(d);j=d+4104|0;e=c[j>>2]|0;f=e+36|0;g=f;h=7920;i=g+88|0;do{c[g>>2]=c[h>>2];g=g+4|0;h=h+4|0}while((g|0)<(i|0));b[f+88>>1]=b[4004]|0;g=e;h=8016;i=g+36|0;do{c[g>>2]=c[h>>2];g=g+4|0;h=h+4|0}while((g|0)<(i|0));g=e+1902|0;h=g;a[h>>0]=-14376213;a[h+1>>0]=-14376213>>8;a[h+2>>0]=-14376213>>16;a[h+3>>0]=-14376213>>24;g=g+4|0;a[g>>0]=-1869282526;a[g+1>>0]=-1869282526>>8;a[g+2>>0]=-1869282526>>16;a[g+3>>0]=-1869282526>>24;g=e+126|0;h=8064;i=g+48|0;do{b[g>>1]=b[h>>1]|0;g=g+2|0;h=h+2|0}while((g|0)<(i|0));g=e+1931|0;a[g>>0]=-507812343;a[g+1>>0]=-507812343>>8;a[g+2>>0]=-507812343>>16;a[g+3>>0]=-507812343>>24;g=e+1935|0;a[g>>0]=a[31683]|0;a[g+1>>0]=a[31684]|0;a[g+2>>0]=a[31685]|0;a[g+3>>0]=a[31686]|0;a[g+4>>0]=a[31687]|0;g=e+1950|0;a[g>>0]=a[31688]|0;a[g+1>>0]=a[31689]|0;a[g+2>>0]=a[31690]|0;a[g+3>>0]=a[31691]|0;a[g+4>>0]=a[31692]|0;g=e+1940|0;h=31693;i=g+10|0;do{a[g>>0]=a[h>>0]|0;g=g+1|0;h=h+1|0}while((g|0)<(i|0));g=e+1955|0;h=31703;i=g+12|0;do{a[g>>0]=a[h>>0]|0;g=g+1|0;h=h+1|0}while((g|0)<(i|0));g=e+1967|0;a[g>>0]=a[31715]|0;a[g+1>>0]=a[31716]|0;a[g+2>>0]=a[31717]|0;e=e+1910|0;g=e;h=8112;i=g+20|0;do{b[g>>1]=b[h>>1]|0;g=g+2|0;h=h+2|0}while((g|0)<(i|0));a[e+20>>0]=a[8132]|0;bc(d);e=c[j>>2]|0;c[e+2040>>2]=1;f=d+564|0;a:do if((c[f>>2]|0)!=0?(c[d+17356>>2]|0)==0:0){switch(c[d+588>>2]|0){case 3:{k=8;break a}case 2:break;default:break a}ig((c[d+4108>>2]|0)+((c[d+4112>>2]|0)*2044|0)|0,e|0,2044)|0}else k=8;while(0);if((k|0)==8){k=d+4108|0;ig(c[k>>2]|0,e|0,2044)|0;ig((c[k>>2]|0)+2044|0,c[j>>2]|0,2044)|0;ig((c[k>>2]|0)+4088|0,c[j>>2]|0,2044)|0;ig((c[k>>2]|0)+6132|0,c[j>>2]|0,2044)|0}if(c[f>>2]|0?(a[d+580>>0]|0)==0:0){k=d+3924|0;c[k>>2]=0;c[k+4>>2]=0;c[k+8>>2]=0;c[k+12>>2]=0;d=d+4112|0;c[d>>2]=0;return}e=c[d+712>>2]|0;if(!e){k=d+3924|0;c[k>>2]=0;c[k+4>>2]=0;c[k+8>>2]=0;c[k+12>>2]=0;d=d+4112|0;c[d>>2]=0;return}kg(e|0,0,((c[d+612>>2]|0)*68|0)*((c[d+600>>2]|0)+1|0)|0)|0;k=d+3924|0;c[k>>2]=0;c[k+4>>2]=0;c[k+8>>2]=0;c[k+12>>2]=0;d=d+4112|0;c[d>>2]=0;return}function _b(a,d){a=a|0;d=d|0;var e=0,f=0,g=0;if(!d)return;f=b[a>>1]|0;e=a+2|0;a=(b[e>>1]|0)==0;a=f<<16>>16==0?(a^1)&1:a?2:3;g=d+(a<<2)|0;c[g>>2]=(c[g>>2]|0)+1;if((a|1|0)==3)$b(f<<16>>16,d+16|0);if((a|2|0)!=3)return;$b(b[e>>1]|0,d+220|0);return}function $b(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=d+(b>>>31<<2)|0;c[e>>2]=(c[e>>2]|0)+1;b=(b|0)<0?0-b|0:b;e=b+-1|0;if((b|0)>8192){h=10;b=b+-8193|0}else{g=a[8176+(e>>3)>>0]|0;b=g&255;h=b;b=e-(g<<24>>24==0?0:8<<b)|0}f=d+8+(h<<2)|0;c[f>>2]=(c[f>>2]|0)+1;f=b>>3;g=b>>>1&3;e=b&1;if(!h){h=d+52+(f<<2)|0;c[h>>2]=(c[h>>2]|0)+1;h=d+140+(f<<4)+(g<<2)|0;c[h>>2]=(c[h>>2]|0)+1;d=d+188+(e<<2)|0;h=c[d>>2]|0;h=h+1|0;c[d>>2]=h;return}b=0;do{i=d+60+(b<<3)+((f>>>b&1)<<2)|0;c[i>>2]=(c[i>>2]|0)+1;b=b+1|0}while((b|0)!=(h|0));i=d+172+(g<<2)|0;c[i>>2]=(c[i>>2]|0)+1;i=d+196+(e<<2)|0;d=c[i>>2]|0;d=d+1|0;c[i>>2]=d;return}function ac(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;i=c[b+4104>>2]|0;j=c[b+4108>>2]|0;k=c[b+4112>>2]|0;wb(31718,j+(k*2044|0)+1970|0,b+16916|0,i+1970|0);h=(d|0)==0;g=0;do{d=a[j+(k*2044|0)+1973+(g*33|0)>>0]|0;e=c[b+16932+(g*204|0)>>2]|0;f=(c[b+16932+(g*204|0)+4>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)>>0]=d;wb(8144,j+(k*2044|0)+1973+(g*33|0)+1|0,b+16932+(g*204|0)+8|0,i+1973+(g*33|0)+1|0);wb(31724,j+(k*2044|0)+1973+(g*33|0)+11|0,b+16932+(g*204|0)+52|0,i+1973+(g*33|0)+11|0);d=a[j+(k*2044|0)+1973+(g*33|0)+12>>0]|0;e=c[b+16932+(g*204|0)+60>>2]|0;f=(c[b+16932+(g*204|0)+64>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+12>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+13>>0]|0;e=c[b+16932+(g*204|0)+68>>2]|0;f=(c[b+16932+(g*204|0)+72>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+13>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+14>>0]|0;e=c[b+16932+(g*204|0)+76>>2]|0;f=(c[b+16932+(g*204|0)+80>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+14>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+15>>0]|0;e=c[b+16932+(g*204|0)+84>>2]|0;f=(c[b+16932+(g*204|0)+88>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+15>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+16>>0]|0;e=c[b+16932+(g*204|0)+92>>2]|0;f=(c[b+16932+(g*204|0)+96>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+16>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+17>>0]|0;e=c[b+16932+(g*204|0)+100>>2]|0;f=(c[b+16932+(g*204|0)+104>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+17>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+18>>0]|0;e=c[b+16932+(g*204|0)+108>>2]|0;f=(c[b+16932+(g*204|0)+112>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+18>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+19>>0]|0;e=c[b+16932+(g*204|0)+116>>2]|0;f=(c[b+16932+(g*204|0)+120>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+19>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+20>>0]|0;e=c[b+16932+(g*204|0)+124>>2]|0;f=(c[b+16932+(g*204|0)+128>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+20>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+21>>0]|0;e=c[b+16932+(g*204|0)+132>>2]|0;f=(c[b+16932+(g*204|0)+136>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+21>>0]=d;wb(31726,j+(k*2044|0)+1973+(g*33|0)+22|0,b+16932+(g*204|0)+140|0,i+1973+(g*33|0)+22|0);wb(31726,j+(k*2044|0)+1973+(g*33|0)+25|0,b+16932+(g*204|0)+156|0,i+1973+(g*33|0)+25|0);wb(31726,j+(k*2044|0)+1973+(g*33|0)+28|0,b+16932+(g*204|0)+172|0,i+1973+(g*33|0)+28|0);if(!h){d=a[j+(k*2044|0)+1973+(g*33|0)+31>>0]|0;e=c[b+16932+(g*204|0)+188>>2]|0;f=(c[b+16932+(g*204|0)+192>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+31>>0]=d;d=a[j+(k*2044|0)+1973+(g*33|0)+32>>0]|0;e=c[b+16932+(g*204|0)+196>>2]|0;f=(c[b+16932+(g*204|0)+200>>2]|0)+e|0;if(f){l=c[9216+((f>>>0<20?f:20)<<2)>>2]|0;e=dg(e|0,0,8)|0;e=Zf(e|0,v()|0,f>>>1|0,0)|0;f=bg(e|0,v()|0,f|0,0)|0;v()|0;d=(((256-l|0)*(d&255)|0)+128+((((255-f|0)>>>23|f)&255|(f|0)==0)*l|0)|0)>>>8&255}a[i+1973+(g*33|0)+32>>0]=d}g=g+1|0}while((g|0)!=2);return}function bc(b){b=b|0;var d=0,e=0;b=(c[b+4104>>2]|0)+1970|0;d=31732;e=b+69|0;do{a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0}while((b|0)<(e|0));return}function cc(a){a=a|0;var b=0,d=0,e=0;e=a+4|0;b=c[e>>2]|0;if((c[a>>2]|0)>0){d=0;do{qb(c[b+(d*12|0)>>2]|0);b=c[e>>2]|0;c[b+(d*12|0)>>2]=0;d=d+1|0}while((d|0)<(c[a>>2]|0))}qb(b);c[e>>2]=0;c[a>>2]=16;a=pb(16,12)|0;c[e>>2]=a;return (a|0)==0|0}function dc(a){a=a|0;var b=0,d=0,e=0;e=a+4|0;b=c[e>>2]|0;if((c[a>>2]|0)<=0){a=b;qb(a);c[e>>2]=0;return}d=0;do{qb(c[b+(d*12|0)>>2]|0);b=c[e>>2]|0;c[b+(d*12|0)>>2]=0;d=d+1|0}while((d|0)<(c[a>>2]|0));qb(b);c[e>>2]=0;return}function ec(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0;if(!a){d=-1;return d|0}g=c[a>>2]|0;a:do if((g|0)>0){f=c[a+4>>2]|0;e=0;while(1){if(!(c[f+(e*12|0)+8>>2]|0)){h=e;break a}e=e+1|0;if((e|0)>=(g|0)){h=e;break}}}else h=0;while(0);if((h|0)==(g|0)){d=-1;return d|0}a=a+4|0;f=c[a>>2]|0;g=c[f+(h*12|0)+4>>2]|0;e=f+(h*12|0)|0;do if(g>>>0<b>>>0){qb(c[e>>2]|0);g=pb(1,b)|0;f=c[a>>2]|0;e=f+(h*12|0)|0;c[e>>2]=g;if(!g){d=-1;return d|0}else{c[f+(h*12|0)+4>>2]=b;a=b;break}}else a=g;while(0);c[d>>2]=c[e>>2];c[d+4>>2]=a;c[f+(h*12|0)+8>>2]=1;c[d+8>>2]=f+(h*12|0);d=0;return d|0}function fc(a,b){a=a|0;b=b|0;a=c[b+8>>2]|0;if(!a)return 0;c[a+8>>2]=0;return 0}function gc(e,f,g,h){e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;i=L;L=L+48|0;q=i;m=i+40|0;n=i+32|0;o=c[9312+(h<<3)+4>>2]|0;W[o&7](e,q);W[o&7](e+8|0,q+8|0);W[o&7](e+16|0,q+16|0);W[o&7](e+24|0,q+24|0);o=9312+(h<<3)|0;b[m>>1]=b[q>>1]|0;s=m+2|0;b[s>>1]=b[q+8>>1]|0;r=m+4|0;b[r>>1]=b[q+16>>1]|0;p=m+6|0;b[p>>1]=b[q+24>>1]|0;t=c[o>>2]|0;W[t&7](m,n);k=((b[n>>1]|0)+8>>4)+(d[f>>0]|0)|0;k=(k|0)>0?k:0;a[f>>0]=(k|0)<255?k:255;k=f+g|0;l=n+2|0;h=((b[l>>1]|0)+8>>4)+(d[k>>0]|0)|0;h=(h|0)>0?h:0;a[k>>0]=(h|0)<255?h:255;k=g<<1;h=f+k|0;j=n+4|0;u=((b[j>>1]|0)+8>>4)+(d[h>>0]|0)|0;u=(u|0)>0?u:0;a[h>>0]=(u|0)<255?u:255;h=g*3|0;u=f+h|0;e=n+6|0;v=((b[e>>1]|0)+8>>4)+(d[u>>0]|0)|0;v=(v|0)>0?v:0;a[u>>0]=(v|0)<255?v:255;b[m>>1]=b[q+2>>1]|0;b[s>>1]=b[q+10>>1]|0;b[r>>1]=b[q+18>>1]|0;b[p>>1]=b[q+26>>1]|0;W[t&7](m,n);t=f+1|0;u=((b[n>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;t=f+(g+1)|0;u=((b[l>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;t=f+(k|1)|0;u=((b[j>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;t=f+(h+1)|0;u=((b[e>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;b[m>>1]=b[q+4>>1]|0;b[s>>1]=b[q+12>>1]|0;b[r>>1]=b[q+20>>1]|0;b[p>>1]=b[q+28>>1]|0;o=c[o>>2]|0;W[o&7](m,n);t=f+2|0;u=((b[n>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;t=f+(g+2)|0;u=((b[l>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;t=f+(k+2)|0;u=((b[j>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;t=f+(h+2)|0;u=((b[e>>1]|0)+8>>4)+(d[t>>0]|0)|0;u=(u|0)>0?u:0;a[t>>0]=(u|0)<255?u:255;b[m>>1]=b[q+6>>1]|0;b[s>>1]=b[q+14>>1]|0;b[r>>1]=b[q+22>>1]|0;b[p>>1]=b[q+30>>1]|0;W[o&7](m,n);m=f+3|0;n=((b[n>>1]|0)+8>>4)+(d[m>>0]|0)|0;n=(n|0)>0?n:0;a[m>>0]=(n|0)<255?n:255;g=f+(g+3)|0;l=((b[l>>1]|0)+8>>4)+(d[g>>0]|0)|0;l=(l|0)>0?l:0;a[g>>0]=(l|0)<255?l:255;g=f+(k+3)|0;j=((b[j>>1]|0)+8>>4)+(d[g>>0]|0)|0;j=(j|0)>0?j:0;a[g>>0]=(j|0)<255?j:255;h=f+(h+3)|0;g=((b[e>>1]|0)+8>>4)+(d[h>>0]|0)|0;g=(g|0)>0?g:0;a[h>>0]=(g|0)<255?g:255;L=i;return}function hc(e,f,g,h){e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0;F=L;L=L+160|0;C=F+32|0;D=F+16|0;E=F;B=c[9344+(h<<3)>>2]|0;h=c[9344+(h<<3)+4>>2]|0;W[h&7](e,C);W[h&7](e+16|0,C+16|0);W[h&7](e+32|0,C+32|0);W[h&7](e+48|0,C+48|0);W[h&7](e+64|0,C+64|0);W[h&7](e+80|0,C+80|0);W[h&7](e+96|0,C+96|0);W[h&7](e+112|0,C+112|0);h=D+2|0;i=D+4|0;j=D+6|0;k=D+8|0;l=D+10|0;m=D+12|0;n=D+14|0;o=E+2|0;p=g<<1;q=E+4|0;r=g*3|0;s=E+6|0;t=g<<2;u=E+8|0;v=g*5|0;w=E+10|0;x=g*6|0;y=E+12|0;z=g*7|0;A=E+14|0;e=0;do{b[D>>1]=b[C+(e<<1)>>1]|0;b[h>>1]=b[C+(e+8<<1)>>1]|0;b[i>>1]=b[C+(e+16<<1)>>1]|0;b[j>>1]=b[C+(e+24<<1)>>1]|0;b[k>>1]=b[C+(e+32<<1)>>1]|0;b[l>>1]=b[C+(e+40<<1)>>1]|0;b[m>>1]=b[C+(e+48<<1)>>1]|0;b[n>>1]=b[C+(e+56<<1)>>1]|0;W[B&7](D,E);G=f+e|0;H=((b[E>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(e+g)|0;H=((b[o>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(p+e)|0;H=((b[q>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(r+e)|0;H=((b[s>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(t+e)|0;H=((b[u>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(v+e)|0;H=((b[w>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(x+e)|0;H=((b[y>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;G=f+(z+e)|0;H=((b[A>>1]|0)+16>>5)+(d[G>>0]|0)|0;H=(H|0)>0?H:0;a[G>>0]=(H|0)<255?H:255;e=e+1|0}while((e|0)!=8);L=F;return}function ic(e,f,g,h){e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0;da=L;L=L+576|0;aa=da+64|0;ba=da+32|0;ca=da;$=c[9376+(h<<3)>>2]|0;h=c[9376+(h<<3)+4>>2]|0;W[h&7](e,aa);W[h&7](e+32|0,aa+32|0);W[h&7](e+64|0,aa+64|0);W[h&7](e+96|0,aa+96|0);W[h&7](e+128|0,aa+128|0);W[h&7](e+160|0,aa+160|0);W[h&7](e+192|0,aa+192|0);W[h&7](e+224|0,aa+224|0);W[h&7](e+256|0,aa+256|0);W[h&7](e+288|0,aa+288|0);W[h&7](e+320|0,aa+320|0);W[h&7](e+352|0,aa+352|0);W[h&7](e+384|0,aa+384|0);W[h&7](e+416|0,aa+416|0);W[h&7](e+448|0,aa+448|0);W[h&7](e+480|0,aa+480|0);h=ba+2|0;i=ba+4|0;j=ba+6|0;k=ba+8|0;l=ba+10|0;m=ba+12|0;n=ba+14|0;o=ba+16|0;p=ba+18|0;q=ba+20|0;r=ba+22|0;s=ba+24|0;t=ba+26|0;u=ba+28|0;v=ba+30|0;w=ca+2|0;x=g<<1;y=ca+4|0;z=g*3|0;A=ca+6|0;B=g<<2;C=ca+8|0;D=g*5|0;E=ca+10|0;F=g*6|0;G=ca+12|0;H=g*7|0;I=ca+14|0;J=g<<3;K=ca+16|0;M=g*9|0;N=ca+18|0;O=g*10|0;P=ca+20|0;Q=g*11|0;R=ca+22|0;S=g*12|0;T=ca+24|0;U=g*13|0;V=ca+26|0;X=g*14|0;Y=ca+28|0;Z=g*15|0;_=ca+30|0;e=0;do{b[ba>>1]=b[aa+(e<<1)>>1]|0;b[h>>1]=b[aa+(e+16<<1)>>1]|0;b[i>>1]=b[aa+(e+32<<1)>>1]|0;b[j>>1]=b[aa+(e+48<<1)>>1]|0;b[k>>1]=b[aa+(e+64<<1)>>1]|0;b[l>>1]=b[aa+(e+80<<1)>>1]|0;b[m>>1]=b[aa+(e+96<<1)>>1]|0;b[n>>1]=b[aa+(e+112<<1)>>1]|0;b[o>>1]=b[aa+(e+128<<1)>>1]|0;b[p>>1]=b[aa+(e+144<<1)>>1]|0;b[q>>1]=b[aa+(e+160<<1)>>1]|0;b[r>>1]=b[aa+(e+176<<1)>>1]|0;b[s>>1]=b[aa+(e+192<<1)>>1]|0;b[t>>1]=b[aa+(e+208<<1)>>1]|0;b[u>>1]=b[aa+(e+224<<1)>>1]|0;b[v>>1]=b[aa+(e+240<<1)>>1]|0;W[$&7](ba,ca);ea=f+e|0;fa=((b[ca>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(e+g)|0;fa=((b[w>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(x+e)|0;fa=((b[y>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(z+e)|0;fa=((b[A>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(B+e)|0;fa=((b[C>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(D+e)|0;fa=((b[E>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(F+e)|0;fa=((b[G>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(H+e)|0;fa=((b[I>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(J+e)|0;fa=((b[K>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(M+e)|0;fa=((b[N>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(O+e)|0;fa=((b[P>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(Q+e)|0;fa=((b[R>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(S+e)|0;fa=((b[T>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(U+e)|0;fa=((b[V>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(X+e)|0;fa=((b[Y>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;ea=f+(Z+e)|0;fa=((b[_>>1]|0)+32>>6)+(d[ea>>0]|0)|0;fa=(fa|0)>0?fa:0;a[ea>>0]=(fa|0)<255?fa:255;e=e+1|0}while((e|0)!=16);L=da;return}function jc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if((d|0)>1){Pe(a,b,c);return}else{Qe(a,b,c);return}}function kc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if((d|0)>1){Le(a,b,c);return}else{Me(a,b,c);return}}function lc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if((d|0)==1){Ve(a,b,c);return}if((d|0)<13){Ue(a,b,c);return}else{Te(a,b,c);return}}function mc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if((d|0)==1){$e(a,b,c);return}if((d|0)<11){_e(a,b,c);return}if((d|0)<39){Ze(a,b,c);return}else{Ye(a,b,c);return}}function nc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if((d|0)==1){ef(a,b,c);return}if((d|0)<35){df(a,b,c);return}if((d|0)<136){cf(a,b,c);return}else{bf(a,b,c);return}}function oc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(a|0){gc(b,c,d,a);return}if((e|0)>1){Pe(b,c,d);return}else{Qe(b,c,d);return}}function pc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(a|0){hc(b,c,d,a);return}if((e|0)==1){Ve(b,c,d);return}if((e|0)<13){Ue(b,c,d);return}else{Te(b,c,d);return}}function qc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(a|0){ic(b,c,d,a);return}if((e|0)==1){$e(b,c,d);return}if((e|0)<11){_e(b,c,d);return}if((e|0)<39){Ze(b,c,d);return}else{Ye(b,c,d);return}}function rc(){if(c[8132]|0)return;c[8132]=1;return}function sc(a,d,e,f,g){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0;e=e<<4;j=c[g>>2]|0;i=((j|0)<0)<<31>>31;e=Yf(j|0,i|0,e|0,((e|0)<0)<<31>>31|0)|0;e=cg(e|0,v()|0,14)|0;v()|0;h=f<<4;g=c[g+4>>2]|0;f=((g|0)<0)<<31>>31;h=Yf(g|0,f|0,h|0,((h|0)<0)<<31>>31|0)|0;h=cg(h|0,v()|0,14)|0;v()|0;k=b[d>>1]|0;f=Yf(k|0,((k|0)<0)<<31>>31|0,g|0,f|0)|0;f=cg(f|0,v()|0,14)|0;v()|0;g=b[d+2>>1]|0;g=Yf(g|0,((g|0)<0)<<31>>31|0,j|0,i|0)|0;g=cg(g|0,v()|0,14)|0;v()|0;c[a>>2]=(h&15)+f;c[a+4>>2]=(e&15)+g;return}function tc(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0;if((d<<4|0)<(f|0)|((b<<4|0)<(e|0)|((e<<1|0)<(b|0)|(f<<1|0)<(d|0)))){c[a>>2]=-1;c[a+4>>2]=-1;return}b=(b<<14|0)/(e|0)|0;c[a>>2]=b;e=(d<<14|0)/(f|0)|0;c[a+4>>2]=e;f=b>>10;c[a+8>>2]=f;d=e>>10;c[a+12>>2]=d;do if((b|0)==-1|(e|0)==-1){b=a+16|0;g=9}else{if((b|0)==16384){b=a+16|0;if((e|0)==16384){g=9;break}}else b=a+16|0;c[b>>2]=26;b=28}while(0);if((g|0)==9){c[b>>2]=27;b=27}c[a+20>>2]=b;b=(d|0)==16;i=a+24|0;if((f|0)==16)if(b){c[i>>2]=5;c[a+28>>2]=6;c[a+32>>2]=7;c[a+36>>2]=8;c[a+40>>2]=9;c[a+44>>2]=10;c[a+48>>2]=11;c[a+52>>2]=12;return}else{b=2;e=4;d=14;f=14;g=13;h=13}else{h=b?1:2;f=b?3:4;b=h;e=f;d=4;g=2}c[i>>2]=h;c[a+28>>2]=f;c[a+32>>2]=g;c[a+36>>2]=d;c[a+40>>2]=b;c[a+44>>2]=e;c[a+48>>2]=2;c[a+52>>2]=4;return}function uc(a,b){a=a|0;b=b|0;b=c[b>>2]|0;b=Yf(b|0,((b|0)<0)<<31>>31|0,a|0,((a|0)<0)<<31>>31|0)|0;b=cg(b|0,v()|0,14)|0;v()|0;return b|0}function vc(a,b){a=a|0;b=b|0;return a|0}function wc(a,b){a=a|0;b=b|0;b=c[b+4>>2]|0;b=Yf(b|0,((b|0)<0)<<31>>31|0,a|0,((a|0)<0)<<31>>31|0)|0;b=cg(b|0,v()|0,14)|0;v()|0;return b|0}function xc(a){a=a|0;var b=0;a=a+16|0;b=a+100|0;do{c[a>>2]=0;a=a+4|0}while((a|0)<(b|0));return}function yc(a,b,d){a=a|0;b=b|0;d=d|0;b=a+80+(b<<2)|0;c[b>>2]=c[b>>2]|1<<d;return}function zc(a){a=a|0;return c[9424+(a<<2)>>2]|0}function Ac(a){a=a|0;return c[9440+(a<<2)>>2]|0}function Bc(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;b[a+16+(c<<3)+(d<<1)>>1]=e;return}function Cc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0;e=c[b+600>>2]|0;b=c[b+17368>>2]|0;f=e+7>>3;g=f*d>>b<<3;c[a>>2]=(g|0)<(e|0)?g:e;d=f*(d+1|0)>>b<<3;c[a+4>>2]=(d|0)<(e|0)?d:e;return}function Dc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0;e=c[b+608>>2]|0;b=c[b+17364>>2]|0;f=e+7>>3;g=f*d>>b<<3;c[a+8>>2]=(g|0)<(e|0)?g:e;d=f*(d+1|0)>>b<<3;c[a+12>>2]=(d|0)<(e|0)?d:e;return}function Ec(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;f=c[b+600>>2]|0;g=c[b+17368>>2]|0;h=f+7>>3;i=h*d>>g<<3;c[a>>2]=(i|0)<(f|0)?i:f;d=h*(d+1|0)>>g<<3;c[a+4>>2]=(d|0)<(f|0)?d:f;d=c[b+608>>2]|0;b=c[b+17364>>2]|0;f=d+7>>3;g=f*e>>b<<3;c[a+8>>2]=(g|0)<(d|0)?g:d;e=f*(e+1|0)>>b<<3;c[a+12>>2]=(e|0)<(d|0)?e:d;return}function Fc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0;e=a+7>>3;a=0;while(1)if((64<<a|0)<(e|0))a=a+1|0;else break;c[b>>2]=a;a=1;while(1)if((e>>a|0)>3)a=a+1|0;else break;c[d>>2]=a+-1;return}function Gc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0;e=a+3948|0;d=c[e>>2]|0;b=(d|0)>0;f=(b&1)+((d|0)>4&1)|0;d=9-d|0;if(b){b=0;do{g=b>>>f;g=(g|0)>(d|0)?d:g;g=(g|0)>1?g:1;kg(a+784+(b*48|0)+16|0,g&255|0,16)|0;kg(a+784+(b*48|0)|0,(b<<1)+4+g&255|0,16)|0;b=b+1|0}while((b|0)!=64)}else{b=0;do{g=b>>>f;g=(g|0)>1?g:1;kg(a+784+(b*48|0)+16|0,g&255|0,16)|0;kg(a+784+(b*48|0)|0,(b<<1)+4+g&255|0,16)|0;b=b+1|0}while((b|0)!=64)}c[a+3952>>2]=c[e>>2];b=0;do{kg(a+784+(b*48|0)+32|0,b>>>4&255|0,16)|0;b=b+1|0}while((b|0)!=64);return}function Hc(d,e){d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;q=e>>5;i=d+3952|0;j=d+3948|0;f=c[j>>2]|0;if((c[i>>2]|0)!=(f|0)){p=(f|0)>0;h=(p&1)+((f|0)>4&1)|0;g=9-f|0;if(p){f=0;do{p=f>>>h;p=(p|0)>(g|0)?g:p;p=(p|0)>1?p:1;kg(d+784+(f*48|0)+16|0,p&255|0,16)|0;kg(d+784+(f*48|0)|0,(f<<1)+4+p&255|0,16)|0;f=f+1|0}while((f|0)!=64)}else{f=0;do{p=f>>>h;p=(p|0)>1?p:1;kg(d+784+(f*48|0)+16|0,p&255|0,16)|0;kg(d+784+(f*48|0)|0,(f<<1)+4+p&255|0,16)|0;f=f+1|0}while((f|0)!=64)}c[i>>2]=c[j>>2]}h=d+3980|0;i=d+3956|0;j=d+3958|0;k=d+3983|0;l=d+3959|0;m=d+3966|0;n=d+3967|0;o=d+3960|0;p=d+3961|0;g=0;do{if((a[h>>0]|0)!=0?(c[d+4060+(g<<2)>>2]&2|0)!=0:0){f=((a[k>>0]|0)==1?0:e)+(b[d+3996+(g<<3)+2>>1]|0)|0;f=(f|0)<63?f:63;f=(f|0)>0?f:0}else f=e;if(!(a[i>>0]|0))kg(d+3856+(g<<3)|0,f&255|0,8)|0;else{t=(a[j>>0]<<q)+f|0;t=(t|0)<63?t:63;a[d+3856+(g<<3)>>0]=(t|0)>0?t:0;t=(a[l>>0]<<q)+f|0;s=a[m>>0]<<q;r=t+s|0;r=(r|0)<63?r:63;a[d+3856+(g<<3)+2>>0]=(r|0)>0?r:0;r=a[n>>0]<<q;t=t+r|0;t=(t|0)<63?t:63;a[d+3856+(g<<3)+3>>0]=(t|0)>0?t:0;t=(a[o>>0]<<q)+f|0;u=t+s|0;u=(u|0)<63?u:63;a[d+3856+(g<<3)+4>>0]=(u|0)>0?u:0;t=t+r|0;t=(t|0)<63?t:63;a[d+3856+(g<<3)+5>>0]=(t|0)>0?t:0;f=(a[p>>0]<<q)+f|0;s=f+s|0;s=(s|0)<63?s:63;a[d+3856+(g<<3)+6>>0]=(s|0)>0?s:0;f=f+r|0;f=(f|0)<63?f:63;a[d+3856+(g<<3)+7>>0]=(f|0)>0?f:0}g=g+1|0}while((g|0)!=8);return}function Ic(a,d,f,g){a=a|0;d=d|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0;i=g+24|0;I=g+16|0;w=I;j=c[w>>2]|c[i>>2];i=c[w+4>>2]|c[i+4>>2];w=I;c[w>>2]=j;c[w+4>>2]=i;w=g+56|0;F=g+48|0;K=F;x=c[K>>2]|c[w>>2];w=c[K+4>>2]|c[w+4>>2];K=F;c[K>>2]=x;c[K+4>>2]=w;K=g+76|0;h=b[K>>1]|b[g+78>>1];b[K>>1]=h;G=g+84|0;u=b[G>>1]|b[g+86>>1];b[G>>1]=u;o=g;p=c[o>>2]|0;o=c[o+4>>2]|0;L=g+8|0;l=L;m=c[l>>2]|p&286331153;l=c[l+4>>2]|o&286331153;B=L;c[B>>2]=m;c[B+4>>2]=l;p=p&-286331154;o=o&-286331154;B=g;c[B>>2]=p;c[B+4>>2]=o;B=g+32|0;s=B;r=c[s>>2]|0;s=c[s+4>>2]|0;D=g+40|0;z=D;y=c[z>>2]|r&255;z=c[z+4>>2]|s&255;H=D;c[H>>2]=y;c[H+4>>2]=z;r=r&-256;s=s&-256;H=B;c[H>>2]=r;c[H+4>>2]=s;H=g+72|0;n=b[H>>1]|0;J=g+74|0;k=b[J>>1]|n&4369;b[J>>1]=k;n=n&-4370;b[H>>1]=n;C=g+80|0;t=b[C>>1]|0;E=g+82|0;A=b[E>>1]|t&15;b[E>>1]=A;t=t&-16;b[C>>1]=t;q=c[a+600>>2]|0;a:do if((d+8|0)>(q|0)){M=q-d|0;O=((M|0)<0)<<31>>31;Q=dg(M|0,O|0,3)|0;v()|0;Q=dg(1,0,Q|0)|0;Q=Zf(Q|0,v()|0,-1,-1)|0;P=v()|0;O=Zf(M|0,O|0,1,0)|0;O=cg(O|0,v()|0,1)|0;O=dg(O|0,v()|0,2)|0;v()|0;O=(1<<O)+65535|0;p=p&Q;o=o&P;q=g;c[q>>2]=p;c[q+4>>2]=o;r=r&Q;s=s&P;q=B;c[q>>2]=r;c[q+4>>2]=s;n=O&(n&65535)&65535;b[H>>1]=n;t=O&(t&65535)&65535;b[C>>1]=t;m=m&Q;l=l&P;q=L;c[q>>2]=m;c[q+4>>2]=l;y=y&Q;z=z&P;q=D;c[q>>2]=y;c[q+4>>2]=z;k=O&(k&65535)&65535;b[J>>1]=k;q=O&(A&65535);A=q&65535;b[E>>1]=A;j=j&Q;i=i&P;d=I;c[d>>2]=j;c[d+4>>2]=i;x=x&Q;w=w&P;d=F;c[d>>2]=x;c[d+4>>2]=w;h=O&(h&65535)&65535;b[K>>1]=h;d=O&(u&65535);u=d&65535;b[G>>1]=u;N=g+64|0;R=N;P=c[R+4>>2]&P;c[N>>2]=c[R>>2]&Q;c[N+4>>2]=P;N=g+88|0;b[N>>1]=O&(e[N>>1]|0);switch(M|0){case 1:{A=(q|d)&65535;b[E>>1]=A;b[G>>1]=0;u=0;break a}case 5:{A=u&-256|A;b[E>>1]=A;u=u&255;b[G>>1]=u;break a}default:break a}}while(0);d=c[a+608>>2]|0;b:do if((f+8|0)>(d|0)){R=d-f|0;O=(1<<R)+-1|0;O=Yf(O|0,((O|0)<0)<<31>>31|0,16843009,16843009)|0;P=v()|0;Q=Zf(R|0,((R|0)<0)<<31>>31|0,1,0)|0;Q=cg(Q|0,v()|0,1)|0;v()|0;Q=(4369<<Q)+61167|0;p=p&O;o=o&P;q=g;c[q>>2]=p;c[q+4>>2]=o;q=B;c[q>>2]=r&O;c[q+4>>2]=s&P;q=Q&(n&65535)&65535;b[H>>1]=q;b[C>>1]=Q&(t&65535);m=m&O;l=l&P;n=L;c[n>>2]=m;c[n+4>>2]=l;n=D;c[n>>2]=y&O;c[n+4>>2]=z&P;n=Q&(k&65535);k=n&65535;b[J>>1]=k;b[E>>1]=Q&(A&65535);d=j&O;i=i&P;j=I;c[j>>2]=d;c[j+4>>2]=i;j=F;c[j>>2]=x&O;c[j+4>>2]=w&P;j=Q&(h&65535);h=j&65535;b[K>>1]=h;b[G>>1]=Q&(u&65535);Q=g+64|0;N=Q;P=c[N+4>>2]&P;c[Q>>2]=c[N>>2]&O;c[Q+4>>2]=P;Q=g+88|0;b[Q>>1]=(4369<<(R>>1))+61167&(e[Q>>1]|0);switch(R|0){case 1:{k=(n|j)&65535;b[J>>1]=k;b[K>>1]=0;n=q;j=d;h=0;break b}case 5:{k=h&-13108|k;b[J>>1]=k;h=h&13107;b[K>>1]=h;n=q;j=d;break b}default:{n=q;j=d;break b}}}while(0);if(f|0)return;R=g;c[R>>2]=p&-16843010;c[R+4>>2]=o&-16843010;b[H>>1]=n&-4370;R=L;c[R>>2]=m&-16843010;c[R+4>>2]=l&-16843010;b[J>>1]=k&-4370;R=I;c[R>>2]=j&-16843010;c[R+4>>2]=i&-16843010;b[K>>1]=h&-4370;return}function Jc(b,e,f,g,h){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0;$=L;L=L+192|0;W=$+160|0;X=$+128|0;Y=$+96|0;Z=$+64|0;_=$;F=e+4|0;G=c[F>>2]|0;H=e+8|0;Q=c[H>>2]|0;T=1<<Q;I=1<<G;J=c[b+612>>2]<<Q;U=e+12|0;K=c[U>>2]|0;c[W>>2]=0;c[W+4>>2]=0;c[W+8>>2]=0;c[W+12>>2]=0;c[W+16>>2]=0;c[W+20>>2]=0;c[W+24>>2]=0;c[W+28>>2]=0;c[X>>2]=0;c[X+4>>2]=0;c[X+8>>2]=0;c[X+12>>2]=0;c[X+16>>2]=0;c[X+20>>2]=0;c[X+24>>2]=0;c[X+28>>2]=0;c[Y>>2]=0;c[Y+4>>2]=0;c[Y+8>>2]=0;c[Y+12>>2]=0;c[Y+16>>2]=0;c[Y+20>>2]=0;c[Y+24>>2]=0;c[Y+28>>2]=0;c[Z>>2]=0;c[Z+4>>2]=0;c[Z+8>>2]=0;c[Z+12>>2]=0;c[Z+16>>2]=0;c[Z+20>>2]=0;c[Z+24>>2]=0;c[Z+28>>2]=0;V=b+600|0;M=b+608|0;N=(G|0)!=0;O=(Q|0)!=0;P=~((h|0)==0);S=e+16|0;R=K;E=0;e=R;while(1){i=E+g|0;j=c[V>>2]|0;if((i|0)>=(j|0))break;u=O&(i|0)==(j+-1|0);D=E<<3;v=(E&T|0)==0;C=Z+(E<<2)|0;y=E>>Q&3;w=(y|0)!=0;x=X+(E<<2)|0;y=(y|0)==0;z=W+(E<<2)|0;A=Y+(E<<2)|0;t=c[M>>2]|0;B=t+-1|0;s=0;l=0;k=0;j=0;do{n=s+h|0;if((n|0)>=(t|0))break;o=c[f+(s<<2)>>2]|0;if(!(a[o+3>>0]|0))q=0;else q=(a[o+8>>0]|0)>0;m=d[o>>0]|0;if((d[31889+m>>0]|0)>1)i=((d[31915+m>>0]|0)+-1&s|0)==0&1;else i=1;p=q&(i|0)==0;if((d[31902+m>>0]|0)>1)i=((d[31928+m>>0]|0)+-1&E|0)==0&1;else i=1;r=q&(i|0)==0;m=a[(c[H>>2]|0)+(11280+(m<<4)+(d[o+2>>0]<<2)+(c[F>>2]<<1))>>0]|0;n=N&(n|0)==(B|0);i=a[(c[9456+(d[o+1>>0]<<2)>>2]|0)+(b+3856+(a[o+4>>0]<<3)+(a[o+8>>0]<<1))>>0]|0;o=s>>G;a[_+(o+D)>>0]=i;a:do if(i<<24>>24)switch(m<<24>>24){case 3:{do if((o&3|0)==0&(p^1)){i=1<<o;if(n){k=i|k;break}else{l=i|l;break}}while(0);if(r|w)break a;i=1<<o;if(u){c[x>>2]=c[x>>2]|i;break a}else{c[z>>2]=c[z>>2]|i;break a}}case 2:{do if((o&1|0)==0&(p^1)){i=1<<o;if(n){k=i|k;break}else{l=i|l;break}}while(0);if(!(v&(r^1)))break a;i=1<<o;if(u){c[x>>2]=c[x>>2]|i;break a}else{c[z>>2]=c[z>>2]|i;break a}}default:{do if(!p){i=1<<o;if((o&3|0)==0|m<<24>>24==1){k=i|k;break}else{j=i|j;break}}while(0);if(!r){r=m<<24>>24==1|y?x:A;c[r>>2]=c[r>>2]|1<<o}if(q|(n|m<<24>>24!=0))break a;c[C>>2]=c[C>>2]|1<<o;break a}}while(0);s=s+I|0}while((s|0)<8);i=c[S>>2]|0;l=l&P;m=k&P;n=j&P;k=c[C>>2]|0;j=m|n|l|k;if(j){q=_+D|0;p=l;o=k;while(1){k=d[q>>0]|0;l=b+784+(k*48|0)|0;do if(j&1|0){if(p&1|0){Ie(e,i,l,b+784+(k*48|0)+16|0,b+784+(k*48|0)+32|0);break}if(m&1|0){De(e,i,l,b+784+(k*48|0)+16|0,b+784+(k*48|0)+32|0);break}if(n&1|0)ze(e,i,l,b+784+(k*48|0)+16|0,b+784+(k*48|0)+32|0)}while(0);if(o&1|0)ze(e+4|0,i,l,b+784+(k*48|0)+16|0,b+784+(k*48|0)+32|0);j=j>>>1;if(!j)break;else{q=q+1|0;p=p>>>1;o=o>>>1;n=n>>>1;m=m>>>1;e=e+8|0}}i=c[S>>2]|0;e=c[U>>2]|0}e=e+(i<<3)|0;c[U>>2]=e;E=E+T|0;if((E|0)>=8)break;else f=f+(J<<2)|0}c[U>>2]=K;m=b+784|0;if(!Q){j=0;k=R;while(1){e=j+g|0;if((e|0)>=(c[V>>2]|0)){e=65;break}if(!e){e=0;f=0;i=0}else{e=c[Y+(j<<2)>>2]|0;f=c[X+(j<<2)>>2]|0;i=c[W+(j<<2)>>2]|0}Kc(k,c[S>>2]|0,i,f,e,c[Z+(j<<2)>>2]|0,m,_+(j<<3)|0);k=(c[U>>2]|0)+(c[S>>2]<<3)|0;c[U>>2]=k;j=j+T|0;if((j|0)>=8){e=65;break}}if((e|0)==65){L=$;return}}l=0;k=R;while(1){f=l+g|0;e=c[V>>2]|0;if((f|0)>=(e|0)){e=65;break}if((f|0)==(e+-1|0))j=0;else j=c[Z+(l<<2)>>2]|0;if(!f){e=0;f=0;i=0}else{e=c[Y+(l<<2)>>2]|0;f=c[X+(l<<2)>>2]|0;i=c[W+(l<<2)>>2]|0}Kc(k,c[S>>2]|0,i,f,e,j,m,_+(l<<3)|0);k=(c[U>>2]|0)+(c[S>>2]<<3)|0;c[U>>2]=k;l=l+T|0;if((l|0)>=8){e=65;break}}if((e|0)==65){L=$;return}}function Kc(a,b,c,e,f,g,h,i){a=a|0;b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;j=e|c|f|g;if(!j)return;q=b<<2;p=e;o=f;while(1){do if(j&1){e=d[i>>0]|0;n=h+(e*48|0)|0;if(c&1|0){f=h+(e*48|0)+16|0;e=h+(e*48|0)+32|0;if((c&3|0)==3){He(a,b,n,f,e);e=2;break}else{Fe(a,b,n,f,e);e=1;break}}if(p&1|0){if((p&3|0)!=3){f=h+(e*48|0)+16|0;e=h+(e*48|0)+32|0;Be(a,b,n,f,e);if(!(g&1)){e=1;break}xe(a+q|0,b,n,f,e);e=1;break}l=d[i+1>>0]|0;m=h+(e*48|0)+16|0;e=h+(e*48|0)+32|0;f=h+(l*48|0)|0;k=h+(l*48|0)+16|0;l=h+(l*48|0)+32|0;Ce(a,b,n,m,e,f,k,l);if((g&3|0)==3){ye(a+q|0,b,n,m,e,f,k,l);e=2;break}if(g&1|0){xe(a+q|0,b,n,m,e);e=2;break}if(!(g&2)){e=2;break}xe(a+8+q|0,b,f,k,l);e=2;break}if(!(o&1)){xe(a+q|0,b,n,h+(e*48|0)+16|0,h+(e*48|0)+32|0);e=1;break}if((o&3|0)!=3){f=h+(e*48|0)+16|0;e=h+(e*48|0)+32|0;xe(a,b,n,f,e);if(!(g&1)){e=1;break}xe(a+q|0,b,n,f,e);e=1;break}l=d[i+1>>0]|0;m=h+(e*48|0)+16|0;e=h+(e*48|0)+32|0;f=h+(l*48|0)|0;k=h+(l*48|0)+16|0;l=h+(l*48|0)+32|0;ye(a,b,n,m,e,f,k,l);if((g&3|0)==3){ye(a+q|0,b,n,m,e,f,k,l);e=2;break}if(g&1|0){xe(a+q|0,b,n,m,e);e=2;break}if(!(g&2))e=2;else{xe(a+8+q|0,b,f,k,l);e=2}}else e=1;while(0);j=j>>>e;if(!j)break;else{c=c>>>e;p=p>>>e;i=i+e|0;o=o>>>e;g=g>>>e;a=a+(e<<3)|0}}return}function Lc(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0;y=b+12|0;p=c[y>>2]|0;g=e+16|0;o=c[g>>2]|0;g=c[g+4>>2]|0;i=e+8|0;h=c[i>>2]|0;i=c[i+4>>2]|0;k=e;j=c[k>>2]|0;k=c[k+4>>2]|0;q=e+64|0;m=q;l=c[m>>2]|0;m=c[m+4>>2]|0;u=a+600|0;n=b+4|0;x=b+16|0;w=a+784|0;b=c[u>>2]|0;r=p;if((b|0)>(d|0)){Mc(c[n>>2]|0,r,c[x>>2]|0,o,h,j,l,w,e+90|0);a=c[x>>2]|0;f=(c[y>>2]|0)+(a<<4)|0;c[y>>2]=f;b=c[u>>2]|0;if((d+2|0)<(b|0)){b=cg(l|0,m|0,16)|0;v()|0;t=cg(j|0,k|0,16)|0;v()|0;s=cg(h|0,i|0,16)|0;v()|0;z=cg(o|0,g|0,16)|0;v()|0;Mc(c[n>>2]|0,f,a,z,s,t,b,w,e+106|0);a=c[x>>2]|0;f=(c[y>>2]|0)+(a<<4)|0;c[y>>2]=f;b=c[u>>2]|0;if((d+4|0)<(b|0)){Mc(c[n>>2]|0,f,a,g,i,k,m,w,e+122|0);a=c[x>>2]|0;f=(c[y>>2]|0)+(a<<4)|0;c[y>>2]=f;b=c[u>>2]|0;if((d+6|0)<(b|0)){b=cg(l|0,m|0,48)|0;v()|0;z=cg(j|0,k|0,48)|0;v()|0;t=cg(h|0,i|0,48)|0;v()|0;s=cg(o|0,g|0,48)|0;v()|0;Mc(c[n>>2]|0,f,a,s,t,z,b,w,e+138|0);c[y>>2]=(c[y>>2]|0)+(c[x>>2]<<4);b=c[u>>2]|0}}}}c[y>>2]=p;m=e+48|0;l=c[m>>2]|0;m=c[m+4>>2]|0;o=e+40|0;n=c[o>>2]|0;o=c[o+4>>2]|0;s=e+32|0;p=c[s>>2]|0;s=c[s+4>>2]|0;t=q;q=c[t>>2]|0;t=c[t+4>>2]|0;if((b|0)<=(d|0))return;if(!d){b=0;a=0;f=0}else{b=p&255;a=n&255;f=l&255}Kc(r,c[x>>2]|0,f,a,b,q&255,w,e+90|0);i=c[x>>2]|0;j=(c[y>>2]|0)+(i<<3)|0;c[y>>2]=j;h=cg(l|0,m|0,8)|0;v()|0;g=cg(n|0,o|0,8)|0;v()|0;b=cg(p|0,s|0,8)|0;v()|0;k=cg(q|0,t|0,8)|0;v()|0;a=d+1|0;if((a|0)>=(c[u>>2]|0))return;if(!a){f=0;a=0;b=0}else{f=b&255;a=g&255;b=h&255}Kc(j,i,b,a,f,k&255,w,e+98|0);i=c[x>>2]|0;j=(c[y>>2]|0)+(i<<3)|0;c[y>>2]=j;h=cg(l|0,m|0,16)|0;v()|0;g=cg(n|0,o|0,16)|0;v()|0;b=cg(p|0,s|0,16)|0;v()|0;k=cg(q|0,t|0,16)|0;v()|0;a=d+2|0;if((a|0)>=(c[u>>2]|0))return;if(!a){f=0;a=0;b=0}else{f=b&255;a=g&255;b=h&255}Kc(j,i,b,a,f,k&255,w,e+106|0);i=c[x>>2]|0;j=(c[y>>2]|0)+(i<<3)|0;c[y>>2]=j;h=cg(l|0,m|0,24)|0;v()|0;g=cg(n|0,o|0,24)|0;v()|0;b=cg(p|0,s|0,24)|0;v()|0;k=cg(q|0,t|0,24)|0;v()|0;a=d+3|0;if((a|0)>=(c[u>>2]|0))return;if(!a){f=0;a=0;b=0}else{f=b&255;a=g&255;b=h&255}Kc(j,i,b,a,f,k&255,w,e+114|0);g=c[x>>2]|0;h=(c[y>>2]|0)+(g<<3)|0;c[y>>2]=h;b=d+4|0;if((b|0)>=(c[u>>2]|0))return;if(!b){b=0;a=0;f=0}else{b=s&255;a=o&255;f=m&255}Kc(h,g,f,a,b,t&255,w,e+122|0);i=c[x>>2]|0;j=(c[y>>2]|0)+(i<<3)|0;c[y>>2]=j;h=cg(l|0,m|0,40)|0;v()|0;g=cg(n|0,o|0,40)|0;v()|0;b=cg(p|0,s|0,40)|0;v()|0;k=cg(q|0,t|0,40)|0;v()|0;a=d+5|0;if((a|0)>=(c[u>>2]|0))return;if(!a){f=0;a=0;b=0}else{f=b&255;a=g&255;b=h&255}Kc(j,i,b,a,f,k&255,w,e+130|0);i=c[x>>2]|0;j=(c[y>>2]|0)+(i<<3)|0;c[y>>2]=j;h=cg(l|0,m|0,48)|0;v()|0;g=cg(n|0,o|0,48)|0;v()|0;b=cg(p|0,s|0,48)|0;v()|0;k=cg(q|0,t|0,48)|0;v()|0;a=d+6|0;if((a|0)>=(c[u>>2]|0))return;if(!a){f=0;a=0;b=0}else{f=b&255;a=g&255;b=h&255}Kc(j,i,b,a,f,k&255,w,e+138|0);j=c[x>>2]|0;k=(c[y>>2]|0)+(j<<3)|0;c[y>>2]=k;i=cg(l|0,m|0,56)|0;v()|0;h=cg(n|0,o|0,56)|0;v()|0;a=cg(p|0,s|0,56)|0;v()|0;g=cg(q|0,t|0,56)|0;v()|0;b=d+7|0;if((b|0)>=(c[u>>2]|0))return;if(!b){f=0;a=0;b=0}else{f=a&255;a=h&255;b=i&255}Kc(k,j,b,a,f,g&255,w,e+146|0);c[y>>2]=(c[y>>2]|0)+(c[x>>2]<<3);return}function Mc(a,b,e,f,g,h,i,j,k){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;y=L;L=L+16|0;x=y+8|0;w=y;l=(a|0)!=0;u=l?4:8;a=1<<u;v=a|1;c[x>>2]=b;l=(g|f|h|i)&(l?255:65535);if(!l){L=y;return}q=a^-2;r=w+4|0;s=e<<3;t=x+4|0;while(1){if(l&v|0){m=d[k>>0]|0;n=j+(m*48|0)|0;c[w>>2]=n;o=d[k+u>>0]|0;p=j+(o*48|0)|0;c[r>>2]=p;c[t>>2]=b+s;a=f&v;do if(a|0)if((a|0)==(v|0)){Ke(b,e,n,j+(m*48|0)+16|0,j+(m*48|0)+32|0);break}else{z=f&1^1;a=c[w+(z<<2)>>2]|0;Ie(c[x+(z<<2)>>2]|0,e,a,a+16|0,a+32|0);break}while(0);a=g&v;do if(a|0)if((a|0)==(v|0)){Ee(b,e,n,j+(m*48|0)+16|0,j+(m*48|0)+32|0,p,j+(o*48|0)+16|0,j+(o*48|0)+32|0);break}else{a=g&1^1;z=c[w+(a<<2)>>2]|0;De(c[x+(a<<2)>>2]|0,e,z,z+16|0,z+32|0);break}while(0);a=h&v;do if(a|0)if((a|0)==(v|0)){Ae(b,e,n,j+(m*48|0)+16|0,j+(m*48|0)+32|0,p,j+(o*48|0)+16|0,j+(o*48|0)+32|0);break}else{a=h&1^1;z=c[w+(a<<2)>>2]|0;ze(c[x+(a<<2)>>2]|0,e,z,z+16|0,z+32|0);break}while(0);a=i&v;do if(a|0)if((a|0)==(v|0)){Ae(b+4|0,e,n,j+(m*48|0)+16|0,j+(m*48|0)+32|0,p,j+(o*48|0)+16|0,j+(o*48|0)+32|0);break}else{p=i&1^1;z=c[w+(p<<2)>>2]|0;ze((c[x+(p<<2)>>2]|0)+4|0,e,z,z+16|0,z+32|0);break}while(0)}b=b+8|0;c[x>>2]=b;a=l&q;if(!a)break;else{f=f>>>1;g=g>>>1;h=h>>>1;i=i>>>1;l=a>>>1;k=k+1|0}}L=y;return}function Nc(d,e,f,g){d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;v=L;L=L+16|0;t=v;u=e+12|0;n=c[u>>2]|0;m=b[g+76>>1]|0;i=b[g+74>>1]|0;j=b[g+72>>1]|0;p=g+88|0;k=b[p>>1]|0;r=d+600|0;l=e+4|0;s=e+16|0;q=d+784|0;e=c[r>>2]|0;o=n;if((e|0)>(f|0)){a[t>>0]=a[g+90>>0]|0;a[t+4>>0]=a[g+106>>0]|0;a[t+1>>0]=a[g+92>>0]|0;a[t+5>>0]=a[g+108>>0]|0;a[t+2>>0]=a[g+94>>0]|0;a[t+6>>0]=a[g+110>>0]|0;a[t+3>>0]=a[g+96>>0]|0;a[t+7>>0]=a[g+112>>0]|0;Mc(c[l>>2]|0,o,c[s>>2]|0,m&65535,i&65535,j&65535,k&65535,q,t);d=c[s>>2]|0;h=(c[u>>2]|0)+(d<<4)|0;c[u>>2]=h;e=c[r>>2]|0;if((f+4|0)<(e|0)){e=t+8|0;a[e>>0]=a[g+122>>0]|0;a[t+12>>0]=a[g+138>>0]|0;a[t+9>>0]=a[g+124>>0]|0;a[t+13>>0]=a[g+140>>0]|0;a[t+10>>0]=a[g+126>>0]|0;a[t+14>>0]=a[g+142>>0]|0;a[t+11>>0]=a[g+128>>0]|0;a[t+15>>0]=a[g+144>>0]|0;Mc(c[l>>2]|0,h,d,(m&65535)>>>8&65535,(i&65535)>>>8&65535,(j&65535)>>>8&65535,(k&65535)>>>8&65535,q,e);c[u>>2]=(c[u>>2]|0)+(c[s>>2]<<4);e=c[r>>2]|0}}c[u>>2]=n;l=b[g+84>>1]|0;m=b[g+82>>1]|0;k=b[g+80>>1]|0;j=b[p>>1]|0;if((e|0)<=(f|0)){L=v;return}d=(f|0)==0;Kc(o,c[s>>2]|0,d?0:l&15,d?0:m&15,d?0:k&15,(e+-1|0)==(f|0)?0:j&15,q,t);e=c[s>>2]|0;d=(c[u>>2]|0)+(e<<3)|0;c[u>>2]=d;h=f+2|0;i=c[r>>2]|0;if((h|0)>=(i|0)){L=v;return}p=(h|0)==0;Kc(d,e,p?0:(l&65535)>>>4&15,p?0:(m&65535)>>>4&15,p?0:(k&65535)>>>4&15,(h|0)==(i+-1|0)?0:(j&65535)>>>4&15,q,t+4|0);e=c[s>>2]|0;d=(c[u>>2]|0)+(e<<3)|0;c[u>>2]=d;h=f+4|0;i=c[r>>2]|0;if((h|0)>=(i|0)){L=v;return}p=(h|0)==0;Kc(d,e,p?0:(l&65535)>>>8&15,p?0:(m&65535)>>>8&15,p?0:(k&65535)>>>8&15,(h|0)==(i+-1|0)?0:(j&65535)>>>8&15,q,t+8|0);h=c[s>>2]|0;i=(c[u>>2]|0)+(h<<3)|0;c[u>>2]=i;d=f+6|0;e=c[r>>2]|0;if((d|0)>=(e|0)){L=v;return}r=(d|0)==0;Kc(i,h,r?0:(l&65535)>>>12&65535,r?0:(m&65535)>>>12&65535,r?0:(k&65535)>>>12&65535,(d|0)==(e+-1|0)?0:(j&65535)>>>12&65535,q,t+12|0);c[u>>2]=(c[u>>2]|0)+(c[s>>2]<<3);L=v;return}function Oc(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;r=(g|0)!=0;s=r?1:3;a:do if(r)l=1;else{switch(c[d+96>>2]|0){case 1:{if((c[d+92>>2]|0)==1){l=0;break a}break}case 0:{if(!(c[d+92>>2]|0)){l=1;break a}break}default:{}}l=2}while(0);if((e|0)>=(f|0))return;o=b+736|0;p=b+612|0;m=b+3972|0;n=b+3976|0;q=b+608|0;t=(l&3)==2;g=c[q>>2]|0;do{k=(c[o>>2]|0)+((c[p>>2]|0)*e<<2)|0;if((g|0)>0){i=(c[m>>2]|0)+(((c[n>>2]|0)*(e>>3)|0)*160|0)|0;j=0;while(1){ad(d,a,e,j);Ic(b,e,j,i);Lc(b,d,e,i);if(!r){h=k+(j<<2)|0;switch(l&3){case 0:{g=1;do{Nc(b,d+(g*88|0)|0,e,i);g=g+1|0}while(g>>>0<s>>>0);break}case 1:{g=1;do{Lc(b,d+(g*88|0)|0,e,i);g=g+1|0}while(g>>>0<s>>>0);break}default:{g=1;do{if(t)Jc(b,d+(g*88|0)|0,h,e,j);g=g+1|0}while(g>>>0<s>>>0)}}}j=j+8|0;g=c[q>>2]|0;if((j|0)>=(g|0))break;else i=i+160|0}}e=e+8|0}while((e|0)<(f|0));return}function Pc(f,g,h,i,j,k){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0;C=a[g+2>>0]|0;q=g+8|0;n=a[(c[9456+(d[g+1>>0]<<2)>>2]|0)+(f+3856+(a[g+4>>0]<<3)+(a[q>>0]<<1))>>0]|0;z=d[g>>0]|0;A=C&255;B=a[11280+(z<<4)+(A<<2)+3>>0]|0;m=c[f+3972>>2]|0;l=((c[f+3976>>2]|0)*(h>>3)|0)+(i>>3)|0;r=m+(l*160|0)+(A<<3)|0;s=m+(l*160|0)+32+(A<<3)|0;t=m+(l*160|0)+64|0;u=B&255;w=m+(l*160|0)+72+(u<<1)|0;x=m+(l*160|0)+80+(u<<1)|0;y=m+(l*160|0)+88|0;h=h&7;i=i&7;o=i|h<<3;p=i>>>1|h>>>1<<2;i=a[10016+(h<<3)+i>>0]|0;if(!(n<<24>>24))return;if((k|0)>0){f=o;h=0;while(1){kg(m+(l*160|0)+90+f|0,n|0,j|0)|0;h=h+1|0;if((h|0)==(k|0))break;else f=f+8|0}}l=9520+(z<<3)|0;l=dg(c[l>>2]|0,c[l+4>>2]|0,o|0)|0;m=v()|0;n=s;l=c[n>>2]|l;m=c[n+4>>2]|m;n=s;c[n>>2]=l;c[n+4>>2]=m;n=9632+(z<<3)|0;n=dg(c[n>>2]|0,c[n+4>>2]|0,o|0)|0;j=v()|0;k=r;n=c[k>>2]|n;j=c[k+4>>2]|j;k=r;c[k>>2]=n;c[k+4>>2]=j;i=i<<24>>24!=0;if(i){b[x>>1]=e[9920+(z<<1)>>1]<<p|e[x>>1];b[w>>1]=e[9952+(z<<1)>>1]<<p|e[w>>1]}if(a[g+3>>0]|0?(a[q>>0]|0)>=1:0)return;h=9744+(z<<3)|0;f=c[h>>2]|0;h=c[h+4>>2]|0;k=9856+(A<<3)|0;k=dg(c[k>>2]&f|0,c[k+4>>2]&h|0,o|0)|0;q=v()|0|m;g=s;c[g>>2]=k|l;c[g+4>>2]=q;g=9888+(A<<3)|0;g=dg(c[g>>2]&f|0,c[g+4>>2]&h|0,o|0)|0;s=v()|0|j;A=r;c[A>>2]=g|n;c[A+4>>2]=s;if(i){A=b[9984+(z<<1)>>1]|0;b[x>>1]=(b[30132+(u<<1)>>1]&A&65535)<<p|e[x>>1];b[w>>1]=(b[30140+(u<<1)>>1]&A&65535)<<p|e[w>>1]}if(!(C<<24>>24)){x=dg(f|0,h|0,o|0)|0;A=v()|0;w=t;A=c[w+4>>2]|A;C=t;c[C>>2]=c[w>>2]|x;c[C+4>>2]=A}if(!(B<<24>>24==0&i))return;b[y>>1]=e[9984+(z<<1)>>1]<<p|e[y>>1];return}function Qc(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;c[a>>2]=b;c[a+4>>2]=d;c[a+272>>2]=0;c[a+276>>2]=0;c[a+280>>2]=0;ig(a+8|0,e|0,264)|0;return}function Rc(a){a=a|0;if(!(c[a+3940>>2]|0))return;kg(c[a+3972>>2]|0,0,((c[a+3976>>2]|0)*160|0)*((c[a+600>>2]|0)+7>>3)|0)|0;return}function Sc(a,b){a=a|0;b=b|0;Oc(c[a>>2]|0,c[a+4>>2]|0,a+8|0,c[a+272>>2]|0,c[a+276>>2]|0,c[a+280>>2]|0);return 1}function Tc(a,b,d,e,f,g,h,i,j){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;s=L;L=L+16|0;n=s+8|0;m=s;if(!e){L=s;return}l=b+600|0;k=c[l>>2]|0;if((g|0)!=0&(k|0)>8){g=(k|0)>71?k>>>3:8;r=k>>>1&2147483640}else{g=k;r=0}q=g+r|0;Hc(b,e);o=Mb()|0;l=(c[l>>2]|0)+7>>3;p=1<<c[b+17364>>2];p=(p|0)>(i|0)?i:p;k=j+4|0;g=j+8|0;if(((c[k>>2]|0)!=0?(l|0)==(c[g>>2]|0):0)?(p|0)<=(c[j+16>>2]|0):0){k=j;g=l<<2}else{t=j+12|0;qb(c[t>>2]|0);qb(c[j>>2]|0);c[j>>2]=0;c[j+4>>2]=0;c[j+8>>2]=0;c[j+12>>2]=0;c[j+16>>2]=0;e=c[b+256>>2]|0;c[g>>2]=l;i=ob(p*284|0)|0;c[t>>2]=i;if(!i)ma(b,2,31815,m);c[j+16>>2]=p;g=l<<2;t=ob(g)|0;c[j>>2]=t;if(!t)ma(b,2,31850,n);c[k>>2]=(e|0)<640?1:(e|0)<1281?2:(e|0)<4097?4:8;k=j}kg(c[k>>2]|0,-1,g|0)|0;if((p|0)<=0){L=s;return}k=j+12|0;e=p+-1|0;l=o+16|0;i=o+12|0;g=0;do{m=h+(g*24|0)|0;t=c[k>>2]|0;n=t+(g*284|0)|0;c[h+(g*24|0)+8>>2]=29;c[h+(g*24|0)+12>>2]=j;c[h+(g*24|0)+16>>2]=n;Qc(n,a,b,d);c[t+(g*284|0)+272>>2]=(g<<3)+r;c[t+(g*284|0)+276>>2]=q;c[t+(g*284|0)+280>>2]=f;if((g|0)==(e|0))V[c[l>>2]&15](m);else V[c[i>>2]&15](m);g=g+1|0}while((g|0)<(p|0));k=o+8|0;g=0;do{P[c[k>>2]&7](h+(g*24|0)|0)|0;g=g+1|0}while((g|0)<(p|0));L=s;return}function Uc(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;q=c[b>>2]|0;r=c[b+4>>2]|0;s=b+8|0;d=c[b+272>>2]|0;t=c[b+276>>2]|0;n=(c[b+280>>2]|0)!=0;o=n?1:3;p=r+608|0;a:do if(n)i=1;else{switch(c[b+104>>2]|0){case 1:{if((c[b+100>>2]|0)==1){i=0;break a}break}case 0:{if(!(c[b+100>>2]|0)){i=1;break a}break}default:{}}i=2}while(0);if((d|0)>=(t|0))return 1;l=r+736|0;m=r+612|0;j=r+3972|0;k=r+3976|0;h=a+16|0;b=c[p>>2]|0;do{g=(c[l>>2]|0)+((c[m>>2]|0)*d<<2)|0;b:do if((b|0)>0){a=(c[j>>2]|0)+(((c[k>>2]|0)*(d>>3)|0)*160|0)|0;if(n){e=0;while(1){ad(s,q,d,e);Ic(r,d,e,a);Lc(r,s,d,a);e=e+8|0;b=c[p>>2]|0;if((e|0)>=(b|0))break b;else a=a+160|0}}f=0;while(1){ad(s,q,d,f);Ic(r,d,f,a);Lc(r,s,d,a);e=g+(f<<2)|0;switch(i&3){case 0:{b=1;do{Nc(r,s+(b*88|0)|0,d,a);b=b+1|0}while(b>>>0<o>>>0);break}case 1:{b=1;do{Lc(r,s+(b*88|0)|0,d,a);b=b+1|0}while(b>>>0<o>>>0);break}case 2:{b=1;do{Jc(r,s+(b*88|0)|0,e,d,f);b=b+1|0}while(b>>>0<o>>>0);break}default:{}}f=f+8|0;b=c[p>>2]|0;if((f|0)>=(b|0))break b;else a=a+160|0}}while(0);d=(c[h>>2]<<3)+d|0}while((d|0)<(t|0));return 1}function Vc(a){a=a|0;if(!a)return;qb(c[a+12>>2]|0);qb(c[a>>2]|0);c[a>>2]=0;c[a+4>>2]=0;c[a+8>>2]=0;c[a+12>>2]=0;c[a+16>>2]=0;return}function Wc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0;c[a>>2]=(c[a>>2]|0)+(c[b>>2]|0);e=a+4|0;c[e>>2]=(c[e>>2]|0)+(c[b+4>>2]|0);e=a+8|0;c[e>>2]=(c[e>>2]|0)+(c[b+8>>2]|0);e=a+12|0;c[e>>2]=(c[e>>2]|0)+(c[b+12>>2]|0);e=a+16|0;c[e>>2]=(c[e>>2]|0)+(c[b+16>>2]|0);e=a+20|0;c[e>>2]=(c[e>>2]|0)+(c[b+20>>2]|0);e=a+24|0;c[e>>2]=(c[e>>2]|0)+(c[b+24>>2]|0);e=a+28|0;c[e>>2]=(c[e>>2]|0)+(c[b+28>>2]|0);e=a+32|0;c[e>>2]=(c[e>>2]|0)+(c[b+32>>2]|0);e=a+36|0;c[e>>2]=(c[e>>2]|0)+(c[b+36>>2]|0);e=a+40|0;c[e>>2]=(c[e>>2]|0)+(c[b+40>>2]|0);e=a+44|0;c[e>>2]=(c[e>>2]|0)+(c[b+44>>2]|0);e=a+48|0;c[e>>2]=(c[e>>2]|0)+(c[b+48>>2]|0);e=a+52|0;c[e>>2]=(c[e>>2]|0)+(c[b+52>>2]|0);e=a+56|0;c[e>>2]=(c[e>>2]|0)+(c[b+56>>2]|0);e=a+60|0;c[e>>2]=(c[e>>2]|0)+(c[b+60>>2]|0);e=a+64|0;c[e>>2]=(c[e>>2]|0)+(c[b+64>>2]|0);e=a+68|0;c[e>>2]=(c[e>>2]|0)+(c[b+68>>2]|0);e=a+72|0;c[e>>2]=(c[e>>2]|0)+(c[b+72>>2]|0);e=a+76|0;c[e>>2]=(c[e>>2]|0)+(c[b+76>>2]|0);e=a+80|0;c[e>>2]=(c[e>>2]|0)+(c[b+80>>2]|0);e=a+84|0;c[e>>2]=(c[e>>2]|0)+(c[b+84>>2]|0);e=a+88|0;c[e>>2]=(c[e>>2]|0)+(c[b+88>>2]|0);e=a+92|0;c[e>>2]=(c[e>>2]|0)+(c[b+92>>2]|0);e=a+96|0;c[e>>2]=(c[e>>2]|0)+(c[b+96>>2]|0);e=a+100|0;c[e>>2]=(c[e>>2]|0)+(c[b+100>>2]|0);e=a+104|0;c[e>>2]=(c[e>>2]|0)+(c[b+104>>2]|0);e=a+108|0;c[e>>2]=(c[e>>2]|0)+(c[b+108>>2]|0);e=a+112|0;c[e>>2]=(c[e>>2]|0)+(c[b+112>>2]|0);e=a+116|0;c[e>>2]=(c[e>>2]|0)+(c[b+116>>2]|0);e=a+120|0;c[e>>2]=(c[e>>2]|0)+(c[b+120>>2]|0);e=a+124|0;c[e>>2]=(c[e>>2]|0)+(c[b+124>>2]|0);e=a+128|0;c[e>>2]=(c[e>>2]|0)+(c[b+128>>2]|0);e=a+132|0;c[e>>2]=(c[e>>2]|0)+(c[b+132>>2]|0);e=a+136|0;c[e>>2]=(c[e>>2]|0)+(c[b+136>>2]|0);e=a+140|0;c[e>>2]=(c[e>>2]|0)+(c[b+140>>2]|0);e=a+144|0;c[e>>2]=(c[e>>2]|0)+(c[b+144>>2]|0);e=a+148|0;c[e>>2]=(c[e>>2]|0)+(c[b+148>>2]|0);e=a+152|0;c[e>>2]=(c[e>>2]|0)+(c[b+152>>2]|0);e=a+156|0;c[e>>2]=(c[e>>2]|0)+(c[b+156>>2]|0);e=0;do{g=a+160+(e*40|0)|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)>>2]|0);g=a+160+(e*40|0)+4|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+4>>2]|0);g=a+160+(e*40|0)+8|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+8>>2]|0);g=a+160+(e*40|0)+12|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+12>>2]|0);g=a+160+(e*40|0)+16|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+16>>2]|0);g=a+160+(e*40|0)+20|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+20>>2]|0);g=a+160+(e*40|0)+24|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+24>>2]|0);g=a+160+(e*40|0)+28|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+28>>2]|0);g=a+160+(e*40|0)+32|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+32>>2]|0);g=a+160+(e*40|0)+36|0;c[g>>2]=(c[g>>2]|0)+(c[b+160+(e*40|0)+36>>2]|0);e=e+1|0}while((e|0)!=10);e=0;do{g=a+560+(e<<4)|0;c[g>>2]=(c[g>>2]|0)+(c[b+560+(e<<4)>>2]|0);g=a+560+(e<<4)+4|0;c[g>>2]=(c[g>>2]|0)+(c[b+560+(e<<4)+4>>2]|0);g=a+560+(e<<4)+8|0;c[g>>2]=(c[g>>2]|0)+(c[b+560+(e<<4)+8>>2]|0);g=a+560+(e<<4)+12|0;c[g>>2]=(c[g>>2]|0)+(c[b+560+(e<<4)+12>>2]|0);e=e+1|0}while((e|0)!=16);if(!d){f=0;do{d=0;do{e=0;do{g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+4|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+4>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+8|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+8>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+12|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+12>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+16|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+16>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+20|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+20>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+24|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+24>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+28|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+28>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+32|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+32>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+36|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+36>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+40|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+40>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+44|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+44>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+48|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+48>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+52|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+52>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+56|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+56>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+60|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+60>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+64|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+64>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+68|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+68>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+72|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+72>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+76|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+76>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+80|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+80>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+84|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+84>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+88|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+88>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+92|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+92>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+96|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+96>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+100|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+100>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+104|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+104>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+108|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+108>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+112|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+112>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+116|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+116>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+120|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+120>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+124|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+124>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+128|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+128>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+132|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+132>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+136|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+136>>2]|0);g=a+10032+(f*576|0)+(d*288|0)+(e*144|0)+140|0;c[g>>2]=(c[g>>2]|0)+(c[b+10032+(f*576|0)+(d*288|0)+(e*144|0)+140>>2]|0);e=e+1|0}while((e|0)!=2);d=d+1|0}while((d|0)!=2);f=f+1|0}while((f|0)!=4)}else{g=0;do{f=0;do{d=0;do{e=0;do{h=a+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)|0;c[h>>2]=(c[h>>2]|0)+(c[b+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+4|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+4>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+8|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+8>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+12|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+12>>2]|0);h=a+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+4|0;c[h>>2]=(c[h>>2]|0)+(c[b+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+4>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+16|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+16>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+20|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+20>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+24|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+24>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+28|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+28>>2]|0);h=a+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+8|0;c[h>>2]=(c[h>>2]|0)+(c[b+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+8>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+32|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+32>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+36|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+36>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+40|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+40>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+44|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+44>>2]|0);h=a+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+12|0;c[h>>2]=(c[h>>2]|0)+(c[b+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+12>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+48|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+48>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+52|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+52>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+56|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+56>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+60|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+60>>2]|0);h=a+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+16|0;c[h>>2]=(c[h>>2]|0)+(c[b+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+16>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+64|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+64>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+68|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+68>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+72|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+72>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+76|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+76>>2]|0);h=a+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+20|0;c[h>>2]=(c[h>>2]|0)+(c[b+10032+(g*576|0)+(f*288|0)+(d*144|0)+(e*24|0)+20>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+80|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+80>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+84|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+84>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+88|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+88>>2]|0);h=a+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+92|0;c[h>>2]=(c[h>>2]|0)+(c[b+816+(g*2304|0)+(f*1152|0)+(d*576|0)+(e*96|0)+92>>2]|0);e=e+1|0}while((e|0)!=6);d=d+1|0}while((d|0)!=2);f=f+1|0}while((f|0)!=2);g=g+1|0}while((g|0)!=4)}e=a+12336|0;c[e>>2]=(c[e>>2]|0)+(c[b+12336>>2]|0);e=a+12340|0;c[e>>2]=(c[e>>2]|0)+(c[b+12340>>2]|0);e=a+12344|0;c[e>>2]=(c[e>>2]|0)+(c[b+12344>>2]|0);e=a+12348|0;c[e>>2]=(c[e>>2]|0)+(c[b+12348>>2]|0);e=a+12352|0;c[e>>2]=(c[e>>2]|0)+(c[b+12352>>2]|0);e=a+12356|0;c[e>>2]=(c[e>>2]|0)+(c[b+12356>>2]|0);e=a+12360|0;c[e>>2]=(c[e>>2]|0)+(c[b+12360>>2]|0);e=a+12364|0;c[e>>2]=(c[e>>2]|0)+(c[b+12364>>2]|0);e=a+12368|0;c[e>>2]=(c[e>>2]|0)+(c[b+12368>>2]|0);e=a+12372|0;c[e>>2]=(c[e>>2]|0)+(c[b+12372>>2]|0);e=a+12376|0;c[e>>2]=(c[e>>2]|0)+(c[b+12376>>2]|0);e=a+12380|0;c[e>>2]=(c[e>>2]|0)+(c[b+12380>>2]|0);e=a+12384|0;c[e>>2]=(c[e>>2]|0)+(c[b+12384>>2]|0);e=a+12388|0;c[e>>2]=(c[e>>2]|0)+(c[b+12388>>2]|0);e=a+12392|0;c[e>>2]=(c[e>>2]|0)+(c[b+12392>>2]|0);e=a+12396|0;c[e>>2]=(c[e>>2]|0)+(c[b+12396>>2]|0);e=a+12400|0;c[e>>2]=(c[e>>2]|0)+(c[b+12400>>2]|0);e=a+12404|0;c[e>>2]=(c[e>>2]|0)+(c[b+12404>>2]|0);e=a+12408|0;c[e>>2]=(c[e>>2]|0)+(c[b+12408>>2]|0);e=a+12412|0;c[e>>2]=(c[e>>2]|0)+(c[b+12412>>2]|0);e=a+12416|0;c[e>>2]=(c[e>>2]|0)+(c[b+12416>>2]|0);e=a+12420|0;c[e>>2]=(c[e>>2]|0)+(c[b+12420>>2]|0);e=a+12424|0;c[e>>2]=(c[e>>2]|0)+(c[b+12424>>2]|0);e=a+12428|0;c[e>>2]=(c[e>>2]|0)+(c[b+12428>>2]|0);e=a+12432|0;c[e>>2]=(c[e>>2]|0)+(c[b+12432>>2]|0);e=a+12436|0;c[e>>2]=(c[e>>2]|0)+(c[b+12436>>2]|0);e=a+12440|0;c[e>>2]=(c[e>>2]|0)+(c[b+12440>>2]|0);e=a+12444|0;c[e>>2]=(c[e>>2]|0)+(c[b+12444>>2]|0);e=a+12448|0;c[e>>2]=(c[e>>2]|0)+(c[b+12448>>2]|0);e=a+12452|0;c[e>>2]=(c[e>>2]|0)+(c[b+12452>>2]|0);e=a+12456|0;c[e>>2]=(c[e>>2]|0)+(c[b+12456>>2]|0);e=a+12460|0;c[e>>2]=(c[e>>2]|0)+(c[b+12460>>2]|0);e=a+12464|0;c[e>>2]=(c[e>>2]|0)+(c[b+12464>>2]|0);e=a+12468|0;c[e>>2]=(c[e>>2]|0)+(c[b+12468>>2]|0);e=a+12472|0;c[e>>2]=(c[e>>2]|0)+(c[b+12472>>2]|0);e=a+12476|0;c[e>>2]=(c[e>>2]|0)+(c[b+12476>>2]|0);e=a+12480|0;c[e>>2]=(c[e>>2]|0)+(c[b+12480>>2]|0);e=a+12484|0;c[e>>2]=(c[e>>2]|0)+(c[b+12484>>2]|0);e=a+12488|0;c[e>>2]=(c[e>>2]|0)+(c[b+12488>>2]|0);e=a+12492|0;c[e>>2]=(c[e>>2]|0)+(c[b+12492>>2]|0);e=a+12496|0;c[e>>2]=(c[e>>2]|0)+(c[b+12496>>2]|0);e=a+12500|0;c[e>>2]=(c[e>>2]|0)+(c[b+12500>>2]|0);e=a+12504|0;c[e>>2]=(c[e>>2]|0)+(c[b+12504>>2]|0);e=a+12508|0;c[e>>2]=(c[e>>2]|0)+(c[b+12508>>2]|0);e=a+12512|0;c[e>>2]=(c[e>>2]|0)+(c[b+12512>>2]|0);e=a+12516|0;c[e>>2]=(c[e>>2]|0)+(c[b+12516>>2]|0);e=a+12520|0;c[e>>2]=(c[e>>2]|0)+(c[b+12520>>2]|0);e=a+12524|0;c[e>>2]=(c[e>>2]|0)+(c[b+12524>>2]|0);e=a+12528|0;c[e>>2]=(c[e>>2]|0)+(c[b+12528>>2]|0);e=a+12532|0;c[e>>2]=(c[e>>2]|0)+(c[b+12532>>2]|0);e=a+12536|0;c[e>>2]=(c[e>>2]|0)+(c[b+12536>>2]|0);e=a+12540|0;c[e>>2]=(c[e>>2]|0)+(c[b+12540>>2]|0);e=a+12544|0;c[e>>2]=(c[e>>2]|0)+(c[b+12544>>2]|0);e=a+12548|0;c[e>>2]=(c[e>>2]|0)+(c[b+12548>>2]|0);e=a+12552|0;c[e>>2]=(c[e>>2]|0)+(c[b+12552>>2]|0);e=a+12556|0;c[e>>2]=(c[e>>2]|0)+(c[b+12556>>2]|0);e=a+12560|0;c[e>>2]=(c[e>>2]|0)+(c[b+12560>>2]|0);e=a+12564|0;c[e>>2]=(c[e>>2]|0)+(c[b+12564>>2]|0);e=a+12568|0;c[e>>2]=(c[e>>2]|0)+(c[b+12568>>2]|0);e=a+12572|0;c[e>>2]=(c[e>>2]|0)+(c[b+12572>>2]|0);e=a+12576|0;c[e>>2]=(c[e>>2]|0)+(c[b+12576>>2]|0);e=a+12580|0;c[e>>2]=(c[e>>2]|0)+(c[b+12580>>2]|0);e=a+12584|0;c[e>>2]=(c[e>>2]|0)+(c[b+12584>>2]|0);e=a+12588|0;c[e>>2]=(c[e>>2]|0)+(c[b+12588>>2]|0);e=a+12592|0;c[e>>2]=(c[e>>2]|0)+(c[b+12592>>2]|0);e=a+12596|0;c[e>>2]=(c[e>>2]|0)+(c[b+12596>>2]|0);e=a+12600|0;c[e>>2]=(c[e>>2]|0)+(c[b+12600>>2]|0);e=a+12604|0;c[e>>2]=(c[e>>2]|0)+(c[b+12604>>2]|0);e=a+12608|0;c[e>>2]=(c[e>>2]|0)+(c[b+12608>>2]|0);e=a+12612|0;c[e>>2]=(c[e>>2]|0)+(c[b+12612>>2]|0);e=a+12616|0;c[e>>2]=(c[e>>2]|0)+(c[b+12616>>2]|0);e=a+12620|0;c[e>>2]=(c[e>>2]|0)+(c[b+12620>>2]|0);e=a+12624|0;c[e>>2]=(c[e>>2]|0)+(c[b+12624>>2]|0);e=a+12628|0;c[e>>2]=(c[e>>2]|0)+(c[b+12628>>2]|0);e=a+12632|0;c[e>>2]=(c[e>>2]|0)+(c[b+12632>>2]|0);e=a+12636|0;c[e>>2]=(c[e>>2]|0)+(c[b+12636>>2]|0);e=a+12640|0;c[e>>2]=(c[e>>2]|0)+(c[b+12640>>2]|0);e=a+12644|0;c[e>>2]=(c[e>>2]|0)+(c[b+12644>>2]|0);e=a+12648|0;c[e>>2]=(c[e>>2]|0)+(c[b+12648>>2]|0);e=a+12652|0;c[e>>2]=(c[e>>2]|0)+(c[b+12652>>2]|0);e=a+12656|0;c[e>>2]=(c[e>>2]|0)+(c[b+12656>>2]|0);e=a+12660|0;c[e>>2]=(c[e>>2]|0)+(c[b+12660>>2]|0);e=a+12664|0;c[e>>2]=(c[e>>2]|0)+(c[b+12664>>2]|0);e=a+12668|0;c[e>>2]=(c[e>>2]|0)+(c[b+12668>>2]|0);e=a+12672|0;c[e>>2]=(c[e>>2]|0)+(c[b+12672>>2]|0);e=a+12676|0;c[e>>2]=(c[e>>2]|0)+(c[b+12676>>2]|0);e=a+12680|0;c[e>>2]=(c[e>>2]|0)+(c[b+12680>>2]|0);e=a+12684|0;c[e>>2]=(c[e>>2]|0)+(c[b+12684>>2]|0);e=a+12688|0;c[e>>2]=(c[e>>2]|0)+(c[b+12688>>2]|0);e=a+12692|0;c[e>>2]=(c[e>>2]|0)+(c[b+12692>>2]|0);e=a+12696|0;c[e>>2]=(c[e>>2]|0)+(c[b+12696>>2]|0);e=a+12700|0;c[e>>2]=(c[e>>2]|0)+(c[b+12700>>2]|0);e=a+12720|0;c[e>>2]=(c[e>>2]|0)+(c[b+12720>>2]|0);e=a+12724|0;c[e>>2]=(c[e>>2]|0)+(c[b+12724>>2]|0);e=a+12728|0;c[e>>2]=(c[e>>2]|0)+(c[b+12728>>2]|0);e=a+12744|0;c[e>>2]=(c[e>>2]|0)+(c[b+12744>>2]|0);e=a+12748|0;c[e>>2]=(c[e>>2]|0)+(c[b+12748>>2]|0);e=a+12704|0;c[e>>2]=(c[e>>2]|0)+(c[b+12704>>2]|0);e=a+12708|0;c[e>>2]=(c[e>>2]|0)+(c[b+12708>>2]|0);e=a+12712|0;c[e>>2]=(c[e>>2]|0)+(c[b+12712>>2]|0);e=a+12716|0;c[e>>2]=(c[e>>2]|0)+(c[b+12716>>2]|0);e=a+12732|0;c[e>>2]=(c[e>>2]|0)+(c[b+12732>>2]|0);e=a+12736|0;c[e>>2]=(c[e>>2]|0)+(c[b+12736>>2]|0);e=a+12740|0;c[e>>2]=(c[e>>2]|0)+(c[b+12740>>2]|0);e=a+12752|0;c[e>>2]=(c[e>>2]|0)+(c[b+12752>>2]|0);e=a+12756|0;c[e>>2]=(c[e>>2]|0)+(c[b+12756>>2]|0);e=a+12760|0;c[e>>2]=(c[e>>2]|0)+(c[b+12760>>2]|0);e=a+12764|0;c[e>>2]=(c[e>>2]|0)+(c[b+12764>>2]|0);e=a+12768|0;c[e>>2]=(c[e>>2]|0)+(c[b+12768>>2]|0);e=a+12772|0;c[e>>2]=(c[e>>2]|0)+(c[b+12772>>2]|0);e=a+12776|0;c[e>>2]=(c[e>>2]|0)+(c[b+12776>>2]|0);e=a+12780|0;c[e>>2]=(c[e>>2]|0)+(c[b+12780>>2]|0);e=a+12784|0;c[e>>2]=(c[e>>2]|0)+(c[b+12784>>2]|0);e=a+12788|0;c[e>>2]=(c[e>>2]|0)+(c[b+12788>>2]|0);e=a+12792|0;c[e>>2]=(c[e>>2]|0)+(c[b+12792>>2]|0);e=a+12796|0;c[e>>2]=(c[e>>2]|0)+(c[b+12796>>2]|0);e=a+12800|0;c[e>>2]=(c[e>>2]|0)+(c[b+12800>>2]|0);e=a+12804|0;c[e>>2]=(c[e>>2]|0)+(c[b+12804>>2]|0);e=a+12808|0;c[e>>2]=(c[e>>2]|0)+(c[b+12808>>2]|0);e=a+12812|0;c[e>>2]=(c[e>>2]|0)+(c[b+12812>>2]|0);e=0;do{h=a+12816+(e*204|0)|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)>>2]|0);h=a+12816+(e*204|0)+188|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+188>>2]|0);h=a+12816+(e*204|0)+196|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+196>>2]|0);h=a+12816+(e*204|0)+4|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+4>>2]|0);h=a+12816+(e*204|0)+192|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+192>>2]|0);h=a+12816+(e*204|0)+200|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+200>>2]|0);h=a+12816+(e*204|0)+8|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+8>>2]|0);h=a+12816+(e*204|0)+12|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+12>>2]|0);h=a+12816+(e*204|0)+16|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+16>>2]|0);h=a+12816+(e*204|0)+20|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+20>>2]|0);h=a+12816+(e*204|0)+24|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+24>>2]|0);h=a+12816+(e*204|0)+28|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+28>>2]|0);h=a+12816+(e*204|0)+32|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+32>>2]|0);h=a+12816+(e*204|0)+36|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+36>>2]|0);h=a+12816+(e*204|0)+40|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+40>>2]|0);h=a+12816+(e*204|0)+44|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+44>>2]|0);h=a+12816+(e*204|0)+48|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+48>>2]|0);h=a+12816+(e*204|0)+52|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+52>>2]|0);h=a+12816+(e*204|0)+140|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+140>>2]|0);h=a+12816+(e*204|0)+144|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+144>>2]|0);h=a+12816+(e*204|0)+148|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+148>>2]|0);h=a+12816+(e*204|0)+152|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+152>>2]|0);h=a+12816+(e*204|0)+56|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+56>>2]|0);h=a+12816+(e*204|0)+156|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+156>>2]|0);h=a+12816+(e*204|0)+160|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+160>>2]|0);h=a+12816+(e*204|0)+164|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+164>>2]|0);h=a+12816+(e*204|0)+168|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+168>>2]|0);h=a+12816+(e*204|0)+60|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+60>>2]|0);h=a+12816+(e*204|0)+64|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+64>>2]|0);h=a+12816+(e*204|0)+68|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+68>>2]|0);h=a+12816+(e*204|0)+72|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+72>>2]|0);h=a+12816+(e*204|0)+76|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+76>>2]|0);h=a+12816+(e*204|0)+80|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+80>>2]|0);h=a+12816+(e*204|0)+84|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+84>>2]|0);h=a+12816+(e*204|0)+88|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+88>>2]|0);h=a+12816+(e*204|0)+92|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+92>>2]|0);h=a+12816+(e*204|0)+96|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+96>>2]|0);h=a+12816+(e*204|0)+100|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+100>>2]|0);h=a+12816+(e*204|0)+104|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+104>>2]|0);h=a+12816+(e*204|0)+108|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+108>>2]|0);h=a+12816+(e*204|0)+112|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+112>>2]|0);h=a+12816+(e*204|0)+116|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+116>>2]|0);h=a+12816+(e*204|0)+120|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+120>>2]|0);h=a+12816+(e*204|0)+124|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+124>>2]|0);h=a+12816+(e*204|0)+128|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+128>>2]|0);h=a+12816+(e*204|0)+132|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+132>>2]|0);h=a+12816+(e*204|0)+136|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+136>>2]|0);h=a+12816+(e*204|0)+172|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+172>>2]|0);h=a+12816+(e*204|0)+176|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+176>>2]|0);h=a+12816+(e*204|0)+180|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+180>>2]|0);h=a+12816+(e*204|0)+184|0;c[h>>2]=(c[h>>2]|0)+(c[b+12816+(e*204|0)+184>>2]|0);e=e+1|0}while((e|0)!=2);return}function Xc(a,c,d){a=a|0;c=c|0;d=d|0;d=c+a|0;d=(d|0)<255?d:255;return b[10080+(((d|0)>0?d:0)<<1)>>1]|0}function Yc(a,c,d){a=a|0;c=c|0;d=d|0;d=c+a|0;d=(d|0)<255?d:255;return b[10592+(((d|0)>0?d:0)<<1)>>1]|0}function Zc(d,e,f){d=d|0;e=e|0;f=f|0;if(!(a[d>>0]|0))return f|0;if(!(c[d+80+(e<<2)>>2]&1))return f|0;f=((a[d+3>>0]|0)==1?0:f)+(b[d+16+(e<<3)>>1]|0)|0;f=(f|0)<255?f:255;f=(f|0)>0?f:0;return f|0}function _c(a,d,e,f,g,h,i){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0;j=f<<4;k=g<<4;f=1-i|0;i=1-h|0;g=-64-j+(c[d+316>>2]<<i)|0;h=j+48+(c[d+320>>2]<<i)|0;j=-64-k+(c[d+324>>2]<<f)|0;d=k+48+(c[d+328>>2]<<f)|0;i=b[e+2>>1]<<i<<16>>16;f=b[e>>1]<<f<<16>>16;i=((f|0)<(j|0)?j:(f|0)>(d|0)?d:f)&65535|((i|0)<(g|0)?g:(i|0)>(h|0)?h:i)<<16;b[a>>1]=i;b[a+2>>1]=i>>>16;return}function $c(a,d,f,g,h){a=a|0;d=d|0;f=f|0;g=g|0;h=h|0;var i=0,j=0;switch((((c[d+4>>2]|0)>0&1)<<1|(c[d+8>>2]|0)>0)&3){case 0:{h=f+20+(h*12|0)+4+(g<<2)|0;h=e[h>>1]|e[h+2>>1]<<16;b[a>>1]=h;b[a+2>>1]=h>>>16;return}case 1:{d=f+20+(h*12|0)+4+(g<<2)|0;g=f+20+((h+2|0)*12|0)+4+(g<<2)|0;h=(b[g>>1]|0)+(b[d>>1]|0)|0;g=(b[g+2>>1]|0)+(b[d+2>>1]|0)|0;h=(((g>>31|1)+g|0)/2|0)<<16|(((h>>31|1)+h|0)/2|0)&65535;b[a>>1]=h;b[a+2>>1]=h>>>16;return}case 2:{d=f+20+(h*12|0)+4+(g<<2)|0;g=f+20+((h+1|0)*12|0)+4+(g<<2)|0;h=(b[g>>1]|0)+(b[d>>1]|0)|0;g=(b[g+2>>1]|0)+(b[d+2>>1]|0)|0;h=(((g>>31|1)+g|0)/2|0)<<16|(((h>>31|1)+h|0)/2|0)&65535;b[a>>1]=h;b[a+2>>1]=h>>>16;return}case 3:{i=f+24+(g<<2)|0;j=f+36+(g<<2)|0;d=f+48+(g<<2)|0;g=f+60+(g<<2)|0;h=(b[j>>1]|0)+(b[i>>1]|0)+(b[d>>1]|0)+(b[g>>1]|0)|0;g=(b[j+2>>1]|0)+(b[i+2>>1]|0)+(b[d+2>>1]|0)+(b[g+2>>1]|0)|0;h=(((g>>31&-4|2)+g|0)/4|0)<<16|(((h>>31&-4|2)+h|0)/4|0)&65535;b[a>>1]=h;b[a+2>>1]=h>>>16;return}default:{}}}function ad(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=c[b+56>>2]|0;g=c[b+60>>2]|0;i=c[b+16>>2]|0;f=c[b+36>>2]|0;e=e<<3;d=d<<3;c[a+12>>2]=(c[b+52>>2]|0)+(((d>>c[a+8>>2])*i|0)+(e>>c[a+4>>2]));c[a+16>>2]=i;c[a+100>>2]=h+(((d>>c[a+96>>2])*f|0)+(e>>c[a+92>>2]));c[a+104>>2]=f;c[a+188>>2]=g+(((d>>c[a+184>>2])*f|0)+(e>>c[a+180>>2]));c[a+192>>2]=f;return}function bd(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;if(!d)return;m=c[d+52>>2]|0;k=c[d+56>>2]|0;o=c[d+60>>2]|0;l=c[d+16>>2]|0;n=c[d+36>>2]|0;h=f<<3;i=e<<3;d=g+16|0;j=g+20|0;f=h>>c[a+4>>2];e=i>>c[a+8>>2];if(!g){c[a+20+(b<<3)>>2]=m+((e*l|0)+f);c[a+20+(b<<3)+4>>2]=l;c[a+108+(b<<3)>>2]=k+(((i>>c[a+96>>2])*n|0)+(h>>c[a+92>>2]));c[a+108+(b<<3)+4>>2]=n;f=h>>c[a+180>>2];d=i>>c[a+184>>2]}else{f=Q[c[d>>2]&31](f,g)|0;c[a+20+(b<<3)>>2]=m+(((Q[c[j>>2]&31](e,g)|0)*l|0)+f);c[a+20+(b<<3)+4>>2]=l;f=i>>c[a+96>>2];m=Q[c[d>>2]&31](h>>c[a+92>>2],g)|0;c[a+108+(b<<3)>>2]=k+(((Q[c[j>>2]&31](f,g)|0)*n|0)+m);c[a+108+(b<<3)+4>>2]=n;m=i>>c[a+184>>2];f=Q[c[d>>2]&31](h>>c[a+180>>2],g)|0;d=Q[c[j>>2]&31](m,g)|0}c[a+196+(b<<3)>>2]=o+((d*n|0)+f);c[a+196+(b<<3)+4>>2]=n;return}function cd(b,d,e,f,g,h,i,j,k,l,m){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0;A=L;L=L+112|0;y=A+80|0;x=A;z=e&255;if(!l)o=(c[b+300>>2]|0)!=0&1;else o=1;if(!k)w=(c[b+296>>2]|0)!=0&1;else w=1;r=((1<<z)+k|0)<(1<<d|0);u=x+16|0;t=4<<z;p=(m|0)==0;n=c[b+344>>2]|0;s=c[(p?n:n+20|0)>>2]|0;n=c[(p?n+4|0:n+24|0)>>2]|0;p=(0-(c[b+316>>2]|0)>>(c[b+(m*88|0)+4>>2]|0)+3)+(k<<2)|0;d=(0-(c[b+324>>2]|0)>>(c[b+(m*88|0)+8>>2]|0)+3)+(l<<2)|0;v=f&255;a:do if(757>>>v&1|0){if(!w){kg(y|0,-127,t|0)|0;break}if((c[b+328>>2]|0)>=0){if((t|0)<=0)break;d=0;while(1){a[y+d>>0]=a[g+((d*h|0)+-1)>>0]|0;d=d+1|0;if((d|0)==(t|0))break a}}if((d+t|0)<=(n|0)){if((t|0)<=0)break;d=0;while(1){a[y+d>>0]=a[g+((d*h|0)+-1)>>0]|0;d=d+1|0;if((d|0)==(t|0))break a}}k=n-d|0;if((k|0)>0){d=0;do{a[y+d>>0]=a[g+((d*h|0)+-1)>>0]|0;d=d+1|0}while((d|0)!=(k|0));d=k}else d=0;if((t|0)>(d|0))kg(y+d|0,a[g+(((k+-1|0)*h|0)+-1)>>0]|0,t-d|0)|0}while(0);if(!(627>>>v&1))d=u;else{do if(!o){kg(u|0,127,t|0)|0;d=u;k=127}else{m=g+(0-h)|0;do if((c[b+320>>2]|0)<0){d=p+t|0;if((d|0)<=(s|0)){ig(u|0,m|0,t|0)|0;q=30;break}k=s-p|0;if((s|0)<(p|0))q=30;else{ig(u|0,m|0,k|0)|0;kg(u+k|0,a[u+(k+-1)>>0]|0,d-s|0)|0;q=30}}else if(e<<24>>24==0&r&(w|0)!=0)d=m;else{ig(u|0,m|0,t|0)|0;q=30}while(0);if((q|0)==30)if(!w){d=u;k=-127;break}else d=u;k=a[m+-1>>0]|0}while(0);a[x+15>>0]=k}switch(f<<24>>24){case 3:case 8:{q=35;break}case 0:{X[c[32256+(w<<5)+(o<<4)+(z<<2)>>2]&63](i,j,d,y);L=A;return}default:{}}if((q|0)==35){do if(!o){kg(u|0,127,t<<1|0)|0;k=127}else{l=g+(0-h)|0;do if((c[b+320>>2]|0)<0){k=t<<1;m=p+k|0;if((m|0)<=(s|0))if(e<<24>>24==0&r){ig(u|0,l|0,k|0)|0;q=51;break}else{ig(u|0,l|0,t|0)|0;kg(u+t|0,a[u+(t+-1)>>0]|0,t|0)|0;q=51;break}if((p+t|0)>(s|0)){k=s-p|0;if((s|0)<(p|0)){q=51;break}ig(u|0,l|0,k|0)|0;kg(u+k|0,a[u+(k+-1)>>0]|0,m-s|0)|0;q=51;break}k=s-p|0;if(e<<24>>24==0&r){ig(u|0,l|0,k|0)|0;kg(u+k|0,a[u+(k+-1)>>0]|0,m-s|0)|0;q=51;break}else{ig(u|0,l|0,t|0)|0;kg(u+t|0,a[u+(t+-1)>>0]|0,t|0)|0;q=51;break}}else{m=e<<24>>24==0&r;if(!(m&(w|0)!=0)){ig(u|0,l|0,t|0)|0;k=u+t|0;if(m){ig(k|0,l+t|0,t|0)|0;q=51;break}else{kg(k|0,a[u+(t+-1)>>0]|0,t|0)|0;q=51;break}}else d=l}while(0);if((q|0)==51)if(!w){k=-127;break}k=a[l+-1>>0]|0}while(0);a[x+15>>0]=k}X[c[32320+(v<<4)+(z<<2)>>2]&63](i,j,d,y);L=A;return}function dd(){if(c[8133]|0)return;c[8084]=4;c[8085]=5;c[8086]=6;c[8087]=7;c[8088]=8;c[8089]=9;c[8090]=10;c[8091]=11;c[8108]=12;c[8109]=13;c[8110]=14;c[8111]=15;c[8092]=16;c[8093]=17;c[8094]=18;c[8095]=19;c[8112]=20;c[8113]=21;c[8114]=22;c[8115]=23;c[8100]=24;c[8101]=25;c[8102]=26;c[8103]=27;c[8096]=28;c[8097]=29;c[8098]=30;c[8099]=31;c[8104]=32;c[8105]=33;c[8106]=34;c[8107]=35;c[8116]=36;c[8117]=37;c[8118]=38;c[8119]=39;c[8064]=40;c[8065]=41;c[8066]=42;c[8067]=43;c[8068]=44;c[8069]=45;c[8070]=46;c[8071]=47;c[8072]=48;c[8073]=49;c[8074]=50;c[8075]=51;c[8076]=52;c[8077]=53;c[8078]=54;c[8079]=55;c[8133]=1;return}function ed(e,f,g,h,i,j){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0;W=L;L=L+32|0;E=W;N=W+24|0;O=W+16|0;P=W+8|0;U=e+12|0;Q=e+13280|0;R=f+432|0;M=e+13572|0;V=c[c[M>>2]>>2]|0;k=c[(c[f+728>>2]|0)+4>>2]|0;T=f+1040|0;q=c[T>>2]|0;if(c[f+996>>2]|0?(a[f+1012>>0]|0)==0:0){v=q*g|0;S=k+(v*12|0)+(h*12|0)|0;v=v+h|0;B=f+4412|0;a:do if(a[B>>0]|0){p=c[f+1200>>2]|0;o=(p|0)==0;if(!o)if((i|0)>0&(j|0)>0){l=0;k=2147483647;do{n=(l*q|0)+v|0;m=0;do{J=d[p+(n+m)>>0]|0;k=(k|0)<(J|0)?k:J;m=m+1|0}while((m|0)!=(i|0));l=l+1|0}while((l|0)!=(j|0))}else k=2147483647;else k=0;if(!(a[f+4413>>0]|0)){n=c[f+1204>>2]|0;if(!((i|0)>0&(j|0)>0))break;if(o){m=0;while(1){l=0;do{a[n+(l+v+((c[T>>2]|0)*m|0))>>0]=0;l=l+1|0}while((l|0)!=(i|0));m=m+1|0;if((m|0)==(j|0))break a}}else{m=0;while(1){l=0;do{J=l+v+((c[T>>2]|0)*m|0)|0;a[n+J>>0]=a[p+J>>0]|0;l=l+1|0}while((l|0)!=(i|0));m=m+1|0;if((m|0)==(j|0))break a}}}if(a[f+4416>>0]|0){m=c[e+13576>>2]|0;l=c[e+13580>>2]|0;if(!l)n=0;else n=a[l+5>>0]|0;if(!m)l=0;else l=a[m+5>>0]|0;m=d[l+n+(f+4424)>>0]|0;r=e+16|0;m=(256-m+((c[r>>2]|0)*m|0)|0)>>>8;s=e+20|0;l=c[s>>2]|0;if((l|0)<0){zb(U);l=c[s>>2]|0}n=c[U>>2]|0;o=m<<24;if(n>>>0<o>>>0)p=0;else{p=1;n=n-o|0;m=(c[r>>2]|0)-m|0}J=d[1664+m>>0]|0;o=m<<J;m=n<<J;l=l-J|0;c[U>>2]=m;c[s>>2]=l;c[r>>2]=o;a[V+5>>0]=p;if(!p){k=0;do{q=k<<24>>24;k=d[(q>>1)+(f+4417)>>0]|0;k=(256-k+(o*k|0)|0)>>>8;if((l|0)<0){zb(U);m=c[U>>2]|0;n=c[s>>2]|0}else n=l;l=k<<24;if(m>>>0<l>>>0){p=0;l=m}else{p=1;l=m-l|0;k=(c[r>>2]|0)-k|0}J=d[1664+k>>0]|0;o=k<<J;m=l<<J;l=n-J|0;c[U>>2]=m;c[s>>2]=l;c[r>>2]=o;k=a[31801+(p+q)>>0]|0}while(k<<24>>24>0);k=0-(k<<24>>24)|0}}else{q=e+16|0;r=e+20|0;k=0;m=c[q>>2]|0;n=c[r>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(f+4417)>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[r>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[q>>2]|0)-k|0}J=d[1664+k>>0]|0;m=k<<J;n=n-J|0;c[U>>2]=l<<J;c[r>>2]=n;c[q>>2]=m;k=a[31801+(o+p)>>0]|0}while(k<<24>>24>0);k=0-(k<<24>>24)|0}if((j|0)>0?(t=k&255,u=f+1204|0,(i|0)>0):0){m=0;do{l=0;do{a[(c[u>>2]|0)+(l+v+((c[T>>2]|0)*m|0))>>0]=t;l=l+1|0}while((l|0)!=(i|0));m=m+1|0}while((m|0)!=(j|0))}}else k=0;while(0);x=V+4|0;a[x>>0]=k;if((a[B>>0]|0)!=0?(c[f+4492+(k<<24>>24<<2)>>2]&8|0)!=0:0)k=1;else{l=c[e+13576>>2]|0;k=c[e+13580>>2]|0;if(!k)m=0;else m=a[k+3>>0]|0;if(!l)k=0;else k=a[l+3>>0]|0;p=k+m|0;l=d[(c[f+4536>>2]|0)+1967+p>>0]|0;q=e+16|0;l=(256-l+((c[q>>2]|0)*l|0)|0)>>>8;r=e+20|0;k=c[r>>2]|0;if((k|0)<0){zb(U);o=c[r>>2]|0}else o=k;m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)k=0;else{k=1;m=m-n|0;l=(c[q>>2]|0)-l|0}J=d[1664+l>>0]|0;c[U>>2]=m<<J;c[r>>2]=o-J;c[q>>2]=l<<J;J=c[e+13548>>2]|0;l=J+12776+(p<<3)+(k<<2)|0;if(J)c[l>>2]=(c[l>>2]|0)+1}s=V+3|0;a[s>>0]=k;k=a[x>>0]|0;if((a[B>>0]|0)!=0?(c[f+4492+(k<<2)>>2]&4|0)!=0:0)k=(b[f+4428+(k<<3)+4>>1]|0)!=0&1;else{k=c[e+13576>>2]|0;l=c[e+13580>>2]|0;m=(l|0)!=0;n=(k|0)!=0;if(!(n&m))if(n|m)p=((a[(m?l:k)+8>>0]|0)<1&1)<<1;else p=0;else{J=(a[l+8>>0]|0)>0;p=(a[k+8>>0]|0)>0;p=J|p?(J&p^1)&1:3}l=d[(c[f+4536>>2]|0)+1931+p>>0]|0;q=e+16|0;l=(256-l+((c[q>>2]|0)*l|0)|0)>>>8;r=e+20|0;k=c[r>>2]|0;if((k|0)<0){zb(U);o=c[r>>2]|0}else o=k;m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)k=0;else{k=1;m=m-n|0;l=(c[q>>2]|0)-l|0}J=d[1664+l>>0]|0;c[U>>2]=m<<J;c[r>>2]=o-J;c[q>>2]=l<<J;J=c[e+13548>>2]|0;l=J+12496+(p<<3)+(k<<2)|0;if(J)c[l>>2]=(c[l>>2]|0)+1}J=(k|0)==0;a[V+2>>0]=fd(R,Q,(J|(a[s>>0]|0)==0)&1,U)|0;y=a[V>>0]|0;if(J){switch(y<<24>>24){case 0:{s=f+4536|0;u=e+16|0;v=e+20|0;r=c[s>>2]|0;n=0;o=c[u>>2]|0;l=c[v>>2]|0;do{q=n<<24>>24;k=d[r+(q>>1)>>0]|0;k=(256-k+(o*k|0)|0)>>>8;if((l|0)<0){zb(U);n=c[v>>2]|0}else n=l;l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)p=0;else{p=1;l=l-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;o=k<<R;m=l<<R;l=n-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=o;n=a[7888+(p+q)>>0]|0}while(n<<24>>24>0);t=e+13548|0;R=c[t>>2]|0;k=R+((0-n&255)<<2)|0;if(!R)k=o;else{c[k>>2]=(c[k>>2]|0)+1;k=c[u>>2]|0;l=c[v>>2]|0}a[V+20>>0]=0-(n&255);r=c[s>>2]|0;n=0;q=k;do{p=n<<24>>24;k=d[r+(p>>1)>>0]|0;k=(256-k+(q*k|0)|0)>>>8;if((l|0)<0){zb(U);n=c[U>>2]|0;l=c[v>>2]|0}else n=m;m=k<<24;if(n>>>0<m>>>0){o=0;m=n}else{o=1;m=n-m|0;k=(c[u>>2]|0)-k|0}n=d[1664+k>>0]|0;q=k<<n;m=m<<n;l=l-n|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=q;n=a[7888+(o+p)>>0]|0}while(n<<24>>24>0);R=c[t>>2]|0;k=R+((0-n&255)<<2)|0;if(!R)k=q;else{c[k>>2]=(c[k>>2]|0)+1;k=c[u>>2]|0;l=c[v>>2]|0}a[V+32>>0]=0-(n&255);r=c[s>>2]|0;n=0;q=k;do{p=n<<24>>24;k=d[r+(p>>1)>>0]|0;k=(256-k+(q*k|0)|0)>>>8;if((l|0)<0){zb(U);n=c[U>>2]|0;l=c[v>>2]|0}else n=m;m=k<<24;if(n>>>0<m>>>0){o=0;m=n}else{o=1;m=n-m|0;k=(c[u>>2]|0)-k|0}n=d[1664+k>>0]|0;q=k<<n;m=m<<n;l=l-n|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=q;n=a[7888+(o+p)>>0]|0}while(n<<24>>24>0);R=c[t>>2]|0;k=R+((0-n&255)<<2)|0;if(!R)k=q;else{c[k>>2]=(c[k>>2]|0)+1;k=c[u>>2]|0;l=c[v>>2]|0}a[V+44>>0]=0-(n&255);q=c[s>>2]|0;o=0;do{p=o<<24>>24;R=d[q+(p>>1)>>0]|0;k=(256-R+(k*R|0)|0)>>>8;if((l|0)<0){zb(U);n=c[U>>2]|0;l=c[v>>2]|0}else n=m;m=k<<24;if(n>>>0<m>>>0){o=0;m=n}else{o=1;m=n-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;k=k<<R;m=m<<R;l=l-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=k;o=a[7888+(o+p)>>0]|0}while(o<<24>>24>0);n=0-(o&255)|0;l=n&255;R=c[t>>2]|0;k=R+((0-o&255)<<2)|0;if(R|0)c[k>>2]=(c[k>>2]|0)+1;a[V+56>>0]=l;a[V+1>>0]=l;t=U;k=n;break}case 1:{s=f+4536|0;r=c[s>>2]|0;u=e+16|0;v=e+20|0;k=0;q=c[u>>2]|0;l=c[v>>2]|0;do{p=k<<24>>24;k=d[r+(p>>1)>>0]|0;k=(256-k+(q*k|0)|0)>>>8;if((l|0)<0){zb(U);n=c[v>>2]|0}else n=l;l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;q=k<<R;m=l<<R;l=n-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=q;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);n=0-(k&255)&255;r=e+13548|0;R=c[r>>2]|0;k=R+((0-k&255)<<2)|0;if(!R)k=q;else{c[k>>2]=(c[k>>2]|0)+1;k=c[u>>2]|0;l=c[v>>2]|0}a[V+44>>0]=n;a[V+20>>0]=n;q=c[s>>2]|0;o=0;do{p=o<<24>>24;R=d[q+(p>>1)>>0]|0;k=(256-R+(k*R|0)|0)>>>8;if((l|0)<0){zb(U);n=c[U>>2]|0;l=c[v>>2]|0}else n=m;m=k<<24;if(n>>>0<m>>>0){o=0;m=n}else{o=1;m=n-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;k=k<<R;m=m<<R;l=l-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=k;o=a[7888+(o+p)>>0]|0}while(o<<24>>24>0);n=0-(o&255)|0;l=n&255;R=c[r>>2]|0;k=R+((0-o&255)<<2)|0;if(R|0)c[k>>2]=(c[k>>2]|0)+1;a[V+1>>0]=l;a[V+56>>0]=l;a[V+32>>0]=l;t=U;k=n;break}case 2:{s=f+4536|0;r=c[s>>2]|0;u=e+16|0;v=e+20|0;k=0;q=c[u>>2]|0;l=c[v>>2]|0;do{p=k<<24>>24;k=d[r+(p>>1)>>0]|0;k=(256-k+(q*k|0)|0)>>>8;if((l|0)<0){zb(U);n=c[v>>2]|0}else n=l;l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;q=k<<R;m=l<<R;l=n-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=q;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);n=0-(k&255)&255;r=e+13548|0;R=c[r>>2]|0;k=R+((0-k&255)<<2)|0;if(!R)k=q;else{c[k>>2]=(c[k>>2]|0)+1;k=c[u>>2]|0;l=c[v>>2]|0}a[V+32>>0]=n;a[V+20>>0]=n;q=c[s>>2]|0;o=0;do{p=o<<24>>24;R=d[q+(p>>1)>>0]|0;k=(256-R+(k*R|0)|0)>>>8;if((l|0)<0){zb(U);n=c[U>>2]|0;l=c[v>>2]|0}else n=m;m=k<<24;if(n>>>0<m>>>0){o=0;m=n}else{o=1;m=n-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;k=k<<R;m=m<<R;l=l-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=k;o=a[7888+(o+p)>>0]|0}while(o<<24>>24>0);n=0-(o&255)|0;l=n&255;R=c[r>>2]|0;k=R+((0-o&255)<<2)|0;if(R|0)c[k>>2]=(c[k>>2]|0)+1;a[V+1>>0]=l;a[V+56>>0]=l;a[V+44>>0]=l;t=U;k=n;break}default:{t=d[31941+(y&255)>>0]|0;s=f+4536|0;r=c[s>>2]|0;u=e+16|0;v=e+20|0;k=0;n=c[u>>2]|0;l=c[v>>2]|0;do{q=k<<24>>24;k=d[(q>>1)+(r+(t*9|0))>>0]|0;k=(256-k+(n*k|0)|0)>>>8;if((l|0)<0){zb(U);o=c[v>>2]|0}else o=l;l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)p=0;else{p=1;l=l-m|0;k=(c[u>>2]|0)-k|0}R=d[1664+k>>0]|0;n=k<<R;m=l<<R;l=o-R|0;c[U>>2]=m;c[v>>2]=l;c[u>>2]=n;k=a[7888+(p+q)>>0]|0}while(k<<24>>24>0);l=0-(k&255)|0;R=c[e+13548>>2]|0;k=R+(t*40|0)+((0-k&255)<<2)|0;if(R|0)c[k>>2]=(c[k>>2]|0)+1;a[V+1>>0]=l;t=U;k=l}}r=c[s>>2]|0;q=k&255;l=0;k=c[u>>2]|0;n=c[v>>2]|0;do{p=l<<24>>24;R=d[(p>>1)+(r+36+(q*9|0))>>0]|0;k=(256-R+(k*R|0)|0)>>>8;if((n|0)<0){zb(U);m=c[t>>2]|0;n=c[v>>2]|0}l=k<<24;if(m>>>0<l>>>0)o=0;else{o=1;m=m-l|0;k=(c[u>>2]|0)-k|0}l=d[1664+k>>0]|0;k=k<<l;m=m<<l;n=n-l|0;c[t>>2]=m;c[v>>2]=n;c[u>>2]=k;l=a[7888+(o+p)>>0]|0}while(l<<24>>24>0);U=c[e+13548>>2]|0;k=U+160+(q*40|0)+((0-l&255)<<2)|0;if(U|0)c[k>>2]=(c[k>>2]|0)+1;a[V+6>>0]=0-(l&255);a[V+7>>0]=3;a[V+8>>0]=0;a[V+9>>0]=-1}else{G=c[f+1016>>2]|0;w=y&255;H=28176+(w<<6)|0;k=a[x>>0]|0;t=V+8|0;I=f+4536|0;u=c[I>>2]|0;J=e+13548|0;v=c[J>>2]|0;if((a[B>>0]|0)!=0?(c[f+4492+(k<<2)>>2]&4|0)!=0:0){a[t>>0]=b[f+4428+(k<<3)+4>>1];a[V+9>>0]=-1}else K=182;b:do if((K|0)==182){k=c[f+4532>>2]|0;if((k|0)==2){p=ff(R,Q)|0;l=d[(c[I>>2]|0)+1935+p>>0]|0;q=e+16|0;l=(256-l+((c[q>>2]|0)*l|0)|0)>>>8;r=e+20|0;k=c[r>>2]|0;if((k|0)<0){zb(U);o=c[r>>2]|0}else o=k;m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)k=0;else{k=1;m=m-n|0;l=(c[q>>2]|0)-l|0}F=d[1664+l>>0]|0;c[U>>2]=m<<F;c[r>>2]=o-F;c[q>>2]=l<<F;F=c[J>>2]|0;l=F+12528+(p<<3)+(k<<2)|0;if(F)c[l>>2]=(c[l>>2]|0)+1}switch(k|0){case 1:{s=f+4528|0;t=c[f+4356+(a[s>>0]<<2)>>2]|0;r=gf(R,Q)|0;l=d[u+1950+r>>0]|0;p=e+16|0;l=(256-l+((c[p>>2]|0)*l|0)|0)>>>8;q=e+20|0;k=c[q>>2]|0;if((k|0)<0){zb(U);k=c[q>>2]|0}m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)o=0;else{o=1;m=m-n|0;l=(c[p>>2]|0)-l|0}F=d[1664+l>>0]|0;c[U>>2]=m<<F;c[q>>2]=k-F;c[p>>2]=l<<F;k=v+12648+(r<<3)+(o<<2)|0;if(v|0)c[k>>2]=(c[k>>2]|0)+1;a[V+8+t>>0]=a[s>>0]|0;a[((t|0)==0&1)+(V+8)>>0]=a[f+4529+o>>0]|0;break b}case 0:break;default:break b}p=hf(Q)|0;l=d[u+1940+(p<<1)>>0]|0;r=e+16|0;l=(256-l+((c[r>>2]|0)*l|0)|0)>>>8;s=e+20|0;k=c[s>>2]|0;if((k|0)<0){zb(U);k=c[s>>2]|0}m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)o=0;else{o=1;m=m-n|0;l=(c[r>>2]|0)-l|0}q=d[1664+l>>0]|0;c[U>>2]=m<<q;c[s>>2]=k-q;c[r>>2]=l<<q;q=(v|0)!=0;k=v+12568+(p<<4)+(o<<2)|0;if(q)c[k>>2]=(c[k>>2]|0)+1;if(!o)k=1;else{p=jf(Q)|0;l=d[u+1940+(p<<1)+1>>0]|0;l=(256-l+((c[r>>2]|0)*l|0)|0)>>>8;k=c[s>>2]|0;if((k|0)<0){zb(U);k=c[s>>2]|0}m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)o=0;else{o=1;m=m-n|0;l=(c[r>>2]|0)-l|0}F=d[1664+l>>0]|0;c[U>>2]=m<<F;c[s>>2]=k-F;c[r>>2]=l<<F;k=v+12568+(p<<4)+8+(o<<2)|0;if(q)c[k>>2]=(c[k>>2]|0)+1;k=(o|0)==0?2:3}a[t>>0]=k;a[V+9>>0]=-1}while(0);v=(a[V+9>>0]|0)>0;F=v&1;n=f+1032|0;o=e+13560|0;p=e+13564|0;q=e+13568|0;k=c[H>>2]|0;l=k+g|0;if(((l|0)>=0?(z=c[28176+(w<<6)+4>>2]|0,A=z+h|0,(l|0)<(c[n>>2]|0)?(A|0)>=(c[o>>2]|0):0):0)?(A|0)<(c[p>>2]|0):0)k=c[29008+(d[(c[(c[M>>2]|0)+(((c[q>>2]|0)*k|0)+z<<2)>>2]|0)+1>>0]<<2)>>2]|0;else k=0;l=c[28176+(w<<6)+8>>2]|0;m=l+g|0;if(((m|0)>=0?(C=c[28176+(w<<6)+12>>2]|0,D=C+h|0,(m|0)<(c[n>>2]|0)?(D|0)>=(c[o>>2]|0):0):0)?(D|0)<(c[p>>2]|0):0)k=(c[29008+(d[(c[(c[M>>2]|0)+(((c[q>>2]|0)*l|0)+C<<2)>>2]|0)+1>>0]<<2)>>2]|0)+k|0;u=c[29072+(k<<2)>>2]|0;if((a[B>>0]|0)!=0?(c[f+4492+(a[x>>0]<<2)>>2]&8|0)!=0:0){a[V+1>>0]=12;if((y&255)<3)ma(c[e+13708>>2]|0,5,31959,E);else K=260}else K=224;c:do if((K|0)==224){if((y&255)>2){t=u&255;q=c[I>>2]|0;r=e+16|0;s=e+20|0;k=0;m=c[r>>2]|0;n=c[s>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(q+1910+(t*3|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[s>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[r>>2]|0)-k|0}E=d[1664+k>>0]|0;m=k<<E;n=n-E|0;c[U>>2]=l<<E;c[s>>2]=n;c[r>>2]=m;k=a[31667+(o+p)>>0]|0}while(k<<24>>24>0);l=k<<24>>24;E=c[J>>2]|0;k=E+12384+(t<<4)+(0-l<<2)|0;if(E|0)c[k>>2]=(c[k>>2]|0)+1;l=10-l&255;k=V+1|0;a[k>>0]=l;if(l<<24>>24==12){K=260;break}}else{k=V+1|0;a[k>>0]=10;l=10}s=N+-4|0;r=v?2:1;if(!G){q=0;while(1){p=gd(R,Q,l,a[V+8+q>>0]|0,H,N,g,h,-1,0)|0;if((p|0)>0){o=0;do{n=N+(o<<2)|0;l=b[n>>1]|0;m=l<<16>>16;if(m&1|0)b[n>>1]=(l<<16>>16>0?65535:1)+m;l=n+2|0;m=b[l>>1]|0;n=m<<16>>16;if(n&1|0)b[l>>1]=(m<<16>>16>0?65535:1)+n;o=o+1|0}while((o|0)!=(p|0));c[O+(q<<2)>>2]=c[s+(p<<2)>>2]}l=q+1|0;if((l|0)==(r|0)){K=260;break c}q=l;l=a[k>>0]|0}}q=0;while(1){p=gd(R,Q,l,a[V+8+q>>0]|0,H,N,g,h,-1,0)|0;if((p|0)>0){o=0;do{n=N+(o<<2)|0;l=b[n>>1]|0;m=l<<16>>16;if(!((((m|0)>-1?m:0-m|0)|0)<64?(E=b[n+2>>1]|0,(((E|0)>-1?E:0-E|0)|0)<64):0))K=252;do if((K|0)==252){K=0;if(m&1|0)b[n>>1]=(l<<16>>16>0?65535:1)+m;l=n+2|0;m=b[l>>1]|0;n=m<<16>>16;if(!(n&1))break;b[l>>1]=(m<<16>>16>0?65535:1)+n}while(0);o=o+1|0}while((o|0)!=(p|0));c[O+(q<<2)>>2]=c[s+(p<<2)>>2]}l=q+1|0;if((l|0)==(r|0)){K=260;break c}q=l;l=a[k>>0]|0}}while(0);do if((K|0)==260){k=a[f+1212>>0]|0;if(k<<24>>24==4){k=c[e+13576>>2]|0;if(!k)m=3;else m=d[k+7>>0]|0;k=c[e+13580>>2]|0;do if(!k){if((m|0)==3)K=268}else{f=a[k+7>>0]|0;k=f&255;l=(m|0)==(k|0);if(f<<24>>24==3)if(l){K=268;break}else break;else if(l){K=268;break}else{m=(m|0)==3?k:3;break}}while(0);r=c[I>>2]|0;s=e+16|0;t=e+20|0;k=0;n=c[s>>2]|0;o=c[t>>2]|0;do{q=k<<24>>24;k=d[(q>>1)+(r+1902+(m<<1))>>0]|0;k=(256-k+(n*k|0)|0)>>>8;if((o|0)<0){zb(U);o=c[t>>2]|0}l=c[U>>2]|0;n=k<<24;if(l>>>0<n>>>0)p=0;else{p=1;l=l-n|0;k=(c[s>>2]|0)-k|0}f=d[1664+k>>0]|0;n=k<<f;o=o-f|0;c[U>>2]=l<<f;c[t>>2]=o;c[s>>2]=n;k=a[31679+(p+q)>>0]|0}while(k<<24>>24>0);f=c[J>>2]|0;k=0-k&255;l=f+12336+(m*12|0)+(k<<2)|0;if(f)c[l>>2]=(c[l>>2]|0)+1}else k=k&255;a[V+7>>0]=k;if((y&255)>=3){R=(hd(R,Q,a[V+1>>0]|0,V+12|0,O,O,F,G,U)|0)==0&1;U=e+13704|0;c[U>>2]=c[U>>2]|R;break}D=a[e+13544>>0]|0;E=1<<(D&255);C=a[e+13545>>0]|0;f=1<<(C&255);c[P+4>>2]=-2147450880;y=u&255;z=e+16|0;A=e+20|0;B=N+4|0;C=C<<24>>24==1;D=D<<24>>24==1;x=e+13704|0;v=v?2:1;t=0;do{w=t<<1;u=0;do{q=c[I>>2]|0;k=0;m=c[z>>2]|0;n=c[A>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(q+1910+(y*3|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[A>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[z>>2]|0)-k|0}e=d[1664+k>>0]|0;m=k<<e;n=n-e|0;c[U>>2]=l<<e;c[A>>2]=n;c[z>>2]=m;k=a[31667+(o+p)>>0]|0}while(k<<24>>24>0);s=u+w|0;k=k<<24>>24;e=c[J>>2]|0;l=e+12384+(y<<4)+(0-k<<2)|0;if(e|0)c[l>>2]=(c[l>>2]|0)+1;r=10-k&255;d:do if((r&-2)<<24>>24==10){o=r<<24>>24==10;switch(s|0){case 0:{k=s;do{c[P+(k<<2)>>2]=c[N+((gd(R,Q,r,a[(c[c[M>>2]>>2]|0)+8+k>>0]|0,H,N,g,h,0,1)|0)+-1<<2)>>2];k=k+1|0}while((k|0)!=(v|0));break}case 3:{if(o){k=0;while(1){e=c[c[M>>2]>>2]|0;gd(R,Q,10,a[e+8+k>>0]|0,H,N,g,h,3,1)|0;c[P+(k<<2)>>2]=c[e+48+(k<<2)>>2];k=k+1|0;if((k|0)==(v|0))break d}}o=0;do{e=c[c[M>>2]>>2]|0;gd(R,Q,r,a[e+8+o>>0]|0,H,N,g,h,3,1)|0;k=c[e+36+(o<<2)>>2]|0;l=c[e+24+(o<<2)>>2]|0;m=c[N>>2]|0;n=c[B>>2]|0;p=P+(o<<2)|0;c[p>>2]=0;do if((c[e+48+(o<<2)>>2]|0)==(k|0)){if((k|0)!=(l|0)){k=l;K=301;break}if((k|0)!=(m|0)){k=m;K=301;break}if((k|0)!=(n|0)){k=n;K=301}}else K=301;while(0);if((K|0)==301){K=0;c[p>>2]=k}o=o+1|0}while((o|0)!=(v|0));break}default:{if((s+-1|0)>>>0>=2){k=0;while(1){gd(R,Q,r,a[(c[c[M>>2]>>2]|0)+8+k>>0]|0,H,N,g,h,s,1)|0;k=k+1|0;if((k|0)==(v|0))break d}}n=0;do{p=P+(n<<2)|0;k=c[c[M>>2]>>2]|0;q=gd(R,Q,r,a[k+8+n>>0]|0,H,N,g,h,s,1)|0;e:do if(o)c[p>>2]=c[k+24+(n<<2)>>2];else{c[p>>2]=0;if((q|0)<=0)break;l=c[k+24+(n<<2)>>2]|0;k=0;while(1){m=c[N+(k<<2)>>2]|0;k=k+1|0;if((l|0)!=(m|0))break;if((k|0)>=(q|0))break e}c[p>>2]=m}while(0);n=n+1|0}while((n|0)!=(v|0))}}}while(0);k=V+20+(s*12|0)|0;if(!(hd(R,Q,r,V+20+(s*12|0)+4|0,O,P,F,G,U)|0)){K=316;break}if(C){e=V+20+((s+2|0)*12|0)|0;c[e>>2]=c[k>>2];c[e+4>>2]=c[k+4>>2];c[e+8>>2]=c[k+8>>2]}if(D){e=V+20+((s+1|0)*12|0)|0;c[e>>2]=c[k>>2];c[e+4>>2]=c[k+4>>2];c[e+8>>2]=c[k+8>>2]}u=u+E|0}while((u|0)<2);if((K|0)==316){K=0;c[x>>2]=c[x>>2]|1}t=t+f|0}while((t|0)<2);a[V+1>>0]=r;R=V+60|0;e=c[R+4>>2]|0;U=V+12|0;c[U>>2]=c[R>>2];c[U+4>>2]=e}while(0)}if((j|0)<=0){L=W;return}o=V+8|0;n=V+12|0;if((i|0)<=0){L=W;return}k=S;l=0;while(1){m=0;do{e=k+(m*12|0)+8|0;U=d[o>>0]|d[o+1>>0]<<8;a[e>>0]=U;a[e+1>>0]=U>>8;e=n;U=c[e+4>>2]|0;V=k+(m*12|0)|0;c[V>>2]=c[e>>2];c[V+4>>2]=U;m=m+1|0}while((m|0)!=(i|0));l=l+1|0;if((l|0)==(j|0))break;else k=k+((c[T>>2]|0)*12|0)|0}L=W;return}v=e+13580|0;z=c[v>>2]|0;u=e+13576|0;A=c[u>>2]|0;y=a[V>>0]|0;s=(q*g|0)+h|0;t=f+4412|0;f:do if(a[t>>0]|0){if(!(a[f+4413>>0]|0)){m=c[f+1200>>2]|0;n=c[f+1204>>2]|0;if(!((i|0)>0&(j|0)>0)){k=0;break}if(!m){l=0;while(1){k=0;do{a[n+(k+s+((c[T>>2]|0)*l|0))>>0]=0;k=k+1|0}while((k|0)!=(i|0));l=l+1|0;if((l|0)==(j|0)){k=0;break f}}}else{l=0;while(1){k=0;do{S=k+s+((c[T>>2]|0)*l|0)|0;a[n+S>>0]=a[m+S>>0]|0;k=k+1|0}while((k|0)!=(i|0));l=l+1|0;if((l|0)==(j|0)){k=0;break f}}}}q=e+16|0;r=e+20|0;k=0;m=c[q>>2]|0;n=c[r>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(f+4417)>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[r>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[q>>2]|0)-k|0}S=d[1664+k>>0]|0;m=k<<S;n=n-S|0;c[U>>2]=l<<S;c[r>>2]=n;c[q>>2]=m;k=a[31801+(o+p)>>0]|0}while(k<<24>>24>0);k=0-(k<<24>>24)|0;if((j|0)>0?(w=k&255,x=f+1204|0,(i|0)>0):0){m=0;do{l=0;do{a[(c[x>>2]|0)+(l+s+((c[T>>2]|0)*m|0))>>0]=w;l=l+1|0}while((l|0)!=(i|0));m=m+1|0}while((m|0)!=(j|0))}}else k=0;while(0);a[V+4>>0]=k;if((a[t>>0]|0)!=0?(c[f+4492+(k<<24>>24<<2)>>2]&8|0)!=0:0)k=1;else{m=c[u>>2]|0;k=c[v>>2]|0;if(!k)l=0;else l=a[k+3>>0]|0;if(!m)k=0;else k=a[m+3>>0]|0;r=k+l|0;l=d[(c[f+4536>>2]|0)+1967+r>>0]|0;p=e+16|0;l=(256-l+((c[p>>2]|0)*l|0)|0)>>>8;q=e+20|0;k=c[q>>2]|0;if((k|0)<0){zb(U);o=c[q>>2]|0}else o=k;m=c[U>>2]|0;n=l<<24;if(m>>>0<n>>>0)k=0;else{k=1;m=m-n|0;l=(c[p>>2]|0)-l|0}j=d[1664+l>>0]|0;c[U>>2]=m<<j;c[q>>2]=o-j;c[p>>2]=l<<j;j=c[e+13548>>2]|0;l=j+12776+(r<<3)+(k<<2)|0;if(j)c[l>>2]=(c[l>>2]|0)+1}a[V+3>>0]=k;a[V+2>>0]=fd(R,Q,1,U)|0;a[V+8>>0]=0;a[V+9>>0]=-1;switch(y<<24>>24){case 0:{s=e+16|0;t=e+20|0;q=(Ub(V,z,0)|0)&255;r=(Tb(V,A,0)|0)&255;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(q*90|0)+(r*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;n=n-j|0;c[U>>2]=l<<j;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);a[V+20>>0]=0-(k&255);q=Ub(V,z,1)|0;q=q&255;r=(Tb(V,A,1)|0)&255;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(q*90|0)+(r*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;n=n-j|0;c[U>>2]=l<<j;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);a[V+32>>0]=0-(k&255);q=Ub(V,z,2)|0;q=q&255;r=(Tb(V,A,2)|0)&255;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(q*90|0)+(r*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;n=n-j|0;c[U>>2]=l<<j;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);a[V+44>>0]=0-(k&255);r=Ub(V,z,3)|0;r=r&255;q=(Tb(V,A,3)|0)&255;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(r*90|0)+(q*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;l=l<<j;n=n-j|0;c[U>>2]=l;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);k=0-(k&255)|0;r=k&255;a[V+56>>0]=r;a[V+1>>0]=r;r=U;break}case 1:{q=Ub(V,z,0)|0;q=q&255;r=(Tb(V,A,0)|0)&255;s=e+16|0;t=e+20|0;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(q*90|0)+(r*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;n=n-j|0;c[U>>2]=l<<j;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);r=0-(k&255)&255;a[V+44>>0]=r;a[V+20>>0]=r;r=Ub(V,z,1)|0;r=r&255;q=(Tb(V,A,1)|0)&255;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(r*90|0)+(q*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;l=l<<j;n=n-j|0;c[U>>2]=l;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);k=0-(k&255)|0;r=k&255;a[V+1>>0]=r;a[V+56>>0]=r;a[V+32>>0]=r;r=U;break}case 2:{q=Ub(V,z,0)|0;q=q&255;r=(Tb(V,A,0)|0)&255;s=e+16|0;t=e+20|0;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(q*90|0)+(r*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;n=n-j|0;c[U>>2]=l<<j;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);r=0-(k&255)&255;a[V+32>>0]=r;a[V+20>>0]=r;r=Ub(V,z,2)|0;r=r&255;q=(Tb(V,A,2)|0)&255;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(r*90|0)+(q*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;l=l<<j;n=n-j|0;c[U>>2]=l;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);k=0-(k&255)|0;r=k&255;a[V+1>>0]=r;a[V+56>>0]=r;a[V+44>>0]=r;r=U;break}default:{r=Ub(V,z,0)|0;r=r&255;q=(Tb(V,A,0)|0)&255;s=e+16|0;t=e+20|0;k=0;m=c[s>>2]|0;n=c[t>>2]|0;do{p=k<<24>>24;k=d[(p>>1)+(6832+(r*90|0)+(q*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);n=c[t>>2]|0}l=c[U>>2]|0;m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;l=l<<j;n=n-j|0;c[U>>2]=l;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);k=0-(k&255)|0;a[V+1>>0]=k;r=U}}q=k&255;k=0;do{p=k<<24>>24;k=d[(p>>1)+(7744+(q*9|0))>>0]|0;k=(256-k+(m*k|0)|0)>>>8;if((n|0)<0){zb(U);l=c[r>>2]|0;n=c[t>>2]|0}m=k<<24;if(l>>>0<m>>>0)o=0;else{o=1;l=l-m|0;k=(c[s>>2]|0)-k|0}j=d[1664+k>>0]|0;m=k<<j;l=l<<j;n=n-j|0;c[r>>2]=l;c[t>>2]=n;c[s>>2]=m;k=a[7888+(o+p)>>0]|0}while(k<<24>>24>0);a[V+6>>0]=0-(k&255);L=W;return}function fd(b,e,f,g){b=b|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;h=c[b+616>>2]|0;p=a[c[c[e+292>>2]>>2]>>0]|0;q=a[31941+(p&255)>>0]|0;if(!((f|0)!=0&(h|0)==4&(p&255)>2)){g=a[31954+h>>0]|0;q=(q&255)<(g&255)?q:g;return q|0}p=c[e+268>>2]|0;i=c[e+296>>2]|0;f=c[e+300>>2]|0;e=q&255;j=(f|0)!=0;k=(i|0)!=0;if(j?(a[f+3>>0]|0)==0:0)h=d[f+2>>0]|0;else h=e;if(k?(a[i+3>>0]|0)==0:0)f=d[i+2>>0]|0;else f=e;o=k?f:h;o=((j?h:o)+o|0)>>>0>e>>>0&1;f=c[b+4104>>2]|0;switch(q<<24>>24){case 1:{l=f+1965+o|0;break}case 2:{l=f+1961+(o<<1)|0;break}case 3:{l=f+1955+(o*3|0)|0;break}default:l=0}e=d[l>>0]|0;m=g+4|0;e=(256-e+((c[m>>2]|0)*e|0)|0)>>>8;n=g+8|0;f=c[n>>2]|0;if((f|0)<0){zb(g);f=c[n>>2]|0}i=c[g>>2]|0;j=e<<24;if(i>>>0<j>>>0)h=0;else{h=1;i=i-j|0;e=(c[m>>2]|0)-e|0}j=d[1664+e>>0]|0;e=e<<j;i=i<<j;j=f-j|0;c[g>>2]=i;c[n>>2]=j;c[m>>2]=e;if((q&255)>1&(h|0)!=0){f=d[l+1>>0]|0;f=(256-f+(e*f|0)|0)>>>8;if((j|0)<0){zb(g);i=c[g>>2]|0;j=c[n>>2]|0}e=f<<24;if(i>>>0<e>>>0)b=0;else{b=1;i=i-e|0;f=(c[m>>2]|0)-f|0}k=d[1664+f>>0]|0;f=f<<k;e=i<<k;k=j-k|0;c[g>>2]=e;c[n>>2]=k;c[m>>2]=f;h=b+h|0;if((q&255)>2&(h|0)!=1){l=d[l+2>>0]|0;f=(256-l+(f*l|0)|0)>>>8;if((k|0)<0){zb(g);e=c[g>>2]|0;k=c[n>>2]|0}i=f<<24;if(e>>>0<i>>>0)j=0;else{j=1;e=e-i|0;f=(c[m>>2]|0)-f|0}l=d[1664+f>>0]|0;c[g>>2]=e<<l;c[n>>2]=k-l;c[m>>2]=f<<l;h=j+h|0}}if(p|0){switch(q<<24>>24){case 1:{f=p+12744+(o<<3)|0;break}case 2:{f=p+12720+(o*12|0)|0;break}case 3:{f=p+12688+(o<<4)|0;break}default:f=0}q=f+(h<<2)|0;c[q>>2]=(c[q>>2]|0)+1}q=h&255;return q|0}
function Pe(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;j=L;L=L+32|0;i=j;n=b[c>>1]|0;g=b[c+4>>1]|0;h=b[c+2>>1]|0;k=b[c+6>>1]|0;l=(((g+n|0)*11585|0)+8192|0)>>>14<<16>>16;m=((h*15137|0)+8192+(k*6270|0)|0)>>>14<<16>>16;b[i>>1]=m+l;g=(((n-g|0)*11585|0)+8192|0)>>>14<<16>>16;k=((h*6270|0)+8192+(k*-15137|0)|0)>>>14<<16>>16;b[i+2>>1]=k+g;b[i+4>>1]=g-k;b[i+6>>1]=l-m;m=b[c+8>>1]|0;l=b[c+12>>1]|0;k=b[c+10>>1]|0;g=b[c+14>>1]|0;h=(((l+m|0)*11585|0)+8192|0)>>>14<<16>>16;n=((k*15137|0)+8192+(g*6270|0)|0)>>>14<<16>>16;b[i+8>>1]=n+h;l=(((m-l|0)*11585|0)+8192|0)>>>14<<16>>16;g=((k*6270|0)+8192+(g*-15137|0)|0)>>>14<<16>>16;b[i+10>>1]=g+l;b[i+12>>1]=l-g;b[i+14>>1]=h-n;n=b[c+16>>1]|0;h=b[c+20>>1]|0;g=b[c+18>>1]|0;l=b[c+22>>1]|0;k=(((h+n|0)*11585|0)+8192|0)>>>14<<16>>16;m=((g*15137|0)+8192+(l*6270|0)|0)>>>14<<16>>16;b[i+16>>1]=m+k;h=(((n-h|0)*11585|0)+8192|0)>>>14<<16>>16;l=((g*6270|0)+8192+(l*-15137|0)|0)>>>14<<16>>16;b[i+18>>1]=l+h;b[i+20>>1]=h-l;b[i+22>>1]=k-m;m=b[c+24>>1]|0;k=b[c+28>>1]|0;l=b[c+26>>1]|0;c=b[c+30>>1]|0;h=(((k+m|0)*11585|0)+8192|0)>>>14<<16>>16;g=((l*15137|0)+8192+(c*6270|0)|0)>>>14<<16>>16;b[i+24>>1]=g+h;k=(((m-k|0)*11585|0)+8192|0)>>>14<<16>>16;c=((l*6270|0)+8192+(c*-15137|0)|0)>>>14<<16>>16;b[i+26>>1]=c+k;b[i+28>>1]=k-c;b[i+30>>1]=h-g;g=f<<1;h=f*3|0;c=0;do{p=b[i+(c<<1)>>1]|0;o=b[i+(c+8<<1)>>1]|0;n=b[i+(c+4<<1)>>1]|0;k=b[i+(c+12<<1)>>1]|0;l=(((o+p|0)*11585|0)+8192|0)>>>14<<16>>16;m=((n*15137|0)+8192+(k*6270|0)|0)>>>14<<16>>16;o=(((p-o|0)*11585|0)+8192|0)>>>14<<16>>16;k=((n*6270|0)+8192+(k*-15137|0)|0)>>>14<<16>>16;n=e+c|0;p=((m+l<<16>>16)+8>>4)+(d[n>>0]|0)|0;p=(p|0)>0?p:0;a[n>>0]=(p|0)<255?p:255;n=e+(c+f)|0;p=((k+o<<16>>16)+8>>4)+(d[n>>0]|0)|0;p=(p|0)>0?p:0;a[n>>0]=(p|0)<255?p:255;n=e+(g+c)|0;k=((o-k<<16>>16)+8>>4)+(d[n>>0]|0)|0;k=(k|0)>0?k:0;a[n>>0]=(k|0)<255?k:255;n=e+(h+c)|0;m=((l-m<<16>>16)+8>>4)+(d[n>>0]|0)|0;m=(m|0)>0?m:0;a[n>>0]=(m|0)<255?m:255;c=c+1|0}while((c|0)!=4);L=j;return}function Qe(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0;c=(((((((b[c>>1]|0)*11585|0)+8192|0)>>>14<<16>>16)*11585|0)+8192|0)>>>14<<16>>16)+8>>4;g=c+(d[e>>0]|0)|0;g=(g|0)>0?g:0;a[e>>0]=(g|0)<255?g:255;g=e+1|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;g=e+2|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;g=e+3|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;e=e+f|0;g=c+(d[e>>0]|0)|0;g=(g|0)>0?g:0;a[e>>0]=(g|0)<255?g:255;g=e+1|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;g=e+2|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;g=e+3|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;e=e+f|0;g=c+(d[e>>0]|0)|0;g=(g|0)>0?g:0;a[e>>0]=(g|0)<255?g:255;g=e+1|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;g=e+2|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;g=e+3|0;h=c+(d[g>>0]|0)|0;h=(h|0)>0?h:0;a[g>>0]=(h|0)<255?h:255;f=e+f|0;e=c+(d[f>>0]|0)|0;e=(e|0)>0?e:0;a[f>>0]=(e|0)<255?e:255;e=f+1|0;g=c+(d[e>>0]|0)|0;g=(g|0)>0?g:0;a[e>>0]=(g|0)<255?g:255;e=f+2|0;g=c+(d[e>>0]|0)|0;g=(g|0)>0?g:0;a[e>>0]=(g|0)<255?g:255;f=f+3|0;e=c+(d[f>>0]|0)|0;e=(e|0)>0?e:0;a[f>>0]=(e|0)<255?e:255;return}function Re(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;q=b[a+14>>1]|0;h=q<<16>>16;r=b[a>>1]|0;i=r<<16>>16;p=b[a+10>>1]|0;j=p<<16>>16;o=b[a+4>>1]|0;d=o<<16>>16;n=b[a+6>>1]|0;e=n<<16>>16;m=b[a+8>>1]|0;f=m<<16>>16;l=b[a+2>>1]|0;g=l<<16>>16;k=b[a+12>>1]|0;a=k<<16>>16;if(!((r|q|p|o|n|m|l|k)<<16>>16)){b[c>>1]=0;b[c+2>>1]=0;b[c+4>>1]=0;b[c+6>>1]=0;b[c+8>>1]=0;b[c+10>>1]=0;b[c+12>>1]=0;b[c+14>>1]=0;return}else{m=(f*12665|0)+(e*10394|0)|0;o=(f*-10394|0)+(e*12665|0)|0;k=(a*15679|0)+(g*4756|0)|0;p=(a*-4756|0)+(g*15679|0)|0;n=(i*1606|0)+(h*16305|0)+8192|0;g=n+m>>14;i=(i*-16305|0)+(h*1606|0)+8192|0;q=i+o>>14;l=(d*7723|0)+(j*14449|0)+8192|0;h=l+k>>14;j=(d*-14449|0)+(j*7723|0)+8192|0;r=j+p>>14;m=n-m>>14;o=i-o>>14;k=l-k>>14;p=j-p>>14;j=(k*-6270|0)+(p*15137|0)|0;p=(k*15137|0)+(p*6270|0)|0;k=g-h|0;l=q-r|0;i=(m*15137|0)+(o*6270|0)+8192|0;o=(m*6270|0)+(o*-15137|0)+8192|0;m=i-j>>14;n=o-p>>14;b[c>>1]=h+g;b[c+2>>1]=0-((i+j|0)>>>14);b[c+4>>1]=(((m+n|0)*11585|0)+8192|0)>>>14;b[c+6>>1]=0-((((k+l|0)*11585|0)+8192|0)>>>14);b[c+8>>1]=(((k-l|0)*11585|0)+8192|0)>>>14;b[c+10>>1]=0-((((m-n|0)*11585|0)+8192|0)>>>14);b[c+12>>1]=(o+p|0)>>>14;b[c+14>>1]=0-(q+r);return}}function Se(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;j=b[a+2>>1]|0;l=b[a+14>>1]|0;d=b[a+10>>1]|0;f=b[a+6>>1]|0;g=b[a>>1]|0;n=b[a+8>>1]|0;m=b[a+4>>1]|0;h=b[a+12>>1]|0;i=((j*3196|0)+8192+(l*-16069|0)|0)>>>14<<16>>16;k=((d*13623|0)+8192+(f*-9102|0)|0)>>>14<<16>>16;f=((d*9102|0)+8192+(f*13623|0)|0)>>>14<<16>>16;a=((j*16069|0)+8192+(l*3196|0)|0)>>>14<<16>>16;l=(((n+g|0)*11585|0)+8192|0)>>>14<<16>>16;j=((m*15137|0)+8192+(h*6270|0)|0)>>>14<<16>>16;n=(((g-n|0)*11585|0)+8192|0)>>>14<<16>>16;h=((m*6270|0)+8192+(h*-15137|0)|0)>>>14<<16>>16;m=a-f<<16>>16;g=i-k<<16>>16;d=j+l<<16>>16;a=f+a<<16>>16;b[c>>1]=a+d;f=h+n<<16>>16;e=(((g+m|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+2>>1]=e+f;h=n-h<<16>>16;g=(((m-g|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+4>>1]=g+h;j=l-j<<16>>16;i=k+i<<16>>16;b[c+6>>1]=i+j;b[c+8>>1]=j-i;b[c+10>>1]=h-g;b[c+12>>1]=f-e;b[c+14>>1]=d-a;return}function Te(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0;D=L;L=L+160|0;A=D+32|0;B=D+16|0;C=D;Se(c,A);Se(c+16|0,A+16|0);Se(c+32|0,A+32|0);Se(c+48|0,A+48|0);Se(c+64|0,A+64|0);Se(c+80|0,A+80|0);Se(c+96|0,A+96|0);Se(c+112|0,A+112|0);g=B+2|0;h=B+4|0;i=B+6|0;j=B+8|0;k=B+10|0;l=B+12|0;m=B+14|0;n=C+2|0;o=f<<1;p=C+4|0;q=f*3|0;r=C+6|0;s=f<<2;t=C+8|0;u=f*5|0;v=C+10|0;w=f*6|0;x=C+12|0;y=f*7|0;z=C+14|0;c=0;do{b[B>>1]=b[A+(c<<1)>>1]|0;b[g>>1]=b[A+(c+8<<1)>>1]|0;b[h>>1]=b[A+(c+16<<1)>>1]|0;b[i>>1]=b[A+(c+24<<1)>>1]|0;b[j>>1]=b[A+(c+32<<1)>>1]|0;b[k>>1]=b[A+(c+40<<1)>>1]|0;b[l>>1]=b[A+(c+48<<1)>>1]|0;b[m>>1]=b[A+(c+56<<1)>>1]|0;Se(B,C);E=e+c|0;F=((b[C>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(c+f)|0;F=((b[n>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(o+c)|0;F=((b[p>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(q+c)|0;F=((b[r>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(s+c)|0;F=((b[t>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(u+c)|0;F=((b[v>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(w+c)|0;F=((b[x>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;E=e+(y+c)|0;F=((b[z>>1]|0)+16>>5)+(d[E>>0]|0)|0;F=(F|0)>0?F:0;a[E>>0]=(F|0)<255?F:255;c=c+1|0}while((c|0)!=8);L=D;return}function Ue(e,f,g){e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;E=L;L=L+160|0;B=E+32|0;C=E+16|0;D=E;h=B;i=h+128|0;do{c[h>>2]=0;h=h+4|0}while((h|0)<(i|0));Se(e,B);Se(e+16|0,B+16|0);Se(e+32|0,B+32|0);Se(e+48|0,B+48|0);h=C+2|0;i=C+4|0;j=C+6|0;k=C+8|0;l=C+10|0;m=C+12|0;n=C+14|0;o=D+2|0;p=g<<1;q=D+4|0;r=g*3|0;s=D+6|0;t=g<<2;u=D+8|0;v=g*5|0;w=D+10|0;x=g*6|0;y=D+12|0;z=g*7|0;A=D+14|0;e=0;do{b[C>>1]=b[B+(e<<1)>>1]|0;b[h>>1]=b[B+(e+8<<1)>>1]|0;b[i>>1]=b[B+(e+16<<1)>>1]|0;b[j>>1]=b[B+(e+24<<1)>>1]|0;b[k>>1]=b[B+(e+32<<1)>>1]|0;b[l>>1]=b[B+(e+40<<1)>>1]|0;b[m>>1]=b[B+(e+48<<1)>>1]|0;b[n>>1]=b[B+(e+56<<1)>>1]|0;Se(C,D);F=f+e|0;G=((b[D>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(e+g)|0;G=((b[o>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(p+e)|0;G=((b[q>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(r+e)|0;G=((b[s>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(t+e)|0;G=((b[u>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(v+e)|0;G=((b[w>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(x+e)|0;G=((b[y>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;F=f+(z+e)|0;G=((b[A>>1]|0)+16>>5)+(d[F>>0]|0)|0;G=(G|0)>0?G:0;a[F>>0]=(G|0)<255?G:255;e=e+1|0}while((e|0)!=8);L=E;return}function Ve(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0;i=(((((((b[c>>1]|0)*11585|0)+8192|0)>>>14<<16>>16)*11585|0)+8192|0)>>>14<<16>>16)+16>>5;if((f|0)==1){g=0;h=e;c=a[e+6>>0]|0;while(1){e=i+(d[h>>0]|0)|0;e=(e|0)>0?e:0;a[h>>0]=(e|0)<255?e:255;e=h+1|0;j=i+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[e>>0]=(j|0)<255?j:255;e=h+2|0;j=i+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[e>>0]=(j|0)<255?j:255;e=h+3|0;j=i+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[e>>0]=(j|0)<255?j:255;e=h+4|0;j=i+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[e>>0]=(j|0)<255?j:255;e=h+5|0;j=i+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[e>>0]=(j|0)<255?j:255;e=i+(c&255)|0;e=(e|0)>0?e:0;a[h+6>>0]=(e|0)<255?e:255;e=h+7|0;j=i+(d[e>>0]|0)|0;j=(j|0)>0?j:0;c=((j|0)<255?j:255)&255;a[e>>0]=c;g=g+1|0;if((g|0)==8)break;else h=h+f|0}return}else{g=0;c=e;while(1){j=i+(d[c>>0]|0)|0;j=(j|0)>0?j:0;a[c>>0]=(j|0)<255?j:255;j=c+1|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;j=c+2|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;j=c+3|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;j=c+4|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;j=c+5|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;j=c+6|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;j=c+7|0;e=i+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[j>>0]=(e|0)<255?e:255;g=g+1|0;if((g|0)==8)break;else c=c+f|0}return}}function We(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0;G=b[a+30>>1]|0;n=G<<16>>16;H=b[a>>1]|0;q=H<<16>>16;F=b[a+26>>1]|0;r=F<<16>>16;E=b[a+4>>1]|0;d=E<<16>>16;D=b[a+22>>1]|0;e=D<<16>>16;C=b[a+8>>1]|0;f=C<<16>>16;B=b[a+18>>1]|0;g=B<<16>>16;A=b[a+12>>1]|0;h=A<<16>>16;z=b[a+14>>1]|0;i=z<<16>>16;y=b[a+16>>1]|0;j=y<<16>>16;x=b[a+10>>1]|0;k=x<<16>>16;w=b[a+20>>1]|0;l=w<<16>>16;v=b[a+6>>1]|0;m=v<<16>>16;u=b[a+24>>1]|0;o=u<<16>>16;t=b[a+2>>1]|0;p=t<<16>>16;s=b[a+28>>1]|0;a=s<<16>>16;if(!((H|G|F|E|D|C|B|A|z|y|x|w|v|u|t|s)<<16>>16)){a=c+32|0;do{b[c>>1]=0;c=c+2|0}while((c|0)<(a|0));return}else{F=(j*12140|0)+(i*11003|0)|0;x=(j*-11003|0)+(i*12140|0)|0;E=(l*14053|0)+(k*8423|0)|0;u=(l*-8423|0)+(k*14053|0)|0;s=(o*15426|0)+(m*5520|0)|0;C=(o*-5520|0)+(m*15426|0)|0;l=(a*16207|0)+(p*2404|0)|0;D=(a*-2404|0)+(p*16207|0)|0;o=(q*804|0)+(n*16364|0)+8192|0;v=o+F>>14;q=(q*-16364|0)+(n*804|0)+8192|0;t=q+x>>14;n=(d*3981|0)+(r*15893|0)+8192|0;m=n+E>>14;r=(d*-15893|0)+(r*3981|0)+8192|0;y=r+u>>14;H=(f*7005|0)+(e*14811|0)+8192|0;z=H+s>>14;k=(f*-14811|0)+(e*7005|0)+8192|0;A=k+C>>14;G=(h*9760|0)+(g*13160|0)+8192|0;p=G+l>>14;w=(h*-13160|0)+(g*9760|0)+8192|0;B=w+D>>14;F=o-F>>14;q=q-x>>14;E=n-E>>14;r=r-u>>14;s=H-s>>14;C=k-C>>14;l=G-l>>14;D=w-D>>14;w=(s*-3196|0)+(C*16069|0)|0;C=(s*16069|0)+(C*3196|0)|0;s=(l*-13623|0)+(D*9102|0)|0;D=(l*9102|0)+(D*13623|0)|0;l=z+v|0;G=A+t|0;k=p+m|0;H=B+y|0;z=v-z|0;A=t-A|0;p=m-p|0;B=y-B|0;y=(F*16069|0)+(q*3196|0)+8192|0;m=y+w>>14;q=(F*3196|0)+(q*-16069|0)+8192|0;F=q+C>>14;t=(E*9102|0)+(r*13623|0)+8192|0;n=t+s>>14;r=(E*13623|0)+(r*-9102|0)+8192|0;E=r+D>>14;w=y-w>>14;C=q-C>>14;s=t-s>>14;D=r-D>>14;r=(p*-6270|0)+(B*15137|0)|0;B=(p*15137|0)+(B*6270|0)|0;p=(s*-6270|0)+(D*15137|0)|0;D=(s*15137|0)+(D*6270|0)|0;s=l-k|0;t=G-H|0;q=(z*15137|0)+(A*6270|0)+8192|0;A=(z*6270|0)+(A*-15137|0)+8192|0;z=q-r>>14;y=A-B>>14;v=m-n|0;u=F-E|0;o=(w*15137|0)+(C*6270|0)+8192|0;C=(w*6270|0)+(C*-15137|0)+8192|0;w=o-p>>14;x=C-D>>14;b[c>>1]=k+l;b[c+2>>1]=0-(m+n);b[c+4>>1]=(o+p|0)>>>14;b[c+6>>1]=0-((q+r|0)>>>14);b[c+8>>1]=(((z+y|0)*11585|0)+8192|0)>>>14;b[c+10>>1]=(((w+x|0)*-11585|0)+8192|0)>>>14;b[c+12>>1]=(((v+u|0)*11585|0)+8192|0)>>>14;b[c+14>>1]=(((s+t|0)*-11585|0)+8192|0)>>>14;b[c+16>>1]=(((s-t|0)*11585|0)+8192|0)>>>14;b[c+18>>1]=(((u-v|0)*11585|0)+8192|0)>>>14;b[c+20>>1]=(((w-x|0)*11585|0)+8192|0)>>>14;b[c+22>>1]=(((y-z|0)*11585|0)+8192|0)>>>14;b[c+24>>1]=(A+B|0)>>>14;b[c+26>>1]=0-((C+D|0)>>>14);b[c+28>>1]=E+F;b[c+30>>1]=0-(G+H);return}}function Xe(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;n=b[a+2>>1]|0;d=b[a+30>>1]|0;y=b[a+18>>1]|0;f=b[a+14>>1]|0;k=b[a+10>>1]|0;j=b[a+22>>1]|0;x=b[a+26>>1]|0;g=b[a+6>>1]|0;h=b[a+4>>1]|0;v=b[a+28>>1]|0;t=b[a+20>>1]|0;r=b[a+12>>1]|0;o=((n*1606|0)+8192+(d*-16305|0)|0)>>>14<<16>>16;s=((y*12665|0)+8192+(f*-10394|0)|0)>>>14<<16>>16;u=((k*7723|0)+8192+(j*-14449|0)|0)>>>14<<16>>16;q=((x*15679|0)+8192+(g*-4756|0)|0)>>>14<<16>>16;g=((x*4756|0)+8192+(g*15679|0)|0)>>>14<<16>>16;j=((k*14449|0)+8192+(j*7723|0)|0)>>>14<<16>>16;f=((y*10394|0)+8192+(f*12665|0)|0)>>>14<<16>>16;d=((n*16305|0)+8192+(d*1606|0)|0)>>>14<<16>>16;n=b[a>>1]|0;y=b[a+16>>1]|0;k=b[a+8>>1]|0;x=b[a+24>>1]|0;m=((h*3196|0)+8192+(v*-16069|0)|0)>>>14<<16>>16;l=((t*13623|0)+8192+(r*-9102|0)|0)>>>14<<16>>16;r=((t*9102|0)+8192+(r*13623|0)|0)>>>14<<16>>16;v=((h*16069|0)+8192+(v*3196|0)|0)>>>14<<16>>16;h=o-s<<16>>16;t=d-f<<16>>16;e=q-u<<16>>16;p=g-j<<16>>16;w=(((y+n|0)*11585|0)+8192|0)>>>14<<16>>16;z=((k*15137|0)+8192+(x*6270|0)|0)>>>14<<16>>16;y=(((n-y|0)*11585|0)+8192|0)>>>14<<16>>16;x=((k*6270|0)+8192+(x*-15137|0)|0)>>>14<<16>>16;k=v-r<<16>>16;n=m-l<<16>>16;s=o+s<<16>>16;q=u+q<<16>>16;u=((t*6270|0)+8192+(h*-15137|0)|0)>>>14<<16>>16;o=((p*-15137|0)+8192+(e*-6270|0)|0)>>>14<<16>>16;a=j+g<<16>>16;f=d+f<<16>>16;e=((p*6270|0)+8192+(e*-15137|0)|0)>>>14<<16>>16;h=((t*15137|0)+8192+(h*6270|0)|0)>>>14<<16>>16;t=z+w<<16>>16;r=v+r<<16>>16;v=x+y<<16>>16;p=(((n+k|0)*11585|0)+8192|0)>>>14<<16>>16;x=y-x<<16>>16;n=(((k-n|0)*11585|0)+8192|0)>>>14<<16>>16;z=w-z<<16>>16;l=m+l<<16>>16;m=u-o<<16>>16;w=h-e<<16>>16;k=s-q<<16>>16;y=f-a<<16>>16;d=r+t<<16>>16;a=f+a<<16>>16;b[c>>1]=a+d;f=p+v<<16>>16;e=h+e<<16>>16;b[c+2>>1]=e+f;h=n+x<<16>>16;g=(((m+w|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+4>>1]=g+h;j=l+z<<16>>16;i=(((k+y|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+6>>1]=i+j;l=z-l<<16>>16;k=(((y-k|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+8>>1]=k+l;n=x-n<<16>>16;m=(((w-m|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+10>>1]=m+n;p=v-p<<16>>16;o=u+o<<16>>16;b[c+12>>1]=o+p;r=t-r<<16>>16;q=s+q<<16>>16;b[c+14>>1]=q+r;b[c+16>>1]=r-q;b[c+18>>1]=p-o;b[c+20>>1]=n-m;b[c+22>>1]=l-k;b[c+24>>1]=j-i;b[c+26>>1]=h-g;b[c+28>>1]=f-e;b[c+30>>1]=d-a;return}function Ye(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0;aa=L;L=L+576|0;Z=aa+64|0;_=aa+32|0;$=aa;Xe(c,Z);Xe(c+32|0,Z+32|0);Xe(c+64|0,Z+64|0);Xe(c+96|0,Z+96|0);Xe(c+128|0,Z+128|0);Xe(c+160|0,Z+160|0);Xe(c+192|0,Z+192|0);Xe(c+224|0,Z+224|0);Xe(c+256|0,Z+256|0);Xe(c+288|0,Z+288|0);Xe(c+320|0,Z+320|0);Xe(c+352|0,Z+352|0);Xe(c+384|0,Z+384|0);Xe(c+416|0,Z+416|0);Xe(c+448|0,Z+448|0);Xe(c+480|0,Z+480|0);g=_+2|0;h=_+4|0;i=_+6|0;j=_+8|0;k=_+10|0;l=_+12|0;m=_+14|0;n=_+16|0;o=_+18|0;p=_+20|0;q=_+22|0;r=_+24|0;s=_+26|0;t=_+28|0;u=_+30|0;v=$+2|0;w=f<<1;x=$+4|0;y=f*3|0;z=$+6|0;A=f<<2;B=$+8|0;C=f*5|0;D=$+10|0;E=f*6|0;F=$+12|0;G=f*7|0;H=$+14|0;I=f<<3;J=$+16|0;K=f*9|0;M=$+18|0;N=f*10|0;O=$+20|0;P=f*11|0;Q=$+22|0;R=f*12|0;S=$+24|0;T=f*13|0;U=$+26|0;V=f*14|0;W=$+28|0;X=f*15|0;Y=$+30|0;c=0;do{b[_>>1]=b[Z+(c<<1)>>1]|0;b[g>>1]=b[Z+(c+16<<1)>>1]|0;b[h>>1]=b[Z+(c+32<<1)>>1]|0;b[i>>1]=b[Z+(c+48<<1)>>1]|0;b[j>>1]=b[Z+(c+64<<1)>>1]|0;b[k>>1]=b[Z+(c+80<<1)>>1]|0;b[l>>1]=b[Z+(c+96<<1)>>1]|0;b[m>>1]=b[Z+(c+112<<1)>>1]|0;b[n>>1]=b[Z+(c+128<<1)>>1]|0;b[o>>1]=b[Z+(c+144<<1)>>1]|0;b[p>>1]=b[Z+(c+160<<1)>>1]|0;b[q>>1]=b[Z+(c+176<<1)>>1]|0;b[r>>1]=b[Z+(c+192<<1)>>1]|0;b[s>>1]=b[Z+(c+208<<1)>>1]|0;b[t>>1]=b[Z+(c+224<<1)>>1]|0;b[u>>1]=b[Z+(c+240<<1)>>1]|0;Xe(_,$);ba=e+c|0;ca=((b[$>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(c+f)|0;ca=((b[v>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(w+c)|0;ca=((b[x>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(y+c)|0;ca=((b[z>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(A+c)|0;ca=((b[B>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(C+c)|0;ca=((b[D>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(E+c)|0;ca=((b[F>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(G+c)|0;ca=((b[H>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(I+c)|0;ca=((b[J>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(K+c)|0;ca=((b[M>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(N+c)|0;ca=((b[O>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(P+c)|0;ca=((b[Q>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(R+c)|0;ca=((b[S>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(T+c)|0;ca=((b[U>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(V+c)|0;ca=((b[W>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(X+c)|0;ca=((b[Y>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;c=c+1|0}while((c|0)!=16);L=aa;return}function Ze(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0;aa=L;L=L+576|0;Z=aa+64|0;_=aa+32|0;$=aa;kg(Z|0,0,512)|0;Xe(c,Z);Xe(c+32|0,Z+32|0);Xe(c+64|0,Z+64|0);Xe(c+96|0,Z+96|0);Xe(c+128|0,Z+128|0);Xe(c+160|0,Z+160|0);Xe(c+192|0,Z+192|0);Xe(c+224|0,Z+224|0);g=_+2|0;h=_+4|0;i=_+6|0;j=_+8|0;k=_+10|0;l=_+12|0;m=_+14|0;n=_+16|0;o=_+18|0;p=_+20|0;q=_+22|0;r=_+24|0;s=_+26|0;t=_+28|0;u=_+30|0;v=$+2|0;w=f<<1;x=$+4|0;y=f*3|0;z=$+6|0;A=f<<2;B=$+8|0;C=f*5|0;D=$+10|0;E=f*6|0;F=$+12|0;G=f*7|0;H=$+14|0;I=f<<3;J=$+16|0;K=f*9|0;M=$+18|0;N=f*10|0;O=$+20|0;P=f*11|0;Q=$+22|0;R=f*12|0;S=$+24|0;T=f*13|0;U=$+26|0;V=f*14|0;W=$+28|0;X=f*15|0;Y=$+30|0;c=0;do{b[_>>1]=b[Z+(c<<1)>>1]|0;b[g>>1]=b[Z+(c+16<<1)>>1]|0;b[h>>1]=b[Z+(c+32<<1)>>1]|0;b[i>>1]=b[Z+(c+48<<1)>>1]|0;b[j>>1]=b[Z+(c+64<<1)>>1]|0;b[k>>1]=b[Z+(c+80<<1)>>1]|0;b[l>>1]=b[Z+(c+96<<1)>>1]|0;b[m>>1]=b[Z+(c+112<<1)>>1]|0;b[n>>1]=b[Z+(c+128<<1)>>1]|0;b[o>>1]=b[Z+(c+144<<1)>>1]|0;b[p>>1]=b[Z+(c+160<<1)>>1]|0;b[q>>1]=b[Z+(c+176<<1)>>1]|0;b[r>>1]=b[Z+(c+192<<1)>>1]|0;b[s>>1]=b[Z+(c+208<<1)>>1]|0;b[t>>1]=b[Z+(c+224<<1)>>1]|0;b[u>>1]=b[Z+(c+240<<1)>>1]|0;Xe(_,$);ba=e+c|0;ca=((b[$>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(c+f)|0;ca=((b[v>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(w+c)|0;ca=((b[x>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(y+c)|0;ca=((b[z>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(A+c)|0;ca=((b[B>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(C+c)|0;ca=((b[D>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(E+c)|0;ca=((b[F>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(G+c)|0;ca=((b[H>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(I+c)|0;ca=((b[J>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(K+c)|0;ca=((b[M>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(N+c)|0;ca=((b[O>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(P+c)|0;ca=((b[Q>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(R+c)|0;ca=((b[S>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(T+c)|0;ca=((b[U>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(V+c)|0;ca=((b[W>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(X+c)|0;ca=((b[Y>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;c=c+1|0}while((c|0)!=16);L=aa;return}function _e(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0;aa=L;L=L+576|0;Z=aa+64|0;_=aa+32|0;$=aa;kg(Z|0,0,512)|0;Xe(c,Z);Xe(c+32|0,Z+32|0);Xe(c+64|0,Z+64|0);Xe(c+96|0,Z+96|0);g=_+2|0;h=_+4|0;i=_+6|0;j=_+8|0;k=_+10|0;l=_+12|0;m=_+14|0;n=_+16|0;o=_+18|0;p=_+20|0;q=_+22|0;r=_+24|0;s=_+26|0;t=_+28|0;u=_+30|0;v=$+2|0;w=f<<1;x=$+4|0;y=f*3|0;z=$+6|0;A=f<<2;B=$+8|0;C=f*5|0;D=$+10|0;E=f*6|0;F=$+12|0;G=f*7|0;H=$+14|0;I=f<<3;J=$+16|0;K=f*9|0;M=$+18|0;N=f*10|0;O=$+20|0;P=f*11|0;Q=$+22|0;R=f*12|0;S=$+24|0;T=f*13|0;U=$+26|0;V=f*14|0;W=$+28|0;X=f*15|0;Y=$+30|0;c=0;do{b[_>>1]=b[Z+(c<<1)>>1]|0;b[g>>1]=b[Z+(c+16<<1)>>1]|0;b[h>>1]=b[Z+(c+32<<1)>>1]|0;b[i>>1]=b[Z+(c+48<<1)>>1]|0;b[j>>1]=b[Z+(c+64<<1)>>1]|0;b[k>>1]=b[Z+(c+80<<1)>>1]|0;b[l>>1]=b[Z+(c+96<<1)>>1]|0;b[m>>1]=b[Z+(c+112<<1)>>1]|0;b[n>>1]=b[Z+(c+128<<1)>>1]|0;b[o>>1]=b[Z+(c+144<<1)>>1]|0;b[p>>1]=b[Z+(c+160<<1)>>1]|0;b[q>>1]=b[Z+(c+176<<1)>>1]|0;b[r>>1]=b[Z+(c+192<<1)>>1]|0;b[s>>1]=b[Z+(c+208<<1)>>1]|0;b[t>>1]=b[Z+(c+224<<1)>>1]|0;b[u>>1]=b[Z+(c+240<<1)>>1]|0;Xe(_,$);ba=e+c|0;ca=((b[$>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(c+f)|0;ca=((b[v>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(w+c)|0;ca=((b[x>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(y+c)|0;ca=((b[z>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(A+c)|0;ca=((b[B>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(C+c)|0;ca=((b[D>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(E+c)|0;ca=((b[F>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(G+c)|0;ca=((b[H>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(I+c)|0;ca=((b[J>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(K+c)|0;ca=((b[M>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(N+c)|0;ca=((b[O>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(P+c)|0;ca=((b[Q>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(R+c)|0;ca=((b[S>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(T+c)|0;ca=((b[U>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(V+c)|0;ca=((b[W>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;ba=e+(X+c)|0;ca=((b[Y>>1]|0)+32>>6)+(d[ba>>0]|0)|0;ca=(ca|0)>0?ca:0;a[ba>>0]=(ca|0)<255?ca:255;c=c+1|0}while((c|0)!=16);L=aa;return}function $e(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0;h=(((((((b[c>>1]|0)*11585|0)+8192|0)>>>14<<16>>16)*11585|0)+8192|0)>>>14<<16>>16)+32>>6;g=0;c=e;while(1){e=h+(d[c>>0]|0)|0;e=(e|0)>0?e:0;a[c>>0]=(e|0)<255?e:255;e=c+1|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+2|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+3|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+4|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+5|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+6|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+7|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+8|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+9|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+10|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+11|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+12|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+13|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+14|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+15|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;g=g+1|0;if((g|0)==16)break;else c=c+f|0}return}function af(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0;N=b[a+2>>1]|0;l=b[a+62>>1]|0;S=b[a+34>>1]|0;D=b[a+30>>1]|0;g=b[a+18>>1]|0;n=b[a+46>>1]|0;X=b[a+50>>1]|0;j=b[a+14>>1]|0;v=b[a+10>>1]|0;f=b[a+54>>1]|0;y=b[a+42>>1]|0;V=b[a+22>>1]|0;w=b[a+26>>1]|0;m=b[a+38>>1]|0;t=b[a+58>>1]|0;p=b[a+6>>1]|0;e=b[a+4>>1]|0;i=b[a+60>>1]|0;u=b[a+36>>1]|0;L=b[a+28>>1]|0;d=b[a+20>>1]|0;J=b[a+44>>1]|0;W=b[a+52>>1]|0;H=b[a+12>>1]|0;G=((N*804|0)+8192+(l*-16364|0)|0)>>>14<<16>>16;M=((S*12140|0)+8192+(D*-11003|0)|0)>>>14<<16>>16;O=((g*7005|0)+8192+(n*-14811|0)|0)>>>14<<16>>16;E=((X*15426|0)+8192+(j*-5520|0)|0)>>>14<<16>>16;I=((v*3981|0)+8192+(f*-15893|0)|0)>>>14<<16>>16;C=((y*14053|0)+8192+(V*-8423|0)|0)>>>14<<16>>16;A=((w*9760|0)+8192+(m*-13160|0)|0)>>>14<<16>>16;K=((t*16207|0)+8192+(p*-2404|0)|0)>>>14<<16>>16;p=((t*2404|0)+8192+(p*16207|0)|0)>>>14<<16>>16;m=((w*13160|0)+8192+(m*9760|0)|0)>>>14<<16>>16;V=((y*8423|0)+8192+(V*14053|0)|0)>>>14<<16>>16;f=((v*15893|0)+8192+(f*3981|0)|0)>>>14<<16>>16;j=((X*5520|0)+8192+(j*15426|0)|0)>>>14<<16>>16;n=((g*14811|0)+8192+(n*7005|0)|0)>>>14<<16>>16;D=((S*11003|0)+8192+(D*12140|0)|0)>>>14<<16>>16;l=((N*16364|0)+8192+(l*804|0)|0)>>>14<<16>>16;N=b[a+8>>1]|0;S=b[a+56>>1]|0;g=b[a+40>>1]|0;X=b[a+24>>1]|0;v=((e*1606|0)+8192+(i*-16305|0)|0)>>>14<<16>>16;y=((u*12665|0)+8192+(L*-10394|0)|0)>>>14<<16>>16;w=((d*7723|0)+8192+(J*-14449|0)|0)>>>14<<16>>16;t=((W*15679|0)+8192+(H*-4756|0)|0)>>>14<<16>>16;H=((W*4756|0)+8192+(H*15679|0)|0)>>>14<<16>>16;J=((d*14449|0)+8192+(J*7723|0)|0)>>>14<<16>>16;L=((u*10394|0)+8192+(L*12665|0)|0)>>>14<<16>>16;i=((e*16305|0)+8192+(i*1606|0)|0)>>>14<<16>>16;e=G-M<<16>>16;u=l-D<<16>>16;d=E-O<<16>>16;W=j-n<<16>>16;h=I-C<<16>>16;B=f-V<<16>>16;k=K-A<<16>>16;Q=p-m<<16>>16;T=b[a>>1]|0;s=b[a+32>>1]|0;z=b[a+16>>1]|0;U=b[a+48>>1]|0;x=((N*3196|0)+8192+(S*-16069|0)|0)>>>14<<16>>16;R=((g*13623|0)+8192+(X*-9102|0)|0)>>>14<<16>>16;X=((g*9102|0)+8192+(X*13623|0)|0)>>>14<<16>>16;S=((N*16069|0)+8192+(S*3196|0)|0)>>>14<<16>>16;N=v-y<<16>>16;g=i-L<<16>>16;F=t-w<<16>>16;P=H-J<<16>>16;G=M+G<<16>>16;O=E+O<<16>>16;E=((u*3196|0)+8192+(e*-16069|0)|0)>>>14<<16>>16;M=((W*-16069|0)+8192+(d*-3196|0)|0)>>>14<<16>>16;I=C+I<<16>>16;A=K+A<<16>>16;K=((B*13623|0)+8192+(h*-9102|0)|0)>>>14<<16>>16;C=((Q*-9102|0)+8192+(k*-13623|0)|0)>>>14<<16>>16;m=p+m<<16>>16;f=V+f<<16>>16;k=((Q*13623|0)+8192+(k*-9102|0)|0)>>>14<<16>>16;h=((B*9102|0)+8192+(h*13623|0)|0)>>>14<<16>>16;n=j+n<<16>>16;a=D+l<<16>>16;d=((W*3196|0)+8192+(d*-16069|0)|0)>>>14<<16>>16;e=((u*16069|0)+8192+(e*3196|0)|0)>>>14<<16>>16;u=(((s+T|0)*11585|0)+8192|0)>>>14<<16>>16;W=((z*15137|0)+8192+(U*6270|0)|0)>>>14<<16>>16;s=(((T-s|0)*11585|0)+8192|0)>>>14<<16>>16;U=((z*6270|0)+8192+(U*-15137|0)|0)>>>14<<16>>16;z=S-X<<16>>16;T=x-R<<16>>16;y=v+y<<16>>16;t=w+t<<16>>16;w=((g*6270|0)+8192+(N*-15137|0)|0)>>>14<<16>>16;v=((P*-15137|0)+8192+(F*-6270|0)|0)>>>14<<16>>16;H=J+H<<16>>16;L=i+L<<16>>16;F=((P*6270|0)+8192+(F*-15137|0)|0)>>>14<<16>>16;N=((g*15137|0)+8192+(N*6270|0)|0)>>>14<<16>>16;g=E-M<<16>>16;P=e-d<<16>>16;i=G-O<<16>>16;J=a-n<<16>>16;l=A-I<<16>>16;D=m-f<<16>>16;j=C-K<<16>>16;B=k-h<<16>>16;Q=W+u<<16>>16;X=S+X<<16>>16;S=U+s<<16>>16;V=(((T+z|0)*11585|0)+8192|0)>>>14<<16>>16;U=s-U<<16>>16;T=(((z-T|0)*11585|0)+8192|0)>>>14<<16>>16;W=u-W<<16>>16;R=x+R<<16>>16;x=w-v<<16>>16;u=N-F<<16>>16;z=y-t<<16>>16;s=L-H<<16>>16;G=O+G<<16>>16;I=A+I<<16>>16;E=M+E<<16>>16;K=C+K<<16>>16;C=((P*6270|0)+8192+(g*-15137|0)|0)>>>14<<16>>16;M=((B*-15137|0)+8192+(j*-6270|0)|0)>>>14<<16>>16;A=((J*6270|0)+8192+(i*-15137|0)|0)>>>14<<16>>16;O=((D*-15137|0)+8192+(l*-6270|0)|0)>>>14<<16>>16;f=m+f<<16>>16;a=n+a<<16>>16;h=k+h<<16>>16;e=d+e<<16>>16;j=((B*6270|0)+8192+(j*-15137|0)|0)>>>14<<16>>16;g=((P*15137|0)+8192+(g*6270|0)|0)>>>14<<16>>16;l=((D*6270|0)+8192+(l*-15137|0)|0)>>>14<<16>>16;i=((J*15137|0)+8192+(i*6270|0)|0)>>>14<<16>>16;J=X+Q<<16>>16;H=L+H<<16>>16;L=V+S<<16>>16;F=N+F<<16>>16;N=T+U<<16>>16;D=(((x+u|0)*11585|0)+8192|0)>>>14<<16>>16;P=R+W<<16>>16;B=(((z+s|0)*11585|0)+8192|0)>>>14<<16>>16;R=W-R<<16>>16;z=(((s-z|0)*11585|0)+8192|0)>>>14<<16>>16;T=U-T<<16>>16;x=(((u-x|0)*11585|0)+8192|0)>>>14<<16>>16;V=S-V<<16>>16;v=w+v<<16>>16;X=Q-X<<16>>16;t=y+t<<16>>16;y=A-O<<16>>16;Q=i-l<<16>>16;w=C-M<<16>>16;S=g-j<<16>>16;u=E-K<<16>>16;U=e-h<<16>>16;s=G-I<<16>>16;W=a-f<<16>>16;d=H+J<<16>>16;a=f+a<<16>>16;b[c>>1]=a+d;f=F+L<<16>>16;e=h+e<<16>>16;b[c+2>>1]=e+f;h=D+N<<16>>16;g=j+g<<16>>16;b[c+4>>1]=g+h;j=B+P<<16>>16;i=l+i<<16>>16;b[c+6>>1]=i+j;l=z+R<<16>>16;k=(((y+Q|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+8>>1]=k+l;n=x+T<<16>>16;m=(((w+S|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+10>>1]=m+n;p=v+V<<16>>16;o=(((u+U|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+12>>1]=o+p;r=t+X<<16>>16;q=(((s+W|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+14>>1]=q+r;t=X-t<<16>>16;s=(((W-s|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+16>>1]=s+t;v=V-v<<16>>16;u=(((U-u|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+18>>1]=u+v;x=T-x<<16>>16;w=(((S-w|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+20>>1]=w+x;z=R-z<<16>>16;y=(((Q-y|0)*11585|0)+8192|0)>>>14<<16>>16;b[c+22>>1]=y+z;B=P-B<<16>>16;A=O+A<<16>>16;b[c+24>>1]=A+B;D=N-D<<16>>16;C=M+C<<16>>16;b[c+26>>1]=C+D;F=L-F<<16>>16;E=K+E<<16>>16;b[c+28>>1]=E+F;H=J-H<<16>>16;G=I+G<<16>>16;b[c+30>>1]=G+H;b[c+32>>1]=H-G;b[c+34>>1]=F-E;b[c+36>>1]=D-C;b[c+38>>1]=B-A;b[c+40>>1]=z-y;b[c+42>>1]=x-w;b[c+44>>1]=v-u;b[c+46>>1]=t-s;b[c+48>>1]=r-q;b[c+50>>1]=p-o;b[c+52>>1]=n-m;b[c+54>>1]=l-k;b[c+56>>1]=j-i;b[c+58>>1]=h-g;b[c+60>>1]=f-e;b[c+62>>1]=d-a;return}function bf(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0;Q=L;L=L+2176|0;N=Q+128|0;O=Q+64|0;P=Q;i=N;j=0;while(1){if(!((b[c+62>>1]|(b[c+60>>1]|(b[c+58>>1]|(b[c+56>>1]|(b[c+54>>1]|(b[c+52>>1]|(b[c+50>>1]|(b[c+48>>1]|(b[c+46>>1]|(b[c+44>>1]|(b[c+42>>1]|(b[c+40>>1]|(b[c+38>>1]|(b[c+36>>1]|(b[c+34>>1]|(b[c+32>>1]|(b[c+30>>1]|(b[c+28>>1]|(b[c+26>>1]|(b[c+24>>1]|(b[c+22>>1]|(b[c+20>>1]|(b[c+18>>1]|(b[c+16>>1]|(b[c+14>>1]|(b[c+12>>1]|(b[c+10>>1]|(b[c+8>>1]|(b[c+6>>1]|(b[c+4>>1]|(b[c+2>>1]|b[c>>1])))))))))))))))))))))))))))))))<<16>>16)){g=i;h=g+64|0;do{b[g>>1]=0;g=g+2|0}while((g|0)<(h|0))}else af(c,i);j=j+1|0;if((j|0)==32)break;else{i=i+64|0;c=c+64|0}}h=O+2|0;i=O+4|0;j=O+6|0;k=O+8|0;l=O+10|0;m=O+12|0;n=O+14|0;o=O+16|0;p=O+18|0;q=O+20|0;r=O+22|0;s=O+24|0;t=O+26|0;u=O+28|0;v=O+30|0;w=O+32|0;x=O+34|0;y=O+36|0;z=O+38|0;A=O+40|0;B=O+42|0;C=O+44|0;D=O+46|0;E=O+48|0;F=O+50|0;G=O+52|0;H=O+54|0;I=O+56|0;J=O+58|0;K=O+60|0;M=O+62|0;c=0;do{b[O>>1]=b[N+(c<<1)>>1]|0;b[h>>1]=b[N+(c+32<<1)>>1]|0;b[i>>1]=b[N+(c+64<<1)>>1]|0;b[j>>1]=b[N+(c+96<<1)>>1]|0;b[k>>1]=b[N+(c+128<<1)>>1]|0;b[l>>1]=b[N+(c+160<<1)>>1]|0;b[m>>1]=b[N+(c+192<<1)>>1]|0;b[n>>1]=b[N+(c+224<<1)>>1]|0;b[o>>1]=b[N+(c+256<<1)>>1]|0;b[p>>1]=b[N+(c+288<<1)>>1]|0;b[q>>1]=b[N+(c+320<<1)>>1]|0;b[r>>1]=b[N+(c+352<<1)>>1]|0;b[s>>1]=b[N+(c+384<<1)>>1]|0;b[t>>1]=b[N+(c+416<<1)>>1]|0;b[u>>1]=b[N+(c+448<<1)>>1]|0;b[v>>1]=b[N+(c+480<<1)>>1]|0;b[w>>1]=b[N+(c+512<<1)>>1]|0;b[x>>1]=b[N+(c+544<<1)>>1]|0;b[y>>1]=b[N+(c+576<<1)>>1]|0;b[z>>1]=b[N+(c+608<<1)>>1]|0;b[A>>1]=b[N+(c+640<<1)>>1]|0;b[B>>1]=b[N+(c+672<<1)>>1]|0;b[C>>1]=b[N+(c+704<<1)>>1]|0;b[D>>1]=b[N+(c+736<<1)>>1]|0;b[E>>1]=b[N+(c+768<<1)>>1]|0;b[F>>1]=b[N+(c+800<<1)>>1]|0;b[G>>1]=b[N+(c+832<<1)>>1]|0;b[H>>1]=b[N+(c+864<<1)>>1]|0;b[I>>1]=b[N+(c+896<<1)>>1]|0;b[J>>1]=b[N+(c+928<<1)>>1]|0;b[K>>1]=b[N+(c+960<<1)>>1]|0;b[M>>1]=b[N+(c+992<<1)>>1]|0;af(O,P);g=0;do{R=e+((g*f|0)+c)|0;S=((b[P+(g<<1)>>1]|0)+32>>6)+(d[R>>0]|0)|0;S=(S|0)>0?S:0;a[R>>0]=(S|0)<255?S:255;g=g+1|0}while((g|0)!=32);c=c+1|0}while((c|0)!=32);L=Q;return}function cf(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0;Q=L;L=L+2176|0;N=Q+128|0;O=Q+64|0;P=Q;kg(N|0,0,2048)|0;af(c,N);af(c+64|0,N+64|0);af(c+128|0,N+128|0);af(c+192|0,N+192|0);af(c+256|0,N+256|0);af(c+320|0,N+320|0);af(c+384|0,N+384|0);af(c+448|0,N+448|0);af(c+512|0,N+512|0);af(c+576|0,N+576|0);af(c+640|0,N+640|0);af(c+704|0,N+704|0);af(c+768|0,N+768|0);af(c+832|0,N+832|0);af(c+896|0,N+896|0);af(c+960|0,N+960|0);h=O+2|0;i=O+4|0;j=O+6|0;k=O+8|0;l=O+10|0;m=O+12|0;n=O+14|0;o=O+16|0;p=O+18|0;q=O+20|0;r=O+22|0;s=O+24|0;t=O+26|0;u=O+28|0;v=O+30|0;w=O+32|0;x=O+34|0;y=O+36|0;z=O+38|0;A=O+40|0;B=O+42|0;C=O+44|0;D=O+46|0;E=O+48|0;F=O+50|0;G=O+52|0;H=O+54|0;I=O+56|0;J=O+58|0;K=O+60|0;M=O+62|0;c=0;do{b[O>>1]=b[N+(c<<1)>>1]|0;b[h>>1]=b[N+(c+32<<1)>>1]|0;b[i>>1]=b[N+(c+64<<1)>>1]|0;b[j>>1]=b[N+(c+96<<1)>>1]|0;b[k>>1]=b[N+(c+128<<1)>>1]|0;b[l>>1]=b[N+(c+160<<1)>>1]|0;b[m>>1]=b[N+(c+192<<1)>>1]|0;b[n>>1]=b[N+(c+224<<1)>>1]|0;b[o>>1]=b[N+(c+256<<1)>>1]|0;b[p>>1]=b[N+(c+288<<1)>>1]|0;b[q>>1]=b[N+(c+320<<1)>>1]|0;b[r>>1]=b[N+(c+352<<1)>>1]|0;b[s>>1]=b[N+(c+384<<1)>>1]|0;b[t>>1]=b[N+(c+416<<1)>>1]|0;b[u>>1]=b[N+(c+448<<1)>>1]|0;b[v>>1]=b[N+(c+480<<1)>>1]|0;b[w>>1]=b[N+(c+512<<1)>>1]|0;b[x>>1]=b[N+(c+544<<1)>>1]|0;b[y>>1]=b[N+(c+576<<1)>>1]|0;b[z>>1]=b[N+(c+608<<1)>>1]|0;b[A>>1]=b[N+(c+640<<1)>>1]|0;b[B>>1]=b[N+(c+672<<1)>>1]|0;b[C>>1]=b[N+(c+704<<1)>>1]|0;b[D>>1]=b[N+(c+736<<1)>>1]|0;b[E>>1]=b[N+(c+768<<1)>>1]|0;b[F>>1]=b[N+(c+800<<1)>>1]|0;b[G>>1]=b[N+(c+832<<1)>>1]|0;b[H>>1]=b[N+(c+864<<1)>>1]|0;b[I>>1]=b[N+(c+896<<1)>>1]|0;b[J>>1]=b[N+(c+928<<1)>>1]|0;b[K>>1]=b[N+(c+960<<1)>>1]|0;b[M>>1]=b[N+(c+992<<1)>>1]|0;af(O,P);g=0;do{R=e+((g*f|0)+c)|0;S=((b[P+(g<<1)>>1]|0)+32>>6)+(d[R>>0]|0)|0;S=(S|0)>0?S:0;a[R>>0]=(S|0)<255?S:255;g=g+1|0}while((g|0)!=32);c=c+1|0}while((c|0)!=32);L=Q;return}function df(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0;Q=L;L=L+2176|0;N=Q+128|0;O=Q+64|0;P=Q;kg(N|0,0,2048)|0;af(c,N);af(c+64|0,N+64|0);af(c+128|0,N+128|0);af(c+192|0,N+192|0);af(c+256|0,N+256|0);af(c+320|0,N+320|0);af(c+384|0,N+384|0);af(c+448|0,N+448|0);h=O+2|0;i=O+4|0;j=O+6|0;k=O+8|0;l=O+10|0;m=O+12|0;n=O+14|0;o=O+16|0;p=O+18|0;q=O+20|0;r=O+22|0;s=O+24|0;t=O+26|0;u=O+28|0;v=O+30|0;w=O+32|0;x=O+34|0;y=O+36|0;z=O+38|0;A=O+40|0;B=O+42|0;C=O+44|0;D=O+46|0;E=O+48|0;F=O+50|0;G=O+52|0;H=O+54|0;I=O+56|0;J=O+58|0;K=O+60|0;M=O+62|0;c=0;do{b[O>>1]=b[N+(c<<1)>>1]|0;b[h>>1]=b[N+(c+32<<1)>>1]|0;b[i>>1]=b[N+(c+64<<1)>>1]|0;b[j>>1]=b[N+(c+96<<1)>>1]|0;b[k>>1]=b[N+(c+128<<1)>>1]|0;b[l>>1]=b[N+(c+160<<1)>>1]|0;b[m>>1]=b[N+(c+192<<1)>>1]|0;b[n>>1]=b[N+(c+224<<1)>>1]|0;b[o>>1]=b[N+(c+256<<1)>>1]|0;b[p>>1]=b[N+(c+288<<1)>>1]|0;b[q>>1]=b[N+(c+320<<1)>>1]|0;b[r>>1]=b[N+(c+352<<1)>>1]|0;b[s>>1]=b[N+(c+384<<1)>>1]|0;b[t>>1]=b[N+(c+416<<1)>>1]|0;b[u>>1]=b[N+(c+448<<1)>>1]|0;b[v>>1]=b[N+(c+480<<1)>>1]|0;b[w>>1]=b[N+(c+512<<1)>>1]|0;b[x>>1]=b[N+(c+544<<1)>>1]|0;b[y>>1]=b[N+(c+576<<1)>>1]|0;b[z>>1]=b[N+(c+608<<1)>>1]|0;b[A>>1]=b[N+(c+640<<1)>>1]|0;b[B>>1]=b[N+(c+672<<1)>>1]|0;b[C>>1]=b[N+(c+704<<1)>>1]|0;b[D>>1]=b[N+(c+736<<1)>>1]|0;b[E>>1]=b[N+(c+768<<1)>>1]|0;b[F>>1]=b[N+(c+800<<1)>>1]|0;b[G>>1]=b[N+(c+832<<1)>>1]|0;b[H>>1]=b[N+(c+864<<1)>>1]|0;b[I>>1]=b[N+(c+896<<1)>>1]|0;b[J>>1]=b[N+(c+928<<1)>>1]|0;b[K>>1]=b[N+(c+960<<1)>>1]|0;b[M>>1]=b[N+(c+992<<1)>>1]|0;af(O,P);g=0;do{R=e+((g*f|0)+c)|0;S=((b[P+(g<<1)>>1]|0)+32>>6)+(d[R>>0]|0)|0;S=(S|0)>0?S:0;a[R>>0]=(S|0)<255?S:255;g=g+1|0}while((g|0)!=32);c=c+1|0}while((c|0)!=32);L=Q;return}function ef(c,e,f){c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0;h=(((((((b[c>>1]|0)*11585|0)+8192|0)>>>14<<16>>16)*11585|0)+8192|0)>>>14<<16>>16)+32>>6;g=0;c=e;while(1){e=h+(d[c>>0]|0)|0;e=(e|0)>0?e:0;a[c>>0]=(e|0)<255?e:255;e=c+1|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+2|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+3|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+4|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+5|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+6|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+7|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+8|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+9|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+10|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+11|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+12|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+13|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+14|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+15|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+16|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+17|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+18|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+19|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+20|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+21|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+22|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+23|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+24|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+25|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+26|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+27|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+28|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+29|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+30|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;e=c+31|0;i=h+(d[e>>0]|0)|0;i=(i|0)>0?i:0;a[e>>0]=(i|0)<255?i:255;g=g+1|0;if((g|0)==32)break;else c=c+f|0}return}function ff(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0;f=c[d+300>>2]|0;h=c[d+296>>2]|0;d=(f|0)!=0;e=(h|0)!=0;if(!(d&e)){if(!(d|e)){b=1;return b|0}d=d?f:h;if((a[d+9>>0]|0)>=1){b=3;return b|0}b=(a[d+8>>0]|0)==(a[b+4096>>0]|0)&1;return b|0}g=(a[h+9>>0]|0)<1;if((a[f+9>>0]|0)>=1){if(!g){b=4;return b|0}d=a[h+8>>0]|0;if(d<<24>>24==(a[b+4096>>0]|0)){b=3;return b|0}b=d<<24>>24<1?3:2;return b|0}f=a[f+8>>0]|0;d=a[b+4096>>0]|0;e=f<<24>>24==d<<24>>24;if(g){b=(e^(a[h+8>>0]|0)==d<<24>>24)&1;return b|0}if(e){b=3;return b|0}b=f<<24>>24<1?3:2;return b|0}function gf(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=c[d+300>>2]|0;k=c[d+296>>2]|0;d=(h|0)!=0;e=(k|0)!=0;m=a[b+4096>>0]|0;l=(c[b+3924+(m<<24>>24<<2)>>2]|0)==0&1;if(!(d&e)){if(!(d|e)){b=2;return b|0}e=d?h:k;d=a[e+8>>0]|0;if(d<<24>>24<1){b=2;return b|0}if((a[e+9>>0]|0)<1){b=d<<24>>24==(a[b+4098>>0]|0)?0:3;return b|0}else{b=((a[e+8+l>>0]|0)!=(a[b+4098>>0]|0)&1)<<2;return b|0}}g=a[h+8>>0]|0;e=g<<24>>24>0;d=a[k+8>>0]|0;f=d<<24>>24>0;if(!(e|f)){b=2;return b|0}if(!(e&f)){m=e?h:k;b=((a[((a[m+9>>0]|0)<1?m+8|0:m+8+l|0)>>0]|0)!=(a[b+4098>>0]|0)&1)<<1|1;return b|0}i=(a[k+9>>0]|0)>0;j=(a[h+9>>0]|0)>0;if(j)f=a[h+8+l>>0]|0;else f=g;h=f<<24>>24;if(i)d=a[k+8+l>>0]|0;g=d<<24>>24;e=f<<24>>24==d<<24>>24;if(e?f<<24>>24==(a[b+4098>>0]|0):0){b=0;return b|0}if(i|j){if(i&j){b=e?4:2;return b|0}f=i?g:h;e=j?g:h;d=a[b+4098>>0]|0;if((f|0)!=(d|0)|(e|0)==(d|0))return ((e|0)!=(d|0)|(f|0)==(d|0)?4:2)|0;else{b=1;return b|0}}if(f<<24>>24==m<<24>>24?d<<24>>24==(a[b+4097>>0]|0):0){b=4;return b|0}if(d<<24>>24==m<<24>>24?f<<24>>24==(a[b+4097>>0]|0):0){b=4;return b|0}b=e?3:1;return b|0}function hf(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;e=c[b+300>>2]|0;i=c[b+296>>2]|0;b=(e|0)!=0;d=(i|0)!=0;if(!(b&d)){if(!(b|d)){i=2;return i|0}b=b?e:i;e=a[b+8>>0]|0;if(e<<24>>24<1){i=2;return i|0}d=a[b+9>>0]|0;b=e<<24>>24==1;if(d<<24>>24<1){i=(b&1)<<2;return i|0}if(b){i=2;return i|0}i=d<<24>>24==1?2:1;return i|0}h=a[e+8>>0]|0;b=h<<24>>24>0;g=a[i+8>>0]|0;d=g<<24>>24>0;if(!(b|d)){i=2;return i|0}if(!(b&d)){d=b?e:i;b=a[d+9>>0]|0;d=(a[d+8>>0]|0)==1;if(b<<24>>24<1){i=(d&1)<<2;return i|0}if(d){i=2;return i|0}i=b<<24>>24==1?2:1;return i|0}e=a[e+9>>0]|0;f=e<<24>>24>0;b=a[i+9>>0]|0;d=b<<24>>24>0;if(f&d){i=h<<24>>24==1|e<<24>>24==1|g<<24>>24==1|b<<24>>24==1?2:1;return i|0}if(f|d){h=h<<24>>24;g=g<<24>>24;i=((f?h:g)|0)==1?1:(f?e:b)<<24>>24==1;i=((f?g:h)|0)==1?(i?4:3):i&1;return i|0}else{i=(g<<24>>24==1&1)+(h<<24>>24==1&1)<<1;return i|0}return 0}function jf(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=c[b+300>>2]|0;f=c[b+296>>2]|0;b=(e|0)!=0;d=(f|0)!=0;if(!(b&d)){if(!(b|d)){j=2;return j|0}b=b?e:f;e=a[b+8>>0]|0;if(e<<24>>24<1){j=2;return j|0}d=a[b+9>>0]|0;f=d<<24>>24<1;do if(e<<24>>24==1){if(f){j=2;return j|0}}else{b=e<<24>>24==2;if(f){j=(b&1)<<2;return j|0}else{if(b)b=3;else break;return b|0}}while(0);j=d<<24>>24==2?3:0;return j|0}j=a[e+8>>0]|0;b=j<<24>>24>0;i=a[f+8>>0]|0;d=i<<24>>24>0;if(!(b|d)){j=2;return j|0}if(!(b&d)){d=b?e:f;b=a[d+9>>0]|0;d=a[d+8>>0]|0;if(b<<24>>24<1){j=d<<24>>24==1?3:(d<<24>>24==2&1)<<2;return j|0}if(d<<24>>24==2){j=3;return j|0}j=(b<<24>>24==2&1)<<1|1;return j|0}g=a[e+9>>0]|0;h=g<<24>>24>0;f=a[f+9>>0]|0;b=f<<24>>24>0;if(h&b){if(!(j<<24>>24==i<<24>>24?g<<24>>24==f<<24>>24:0)){j=2;return j|0}j=j<<24>>24==2|g<<24>>24==2?3:0;return j|0}if(h|b){b=j<<24>>24;e=i<<24>>24;d=h?e:b;e=h?b:e;b=h?g:f;if((d|0)!=2){j=((e|0)==2|b<<24>>24==2)&1;return ((d|0)==3?j:j<<1|1)|0}j=(e|0)==2|b<<24>>24==2?4:3;return j|0}b=j<<24>>24==1;d=i<<24>>24==1;if(b&d){j=3;return j|0}if(b|d){j=((b?i:j)<<24>>24==2&1)<<2;return j|0}else{j=(i<<24>>24==2&1)+(j<<24>>24==2&1)<<1;return j|0}return 0}function kf(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;w=L;L=L+16|0;n=w;do if(a>>>0<245){k=a>>>0<11?16:a+11&-8;a=k>>>3;m=c[8134]|0;d=m>>>a;if(d&3|0){b=(d&1^1)+a|0;a=32576+(b<<1<<2)|0;d=a+8|0;e=c[d>>2]|0;f=e+8|0;g=c[f>>2]|0;if((g|0)==(a|0))c[8134]=m&~(1<<b);else{c[g+12>>2]=a;c[d>>2]=g}v=b<<3;c[e+4>>2]=v|3;v=e+v+4|0;c[v>>2]=c[v>>2]|1;v=f;L=w;return v|0}l=c[8136]|0;if(k>>>0>l>>>0){if(d|0){b=2<<a;b=d<<a&(b|0-b);b=(b&0-b)+-1|0;i=b>>>12&16;b=b>>>i;d=b>>>5&8;b=b>>>d;g=b>>>2&4;b=b>>>g;a=b>>>1&2;b=b>>>a;e=b>>>1&1;e=(d|i|g|a|e)+(b>>>e)|0;b=32576+(e<<1<<2)|0;a=b+8|0;g=c[a>>2]|0;i=g+8|0;d=c[i>>2]|0;if((d|0)==(b|0)){a=m&~(1<<e);c[8134]=a}else{c[d+12>>2]=b;c[a>>2]=d;a=m}v=e<<3;h=v-k|0;c[g+4>>2]=k|3;f=g+k|0;c[f+4>>2]=h|1;c[g+v>>2]=h;if(l|0){e=c[8139]|0;b=l>>>3;d=32576+(b<<1<<2)|0;b=1<<b;if(!(a&b)){c[8134]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=e;c[b+12>>2]=e;c[e+8>>2]=b;c[e+12>>2]=d}c[8136]=h;c[8139]=f;v=i;L=w;return v|0}g=c[8135]|0;if(g){d=(g&0-g)+-1|0;f=d>>>12&16;d=d>>>f;e=d>>>5&8;d=d>>>e;h=d>>>2&4;d=d>>>h;i=d>>>1&2;d=d>>>i;j=d>>>1&1;j=c[32840+((e|f|h|i|j)+(d>>>j)<<2)>>2]|0;d=j;i=j;j=(c[j+4>>2]&-8)-k|0;while(1){a=c[d+16>>2]|0;if(!a){a=c[d+20>>2]|0;if(!a)break}h=(c[a+4>>2]&-8)-k|0;f=h>>>0<j>>>0;d=a;i=f?a:i;j=f?h:j}h=i+k|0;if(h>>>0>i>>>0){f=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=c[a>>2]|0;if(!b){a=i+16|0;b=c[a>>2]|0;if(!b){d=0;break}}while(1){e=b+20|0;d=c[e>>2]|0;if(!d){e=b+16|0;d=c[e>>2]|0;if(!d)break;else{b=d;a=e}}else{b=d;a=e}}c[a>>2]=0;d=b}else{d=c[i+8>>2]|0;c[d+12>>2]=b;c[b+8>>2]=d;d=b}while(0);do if(f|0){b=c[i+28>>2]|0;a=32840+(b<<2)|0;if((i|0)==(c[a>>2]|0)){c[a>>2]=d;if(!d){c[8135]=g&~(1<<b);break}}else{v=f+16|0;c[((c[v>>2]|0)==(i|0)?v:f+20|0)>>2]=d;if(!d)break}c[d+24>>2]=f;b=c[i+16>>2]|0;if(b|0){c[d+16>>2]=b;c[b+24>>2]=d}b=c[i+20>>2]|0;if(b|0){c[d+20>>2]=b;c[b+24>>2]=d}}while(0);if(j>>>0<16){v=j+k|0;c[i+4>>2]=v|3;v=i+v+4|0;c[v>>2]=c[v>>2]|1}else{c[i+4>>2]=k|3;c[h+4>>2]=j|1;c[h+j>>2]=j;if(l|0){e=c[8139]|0;b=l>>>3;d=32576+(b<<1<<2)|0;b=1<<b;if(!(b&m)){c[8134]=b|m;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=e;c[b+12>>2]=e;c[e+8>>2]=b;c[e+12>>2]=d}c[8136]=j;c[8139]=h}v=i+8|0;L=w;return v|0}else m=k}else m=k}else m=k}else if(a>>>0<=4294967231){a=a+11|0;k=a&-8;e=c[8135]|0;if(e){f=0-k|0;a=a>>>8;if(a)if(k>>>0>16777215)j=31;else{m=(a+1048320|0)>>>16&8;q=a<<m;i=(q+520192|0)>>>16&4;q=q<<i;j=(q+245760|0)>>>16&2;j=14-(i|m|j)+(q<<j>>>15)|0;j=k>>>(j+7|0)&1|j<<1}else j=0;d=c[32840+(j<<2)>>2]|0;a:do if(!d){d=0;a=0;q=61}else{a=0;i=k<<((j|0)==31?0:25-(j>>>1)|0);g=0;while(1){h=(c[d+4>>2]&-8)-k|0;if(h>>>0<f>>>0)if(!h){a=d;f=0;q=65;break a}else{a=d;f=h}q=c[d+20>>2]|0;d=c[d+16+(i>>>31<<2)>>2]|0;g=(q|0)==0|(q|0)==(d|0)?g:q;if(!d){d=g;q=61;break}else i=i<<1}}while(0);if((q|0)==61){if((d|0)==0&(a|0)==0){a=2<<j;a=(a|0-a)&e;if(!a){m=k;break}m=(a&0-a)+-1|0;h=m>>>12&16;m=m>>>h;g=m>>>5&8;m=m>>>g;i=m>>>2&4;m=m>>>i;j=m>>>1&2;m=m>>>j;d=m>>>1&1;a=0;d=c[32840+((g|h|i|j|d)+(m>>>d)<<2)>>2]|0}if(!d){i=a;h=f}else q=65}if((q|0)==65){g=d;while(1){m=(c[g+4>>2]&-8)-k|0;d=m>>>0<f>>>0;f=d?m:f;a=d?g:a;d=c[g+16>>2]|0;if(!d)d=c[g+20>>2]|0;if(!d){i=a;h=f;break}else g=d}}if(((i|0)!=0?h>>>0<((c[8136]|0)-k|0)>>>0:0)?(l=i+k|0,l>>>0>i>>>0):0){g=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=c[a>>2]|0;if(!b){a=i+16|0;b=c[a>>2]|0;if(!b){b=0;break}}while(1){f=b+20|0;d=c[f>>2]|0;if(!d){f=b+16|0;d=c[f>>2]|0;if(!d)break;else{b=d;a=f}}else{b=d;a=f}}c[a>>2]=0}else{v=c[i+8>>2]|0;c[v+12>>2]=b;c[b+8>>2]=v}while(0);do if(g){a=c[i+28>>2]|0;d=32840+(a<<2)|0;if((i|0)==(c[d>>2]|0)){c[d>>2]=b;if(!b){e=e&~(1<<a);c[8135]=e;break}}else{v=g+16|0;c[((c[v>>2]|0)==(i|0)?v:g+20|0)>>2]=b;if(!b)break}c[b+24>>2]=g;a=c[i+16>>2]|0;if(a|0){c[b+16>>2]=a;c[a+24>>2]=b}a=c[i+20>>2]|0;if(a){c[b+20>>2]=a;c[a+24>>2]=b}}while(0);b:do if(h>>>0<16){v=h+k|0;c[i+4>>2]=v|3;v=i+v+4|0;c[v>>2]=c[v>>2]|1}else{c[i+4>>2]=k|3;c[l+4>>2]=h|1;c[l+h>>2]=h;b=h>>>3;if(h>>>0<256){d=32576+(b<<1<<2)|0;a=c[8134]|0;b=1<<b;if(!(a&b)){c[8134]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=l;c[b+12>>2]=l;c[l+8>>2]=b;c[l+12>>2]=d;break}b=h>>>8;if(b)if(h>>>0>16777215)d=31;else{u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;d=(v+245760|0)>>>16&2;d=14-(t|u|d)+(v<<d>>>15)|0;d=h>>>(d+7|0)&1|d<<1}else d=0;b=32840+(d<<2)|0;c[l+28>>2]=d;a=l+16|0;c[a+4>>2]=0;c[a>>2]=0;a=1<<d;if(!(e&a)){c[8135]=e|a;c[b>>2]=l;c[l+24>>2]=b;c[l+12>>2]=l;c[l+8>>2]=l;break}b=c[b>>2]|0;c:do if((c[b+4>>2]&-8|0)!=(h|0)){e=h<<((d|0)==31?0:25-(d>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(h|0)){b=a;break c}else{e=e<<1;b=a}}c[d>>2]=l;c[l+24>>2]=b;c[l+12>>2]=l;c[l+8>>2]=l;break b}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=l;c[u>>2]=l;c[l+8>>2]=v;c[l+12>>2]=b;c[l+24>>2]=0}while(0);v=i+8|0;L=w;return v|0}else m=k}else m=k}else m=-1;while(0);d=c[8136]|0;if(d>>>0>=m>>>0){b=d-m|0;a=c[8139]|0;if(b>>>0>15){v=a+m|0;c[8139]=v;c[8136]=b;c[v+4>>2]=b|1;c[a+d>>2]=b;c[a+4>>2]=m|3}else{c[8136]=0;c[8139]=0;c[a+4>>2]=d|3;v=a+d+4|0;c[v>>2]=c[v>>2]|1}v=a+8|0;L=w;return v|0}h=c[8137]|0;if(h>>>0>m>>>0){t=h-m|0;c[8137]=t;v=c[8140]|0;u=v+m|0;c[8140]=u;c[u+4>>2]=t|1;c[v+4>>2]=m|3;v=v+8|0;L=w;return v|0}if(!(c[8252]|0)){c[8254]=4096;c[8253]=4096;c[8255]=-1;c[8256]=-1;c[8257]=0;c[8245]=0;c[8252]=n&-16^1431655768;a=4096}else a=c[8254]|0;i=m+48|0;j=m+47|0;g=a+j|0;f=0-a|0;k=g&f;if(k>>>0<=m>>>0){v=0;L=w;return v|0}a=c[8244]|0;if(a|0?(l=c[8242]|0,n=l+k|0,n>>>0<=l>>>0|n>>>0>a>>>0):0){v=0;L=w;return v|0}d:do if(!(c[8245]&4)){d=c[8140]|0;e:do if(d){e=32984;while(1){n=c[e>>2]|0;if(n>>>0<=d>>>0?(n+(c[e+4>>2]|0)|0)>>>0>d>>>0:0)break;a=c[e+8>>2]|0;if(!a){q=128;break e}else e=a}b=g-h&f;if(b>>>0<2147483647){a=lg(b|0)|0;if((a|0)==((c[e>>2]|0)+(c[e+4>>2]|0)|0)){if((a|0)!=(-1|0)){h=b;g=a;q=145;break d}}else{e=a;q=136}}else b=0}else q=128;while(0);do if((q|0)==128){d=lg(0)|0;if((d|0)!=(-1|0)?(b=d,o=c[8253]|0,p=o+-1|0,b=((p&b|0)==0?0:(p+b&0-o)-b|0)+k|0,o=c[8242]|0,p=b+o|0,b>>>0>m>>>0&b>>>0<2147483647):0){n=c[8244]|0;if(n|0?p>>>0<=o>>>0|p>>>0>n>>>0:0){b=0;break}a=lg(b|0)|0;if((a|0)==(d|0)){h=b;g=d;q=145;break d}else{e=a;q=136}}else b=0}while(0);do if((q|0)==136){d=0-b|0;if(!(i>>>0>b>>>0&(b>>>0<2147483647&(e|0)!=(-1|0))))if((e|0)==(-1|0)){b=0;break}else{h=b;g=e;q=145;break d}a=c[8254]|0;a=j-b+a&0-a;if(a>>>0>=2147483647){h=b;g=e;q=145;break d}if((lg(a|0)|0)==(-1|0)){lg(d|0)|0;b=0;break}else{h=a+b|0;g=e;q=145;break d}}while(0);c[8245]=c[8245]|4;q=143}else{b=0;q=143}while(0);if(((q|0)==143?k>>>0<2147483647:0)?(t=lg(k|0)|0,p=lg(0)|0,r=p-t|0,s=r>>>0>(m+40|0)>>>0,!((t|0)==(-1|0)|s^1|t>>>0<p>>>0&((t|0)!=(-1|0)&(p|0)!=(-1|0))^1)):0){h=s?r:b;g=t;q=145}if((q|0)==145){b=(c[8242]|0)+h|0;c[8242]=b;if(b>>>0>(c[8243]|0)>>>0)c[8243]=b;j=c[8140]|0;f:do if(j){b=32984;while(1){a=c[b>>2]|0;d=c[b+4>>2]|0;if((g|0)==(a+d|0)){q=154;break}e=c[b+8>>2]|0;if(!e)break;else b=e}if(((q|0)==154?(u=b+4|0,(c[b+12>>2]&8|0)==0):0)?g>>>0>j>>>0&a>>>0<=j>>>0:0){c[u>>2]=d+h;v=(c[8137]|0)+h|0;t=j+8|0;t=(t&7|0)==0?0:0-t&7;u=j+t|0;t=v-t|0;c[8140]=u;c[8137]=t;c[u+4>>2]=t|1;c[j+v+4>>2]=40;c[8141]=c[8256];break}if(g>>>0<(c[8138]|0)>>>0)c[8138]=g;d=g+h|0;b=32984;while(1){if((c[b>>2]|0)==(d|0)){q=162;break}a=c[b+8>>2]|0;if(!a)break;else b=a}if((q|0)==162?(c[b+12>>2]&8|0)==0:0){c[b>>2]=g;l=b+4|0;c[l>>2]=(c[l>>2]|0)+h;l=g+8|0;l=g+((l&7|0)==0?0:0-l&7)|0;b=d+8|0;b=d+((b&7|0)==0?0:0-b&7)|0;k=l+m|0;i=b-l-m|0;c[l+4>>2]=m|3;g:do if((j|0)==(b|0)){v=(c[8137]|0)+i|0;c[8137]=v;c[8140]=k;c[k+4>>2]=v|1}else{if((c[8139]|0)==(b|0)){v=(c[8136]|0)+i|0;c[8136]=v;c[8139]=k;c[k+4>>2]=v|1;c[k+v>>2]=v;break}a=c[b+4>>2]|0;if((a&3|0)==1){h=a&-8;e=a>>>3;h:do if(a>>>0<256){a=c[b+8>>2]|0;d=c[b+12>>2]|0;if((d|0)==(a|0)){c[8134]=c[8134]&~(1<<e);break}else{c[a+12>>2]=d;c[d+8>>2]=a;break}}else{g=c[b+24>>2]|0;a=c[b+12>>2]|0;do if((a|0)==(b|0)){d=b+16|0;e=d+4|0;a=c[e>>2]|0;if(!a){a=c[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=c[f>>2]|0;if(!e){f=a+16|0;e=c[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}c[d>>2]=0}else{v=c[b+8>>2]|0;c[v+12>>2]=a;c[a+8>>2]=v}while(0);if(!g)break;d=c[b+28>>2]|0;e=32840+(d<<2)|0;do if((c[e>>2]|0)!=(b|0)){v=g+16|0;c[((c[v>>2]|0)==(b|0)?v:g+20|0)>>2]=a;if(!a)break h}else{c[e>>2]=a;if(a|0)break;c[8135]=c[8135]&~(1<<d);break h}while(0);c[a+24>>2]=g;d=b+16|0;e=c[d>>2]|0;if(e|0){c[a+16>>2]=e;c[e+24>>2]=a}d=c[d+4>>2]|0;if(!d)break;c[a+20>>2]=d;c[d+24>>2]=a}while(0);b=b+h|0;f=h+i|0}else f=i;b=b+4|0;c[b>>2]=c[b>>2]&-2;c[k+4>>2]=f|1;c[k+f>>2]=f;b=f>>>3;if(f>>>0<256){d=32576+(b<<1<<2)|0;a=c[8134]|0;b=1<<b;if(!(a&b)){c[8134]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=k;c[b+12>>2]=k;c[k+8>>2]=b;c[k+12>>2]=d;break}b=f>>>8;do if(!b)e=0;else{if(f>>>0>16777215){e=31;break}u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}while(0);b=32840+(e<<2)|0;c[k+28>>2]=e;a=k+16|0;c[a+4>>2]=0;c[a>>2]=0;a=c[8135]|0;d=1<<e;if(!(a&d)){c[8135]=a|d;c[b>>2]=k;c[k+24>>2]=b;c[k+12>>2]=k;c[k+8>>2]=k;break}b=c[b>>2]|0;i:do if((c[b+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(f|0)){b=a;break i}else{e=e<<1;b=a}}c[d>>2]=k;c[k+24>>2]=b;c[k+12>>2]=k;c[k+8>>2]=k;break g}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=k;c[u>>2]=k;c[k+8>>2]=v;c[k+12>>2]=b;c[k+24>>2]=0}while(0);v=l+8|0;L=w;return v|0}b=32984;while(1){a=c[b>>2]|0;if(a>>>0<=j>>>0?(v=a+(c[b+4>>2]|0)|0,v>>>0>j>>>0):0)break;b=c[b+8>>2]|0}f=v+-47|0;a=f+8|0;a=f+((a&7|0)==0?0:0-a&7)|0;f=j+16|0;a=a>>>0<f>>>0?j:a;b=a+8|0;d=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=d-t|0;c[8140]=u;c[8137]=t;c[u+4>>2]=t|1;c[g+d+4>>2]=40;c[8141]=c[8256];d=a+4|0;c[d>>2]=27;c[b>>2]=c[8246];c[b+4>>2]=c[8247];c[b+8>>2]=c[8248];c[b+12>>2]=c[8249];c[8246]=g;c[8247]=h;c[8249]=0;c[8248]=b;b=a+24|0;do{u=b;b=b+4|0;c[b>>2]=7}while((u+8|0)>>>0<v>>>0);if((a|0)!=(j|0)){g=a-j|0;c[d>>2]=c[d>>2]&-2;c[j+4>>2]=g|1;c[a>>2]=g;b=g>>>3;if(g>>>0<256){d=32576+(b<<1<<2)|0;a=c[8134]|0;b=1<<b;if(!(a&b)){c[8134]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=j;c[b+12>>2]=j;c[j+8>>2]=b;c[j+12>>2]=d;break}b=g>>>8;if(b)if(g>>>0>16777215)e=31;else{u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=g>>>(e+7|0)&1|e<<1}else e=0;d=32840+(e<<2)|0;c[j+28>>2]=e;c[j+20>>2]=0;c[f>>2]=0;b=c[8135]|0;a=1<<e;if(!(b&a)){c[8135]=b|a;c[d>>2]=j;c[j+24>>2]=d;c[j+12>>2]=j;c[j+8>>2]=j;break}b=c[d>>2]|0;j:do if((c[b+4>>2]&-8|0)!=(g|0)){e=g<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(g|0)){b=a;break j}else{e=e<<1;b=a}}c[d>>2]=j;c[j+24>>2]=b;c[j+12>>2]=j;c[j+8>>2]=j;break f}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=j;c[u>>2]=j;c[j+8>>2]=v;c[j+12>>2]=b;c[j+24>>2]=0}}else{v=c[8138]|0;if((v|0)==0|g>>>0<v>>>0)c[8138]=g;c[8246]=g;c[8247]=h;c[8249]=0;c[8143]=c[8252];c[8142]=-1;c[8147]=32576;c[8146]=32576;c[8149]=32584;c[8148]=32584;c[8151]=32592;c[8150]=32592;c[8153]=32600;c[8152]=32600;c[8155]=32608;c[8154]=32608;c[8157]=32616;c[8156]=32616;c[8159]=32624;c[8158]=32624;c[8161]=32632;c[8160]=32632;c[8163]=32640;c[8162]=32640;c[8165]=32648;c[8164]=32648;c[8167]=32656;c[8166]=32656;c[8169]=32664;c[8168]=32664;c[8171]=32672;c[8170]=32672;c[8173]=32680;c[8172]=32680;c[8175]=32688;c[8174]=32688;c[8177]=32696;c[8176]=32696;c[8179]=32704;c[8178]=32704;c[8181]=32712;c[8180]=32712;c[8183]=32720;c[8182]=32720;c[8185]=32728;c[8184]=32728;c[8187]=32736;c[8186]=32736;c[8189]=32744;c[8188]=32744;c[8191]=32752;c[8190]=32752;c[8193]=32760;c[8192]=32760;c[8195]=32768;c[8194]=32768;c[8197]=32776;c[8196]=32776;c[8199]=32784;c[8198]=32784;c[8201]=32792;c[8200]=32792;c[8203]=32800;c[8202]=32800;c[8205]=32808;c[8204]=32808;c[8207]=32816;c[8206]=32816;c[8209]=32824;c[8208]=32824;v=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=v-t|0;c[8140]=u;c[8137]=t;c[u+4>>2]=t|1;c[g+v+4>>2]=40;c[8141]=c[8256]}while(0);b=c[8137]|0;if(b>>>0>m>>>0){t=b-m|0;c[8137]=t;v=c[8140]|0;u=v+m|0;c[8140]=u;c[u+4>>2]=t|1;c[v+4>>2]=m|3;v=v+8|0;L=w;return v|0}}c[(pf()|0)>>2]=12;v=0;L=w;return v|0}function lf(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!a)return;d=a+-8|0;f=c[8138]|0;a=c[a+-4>>2]|0;b=a&-8;j=d+b|0;do if(!(a&1)){e=c[d>>2]|0;if(!(a&3))return;h=d+(0-e)|0;g=e+b|0;if(h>>>0<f>>>0)return;if((c[8139]|0)==(h|0)){a=j+4|0;b=c[a>>2]|0;if((b&3|0)!=3){i=h;b=g;break}c[8136]=g;c[a>>2]=b&-2;c[h+4>>2]=g|1;c[h+g>>2]=g;return}d=e>>>3;if(e>>>0<256){a=c[h+8>>2]|0;b=c[h+12>>2]|0;if((b|0)==(a|0)){c[8134]=c[8134]&~(1<<d);i=h;b=g;break}else{c[a+12>>2]=b;c[b+8>>2]=a;i=h;b=g;break}}f=c[h+24>>2]|0;a=c[h+12>>2]|0;do if((a|0)==(h|0)){b=h+16|0;d=b+4|0;a=c[d>>2]|0;if(!a){a=c[b>>2]|0;if(!a){a=0;break}}else b=d;while(1){e=a+20|0;d=c[e>>2]|0;if(!d){e=a+16|0;d=c[e>>2]|0;if(!d)break;else{a=d;b=e}}else{a=d;b=e}}c[b>>2]=0}else{i=c[h+8>>2]|0;c[i+12>>2]=a;c[a+8>>2]=i}while(0);if(f){b=c[h+28>>2]|0;d=32840+(b<<2)|0;if((c[d>>2]|0)==(h|0)){c[d>>2]=a;if(!a){c[8135]=c[8135]&~(1<<b);i=h;b=g;break}}else{i=f+16|0;c[((c[i>>2]|0)==(h|0)?i:f+20|0)>>2]=a;if(!a){i=h;b=g;break}}c[a+24>>2]=f;b=h+16|0;d=c[b>>2]|0;if(d|0){c[a+16>>2]=d;c[d+24>>2]=a}b=c[b+4>>2]|0;if(b){c[a+20>>2]=b;c[b+24>>2]=a;i=h;b=g}else{i=h;b=g}}else{i=h;b=g}}else{i=d;h=d}while(0);if(h>>>0>=j>>>0)return;a=j+4|0;e=c[a>>2]|0;if(!(e&1))return;if(!(e&2)){if((c[8140]|0)==(j|0)){j=(c[8137]|0)+b|0;c[8137]=j;c[8140]=i;c[i+4>>2]=j|1;if((i|0)!=(c[8139]|0))return;c[8139]=0;c[8136]=0;return}if((c[8139]|0)==(j|0)){j=(c[8136]|0)+b|0;c[8136]=j;c[8139]=h;c[i+4>>2]=j|1;c[h+j>>2]=j;return}f=(e&-8)+b|0;d=e>>>3;do if(e>>>0<256){b=c[j+8>>2]|0;a=c[j+12>>2]|0;if((a|0)==(b|0)){c[8134]=c[8134]&~(1<<d);break}else{c[b+12>>2]=a;c[a+8>>2]=b;break}}else{g=c[j+24>>2]|0;a=c[j+12>>2]|0;do if((a|0)==(j|0)){b=j+16|0;d=b+4|0;a=c[d>>2]|0;if(!a){a=c[b>>2]|0;if(!a){d=0;break}}else b=d;while(1){e=a+20|0;d=c[e>>2]|0;if(!d){e=a+16|0;d=c[e>>2]|0;if(!d)break;else{a=d;b=e}}else{a=d;b=e}}c[b>>2]=0;d=a}else{d=c[j+8>>2]|0;c[d+12>>2]=a;c[a+8>>2]=d;d=a}while(0);if(g|0){a=c[j+28>>2]|0;b=32840+(a<<2)|0;if((c[b>>2]|0)==(j|0)){c[b>>2]=d;if(!d){c[8135]=c[8135]&~(1<<a);break}}else{e=g+16|0;c[((c[e>>2]|0)==(j|0)?e:g+20|0)>>2]=d;if(!d)break}c[d+24>>2]=g;a=j+16|0;b=c[a>>2]|0;if(b|0){c[d+16>>2]=b;c[b+24>>2]=d}a=c[a+4>>2]|0;if(a|0){c[d+20>>2]=a;c[a+24>>2]=d}}}while(0);c[i+4>>2]=f|1;c[h+f>>2]=f;if((i|0)==(c[8139]|0)){c[8136]=f;return}}else{c[a>>2]=e&-2;c[i+4>>2]=b|1;c[h+b>>2]=b;f=b}a=f>>>3;if(f>>>0<256){d=32576+(a<<1<<2)|0;b=c[8134]|0;a=1<<a;if(!(b&a)){c[8134]=b|a;a=d;b=d+8|0}else{b=d+8|0;a=c[b>>2]|0}c[b>>2]=i;c[a+12>>2]=i;c[i+8>>2]=a;c[i+12>>2]=d;return}a=f>>>8;if(a)if(f>>>0>16777215)e=31;else{h=(a+1048320|0)>>>16&8;j=a<<h;g=(j+520192|0)>>>16&4;j=j<<g;e=(j+245760|0)>>>16&2;e=14-(g|h|e)+(j<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;a=32840+(e<<2)|0;c[i+28>>2]=e;c[i+20>>2]=0;c[i+16>>2]=0;b=c[8135]|0;d=1<<e;a:do if(!(b&d)){c[8135]=b|d;c[a>>2]=i;c[i+24>>2]=a;c[i+12>>2]=i;c[i+8>>2]=i}else{a=c[a>>2]|0;b:do if((c[a+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b)break;if((c[b+4>>2]&-8|0)==(f|0)){a=b;break b}else{e=e<<1;a=b}}c[d>>2]=i;c[i+24>>2]=a;c[i+12>>2]=i;c[i+8>>2]=i;break a}while(0);h=a+8|0;j=c[h>>2]|0;c[j+12>>2]=i;c[h>>2]=i;c[i+8>>2]=j;c[i+12>>2]=a;c[i+24>>2]=0}while(0);j=(c[8142]|0)+-1|0;c[8142]=j;if(j|0)return;a=32992;while(1){a=c[a>>2]|0;if(!a)break;else a=a+8|0}c[8142]=-1;return}function mf(a,b){a=a|0;b=b|0;var d=0,e=0;if(!a){b=kf(b)|0;return b|0}if(b>>>0>4294967231){c[(pf()|0)>>2]=12;b=0;return b|0}d=nf(a+-8|0,b>>>0<11?16:b+11&-8)|0;if(d|0){b=d+8|0;return b|0}d=kf(b)|0;if(!d){b=0;return b|0}e=c[a+-4>>2]|0;e=(e&-8)-((e&3|0)==0?8:4)|0;ig(d|0,a|0,(e>>>0<b>>>0?e:b)|0)|0;lf(a);b=d;return b|0}function nf(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=a+4|0;m=c[l>>2]|0;d=m&-8;i=a+d|0;if(!(m&3)){if(b>>>0<256){a=0;return a|0}if(d>>>0>=(b+4|0)>>>0?(d-b|0)>>>0<=c[8254]<<1>>>0:0)return a|0;a=0;return a|0}if(d>>>0>=b>>>0){d=d-b|0;if(d>>>0<=15)return a|0;k=a+b|0;c[l>>2]=m&1|b|2;c[k+4>>2]=d|3;m=i+4|0;c[m>>2]=c[m>>2]|1;of(k,d);return a|0}if((c[8140]|0)==(i|0)){k=(c[8137]|0)+d|0;d=k-b|0;e=a+b|0;if(k>>>0<=b>>>0){a=0;return a|0}c[l>>2]=m&1|b|2;c[e+4>>2]=d|1;c[8140]=e;c[8137]=d;return a|0}if((c[8139]|0)==(i|0)){e=(c[8136]|0)+d|0;if(e>>>0<b>>>0){a=0;return a|0}d=e-b|0;if(d>>>0>15){k=a+b|0;e=a+e|0;c[l>>2]=m&1|b|2;c[k+4>>2]=d|1;c[e>>2]=d;e=e+4|0;c[e>>2]=c[e>>2]&-2;e=k}else{c[l>>2]=m&1|e|2;e=a+e+4|0;c[e>>2]=c[e>>2]|1;e=0;d=0}c[8136]=d;c[8139]=e;return a|0}e=c[i+4>>2]|0;if(e&2|0){a=0;return a|0}j=(e&-8)+d|0;if(j>>>0<b>>>0){a=0;return a|0}k=j-b|0;f=e>>>3;do if(e>>>0<256){e=c[i+8>>2]|0;d=c[i+12>>2]|0;if((d|0)==(e|0)){c[8134]=c[8134]&~(1<<f);break}else{c[e+12>>2]=d;c[d+8>>2]=e;break}}else{h=c[i+24>>2]|0;d=c[i+12>>2]|0;do if((d|0)==(i|0)){e=i+16|0;f=e+4|0;d=c[f>>2]|0;if(!d){d=c[e>>2]|0;if(!d){f=0;break}}else e=f;while(1){g=d+20|0;f=c[g>>2]|0;if(!f){g=d+16|0;f=c[g>>2]|0;if(!f)break;else{d=f;e=g}}else{d=f;e=g}}c[e>>2]=0;f=d}else{f=c[i+8>>2]|0;c[f+12>>2]=d;c[d+8>>2]=f;f=d}while(0);if(h|0){d=c[i+28>>2]|0;e=32840+(d<<2)|0;if((c[e>>2]|0)==(i|0)){c[e>>2]=f;if(!f){c[8135]=c[8135]&~(1<<d);break}}else{g=h+16|0;c[((c[g>>2]|0)==(i|0)?g:h+20|0)>>2]=f;if(!f)break}c[f+24>>2]=h;d=i+16|0;e=c[d>>2]|0;if(e|0){c[f+16>>2]=e;c[e+24>>2]=f}d=c[d+4>>2]|0;if(d|0){c[f+20>>2]=d;c[d+24>>2]=f}}}while(0);if(k>>>0<16){c[l>>2]=m&1|j|2;m=a+j+4|0;c[m>>2]=c[m>>2]|1;return a|0}else{i=a+b|0;c[l>>2]=m&1|b|2;c[i+4>>2]=k|3;m=a+j+4|0;c[m>>2]=c[m>>2]|1;of(i,k);return a|0}return 0}function of(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;i=a+b|0;d=c[a+4>>2]|0;do if(!(d&1)){f=c[a>>2]|0;if(!(d&3))return;h=a+(0-f)|0;b=f+b|0;if((c[8139]|0)==(h|0)){a=i+4|0;d=c[a>>2]|0;if((d&3|0)!=3)break;c[8136]=b;c[a>>2]=d&-2;c[h+4>>2]=b|1;c[i>>2]=b;return}e=f>>>3;if(f>>>0<256){a=c[h+8>>2]|0;d=c[h+12>>2]|0;if((d|0)==(a|0)){c[8134]=c[8134]&~(1<<e);break}else{c[a+12>>2]=d;c[d+8>>2]=a;break}}g=c[h+24>>2]|0;a=c[h+12>>2]|0;do if((a|0)==(h|0)){d=h+16|0;e=d+4|0;a=c[e>>2]|0;if(!a){a=c[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=c[f>>2]|0;if(!e){f=a+16|0;e=c[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}c[d>>2]=0}else{f=c[h+8>>2]|0;c[f+12>>2]=a;c[a+8>>2]=f}while(0);if(g){d=c[h+28>>2]|0;e=32840+(d<<2)|0;if((c[e>>2]|0)==(h|0)){c[e>>2]=a;if(!a){c[8135]=c[8135]&~(1<<d);break}}else{f=g+16|0;c[((c[f>>2]|0)==(h|0)?f:g+20|0)>>2]=a;if(!a)break}c[a+24>>2]=g;d=h+16|0;e=c[d>>2]|0;if(e|0){c[a+16>>2]=e;c[e+24>>2]=a}d=c[d+4>>2]|0;if(d){c[a+20>>2]=d;c[d+24>>2]=a}}}else h=a;while(0);a=i+4|0;e=c[a>>2]|0;if(!(e&2)){if((c[8140]|0)==(i|0)){i=(c[8137]|0)+b|0;c[8137]=i;c[8140]=h;c[h+4>>2]=i|1;if((h|0)!=(c[8139]|0))return;c[8139]=0;c[8136]=0;return}if((c[8139]|0)==(i|0)){i=(c[8136]|0)+b|0;c[8136]=i;c[8139]=h;c[h+4>>2]=i|1;c[h+i>>2]=i;return}f=(e&-8)+b|0;d=e>>>3;do if(e>>>0<256){a=c[i+8>>2]|0;b=c[i+12>>2]|0;if((b|0)==(a|0)){c[8134]=c[8134]&~(1<<d);break}else{c[a+12>>2]=b;c[b+8>>2]=a;break}}else{g=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+16|0;d=a+4|0;b=c[d>>2]|0;if(!b){b=c[a>>2]|0;if(!b){d=0;break}}else a=d;while(1){e=b+20|0;d=c[e>>2]|0;if(!d){e=b+16|0;d=c[e>>2]|0;if(!d)break;else{b=d;a=e}}else{b=d;a=e}}c[a>>2]=0;d=b}else{d=c[i+8>>2]|0;c[d+12>>2]=b;c[b+8>>2]=d;d=b}while(0);if(g|0){b=c[i+28>>2]|0;a=32840+(b<<2)|0;if((c[a>>2]|0)==(i|0)){c[a>>2]=d;if(!d){c[8135]=c[8135]&~(1<<b);break}}else{e=g+16|0;c[((c[e>>2]|0)==(i|0)?e:g+20|0)>>2]=d;if(!d)break}c[d+24>>2]=g;b=i+16|0;a=c[b>>2]|0;if(a|0){c[d+16>>2]=a;c[a+24>>2]=d}b=c[b+4>>2]|0;if(b|0){c[d+20>>2]=b;c[b+24>>2]=d}}}while(0);c[h+4>>2]=f|1;c[h+f>>2]=f;if((h|0)==(c[8139]|0)){c[8136]=f;return}}else{c[a>>2]=e&-2;c[h+4>>2]=b|1;c[h+b>>2]=b;f=b}b=f>>>3;if(f>>>0<256){d=32576+(b<<1<<2)|0;a=c[8134]|0;b=1<<b;if(!(a&b)){c[8134]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=h;c[b+12>>2]=h;c[h+8>>2]=b;c[h+12>>2]=d;return}b=f>>>8;if(b)if(f>>>0>16777215)e=31;else{g=(b+1048320|0)>>>16&8;i=b<<g;d=(i+520192|0)>>>16&4;i=i<<d;e=(i+245760|0)>>>16&2;e=14-(d|g|e)+(i<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;b=32840+(e<<2)|0;c[h+28>>2]=e;c[h+20>>2]=0;c[h+16>>2]=0;a=c[8135]|0;d=1<<e;if(!(a&d)){c[8135]=a|d;c[b>>2]=h;c[h+24>>2]=b;c[h+12>>2]=h;c[h+8>>2]=h;return}b=c[b>>2]|0;a:do if((c[b+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(f|0)){b=a;break a}else{e=e<<1;b=a}}c[d>>2]=h;c[h+24>>2]=b;c[h+12>>2]=h;c[h+8>>2]=h;return}while(0);g=b+8|0;i=c[g>>2]|0;c[i+12>>2]=h;c[g>>2]=h;c[h+8>>2]=i;c[h+12>>2]=b;c[h+24>>2]=0;return}function pf(){return 33096}function qf(a){a=a|0;return (a+-48|0)>>>0<10|0}function rf(){return 29764}function sf(a){a=a|0;return}function tf(a){a=a|0;return 1}function uf(b){b=b|0;var d=0,e=0;d=b+74|0;e=a[d>>0]|0;a[d>>0]=e+255|e;d=c[b>>2]|0;if(!(d&8)){c[b+8>>2]=0;c[b+4>>2]=0;e=c[b+44>>2]|0;c[b+28>>2]=e;c[b+20>>2]=e;c[b+16>>2]=e+(c[b+48>>2]|0);b=0}else{c[b>>2]=d|32;b=-1}return b|0}function vf(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0;f=e+16|0;g=c[f>>2]|0;if(!g)if(!(uf(e)|0)){g=c[f>>2]|0;h=5}else f=0;else h=5;a:do if((h|0)==5){j=e+20|0;i=c[j>>2]|0;f=i;if((g-i|0)>>>0<d>>>0){f=R[c[e+36>>2]&3](e,b,d)|0;break}b:do if((a[e+75>>0]|0)<0|(d|0)==0){h=0;g=b}else{i=d;while(1){g=i+-1|0;if((a[b+g>>0]|0)==10)break;if(!g){h=0;g=b;break b}else i=g}f=R[c[e+36>>2]&3](e,b,i)|0;if(f>>>0<i>>>0)break a;h=i;g=b+i|0;d=d-i|0;f=c[j>>2]|0}while(0);ig(f|0,g|0,d|0)|0;c[j>>2]=(c[j>>2]|0)+d;f=h+d|0}while(0);return f|0}function wf(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=d&255;f=(e|0)!=0;a:do if(f&(b&3|0)!=0){g=d&255;while(1){if((a[b>>0]|0)==g<<24>>24){i=6;break a}b=b+1|0;e=e+-1|0;f=(e|0)!=0;if(!(f&(b&3|0)!=0)){i=5;break}}}else i=5;while(0);if((i|0)==5)if(f)i=6;else i=16;b:do if((i|0)==6){g=d&255;if((a[b>>0]|0)==g<<24>>24)if(!e){i=16;break}else break;f=h*16843009|0;c:do if(e>>>0>3)while(1){h=c[b>>2]^f;if((h&-2139062144^-2139062144)&h+-16843009|0)break c;b=b+4|0;e=e+-4|0;if(e>>>0<=3){i=11;break}}else i=11;while(0);if((i|0)==11)if(!e){i=16;break}while(1){if((a[b>>0]|0)==g<<24>>24)break b;e=e+-1|0;if(!e){i=16;break}else b=b+1|0}}while(0);if((i|0)==16)b=0;return b|0}function xf(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;r=L;L=L+224|0;m=r+208|0;n=r+160|0;p=r+80|0;q=r;f=n;g=f+40|0;do{c[f>>2]=0;f=f+4|0}while((f|0)<(g|0));c[m>>2]=c[e>>2];if((yf(0,d,m,p,n)|0)<0)e=-1;else{if((c[b+76>>2]|0)>-1)o=tf(b)|0;else o=0;e=c[b>>2]|0;l=e&32;if((a[b+74>>0]|0)<1)c[b>>2]=e&-33;f=b+48|0;if(!(c[f>>2]|0)){g=b+44|0;h=c[g>>2]|0;c[g>>2]=q;i=b+28|0;c[i>>2]=q;j=b+20|0;c[j>>2]=q;c[f>>2]=80;k=b+16|0;c[k>>2]=q+80;e=yf(b,d,m,p,n)|0;if(h){R[c[b+36>>2]&3](b,0,0)|0;e=(c[j>>2]|0)==0?-1:e;c[g>>2]=h;c[f>>2]=0;c[k>>2]=0;c[i>>2]=0;c[j>>2]=0}}else e=yf(b,d,m,p,n)|0;f=c[b>>2]|0;c[b>>2]=f|l;if(o|0)sf(b);e=(f&32|0)==0?e:-1}L=r;return e|0}function yf(d,e,f,h,i){d=d|0;e=e|0;f=f|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;H=L;L=L+64|0;C=H+56|0;D=H+40|0;z=H;F=H+48|0;G=H+60|0;c[C>>2]=e;w=(d|0)!=0;x=z+40|0;y=x;z=z+39|0;A=F+4|0;j=0;e=0;l=0;a:while(1){do{do if((e|0)>-1)if((j|0)>(2147483647-e|0)){c[(pf()|0)>>2]=75;e=-1;break}else{e=j+e|0;break}while(0);p=c[C>>2]|0;j=a[p>>0]|0;if(!(j<<24>>24)){u=92;break a}k=p;b:while(1){switch(j<<24>>24){case 37:{u=10;break b}case 0:{j=k;break b}default:{}}t=k+1|0;c[C>>2]=t;j=a[t>>0]|0;k=t}c:do if((u|0)==10){u=0;j=k;do{if((a[k+1>>0]|0)!=37)break c;j=j+1|0;k=k+2|0;c[C>>2]=k}while((a[k>>0]|0)==37)}while(0);j=j-p|0;if(w)zf(d,p,j)}while((j|0)!=0);t=(qf(a[(c[C>>2]|0)+1>>0]|0)|0)==0;k=c[C>>2]|0;if(!t?(a[k+2>>0]|0)==36:0){r=(a[k+1>>0]|0)+-48|0;n=1;j=3}else{r=-1;n=l;j=1}j=k+j|0;c[C>>2]=j;k=a[j>>0]|0;l=(k<<24>>24)+-32|0;if(l>>>0>31|(1<<l&75913|0)==0)m=0;else{m=0;do{m=1<<l|m;j=j+1|0;c[C>>2]=j;k=a[j>>0]|0;l=(k<<24>>24)+-32|0}while(!(l>>>0>31|(1<<l&75913|0)==0))}if(k<<24>>24==42){if((qf(a[j+1>>0]|0)|0)!=0?(E=c[C>>2]|0,(a[E+2>>0]|0)==36):0){j=E+1|0;c[i+((a[j>>0]|0)+-48<<2)>>2]=10;j=c[h+((a[j>>0]|0)+-48<<3)>>2]|0;l=1;k=E+3|0}else{if(n|0){e=-1;break}if(w){t=(c[f>>2]|0)+(4-1)&~(4-1);j=c[t>>2]|0;c[f>>2]=t+4}else j=0;l=0;k=(c[C>>2]|0)+1|0}c[C>>2]=k;t=(j|0)<0;s=t?0-j|0:j;m=t?m|8192:m;t=l}else{j=Af(C)|0;if((j|0)<0){e=-1;break}s=j;t=n;k=c[C>>2]|0}do if((a[k>>0]|0)==46){j=k+1|0;if((a[j>>0]|0)!=42){c[C>>2]=j;j=Af(C)|0;k=c[C>>2]|0;break}if(qf(a[k+2>>0]|0)|0?(B=c[C>>2]|0,(a[B+3>>0]|0)==36):0){j=B+2|0;c[i+((a[j>>0]|0)+-48<<2)>>2]=10;j=c[h+((a[j>>0]|0)+-48<<3)>>2]|0;k=B+4|0;c[C>>2]=k;break}if(t|0){e=-1;break a}if(w){q=(c[f>>2]|0)+(4-1)&~(4-1);j=c[q>>2]|0;c[f>>2]=q+4}else j=0;k=(c[C>>2]|0)+2|0;c[C>>2]=k}else j=-1;while(0);q=0;while(1){if(((a[k>>0]|0)+-65|0)>>>0>57){e=-1;break a}l=k;k=k+1|0;c[C>>2]=k;l=a[(a[l>>0]|0)+-65+(29184+(q*58|0))>>0]|0;n=l&255;if((n+-1|0)>>>0>=8)break;else q=n}if(!(l<<24>>24)){e=-1;break}o=(r|0)>-1;do if(l<<24>>24==19)if(o){e=-1;break a}else u=54;else{if(o){c[i+(r<<2)>>2]=n;o=h+(r<<3)|0;r=c[o+4>>2]|0;u=D;c[u>>2]=c[o>>2];c[u+4>>2]=r;u=54;break}if(!w){e=0;break a}Bf(D,n,f);k=c[C>>2]|0;u=55}while(0);if((u|0)==54){u=0;if(w)u=55;else j=0}d:do if((u|0)==55){u=0;k=a[k+-1>>0]|0;k=(q|0)!=0&(k&15|0)==3?k&-33:k;o=m&-65537;r=(m&8192|0)==0?m:o;e:do switch(k|0){case 110:switch((q&255)<<24>>24){case 0:{c[c[D>>2]>>2]=e;j=0;break d}case 1:{c[c[D>>2]>>2]=e;j=0;break d}case 2:{j=c[D>>2]|0;c[j>>2]=e;c[j+4>>2]=((e|0)<0)<<31>>31;j=0;break d}case 3:{b[c[D>>2]>>1]=e;j=0;break d}case 4:{a[c[D>>2]>>0]=e;j=0;break d}case 6:{c[c[D>>2]>>2]=e;j=0;break d}case 7:{j=c[D>>2]|0;c[j>>2]=e;c[j+4>>2]=((e|0)<0)<<31>>31;j=0;break d}default:{j=0;break d}}case 112:{k=120;j=j>>>0>8?j:8;l=r|8;u=67;break}case 88:case 120:{l=r;u=67;break}case 111:{l=D;k=c[l>>2]|0;l=c[l+4>>2]|0;p=Df(k,l,x)|0;o=y-p|0;m=0;n=32009;j=(r&8|0)==0|(j|0)>(o|0)?j:o+1|0;o=r;u=73;break}case 105:case 100:{l=D;k=c[l>>2]|0;l=c[l+4>>2]|0;if((l|0)<0){k=_f(0,0,k|0,l|0)|0;l=v()|0;m=D;c[m>>2]=k;c[m+4>>2]=l;m=1;n=32009;u=72;break e}else{m=(r&2049|0)!=0&1;n=(r&2048|0)==0?((r&1|0)==0?32009:32011):32010;u=72;break e}}case 117:{l=D;m=0;n=32009;k=c[l>>2]|0;l=c[l+4>>2]|0;u=72;break}case 99:{a[z>>0]=c[D>>2];p=z;m=0;n=32009;l=1;k=o;j=y;break}case 115:{q=c[D>>2]|0;q=(q|0)==0?32019:q;r=wf(q,0,j)|0;I=(r|0)==0;p=q;m=0;n=32009;l=I?j:r-q|0;k=o;j=I?q+j|0:r;break}case 67:{c[F>>2]=c[D>>2];c[A>>2]=0;c[D>>2]=F;n=-1;u=79;break}case 83:{if(!j){Ff(d,32,s,0,r);j=0;u=89}else{n=j;u=79}break}case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:{j=Hf(d,+g[D>>3],s,j,r,k)|0;break d}default:{m=0;n=32009;l=j;k=r;j=y}}while(0);f:do if((u|0)==67){I=D;r=c[I>>2]|0;I=c[I+4>>2]|0;p=Cf(r,I,x,k&32)|0;n=(l&8|0)==0|(r|0)==0&(I|0)==0;m=n?0:2;n=n?32009:32009+(k>>>4)|0;o=l;k=r;l=I;u=73}else if((u|0)==72){p=Ef(k,l,x)|0;o=r;u=73}else if((u|0)==79){u=0;m=c[D>>2]|0;j=0;while(1){k=c[m>>2]|0;if(!k)break;k=Gf(G,k)|0;l=(k|0)<0;if(l|k>>>0>(n-j|0)>>>0){u=83;break}j=k+j|0;if(n>>>0>j>>>0)m=m+4|0;else break}if((u|0)==83){u=0;if(l){e=-1;break a}}Ff(d,32,s,j,r);if(!j){j=0;u=89}else{l=c[D>>2]|0;m=0;while(1){k=c[l>>2]|0;if(!k){u=89;break f}k=Gf(G,k)|0;m=k+m|0;if((m|0)>(j|0)){u=89;break f}zf(d,G,k);if(m>>>0>=j>>>0){u=89;break}else l=l+4|0}}}while(0);if((u|0)==73){u=0;l=(k|0)!=0|(l|0)!=0;k=(j|0)!=0|l;l=y-p+((l^1)&1)|0;p=k?p:x;l=k?((j|0)>(l|0)?j:l):0;k=(j|0)>-1?o&-65537:o;j=y}else if((u|0)==89){u=0;Ff(d,32,s,j,r^8192);j=(s|0)>(j|0)?s:j;break}r=j-p|0;q=(l|0)<(r|0)?r:l;I=q+m|0;j=(s|0)<(I|0)?I:s;Ff(d,32,j,I,k);zf(d,n,m);Ff(d,48,j,I,k^65536);Ff(d,48,q,r,0);zf(d,p,r);Ff(d,32,j,I,k^8192)}while(0);l=t}g:do if((u|0)==92)if(!d)if(!l)e=0;else{e=1;while(1){j=c[i+(e<<2)>>2]|0;if(!j)break;Bf(h+(e<<3)|0,j,f);e=e+1|0;if(e>>>0>=10){e=1;break g}}while(1){if(c[i+(e<<2)>>2]|0){e=-1;break g}e=e+1|0;if(e>>>0>=10){e=1;break}}}while(0);L=H;return e|0}function zf(a,b,d){a=a|0;b=b|0;d=d|0;if(!(c[a>>2]&32))vf(b,d,a)|0;return}function Af(b){b=b|0;var d=0,e=0;if(!(qf(a[c[b>>2]>>0]|0)|0))d=0;else{d=0;do{e=c[b>>2]|0;d=(d*10|0)+-48+(a[e>>0]|0)|0;e=e+1|0;c[b>>2]=e}while((qf(a[e>>0]|0)|0)!=0)}return d|0}function Bf(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,h=0.0;a:do if(b>>>0<=20)do switch(b|0){case 9:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;c[a>>2]=b;break a}case 10:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=b;c[e+4>>2]=((b|0)<0)<<31>>31;break a}case 11:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=b;c[e+4>>2]=0;break a}case 12:{e=(c[d>>2]|0)+(8-1)&~(8-1);b=e;f=c[b>>2]|0;b=c[b+4>>2]|0;c[d>>2]=e+8;e=a;c[e>>2]=f;c[e+4>>2]=b;break a}case 13:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;e=(e&65535)<<16>>16;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a}case 14:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e&65535;c[f+4>>2]=0;break a}case 15:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;e=(e&255)<<24>>24;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a}case 16:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e&255;c[f+4>>2]=0;break a}case 17:{f=(c[d>>2]|0)+(8-1)&~(8-1);h=+g[f>>3];c[d>>2]=f+8;g[a>>3]=h;break a}case 18:{f=(c[d>>2]|0)+(8-1)&~(8-1);h=+g[f>>3];c[d>>2]=f+8;g[a>>3]=h;break a}default:break a}while(0);while(0);return}function Cf(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;if(!((b|0)==0&(c|0)==0))do{e=e+-1|0;a[e>>0]=d[29648+(b&15)>>0]|0|f;b=cg(b|0,c|0,4)|0;c=v()|0}while(!((b|0)==0&(c|0)==0));return e|0}function Df(b,c,d){b=b|0;c=c|0;d=d|0;if(!((b|0)==0&(c|0)==0))do{d=d+-1|0;a[d>>0]=b&7|48;b=cg(b|0,c|0,3)|0;c=v()|0}while(!((b|0)==0&(c|0)==0));return d|0}function Ef(b,c,d){b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;if(c>>>0>0|(c|0)==0&b>>>0>4294967295){do{e=b;b=bg(b|0,c|0,10,0)|0;f=c;c=v()|0;g=Yf(b|0,c|0,10,0)|0;g=_f(e|0,f|0,g|0,v()|0)|0;v()|0;d=d+-1|0;a[d>>0]=g&255|48}while(f>>>0>9|(f|0)==9&e>>>0>4294967295);c=b}else c=b;if(c)do{g=c;c=(c>>>0)/10|0;d=d+-1|0;a[d>>0]=g-(c*10|0)|48}while(g>>>0>=10);return d|0}function Ff(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0;g=L;L=L+256|0;f=g;if((c|0)>(d|0)&(e&73728|0)==0){e=c-d|0;kg(f|0,b<<24>>24|0,(e>>>0<256?e:256)|0)|0;if(e>>>0>255){b=c-d|0;do{zf(a,f,256);e=e+-256|0}while(e>>>0>255);e=b&255}zf(a,f,e)}L=g;return}function Gf(a,b){a=a|0;b=b|0;if(!a)a=0;else a=Lf(a,b,0)|0;return a|0}function Hf(b,e,f,g,h,i){b=b|0;e=+e;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;G=L;L=L+560|0;l=G+32|0;u=G+536|0;F=G;E=F;m=G+540|0;c[u>>2]=0;D=m+12|0;If(e)|0;j=v()|0;if((j|0)<0){e=-e;If(e)|0;C=1;B=32026;j=v()|0}else{C=(h&2049|0)!=0&1;B=(h&2048|0)==0?((h&1|0)==0?32027:32032):32029}do if(0==0&(j&2146435072|0)==2146435072){F=(i&32|0)!=0;j=C+3|0;Ff(b,32,f,j,h&-65537);zf(b,B,C);zf(b,e!=e|0.0!=0.0?(F?32053:32057):F?32045:32049,3);Ff(b,32,f,j,h^8192)}else{q=+Jf(e,u)*2.0;j=q!=0.0;if(j)c[u>>2]=(c[u>>2]|0)+-1;t=i|32;if((t|0)==97){o=i&32;r=(o|0)==0?B:B+9|0;p=C|2;j=12-g|0;do if(!(g>>>0>11|(j|0)==0)){e=8.0;do{j=j+-1|0;e=e*16.0}while((j|0)!=0);if((a[r>>0]|0)==45){e=-(e+(-q-e));break}else{e=q+e-e;break}}else e=q;while(0);k=c[u>>2]|0;j=(k|0)<0?0-k|0:k;j=Ef(j,((j|0)<0)<<31>>31,D)|0;if((j|0)==(D|0)){j=m+11|0;a[j>>0]=48}a[j+-1>>0]=(k>>31&2)+43;n=j+-2|0;a[n>>0]=i+15;k=(g|0)<1;l=(h&8|0)==0;m=F;do{C=~~e;j=m+1|0;a[m>>0]=o|d[29648+C>>0];e=(e-+(C|0))*16.0;if((j-E|0)==1?!(l&(k&e==0.0)):0){a[j>>0]=46;m=m+2|0}else m=j}while(e!=0.0);if((g|0)!=0?(-2-E+m|0)<(g|0):0){k=D;l=n;j=g+2+k-l|0}else{k=D;l=n;j=k-E-l+m|0}D=j+p|0;Ff(b,32,f,D,h);zf(b,r,p);Ff(b,48,f,D,h^65536);E=m-E|0;zf(b,F,E);F=k-l|0;Ff(b,48,j-(E+F)|0,0,0);zf(b,n,F);Ff(b,32,f,D,h^8192);j=D;break}k=(g|0)<0?6:g;if(j){j=(c[u>>2]|0)+-28|0;c[u>>2]=j;e=q*268435456.0}else{e=q;j=c[u>>2]|0}A=(j|0)<0?l:l+288|0;l=A;do{y=~~e>>>0;c[l>>2]=y;l=l+4|0;e=(e-+(y>>>0))*1.0e9}while(e!=0.0);y=A;if((j|0)>0){o=A;while(1){n=(j|0)<29?j:29;j=l+-4|0;if(j>>>0>=o>>>0){m=0;do{s=dg(c[j>>2]|0,0,n|0)|0;s=Zf(s|0,v()|0,m|0,0)|0;w=v()|0;m=bg(s|0,w|0,1e9,0)|0;x=Yf(m|0,v()|0,1e9,0)|0;x=_f(s|0,w|0,x|0,v()|0)|0;v()|0;c[j>>2]=x;j=j+-4|0}while(j>>>0>=o>>>0);if(m){x=o+-4|0;c[x>>2]=m;m=x}else m=o}else m=o;a:do if(l>>>0>m>>>0){j=l;while(1){l=j+-4|0;if(c[l>>2]|0){l=j;break a}if(l>>>0>m>>>0)j=l;else break}}while(0);j=(c[u>>2]|0)-n|0;c[u>>2]=j;if((j|0)>0)o=m;else break}}else m=A;if((j|0)<0){g=((k+25|0)/9|0)+1|0;s=(t|0)==102;do{r=0-j|0;r=(r|0)<9?r:9;if(m>>>0<l>>>0){n=(1<<r)+-1|0;o=1e9>>>r;p=0;j=m;do{x=c[j>>2]|0;c[j>>2]=(x>>>r)+p;p=(x&n)*o|0;j=j+4|0}while(j>>>0<l>>>0);m=(c[m>>2]|0)==0?m+4|0:m;if(p){c[l>>2]=p;l=l+4|0}}else m=(c[m>>2]|0)==0?m+4|0:m;j=s?A:m;l=(l-j>>2|0)>(g|0)?j+(g<<2)|0:l;j=(c[u>>2]|0)+r|0;c[u>>2]=j}while((j|0)<0);s=m}else s=m;if(s>>>0<l>>>0){j=(y-s>>2)*9|0;n=c[s>>2]|0;if(n>>>0>=10){m=10;do{m=m*10|0;j=j+1|0}while(n>>>0>=m>>>0)}}else j=0;w=(t|0)==103;x=(k|0)!=0;m=k-((t|0)==102?0:j)+((x&w)<<31>>31)|0;if((m|0)<(((l-y>>2)*9|0)+-9|0)){u=m+9216|0;m=(u|0)/9|0;g=A+4+(m+-1024<<2)|0;m=u-(m*9|0)|0;if((m|0)<8){n=10;while(1){n=n*10|0;if((m|0)<7)m=m+1|0;else break}}else n=10;p=c[g>>2]|0;m=(p>>>0)/(n>>>0)|0;r=p-(m*n|0)|0;o=(g+4|0)==(l|0);if(!(o&(r|0)==0)){q=(m&1|0)==0?9007199254740992.0:9007199254740994.0;u=n>>>1;e=r>>>0<u>>>0?.5:o&(r|0)==(u|0)?1.0:1.5;if(C){u=(a[B>>0]|0)==45;e=u?-e:e;q=u?-q:q}m=p-r|0;c[g>>2]=m;if(q+e!=q){u=m+n|0;c[g>>2]=u;if(u>>>0>999999999){n=g;j=s;while(1){m=n+-4|0;c[n>>2]=0;if(m>>>0<j>>>0){j=j+-4|0;c[j>>2]=0}u=(c[m>>2]|0)+1|0;c[m>>2]=u;if(u>>>0>999999999)n=m;else{n=j;break}}}else{m=g;n=s}j=(y-n>>2)*9|0;p=c[n>>2]|0;if(p>>>0>=10){o=10;do{o=o*10|0;j=j+1|0}while(p>>>0>=o>>>0)}}else{m=g;n=s}}else{m=g;n=s}u=m+4|0;l=l>>>0>u>>>0?u:l}else n=s;g=0-j|0;b:do if(l>>>0>n>>>0)while(1){m=l+-4|0;if(c[m>>2]|0){u=l;t=1;break b}if(m>>>0>n>>>0)l=m;else{u=m;t=0;break}}else{u=l;t=0}while(0);do if(w){k=k+((x^1)&1)|0;if((k|0)>(j|0)&(j|0)>-5){o=i+-1|0;k=k+-1-j|0}else{o=i+-2|0;k=k+-1|0}if(!(h&8)){if(t?(z=c[u+-4>>2]|0,(z|0)!=0):0)if(!((z>>>0)%10|0)){m=0;l=10;do{l=l*10|0;m=m+1|0}while(!((z>>>0)%(l>>>0)|0|0))}else m=0;else m=9;l=((u-y>>2)*9|0)+-9|0;if((o|32|0)==102){i=l-m|0;i=(i|0)>0?i:0;k=(k|0)<(i|0)?k:i;break}else{i=l+j-m|0;i=(i|0)>0?i:0;k=(k|0)<(i|0)?k:i;break}}}else o=i;while(0);s=(k|0)!=0;p=s?1:h>>>3&1;r=(o|32|0)==102;if(r){w=0;j=(j|0)>0?j:0}else{l=(j|0)<0?g:j;l=Ef(l,((l|0)<0)<<31>>31,D)|0;m=D;if((m-l|0)<2)do{l=l+-1|0;a[l>>0]=48}while((m-l|0)<2);a[l+-1>>0]=(j>>31&2)+43;j=l+-2|0;a[j>>0]=o;w=j;j=m-j|0}j=C+1+k+p+j|0;Ff(b,32,f,j,h);zf(b,B,C);Ff(b,48,f,j,h^65536);if(r){p=n>>>0>A>>>0?A:n;r=F+9|0;n=r;o=F+8|0;m=p;do{l=Ef(c[m>>2]|0,0,r)|0;if((m|0)==(p|0)){if((l|0)==(r|0)){a[o>>0]=48;l=o}}else if(l>>>0>F>>>0){kg(F|0,48,l-E|0)|0;do l=l+-1|0;while(l>>>0>F>>>0)}zf(b,l,n-l|0);m=m+4|0}while(m>>>0<=A>>>0);if(!((h&8|0)==0&(s^1)))zf(b,32061,1);if(m>>>0<u>>>0&(k|0)>0)while(1){l=Ef(c[m>>2]|0,0,r)|0;if(l>>>0>F>>>0){kg(F|0,48,l-E|0)|0;do l=l+-1|0;while(l>>>0>F>>>0)}zf(b,l,(k|0)<9?k:9);m=m+4|0;l=k+-9|0;if(!(m>>>0<u>>>0&(k|0)>9)){k=l;break}else k=l}Ff(b,48,k+9|0,9,0)}else{u=t?u:n+4|0;if(n>>>0<u>>>0&(k|0)>-1){g=F+9|0;s=(h&8|0)==0;t=g;p=0-E|0;r=F+8|0;o=n;do{l=Ef(c[o>>2]|0,0,g)|0;if((l|0)==(g|0)){a[r>>0]=48;l=r}do if((o|0)==(n|0)){m=l+1|0;zf(b,l,1);if(s&(k|0)<1){l=m;break}zf(b,32061,1);l=m}else{if(l>>>0<=F>>>0)break;kg(F|0,48,l+p|0)|0;do l=l+-1|0;while(l>>>0>F>>>0)}while(0);E=t-l|0;zf(b,l,(k|0)>(E|0)?E:k);k=k-E|0;o=o+4|0}while(o>>>0<u>>>0&(k|0)>-1)}Ff(b,48,k+18|0,18,0);zf(b,w,D-w|0)}Ff(b,32,f,j,h^8192)}while(0);L=G;return ((j|0)<(f|0)?f:j)|0}function If(a){a=+a;var b=0;g[h>>3]=a;b=c[h>>2]|0;u(c[h+4>>2]|0);return b|0}function Jf(a,b){a=+a;b=b|0;return +(+Kf(a,b))}function Kf(a,b){a=+a;b=b|0;var d=0,e=0,f=0;g[h>>3]=a;d=c[h>>2]|0;e=c[h+4>>2]|0;f=cg(d|0,e|0,52)|0;v()|0;switch(f&2047){case 0:{if(a!=0.0){a=+Kf(a*18446744073709551616.0,b);d=(c[b>>2]|0)+-64|0}else d=0;c[b>>2]=d;break}case 2047:break;default:{c[b>>2]=(f&2047)+-1022;c[h>>2]=d;c[h+4>>2]=e&-2146435073|1071644672;a=+g[h>>3]}}return +a}function Lf(b,d,e){b=b|0;d=d|0;e=e|0;do if(b){if(d>>>0<128){a[b>>0]=d;b=1;break}if(!(c[c[(Mf()|0)+188>>2]>>2]|0))if((d&-128|0)==57216){a[b>>0]=d;b=1;break}else{c[(pf()|0)>>2]=84;b=-1;break}if(d>>>0<2048){a[b>>0]=d>>>6|192;a[b+1>>0]=d&63|128;b=2;break}if(d>>>0<55296|(d&-8192|0)==57344){a[b>>0]=d>>>12|224;a[b+1>>0]=d>>>6&63|128;a[b+2>>0]=d&63|128;b=3;break}if((d+-65536|0)>>>0<1048576){a[b>>0]=d>>>18|240;a[b+1>>0]=d>>>12&63|128;a[b+2>>0]=d>>>6&63|128;a[b+3>>0]=d&63|128;b=4;break}else{c[(pf()|0)>>2]=84;b=-1;break}}else b=1;while(0);return b|0}function Mf(){return rf()|0}function Nf(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=L;L=L+128|0;g=m+124|0;l=m;h=l;i=30008;j=h+124|0;do{c[h>>2]=c[i>>2];h=h+4|0;i=i+4|0}while((h|0)<(j|0));if((d+-1|0)>>>0>2147483646)if(!d){b=g;d=1;k=4}else{c[(pf()|0)>>2]=75;d=-1}else k=4;if((k|0)==4){k=-2-b|0;k=d>>>0>k>>>0?k:d;c[l+48>>2]=k;g=l+20|0;c[g>>2]=b;c[l+44>>2]=b;d=b+k|0;b=l+16|0;c[b>>2]=d;c[l+28>>2]=d;d=xf(l,e,f)|0;if(k){l=c[g>>2]|0;a[l+(((l|0)==(c[b>>2]|0))<<31>>31)>>0]=0}}L=m;return d|0}function Of(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0;e=a+20|0;f=c[e>>2]|0;a=(c[a+16>>2]|0)-f|0;a=a>>>0>d>>>0?d:a;ig(f|0,b|0,a|0)|0;c[e>>2]=(c[e>>2]|0)+a;return d|0}function Pf(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;m=L;L=L+208|0;j=m;k=m+192|0;h=d*b|0;i=k;c[i>>2]=1;c[i+4>>2]=0;a:do if(h|0){i=0-d|0;c[j+4>>2]=d;c[j>>2]=d;f=2;b=d;g=d;while(1){b=b+d+g|0;c[j+(f<<2)>>2]=b;if(b>>>0<h>>>0){n=g;f=f+1|0;g=b;b=n}else break}g=a+h+i|0;if(g>>>0>a>>>0){h=g;f=1;b=1;do{do if((b&3|0)!=3){b=f+-1|0;if((c[j+(b<<2)>>2]|0)>>>0<(h-a|0)>>>0)Qf(a,d,e,f,j);else Sf(a,d,e,k,f,0,j);if((f|0)==1){Tf(k,1);f=0;break}else{Tf(k,b);f=1;break}}else{Qf(a,d,e,f,j);Rf(k,2);f=f+2|0}while(0);b=c[k>>2]|1;c[k>>2]=b;a=a+d|0}while(a>>>0<g>>>0)}else{f=1;b=1}Sf(a,d,e,k,f,0,j);g=k+4|0;while(1){if((f|0)==1&(b|0)==1)if(!(c[g>>2]|0))break a;else l=19;else if((f|0)<2)l=19;else{Tf(k,2);n=f+-2|0;c[k>>2]=c[k>>2]^7;Rf(k,1);Sf(a+(0-(c[j+(n<<2)>>2]|0))+i|0,d,e,k,f+-1|0,1,j);Tf(k,1);b=c[k>>2]|1;c[k>>2]=b;a=a+i|0;Sf(a,d,e,k,n,1,j);f=n}if((l|0)==19){l=0;b=Uf(k)|0;Rf(k,b);a=a+i|0;f=b+f|0;b=c[k>>2]|0}}}while(0);L=m;return}function Qf(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=L;L=L+240|0;l=m;c[l>>2]=a;a:do if((e|0)>1){k=0-b|0;i=a;g=e;e=1;h=a;while(1){i=i+k|0;j=g+-2|0;a=i+(0-(c[f+(j<<2)>>2]|0))|0;if((Q[d&31](h,a)|0)>-1?(Q[d&31](h,i)|0)>-1:0)break a;h=l+(e<<2)|0;if((Q[d&31](a,i)|0)>-1){c[h>>2]=a;g=g+-1|0}else{c[h>>2]=i;a=i;g=j}e=e+1|0;if((g|0)<=1)break a;i=a;h=c[l>>2]|0}}else e=1;while(0);Wf(b,l,e);L=m;return}function Rf(a,b){a=a|0;b=b|0;var d=0,e=0,f=0;f=a+4|0;if(b>>>0>31){e=c[f>>2]|0;c[a>>2]=e;c[f>>2]=0;b=b+-32|0;d=0}else{d=c[f>>2]|0;e=c[a>>2]|0}c[a>>2]=d<<32-b|e>>>b;c[f>>2]=d>>>b;return}function Sf(a,b,d,e,f,g,h){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;o=L;L=L+240|0;m=o+232|0;n=o;p=c[e>>2]|0;c[m>>2]=p;j=c[e+4>>2]|0;k=m+4|0;c[k>>2]=j;c[n>>2]=a;a:do if((p|0)!=1|(j|0)!=0?(l=0-b|0,i=a+(0-(c[h+(f<<2)>>2]|0))|0,(Q[d&31](i,a)|0)>=1):0){e=1;g=(g|0)==0;j=i;while(1){if(g&(f|0)>1){g=a+l|0;i=c[h+(f+-2<<2)>>2]|0;if((Q[d&31](g,j)|0)>-1){i=10;break a}if((Q[d&31](g+(0-i)|0,j)|0)>-1){i=10;break a}}g=e+1|0;c[n+(e<<2)>>2]=j;p=Uf(m)|0;Rf(m,p);f=p+f|0;if(!((c[m>>2]|0)!=1|(c[k>>2]|0)!=0)){e=g;a=j;i=10;break a}a=j+(0-(c[h+(f<<2)>>2]|0))|0;if((Q[d&31](a,c[n>>2]|0)|0)<1){a=j;e=g;g=0;i=9;break}else{p=j;e=g;g=1;j=a;a=p}}}else{e=1;i=9}while(0);if((i|0)==9?(g|0)==0:0)i=10;if((i|0)==10){Wf(b,n,e);Qf(a,b,d,f,h)}L=o;return}function Tf(a,b){a=a|0;b=b|0;var d=0,e=0,f=0;f=a+4|0;if(b>>>0>31){e=c[a>>2]|0;c[f>>2]=e;c[a>>2]=0;b=b+-32|0;d=0}else{d=c[a>>2]|0;e=c[f>>2]|0}c[f>>2]=d>>>(32-b|0)|e<<b;c[a>>2]=d<<b;return}function Uf(a){a=a|0;var b=0;b=Vf((c[a>>2]|0)+-1|0)|0;if(!b){b=Vf(c[a+4>>2]|0)|0;return ((b|0)==0?0:b+32|0)|0}else return b|0;return 0}function Vf(a){a=a|0;var b=0;if(a)if(!(a&1)){b=a;a=0;while(1){a=a+1|0;if(!(b&2))b=b>>>1;else break}}else a=0;else a=32;return a|0}function Wf(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;h=L;L=L+256|0;e=h;a:do if((d|0)>=2?(g=b+(d<<2)|0,c[g>>2]=e,a|0):0)while(1){f=a>>>0<256?a:256;ig(e|0,c[b>>2]|0,f|0)|0;e=0;do{i=b+(e<<2)|0;e=e+1|0;ig(c[i>>2]|0,c[b+(e<<2)>>2]|0,f|0)|0;c[i>>2]=(c[i>>2]|0)+f}while((e|0)!=(d|0));a=a-f|0;if(!a)break a;e=c[g>>2]|0}while(0);L=h;return}function Xf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=a&65535;e=b&65535;c=e*f|0;d=a>>>16;a=(c>>>16)+(e*d|0)|0;e=b>>>16;b=e*f|0;return (u((a>>>16)+(e*d|0)+(((a&65535)+b|0)>>>16)|0),a+b<<16|c&65535|0)|0}function Yf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=a;f=c;c=Xf(e,f)|0;a=v()|0;return (u((b*f|0)+(d*e|0)+a|a&0|0),c|0|0)|0}function Zf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;c=a+c>>>0;return (u(b+d+(c>>>0<a>>>0|0)>>>0|0),c|0)|0}function _f(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;d=b-d-(c>>>0>a>>>0|0)>>>0;return (u(d|0),a-c>>>0|0)|0}function $f(a){a=a|0;return (a?31-(s(a^a-1)|0)|0:32)|0}function ag(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;l=a;j=b;k=j;h=d;n=e;i=n;if(!k){g=(f|0)!=0;if(!i){if(g){c[f>>2]=(l>>>0)%(h>>>0);c[f+4>>2]=0}n=0;f=(l>>>0)/(h>>>0)>>>0;return (u(n|0),f)|0}else{if(!g){n=0;f=0;return (u(n|0),f)|0}c[f>>2]=a|0;c[f+4>>2]=b&0;n=0;f=0;return (u(n|0),f)|0}}g=(i|0)==0;do if(h){if(!g){g=(s(i|0)|0)-(s(k|0)|0)|0;if(g>>>0<=31){m=g+1|0;i=31-g|0;b=g-31>>31;h=m;a=l>>>(m>>>0)&b|k<<i;b=k>>>(m>>>0)&b;g=0;i=l<<i;break}if(!f){n=0;f=0;return (u(n|0),f)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;n=0;f=0;return (u(n|0),f)|0}g=h-1|0;if(g&h|0){i=(s(h|0)|0)+33-(s(k|0)|0)|0;p=64-i|0;m=32-i|0;j=m>>31;o=i-32|0;b=o>>31;h=i;a=m-1>>31&k>>>(o>>>0)|(k<<m|l>>>(i>>>0))&b;b=b&k>>>(i>>>0);g=l<<p&j;i=(k<<p|l>>>(o>>>0))&j|l<<m&i-33>>31;break}if(f|0){c[f>>2]=g&l;c[f+4>>2]=0}if((h|0)==1){o=j|b&0;p=a|0|0;return (u(o|0),p)|0}else{p=$f(h|0)|0;o=k>>>(p>>>0)|0;p=k<<32-p|l>>>(p>>>0)|0;return (u(o|0),p)|0}}else{if(g){if(f|0){c[f>>2]=(k>>>0)%(h>>>0);c[f+4>>2]=0}o=0;p=(k>>>0)/(h>>>0)>>>0;return (u(o|0),p)|0}if(!l){if(f|0){c[f>>2]=0;c[f+4>>2]=(k>>>0)%(i>>>0)}o=0;p=(k>>>0)/(i>>>0)>>>0;return (u(o|0),p)|0}g=i-1|0;if(!(g&i)){if(f|0){c[f>>2]=a|0;c[f+4>>2]=g&k|b&0}o=0;p=k>>>(($f(i|0)|0)>>>0);return (u(o|0),p)|0}g=(s(i|0)|0)-(s(k|0)|0)|0;if(g>>>0<=30){b=g+1|0;i=31-g|0;h=b;a=k<<i|l>>>(b>>>0);b=k>>>(b>>>0);g=0;i=l<<i;break}if(!f){o=0;p=0;return (u(o|0),p)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;o=0;p=0;return (u(o|0),p)|0}while(0);if(!h){k=i;j=0;i=0}else{m=d|0|0;l=n|e&0;k=Zf(m|0,l|0,-1,-1)|0;d=v()|0;j=i;i=0;do{e=j;j=g>>>31|j<<1;g=i|g<<1;e=a<<1|e>>>31|0;n=a>>>31|b<<1|0;_f(k|0,d|0,e|0,n|0)|0;p=v()|0;o=p>>31|((p|0)<0?-1:0)<<1;i=o&1;a=_f(e|0,n|0,o&m|0,(((p|0)<0?-1:0)>>31|((p|0)<0?-1:0)<<1)&l|0)|0;b=v()|0;h=h-1|0}while((h|0)!=0);k=j;j=0}h=0;if(f|0){c[f>>2]=a;c[f+4>>2]=b}o=(g|0)>>>31|(k|h)<<1|(h<<1|g>>>31)&0|j;p=(g<<1|0>>>31)&-2|i;return (u(o|0),p)|0}function bg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return ag(a,b,c,d,0)|0}function cg(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){u(b>>>c|0);return a>>>c|(b&(1<<c)-1)<<32-c}u(0);return b>>>c-32|0}function dg(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){u(b<<c|(a&(1<<c)-1<<32-c)>>>32-c|0);return a<<c}u(a<<c-32|0);return 0}function eg(a){a=a|0;return (a&255)<<24|(a>>8&255)<<16|(a>>16&255)<<8|a>>>24|0}function fg(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0;l=l+1|0;c[a>>2]=l;while((f|0)<(e|0)){if(!(c[d+(f<<3)>>2]|0)){c[d+(f<<3)>>2]=l;c[d+((f<<3)+4)>>2]=b;c[d+((f<<3)+8)>>2]=0;u(e|0);return d|0}f=f+1|0}e=e*2|0;d=mf(d|0,8*(e+1|0)|0)|0;d=fg(a|0,b|0,d|0,e|0)|0;u(e|0);return d|0}function gg(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0;while((f|0)<(d|0)){e=c[b+(f<<3)>>2]|0;if(!e)break;if((e|0)==(a|0))return c[b+((f<<3)+4)>>2]|0;f=f+1|0}return 0}function hg(a,b){a=a|0;b=b|0;if(!j){j=a;k=b}}function ig(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0;if((e|0)>=8192){G(b|0,d|0,e|0)|0;return b|0}h=b|0;g=b+e|0;if((b&3)==(d&3)){while(b&3){if(!e)return h|0;a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}e=g&-4|0;f=e-64|0;while((b|0)<=(f|0)){c[b>>2]=c[d>>2];c[b+4>>2]=c[d+4>>2];c[b+8>>2]=c[d+8>>2];c[b+12>>2]=c[d+12>>2];c[b+16>>2]=c[d+16>>2];c[b+20>>2]=c[d+20>>2];c[b+24>>2]=c[d+24>>2];c[b+28>>2]=c[d+28>>2];c[b+32>>2]=c[d+32>>2];c[b+36>>2]=c[d+36>>2];c[b+40>>2]=c[d+40>>2];c[b+44>>2]=c[d+44>>2];c[b+48>>2]=c[d+48>>2];c[b+52>>2]=c[d+52>>2];c[b+56>>2]=c[d+56>>2];c[b+60>>2]=c[d+60>>2];b=b+64|0;d=d+64|0}while((b|0)<(e|0)){c[b>>2]=c[d>>2];b=b+4|0;d=d+4|0}}else{e=g-4|0;while((b|0)<(e|0)){a[b>>0]=a[d>>0]|0;a[b+1>>0]=a[d+1>>0]|0;a[b+2>>0]=a[d+2>>0]|0;a[b+3>>0]=a[d+3>>0]|0;b=b+4|0;d=d+4|0}}while((b|0)<(g|0)){a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0}return h|0}function jg(b,c,d){b=b|0;c=c|0;d=d|0;var e=0;if((c|0)<(b|0)&(b|0)<(c+d|0)){e=b;c=c+d|0;b=b+d|0;while((d|0)>0){b=b-1|0;c=c-1|0;d=d-1|0;a[b>>0]=a[c>>0]|0}b=e}else ig(b,c,d)|0;return b|0}function kg(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=b+e|0;d=d&255;if((e|0)>=67){while(b&3){a[b>>0]=d;b=b+1|0}f=h&-4|0;i=d|d<<8|d<<16|d<<24;g=f-64|0;while((b|0)<=(g|0)){c[b>>2]=i;c[b+4>>2]=i;c[b+8>>2]=i;c[b+12>>2]=i;c[b+16>>2]=i;c[b+20>>2]=i;c[b+24>>2]=i;c[b+28>>2]=i;c[b+32>>2]=i;c[b+36>>2]=i;c[b+40>>2]=i;c[b+44>>2]=i;c[b+48>>2]=i;c[b+52>>2]=i;c[b+56>>2]=i;c[b+60>>2]=i;b=b+64|0}while((b|0)<(f|0)){c[b>>2]=i;b=b+4|0}}while((b|0)<(h|0)){a[b>>0]=d;b=b+1|0}return h-e|0}function lg(a){a=a|0;var b=0,d=0;d=c[i>>2]|0;b=d+a|0;if((a|0)>0&(b|0)<(d|0)|(b|0)<0){K(b|0)|0;E(12);return -1}if((b|0)>(F()|0)){if(!(H(b|0)|0)){E(12);return -1}}else c[i>>2]=b;return d|0}function mg(a){a=a|0;return O[a&1]()|0}function ng(a,b){a=a|0;b=b|0;return P[a&7](b|0)|0}function og(a,b,c){a=a|0;b=b|0;c=c|0;return Q[a&31](b|0,c|0)|0}function pg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return R[a&3](b|0,c|0,d|0)|0}function qg(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;return S[a&1](b|0,c|0,d|0,e|0)|0}function rg(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return T[a&3](b|0,c|0,d|0,e|0,f|0)|0}function sg(a){a=a|0;U[a&7]()}function tg(a,b){a=a|0;b=b|0;V[a&15](b|0)}function ug(a,b,c){a=a|0;b=b|0;c=c|0;W[a&7](b|0,c|0)}function vg(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;X[a&63](b|0,c|0,d|0,e|0)}function wg(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;Y[a&1](b|0,c|0,d|0,e|0,f|0,g|0)}function xg(a,b,c,d,e,f,g,h,i,j,k,l){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;Z[a&15](b|0,c|0,d|0,e|0,f|0,g|0,h|0,i|0,j|0,k|0,l|0)}function yg(){t(0);return 0}function zg(a){a=a|0;t(1);return 0}function Ag(a,b){a=a|0;b=b|0;t(2);return 0}function Bg(a,b,c){a=a|0;b=b|0;c=c|0;t(3);return 0}function Cg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;t(4);return 0}function Dg(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;t(5);return 0}function Eg(){t(6)}function Fg(a){a=a|0;t(7)}function Gg(a,b){a=a|0;b=b|0;t(8)}function Hg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;t(9)}function Ig(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;t(10)}function Jg(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;t(11)}

// EMSCRIPTEN_END_FUNCS
var O=[yg,Mb];var P=[zg,oa,Hb,Ib,Ab,zg,zg,zg];var Q=[Ag,na,qa,sa,wa,xa,ya,za,Aa,Ba,Ca,Da,Ea,Fa,Ga,Ha,Ia,Ja,Ka,fc,Va,Xa,Sc,nb,pb,eb,uc,vc,wc,Uc,Ag,Ag];var R=[Bg,pa,Of,ec];var S=[Cg,ta];var T=[Dg,ra,yb,Dg];var U=[Eg,rc,Fb,vb,dd,Eg,Eg,Eg];var V=[Fg,Gb,Jb,Kb,Lb,Qa,db,fb,gb,Gc,Sb,Fg,Fg,Fg,Fg,Fg];var W=[Gg,Oe,Ne,Se,Re,Xe,We,Gg];var X=[Hg,Ec,ma,Pa,Id,Jd,Kd,Ld,Md,Nd,Od,Pd,jd,pd,qd,rd,ld,vd,wd,xd,kd,sd,td,ud,md,yd,zd,Ad,nd,Bd,Cd,Dd,od,Ed,Gd,Hd,Qd,Rd,Sd,Td,Ud,Vd,Wd,Xd,ae,be,ce,de,Yd,Zd,_d,$d,ee,fe,ge,he,Hg,Hg,Hg,Hg,Hg,Hg,Hg,Hg];var Y=[Ig,Ya];var Z=[Jg,re,te,ue,we,qe,pe,ke,me,ie,je,ne,oe,se,ve,Jg];return{___muldi3:Yf,___udivdi3:bg,_bitshift64Lshr:cg,_bitshift64Shl:dg,_free:lf,_i64Add:Zf,_i64Subtract:_f,_llvm_bswap_i32:eg,_malloc:kf,_memcpy:ig,_memmove:jg,_memset:kg,_ogv_video_decoder_async:da,_ogv_video_decoder_destroy:ea,_ogv_video_decoder_init:ca,_ogv_video_decoder_process_frame:ga,_ogv_video_decoder_process_header:fa,_realloc:mf,_saveSetjmp:fg,_sbrk:lg,_setThrew:hg,_testSetjmp:gg,dynCall_i:mg,dynCall_ii:ng,dynCall_iii:og,dynCall_iiii:pg,dynCall_iiiii:qg,dynCall_iiiiii:rg,dynCall_v:sg,dynCall_vi:tg,dynCall_vii:ug,dynCall_viiii:vg,dynCall_viiiiii:wg,dynCall_viiiiiiiiiii:xg,establishStackSpace:ba,stackAlloc:_,stackRestore:aa,stackSave:$}})


// EMSCRIPTEN_END_ASM
(va,Ea,buffer);b.___muldi3=S.___muldi3;b.___udivdi3=S.___udivdi3;b._bitshift64Lshr=S._bitshift64Lshr;b._bitshift64Shl=S._bitshift64Shl;b._free=S._free;b._i64Add=S._i64Add;b._i64Subtract=S._i64Subtract;b._llvm_bswap_i32=S._llvm_bswap_i32;b._malloc=S._malloc;b._memcpy=S._memcpy;b._memmove=S._memmove;
b._memset=S._memset;b._ogv_video_decoder_async=S._ogv_video_decoder_async;b._ogv_video_decoder_destroy=S._ogv_video_decoder_destroy;b._ogv_video_decoder_init=S._ogv_video_decoder_init;b._ogv_video_decoder_process_frame=S._ogv_video_decoder_process_frame;b._ogv_video_decoder_process_header=S._ogv_video_decoder_process_header;b._realloc=S._realloc;b._saveSetjmp=S._saveSetjmp;b._sbrk=S._sbrk;var R=b._setThrew=S._setThrew;b._testSetjmp=S._testSetjmp;b.establishStackSpace=S.establishStackSpace;
b.stackAlloc=S.stackAlloc;var Q=b.stackRestore=S.stackRestore,P=b.stackSave=S.stackSave,wa=b.dynCall_i=S.dynCall_i,xa=b.dynCall_ii=S.dynCall_ii,ya=b.dynCall_iii=S.dynCall_iii;b.dynCall_iiii=S.dynCall_iiii;b.dynCall_iiiii=S.dynCall_iiiii;var za=b.dynCall_iiiiii=S.dynCall_iiiiii,Aa=b.dynCall_v=S.dynCall_v,Ba=b.dynCall_vi=S.dynCall_vi;b.dynCall_vii=S.dynCall_vii;var Ca=b.dynCall_viiii=S.dynCall_viiii,Da=b.dynCall_viiiiii=S.dynCall_viiiiii;b.dynCall_viiiiiiiiiii=S.dynCall_viiiiiiiiiii;b.asm=S;
if(K){if(String.prototype.startsWith?!K.startsWith(O):0!==K.indexOf(O)){var Fa=K;K=b.locateFile?b.locateFile(Fa,w):w+Fa}if(u||v){var Ga=b.readBinary(K);C.set(Ga,8)}else{H++;b.monitorRunDependencies&&b.monitorRunDependencies(H);var T=function(a){a.byteLength&&(a=new Uint8Array(a));C.set(a,8);b.memoryInitializerRequest&&delete b.memoryInitializerRequest.response;H--;b.monitorRunDependencies&&b.monitorRunDependencies(H);0==H&&(null!==I&&(clearInterval(I),I=null),J&&(a=J,J=null,a()))},Ha=function(){b.readAsync(K,
T,function(){throw"could not load memory initializer "+K;})},Ia=A(K);if(Ia)T(Ia.buffer);else if(b.memoryInitializerRequest){var Ja=function(){var a=b.memoryInitializerRequest,c=a.response;if(200!==a.status&&0!==a.status)if(c=A(b.memoryInitializerRequestURL))c=c.buffer;else{console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+a.status+", retrying "+K);Ha();return}T(c)};b.memoryInitializerRequest.response?setTimeout(Ja,0):b.memoryInitializerRequest.addEventListener("load",
Ja)}else Ha()}}b.then=function(a){if(b.calledRun)a(b);else{var c=b.onRuntimeInitialized;b.onRuntimeInitialized=function(){c&&c();a(b)}}return b};function U(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}U.prototype=Error();U.prototype.constructor=U;J=function Ka(){b.calledRun||Ma();b.calledRun||(J=Ka)};
function Ma(){function a(){if(!b.calledRun&&(b.calledRun=!0,!ea)){ka||(ka=!0,F(ha));F(ia);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var a=b.postRun.shift();ja.unshift(a)}F(ja)}}if(!(0<H)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)la();F(fa);0<H||b.calledRun||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},
1);a()},1)):a())}}b.run=Ma;function B(a){if(b.onAbort)b.onAbort(a);void 0!==a?(ba(a),ca(a),a=JSON.stringify(a)):a="";ea=!0;throw"abort("+a+"). Build with -s ASSERTIONS=1 for more info.";}b.abort=B;if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();b.noExitRuntime=!0;Ma();var Y,Na,Oa;Oa="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);
function Z(a){var c=Oa();a=a();b.cpuTime+=Oa()-c;return a}b.loadedMetadata=!!f.videoFormat;b.videoFormat=f.videoFormat||null;b.frameBuffer=null;b.cpuTime=0;Object.defineProperty(b,"processing",{get:function(){return!1}});b.init=function(a){Z(function(){b._ogv_video_decoder_init()});a()};b.processHeader=function(a,c){var d=Z(function(){var c=a.byteLength;Y&&Na>=c||(Y&&b._free(Y),Na=c,Y=b._malloc(Na));var d=Y;b.HEAPU8.set(new Uint8Array(a),d);return b._ogv_video_decoder_process_header(d,c)});c(d)};
b.A=[];b.processFrame=function(a,c){function d(a){b._free(h);c(a)}var e=b._ogv_video_decoder_async(),g=a.byteLength,h=b._malloc(g);e&&b.A.push(d);var k=Z(function(){b.HEAPU8.set(new Uint8Array(a),h);return b._ogv_video_decoder_process_frame(h,g)});e||d(k)};b.close=function(){};b.sync=function(){b._ogv_video_decoder_async()&&(b.A.push(function(){}),Z(function(){b._ogv_video_decoder_process_frame(0,0)}))};



  return OGVDecoderVideoVP9
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
      module.exports = OGVDecoderVideoVP9;
    else if (typeof define === 'function' && define['amd'])
      define([], function() { return OGVDecoderVideoVP9; });
    else if (typeof exports === 'object')
      exports["OGVDecoderVideoVP9"] = OGVDecoderVideoVP9;
    