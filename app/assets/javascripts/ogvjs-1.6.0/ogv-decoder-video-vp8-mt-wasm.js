
var OGVDecoderVideoVP8MTW = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  return (
function(OGVDecoderVideoVP8MTW) {
  OGVDecoderVideoVP8MTW = OGVDecoderVideoVP8MTW || {};

var Module=typeof OGVDecoderVideoVP8MTW!=="undefined"?OGVDecoderVideoVP8MTW:{};var options=Module;if(Module["memoryLimit"]){Module["TOTAL_MEMORY"]=options["memoryLimit"]}var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}Module["arguments"]=[];Module["thisProgram"]="./this.program";Module["quit"]=function(status,toThrow){throw toThrow};Module["preRun"]=[];Module["postRun"]=[];var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=false;var ENVIRONMENT_IS_NODE=false;var ENVIRONMENT_IS_SHELL=false;ENVIRONMENT_IS_WEB=typeof window==="object";ENVIRONMENT_IS_WORKER=typeof importScripts==="function";ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof require==="function"&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER;ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(typeof ENVIRONMENT_IS_PTHREAD==="undefined"){ENVIRONMENT_IS_PTHREAD=false;var PthreadWorkerInit={}}else{var buffer=OGVDecoderVideoVP8MTW.buffer;var tempDoublePtr=OGVDecoderVideoVP8MTW.tempDoublePtr;var TOTAL_MEMORY=OGVDecoderVideoVP8MTW.TOTAL_MEMORY;var STATICTOP=OGVDecoderVideoVP8MTW.STATICTOP;var DYNAMIC_BASE=OGVDecoderVideoVP8MTW.DYNAMIC_BASE;var DYNAMICTOP_PTR=OGVDecoderVideoVP8MTW.DYNAMICTOP_PTR;var PthreadWorkerInit=OGVDecoderVideoVP8MTW.PthreadWorkerInit}var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}else{return scriptDirectory+path}}if(ENVIRONMENT_IS_NODE){scriptDirectory=__dirname+"/";var nodeFS;var nodePath;Module["read"]=function shell_read(filename,binary){var ret;if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);ret=nodeFS["readFileSync"](filename);return binary?ret:ret.toString()};Module["readBinary"]=function readBinary(filename){var ret=Module["read"](filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};if(process["argv"].length>1){Module["thisProgram"]=process["argv"][1].replace(/\\/g,"/")}Module["arguments"]=process["argv"].slice(2);process["on"]("unhandledRejection",abort);Module["quit"]=function(status){process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_SHELL){if(typeof read!="undefined"){Module["read"]=function shell_read(f){return read(f)}}Module["readBinary"]=function readBinary(f){var data;if(typeof readbuffer==="function"){return new Uint8Array(readbuffer(f))}data=read(f,"binary");assert(typeof data==="object");return data};if(typeof scriptArgs!="undefined"){Module["arguments"]=scriptArgs}else if(typeof arguments!="undefined"){Module["arguments"]=arguments}if(typeof quit==="function"){Module["quit"]=function(status){quit(status)}}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.lastIndexOf("/")+1)}else{scriptDirectory=""}Module["read"]=function shell_read(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){Module["readBinary"]=function readBinary(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}Module["readAsync"]=function readAsync(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function xhr_onload(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)};Module["setWindowTitle"]=function(title){document.title=title}}else{}var out=Module["print"]||(typeof console!=="undefined"?console.log.bind(console):typeof print!=="undefined"?print:null);var err=Module["printErr"]||(typeof printErr!=="undefined"?printErr:typeof console!=="undefined"&&console.warn.bind(console)||out);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=undefined;function dynamicAlloc(size){var ret=HEAP32[DYNAMICTOP_PTR>>2];var end=ret+size+15&-16;if(end<=_emscripten_get_heap_size()){HEAP32[DYNAMICTOP_PTR>>2]=end}else{return 0}return ret}var asm2wasmImports={"f64-rem":function(x,y){return x%y},"debugger":function(){debugger}};var functionPointers=new Array(0);var tempRet0=0;var setTempRet0=function(value){tempRet0=value};var getTempRet0=function(){return tempRet0};var GLOBAL_BASE=1024;if(typeof WebAssembly!=="object"){err("no native wasm support detected")}var wasmMemory;var wasmTable;var wasmModule;var ABORT=false;var EXITSTATUS=0;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function getMemory(size){if(!runtimeInitialized)return dynamicAlloc(size);return _malloc(size)}function UTF8ArrayToString(u8Array,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var str="";while(!(idx>=endIdx)){var u0=u8Array[idx++];if(!u0)return str;if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=u8Array[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=u8Array[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|u8Array[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,outU8Array,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;outU8Array[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;outU8Array[outIdx++]=192|u>>6;outU8Array[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;outU8Array[outIdx++]=224|u>>12;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;outU8Array[outIdx++]=240|u>>18;outU8Array[outIdx++]=128|u>>12&63;outU8Array[outIdx++]=128|u>>6&63;outU8Array[outIdx++]=128|u&63}}outU8Array[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}var PAGE_SIZE=16384;var WASM_PAGE_SIZE=65536;var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferViews(){Module["HEAP8"]=HEAP8=new Int8Array(buffer);Module["HEAP16"]=HEAP16=new Int16Array(buffer);Module["HEAP32"]=HEAP32=new Int32Array(buffer);Module["HEAPU8"]=HEAPU8=new Uint8Array(buffer);Module["HEAPU16"]=HEAPU16=new Uint16Array(buffer);Module["HEAPU32"]=HEAPU32=new Uint32Array(buffer);Module["HEAPF32"]=HEAPF32=new Float32Array(buffer);Module["HEAPF64"]=HEAPF64=new Float64Array(buffer)}if(!ENVIRONMENT_IS_PTHREAD){var STACK_BASE=13632,STACKTOP=STACK_BASE,STACK_MAX=5256512,DYNAMIC_BASE=5256512,DYNAMICTOP_PTR=12576}var TOTAL_STACK=5242880;var TOTAL_MEMORY=Module["TOTAL_MEMORY"]||67108864;if(TOTAL_MEMORY<TOTAL_STACK)err("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")");if(!ENVIRONMENT_IS_PTHREAD){wasmMemory=new WebAssembly.Memory({"initial":TOTAL_MEMORY/WASM_PAGE_SIZE,"maximum":TOTAL_MEMORY/WASM_PAGE_SIZE,"shared":true});buffer=wasmMemory.buffer;assert(buffer instanceof SharedArrayBuffer,"requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag")}updateGlobalBufferViews();if(!ENVIRONMENT_IS_PTHREAD){HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback();continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){Module["dynCall_v"](func)}else{Module["dynCall_vi"](func,callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;if(ENVIRONMENT_IS_PTHREAD)runtimeInitialized=true;function preRun(){if(ENVIRONMENT_IS_PTHREAD)return;if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){if(ENVIRONMENT_IS_PTHREAD)return;if(runtimeInitialized)return;runtimeInitialized=true;__register_pthread_ptr(PThread.mainThreadBlock,!ENVIRONMENT_IS_WORKER,1);_emscripten_register_main_browser_thread_id(PThread.mainThreadBlock);callRuntimeCallbacks(__ATINIT__)}function preMain(){if(ENVIRONMENT_IS_PTHREAD)return;callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){if(ENVIRONMENT_IS_PTHREAD)return;runtimeExited=true}function postRun(){if(ENVIRONMENT_IS_PTHREAD)return;if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){assert(!ENVIRONMENT_IS_PTHREAD,"addRunDependency cannot be used in a pthread worker");runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};var memoryInitializer=null;if(!ENVIRONMENT_IS_PTHREAD)addOnPreRun(function(){if(typeof SharedArrayBuffer!=="undefined"){addRunDependency("pthreads");PThread.allocateUnusedWorkers(1,function(){removeRunDependency("pthreads")})}});var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return String.prototype.startsWith?filename.startsWith(dataURIPrefix):filename.indexOf(dataURIPrefix)===0}var wasmBinaryFile="ogv-decoder-video-vp8-mt-wasm.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(){try{if(Module["wasmBinary"]){return new Uint8Array(Module["wasmBinary"])}if(Module["readBinary"]){return Module["readBinary"](wasmBinaryFile)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!Module["wasmBinary"]&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary()})}return new Promise(function(resolve,reject){resolve(getBinary())})}function createWasm(env){var info={"env":env,"global":{"NaN":NaN,Infinity:Infinity},"global.Math":Math,"asm2wasm":asm2wasmImports};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmModule=module;if(!ENVIRONMENT_IS_PTHREAD)removeRunDependency("wasm-instantiate")}if(!ENVIRONMENT_IS_PTHREAD){addRunDependency("wasm-instantiate")}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}function receiveInstantiatedSource(output){receiveInstance(output["instance"],output["module"])}function instantiateArrayBuffer(receiver){getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}if(!Module["wasmBinary"]&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){WebAssembly.instantiateStreaming(fetch(wasmBinaryFile,{credentials:"same-origin"}),info).then(receiveInstantiatedSource,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");instantiateArrayBuffer(receiveInstantiatedSource)})}else{instantiateArrayBuffer(receiveInstantiatedSource)}return{}}Module["asm"]=function(global,env,providedBuffer){env["memory"]=wasmMemory||Module["wasmMemory"];env["table"]=wasmTable=new WebAssembly.Table({"initial":132,"maximum":132,"element":"anyfunc"});env["__memory_base"]=1024;env["__table_base"]=0;var exports=createWasm(env);return exports};var ASM_CONSTS=[function(){postMessage({cmd:"processQueuedMainThreadWork"})},function($0){if(!ENVIRONMENT_IS_PTHREAD){if(!PThread.pthreads[$0]||!PThread.pthreads[$0].worker){return 0}PThread.pthreads[$0].worker.postMessage({cmd:"processThreadQueue"})}else{postMessage({targetThread:$0,cmd:"processThreadQueue"})}return 1},function(){return!!Module["canvas"]},function(){Module["noExitRuntime"]=true},function(){throw"Canceled!"}];function _emscripten_asm_const_i(code){return ASM_CONSTS[code]()}function _emscripten_asm_const_ii(code,a0){return ASM_CONSTS[code](a0)}if(!ENVIRONMENT_IS_PTHREAD)__ATINIT__.push({func:function(){___emscripten_pthread_data_constructor()}});if(!ENVIRONMENT_IS_PTHREAD){memoryInitializer="ogv-decoder-video-vp8-mt-wasm.js.mem"}var tempDoublePtr;if(!ENVIRONMENT_IS_PTHREAD)tempDoublePtr=13616;var PROCINFO={ppid:1,pid:42,sid:42,pgid:42};var PThread={MAIN_THREAD_ID:1,mainThreadInfo:{schedPolicy:0,schedPrio:0},unusedWorkerPool:[],runningWorkers:[],initMainThreadBlock:function(){if(ENVIRONMENT_IS_PTHREAD)return undefined;PThread.mainThreadBlock=12816;for(var i=0;i<244/4;++i)HEAPU32[PThread.mainThreadBlock/4+i]=0;HEAP32[PThread.mainThreadBlock+24>>2]=PThread.mainThreadBlock;var headPtr=PThread.mainThreadBlock+168;HEAP32[headPtr>>2]=headPtr;var tlsMemory=13072;for(var i=0;i<128;++i)HEAPU32[tlsMemory/4+i]=0;Atomics.store(HEAPU32,PThread.mainThreadBlock+116>>2,tlsMemory);Atomics.store(HEAPU32,PThread.mainThreadBlock+52>>2,PThread.mainThreadBlock);Atomics.store(HEAPU32,PThread.mainThreadBlock+56>>2,PROCINFO.pid)},pthreads:{},pthreadIdCounter:2,exitHandlers:null,setThreadStatus:function(){},runExitHandlers:function(){if(PThread.exitHandlers!==null){while(PThread.exitHandlers.length>0){PThread.exitHandlers.pop()()}PThread.exitHandlers=null}if(ENVIRONMENT_IS_PTHREAD&&threadInfoStruct)___pthread_tsd_run_dtors()},threadExit:function(exitCode){var tb=_pthread_self();if(tb){Atomics.store(HEAPU32,tb+4>>2,exitCode);Atomics.store(HEAPU32,tb+0>>2,1);Atomics.store(HEAPU32,tb+72>>2,1);Atomics.store(HEAPU32,tb+76>>2,0);PThread.runExitHandlers();_emscripten_futex_wake(tb+0,2147483647);__register_pthread_ptr(0,0,0);threadInfoStruct=0;if(ENVIRONMENT_IS_PTHREAD){postMessage({cmd:"exit"})}}},threadCancel:function(){PThread.runExitHandlers();Atomics.store(HEAPU32,threadInfoStruct+4>>2,-1);Atomics.store(HEAPU32,threadInfoStruct+0>>2,1);_emscripten_futex_wake(threadInfoStruct+0,2147483647);threadInfoStruct=selfThreadId=0;__register_pthread_ptr(0,0,0);postMessage({cmd:"cancelDone"})},terminateAllThreads:function(){for(var t in PThread.pthreads){var pthread=PThread.pthreads[t];if(pthread){PThread.freeThreadData(pthread);if(pthread.worker)pthread.worker.terminate()}}PThread.pthreads={};for(var t in PThread.unusedWorkerPool){var pthread=PThread.unusedWorkerPool[t];if(pthread){PThread.freeThreadData(pthread);if(pthread.worker)pthread.worker.terminate()}}PThread.unusedWorkerPool=[];for(var t in PThread.runningWorkers){var pthread=PThread.runningWorkers[t];if(pthread){PThread.freeThreadData(pthread);if(pthread.worker)pthread.worker.terminate()}}PThread.runningWorkers=[]},freeThreadData:function(pthread){if(!pthread)return;if(pthread.threadInfoStruct){var tlsMemory=HEAP32[pthread.threadInfoStruct+116>>2];HEAP32[pthread.threadInfoStruct+116>>2]=0;_free(pthread.tlsMemory);_free(pthread.threadInfoStruct)}pthread.threadInfoStruct=0;if(pthread.allocatedOwnStack&&pthread.stackBase)_free(pthread.stackBase);pthread.stackBase=0;if(pthread.worker)pthread.worker.pthread=null},receiveObjectTransfer:function(data){},allocateUnusedWorkers:function(numWorkers,onFinishedLoading){if(typeof SharedArrayBuffer==="undefined")return;out("Preallocating "+numWorkers+" workers for a pthread spawn pool.");var numWorkersLoaded=0;var pthreadMainJs="ogv-decoder-video-vp8-mt-wasm.worker.js";pthreadMainJs=locateFile(pthreadMainJs);for(var i=0;i<numWorkers;++i){var worker=new Worker(pthreadMainJs);(function(worker){worker.onmessage=function(e){var d=e.data;if(worker.pthread)PThread.currentProxiedOperationCallerThread=worker.pthread.threadInfoStruct;if(d.targetThread&&d.targetThread!=_pthread_self()){var thread=PThread.pthreads[d.targetThread];if(thread){thread.worker.postMessage(e.data,d.transferList)}else{console.error('Internal error! Worker sent a message "'+d.cmd+'" to target pthread '+d.targetThread+", but that thread no longer exists!")}PThread.currentProxiedOperationCallerThread=undefined;return}if(d.cmd==="processQueuedMainThreadWork"){_emscripten_main_thread_process_queued_calls()}else if(d.cmd==="spawnThread"){__spawn_thread(e.data)}else if(d.cmd==="cleanupThread"){__cleanup_thread(d.thread)}else if(d.cmd==="killThread"){__kill_thread(d.thread)}else if(d.cmd==="cancelThread"){__cancel_thread(d.thread)}else if(d.cmd==="loaded"){worker.loaded=true;if(worker.runPthread){worker.runPthread();delete worker.runPthread}++numWorkersLoaded;if(numWorkersLoaded===numWorkers&&onFinishedLoading){onFinishedLoading()}}else if(d.cmd==="print"){out("Thread "+d.threadId+": "+d.text)}else if(d.cmd==="printErr"){err("Thread "+d.threadId+": "+d.text)}else if(d.cmd==="alert"){alert("Thread "+d.threadId+": "+d.text)}else if(d.cmd==="exit"){}else if(d.cmd==="exitProcess"){Module["noExitRuntime"]=false;exit(d.returnCode)}else if(d.cmd==="cancelDone"){PThread.freeThreadData(worker.pthread);worker.pthread=undefined;PThread.unusedWorkerPool.push(worker);PThread.runningWorkers.splice(PThread.runningWorkers.indexOf(worker.pthread),1)}else if(d.cmd==="objectTransfer"){PThread.receiveObjectTransfer(e.data)}else if(e.data.target==="setimmediate"){worker.postMessage(e.data)}else{err("worker sent an unknown command "+d.cmd)}PThread.currentProxiedOperationCallerThread=undefined};worker.onerror=function(e){err("pthread sent an error! "+e.filename+":"+e.lineno+": "+e.message)}})(worker);var tempDoublePtr=getMemory(8);worker.postMessage({cmd:"load",urlOrBlob:Module["mainScriptUrlOrBlob"]||_scriptDir,wasmMemory:wasmMemory,wasmModule:wasmModule,tempDoublePtr:tempDoublePtr,TOTAL_MEMORY:TOTAL_MEMORY,DYNAMIC_BASE:DYNAMIC_BASE,DYNAMICTOP_PTR:DYNAMICTOP_PTR,PthreadWorkerInit:PthreadWorkerInit});PThread.unusedWorkerPool.push(worker)}},getNewWorker:function(){if(PThread.unusedWorkerPool.length==0)PThread.allocateUnusedWorkers(1);if(PThread.unusedWorkerPool.length>0)return PThread.unusedWorkerPool.pop();else return null},busySpinWait:function(msecs){var t=performance.now()+msecs;while(performance.now()<t){}}};function establishStackSpaceInJsModule(stackBase,stackMax){STACK_BASE=STACKTOP=stackBase;STACK_MAX=stackMax}Module["establishStackSpaceInJsModule"]=establishStackSpaceInJsModule;function ___assert_fail(condition,filename,line,func){abort("Assertion failed: "+UTF8ToString(condition)+", at: "+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"])}function ___call_main(argc,argv){var returnCode=_main(argc,argv);if(!Module["noExitRuntime"])postMessage({cmd:"exitProcess",returnCode:returnCode});return returnCode}function _emscripten_get_now(){abort()}function _emscripten_get_now_is_monotonic(){return 0||ENVIRONMENT_IS_NODE||typeof dateNow!=="undefined"||(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&self["performance"]&&self["performance"]["now"]}function ___setErrNo(value){if(Module["___errno_location"])HEAP32[Module["___errno_location"]()>>2]=value;return value}function _clock_gettime(clk_id,tp){var now;if(clk_id===0){now=Date.now()}else if(clk_id===1&&_emscripten_get_now_is_monotonic()){now=_emscripten_get_now()}else{___setErrNo(22);return-1}HEAP32[tp>>2]=now/1e3|0;HEAP32[tp+4>>2]=now%1e3*1e3*1e3|0;return 0}function ___clock_gettime(a0,a1){return _clock_gettime(a0,a1)}function _abort(){Module["abort"]()}var __main_thread_futex_wait_address;if(ENVIRONMENT_IS_PTHREAD)__main_thread_futex_wait_address=PthreadWorkerInit.__main_thread_futex_wait_address;else PthreadWorkerInit.__main_thread_futex_wait_address=__main_thread_futex_wait_address=13600;function _emscripten_futex_wait(addr,val,timeout){if(addr<=0||addr>HEAP8.length||addr&3!=0)return-22;if(ENVIRONMENT_IS_WORKER){var ret=Atomics.wait(HEAP32,addr>>2,val,timeout);if(ret==="timed-out")return-110;if(ret==="not-equal")return-11;if(ret==="ok")return 0;throw"Atomics.wait returned an unexpected value "+ret}else{var loadedVal=Atomics.load(HEAP32,addr>>2);if(val!=loadedVal)return-11;var tNow=performance.now();var tEnd=tNow+timeout;Atomics.store(HEAP32,__main_thread_futex_wait_address>>2,addr);var ourWaitAddress=addr;while(addr==ourWaitAddress){tNow=performance.now();if(tNow>tEnd){return-110}_emscripten_main_thread_process_queued_calls();addr=Atomics.load(HEAP32,__main_thread_futex_wait_address>>2)}return 0}}function _emscripten_futex_wake(addr,count){if(addr<=0||addr>HEAP8.length||addr&3!=0||count<0)return-22;if(count==0)return 0;var mainThreadWaitAddress=Atomics.load(HEAP32,__main_thread_futex_wait_address>>2);var mainThreadWoken=0;if(mainThreadWaitAddress==addr){var loadedAddr=Atomics.compareExchange(HEAP32,__main_thread_futex_wait_address>>2,mainThreadWaitAddress,0);if(loadedAddr==mainThreadWaitAddress){--count;mainThreadWoken=1;if(count<=0)return 1}}var ret=Atomics.wake(HEAP32,addr>>2,count);if(ret>=0)return ret+mainThreadWoken;throw"Atomics.wake returned an unexpected value "+ret}function _emscripten_get_heap_size(){return TOTAL_MEMORY}function _emscripten_has_threading_support(){return typeof SharedArrayBuffer!=="undefined"}var __num_logical_cores;if(ENVIRONMENT_IS_PTHREAD)__num_logical_cores=PthreadWorkerInit.__num_logical_cores;else{PthreadWorkerInit.__num_logical_cores=__num_logical_cores=13584;HEAPU32[__num_logical_cores>>2]=navigator["hardwareConcurrency"]||4}function _emscripten_num_logical_cores(){return HEAP32[__num_logical_cores>>2]}function _emscripten_proxy_to_main_thread_js(index,sync){var numCallArgs=arguments.length-2;var stack=stackSave();var buffer=stackAlloc(numCallArgs*8);for(var i=0;i<numCallArgs;i++){HEAPF64[(buffer>>3)+i]=arguments[2+i]}var ret=_emscripten_run_in_main_runtime_thread_js(index,numCallArgs,buffer,sync);stackRestore(stack);return ret}function _emscripten_receive_on_main_thread_js(index,numCallArgs,buffer){if(!_emscripten_receive_on_main_thread_js.callArgs){_emscripten_receive_on_main_thread_js.callArgs=[]}var callArgs=_emscripten_receive_on_main_thread_js.callArgs;callArgs.length=numCallArgs;for(var i=0;i<numCallArgs;i++){callArgs[i]=HEAPF64[(buffer>>3)+i]}var func;if(index>0){func=proxiedFunctionTable[index]}else{func=ASM_CONSTS[-index-1]}return func.apply(null,callArgs)}function abortOnCannotGrowMemory(requestedSize){abort("OOM")}var JSEvents={keyEvent:0,mouseEvent:0,wheelEvent:0,uiEvent:0,focusEvent:0,deviceOrientationEvent:0,deviceMotionEvent:0,fullscreenChangeEvent:0,pointerlockChangeEvent:0,visibilityChangeEvent:0,touchEvent:0,previousFullscreenElement:null,previousScreenX:null,previousScreenY:null,removeEventListenersRegistered:false,removeAllEventListeners:function(){for(var i=JSEvents.eventHandlers.length-1;i>=0;--i){JSEvents._removeHandler(i)}JSEvents.eventHandlers=[];JSEvents.deferredCalls=[]},registerRemoveEventListeners:function(){if(!JSEvents.removeEventListenersRegistered){__ATEXIT__.push(JSEvents.removeAllEventListeners);JSEvents.removeEventListenersRegistered=true}},deferredCalls:[],deferCall:function(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var i in JSEvents.deferredCalls){var call=JSEvents.deferredCalls[i];if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction:targetFunction,precedence:precedence,argsList:argsList});JSEvents.deferredCalls.sort(function(x,y){return x.precedence<y.precedence})},removeDeferredCalls:function(targetFunction){for(var i=0;i<JSEvents.deferredCalls.length;++i){if(JSEvents.deferredCalls[i].targetFunction==targetFunction){JSEvents.deferredCalls.splice(i,1);--i}}},canPerformEventHandlerRequests:function(){return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(!JSEvents.canPerformEventHandlerRequests()){return}for(var i=0;i<JSEvents.deferredCalls.length;++i){var call=JSEvents.deferredCalls[i];JSEvents.deferredCalls.splice(i,1);--i;call.targetFunction.apply(this,call.argsList)}},inEventHandler:0,currentEventHandler:null,eventHandlers:[],isInternetExplorer:function(){return navigator.userAgent.indexOf("MSIE")!==-1||navigator.appVersion.indexOf("Trident/")>0},removeAllHandlersOnTarget:function(target,eventTypeString){for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler:function(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler:function(eventHandler){var jsEventHandler=function jsEventHandler(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=jsEventHandler;eventHandler.target.addEventListener(eventHandler.eventTypeString,jsEventHandler,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler);JSEvents.registerRemoveEventListeners()}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}},queueEventHandlerOnThread_iiii:function(targetThread,eventHandlerFunc,eventTypeId,eventData,userData){var stackTop=stackSave();var varargs=stackAlloc(12);HEAP32[varargs>>2]=eventTypeId;HEAP32[varargs+4>>2]=eventData;HEAP32[varargs+8>>2]=userData;_emscripten_async_queue_on_thread_(targetThread,637534208,eventHandlerFunc,eventData,varargs);stackRestore(stackTop)},getTargetThreadForEventCallback:function(targetThread){switch(targetThread){case 1:return 0;case 2:return PThread.currentProxiedOperationCallerThread;default:return targetThread}},getBoundingClientRectOrZeros:function(target){return target.getBoundingClientRect?target.getBoundingClientRect():{left:0,top:0}},pageScrollPos:function(){if(window.pageXOffset>0||window.pageYOffset>0){return[window.pageXOffset,window.pageYOffset]}if(typeof document.documentElement.scrollLeft!=="undefined"||typeof document.documentElement.scrollTop!=="undefined"){return[document.documentElement.scrollLeft,document.documentElement.scrollTop]}return[document.body.scrollLeft|0,document.body.scrollTop|0]},getNodeNameForTarget:function(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target&&target.nodeName?target.nodeName:""},tick:function(){if(window["performance"]&&window["performance"]["now"])return window["performance"]["now"]();else return Date.now()},fullscreenEnabled:function(){return document.fullscreenEnabled||document.mozFullScreenEnabled||document.webkitFullscreenEnabled||document.msFullscreenEnabled}};function stringToNewUTF8(jsString){var length=lengthBytesUTF8(jsString)+1;var cString=_malloc(length);stringToUTF8(jsString,cString,length);return cString}function _emscripten_set_offscreencanvas_size_on_target_thread_js(targetThread,targetCanvas,width,height){var stackTop=stackSave();var varargs=stackAlloc(12);var targetCanvasPtr=0;if(targetCanvas){targetCanvasPtr=stringToNewUTF8(targetCanvas)}HEAP32[varargs>>2]=targetCanvasPtr;HEAP32[varargs+4>>2]=width;HEAP32[varargs+8>>2]=height;_emscripten_async_queue_on_thread_(targetThread,657457152,0,targetCanvasPtr,varargs);stackRestore(stackTop)}function _emscripten_set_offscreencanvas_size_on_target_thread(targetThread,targetCanvas,width,height){targetCanvas=targetCanvas?UTF8ToString(targetCanvas):"";_emscripten_set_offscreencanvas_size_on_target_thread_js(targetThread,targetCanvas,width,height)}var __specialEventTargets=[0,typeof document!=="undefined"?document:0,typeof window!=="undefined"?window:0];function __findEventTarget(target){try{if(!target)return window;if(typeof target==="number")target=__specialEventTargets[target]||UTF8ToString(target);if(target==="#window")return window;else if(target==="#document")return document;else if(target==="#screen")return screen;else if(target==="#canvas")return Module["canvas"];return typeof target==="string"?document.getElementById(target):target}catch(e){return null}}function __findCanvasEventTarget(target){if(typeof target==="number")target=UTF8ToString(target);if(!target||target==="#canvas"){if(typeof GL!=="undefined"&&GL.offscreenCanvases["canvas"])return GL.offscreenCanvases["canvas"];return Module["canvas"]}if(typeof GL!=="undefined"&&GL.offscreenCanvases[target])return GL.offscreenCanvases[target];return __findEventTarget(target)}function _emscripten_set_canvas_element_size_calling_thread(target,width,height){var canvas=__findCanvasEventTarget(target);if(!canvas)return-4;if(canvas.canvasSharedPtr){HEAP32[canvas.canvasSharedPtr>>2]=width;HEAP32[canvas.canvasSharedPtr+4>>2]=height}if(canvas.offscreenCanvas||!canvas.controlTransferredOffscreen){if(canvas.offscreenCanvas)canvas=canvas.offscreenCanvas;var autoResizeViewport=false;if(canvas.GLctxObject&&canvas.GLctxObject.GLctx){var prevViewport=canvas.GLctxObject.GLctx.getParameter(canvas.GLctxObject.GLctx.VIEWPORT);autoResizeViewport=prevViewport[0]===0&&prevViewport[1]===0&&prevViewport[2]===canvas.width&&prevViewport[3]===canvas.height}canvas.width=width;canvas.height=height;if(autoResizeViewport){canvas.GLctxObject.GLctx.viewport(0,0,width,height)}}else if(canvas.canvasSharedPtr){var targetThread=HEAP32[canvas.canvasSharedPtr+8>>2];_emscripten_set_offscreencanvas_size_on_target_thread(targetThread,target,width,height);return 1}else{return-4}return 0}function _emscripten_set_canvas_element_size_main_thread(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(1,1,target,width,height);return _emscripten_set_canvas_element_size_calling_thread(target,width,height)}function _emscripten_set_canvas_element_size(target,width,height){var canvas=__findCanvasEventTarget(target);if(canvas)return _emscripten_set_canvas_element_size_calling_thread(target,width,height);else return _emscripten_set_canvas_element_size_main_thread(target,width,height)}function _emscripten_syscall(which,varargs){switch(which){default:throw"surprising proxied syscall: "+which}}function _emscripten_webgl_create_context(){err("missing function: emscripten_webgl_create_context");abort(-1)}function _longjmp(env,value){_setThrew(env,value||1);throw"longjmp"}function _emscripten_memcpy_big(dest,src,num){HEAPU8.set(HEAPU8.subarray(src,src+num),dest)}function _ogvjs_callback_async_complete(ret,cpuTime){var callback=Module.callbacks.shift();Module["cpuTime"]+=cpuTime;callback(ret);return}function _ogvjs_callback_frame(bufferY,strideY,bufferCb,strideCb,bufferCr,strideCr,width,height,chromaWidth,chromaHeight,picWidth,picHeight,picX,picY,displayWidth,displayHeight){var HEAPU8=Module["HEAPU8"],format=Module["videoFormat"];function copyAndTrim(buffer,stride,height,picX,picY,picWidth,picHeight,fill){var arr=copyByteArray(HEAPU8.subarray(buffer,buffer+stride*height));var x,y,ptr;for(ptr=0,y=0;y<picY;y++,ptr+=stride){for(x=0;x<stride;x++){arr[ptr+x]=fill}}for(;y<picY+picHeight;y++,ptr+=stride){for(x=0;x<picX;x++){arr[ptr+x]=fill}for(x=picX+picWidth;x<stride;x++){arr[ptr+x]=fill}}for(;y<height;y++,ptr+=stride){for(x=0;x<stride;x++){arr[ptr+x]=fill}}return arr}var outPicX=picX&~1;var outPicY=picY&~1;var chromaPicX=outPicX*chromaWidth/width;var chromaPicY=outPicY*chromaHeight/height;var chromaPicWidth=picWidth*chromaWidth/width;var chromaPicHeight=picHeight*chromaHeight/height;var isOriginal=picWidth===format["cropWidth"]&&picHeight===format["cropHeight"];if(isOriginal){displayWidth=format["displayWidth"];displayHeight=format["displayHeight"]}Module["frameBuffer"]={"format":{"width":width,"height":height,"chromaWidth":chromaWidth,"chromaHeight":chromaHeight,"cropLeft":picX,"cropTop":picY,"cropWidth":picWidth,"cropHeight":picHeight,"displayWidth":displayWidth,"displayHeight":displayHeight},"y":{"bytes":copyAndTrim(bufferY,strideY,height,picX,picY,picWidth,picHeight,0),"stride":strideY},"u":{"bytes":copyAndTrim(bufferCb,strideCb,chromaHeight,chromaPicX,chromaPicY,chromaPicWidth,chromaPicHeight,128),"stride":strideCb},"v":{"bytes":copyAndTrim(bufferCr,strideCr,chromaHeight,chromaPicX,chromaPicY,chromaPicWidth,chromaPicHeight,128),"stride":strideCr}}}function _pthread_cleanup_pop(execute){var routine=PThread.exitHandlers.pop();if(execute)routine()}function _pthread_cleanup_push(routine,arg){if(PThread.exitHandlers===null){PThread.exitHandlers=[];if(!ENVIRONMENT_IS_PTHREAD){__ATEXIT__.push(function(){PThread.runExitHandlers()})}}PThread.exitHandlers.push(function(){dynCall_vi(routine,arg)})}function __spawn_thread(threadParams){if(ENVIRONMENT_IS_PTHREAD)throw"Internal Error! _spawn_thread() can only ever be called from main application thread!";var worker=PThread.getNewWorker();if(worker.pthread!==undefined)throw"Internal error!";if(!threadParams.pthread_ptr)throw"Internal error, no pthread ptr!";PThread.runningWorkers.push(worker);var tlsMemory=_malloc(128*4);for(var i=0;i<128;++i){HEAP32[tlsMemory+i*4>>2]=0}var pthread=PThread.pthreads[threadParams.pthread_ptr]={worker:worker,stackBase:threadParams.stackBase,stackSize:threadParams.stackSize,allocatedOwnStack:threadParams.allocatedOwnStack,thread:threadParams.pthread_ptr,threadInfoStruct:threadParams.pthread_ptr};Atomics.store(HEAPU32,pthread.threadInfoStruct+0>>2,0);Atomics.store(HEAPU32,pthread.threadInfoStruct+4>>2,0);Atomics.store(HEAPU32,pthread.threadInfoStruct+20>>2,0);Atomics.store(HEAPU32,pthread.threadInfoStruct+80>>2,threadParams.detached);Atomics.store(HEAPU32,pthread.threadInfoStruct+116>>2,tlsMemory);Atomics.store(HEAPU32,pthread.threadInfoStruct+60>>2,0);Atomics.store(HEAPU32,pthread.threadInfoStruct+52>>2,pthread.threadInfoStruct);Atomics.store(HEAPU32,pthread.threadInfoStruct+56>>2,PROCINFO.pid);Atomics.store(HEAPU32,pthread.threadInfoStruct+120>>2,threadParams.stackSize);Atomics.store(HEAPU32,pthread.threadInfoStruct+96>>2,threadParams.stackSize);Atomics.store(HEAPU32,pthread.threadInfoStruct+92>>2,threadParams.stackBase);Atomics.store(HEAPU32,pthread.threadInfoStruct+120+8>>2,threadParams.stackBase);Atomics.store(HEAPU32,pthread.threadInfoStruct+120+12>>2,threadParams.detached);Atomics.store(HEAPU32,pthread.threadInfoStruct+120+20>>2,threadParams.schedPolicy);Atomics.store(HEAPU32,pthread.threadInfoStruct+120+24>>2,threadParams.schedPrio);var global_libc=_emscripten_get_global_libc();var global_locale=global_libc+40;Atomics.store(HEAPU32,pthread.threadInfoStruct+188>>2,global_locale);worker.pthread=pthread;var msg={cmd:"run",start_routine:threadParams.startRoutine,arg:threadParams.arg,threadInfoStruct:threadParams.pthread_ptr,selfThreadId:threadParams.pthread_ptr,parentThreadId:threadParams.parent_pthread_ptr,stackBase:threadParams.stackBase,stackSize:threadParams.stackSize};worker.runPthread=function(){msg.time=performance.now();worker.postMessage(msg,threadParams.transferList)};if(worker.loaded){worker.runPthread();delete worker.runPthread}}function _pthread_getschedparam(thread,policy,schedparam){if(!policy&&!schedparam)return ERRNO_CODES.EINVAL;if(!thread){err("pthread_getschedparam called with a null thread pointer!");return ERRNO_CODES.ESRCH}var self=HEAP32[thread+24>>2];if(self!=thread){err("pthread_getschedparam attempted on thread "+thread+", which does not point to a valid thread, or does not exist anymore!");return ERRNO_CODES.ESRCH}var schedPolicy=Atomics.load(HEAPU32,thread+120+20>>2);var schedPrio=Atomics.load(HEAPU32,thread+120+24>>2);if(policy)HEAP32[policy>>2]=schedPolicy;if(schedparam)HEAP32[schedparam>>2]=schedPrio;return 0}function _pthread_create(pthread_ptr,attr,start_routine,arg){if(typeof SharedArrayBuffer==="undefined"){err("Current environment does not support SharedArrayBuffer, pthreads are not available!");return 11}if(!pthread_ptr){err("pthread_create called with a null thread pointer!");return 22}var transferList=[];var error=0;if(ENVIRONMENT_IS_PTHREAD&&(transferList.length==0||error)){return _emscripten_sync_run_in_main_thread_4(687865856,pthread_ptr,attr,start_routine,arg)}if(error)return error;var stackSize=0;var stackBase=0;var detached=0;var schedPolicy=0;var schedPrio=0;if(attr){stackSize=HEAP32[attr>>2];stackSize+=81920;stackBase=HEAP32[attr+8>>2];detached=HEAP32[attr+12>>2]!=0;var inheritSched=HEAP32[attr+16>>2]==0;if(inheritSched){var prevSchedPolicy=HEAP32[attr+20>>2];var prevSchedPrio=HEAP32[attr+24>>2];var parentThreadPtr=PThread.currentProxiedOperationCallerThread?PThread.currentProxiedOperationCallerThread:_pthread_self();_pthread_getschedparam(parentThreadPtr,attr+20,attr+24);schedPolicy=HEAP32[attr+20>>2];schedPrio=HEAP32[attr+24>>2];HEAP32[attr+20>>2]=prevSchedPolicy;HEAP32[attr+24>>2]=prevSchedPrio}else{schedPolicy=HEAP32[attr+20>>2];schedPrio=HEAP32[attr+24>>2]}}else{stackSize=2097152}var allocatedOwnStack=stackBase==0;if(allocatedOwnStack){stackBase=_malloc(stackSize)}else{stackBase-=stackSize;assert(stackBase>0)}var threadInfoStruct=_malloc(244);for(var i=0;i<244>>2;++i)HEAPU32[(threadInfoStruct>>2)+i]=0;HEAP32[pthread_ptr>>2]=threadInfoStruct;HEAP32[threadInfoStruct+24>>2]=threadInfoStruct;var headPtr=threadInfoStruct+168;HEAP32[headPtr>>2]=headPtr;var threadParams={stackBase:stackBase,stackSize:stackSize,allocatedOwnStack:allocatedOwnStack,schedPolicy:schedPolicy,schedPrio:schedPrio,detached:detached,startRoutine:start_routine,pthread_ptr:threadInfoStruct,parent_pthread_ptr:_pthread_self(),arg:arg,transferList:transferList};if(ENVIRONMENT_IS_PTHREAD){threadParams.cmd="spawnThread";postMessage(threadParams,transferList)}else{__spawn_thread(threadParams)}return 0}function _exit(status){exit(status)}function _pthread_exit(status){if(!ENVIRONMENT_IS_PTHREAD)_exit(status);else PThread.threadExit(status)}function __cleanup_thread(pthread_ptr){if(ENVIRONMENT_IS_PTHREAD)throw"Internal Error! _cleanup_thread() can only ever be called from main application thread!";if(!pthread_ptr)throw"Internal Error! Null pthread_ptr in _cleanup_thread!";HEAP32[pthread_ptr+24>>2]=0;var pthread=PThread.pthreads[pthread_ptr];var worker=pthread.worker;PThread.freeThreadData(pthread);worker.pthread=undefined;PThread.unusedWorkerPool.push(worker);PThread.runningWorkers.splice(PThread.runningWorkers.indexOf(worker.pthread),1)}function __pthread_testcancel_js(){if(!ENVIRONMENT_IS_PTHREAD)return;if(!threadInfoStruct)return;var cancelDisabled=Atomics.load(HEAPU32,threadInfoStruct+72>>2);if(cancelDisabled)return;var canceled=Atomics.load(HEAPU32,threadInfoStruct+0>>2);if(canceled==2)throw"Canceled!"}function _pthread_join(thread,status){if(!thread){err("pthread_join attempted on a null thread pointer!");return ERRNO_CODES.ESRCH}if(ENVIRONMENT_IS_PTHREAD&&selfThreadId==thread){err("PThread "+thread+" is attempting to join to itself!");return ERRNO_CODES.EDEADLK}else if(!ENVIRONMENT_IS_PTHREAD&&PThread.mainThreadBlock==thread){err("Main thread "+thread+" is attempting to join to itself!");return ERRNO_CODES.EDEADLK}var self=HEAP32[thread+24>>2];if(self!=thread){err("pthread_join attempted on thread "+thread+", which does not point to a valid thread, or does not exist anymore!");return ERRNO_CODES.ESRCH}var detached=Atomics.load(HEAPU32,thread+80>>2);if(detached){err("Attempted to join thread "+thread+", which was already detached!");return ERRNO_CODES.EINVAL}for(;;){var threadStatus=Atomics.load(HEAPU32,thread+0>>2);if(threadStatus==1){var threadExitCode=Atomics.load(HEAPU32,thread+4>>2);if(status)HEAP32[status>>2]=threadExitCode;Atomics.store(HEAPU32,thread+80>>2,1);if(!ENVIRONMENT_IS_PTHREAD)__cleanup_thread(thread);else postMessage({cmd:"cleanupThread",thread:thread});return 0}__pthread_testcancel_js();if(!ENVIRONMENT_IS_PTHREAD)_emscripten_main_thread_process_queued_calls();_emscripten_futex_wait(thread+0,threadStatus,ENVIRONMENT_IS_PTHREAD?100:1)}}function _sched_yield(){return 0}function _sysconf(name){if(ENVIRONMENT_IS_PTHREAD)return _emscripten_proxy_to_main_thread_js(2,1,name);switch(name){case 30:return PAGE_SIZE;case 85:var maxHeapSize=2*1024*1024*1024-65536;maxHeapSize=HEAPU8.length;return maxHeapSize/PAGE_SIZE;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:{if(typeof navigator==="object")return navigator["hardwareConcurrency"]||1;return 1}}___setErrNo(22);return-1}if(!ENVIRONMENT_IS_PTHREAD)PThread.initMainThreadBlock();if(ENVIRONMENT_IS_NODE){_emscripten_get_now=function _emscripten_get_now_actual(){var t=process["hrtime"]();return t[0]*1e3+t[1]/1e6}}else if(ENVIRONMENT_IS_PTHREAD){_emscripten_get_now=function(){return performance["now"]()-__performance_now_clock_drift}}else if(typeof dateNow!=="undefined"){_emscripten_get_now=dateNow}else if(typeof self==="object"&&self["performance"]&&typeof self["performance"]["now"]==="function"){_emscripten_get_now=function(){return self["performance"]["now"]()}}else if(typeof performance==="object"&&typeof performance["now"]==="function"){_emscripten_get_now=function(){return performance["now"]()}}else{_emscripten_get_now=Date.now}var proxiedFunctionTable=[null,_emscripten_set_canvas_element_size_main_thread,_sysconf];function invoke_i(index){var sp=stackSave();try{return dynCall_i(index)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return dynCall_ii(index,a1)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_iii(index,a1,a2){var sp=stackSave();try{return dynCall_iii(index,a1,a2)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return dynCall_iiii(index,a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_iiiij(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return dynCall_iiiij(index,a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{dynCall_vi(index,a1)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{dynCall_vii(index,a1,a2)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{dynCall_viii(index,a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{dynCall_viiii(index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0&&e!=="longjmp")throw e;_setThrew(1,0)}}var asmGlobalArg={};var asmLibraryArg={"d":abort,"b":setTempRet0,"c":getTempRet0,"C":invoke_i,"u":invoke_ii,"n":invoke_iii,"t":invoke_iiii,"v":invoke_iiiij,"h":invoke_vi,"s":invoke_vii,"r":invoke_viii,"m":invoke_viiii,"f":___assert_fail,"o":___call_main,"O":___clock_gettime,"N":_abort,"l":_emscripten_asm_const_i,"M":_emscripten_asm_const_ii,"j":_emscripten_futex_wait,"g":_emscripten_futex_wake,"L":_emscripten_get_heap_size,"i":_emscripten_get_now,"K":_emscripten_has_threading_support,"J":_emscripten_memcpy_big,"I":_emscripten_num_logical_cores,"H":_emscripten_receive_on_main_thread_js,"G":_emscripten_set_canvas_element_size,"F":_emscripten_syscall,"E":_emscripten_webgl_create_context,"e":_longjmp,"D":_ogvjs_callback_async_complete,"B":_ogvjs_callback_frame,"q":_pthread_cleanup_pop,"p":_pthread_cleanup_push,"k":_pthread_create,"A":_pthread_exit,"z":_pthread_join,"y":_sched_yield,"x":_sysconf,"w":abortOnCannotGrowMemory,"a":DYNAMICTOP_PTR};var asm=Module["asm"](asmGlobalArg,asmLibraryArg,buffer);Module["asm"]=asm;var ___emscripten_pthread_data_constructor=Module["___emscripten_pthread_data_constructor"]=function(){return Module["asm"]["P"].apply(null,arguments)};var ___pthread_tsd_run_dtors=Module["___pthread_tsd_run_dtors"]=function(){return Module["asm"]["Q"].apply(null,arguments)};var __emscripten_atomic_fetch_and_add_u64=Module["__emscripten_atomic_fetch_and_add_u64"]=function(){return Module["asm"]["R"].apply(null,arguments)};var __emscripten_atomic_fetch_and_and_u64=Module["__emscripten_atomic_fetch_and_and_u64"]=function(){return Module["asm"]["S"].apply(null,arguments)};var __emscripten_atomic_fetch_and_or_u64=Module["__emscripten_atomic_fetch_and_or_u64"]=function(){return Module["asm"]["T"].apply(null,arguments)};var __emscripten_atomic_fetch_and_sub_u64=Module["__emscripten_atomic_fetch_and_sub_u64"]=function(){return Module["asm"]["U"].apply(null,arguments)};var __emscripten_atomic_fetch_and_xor_u64=Module["__emscripten_atomic_fetch_and_xor_u64"]=function(){return Module["asm"]["V"].apply(null,arguments)};var __register_pthread_ptr=Module["__register_pthread_ptr"]=function(){return Module["asm"]["W"].apply(null,arguments)};var _emscripten_async_queue_call_on_thread=Module["_emscripten_async_queue_call_on_thread"]=function(){return Module["asm"]["X"].apply(null,arguments)};var _emscripten_async_queue_on_thread_=Module["_emscripten_async_queue_on_thread_"]=function(){return Module["asm"]["Y"].apply(null,arguments)};var _emscripten_async_run_in_main_thread=Module["_emscripten_async_run_in_main_thread"]=function(){return Module["asm"]["Z"].apply(null,arguments)};var _emscripten_atomic_add_u64=Module["_emscripten_atomic_add_u64"]=function(){return Module["asm"]["_"].apply(null,arguments)};var _emscripten_atomic_and_u64=Module["_emscripten_atomic_and_u64"]=function(){return Module["asm"]["$"].apply(null,arguments)};var _emscripten_atomic_cas_u64=Module["_emscripten_atomic_cas_u64"]=function(){return Module["asm"]["aa"].apply(null,arguments)};var _emscripten_atomic_exchange_u64=Module["_emscripten_atomic_exchange_u64"]=function(){return Module["asm"]["ba"].apply(null,arguments)};var _emscripten_atomic_load_f32=Module["_emscripten_atomic_load_f32"]=function(){return Module["asm"]["ca"].apply(null,arguments)};var _emscripten_atomic_load_f64=Module["_emscripten_atomic_load_f64"]=function(){return Module["asm"]["da"].apply(null,arguments)};var _emscripten_atomic_load_u64=Module["_emscripten_atomic_load_u64"]=function(){return Module["asm"]["ea"].apply(null,arguments)};var _emscripten_atomic_or_u64=Module["_emscripten_atomic_or_u64"]=function(){return Module["asm"]["fa"].apply(null,arguments)};var _emscripten_atomic_store_f32=Module["_emscripten_atomic_store_f32"]=function(){return Module["asm"]["ga"].apply(null,arguments)};var _emscripten_atomic_store_f64=Module["_emscripten_atomic_store_f64"]=function(){return Module["asm"]["ha"].apply(null,arguments)};var _emscripten_atomic_store_u64=Module["_emscripten_atomic_store_u64"]=function(){return Module["asm"]["ia"].apply(null,arguments)};var _emscripten_atomic_sub_u64=Module["_emscripten_atomic_sub_u64"]=function(){return Module["asm"]["ja"].apply(null,arguments)};var _emscripten_atomic_xor_u64=Module["_emscripten_atomic_xor_u64"]=function(){return Module["asm"]["ka"].apply(null,arguments)};var _emscripten_current_thread_process_queued_calls=Module["_emscripten_current_thread_process_queued_calls"]=function(){return Module["asm"]["la"].apply(null,arguments)};var _emscripten_get_global_libc=Module["_emscripten_get_global_libc"]=function(){return Module["asm"]["ma"].apply(null,arguments)};var _emscripten_main_browser_thread_id=Module["_emscripten_main_browser_thread_id"]=function(){return Module["asm"]["na"].apply(null,arguments)};var _emscripten_main_thread_process_queued_calls=Module["_emscripten_main_thread_process_queued_calls"]=function(){return Module["asm"]["oa"].apply(null,arguments)};var _emscripten_register_main_browser_thread_id=Module["_emscripten_register_main_browser_thread_id"]=function(){return Module["asm"]["pa"].apply(null,arguments)};var _emscripten_run_in_main_runtime_thread_js=Module["_emscripten_run_in_main_runtime_thread_js"]=function(){return Module["asm"]["qa"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread=Module["_emscripten_sync_run_in_main_thread"]=function(){return Module["asm"]["ra"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_0=Module["_emscripten_sync_run_in_main_thread_0"]=function(){return Module["asm"]["sa"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_1=Module["_emscripten_sync_run_in_main_thread_1"]=function(){return Module["asm"]["ta"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_2=Module["_emscripten_sync_run_in_main_thread_2"]=function(){return Module["asm"]["ua"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_3=Module["_emscripten_sync_run_in_main_thread_3"]=function(){return Module["asm"]["va"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_4=Module["_emscripten_sync_run_in_main_thread_4"]=function(){return Module["asm"]["wa"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_5=Module["_emscripten_sync_run_in_main_thread_5"]=function(){return Module["asm"]["xa"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_6=Module["_emscripten_sync_run_in_main_thread_6"]=function(){return Module["asm"]["ya"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_7=Module["_emscripten_sync_run_in_main_thread_7"]=function(){return Module["asm"]["za"].apply(null,arguments)};var _emscripten_sync_run_in_main_thread_xprintf_varargs=Module["_emscripten_sync_run_in_main_thread_xprintf_varargs"]=function(){return Module["asm"]["Aa"].apply(null,arguments)};var _free=Module["_free"]=function(){return Module["asm"]["Ba"].apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return Module["asm"]["Ca"].apply(null,arguments)};var _ogv_video_decoder_async=Module["_ogv_video_decoder_async"]=function(){return Module["asm"]["Da"].apply(null,arguments)};var _ogv_video_decoder_destroy=Module["_ogv_video_decoder_destroy"]=function(){return Module["asm"]["Ea"].apply(null,arguments)};var _ogv_video_decoder_init=Module["_ogv_video_decoder_init"]=function(){return Module["asm"]["Fa"].apply(null,arguments)};var _ogv_video_decoder_process_frame=Module["_ogv_video_decoder_process_frame"]=function(){return Module["asm"]["Ga"].apply(null,arguments)};var _ogv_video_decoder_process_header=Module["_ogv_video_decoder_process_header"]=function(){return Module["asm"]["Ha"].apply(null,arguments)};var _proxy_main=Module["_proxy_main"]=function(){return Module["asm"]["Ia"].apply(null,arguments)};var _pthread_self=Module["_pthread_self"]=function(){return Module["asm"]["Ja"].apply(null,arguments)};var _setThrew=Module["_setThrew"]=function(){return Module["asm"]["Ka"].apply(null,arguments)};var establishStackSpace=Module["establishStackSpace"]=function(){return Module["asm"]["Va"].apply(null,arguments)};var stackAlloc=Module["stackAlloc"]=function(){return Module["asm"]["Wa"].apply(null,arguments)};var stackRestore=Module["stackRestore"]=function(){return Module["asm"]["Xa"].apply(null,arguments)};var stackSave=Module["stackSave"]=function(){return Module["asm"]["Ya"].apply(null,arguments)};var dynCall_i=Module["dynCall_i"]=function(){return Module["asm"]["La"].apply(null,arguments)};var dynCall_ii=Module["dynCall_ii"]=function(){return Module["asm"]["Ma"].apply(null,arguments)};var dynCall_iii=Module["dynCall_iii"]=function(){return Module["asm"]["Na"].apply(null,arguments)};var dynCall_iiii=Module["dynCall_iiii"]=function(){return Module["asm"]["Oa"].apply(null,arguments)};var dynCall_iiiij=Module["dynCall_iiiij"]=function(){return Module["asm"]["Pa"].apply(null,arguments)};var dynCall_v=Module["dynCall_v"]=function(){return Module["asm"]["Qa"].apply(null,arguments)};var dynCall_vi=Module["dynCall_vi"]=function(){return Module["asm"]["Ra"].apply(null,arguments)};var dynCall_vii=Module["dynCall_vii"]=function(){return Module["asm"]["Sa"].apply(null,arguments)};var dynCall_viii=Module["dynCall_viii"]=function(){return Module["asm"]["Ta"].apply(null,arguments)};var dynCall_viiii=Module["dynCall_viiii"]=function(){return Module["asm"]["Ua"].apply(null,arguments)};Module["asm"]=asm;Module["establishStackSpace"]=establishStackSpace;Module["PThread"]=PThread;Module["ExitStatus"]=ExitStatus;Module["dynCall_ii"]=dynCall_ii;if(memoryInitializer&&!ENVIRONMENT_IS_PTHREAD){if(!isDataURI(memoryInitializer)){memoryInitializer=locateFile(memoryInitializer)}if(ENVIRONMENT_IS_NODE||ENVIRONMENT_IS_SHELL){var data=Module["readBinary"](memoryInitializer);HEAPU8.set(data,GLOBAL_BASE)}else{addRunDependency("memory initializer");var applyMemoryInitializer=function(data){if(data.byteLength)data=new Uint8Array(data);HEAPU8.set(data,GLOBAL_BASE);if(Module["memoryInitializerRequest"])delete Module["memoryInitializerRequest"].response;removeRunDependency("memory initializer")};var doBrowserLoad=function(){Module["readAsync"](memoryInitializer,applyMemoryInitializer,function(){throw"could not load memory initializer "+memoryInitializer})};if(Module["memoryInitializerRequest"]){var useRequest=function(){var request=Module["memoryInitializerRequest"];var response=request.response;if(request.status!==200&&request.status!==0){console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+request.status+", retrying "+memoryInitializer);doBrowserLoad();return}applyMemoryInitializer(response)};if(Module["memoryInitializerRequest"].response){setTimeout(useRequest,0)}else{Module["memoryInitializerRequest"].addEventListener("load",useRequest)}}else{doBrowserLoad()}}}Module["then"]=function(func){if(Module["calledRun"]){func(Module)}else{var old=Module["onRuntimeInitialized"];Module["onRuntimeInitialized"]=function(){if(old)old();func(Module)}}return Module};function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}ExitStatus.prototype=new Error;ExitStatus.prototype.constructor=ExitStatus;dependenciesFulfilled=function runCaller(){if(!Module["calledRun"])run();if(!Module["calledRun"])dependenciesFulfilled=runCaller};function run(args){args=args||Module["arguments"];if(runDependencies>0){return}preRun();if(runDependencies>0)return;if(Module["calledRun"])return;function doRun(){if(Module["calledRun"])return;Module["calledRun"]=true;if(ABORT)return;ensureInitRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;function exit(status,implicit){if(implicit&&Module["noExitRuntime"]&&status===0){return}if(Module["noExitRuntime"]){}else{PThread.terminateAllThreads();ABORT=true;EXITSTATUS=status;exitRuntime();if(Module["onExit"])Module["onExit"](status)}Module["quit"](status,new ExitStatus(status))}function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}if(ENVIRONMENT_IS_PTHREAD)console.error("Pthread aborting at "+(new Error).stack);if(what!==undefined){out(what);err(what);what=JSON.stringify(what)}else{what=""}ABORT=true;EXITSTATUS=1;throw"abort("+what+"). Build with -s ASSERTIONS=1 for more info."}Module["abort"]=abort;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}if(!ENVIRONMENT_IS_PTHREAD)Module["noExitRuntime"]=true;if(!ENVIRONMENT_IS_PTHREAD)run();var inputBuffer,inputBufferSize;function reallocInputBuffer(size){if(inputBuffer&&inputBufferSize>=size){return inputBuffer}if(inputBuffer){Module["_free"](inputBuffer)}inputBufferSize=size;inputBuffer=Module["_malloc"](inputBufferSize);return inputBuffer}var getTimestamp;if(typeof performance==="undefined"||typeof performance.now==="undefined"){getTimestamp=Date.now}else{getTimestamp=performance.now.bind(performance)}function time(func){var start=getTimestamp(),ret;ret=func();Module["cpuTime"]+=getTimestamp()-start;return ret}function copyByteArray(bytes){var heap=bytes.buffer;if(typeof heap.slice==="function"){var extract=heap.slice(bytes.byteOffset,bytes.byteOffset+bytes.byteLength);return new Uint8Array(extract)}else{return new Uint8Array(bytes)}}Module["loadedMetadata"]=!!options["videoFormat"];Module["videoFormat"]=options["videoFormat"]||null;Module["frameBuffer"]=null;Module["cpuTime"]=0;Object.defineProperty(Module,"processing",{get:function getProcessing(){return false}});Module["init"]=function(callback){time(function(){Module["_ogv_video_decoder_init"]()});callback()};Module["processHeader"]=function(data,callback){var ret=time(function(){var len=data.byteLength;var buffer=reallocInputBuffer(len);Module["HEAPU8"].set(new Uint8Array(data),buffer);return Module["_ogv_video_decoder_process_header"](buffer,len)});callback(ret)};Module.callbacks=[];Module["processFrame"]=function(data,callback){var isAsync=Module["_ogv_video_decoder_async"]();var len=data.byteLength;var buffer=Module["_malloc"](len);function callbackWrapper(ret){Module["_free"](buffer);callback(ret)}if(isAsync){Module.callbacks.push(callbackWrapper)}var ret=time(function(){Module["HEAPU8"].set(new Uint8Array(data),buffer);return Module["_ogv_video_decoder_process_frame"](buffer,len)});if(!isAsync){callbackWrapper(ret)}};Module["close"]=function(){};Module["sync"]=function(){var isAsync=Module["_ogv_video_decoder_async"]();if(isAsync){Module.callbacks.push(function(){});time(function(){Module["_ogv_video_decoder_process_frame"](0,0)})}};


  return OGVDecoderVideoVP8MTW
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
      module.exports = OGVDecoderVideoVP8MTW;
    else if (typeof define === 'function' && define['amd'])
      define([], function() { return OGVDecoderVideoVP8MTW; });
    else if (typeof exports === 'object')
      exports["OGVDecoderVideoVP8MTW"] = OGVDecoderVideoVP8MTW;
    