
var OGVDecoderVideoVP8 = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  return (
function(OGVDecoderVideoVP8) {
  OGVDecoderVideoVP8 = OGVDecoderVideoVP8 || {};

var b;b||(b=typeof OGVDecoderVideoVP8 !== 'undefined' ? OGVDecoderVideoVP8 : {});var g=b;b.memoryLimit&&(b.TOTAL_MEMORY=g.memoryLimit);var n={},p;for(p in b)b.hasOwnProperty(p)&&(n[p]=b[p]);b.arguments=[];b.thisProgram="./this.program";b.quit=function(a,c){throw c;};b.preRun=[];b.postRun=[];var q=!1,r=!1,t=!1,u=!1;q="object"===typeof window;r="function"===typeof importScripts;t="object"===typeof process&&"function"===typeof require&&!q&&!r;u=!q&&!t&&!r;var v="";
if(t){v=__dirname+"/";var x,z;b.read=function(a,c){var d=A(a);d||(x||(x=require("fs")),z||(z=require("path")),a=z.normalize(a),d=x.readFileSync(a));return c?d:d.toString()};b.readBinary=function(a){a=b.read(a,!0);a.buffer||(a=new Uint8Array(a));assert(a.buffer);return a};1<process.argv.length&&(b.thisProgram=process.argv[1].replace(/\\/g,"/"));b.arguments=process.argv.slice(2);process.on("unhandledRejection",B);b.quit=function(a){process.exit(a)};b.inspect=function(){return"[Emscripten Module object]"}}else if(u)"undefined"!=
typeof read&&(b.read=function(a){var c=A(a);return c?aa(c):read(a)}),b.readBinary=function(a){var c;if(c=A(a))return c;if("function"===typeof readbuffer)return new Uint8Array(readbuffer(a));c=read(a,"binary");assert("object"===typeof c);return c},"undefined"!=typeof scriptArgs?b.arguments=scriptArgs:"undefined"!=typeof arguments&&(b.arguments=arguments),"function"===typeof quit&&(b.quit=function(a){quit(a)});else if(q||r)r?v=self.location.href:document.currentScript&&(v=document.currentScript.src),
_scriptDir&&(v=_scriptDir),0!==v.indexOf("blob:")?v=v.substr(0,v.lastIndexOf("/")+1):v="",b.read=function(a){try{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText}catch(d){if(a=A(a))return aa(a);throw d;}},r&&(b.readBinary=function(a){try{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}catch(d){if(a=A(a))return a;throw d;}}),b.readAsync=function(a,c,d){var e=new XMLHttpRequest;e.open("GET",a,!0);e.responseType=
"arraybuffer";e.onload=function(){if(200==e.status||0==e.status&&e.response)c(e.response);else{var f=A(a);f?c(f.buffer):d()}};e.onerror=d;e.send(null)},b.setWindowTitle=function(a){document.title=a};var ba=b.print||("undefined"!==typeof console?console.log.bind(console):"undefined"!==typeof print?print:null),ca=b.printErr||("undefined"!==typeof printErr?printErr:"undefined"!==typeof console&&console.warn.bind(console)||ba);for(p in n)n.hasOwnProperty(p)&&(b[p]=n[p]);n=void 0;var da=0,ea=!1;
function assert(a,c){a||B("Assertion failed: "+c)}"undefined"!==typeof TextDecoder&&new TextDecoder("utf8");"undefined"!==typeof TextDecoder&&new TextDecoder("utf-16le");var buffer,C,D,E=b.TOTAL_MEMORY||33554432;5242880>E&&ca("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+E+"! (TOTAL_STACK=5242880)");b.buffer?buffer=b.buffer:(buffer=new ArrayBuffer(E),b.buffer=buffer);b.HEAP8=new Int8Array(buffer);b.HEAP16=new Int16Array(buffer);b.HEAP32=D=new Int32Array(buffer);b.HEAPU8=C=new Uint8Array(buffer);
b.HEAPU16=new Uint16Array(buffer);b.HEAPU32=new Uint32Array(buffer);b.HEAPF32=new Float32Array(buffer);b.HEAPF64=new Float64Array(buffer);D[1832]=5250464;function F(a){for(;0<a.length;){var c=a.shift();if("function"==typeof c)c();else{var d=c.B;"number"===typeof d?void 0===c.w?b.dynCall_v(d):b.dynCall_vi(d,c.w):d(void 0===c.w?null:c.w)}}}var fa=[],ha=[],ia=[],ja=[],ka=!1;function la(){var a=b.preRun.shift();fa.unshift(a)}
Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(a,c){var d=a&65535,e=c&65535;return d*e+((a>>>16)*e+d*(c>>>16)<<16)|0});Math.clz32||(Math.clz32=function(a){var c=32,d=a>>16;d&&(c-=16,a=d);if(d=a>>8)c-=8,a=d;if(d=a>>4)c-=4,a=d;if(d=a>>2)c-=2,a=d;return a>>1?c-2:c-a});Math.trunc||(Math.trunc=function(a){return 0>a?Math.ceil(a):Math.floor(a)});var H=0,I=null,J=null;b.preloadedImages={};b.preloadedAudios={};var K=null,O="data:application/octet-stream;base64,";K="data:application/octet-stream;base64,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";
function ma(){B("OOM")}var na=!1;function aa(a){for(var c=[],d=0;d<a.length;d++){var e=a[d];255<e&&(na&&assert(!1,"Character code "+e+" ("+String.fromCharCode(e)+")  at offset "+d+" not in 0x00-0xFF."),e&=255);c.push(String.fromCharCode(e))}return c.join("")}
var ua="function"===typeof atob?atob:function(a){var c="",d=0;a=a.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));var h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));var l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));e=e<<2|f>>4;
f=(f&15)<<4|h>>2;var m=(h&3)<<6|l;c+=String.fromCharCode(e);64!==h&&(c+=String.fromCharCode(f));64!==l&&(c+=String.fromCharCode(m))}while(d<a.length);return c};
function A(a){if(String.prototype.startsWith?a.startsWith(O):0===a.indexOf(O)){a=a.slice(O.length);if("boolean"===typeof t&&t){try{var c=Buffer.from(a,"base64")}catch(h){c=new Buffer(a,"base64")}var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength)}else try{var e=ua(a),f=new Uint8Array(e.length);for(c=0;c<e.length;++c)f[c]=e.charCodeAt(c);d=f}catch(h){throw Error("Converting base64 string to bytes failed.");}return d}}
var va={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Float32Array:Float32Array,Float64Array:Float64Array},Ea={a:B,b:function(a){da=a},c:function(){return da},d:function(a,c){var d=P();try{return wa(a,c)}catch(e){Q(d);if(e!==e+0&&"longjmp"!==e)throw e;R(1,0)}},e:function(a,c,d){var e=P();try{return xa(a,c,d)}catch(f){Q(e);if(f!==f+0&&"longjmp"!==f)throw f;R(1,0)}},f:function(a,c,d,e){var f=P();try{return ya(a,c,d,e)}catch(h){Q(f);
if(h!==h+0&&"longjmp"!==h)throw h;R(1,0)}},g:function(a,c,d,e,f,h){var l=P();try{return za(a,c,d,e,f,h)}catch(m){Q(l);if(m!==m+0&&"longjmp"!==m)throw m;R(1,0)}},h:function(a){var c=P();try{Aa(a)}catch(d){Q(c);if(d!==d+0&&"longjmp"!==d)throw d;R(1,0)}},i:function(a,c){var d=P();try{Ba(a,c)}catch(e){Q(d);if(e!==e+0&&"longjmp"!==e)throw e;R(1,0)}},j:function(a,c,d){var e=P();try{Ca(a,c,d)}catch(f){Q(e);if(f!==f+0&&"longjmp"!==f)throw f;R(1,0)}},k:function(a,c,d,e,f){var h=P();try{Da(a,c,d,e,f)}catch(l){Q(h);
if(l!==l+0&&"longjmp"!==l)throw l;R(1,0)}},l:function(a){b.___errno_location&&(D[b.___errno_location()>>2]=a);return a},m:function(){return E},n:function(a,c,d){C.set(C.subarray(c,c+d),a)},o:function(a){ma(a)},p:function(a,c){R(a,c||1);throw"longjmp";},q:function(a,c,d,e,f,h,l,m,V,G,L,M,W,X,oa,pa){function Y(a,c,d,e,f,h,l,m){a=Ma.subarray(a,a+c*d);var k=a.buffer;"function"===typeof k.slice?(a=k.slice(a.byteOffset,a.byteOffset+a.byteLength),a=new Uint8Array(a)):a=new Uint8Array(a);var y,w;for(y=w=
0;y<f;y++,w+=c)for(k=0;k<c;k++)a[w+k]=m;for(;y<f+l;y++,w+=c){for(k=0;k<e;k++)a[w+k]=m;for(k=e+h;k<c;k++)a[w+k]=m}for(;y<d;y++,w+=c)for(k=0;k<c;k++)a[w+k]=m;return a}var Ma=b.HEAPU8,N=b.videoFormat,qa=(W&-2)*V/l,ra=(X&-2)*G/m,sa=L*V/l,ta=M*G/m;L===N.cropWidth&&M===N.cropHeight&&(oa=N.displayWidth,pa=N.displayHeight);b.frameBuffer={format:{width:l,height:m,chromaWidth:V,chromaHeight:G,cropLeft:W,cropTop:X,cropWidth:L,cropHeight:M,displayWidth:oa,displayHeight:pa},y:{bytes:Y(a,c,m,W,X,L,M,0),stride:c},
u:{bytes:Y(d,e,G,qa,ra,sa,ta,128),stride:e},v:{bytes:Y(f,h,G,qa,ra,sa,ta,128),stride:h}}},r:ma,s:7568,t:7328};// EMSCRIPTEN_START_ASM
var S=(/** @suppress {uselessCode} */ function(global,env,buffer) {
"use asm";var a=new global.Int8Array(buffer),b=new global.Int16Array(buffer),c=new global.Int32Array(buffer),d=new global.Uint8Array(buffer),e=new global.Uint16Array(buffer),f=new global.Float32Array(buffer),g=new global.Float64Array(buffer),h=env.s|0,i=env.t|0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=global.Math.imul,s=global.Math.clz32,t=env.a,u=env.b,v=env.c,w=env.d,x=env.e,y=env.f,z=env.g,A=env.h,B=env.i,C=env.j,D=env.k,E=env.l,F=env.m,G=env.n,H=env.o,I=env.p,J=env.q,K=env.r,L=7584,M=5250464,N=0.0;
// EMSCRIPTEN_START_FUNCS
function X(a){a=a|0;var b=0;b=L;L=L+a|0;L=L+15&-16;return b|0}function Y(){return L|0}function Z(a){a=a|0;L=a}function _(a,b){a=a|0;b=b|0;L=a;M=b}function $(){var a=0,b=0,d=0;a=L;L=L+16|0;b=a;d=ya()|0;c[1664]=d;c[b>>2]=0;c[b+4>>2]=0;c[b+8>>2]=0;fa(6660,d,b,0,11)|0;L=a;return}function aa(){return 0}function ba(){return}function ca(a,b){a=a|0;b=b|0;return 0}function da(a,b){a=a|0;b=b|0;if(!a){ea();return 1}else{ga(6660,a,b,0,1)|0;ga(6660,0,0,0,1)|0;ea();return 1}return 0}function ea(){var a=0,b=0,d=0,e=0,f=0,g=0,h=0,i=0;i=L;L=L+16|0;h=i;c[h>>2]=0;a=ha(6660,h)|0;if(!a){L=i;return}b=1;a:while(1){if(!b){a=4;break}f=c[a+28>>2]|0;g=(f&1)+f|0;switch(c[a>>2]|0){case 258:{e=c[a+12>>2]|0;b=g>>1;d=e>>>1;break}case 261:{e=c[a+12>>2]|0;b=g;d=e>>>1;break}case 262:{e=c[a+12>>2]|0;b=g;d=e;break}default:{a=11;break a}}J(c[a+48>>2]|0,c[a+64>>2]|0,c[a+52>>2]|0,c[a+68>>2]|0,c[a+56>>2]|0,c[a+72>>2]|0,e|0,g|0,d|0,b|0,c[a+24>>2]|0,f|0,0,0,c[a+32>>2]|0,c[a+36>>2]|0);a=ha(6660,h)|0;if(!a){a=11;break}else b=0}if((a|0)==4){do{}while((ha(6660,h)|0)!=0);L=i;return}else if((a|0)==11){L=i;return}}function fa(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;do if((f|0)==11)if((a|0)!=0&(b|0)!=0)if((c[b+4>>2]|0)==5){if(e&65536|0?(c[b+8>>2]&262144|0)==0:0){f=4;break}if(e&131072|0?(c[b+8>>2]&524288|0)==0:0){f=4;break}g=c[b+8>>2]|0;if(!((e&262144|0)!=0&(g&1048576|0)==0|(g&1|0)==0)){g=a+8|0;c[g>>2]=0;c[g+4>>2]=0;c[g+8>>2]=0;c[g+12>>2]=0;c[a+4>>2]=b;c[a>>2]=c[b>>2];g=a+24|0;c[g>>2]=0;c[a+16>>2]=e;c[a+20>>2]=d;f=P[c[b+12>>2]&15](a,0)|0;if(!f)f=0;else{b=c[g>>2]|0;if(!b)b=0;else b=c[b>>2]|0;c[a+12>>2]=b;ia(a)|0}}else f=4}else f=3;else{f=8;h=13}else{f=3;h=13}while(0);if((h|0)==13)if(!a){h=f;return h|0}c[a+8>>2]=f;h=f;return h|0}function ga(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;if(!a){a=8;return a|0}if((b|0)==0^(d|0)!=0){h=c[a+4>>2]|0;if((h|0)!=0?(g=c[a+24>>2]|0,(g|0)!=0):0)b=R[c[h+32>>2]&3](g,b,d,e,f)|0;else b=1}else b=8;c[a+8>>2]=b;a=b;return a|0}function ha(a,b){a=a|0;b=b|0;var d=0;if(!((a|0)!=0&(b|0)!=0)){d=0;return d|0}d=c[a+4>>2]|0;if(!d){d=0;return d|0}a=c[a+24>>2]|0;if(!a){d=0;return d|0}d=P[c[d+36>>2]&15](a,b)|0;return d|0}function ia(a){a=a|0;var b=0,d=0,e=0,f=0;if(!a){a=8;return a|0}b=a+4|0;d=c[b>>2]|0;if((d|0)!=0?(e=a+24|0,f=c[e>>2]|0,(f|0)!=0):0){O[c[d+16>>2]&3](f)|0;c[b>>2]=0;c[a>>2]=0;c[e>>2]=0;b=0}else b=1;c[a+8>>2]=b;a=b;return a|0}function ja(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;h=L;L=L+16|0;g=h;c[b>>2]=d;d=b+4|0;c[d>>2]=0;if(e|0){c[d>>2]=1;c[g>>2]=f;fd(b+8|0,79,e,g)|0;a[b+87>>0]=0}if(!(c[b+88>>2]|0)){L=h;return}else I(b+92|0,c[b>>2]|0)}function ka(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;f=L;L=L+128|0;d=f;g=(c[b>>2]|0)+(4-1)&~(4-1);e=c[g>>2]|0;c[b>>2]=g+4;if(!e){g=8;L=f;return g|0}g=c[e+28>>2]|0;i=c[e+32>>2]|0;h=(g+1|0)>>>1;b=(i+1|0)>>>1;c[d+52>>2]=c[e+52>>2];c[d+56>>2]=c[e+56>>2];c[d+60>>2]=c[e+60>>2];c[d+8>>2]=g;c[d+12>>2]=i;c[d>>2]=g;c[d+4>>2]=i;c[d+28>>2]=h;c[d+32>>2]=b;c[d+20>>2]=h;c[d+24>>2]=b;b=c[e+68>>2]|0;c[d+16>>2]=b;c[d+36>>2]=c[e+72>>2];c[d+76>>2]=(b-g|0)>>>1;g=Aa(c[a+348>>2]|0,c[e>>2]|0,d)|0;L=f;return g|0}function la(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;f=L;L=L+128|0;d=f;g=(c[b>>2]|0)+(4-1)&~(4-1);e=c[g>>2]|0;c[b>>2]=g+4;if(!e){g=8;L=f;return g|0}g=c[e+28>>2]|0;i=c[e+32>>2]|0;h=(g+1|0)>>>1;b=(i+1|0)>>>1;c[d+52>>2]=c[e+52>>2];c[d+56>>2]=c[e+56>>2];c[d+60>>2]=c[e+60>>2];c[d+8>>2]=g;c[d+12>>2]=i;c[d>>2]=g;c[d+4>>2]=i;c[d+28>>2]=h;c[d+32>>2]=b;c[d+20>>2]=h;c[d+24>>2]=b;b=c[e+68>>2]|0;c[d+16>>2]=b;c[d+36>>2]=c[e+72>>2];c[d+76>>2]=(b-g|0)>>>1;g=za(c[a+348>>2]|0,c[e>>2]|0,d)|0;L=f;return g|0}function ma(a,b){a=a|0;b=b|0;return 4}function na(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}e=c[a+348>>2]|0;c[d>>2]=(c[e+9152>>2]<<1)+(c[e+9156>>2]<<2)+(c[e+9148>>2]|0);e=0;return e|0}function oa(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;a=c[a+348>>2]|0;if(!((d|0)!=0&(a|0)!=0)){e=8;return e|0}a=c[a+5060>>2]|0;if(!a){e=1;return e|0}c[d>>2]=c[a+112>>2];e=0;return e|0}function pa(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}e=(c[a+348>>2]|0)+3248|0;b=(Da(e,3)|0)==0;a=(Da(e,2)|0)==0;c[d>>2]=(a?0:2)|(b?0:4)|(Da(e,1)|0)!=0;e=0;return e|0}function qa(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){e=8;return e|0}c[d>>2]=Ha(c[a+348>>2]|0)|0;e=0;return e|0}function ra(a,b){a=a|0;b=b|0;var d=0,e=0;e=(c[b>>2]|0)+(4-1)&~(4-1);d=c[e>>2]|0;c[b>>2]=e+4;if(!d){c[a+232>>2]=0;c[a+236>>2]=0;return 0}else{c[a+232>>2]=c[d>>2];c[a+236>>2]=c[d+4>>2];return 0}return 0}function sa(a,b){a=a|0;b=b|0;var d=0,e=0;Wa();Oa();Na();e=a+24|0;if(c[e>>2]|0){e=0;return e|0}b=Ja(1,560)|0;if(!b){e=2;return e|0}c[e>>2]=b;c[b+4>>2]=c[a+16>>2];c[b+196>>2]=16;c[b+232>>2]=0;c[b+236>>2]=0;a=a+20|0;d=c[a>>2]|0;if(d){b=b+184|0;c[b>>2]=c[d>>2];c[b+4>>2]=c[d+4>>2];c[b+8>>2]=c[d+8>>2];c[a>>2]=b;b=c[e>>2]|0}c[b+484>>2]=0;c[b+480>>2]=c[b+4>>2]&262144;e=0;return e|0}function ta(a){a=a|0;Ga(a+348|0)|0;Ka(a);return 0}function ua(b,e,f){b=b|0;e=e|0;f=f|0;var g=0;if((e|0)<1){g=8;return g|0}g=f+12|0;c[g>>2]=0;if(e>>>0<=9){g=5;return g|0}if(a[b>>0]&1){g=5;return g|0}c[g>>2]=1;if((a[b+3>>0]|0)!=-99){g=5;return g|0}if((a[b+4>>0]|0)!=1){g=5;return g|0}if((a[b+5>>0]|0)!=42){g=5;return g|0}e=d[b+7>>0]<<8&16128|d[b+6>>0];c[f+4>>2]=e;g=d[b+9>>0]<<8&16128|d[b+8>>0];c[f+8>>2]=g;g=(e|0)==0|(g|0)==0?7:0;return g|0}function va(a,b){a=a|0;b=b|0;a=a+196|0;c[b>>2]=c[a>>2];c[b+4>>2]=c[a+4>>2];c[b+8>>2]=c[a+8>>2];c[b+12>>2]=c[a+12>>2];c[b>>2]=16;return 0}function wa(b,e,f,g,h){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,w=0,A=0,C=0,E=0,F=0,G=0,H=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0;Q=L;L=L+64|0;K=Q+16|0;J=Q+8|0;H=Q;M=4;P=Ec(40)|0;c[P>>2]=0;o=Q+52|0;O=Q+48|0;r=Q+24|0;G=b+480|0;i=c[G>>2]|0;m=(e|0)==0;if(m&(i|f|0)==0){O=0;Fc(P|0);L=Q;return O|0}c[O>>2]=0;N=b+484|0;n=c[N>>2]|0;if(!n){l=b+488|0;t=l+72|0;do{c[l>>2]=0;l=l+4|0}while((l|0)<(t|0))}l=(i|0)==0;if(!(m&(f|0)==0|l)){c[b+488+(n<<2)>>2]=e;c[b+524+(n<<2)>>2]=f;b=(c[N>>2]|0)+1|0;c[N>>2]=b;if(b>>>0>9){c[N>>2]=0;c[O>>2]=8}O=c[O>>2]|0;Fc(P|0);L=Q;return O|0}i=b+488|0;if(l){c[i>>2]=e;c[b+524>>2]=f;c[N>>2]=1;i=e;n=f}else{i=c[i>>2]|0;n=c[b+524>>2]|0}A=b+200|0;C=c[A>>2]|0;E=b+204|0;F=c[E>>2]|0;p=b+232|0;l=c[p>>2]|0;q=b+236|0;m=c[q>>2]|0;a:do if((n|0)<1){i=8;l=23}else{if(l){j=0;D(l|0,m|0,i|0,o|0,(n>>>0<10?n:10)|0);i=j;j=0;if((i|0)!=0&(k|0)!=0){l=rd(c[i>>2]|0,P|0,M|0)|0;if(!l)I(i|0,k|0);u(k|0)}else l=-1;i=v()|0;if((l|0)==1){q=0;m=0;n=0;r=0;s=0;p=0;l=46;break}i=o}l=b+208|0;c[l>>2]=0;do if(n>>>0>9?(a[i>>0]&1)==0:0){c[l>>2]=1;if(((a[i+3>>0]|0)==-99?(a[i+4>>0]|0)==1:0)?(a[i+5>>0]|0)==42:0){w=d[i+7>>0]<<8&16128|d[i+6>>0];c[A>>2]=w;i=d[i+9>>0]<<8&16128|d[i+8>>0];c[E>>2]=i;i=(w|0)==0|(i|0)==0?7:0;break}i=5;l=23;break a}else i=5;while(0);l=23}while(0);do if((l|0)==23){c[O>>2]=i;if((c[O>>2]|0)==5?(c[b+208>>2]|0)==0:0)c[O>>2]=0;n=b+212|0;i=c[n>>2]|0;l=(i|0)==0;if(l?(c[b+208>>2]|0)==0:0)c[O>>2]=5;m=c[E>>2]|0;if((m|0)==(F|0)?(c[A>>2]|0)==(C|0):0)o=0;else o=1;if(!((c[O>>2]|0)!=0|l^1)){c[r>>2]=c[A>>2];c[r+4>>2]=m;c[r+8>>2]=9;c[r+12>>2]=0;c[r+16>>2]=c[b+184>>2];w=c[b+4>>2]|0;c[r+20>>2]=w&131072;if(!((w&65536|0)==0|(c[b+216>>2]|0)!=0)){c[b+220>>2]=1027;c[b+224>>2]=4;c[b+228>>2]=0}j=0;l=x(12,b+348|0,r|0)|0;i=j;j=0;if((i|0)!=0&(k|0)!=0){m=rd(c[i>>2]|0,P|0,M|0)|0;if(!m)I(i|0,k|0);u(k|0)}else m=-1;i=v()|0;if((m|0)==1){q=0;m=0;n=0;r=0;s=0;p=0;l=46;break}c[O>>2]=l;if(!(c[O>>2]|0)){c[n>>2]=1;i=1}else i=c[n>>2]|0}if(i|0){w=b+348|0;c[(c[w>>2]|0)+11852>>2]=c[p>>2];c[(c[w>>2]|0)+11856>>2]=c[q>>2]}if(!(c[O>>2]|0)){p=c[b+348>>2]|0;if(o){o=p+3248|0;n=p+5040|0;c[n>>2]=c[A>>2];m=p+5044|0;c[m>>2]=c[E>>2];P=qd(p+3340|0,1,P|0,M|0)|0;M=v()|0;j=0;i=j;j=0;if((i|0)!=0&(k|0)!=0){l=rd(c[i>>2]|0,P|0,M|0)|0;if(!l)I(i|0,k|0);u(k|0)}else l=-1;i=v()|0;if((l|0)==1){q=o;r=p;s=o;l=46}else{i=0;q=o;r=p;s=o;l=46}}else{q=0;m=0;n=0;r=0;s=0;l=59}}else l=66}while(0);while(1)if((l|0)==46){l=0;w=p+3336|0;if(i|0)break;c[w>>2]=1;if((c[n>>2]|0)<1){c[n>>2]=C;j=0;D(1,q|0,7,5955,H|0);i=j;j=0;if((i|0)!=0&(k|0)!=0){l=rd(c[i>>2]|0,P|0,M|0)|0;if(!l)I(i|0,k|0);u(k|0)}else l=-1;i=v()|0;if((l|0)==1){S=p;R=s;o=r;t=n;w=m;l=q;p=S;s=R;r=o;n=t;m=w;q=l;l=46;continue}}i=c[m>>2]|0;if((i|0)<1){c[m>>2]=F;j=0;D(1,q|0,7,5975,J|0);i=j;j=0;if((i|0)!=0&(k|0)!=0){l=rd(c[i>>2]|0,P|0,M|0)|0;if(!l)I(i|0,k|0);u(k|0)}else l=-1;i=v()|0;if((l|0)==1){o=p;t=s;w=r;R=n;S=m;l=q;p=o;s=t;r=w;n=R;m=S;q=l;l=46;continue}i=c[m>>2]|0}j=0;l=y(3,s|0,c[n>>2]|0,i|0)|0;i=j;j=0;if((i|0)!=0&(k|0)!=0){o=rd(c[i>>2]|0,P|0,M|0)|0;if(!o)I(i|0,k|0);u(k|0)}else o=-1;i=v()|0;if((o|0)==1){o=p;t=s;w=r;R=n;S=m;l=q;p=o;s=t;r=w;n=R;m=S;q=l;l=46;continue}if(l|0){j=0;D(1,q|0,2,5996,K|0);i=j;j=0;if((i|0)!=0&(k|0)!=0){l=rd(c[i>>2]|0,P|0,M|0)|0;if(!l)I(i|0,k|0);u(k|0)}else l=-1;i=v()|0;if((l|0)==1){o=p;t=s;w=r;R=n;S=m;l=q;p=o;s=t;r=w;n=R;m=S;q=l;l=46;continue}}l=p+2848|0;i=p+5064+((c[p+5564>>2]|0)*120|0)|0;t=l+120|0;do{c[l>>2]=c[i>>2];l=l+4|0;i=i+4|0}while((l|0)<(t|0));l=p+2968|0;i=p+5064+((c[p+5560>>2]|0)*120|0)|0;t=l+120|0;do{c[l>>2]=c[i>>2];l=l+4|0;i=i+4|0}while((l|0)<(t|0));j=0;B(1,r|0);i=j;j=0;if((i|0)!=0&(k|0)!=0){l=rd(c[i>>2]|0,P|0,M|0)|0;if(!l)I(i|0,k|0);u(k|0)}else l=-1;i=v()|0;if((l|0)==1){o=p;t=s;w=r;R=n;S=m;l=q;p=o;s=t;r=w;n=R;m=S;q=l;l=46;continue}c[w>>2]=0;c[p+5544>>2]=0;l=59;continue}else if((l|0)==59){l=p+11732|0;i=G;t=l+80|0;do{c[l>>2]=c[i>>2];l=l+4|0;i=i+4|0}while((l|0)<(t|0));c[b+476>>2]=g;j=0;l=z(2,p|0,f|0,e|0,h|0,((h|0)<0)<<31>>31|0)|0;i=j;j=0;if((i|0)!=0&(k|0)!=0){o=rd(c[i>>2]|0,P|0,M|0)|0;if(!o)I(i|0,k|0);u(k|0)}else o=-1;i=v()|0;if((o|0)==1){l=46;continue}if(l|0){i=c[p+3248>>2]|0;if(i|0)c[b>>2]=(c[p+3252>>2]|0)==0?0:p+3256|0;c[O>>2]=i}c[N>>2]=0;l=66;continue}else if((l|0)==66){i=c[O>>2]|0;l=67;break}if((l|0)==67){Fc(P|0);L=Q;return i|0}c[w>>2]=0;c[A>>2]=0;c[E>>2]=0;S=-1;Fc(P|0);L=Q;return S|0}function xa(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;i=L;L=L+176|0;d=i+48|0;e=i+8|0;f=i;g=i+16|0;if(c[b>>2]|0){h=0;L=i;return h|0}h=c[a+348>>2]|0;if(!h){h=0;L=i;return h|0}j=e;c[j>>2]=0;c[j+4>>2]=0;j=f;c[j>>2]=0;c[j+4>>2]=0;c[g>>2]=0;c[g+4>>2]=0;c[g+8>>2]=0;c[g+12>>2]=0;c[g+16>>2]=0;c[g+20>>2]=0;c[g+24>>2]=0;if(c[a+4>>2]&65536|0){c[g>>2]=c[a+220>>2];c[g+4>>2]=c[a+224>>2];c[g+8>>2]=c[a+228>>2]}if(!(Ca(h,d,e,f,g)|0)){j=a+240|0;h=c[a+476>>2]|0;c[j>>2]=258;g=c[d+16>>2]|0;c[a+252>>2]=g;f=c[d+4>>2]|0;c[a+256>>2]=f+79&-16;e=c[d>>2]|0;c[a+272>>2]=e;c[a+264>>2]=e;c[a+276>>2]=f;c[a+268>>2]=f;c[a+280>>2]=1;c[a+284>>2]=1;c[a+288>>2]=c[d+52>>2];c[a+292>>2]=c[d+56>>2];c[a+296>>2]=c[d+60>>2];c[a+300>>2]=0;c[a+304>>2]=g;f=c[d+36>>2]|0;c[a+308>>2]=f;c[a+312>>2]=f;c[a+316>>2]=g;c[a+260>>2]=8;c[a+320>>2]=12;c[a+324>>2]=h;c[a+328>>2]=c[d+68>>2];c[a+332>>2]=0;c[a+336>>2]=0;c[b>>2]=j;a=j}else a=0;j=a;L=i;return j|0}function ya(){return 5456}function za(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0;g=L;L=L+16|0;f=g+8|0;e=a+3248|0;switch(b|0){case 1:{b=a+5564|0;break}case 2:{b=a+5568|0;break}case 4:{b=a+5572|0;break}default:{ja(e,1,6029,g);f=c[e>>2]|0;L=g;return f|0}}h=c[b>>2]|0;b=a+5064+(h*120|0)|0;if((((c[a+5064+(h*120|0)+4>>2]|0)==(c[d+4>>2]|0)?(c[b>>2]|0)==(c[d>>2]|0):0)?(c[a+5064+(h*120|0)+24>>2]|0)==(c[d+24>>2]|0):0)?(c[a+5064+(h*120|0)+20>>2]|0)==(c[d+20>>2]|0):0){Ma(b,d);h=c[e>>2]|0;L=g;return h|0}ja(e,1,6053,f);h=c[e>>2]|0;L=g;return h|0}function Aa(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;j=L;L=L+16|0;e=j+8|0;i=a+3248|0;switch(b|0){case 1:{h=a+5564|0;break}case 2:{h=a+5568|0;break}case 4:{h=a+5572|0;break}default:{ja(i,1,6029,j);i=c[i>>2]|0;L=j;return i|0}}g=c[h>>2]|0;if((((c[a+5064+(g*120|0)+4>>2]|0)==(c[d+4>>2]|0)?(c[a+5064+(g*120|0)>>2]|0)==(c[d>>2]|0):0)?(c[a+5064+(g*120|0)+24>>2]|0)==(c[d+24>>2]|0):0)?(c[a+5064+(g*120|0)+20>>2]|0)==(c[d+20>>2]|0):0){b=a+5544|0;if(c[b>>2]|0){b=a+5548|0;if(c[b>>2]|0){b=a+5552|0;if(!(c[b>>2]|0))g=2;else{b=(c[a+5556>>2]|0)==0?3:4;g=b;b=a+5544+(b<<2)|0}}else g=1}else g=0;c[b>>2]=0;e=a+5544+(c[h>>2]<<2)|0;f=c[e>>2]|0;if((f|0)>0)c[e>>2]=f+-1;c[h>>2]=g;c[b>>2]=(c[b>>2]|0)+1;Ma(d,a+5064+((c[h>>2]|0)*120|0)|0);i=c[i>>2]|0;L=j;return i|0}ja(i,1,6053,e);i=c[i>>2]|0;L=j;return i|0}function Ba(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;q=4;s=Ec(40)|0;c[s>>2]=0;r=a+3248|0;c[r>>2]=0;do if(((c[a+11836>>2]|0)==0?(c[a+11736>>2]|0)>>>0<2:0)?(c[a+11776>>2]|0)==0:0){h=a+5564|0;b=c[h>>2]|0;d=a+5544+(b<<2)|0;g=c[d>>2]|0;if((g|0)>1){c[d>>2]=g+-1;d=a+5544|0;if(c[d>>2]|0){d=a+5548|0;if(c[d>>2]|0){d=a+5552|0;if(!(c[d>>2]|0))g=2;else{d=(c[a+5556>>2]|0)==0?3:4;g=d;d=a+5544+(d<<2)|0}}else g=1}else g=0;c[d>>2]=1;c[h>>2]=g;j=0;C(1,a+5064+(b*120|0)|0,a+5064+(g*120|0)|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,s|0,q|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=b;n=0;m=0;o=0;l=0;break}b=c[h>>2]|0}c[a+5064+(b*120|0)+112>>2]=1;r=0;a=a+5704|0;c[a>>2]=0;Fc(s|0);return r|0}else p=12;while(0);if((p|0)==12){b=a+5544|0;if(c[b>>2]|0){b=a+5548|0;if(c[b>>2]|0){b=a+5552|0;if(!(c[b>>2]|0))d=2;else{b=(c[a+5556>>2]|0)==0?3:4;d=b;b=a+5544+(b<<2)|0}}else d=1}else d=0;c[b>>2]=1;l=a+5560|0;c[l>>2]=d;c[a+3232>>2]=a+5064+(d*120|0);i=a+5564|0;c[a+3236>>2]=a+5064+((c[i>>2]|0)*120|0);h=a+5568|0;c[a+3240>>2]=a+5064+((c[h>>2]|0)*120|0);g=a+5572|0;c[a+3244>>2]=a+5064+((c[g>>2]|0)*120|0);s=qd(a+3340|0,1,s|0,q|0)|0;q=v()|0;j=0;b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,s|0,q|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=b;n=g;m=h;o=i}else{d=0;n=g;m=h;o=i}}while(1){if(d|0){p=19;break}c[a+3336>>2]=1;j=0;b=w(2,a|0)|0;d=j;j=0;if((d|0)!=0&(k|0)!=0){g=rd(c[d>>2]|0,s|0,q|0)|0;if(!g)I(d|0,k|0);u(k|0)}else g=-1;d=v()|0;if((g|0)!=1){p=22;break}}do if((p|0)==19){c[a+5064+((c[o>>2]|0)*120|0)+112>>2]=1;b=a+5544+(c[l>>2]<<2)|0;d=c[b>>2]|0;if((d|0)>0){c[b>>2]=d+-1;b=1}else b=1}else if((p|0)==22){if((b|0)<0){d=a+5544+(c[l>>2]<<2)|0;g=c[d>>2]|0;if((g|0)>0)c[d>>2]=g+-1;c[r>>2]=1;break}d=c[a+9164>>2]|0;switch(d|0){case 0:break;case 1:{g=c[o>>2]|0;d=0;p=29;break}case 2:{g=c[m>>2]|0;d=0;p=29;break}default:{g=0;d=-1;p=29}}if((p|0)==29){h=a+5544+(c[n>>2]<<2)|0;i=c[h>>2]|0;if((i|0)>0)c[h>>2]=i+-1;c[n>>2]=g;q=a+5544+(g<<2)|0;c[q>>2]=(c[q>>2]|0)+1}switch(c[a+9160>>2]|0){case 0:{i=d;break}case 1:{g=o;p=34;break}case 2:{g=n;p=34;break}default:{i=0;d=-1;p=35}}if((p|0)==34){i=c[g>>2]|0;p=35}if((p|0)==35){g=a+5544+(c[m>>2]<<2)|0;h=c[g>>2]|0;if((h|0)>0)c[g>>2]=h+-1;c[m>>2]=i;i=a+5544+(i<<2)|0;c[i>>2]=(c[i>>2]|0)+1;i=d}if(c[a+9152>>2]|0){d=c[l>>2]|0;g=a+5544+(c[m>>2]<<2)|0;h=c[g>>2]|0;if((h|0)>0)c[g>>2]=h+-1;c[m>>2]=d;q=a+5544+(d<<2)|0;c[q>>2]=(c[q>>2]|0)+1}if(c[a+9156>>2]|0){d=c[l>>2]|0;g=a+5544+(c[n>>2]<<2)|0;h=c[g>>2]|0;if((h|0)>0)c[g>>2]=h+-1;c[n>>2]=d;q=a+5544+(d<<2)|0;c[q>>2]=(c[q>>2]|0)+1}d=c[l>>2]|0;if(!(c[a+9148>>2]|0))c[a+5060>>2]=a+5064+(d*120|0);else{g=a+5544+(c[o>>2]<<2)|0;h=c[g>>2]|0;if((h|0)>0)c[g>>2]=h+-1;c[o>>2]=d;d=a+5544+(d<<2)|0;c[d>>2]=(c[d>>2]|0)+1;c[a+5060>>2]=a+5064+((c[o>>2]|0)*120|0);d=c[l>>2]|0}q=a+5544+(d<<2)|0;c[q>>2]=(c[q>>2]|0)+-1;if(i|0){c[r>>2]=1;break}if(c[a+5704>>2]|0){r=a+11428|0;c[r>>2]=(c[r>>2]|0)+1;c[a+5776>>2]=c[a+5772>>2]}c[a+11824>>2]=0;r=a+11816|0;c[r>>2]=e;c[r+4>>2]=f}while(0);r=b;a=a+3336|0;c[a>>2]=0;Fc(s|0);return r|0}function Ca(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;f=a+11824|0;if((c[f>>2]|0)==1){b=-1;return b|0}if(!(c[a+5704>>2]|0)){b=-1;return b|0}c[f>>2]=1;h=a+11816|0;g=c[h+4>>2]|0;f=d;c[f>>2]=c[h>>2];c[f+4>>2]=g;f=e;c[f>>2]=0;c[f+4>>2]=0;f=c[a+5060>>2]|0;if(!f){h=-1;return h|0}e=b;d=e+120|0;do{c[e>>2]=c[f>>2];e=e+4|0;f=f+4|0}while((e|0)<(d|0));c[b>>2]=c[a+5040>>2];h=c[a+5044>>2]|0;c[b+4>>2]=h;c[b+24>>2]=(h|0)/2|0;h=0;return h|0}function Da(a,b){a=a|0;b=b|0;var e=0,f=0,g=0,h=0;g=c[a+2468>>2]|0;if((g|0)<=0){h=0;return h|0}h=c[a+2472>>2]|0;if((h|0)<=0){h=0;return h|0}f=0;a=c[a+2524>>2]|0;a:while(1){e=0;while(1){if((d[a+2>>0]|0|0)==(b|0)){a=1;e=8;break a}e=e+1|0;if((e|0)>=(h|0))break;else a=a+76|0}f=f+1|0;if((f|0)>=(g|0)){a=0;e=8;break}else a=a+152|0}if((e|0)==8)return a|0;return 0}function Ea(a,b){a=a|0;b=b|0;b=Fa()|0;c[a>>2]=b;return (b|0)==0|0}function Fa(){var a=0,b=0,d=0,e=0,f=0,g=0,h=0,i=0,l=0;h=4;i=Ec(40)|0;c[i>>2]=0;j=0;g=x(13,32,11872)|0;a=j;j=0;if((a|0)!=0&(k|0)!=0){b=rd(c[a>>2]|0,i|0,h|0)|0;if(!b)I(a|0,k|0);u(k|0)}else b=-1;d=v()|0;if((b|0)!=1){if(!g){l=0;Fc(i|0);return l|0}ud(g|0,0,11872)|0;d=g+3248|0;i=qd(g+3340|0,1,i|0,h|0)|0;h=v()|0;j=0;a=j;j=0;if((a|0)!=0&(k|0)!=0){e=rd(c[a>>2]|0,i|0,h|0)|0;if(!e)I(a|0,k|0);u(k|0)}else e=-1;b=v()|0;if((e|0)==1){f=d;a=g}else{f=d;a=g;b=0}}else{f=0;a=0;b=d}while(1){e=g+3336|0;if(b|0){c[e>>2]=0;j=0;B(2,f|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}j=0;B(3,g|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}else{l=8;break}}c[e>>2]=1;j=0;B(4,f|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}c[g+11428>>2]=0;c[g+11824>>2]=1;j=0;B(5,a|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}j=0;B(6,f|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}c[e>>2]=0;b=g+11832|0;c[b>>2]=0;c[b+4>>2]=0;c[b+8>>2]=0;c[b+12>>2]=0;j=0;B(7,g|0);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}if(c[1672]|0){l=19;break}if(c[1673]|0)break;j=0;A(1);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)==1){d=a;e=f;a=d;f=e;continue}j=0;A(2);b=j;j=0;if((b|0)!=0&(k|0)!=0){d=rd(c[b>>2]|0,i|0,h|0)|0;if(!d)I(b|0,k|0);u(k|0)}else d=-1;b=v()|0;if((d|0)!=1){l=17;break}else{d=a;e=f;a=d;f=e}}if((l|0)==8){l=0;Fc(i|0);return l|0}else if((l|0)==17)c[1673]=1;else if((l|0)==19){Fc(i|0);return a|0}c[1672]=1;l=a;Fc(i|0);return l|0}function Ga(a){a=a|0;a=c[a>>2]|0;if(!a){a=1;return a|0}Sa(a+3248|0);Ka(a);a=0;return a|0}function Ha(a){a=a|0;return c[a+5744>>2]|0}function Ia(a,b){a=a|0;b=b|0;var d=0;d=jd(a|0,0,3,0)|0;b=jd(d|0,v()|0,b|0,0)|0;d=v()|0;if(d>>>0>0|(d|0)==0&b>>>0>2147418112|((b|0)!=(b|0)|(d|0)!=0)){a=0;return a|0}d=Ec(b)|0;b=a+-1+(d+4)&0-a;if(!d){a=0;return a|0}c[b+-4>>2]=d;a=b;return a|0}function Ja(a,b){a=a|0;b=b|0;var d=0,e=0;if(a|0?(e=(a|0)==0,(2147418112/(a>>>0)|0)>>>0<b>>>0?1:e?0:(((b*a|0)>>>0)/((e?1:a)>>>0)|0|0)!=(b|0)):0){e=0;return e|0}e=b*a|0;a=jd(e|0,0,11,0)|0;d=v()|0;if(d>>>0>0|(d|0)==0&a>>>0>2147418112|((a|0)!=(e+11|0)|(d|0)!=0)){e=0;return e|0}a=Ec(a)|0;b=a+4+7&-8;d=b;if(!a){e=0;return e|0}c[d+-4>>2]=a;if(!b){e=0;return e|0}ud(d|0,0,e|0)|0;e=d;return e|0}function Ka(a){a=a|0;if(!a)return;Fc(c[a+-4>>2]|0);return}function La(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;u=c[b+76>>2]|0;w=(u|0)/2|0;l=c[b+52>>2]|0;p=c[b+16>>2]|0;d=c[b+8>>2]|0;j=c[b+12>>2]|0;n=u-j+(c[b+4>>2]|0)|0;o=(c[b>>2]|0)+u|0;i=o-d|0;o=o+u|0;d=l+d|0;k=0-u|0;m=l+k|0;if((j|0)>0){e=m;f=d+-1|0;g=l;h=0;while(1){ud(e|0,a[g>>0]|0,u|0)|0;ud(d|0,a[f>>0]|0,i|0)|0;h=h+1|0;if((h|0)==(j|0))break;else{e=e+p|0;f=f+p|0;g=g+p|0;d=d+p|0}}}g=l+((j+-1|0)*p|0)+k|0;d=l+(j*p|0)+k|0;if((u|0)>0){e=l+(p*k|0)+k|0;f=0;while(1){td(e|0,m|0,o|0)|0;f=f+1|0;if((f|0)==(u|0))break;else e=e+p|0}}if((n|0)>0){e=0;while(1){td(d|0,g|0,o|0)|0;e=e+1|0;if((e|0)==(n|0))break;else d=d+p|0}}k=c[b+56>>2]|0;p=b+36|0;m=c[p>>2]|0;q=b+28|0;d=c[q>>2]|0;r=b+32|0;j=c[r>>2]|0;s=b+24|0;n=w-j+(c[s>>2]|0)|0;t=b+20|0;o=(c[t>>2]|0)+w|0;i=o-d|0;o=o+w|0;d=k+d|0;v=0-w|0;l=k+v|0;if((j|0)>0){e=l;f=d+-1|0;g=k;h=0;while(1){ud(e|0,a[g>>0]|0,w|0)|0;ud(d|0,a[f>>0]|0,i|0)|0;h=h+1|0;if((h|0)==(j|0))break;else{e=e+m|0;f=f+m|0;g=g+m|0;d=d+m|0}}}g=k+((j+-1|0)*m|0)+v|0;d=k+(j*m|0)+v|0;u=(u|0)>1;if(u){e=k+(m*v|0)+v|0;f=0;while(1){td(e|0,l|0,o|0)|0;f=f+1|0;if((f|0)==(w|0))break;else e=e+m|0}}if((n|0)>0){e=0;while(1){td(d|0,g|0,o|0)|0;e=e+1|0;if((e|0)==(n|0))break;else d=d+m|0}}n=c[b+60>>2]|0;o=c[p>>2]|0;d=c[q>>2]|0;j=c[r>>2]|0;m=w-j+(c[s>>2]|0)|0;l=(c[t>>2]|0)+w|0;i=l-d|0;l=l+w|0;d=n+d|0;k=n+v|0;if((j|0)>0){e=k;f=d+-1|0;g=n;h=0;while(1){ud(e|0,a[g>>0]|0,w|0)|0;ud(d|0,a[f>>0]|0,i|0)|0;h=h+1|0;if((h|0)==(j|0))break;else{e=e+o|0;f=f+o|0;g=g+o|0;d=d+o|0}}}g=n+((j+-1|0)*o|0)+v|0;d=n+(j*o|0)+v|0;if(u){e=n+(o*v|0)+v|0;f=0;while(1){td(e|0,k|0,l|0)|0;f=f+1|0;if((f|0)==(w|0))break;else e=e+o|0}}if((m|0)<=0)return;e=0;while(1){td(d|0,g|0,l|0)|0;e=e+1|0;if((e|0)==(m|0))break;else d=d+o|0}return}function Ma(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;h=a+4|0;if((c[h>>2]|0)>0){i=a+16|0;g=b+16|0;d=c[a+52>>2]|0;e=0;f=c[b+52>>2]|0;while(1){td(f|0,d|0,c[a>>2]|0)|0;e=e+1|0;if((e|0)>=(c[h>>2]|0))break;else{d=d+(c[i>>2]|0)|0;f=f+(c[g>>2]|0)|0}}}k=a+24|0;if((c[k>>2]|0)<=0){La(b);return}g=a+20|0;h=a+36|0;i=b+36|0;d=c[a+56>>2]|0;e=0;f=c[b+56>>2]|0;while(1){td(f|0,d|0,c[g>>2]|0)|0;e=e+1|0;j=c[k>>2]|0;if((e|0)>=(j|0))break;else{d=d+(c[h>>2]|0)|0;f=f+(c[i>>2]|0)|0}}if((j|0)<=0){La(b);return}g=a+20|0;h=a+36|0;i=b+36|0;d=c[a+60>>2]|0;e=0;f=c[b+60>>2]|0;while(1){td(f|0,d|0,c[g>>2]|0)|0;e=e+1|0;if((e|0)>=(c[k>>2]|0))break;else{d=d+(c[h>>2]|0)|0;f=f+(c[i>>2]|0)|0}}La(b);return}function Na(){if(c[1674]|0)return;c[1674]=1;return}function Oa(){if(c[1675]|0)return;c[1675]=1;return}function Pa(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=a+1816|0;Ab(h)|0;l=a+1936|0;Ab(l)|0;m=a+2056|0;Ab(m)|0;p=a+2176|0;Ab(p)|0;q=a+2328|0;Ab(q)|0;r=a+5940|0;Ka(c[r>>2]|0);f=a+2520|0;Ka(c[f>>2]|0);c[r>>2]=0;c[f>>2]=0;e=b&15;e=((e|0)==0?0:16-e|0)+b|0;b=d&15;b=((b|0)==0?0:16-b|0)+d|0;d=a+2296|0;c[d>>2]=0;c[a+1932>>2]=0;if(((((((Cb(h,e,b,32)|0)>=0?(g=a+2300|0,c[g>>2]=0,c[a+2052>>2]=0,(Cb(l,e,b,32)|0)>=0):0)?(n=a+2304|0,c[n>>2]=0,c[a+2172>>2]=0,(Cb(m,e,b,32)|0)>=0):0)?(o=a+2308|0,c[o>>2]=0,c[a+2292>>2]=0,(Cb(p,e,b,32)|0)>=0):0)?(c[a+2312>>2]=0,c[a+2316>>2]=1,c[a+2320>>2]=2,c[a+2324>>2]=3,c[d>>2]=1,c[g>>2]=1,c[n>>2]=1,c[o>>2]=1,(Cb(q,e,16,32)|0)>=0):0)?(o=b>>4,c[a+2468>>2]=o,k=e>>4,i=a+2472|0,c[i>>2]=k,c[a+2464>>2]=o*k,k=k+1|0,j=a+2476|0,c[j>>2]=k,k=Ja((o+1|0)*k|0,76)|0,c[f>>2]=k,k|0):0)?(c[a+2524>>2]=k+((c[j>>2]|0)*76|0)+76,o=Ja((c[i>>2]|0)*9|0,1)|0,c[r>>2]=o,o|0):0){r=0;return r|0}Ab(h)|0;Ab(l)|0;Ab(m)|0;Ab(p)|0;Ab(q)|0;Ka(c[r>>2]|0);Ka(c[f>>2]|0);c[r>>2]=0;c[f>>2]=0;r=1;return r|0}function Qa(a){a=a|0;var b=0,d=0,e=0,f=0;switch(c[a+8184>>2]|0){case 3:{b=1;d=1;e=1;f=1;break}case 1:{b=0;d=1;e=1;f=0;break}case 2:{b=0;d=1;e=0;f=1;break}default:{b=0;d=0;e=0;f=0}}c[a+2484>>2]=f;c[a+2532>>2]=e;c[a+2488>>2]=d;c[a+2492>>2]=b;return}function Ra(a){a=a|0;var b=0;Ta(a);Ua(a+7066|0);c[a+2480>>2]=1;c[a+2484>>2]=0;c[a+2532>>2]=0;c[a+2488>>2]=0;c[a+2492>>2]=0;c[a+8188>>2]=0;c[a+1808>>2]=0;b=a+5924|0;c[b>>2]=0;c[b+4>>2]=0;c[b+8>>2]=0;c[b+12>>2]=0;c[a+5912>>2]=0;c[a+5916>>2]=0;return}function Sa(a){a=a|0;var b=0;Ab(a+1816|0)|0;Ab(a+1936|0)|0;Ab(a+2056|0)|0;Ab(a+2176|0)|0;Ab(a+2328|0)|0;b=a+5940|0;Ka(c[b>>2]|0);a=a+2520|0;Ka(c[a>>2]|0);c[b>>2]=0;c[a>>2]=0;return}function Ta(b){b=b|0;var c=0;c=b+7075|0;a[c>>0]=112;a[c+1>>0]=86;a[c+2>>0]=140;a[c+3>>0]=37;c=b+7079|0;a[c>>0]=a[6085]|0;a[c+1>>0]=a[6086]|0;a[c+2>>0]=a[6087]|0;b=b+7082|0;a[b>>0]=a[6136]|0;a[b+1>>0]=a[6137]|0;a[b+2>>0]=a[6138]|0;return}function Ua(b){b=b|0;var c=0,d=0;c=6091;d=b+9|0;do{a[b>>0]=a[c>>0]|0;b=b+1|0;c=c+1|0}while((b|0)<(d|0));return}function Va(a){a=a|0;return}function Wa(){if(c[1676]|0)return;c[1676]=1;return}function Xa(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;$a();f=b+5896|0;e=c[f>>2]|0;d=(e|0)>0;g=d&1;h=(e|0)>4&1;e=9-e|0;if(d){d=0;do{i=d>>>g>>>h;i=(i|0)>(e|0)?e:i;i=(i|0)>1?i:1;ud(b+4592+(d<<4)|0,i&255|0,16)|0;i=i+(d<<1)|0;ud(b+3568+(d<<4)|0,i&255|0,16)|0;ud(b+2544+(d<<4)|0,i+4&255|0,16)|0;d=d+1|0}while((d|0)!=64)}else{d=0;do{i=d>>>g>>>h;i=(i|0)>1?i:1;ud(b+4592+(d<<4)|0,i&255|0,16)|0;i=i+(d<<1)|0;ud(b+3568+(d<<4)|0,i&255|0,16)|0;ud(b+2544+(d<<4)|0,i+4&255|0,16)|0;d=d+1|0}while((d|0)!=64)}c[b+5892>>2]=c[f>>2];e=0;do{do if(e>>>0<=39){if(e>>>0>19){a[b+5744+e>>0]=1;d=2;break}d=b+5744+e|0;if(e>>>0>14){a[d>>0]=1;d=1;break}else{a[d>>0]=0;d=0;break}}else{a[b+5744+e>>0]=2;d=3}while(0);a[b+5808+e>>0]=d;e=e+1|0}while((e|0)!=64);c[b+5872>>2]=16843009;a[b+5876>>0]=0;a[b+5879>>0]=1;a[b+5877>>0]=2;a[b+5878>>0]=2;a[b+5880>>0]=2;a[b+5881>>0]=3;i=b+5616|0;c[i>>2]=0;c[i+4>>2]=0;c[i+8>>2]=0;c[i+12>>2]=0;i=b+5632|0;c[i>>2]=16843009;c[i+4>>2]=16843009;c[i+8>>2]=16843009;c[i+12>>2]=16843009;i=b+5648|0;c[i>>2]=33686018;c[i+4>>2]=33686018;c[i+8>>2]=33686018;c[i+12>>2]=33686018;i=b+5664|0;c[i>>2]=50529027;c[i+4>>2]=50529027;c[i+8>>2]=50529027;c[i+12>>2]=50529027;return}function Ya(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;i=b+5892|0;j=b+5896|0;f=c[j>>2]|0;if((c[i>>2]|0)!=(f|0)){r=(f|0)>0;k=r&1;h=(f|0)>4&1;g=9-f|0;if(r){f=0;do{r=f>>>k>>>h;r=(r|0)>(g|0)?g:r;r=(r|0)>1?r:1;ud(b+4592+(f<<4)|0,r&255|0,16)|0;r=r+(f<<1)|0;ud(b+3568+(f<<4)|0,r&255|0,16)|0;ud(b+2544+(f<<4)|0,r+4&255|0,16)|0;f=f+1|0}while((f|0)!=64)}else{f=0;do{r=f>>>k>>>h;r=(r|0)>1?r:1;ud(b+4592+(f<<4)|0,r&255|0,16)|0;r=r+(f<<1)|0;ud(b+3568+(f<<4)|0,r&255|0,16)|0;ud(b+2544+(f<<4)|0,r+4&255|0,16)|0;f=f+1|0}while((f|0)!=64)}c[i>>2]=c[j>>2]}h=d+3148|0;i=d+3163|0;j=d+3151|0;k=d+3169|0;l=d+3177|0;m=d+3178|0;n=d+3170|0;o=d+3179|0;p=d+3180|0;q=d+3171|0;r=d+3172|0;f=0;do{if(!(a[h>>0]|0))g=e;else{g=((a[j>>0]|0)==1?0:e)+(a[d+3159+f>>0]|0)|0;g=(g|0)<63?g:63;g=(g|0)>0?g:0}if(!(a[i>>0]|0))ud(b+5680+(f<<4)|0,g&255|0,16)|0;else{s=g+(a[k>>0]|0)|0;t=s+(a[l>>0]|0)|0;t=(t|0)<63?t:63;a[b+5680+(f<<4)>>0]=(t|0)>0?t:0;s=(s|0)<63?s:63;a[b+5680+(f<<4)+1>>0]=(s|0)>0?s:0;s=g+(a[n>>0]|0)|0;t=s+(a[m>>0]|0)|0;t=(t|0)<63?t:63;a[b+5680+(f<<4)+5>>0]=(t|0)>0?t:0;t=s+(a[o>>0]|0)|0;t=(t|0)<63?t:63;a[b+5680+(f<<4)+6>>0]=(t|0)>0?t:0;s=s+(a[p>>0]|0)|0;s=(s|0)<63?s:63;a[b+5680+(f<<4)+7>>0]=(s|0)>0?s:0;s=g+(a[q>>0]|0)|0;t=s+(a[m>>0]|0)|0;t=(t|0)<63?t:63;a[b+5680+(f<<4)+9>>0]=(t|0)>0?t:0;t=s+(a[o>>0]|0)|0;t=(t|0)<63?t:63;a[b+5680+(f<<4)+10>>0]=(t|0)>0?t:0;s=s+(a[p>>0]|0)|0;s=(s|0)<63?s:63;a[b+5680+(f<<4)+11>>0]=(s|0)>0?s:0;g=g+(a[r>>0]|0)|0;s=g+(a[m>>0]|0)|0;s=(s|0)<63?s:63;a[b+5680+(f<<4)+13>>0]=(s|0)>0?s:0;s=g+(a[o>>0]|0)|0;s=(s|0)<63?s:63;a[b+5680+(f<<4)+14>>0]=(s|0)>0?s:0;g=g+(a[p>>0]|0)|0;g=(g|0)<63?g:63;a[b+5680+(f<<4)+15>>0]=(g|0)>0?g:0}f=f+1|0}while((f|0)!=4);return}function Za(b,e,f,g,h,i,j,k){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;t=L;L=L+16|0;s=t;n=c[b+2452>>2]|0;o=b+2472|0;if((c[o>>2]|0)<=0){L=t;return}p=s+4|0;q=s+8|0;r=s+12|0;if((f|0)<=0){m=0;while(1){f=a[e>>0]|0;switch(f<<24>>24){case 9:case 4:{l=0;break}default:l=(a[e+9>>0]|0)!=0}u=a[(d[(f&255)+(b+5872)>>0]|0)+(b+5680+(d[e+11>>0]<<4)+(d[e+2>>0]<<2))>>0]|0;f=u&255;if(u<<24>>24){u=d[b+5744+(n<<6)+f>>0]|0;c[s>>2]=b+2544+(f<<4);c[p>>2]=b+3568+(f<<4);c[q>>2]=b+4592+(f<<4);c[r>>2]=b+5616+(u<<4);if(m|0)eb(i,j,k,g,h,s);if(!l){jb(i,j,k,g,h,s);gb(i,j,k,g,h,s)}}m=m+1|0;if((m|0)>=(c[o>>2]|0))break;else{i=i+16|0;j=j+8|0;k=k+8|0;e=e+76|0}}L=t;return}m=0;while(1){f=a[e>>0]|0;switch(f<<24>>24){case 9:case 4:{l=0;break}default:l=(a[e+9>>0]|0)!=0}u=a[(d[(f&255)+(b+5872)>>0]|0)+(b+5680+(d[e+11>>0]<<4)+(d[e+2>>0]<<2))>>0]|0;f=u&255;do if(u<<24>>24){u=d[b+5744+(n<<6)+f>>0]|0;c[s>>2]=b+2544+(f<<4);c[p>>2]=b+3568+(f<<4);c[q>>2]=b+4592+(f<<4);c[r>>2]=b+5616+(u<<4);if(m|0)eb(i,j,k,g,h,s);if(l){cb(i,j,k,g,h,s);break}else{jb(i,j,k,g,h,s);cb(i,j,k,g,h,s);gb(i,j,k,g,h,s);break}}while(0);m=m+1|0;if((m|0)>=(c[o>>2]|0))break;else{i=i+16|0;j=j+8|0;k=k+8|0;e=e+76|0}}L=t;return}function _a(b,e,f,g,h,i,j,k){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0;l=b+2472|0;if((c[l>>2]|0)<=0)return;if((f|0)<=0){f=0;j=e;while(1){h=a[j>>0]|0;switch(h<<24>>24){case 9:case 4:{k=0;break}default:k=(a[j+9>>0]|0)!=0}e=a[(d[(h&255)+(b+5872)>>0]|0)+(b+5680+(d[j+11>>0]<<4)+(d[j+2>>0]<<2))>>0]|0;h=e&255;if(e<<24>>24){if(f|0)bb(i,g,b+2544+(h<<4)|0);if(!k){e=b+3568+(h<<4)|0;lb(i,g,e);ib(i,g,e)}}f=f+1|0;if((f|0)>=(c[l>>2]|0))break;else{i=i+16|0;j=j+76|0}}return}f=0;j=e;while(1){h=a[j>>0]|0;switch(h<<24>>24){case 9:case 4:{k=0;break}default:k=(a[j+9>>0]|0)!=0}e=a[(d[(h&255)+(b+5872)>>0]|0)+(b+5680+(d[j+11>>0]<<4)+(d[j+2>>0]<<2))>>0]|0;h=e&255;do if(e<<24>>24){if(f|0)bb(i,g,b+2544+(h<<4)|0);if(k){ab(i,g,b+2544+(h<<4)|0);break}else{e=b+3568+(h<<4)|0;lb(i,g,e);ab(i,g,b+2544+(h<<4)|0);ib(i,g,e);break}}while(0);f=f+1|0;if((f|0)>=(c[l>>2]|0))break;else{i=i+16|0;j=j+76|0}}return}function $a(){var b=0,d=0,e=0;if(!(c[1677]|0)){d=Ec(32768)|0;c[1677]=d;c[1678]=d+16384;b=0;do{e=b+-16384|0;a[d+b>>0]=b>>>0<16256?-128:((e|0)<127?e:127)&255;b=b+1|0}while((b|0)!=32768)}if(!(c[1679]|0)){d=Ec(32768)|0;c[1679]=d;c[1680]=d+16384;b=0;do{e=b+-16384|0;a[d+b>>0]=b>>>0<16384?0:((e|0)<255?e:255)&255;b=b+1|0}while((b|0)!=32768)}if(!(c[1681]|0)){d=Ec(512)|0;c[1681]=d;c[1682]=d+256;b=0;do{a[d+b>>0]=b>>>0<256?0-b|0:b;b=b+1|0}while((b|0)!=512)}if(c[1683]|0)return;d=Ec(2048)|0;c[1683]=d;c[1684]=d+1024;b=0;do{a[d+b>>0]=(b>>>0<1024)<<31>>31;b=b+1|0}while((b|0)!=2048);return}function ab(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=e*-2|0;i=0-e|0;j=c[1682]|0;k=0-(e<<1)|0;l=c[1678]|0;g=0;while(1){m=b+i|0;r=a[m>>0]|0;q=a[b>>0]|0;o=a[b+e>>0]|0;p=(q^-128)<<24>>24;n=(r^-128)<<24>>24;o=((d[j+((r&255)-(q&255))>>0]<<1)+((d[j+((d[b+h>>0]|0)-(o&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[l+(((p-n|0)*3|0)+(a[l+(((a[b+k>>0]^-128)<<24>>24)-((o^-128)<<24>>24))>>0]|0))>>0]|0;a[b>>0]=a[l+(p-(a[l+(o+4)>>0]>>3))>>0]^-128;a[m>>0]=a[l+((a[l+(o+3)>>0]>>3)+n)>>0]^-128;g=g+1|0;if((g|0)==16)break;else b=b+1|0}return}function bb(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;h=c[1682]|0;i=c[1678]|0;g=0;while(1){n=a[b+-2>>0]|0;j=b+-1|0;p=a[j>>0]|0;o=a[b>>0]|0;l=a[b+1>>0]|0;m=(o^-128)<<24>>24;k=(p^-128)<<24>>24;l=((d[h+((p&255)-(o&255))>>0]<<1)+((d[h+((n&255)-(l&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[i+(((m-k|0)*3|0)+(a[i+(((n^-128)<<24>>24)-((l^-128)<<24>>24))>>0]|0))>>0]|0;a[b>>0]=a[i+(m-(a[i+(l+4)>>0]>>3))>>0]^-128;a[j>>0]=a[i+((a[i+(l+3)>>0]>>3)+k)>>0]^-128;g=g+1|0;if((g|0)==16)break;else b=b+e|0}return}function cb(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0;h=g+8|0;i=g+12|0;db(a,e,c[g>>2]|0,c[h>>2]|0,c[i>>2]|0,2);if(b|0)db(b,f,c[g>>2]|0,c[h>>2]|0,c[i>>2]|0,1);if(!d)return;db(d,f,c[g>>2]|0,c[h>>2]|0,c[i>>2]|0,1);return}function db(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0;t=e*-4|0;u=e*-3|0;v=e*-2|0;w=0-e|0;l=e<<1;m=e*3|0;n=c[1682]|0;o=c[1684]|0;p=0-m|0;q=0-l|0;r=c[1678]|0;s=c[1680]|0;k=i<<3;j=0;i=b;while(1){I=d[g>>0]|0;M=d[i+u>>0]|0;H=d[i+v>>0]|0;D=i+w|0;G=d[D>>0]|0;J=d[i>>0]|0;E=i+e|0;F=d[E>>0]|0;z=i+l|0;A=d[z>>0]|0;L=d[n+(H-G)>>0]|0;K=d[n+(F-J)>>0]|0;y=d[h>>0]|0;y=a[o+(y-K)>>0]|a[o+(y-L)>>0];b=i+p|0;B=i+q|0;x=d[b>>0]|0;C=d[B>>0]|0;H=a[r+(((J-G|0)*3|0)+(a[r+(C-F)>>0]|0))>>0]&~(a[o+(I-(d[n+(M-H)>>0]|0))>>0]|a[o+(I-(d[n+((d[i+t>>0]|0)-M)>>0]|0))>>0]|a[o+(I-L)>>0]|a[o+(I-K)>>0]|a[o+(I-(d[n+(A-F)>>0]|0))>>0]|a[o+(I-(d[n+((d[i+m>>0]|0)-A)>>0]|0))>>0]|a[o+(((d[n+(G-J)>>0]|0)*-2|0)+(d[f>>0]|0)-((d[n+(H-F)>>0]|0)>>>1&255))>>0]);I=(H&y)<<24>>24;G=d[s+((a[r+(I+3)>>0]>>3)+G)>>0]|0;y=(H&~y)<<24>>24;H=a[r+((y*27|0)+63>>7)>>0]|0;a[i>>0]=a[s+((d[s+(J-(a[r+(I+4)>>0]>>3))>>0]|0)-H)>>0]|0;a[D>>0]=a[s+(H+G)>>0]|0;D=a[r+((y*18|0)+63>>7)>>0]|0;a[E>>0]=a[s+(F-D)>>0]|0;a[B>>0]=a[s+(D+C)>>0]|0;y=a[r+((y*9|0)+63>>7)>>0]|0;a[z>>0]=a[s+(A-y)>>0]|0;a[b>>0]=a[s+(y+x)>>0]|0;j=j+1|0;if((j|0)>=(k|0))break;else i=i+1|0}return}function eb(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0;h=g+8|0;i=g+12|0;fb(a,e,c[g>>2]|0,c[h>>2]|0,c[i>>2]|0,2);if(b|0)fb(b,f,c[g>>2]|0,c[h>>2]|0,c[i>>2]|0,1);if(!d)return;fb(d,f,c[g>>2]|0,c[h>>2]|0,c[i>>2]|0,1);return}function fb(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0;l=c[1682]|0;m=c[1684]|0;n=c[1678]|0;o=c[1680]|0;k=i<<3;j=0;i=b;while(1){A=d[g>>0]|0;b=i+-3|0;p=d[b>>0]|0;t=i+-2|0;u=d[t>>0]|0;v=i+-1|0;y=d[v>>0]|0;B=d[i>>0]|0;w=i+1|0;x=d[w>>0]|0;r=i+2|0;s=d[r>>0]|0;D=d[l+(u-y)>>0]|0;C=d[l+(x-B)>>0]|0;z=u-x|0;q=d[h>>0]|0;q=a[m+(q-C)>>0]|a[m+(q-D)>>0];z=a[n+(((B-y|0)*3|0)+(a[n+z>>0]|0))>>0]&~(a[m+(A-(d[l+(p-u)>>0]|0))>>0]|a[m+(A-(d[l+((d[i+-4>>0]|0)-p)>>0]|0))>>0]|a[m+(A-D)>>0]|a[m+(A-C)>>0]|a[m+(A-(d[l+(s-x)>>0]|0))>>0]|a[m+(A-(d[l+((d[i+3>>0]|0)-s)>>0]|0))>>0]|a[m+(((d[l+(y-B)>>0]|0)*-2|0)+(d[f>>0]|0)-((d[l+z>>0]|0)>>>1&255))>>0]);A=(z&q)<<24>>24;y=d[o+((a[n+(A+3)>>0]>>3)+y)>>0]|0;q=(z&~q)<<24>>24;z=a[n+((q*27|0)+63>>7)>>0]|0;a[i>>0]=a[o+((d[o+(B-(a[n+(A+4)>>0]>>3))>>0]|0)-z)>>0]|0;a[v>>0]=a[o+(z+y)>>0]|0;v=a[n+((q*18|0)+63>>7)>>0]|0;a[w>>0]=a[o+(x-v)>>0]|0;a[t>>0]=a[o+(v+u)>>0]|0;q=a[n+((q*9|0)+63>>7)>>0]|0;a[r>>0]=a[o+(s-q)>>0]|0;a[b>>0]=a[o+(q+p)>>0]|0;j=j+1|0;if((j|0)>=(k|0))break;else i=i+e|0}return}function gb(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0;i=g+4|0;h=g+8|0;g=g+12|0;hb(a+(e<<2)|0,e,c[i>>2]|0,c[h>>2]|0,c[g>>2]|0,2);hb(a+(e<<3)|0,e,c[i>>2]|0,c[h>>2]|0,c[g>>2]|0,2);hb(a+(e*12|0)|0,e,c[i>>2]|0,c[h>>2]|0,c[g>>2]|0,2);if(b|0)hb(b+(f<<2)|0,f,c[i>>2]|0,c[h>>2]|0,c[g>>2]|0,1);if(!d)return;hb(d+(f<<2)|0,f,c[i>>2]|0,c[h>>2]|0,c[g>>2]|0,1);return}function hb(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;s=e*-4|0;t=e*-3|0;u=e*-2|0;v=0-e|0;l=e<<1;m=e*3|0;n=c[1682]|0;o=c[1684]|0;p=0-l|0;q=c[1678]|0;r=c[1680]|0;k=i<<3;j=0;i=b;while(1){F=d[g>>0]|0;I=d[i+t>>0]|0;D=d[i+u>>0]|0;B=i+v|0;C=d[B>>0]|0;E=d[i>>0]|0;y=i+e|0;z=d[y>>0]|0;A=d[i+l>>0]|0;H=d[n+(D-C)>>0]|0;G=d[n+(z-E)>>0]|0;x=d[h>>0]|0;x=a[o+(x-G)>>0]|a[o+(x-H)>>0];b=i+p|0;w=d[b>>0]|0;D=(a[q+(((E-C|0)*3|0)+((a[q+(w-z)>>0]&x)<<24>>24))>>0]&~(a[o+(F-(d[n+(I-D)>>0]|0))>>0]|a[o+(F-(d[n+((d[i+s>>0]|0)-I)>>0]|0))>>0]|a[o+(F-H)>>0]|a[o+(F-G)>>0]|a[o+(F-(d[n+(A-z)>>0]|0))>>0]|a[o+(F-(d[n+((d[i+m>>0]|0)-A)>>0]|0))>>0]|a[o+(((d[n+(C-E)>>0]|0)*-2|0)+(d[f>>0]|0)-((d[n+(D-z)>>0]|0)>>>1&255))>>0]))<<24>>24;A=a[q+(D+4)>>0]>>3;D=a[q+(D+3)>>0]>>3;a[i>>0]=a[r+(E-A)>>0]|0;a[B>>0]=a[r+(D+C)>>0]|0;x=A+1>>1&~x<<24>>24;a[y>>0]=a[r+(z-x)>>0]|0;a[b>>0]=a[r+(x+w)>>0]|0;j=j+1|0;if((j|0)>=(k|0))break;else i=i+1|0}return}function ib(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;j=e*-2|0;k=0-e|0;l=c[1682]|0;m=0-(e<<1)|0;i=c[1678]|0;g=0;h=b+(e<<2)|0;while(1){n=h+k|0;s=a[n>>0]|0;r=a[h>>0]|0;p=a[h+e>>0]|0;q=(r^-128)<<24>>24;o=(s^-128)<<24>>24;p=((d[l+((s&255)-(r&255))>>0]<<1)+((d[l+((d[h+j>>0]|0)-(p&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[i+(((q-o|0)*3|0)+(a[i+(((a[h+m>>0]^-128)<<24>>24)-((p^-128)<<24>>24))>>0]|0))>>0]|0;a[h>>0]=a[i+(q-(a[i+(p+4)>>0]>>3))>>0]^-128;a[n>>0]=a[i+((a[i+(p+3)>>0]>>3)+o)>>0]^-128;g=g+1|0;if((g|0)==16)break;else h=h+1|0}g=0;h=b+(e<<3)|0;while(1){s=h+k|0;n=a[s>>0]|0;o=a[h>>0]|0;q=a[h+e>>0]|0;p=(o^-128)<<24>>24;r=(n^-128)<<24>>24;q=((d[l+((n&255)-(o&255))>>0]<<1)+((d[l+((d[h+j>>0]|0)-(q&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[i+(((p-r|0)*3|0)+(a[i+(((a[h+m>>0]^-128)<<24>>24)-((q^-128)<<24>>24))>>0]|0))>>0]|0;a[h>>0]=a[i+(p-(a[i+(q+4)>>0]>>3))>>0]^-128;a[s>>0]=a[i+((a[i+(q+3)>>0]>>3)+r)>>0]^-128;g=g+1|0;if((g|0)==16)break;else h=h+1|0}h=0;g=b+(e*12|0)|0;while(1){s=g+k|0;n=a[s>>0]|0;o=a[g>>0]|0;q=a[g+e>>0]|0;p=(o^-128)<<24>>24;r=(n^-128)<<24>>24;q=((d[l+((n&255)-(o&255))>>0]<<1)+((d[l+((d[g+j>>0]|0)-(q&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[i+(((p-r|0)*3|0)+(a[i+(((a[g+m>>0]^-128)<<24>>24)-((q^-128)<<24>>24))>>0]|0))>>0]|0;a[g>>0]=a[i+(p-(a[i+(q+4)>>0]>>3))>>0]^-128;a[s>>0]=a[i+((a[i+(q+3)>>0]>>3)+r)>>0]^-128;h=h+1|0;if((h|0)==16)break;else g=g+1|0}return}function jb(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0;h=g+4|0;i=g+8|0;g=g+12|0;kb(a+4|0,e,c[h>>2]|0,c[i>>2]|0,c[g>>2]|0,2);kb(a+8|0,e,c[h>>2]|0,c[i>>2]|0,c[g>>2]|0,2);kb(a+12|0,e,c[h>>2]|0,c[i>>2]|0,c[g>>2]|0,2);if(b|0)kb(b+4|0,f,c[h>>2]|0,c[i>>2]|0,c[g>>2]|0,1);if(!d)return;kb(d+4|0,f,c[h>>2]|0,c[i>>2]|0,c[g>>2]|0,1);return}function kb(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;l=c[1682]|0;m=c[1684]|0;n=c[1678]|0;o=c[1680]|0;k=i<<3;j=0;i=b;while(1){y=d[g>>0]|0;B=d[i+-3>>0]|0;b=i+-2|0;p=d[b>>0]|0;u=i+-1|0;v=d[u>>0]|0;x=d[i>>0]|0;r=i+1|0;s=d[r>>0]|0;t=d[i+2>>0]|0;A=d[l+(p-v)>>0]|0;z=d[l+(s-x)>>0]|0;w=p-s|0;q=d[h>>0]|0;q=a[m+(q-z)>>0]|a[m+(q-A)>>0];w=(a[n+(((x-v|0)*3|0)+((a[n+w>>0]&q)<<24>>24))>>0]&~(a[m+(y-(d[l+(B-p)>>0]|0))>>0]|a[m+(y-(d[l+((d[i+-4>>0]|0)-B)>>0]|0))>>0]|a[m+(y-A)>>0]|a[m+(y-z)>>0]|a[m+(y-(d[l+(t-s)>>0]|0))>>0]|a[m+(y-(d[l+((d[i+3>>0]|0)-t)>>0]|0))>>0]|a[m+(((d[l+(v-x)>>0]|0)*-2|0)+(d[f>>0]|0)-((d[l+w>>0]|0)>>>1&255))>>0]))<<24>>24;t=a[n+(w+4)>>0]>>3;w=a[n+(w+3)>>0]>>3;a[i>>0]=a[o+(x-t)>>0]|0;a[u>>0]=a[o+(w+v)>>0]|0;q=t+1>>1&~q<<24>>24;a[r>>0]=a[o+(s-q)>>0]|0;a[b>>0]=a[o+(q+p)>>0]|0;j=j+1|0;if((j|0)>=(k|0))break;else i=i+e|0}return}function lb(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;i=c[1682]|0;j=c[1678]|0;g=0;h=b+4|0;while(1){o=a[h+-2>>0]|0;k=h+-1|0;q=a[k>>0]|0;p=a[h>>0]|0;m=a[h+1>>0]|0;n=(p^-128)<<24>>24;l=(q^-128)<<24>>24;m=((d[i+((q&255)-(p&255))>>0]<<1)+((d[i+((o&255)-(m&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[j+(((n-l|0)*3|0)+(a[j+(((o^-128)<<24>>24)-((m^-128)<<24>>24))>>0]|0))>>0]|0;a[h>>0]=a[j+(n-(a[j+(m+4)>>0]>>3))>>0]^-128;a[k>>0]=a[j+((a[j+(m+3)>>0]>>3)+l)>>0]^-128;g=g+1|0;if((g|0)==16)break;else h=h+e|0}g=0;h=b+8|0;while(1){m=a[h+-2>>0]|0;q=h+-1|0;k=a[q>>0]|0;l=a[h>>0]|0;o=a[h+1>>0]|0;n=(l^-128)<<24>>24;p=(k^-128)<<24>>24;o=((d[i+((k&255)-(l&255))>>0]<<1)+((d[i+((m&255)-(o&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[j+(((n-p|0)*3|0)+(a[j+(((m^-128)<<24>>24)-((o^-128)<<24>>24))>>0]|0))>>0]|0;a[h>>0]=a[j+(n-(a[j+(o+4)>>0]>>3))>>0]^-128;a[q>>0]=a[j+((a[j+(o+3)>>0]>>3)+p)>>0]^-128;g=g+1|0;if((g|0)==16)break;else h=h+e|0}h=0;g=b+12|0;while(1){m=a[g+-2>>0]|0;q=g+-1|0;k=a[q>>0]|0;l=a[g>>0]|0;o=a[g+1>>0]|0;n=(l^-128)<<24>>24;p=(k^-128)<<24>>24;o=((d[i+((k&255)-(l&255))>>0]<<1)+((d[i+((m&255)-(o&255))>>0]|0)>>>1&255)|0)>>>0>(d[f>>0]|0)>>>0?0:a[j+(((n-p|0)*3|0)+(a[j+(((m^-128)<<24>>24)-((o^-128)<<24>>24))>>0]|0))>>0]|0;a[g>>0]=a[j+(n-(a[j+(o+4)>>0]>>3))>>0]^-128;a[q>>0]=a[j+((a[j+(o+3)>>0]>>3)+p)>>0]^-128;h=h+1|0;if((h|0)==16)break;else g=g+e|0}return}function mb(a){a=a|0;c[a+2152>>2]=a;c[a+2180>>2]=a+4;c[a+2208>>2]=a+8;c[a+2236>>2]=a+12;c[a+2264>>2]=a+64;c[a+2292>>2]=a+68;c[a+2320>>2]=a+72;c[a+2348>>2]=a+76;c[a+2376>>2]=a+128;c[a+2404>>2]=a+132;c[a+2432>>2]=a+136;c[a+2460>>2]=a+140;c[a+2488>>2]=a+192;c[a+2516>>2]=a+196;c[a+2544>>2]=a+200;c[a+2572>>2]=a+204;c[a+2600>>2]=a+256;c[a+2628>>2]=a+260;c[a+2656>>2]=a+288;c[a+2684>>2]=a+292;c[a+2712>>2]=a+320;c[a+2740>>2]=a+324;c[a+2768>>2]=a+352;c[a+2796>>2]=a+356;c[a+2144>>2]=a+384;c[a+2148>>2]=a+1184;c[a+2164>>2]=a+1984;c[a+2172>>2]=a+416;c[a+2176>>2]=a+1216;c[a+2192>>2]=a+1985;c[a+2200>>2]=a+448;c[a+2204>>2]=a+1248;c[a+2220>>2]=a+1986;c[a+2228>>2]=a+480;c[a+2232>>2]=a+1280;c[a+2248>>2]=a+1987;c[a+2256>>2]=a+512;c[a+2260>>2]=a+1312;c[a+2276>>2]=a+1988;c[a+2284>>2]=a+544;c[a+2288>>2]=a+1344;c[a+2304>>2]=a+1989;c[a+2312>>2]=a+576;c[a+2316>>2]=a+1376;c[a+2332>>2]=a+1990;c[a+2340>>2]=a+608;c[a+2344>>2]=a+1408;c[a+2360>>2]=a+1991;c[a+2368>>2]=a+640;c[a+2372>>2]=a+1440;c[a+2388>>2]=a+1992;c[a+2396>>2]=a+672;c[a+2400>>2]=a+1472;c[a+2416>>2]=a+1993;c[a+2424>>2]=a+704;c[a+2428>>2]=a+1504;c[a+2444>>2]=a+1994;c[a+2452>>2]=a+736;c[a+2456>>2]=a+1536;c[a+2472>>2]=a+1995;c[a+2480>>2]=a+768;c[a+2484>>2]=a+1568;c[a+2500>>2]=a+1996;c[a+2508>>2]=a+800;c[a+2512>>2]=a+1600;c[a+2528>>2]=a+1997;c[a+2536>>2]=a+832;c[a+2540>>2]=a+1632;c[a+2556>>2]=a+1998;c[a+2564>>2]=a+864;c[a+2568>>2]=a+1664;c[a+2584>>2]=a+1999;c[a+2592>>2]=a+896;c[a+2596>>2]=a+1696;c[a+2612>>2]=a+2e3;c[a+2620>>2]=a+928;c[a+2624>>2]=a+1728;c[a+2640>>2]=a+2001;c[a+2648>>2]=a+960;c[a+2652>>2]=a+1760;c[a+2668>>2]=a+2002;c[a+2676>>2]=a+992;c[a+2680>>2]=a+1792;c[a+2696>>2]=a+2003;c[a+2704>>2]=a+1024;c[a+2708>>2]=a+1824;c[a+2724>>2]=a+2004;c[a+2732>>2]=a+1056;c[a+2736>>2]=a+1856;c[a+2752>>2]=a+2005;c[a+2760>>2]=a+1088;c[a+2764>>2]=a+1888;c[a+2780>>2]=a+2006;c[a+2788>>2]=a+1120;c[a+2792>>2]=a+1920;c[a+2808>>2]=a+2007;c[a+2816>>2]=a+1152;c[a+2820>>2]=a+1952;c[a+2836>>2]=a+2008;return}function nb(a){a=a|0;var b=0,d=0;b=c[a+2984>>2]|0;c[a+2160>>2]=0;c[a+2188>>2]=4;c[a+2216>>2]=8;c[a+2244>>2]=12;d=b<<2;c[a+2272>>2]=d;c[a+2300>>2]=d+4;c[a+2328>>2]=d+8;c[a+2356>>2]=d+12;d=b<<3;c[a+2384>>2]=d;c[a+2412>>2]=d|4;c[a+2440>>2]=d+8;c[a+2468>>2]=d+12;b=b*12|0;c[a+2496>>2]=b;c[a+2524>>2]=b+4;c[a+2552>>2]=b+8;c[a+2580>>2]=b+12;b=c[a+3004>>2]|0;c[a+2608>>2]=0;c[a+2720>>2]=0;c[a+2636>>2]=4;c[a+2748>>2]=4;b=b<<2;c[a+2664>>2]=b;c[a+2776>>2]=b;b=b+4|0;c[a+2692>>2]=b;c[a+2804>>2]=b;return}function ob(b,d,e,f,g,h){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0;k=L;L=L+16|0;i=k;j=a[c[b+3088>>2]>>0]|0;a[i>>0]=a[e>>0]|0;a[i+1>>0]=a[e+f>>0]|0;a[i+2>>0]=a[e+(f<<1)>>0]|0;a[i+3>>0]=a[e+(f*3|0)>>0]|0;a[i+4>>0]=a[e+(f<<2)>>0]|0;a[i+5>>0]=a[e+(f*5|0)>>0]|0;a[i+6>>0]=a[e+(f*6|0)>>0]|0;a[i+7>>0]=a[e+(f*7|0)>>0]|0;a[i+8>>0]=a[e+(f<<3)>>0]|0;a[i+9>>0]=a[e+(f*9|0)>>0]|0;a[i+10>>0]=a[e+(f*10|0)>>0]|0;a[i+11>>0]=a[e+(f*11|0)>>0]|0;a[i+12>>0]=a[e+(f*12|0)>>0]|0;a[i+13>>0]=a[e+(f*13|0)>>0]|0;a[i+14>>0]=a[e+(f*14|0)>>0]|0;a[i+15>>0]=a[e+(f*15|0)>>0]|0;if(!(j<<24>>24)){j=6544+(c[b+3104>>2]<<4)+(c[b+3100>>2]<<3)|0;j=c[j>>2]|0;V[j&31](g,h,d,i);L=k;return}else{j=6576+((j&255)<<3)|0;j=c[j>>2]|0;V[j&31](g,h,d,i);L=k;return}}function pb(b,d,e,f,g,h,i,j,k){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0;o=L;L=L+16|0;n=o+8|0;l=o;m=a[(c[b+3088>>2]|0)+1>>0]|0;a[n>>0]=a[f>>0]|0;a[l>>0]=a[g>>0]|0;a[n+1>>0]=a[f+h>>0]|0;a[l+1>>0]=a[g+h>>0]|0;p=h<<1;a[n+2>>0]=a[f+p>>0]|0;a[l+2>>0]=a[g+p>>0]|0;p=h*3|0;a[n+3>>0]=a[f+p>>0]|0;a[l+3>>0]=a[g+p>>0]|0;p=h<<2;a[n+4>>0]=a[f+p>>0]|0;a[l+4>>0]=a[g+p>>0]|0;p=h*5|0;a[n+5>>0]=a[f+p>>0]|0;a[l+5>>0]=a[g+p>>0]|0;p=h*6|0;a[n+6>>0]=a[f+p>>0]|0;a[l+6>>0]=a[g+p>>0]|0;h=h*7|0;a[n+7>>0]=a[f+h>>0]|0;a[l+7>>0]=a[g+h>>0]|0;if(!(m<<24>>24)){p=6544+(c[b+3104>>2]<<4)+(c[b+3100>>2]<<3)+4|0;p=c[p>>2]|0;V[p&31](i,k,d,n);V[p&31](j,k,e,l);L=o;return}else{p=6576+((m&255)<<3)+4|0;p=c[p>>2]|0;V[p&31](i,k,d,n);V[p&31](j,k,e,l);L=o;return}}function qb(){if(c[1685]|0)return;c[1646]=2;c[1648]=3;c[1650]=4;c[1636]=5;c[1638]=6;c[1640]=7;c[1642]=8;c[1647]=9;c[1649]=10;c[1651]=11;c[1637]=12;c[1639]=13;c[1641]=14;c[1643]=15;rb();c[1685]=1;return}function rb(){c[1652]=16;c[1653]=17;c[1654]=18;c[1655]=19;c[1656]=20;c[1657]=21;c[1658]=22;c[1659]=23;c[1660]=24;c[1661]=25;return}function sb(b,e,f,g,h,i,j){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0;k=L;L=L+16|0;n=k+4|0;l=k;m=n+4|0;a[l>>0]=a[e>>0]|0;a[l+1>>0]=a[e+f>>0]|0;a[l+2>>0]=a[e+(f<<1)>>0]|0;a[l+3>>0]=a[e+(f*3|0)>>0]|0;e=b;o=e;o=d[o>>0]|d[o+1>>0]<<8|d[o+2>>0]<<16|d[o+3>>0]<<24;e=e+4|0;e=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;f=m;b=f;a[b>>0]=o;a[b+1>>0]=o>>8;a[b+2>>0]=o>>16;a[b+3>>0]=o>>24;f=f+4|0;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;a[n+3>>0]=j;V[c[6608+(g<<2)>>2]&31](h,i,m,l);L=k;return}function tb(a){a=a|0;var d=0,e=0,f=0,g=0,h=0,i=0;e=a+5748|0;f=a+5752|0;g=a+5760|0;h=a+5756|0;i=a+5764|0;d=0;do{b[a+3504+(d<<2)>>1]=rc(d,c[e>>2]|0)|0;b[a+4016+(d<<2)>>1]=sc(d,c[f>>2]|0)|0;b[a+4528+(d<<2)>>1]=tc(d,c[g>>2]|0)|0;b[a+3504+(d<<2)+2>>1]=uc(d)|0;b[a+4016+(d<<2)+2>>1]=vc(d,c[h>>2]|0)|0;b[a+4528+(d<<2)+2>>1]=wc(d,c[i>>2]|0)|0;d=d+1|0}while((d|0)!=128);return}function ub(e,f){e=e|0;f=f|0;var g=0,h=0,i=0;g=c[f+3088>>2]|0;if(!(a[f+3148>>0]|0))g=c[e+5744>>2]|0;else{if((a[f+3151>>0]|0)==1)g=a[(d[g+11>>0]|0)+(f+3155)>>0]|0;else g=(c[e+5744>>2]|0)+(a[(d[g+11>>0]|0)+(f+3155)>>0]|0)|0;g=(g|0)>-1?((g|0)<127?g:127):0}b[f+2048>>1]=1;b[f+2016>>1]=b[e+3504+(g<<2)>>1]|0;b[f+2080>>1]=b[e+4016+(g<<2)>>1]|0;b[f+2112>>1]=b[e+4528+(g<<2)>>1]|0;i=e+3504+(g<<2)+2|0;h=e+4016+(g<<2)+2|0;e=e+4528+(g<<2)+2|0;g=b[i>>1]|0;b[f+2018>>1]=g;b[f+2050>>1]=g;b[f+2082>>1]=b[h>>1]|0;b[f+2114>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2020>>1]=g;b[f+2052>>1]=g;b[f+2084>>1]=b[h>>1]|0;b[f+2116>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2022>>1]=g;b[f+2054>>1]=g;b[f+2086>>1]=b[h>>1]|0;b[f+2118>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2024>>1]=g;b[f+2056>>1]=g;b[f+2088>>1]=b[h>>1]|0;b[f+2120>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2026>>1]=g;b[f+2058>>1]=g;b[f+2090>>1]=b[h>>1]|0;b[f+2122>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2028>>1]=g;b[f+2060>>1]=g;b[f+2092>>1]=b[h>>1]|0;b[f+2124>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2030>>1]=g;b[f+2062>>1]=g;b[f+2094>>1]=b[h>>1]|0;b[f+2126>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2032>>1]=g;b[f+2064>>1]=g;b[f+2096>>1]=b[h>>1]|0;b[f+2128>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2034>>1]=g;b[f+2066>>1]=g;b[f+2098>>1]=b[h>>1]|0;b[f+2130>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2036>>1]=g;b[f+2068>>1]=g;b[f+2100>>1]=b[h>>1]|0;b[f+2132>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2038>>1]=g;b[f+2070>>1]=g;b[f+2102>>1]=b[h>>1]|0;b[f+2134>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2040>>1]=g;b[f+2072>>1]=g;b[f+2104>>1]=b[h>>1]|0;b[f+2136>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2042>>1]=g;b[f+2074>>1]=g;b[f+2106>>1]=b[h>>1]|0;b[f+2138>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2044>>1]=g;b[f+2076>>1]=g;b[f+2108>>1]=b[h>>1]|0;b[f+2140>>1]=b[e>>1]|0;g=b[i>>1]|0;b[f+2046>>1]=g;b[f+2078>>1]=g;b[f+2110>>1]=b[h>>1]|0;b[f+2142>>1]=b[e>>1]|0;return}function vb(e){e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Ra=0,Sa=0,Ua=0,Va=0;Va=L;L=L+144|0;Ua=Va+128|0;H=Va+120|0;F=Va+112|0;G=Va+104|0;t=Va+96|0;o=Va+88|0;n=Va+80|0;k=Va+72|0;g=Va+64|0;Ja=Va+16|0;Ka=Va;M=e+11680|0;Sa=e+3248|0;f=c[e+11740>>2]|0;E=e+11776|0;l=c[E>>2]|0;m=f+l|0;Oa=e+11844|0;Pa=c[Oa>>2]|0;N=e+3232|0;q=c[N>>2]|0;Na=e+3220|0;c[Na>>2]=0;Ra=q+112|0;c[Ra>>2]=0;s=m;if((l|0)<3){if(!(c[e+11836>>2]|0))ja(Sa,7,6139,g);c[e+5700>>2]=1;c[e+11432>>2]=0;c[e+5704>>2]=1;p=0}else{g=c[e+11852>>2]|0;if(!g)h=f;else{V[g&31](c[e+11856>>2]|0,f,Ja,l>>>0<10?l:10);h=Ja}i=e+5700|0;c[i>>2]=a[h>>0]&1;c[e+11432>>2]=(d[h>>0]|0)>>>1&7;c[e+5704>>2]=(d[h>>0]|0)>>>4&1;p=(d[h+1>>0]<<8|d[h>>0]|d[h+2>>0]<<16)>>>5;j=e+11836|0;if((c[j>>2]|0)==0&(p|0)>(l|0))ja(Sa,7,6156,k);k=f+3|0;g=h+3|0;Qa(Sa);do if(!(c[i>>2]|0)){if((l|0)>6){if(!(((a[g>>0]|0)==-99?(a[h+4>>0]|0)==1:0)?(a[h+5>>0]|0)==42:0))ja(Sa,5,6203,n);if((l|0)>9){Ma=h+7|0;c[e+5040>>2]=d[Ma>>0]<<8&16128|d[h+6>>0];c[e+5048>>2]=(d[Ma>>0]|0)>>>6;Ma=h+9|0;c[e+5044>>2]=d[Ma>>0]<<8&16128|d[h+8>>0];c[e+5052>>2]=(d[Ma>>0]|0)>>>6;f=f+10|0;break}}if(!(c[j>>2]|0)){ja(Sa,7,6227,o);f=k}else f=m}else{u=e+2848|0;g=q;v=u+120|0;do{c[u>>2]=c[g>>2];u=u+4|0;g=g+4|0}while((u|0)<(v|0));u=e+2968|0;g=q;v=u+120|0;do{c[u>>2]=c[g>>2];u=u+4|0;g=g+4|0}while((u|0)<(v|0));f=k}while(0)}La=e+11840|0;Ma=e+5700|0;g=c[Ma>>2]|0;h=(g|0)==0;if(!(c[La>>2]|0))if(h)Ia=24;else{Ua=-1;L=Va;return Ua|0}else if(!h){Ha=(c[e+5736>>2]|0)==0;c[e+3200>>2]=Ha?1:2;c[e+3204>>2]=Ha?3:4;c[e+3208>>2]=Ha?5:6;c[e+3212>>2]=Ha?7:8;if((c[e+11832>>2]|0)!=0?(r=e+11836|0,(c[r>>2]|0)==0):0)c[r>>2]=1}else Ia=24;if((Ia|0)==24){u=e+11389|0;g=3520;v=u+38|0;do{a[u>>0]=a[g>>0]|0;u=u+1|0;g=g+1|0}while((u|0)<(v|0));Ta(Sa);bc(Sa);g=e+3155|0;Ha=g;a[Ha>>0]=0;a[Ha+1>>0]=0;a[Ha+2>>0]=0;a[Ha+3>>0]=0;g=g+4|0;a[g>>0]=0;a[g+1>>0]=0;a[g+2>>0]=0;a[g+3>>0]=0;a[e+3151>>0]=0;g=e+3169|0;a[g>>0]=0;a[g+1>>0]=0;a[g+2>>0]=0;a[g+3>>0]=0;g=e+3177|0;a[g>>0]=0;a[g+1>>0]=0;a[g+2>>0]=0;a[g+3>>0]=0;c[e+9152>>2]=1;c[e+9156>>2]=1;c[e+9160>>2]=0;c[e+9164>>2]=0;c[e+9180>>2]=0;c[e+9184>>2]=0;g=c[Ma>>2]|0}Ga=e+3144|0;c[Ga>>2]=e+9192;C=c[e+5772>>2]|0;Ha=e+3088|0;c[Ha>>2]=C;c[e+3096>>2]=g;a[C>>0]=0;c[e+3092>>2]=c[e+5724>>2];c[Na>>2]=0;c[e+2844>>2]=(c[e+5740>>2]|0)==0?-1:-8;C=e+11852|0;D=e+11856|0;if(Ac(M,f,s-f|0,c[C>>2]|0,c[D>>2]|0)|0)ja(Sa,2,6254,t);K=e+11696|0;g=c[K>>2]|0;if(!(c[Ma>>2]|0)){i=(((g<<7)+-128|0)>>>8)+1|0;n=e+11692|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}m=e+11688|0;h=c[m>>2]|0;j=i<<24;if(h>>>0>=j>>>0){i=(c[K>>2]|0)-i|0;h=h-j|0}Fa=d[2160+i>>0]|0;l=i<<Fa;h=h<<Fa;i=g-Fa|0;c[m>>2]=h;c[n>>2]=i;c[K>>2]=l;l=(((l<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);g=c[m>>2]|0;i=c[n>>2]|0}else g=h;j=l<<24;if(g>>>0<j>>>0){k=0;h=l}else{k=1;h=(c[K>>2]|0)-l|0;g=g-j|0}Ea=d[2160+h>>0]|0;Fa=h<<Ea;h=i-Ea|0;c[m>>2]=g<<Ea;c[n>>2]=h;c[K>>2]=Fa;c[e+5056>>2]=k;g=Fa}else{h=e+11692|0;n=h;h=c[h>>2]|0}i=(((g<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);h=c[n>>2]|0}J=e+11688|0;g=c[J>>2]|0;j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;g=g-j|0}j=d[2160+i>>0]|0;i=i<<j;g=g<<j;j=h-j|0;c[J>>2]=g;c[n>>2]=j;c[K>>2]=i;Fa=e+3148|0;a[Fa>>0]=k;if(k<<24>>24){h=(((i<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);g=c[J>>2]|0;j=c[n>>2]|0}i=h<<24;if(g>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;g=g-i|0}t=d[2160+h>>0]|0;i=h<<t;g=g<<t;h=j-t|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;t=e+3149|0;a[t>>0]=k;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;g=g-j|0}j=d[2160+i>>0]|0;i=i<<j;g=g<<j;j=h-j|0;c[J>>2]=g;c[n>>2]=j;c[K>>2]=i;a[e+3150>>0]=k;if(k<<24>>24){h=(((i<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);g=c[J>>2]|0;j=c[n>>2]|0}i=h<<24;if(g>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;g=g-i|0}r=d[2160+h>>0]|0;h=h<<r;i=j-r|0;c[J>>2]=g<<r;c[n>>2]=i;c[K>>2]=h;a[e+3151>>0]=k;r=e+3155|0;s=r;a[s>>0]=0;a[s+1>>0]=0;a[s+2>>0]=0;a[s+3>>0]=0;r=r+4|0;a[r>>0]=0;a[r+1>>0]=0;a[r+2>>0]=0;a[r+3>>0]=0;r=c[1383]|0;s=(r|0)>0;g=0;while(1){j=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);i=c[n>>2]|0}h=c[J>>2]|0;k=j<<24;if(h>>>0<k>>>0)l=1;else{l=0;j=(c[K>>2]|0)-j|0;h=h-k|0}Ea=d[2160+j>>0]|0;k=j<<Ea;j=h<<Ea;i=i-Ea|0;c[J>>2]=j;c[n>>2]=i;c[K>>2]=k;do if(l)a[e+3155+g>>0]=0;else{if(s){q=r;h=0;do{o=q;q=q+-1|0;k=(((k<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);j=c[J>>2]|0;i=c[n>>2]|0}l=k<<24;if(j>>>0<l>>>0)m=0;else{m=1;k=(c[K>>2]|0)-k|0;j=j-l|0}Ea=d[2160+k>>0]|0;k=k<<Ea;j=j<<Ea;i=i-Ea|0;c[J>>2]=j;c[n>>2]=i;c[K>>2]=k;h=m<<q|h}while((o|0)>1)}else h=0;m=e+3155+g|0;a[m>>0]=h;j=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;h=c[n>>2]|0;if((h|0)<0){Bc(M);h=c[n>>2]|0}i=c[J>>2]|0;k=j<<24;if(i>>>0<k>>>0)l=1;else{l=0;j=(c[K>>2]|0)-j|0;i=i-k|0}Ea=d[2160+j>>0]|0;c[J>>2]=i<<Ea;c[n>>2]=h-Ea;c[K>>2]=j<<Ea;if(l)break;a[m>>0]=0-(d[m>>0]|0)}while(0);g=g+1|0;if((g|0)==4)break;h=c[K>>2]|0;i=c[n>>2]|0}r=c[1384]|0;s=(r|0)>0;q=0;do{i=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}h=c[J>>2]|0;j=i<<24;if(h>>>0<j>>>0)k=1;else{k=0;i=(c[K>>2]|0)-i|0;h=h-j|0}Ea=d[2160+i>>0]|0;j=i<<Ea;i=h<<Ea;h=g-Ea|0;c[J>>2]=i;c[n>>2]=h;c[K>>2]=j;do if(k)a[e+3159+q>>0]=0;else{if(s){o=r;g=0;do{m=o;o=o+-1|0;j=(((j<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);i=c[J>>2]|0;h=c[n>>2]|0}k=j<<24;if(i>>>0<k>>>0)l=0;else{l=1;j=(c[K>>2]|0)-j|0;i=i-k|0}Ea=d[2160+j>>0]|0;j=j<<Ea;i=i<<Ea;h=h-Ea|0;c[J>>2]=i;c[n>>2]=h;c[K>>2]=j;g=l<<o|g}while((m|0)>1)}else g=0;l=e+3159+q|0;a[l>>0]=g;i=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}h=c[J>>2]|0;j=i<<24;if(h>>>0<j>>>0)k=1;else{k=0;i=(c[K>>2]|0)-i|0;h=h-j|0}Ea=d[2160+i>>0]|0;c[J>>2]=h<<Ea;c[n>>2]=g-Ea;c[K>>2]=i<<Ea;if(k)break;a[l>>0]=0-(d[l>>0]|0)}while(0);q=q+1|0}while((q|0)!=4)}if(a[t>>0]|0){u=e+3152|0;b[u>>1]=65535;a[u+2>>0]=255;u=0;do{i=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}h=c[J>>2]|0;j=i<<24;if(h>>>0<j>>>0)l=1;else{l=0;i=(c[K>>2]|0)-i|0;h=h-j|0}k=d[2160+i>>0]|0;i=i<<k;j=h<<k;k=g-k|0;c[J>>2]=j;c[n>>2]=k;c[K>>2]=i;if(!l){h=(((i<<7)+-128|0)>>>8)+1|0;if((k|0)<0){Bc(M);g=c[J>>2]|0;k=c[n>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)t=0;else{t=128;h=(c[K>>2]|0)-h|0;g=g-i|0}Ea=d[2160+h>>0]|0;i=h<<Ea;g=g<<Ea;h=k-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)s=0;else{s=64;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;i=i<<Ea;g=g<<Ea;h=h-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)r=0;else{r=32;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;i=i<<Ea;g=g<<Ea;h=h-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=16;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;i=i<<Ea;g=g<<Ea;h=h-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=8;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;i=i<<Ea;g=g<<Ea;h=h-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=4;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;i=i<<Ea;g=g<<Ea;h=h-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;i=i<<Ea;g=g<<Ea;h=h-Ea|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;g=g-j|0}Ea=d[2160+i>>0]|0;c[J>>2]=g<<Ea;c[n>>2]=h-Ea;c[K>>2]=i<<Ea;a[e+3152+u>>0]=k|(l|(m|(o|(q|(r|(s|t))))))}u=u+1|0}while((u|0)!=3)}}else{a[e+3149>>0]=0;a[e+3150>>0]=0}i=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}h=c[J>>2]|0;j=i<<24;if(h>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;h=h-j|0}Ea=d[2160+i>>0]|0;Da=i<<Ea;h=h<<Ea;i=g-Ea|0;c[J>>2]=h;c[n>>2]=i;c[K>>2]=Da;Ea=e+5780|0;c[Ea>>2]=k;k=(((Da<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);g=c[J>>2]|0;i=c[n>>2]|0}else g=h;j=k<<24;if(g>>>0<j>>>0){r=0;h=k}else{r=32;h=(c[K>>2]|0)-k|0;g=g-j|0}Ca=d[2160+h>>0]|0;Da=h<<Ca;g=g<<Ca;h=i-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=Da;i=(((Da<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=16;i=(c[K>>2]|0)-i|0;g=g-j|0}Da=d[2160+i>>0]|0;i=i<<Da;g=g<<Da;h=h-Da|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=8;i=(c[K>>2]|0)-i|0;g=g-j|0}Da=d[2160+i>>0]|0;i=i<<Da;g=g<<Da;h=h-Da|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=4;i=(c[K>>2]|0)-i|0;g=g-j|0}Da=d[2160+i>>0]|0;i=i<<Da;g=g<<Da;h=h-Da|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[K>>2]|0)-i|0;g=g-j|0}Da=d[2160+i>>0]|0;i=i<<Da;g=g<<Da;h=h-Da|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;g=g-j|0}j=d[2160+i>>0]|0;Ca=i<<j;g=g<<j;j=h-j|0;c[J>>2]=g;c[n>>2]=j;c[K>>2]=Ca;Da=e+9136|0;c[Da>>2]=k|(l|(m|(o|(q|r))));h=(((Ca<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);g=c[J>>2]|0;j=c[n>>2]|0}i=h<<24;if(g>>>0<i>>>0)m=0;else{m=4;h=(c[K>>2]|0)-h|0;g=g-i|0}Ca=d[2160+h>>0]|0;i=h<<Ca;g=g<<Ca;h=j-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[K>>2]|0)-i|0;g=g-j|0}Ca=d[2160+i>>0]|0;i=i<<Ca;g=g<<Ca;h=h-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;g=g-j|0}Ba=d[2160+i>>0]|0;Ca=i<<Ba;g=g<<Ba;i=h-Ba|0;c[J>>2]=g;c[n>>2]=i;c[K>>2]=Ca;c[e+9144>>2]=k|(l|m);m=e+3164|0;a[m>>0]=0;h=(((Ca<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);g=c[J>>2]|0;k=c[n>>2]|0}else k=i;i=h<<24;if(g>>>0<i>>>0)l=0;else{l=1;h=(c[K>>2]|0)-h|0;g=g-i|0}i=d[2160+h>>0]|0;h=h<<i;j=g<<i;i=k-i|0;c[J>>2]=j;c[n>>2]=i;c[K>>2]=h;a[e+3163>>0]=l;do if(l<<24>>24){g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);j=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(j>>>0<h>>>0)k=0;else{k=1;g=(c[K>>2]|0)-g|0;j=j-h|0}Ca=d[2160+g>>0]|0;h=g<<Ca;i=i-Ca|0;c[J>>2]=j<<Ca;c[n>>2]=i;c[K>>2]=h;a[m>>0]=k;if(!(k<<24>>24))break;g=0;while(1){j=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);i=c[n>>2]|0}h=c[J>>2]|0;k=j<<24;if(h>>>0<k>>>0)l=1;else{l=0;j=(c[K>>2]|0)-j|0;h=h-k|0}k=d[2160+j>>0]|0;j=j<<k;h=h<<k;k=i-k|0;c[J>>2]=h;c[n>>2]=k;c[K>>2]=j;do if(!l){i=(((j<<7)+-128|0)>>>8)+1|0;if((k|0)<0){Bc(M);h=c[J>>2]|0;k=c[n>>2]|0}j=i<<24;if(h>>>0<j>>>0)t=0;else{t=32;i=(c[K>>2]|0)-i|0;h=h-j|0}Ca=d[2160+i>>0]|0;j=i<<Ca;h=h<<Ca;i=k-Ca|0;c[J>>2]=h;c[n>>2]=i;c[K>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);h=c[J>>2]|0;i=c[n>>2]|0}k=j<<24;if(h>>>0<k>>>0)s=0;else{s=16;j=(c[K>>2]|0)-j|0;h=h-k|0}Ca=d[2160+j>>0]|0;j=j<<Ca;h=h<<Ca;i=i-Ca|0;c[J>>2]=h;c[n>>2]=i;c[K>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);h=c[J>>2]|0;i=c[n>>2]|0}k=j<<24;if(h>>>0<k>>>0)r=0;else{r=8;j=(c[K>>2]|0)-j|0;h=h-k|0}Ca=d[2160+j>>0]|0;j=j<<Ca;h=h<<Ca;i=i-Ca|0;c[J>>2]=h;c[n>>2]=i;c[K>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);h=c[J>>2]|0;i=c[n>>2]|0}k=j<<24;if(h>>>0<k>>>0)q=0;else{q=4;j=(c[K>>2]|0)-j|0;h=h-k|0}Ca=d[2160+j>>0]|0;j=j<<Ca;h=h<<Ca;i=i-Ca|0;c[J>>2]=h;c[n>>2]=i;c[K>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);h=c[J>>2]|0;i=c[n>>2]|0}k=j<<24;if(h>>>0<k>>>0)o=0;else{o=2;j=(c[K>>2]|0)-j|0;h=h-k|0}Ca=d[2160+j>>0]|0;j=j<<Ca;h=h<<Ca;i=i-Ca|0;c[J>>2]=h;c[n>>2]=i;c[K>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);h=c[J>>2]|0;i=c[n>>2]|0}k=j<<24;if(h>>>0<k>>>0)l=0;else{l=1;j=(c[K>>2]|0)-j|0;h=h-k|0}m=d[2160+j>>0]|0;c[J>>2]=h<<m;c[n>>2]=i-m;c[K>>2]=j<<m;m=e+3169+g|0;a[m>>0]=l|(o|(q|(r|(s|t))));j=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;h=c[n>>2]|0;if((h|0)<0){Bc(M);h=c[n>>2]|0}i=c[J>>2]|0;k=j<<24;if(i>>>0<k>>>0)l=1;else{l=0;j=(c[K>>2]|0)-j|0;i=i-k|0}Ca=d[2160+j>>0]|0;c[J>>2]=i<<Ca;c[n>>2]=h-Ca;c[K>>2]=j<<Ca;if(l)break;a[m>>0]=0-(d[m>>0]|0)}while(0);g=g+1|0;if((g|0)==4)break;h=c[K>>2]|0;i=c[n>>2]|0}t=0;do{i=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}h=c[J>>2]|0;j=i<<24;if(h>>>0<j>>>0)l=1;else{l=0;i=(c[K>>2]|0)-i|0;h=h-j|0}k=d[2160+i>>0]|0;i=i<<k;j=h<<k;k=g-k|0;c[J>>2]=j;c[n>>2]=k;c[K>>2]=i;do if(!l){h=(((i<<7)+-128|0)>>>8)+1|0;if((k|0)<0){Bc(M);g=c[J>>2]|0;k=c[n>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)s=0;else{s=32;h=(c[K>>2]|0)-h|0;g=g-i|0}Ca=d[2160+h>>0]|0;i=h<<Ca;g=g<<Ca;h=k-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)r=0;else{r=16;i=(c[K>>2]|0)-i|0;g=g-j|0}Ca=d[2160+i>>0]|0;i=i<<Ca;g=g<<Ca;h=h-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=8;i=(c[K>>2]|0)-i|0;g=g-j|0}Ca=d[2160+i>>0]|0;i=i<<Ca;g=g<<Ca;h=h-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=4;i=(c[K>>2]|0)-i|0;g=g-j|0}Ca=d[2160+i>>0]|0;i=i<<Ca;g=g<<Ca;h=h-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=2;i=(c[K>>2]|0)-i|0;g=g-j|0}Ca=d[2160+i>>0]|0;i=i<<Ca;g=g<<Ca;h=h-Ca|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);g=c[J>>2]|0;h=c[n>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[K>>2]|0)-i|0;g=g-j|0}l=d[2160+i>>0]|0;c[J>>2]=g<<l;c[n>>2]=h-l;c[K>>2]=i<<l;l=e+3177+t|0;a[l>>0]=k|(m|(o|(q|(r|s))));i=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;g=c[n>>2]|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}h=c[J>>2]|0;j=i<<24;if(h>>>0<j>>>0)k=1;else{k=0;i=(c[K>>2]|0)-i|0;h=h-j|0}Ca=d[2160+i>>0]|0;c[J>>2]=h<<Ca;c[n>>2]=g-Ca;c[K>>2]=i<<Ca;if(k)break;a[l>>0]=0-(d[l>>0]|0)}while(0);t=t+1|0}while((t|0)!=4);h=c[K>>2]|0;i=c[n>>2]|0}while(0);A=f+p|0;B=e+11740|0;l=c[B>>2]|0;m=c[E>>2]|0;g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);i=c[n>>2]|0}f=c[J>>2]|0;h=g<<24;if(f>>>0<h>>>0)k=0;else{k=2;g=(c[K>>2]|0)-g|0;f=f-h|0}Ca=d[2160+g>>0]|0;h=g<<Ca;f=f<<Ca;g=i-Ca|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}z=d[2160+h>>0]|0;Ca=g-z|0;c[J>>2]=f<<z;c[n>>2]=Ca;c[K>>2]=h<<z;f=j|k;z=e+11456|0;x=l+m|0;I=e+11436|0;if((Ca+-33|0)>>>0>1073741790)c[I>>2]=f;else f=c[I>>2]|0;v=1<<f;y=e+11736|0;g=c[y>>2]|0;if(g|0){w=A;p=(3<<f)+-3|0;q=v+-1|0;r=x;s=e+11836|0;t=e+3248|0;u=e+11744|0;f=0;do{j=c[e+11776+(f<<2)>>2]|0;h=c[e+11740+(f<<2)>>2]|0;o=h+j|0;do if(f){if(j){g=j;Ia=166}}else{h=c[B>>2]|0;i=p+(w-h)|0;f=j-i|0;if(!f){f=0;break}c[E>>2]=i;h=h+i|0;c[u>>2]=h;g=f;f=1;Ia=166}while(0);if((Ia|0)==166){m=o;l=g;while(1){k=e+11740+(f<<2)|0;Ia=f+-1|0;g=A+(Ia*3|0)|0;j=m-h|0;do if((q|0)>(Ia|0)){if(g>>>0>=x>>>0|(r-g|0)>>>0<3){if(c[s>>2]|0){g=j;Ia=174;break}ja(t,7,6288,G);g=0;Ia=175;break}i=c[C>>2]|0;if(i){V[i&31](c[D>>2]|0,g,Ja,3);g=Ja}g=d[g+1>>0]<<8|d[g>>0]|d[g+2>>0]<<16;Ia=174}else{g=j;Ia=174}while(0);if((Ia|0)==174){Ia=0;if(!(j>>>0>=g>>>0&(o>>>0>h>>>0&(g|0)!=0)))Ia=175}do if((Ia|0)==175){Ia=0;if(c[s>>2]|0){g=j;break}c[F>>2]=f;ja(t,7,6318,F)}while(0);c[e+11776+(f<<2)>>2]=g;h=l-g|0;i=f+1|0;if(!h)break;Ia=(c[k>>2]|0)+g|0;c[e+11740+(i<<2)>>2]=Ia;l=h;f=i;h=Ia}g=c[y>>2]|0}f=f+1|0}while(f>>>0<g>>>0)}Ca=v+1|0;c[y>>2]=Ca;if(Ca>>>0>1){h=e+3248|0;f=1;g=z;while(1){if(Ac(g,c[e+11740+(f<<2)>>2]|0,c[e+11776+(f<<2)>>2]|0,c[C>>2]|0,c[D>>2]|0)|0){c[H>>2]=f;ja(h,2,6366,H)}f=f+1|0;if(f>>>0>=(c[y>>2]|0)>>>0)break;else g=g+28|0}}Ca=e+3216|0;c[Ca>>2]=z;f=c[n>>2]|0;h=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;if((f|0)<0){Bc(M);f=c[n>>2]|0}g=c[J>>2]|0;i=h<<24;if(g>>>0<i>>>0)q=0;else{q=64;h=(c[K>>2]|0)-h|0;g=g-i|0}Ba=d[2160+h>>0]|0;j=h<<Ba;g=g<<Ba;h=f-Ba|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);f=c[J>>2]|0;h=c[n>>2]|0}else f=g;i=j<<24;if(f>>>0<i>>>0){p=0;g=j}else{p=32;g=(c[K>>2]|0)-j|0;f=f-i|0}Aa=d[2160+g>>0]|0;Ba=g<<Aa;f=f<<Aa;g=h-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=Ba;h=(((Ba<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)o=0;else{o=16;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=8;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}i=d[2160+h>>0]|0;Ba=h<<i;f=f<<i;i=g-i|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=Ba;c[e+5744>>2]=j|(k|(l|(m|(o|(p|q)))));q=e+5748|0;p=c[q>>2]|0;g=(((Ba<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)j=1;else{j=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;i=i-Ba|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=h;if(j)g=0;else{g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)o=0;else{o=8;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;g=i-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;f=f-i|0}j=d[2160+h>>0]|0;Ba=h<<j;f=f<<j;j=g-j|0;c[J>>2]=f;c[n>>2]=j;c[K>>2]=Ba;k=k|(l|(m|o));g=(((Ba<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)i=1;else{i=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;Ba=j-Ba|0;c[J>>2]=f;c[n>>2]=Ba;c[K>>2]=h;g=i?k:0-k|0;i=Ba}r=(g|0)!=(p|0);c[q>>2]=g;q=e+5752|0;p=c[q>>2]|0;g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)j=1;else{j=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;i=i-Ba|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=h;if(j)g=0;else{g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)o=0;else{o=8;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;g=i-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;f=f-i|0}j=d[2160+h>>0]|0;Ba=h<<j;f=f<<j;j=g-j|0;c[J>>2]=f;c[n>>2]=j;c[K>>2]=Ba;k=k|(l|(m|o));g=(((Ba<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)i=1;else{i=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;Ba=j-Ba|0;c[J>>2]=f;c[n>>2]=Ba;c[K>>2]=h;g=i?k:0-k|0;i=Ba}r=r|(g|0)!=(p|0);c[q>>2]=g;q=e+5756|0;p=c[q>>2]|0;g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)j=1;else{j=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;i=i-Ba|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=h;if(j)g=0;else{g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)o=0;else{o=8;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;g=i-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;f=f-i|0}j=d[2160+h>>0]|0;Ba=h<<j;f=f<<j;j=g-j|0;c[J>>2]=f;c[n>>2]=j;c[K>>2]=Ba;k=k|(l|(m|o));g=(((Ba<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)i=1;else{i=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;Ba=j-Ba|0;c[J>>2]=f;c[n>>2]=Ba;c[K>>2]=h;g=i?k:0-k|0;i=Ba}r=r|(g|0)!=(p|0);c[q>>2]=g;q=e+5760|0;p=c[q>>2]|0;g=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);i=c[n>>2]|0;f=c[J>>2]|0}h=g<<24;if(f>>>0<h>>>0)j=1;else{j=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;g=g<<Ba;f=f<<Ba;i=i-Ba|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=g;if(j)h=0;else{g=(((g<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)o=0;else{o=8;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;g=i-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;f=f-i|0}j=d[2160+h>>0]|0;Ba=h<<j;f=f<<j;j=g-j|0;c[J>>2]=f;c[n>>2]=j;c[K>>2]=Ba;k=k|(l|(m|o));g=(((Ba<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)i=1;else{i=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;g=g<<Ba;f=f<<Ba;Ba=j-Ba|0;c[J>>2]=f;c[n>>2]=Ba;c[K>>2]=g;h=i?k:0-k|0;i=Ba}r=r|(h|0)!=(p|0);c[q>>2]=h;p=e+5764|0;q=c[p>>2]|0;g=(((g<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)j=1;else{j=0;g=(c[K>>2]|0)-g|0;f=f-h|0}h=d[2160+g>>0]|0;g=g<<h;f=f<<h;h=i-h|0;c[J>>2]=f;c[n>>2]=h;c[K>>2]=g;if(j)f=0;else{g=(((g<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);f=c[J>>2]|0;h=c[n>>2]|0}i=g<<24;if(f>>>0<i>>>0)o=0;else{o=8;g=(c[K>>2]|0)-g|0;f=f-i|0}Aa=d[2160+g>>0]|0;Ba=g<<Aa;f=f<<Aa;g=h-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=Ba;h=(((Ba<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Ba=d[2160+h>>0]|0;h=h<<Ba;f=f<<Ba;g=g-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=1;h=(c[K>>2]|0)-h|0;f=f-i|0}j=d[2160+h>>0]|0;Ba=h<<j;f=f<<j;j=g-j|0;c[J>>2]=f;c[n>>2]=j;c[K>>2]=Ba;k=k|(l|(m|o));g=(((Ba<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)i=1;else{i=0;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;c[J>>2]=f<<Ba;c[n>>2]=j-Ba;c[K>>2]=g<<Ba;f=i?k:0-k|0}c[p>>2]=f;if(!((f|0)==(q|0)&(r^1)))tb(e);ub(e,e);if(!(c[Ma>>2]|0)){f=c[K>>2]|0;g=c[n>>2]|0}else{h=(((c[K>>2]<<7)+-128|0)>>>8)+1|0;f=c[n>>2]|0;if((f|0)<0){Bc(M);f=c[n>>2]|0}g=c[J>>2]|0;i=h<<24;if(g>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;g=g-i|0}l=d[2160+h>>0]|0;Ba=h<<l;g=g<<l;h=f-l|0;c[J>>2]=g;c[n>>2]=h;c[K>>2]=Ba;l=e+9152|0;c[l>>2]=j;j=(((Ba<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);f=c[J>>2]|0;h=c[n>>2]|0}else f=g;i=j<<24;if(f>>>0<i>>>0){k=0;g=j}else{k=1;g=(c[K>>2]|0)-j|0;f=f-i|0}i=d[2160+g>>0]|0;g=g<<i;f=f<<i;i=h-i|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=g;o=e+9156|0;c[o>>2]=k;m=e+9160|0;c[m>>2]=0;if(!(c[l>>2]|0)){g=(((g<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)k=0;else{k=2;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;g=i-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}i=d[2160+h>>0]|0;h=h<<i;f=f<<i;i=g-i|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=h;c[m>>2]=j|k;g=h;h=c[o>>2]|0}else h=k;l=e+9164|0;c[l>>2]=0;if(!h){g=(((g<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);f=c[J>>2]|0;i=c[n>>2]|0}h=g<<24;if(f>>>0<h>>>0)k=0;else{k=2;g=(c[K>>2]|0)-g|0;f=f-h|0}Ba=d[2160+g>>0]|0;h=g<<Ba;f=f<<Ba;g=i-Ba|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;Ba=h<<Aa;f=f<<Aa;h=g-Aa|0;c[J>>2]=f;c[n>>2]=h;c[K>>2]=Ba;c[l>>2]=j|k;g=Ba}else h=i;g=(((g<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(M);f=c[J>>2]|0;h=c[n>>2]|0}i=g<<24;if(f>>>0<i>>>0)j=0;else{j=1;g=(c[K>>2]|0)-g|0;f=f-i|0}Aa=d[2160+g>>0]|0;Ba=g<<Aa;f=f<<Aa;g=h-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=Ba;c[e+9180>>2]=j;h=(((Ba<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;Ba=h<<Aa;g=g-Aa|0;c[J>>2]=f<<Aa;c[n>>2]=g;c[K>>2]=Ba;c[e+9184>>2]=j;f=Ba}h=(((f<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);g=c[n>>2]|0}f=c[J>>2]|0;i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}i=d[2160+h>>0]|0;h=h<<i;f=f<<i;i=g-i|0;c[J>>2]=f;c[n>>2]=i;c[K>>2]=h;Ba=e+9168|0;c[Ba>>2]=j;if(!j)td(e+9201|0,e+10314|0,1113)|0;if(!(c[Ma>>2]|0))f=1;else{h=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(M);g=c[J>>2]|0;i=c[n>>2]|0}else g=f;j=h<<24;if(g>>>0<j>>>0)f=0;else{f=1;h=(c[K>>2]|0)-h|0;g=g-j|0}Aa=d[2160+h>>0]|0;c[J>>2]=g<<Aa;c[n>>2]=i-Aa;c[K>>2]=h<<Aa}c[e+9148>>2]=f;c[Oa>>2]=1;x=0;do{w=0;do{v=0;do{u=v+-1|0;if(!v){s=0;do{t=e+10333+(x*264|0)+(w*33|0)+s|0;h=((((c[K>>2]|0)+-1|0)*(d[1104+(x*264|0)+(w*33|0)+s>>0]|0)|0)>>>8)+1|0;f=c[n>>2]|0;if((f|0)<0){Bc(M);f=c[n>>2]|0}g=c[J>>2]|0;i=h<<24;if(g>>>0<i>>>0)k=1;else{k=0;h=(c[K>>2]|0)-h|0;g=g-i|0}j=d[2160+h>>0]|0;h=h<<j;i=g<<j;j=f-j|0;c[J>>2]=i;c[n>>2]=j;c[K>>2]=h;if(!k){g=(((h<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}else f=i;h=g<<24;if(f>>>0<h>>>0)r=0;else{r=128;g=(c[K>>2]|0)-g|0;f=f-h|0}Aa=d[2160+g>>0]|0;h=g<<Aa;f=f<<Aa;g=j-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)q=0;else{q=64;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)p=0;else{p=32;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)o=0;else{o=16;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=8;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;c[J>>2]=f<<Aa;c[n>>2]=g-Aa;c[K>>2]=h<<Aa;a[t>>0]=j|(k|(l|(m|(o|(p|(q|r))))))}s=s+1|0}while((s|0)!=11)}else{t=0;do{s=e+10333+(x*264|0)+(w*33|0)+(v*11|0)+t|0;h=((((c[K>>2]|0)+-1|0)*(d[1104+(x*264|0)+(w*33|0)+(v*11|0)+t>>0]|0)|0)>>>8)+1|0;f=c[n>>2]|0;if((f|0)<0){Bc(M);f=c[n>>2]|0}g=c[J>>2]|0;i=h<<24;if(g>>>0<i>>>0)k=1;else{k=0;h=(c[K>>2]|0)-h|0;g=g-i|0}j=d[2160+h>>0]|0;h=h<<j;i=g<<j;j=f-j|0;c[J>>2]=i;c[n>>2]=j;c[K>>2]=h;if(k)f=a[s>>0]|0;else{g=(((h<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(M);f=c[J>>2]|0;j=c[n>>2]|0}else f=i;h=g<<24;if(f>>>0<h>>>0)r=0;else{r=128;g=(c[K>>2]|0)-g|0;f=f-h|0}Aa=d[2160+g>>0]|0;h=g<<Aa;f=f<<Aa;g=j-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)q=0;else{q=64;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)p=0;else{p=32;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)o=0;else{o=16;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=8;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=4;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=2;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;h=h<<Aa;f=f<<Aa;g=g-Aa|0;c[J>>2]=f;c[n>>2]=g;c[K>>2]=h;h=(((h<<7)+-128|0)>>>8)+1|0;if((g|0)<0){Bc(M);f=c[J>>2]|0;g=c[n>>2]|0}i=h<<24;if(f>>>0<i>>>0)j=0;else{j=1;h=(c[K>>2]|0)-h|0;f=f-i|0}Aa=d[2160+h>>0]|0;c[J>>2]=f<<Aa;c[n>>2]=g-Aa;c[K>>2]=h<<Aa;f=(j|(k|(l|(m|(o|(p|(q|r)))))))&255;a[s>>0]=f}if(f<<24>>24!=(a[e+10333+(x*264|0)+(w*33|0)+(u*11|0)+t>>0]|0))c[Oa>>2]=0;t=t+1|0}while((t|0)!=11)}v=v+1|0}while((v|0)!=3);w=w+1|0}while((w|0)!=8);x=x+1|0}while((x|0)!=4);na=e+384|0;ud(na|0,0,800)|0;Cc(e);oa=e+9188|0;pa=e+5720|0;ud(c[oa>>2]|0,0,(c[pa>>2]|0)*9|0)|0;c[e+11848>>2]=0;m=c[Ha>>2]|0;la=c[I>>2]|0;ya=c[N>>2]|0;va=ya+16|0;ta=c[va>>2]|0;xa=ya+36|0;ua=c[xa>>2]|0;c[Ka>>2]=0;wa=c[e+3236>>2]|0;c[Ja+12>>2]=c[wa+52>>2];c[Ja+16>>2]=c[wa+56>>2];c[Ja+20>>2]=c[wa+60>>2];c[Ka+4>>2]=c[wa+112>>2];wa=c[e+3240>>2]|0;c[Ja+24>>2]=c[wa+52>>2];c[Ja+28>>2]=c[wa+56>>2];c[Ja+32>>2]=c[wa+60>>2];c[Ka+8>>2]=c[wa+112>>2];wa=c[e+3244>>2]|0;c[Ja+36>>2]=c[wa+52>>2];c[Ja+40>>2]=c[wa+56>>2];c[Ja+44>>2]=c[wa+60>>2];c[Ka+12>>2]=c[wa+112>>2];la=1<<la;wa=ya+52|0;qa=c[wa>>2]|0;za=ya+56|0;sa=c[za>>2]|0;Aa=ya+60|0;ra=c[Aa>>2]|0;ma=e+3100|0;c[ma>>2]=0;f=c[Da>>2]|0;if(f|0)Ya(Sa,e,f);zc(ya);A=e+5716|0;if((c[A>>2]|0)>0){B=(la|0)>1;C=ta<<4;D=ua<<3;E=e+3140|0;F=e+3104|0;G=e+3192|0;H=e+3196|0;I=e+3108|0;J=e+3112|0;K=e+3116|0;M=e+3120|0;N=e+3124|0;O=e+3128|0;P=e+2984|0;Q=e+3004|0;R=e+3132|0;S=e+3136|0;T=e+3020|0;U=e+3024|0;W=e+3028|0;X=e+3184|0;Y=e+3188|0;Z=e+2900|0;_=e+2904|0;$=e+2908|0;aa=e+2016|0;ba=e+1984|0;ca=e+896|0;da=e+2112|0;ea=e+2e3|0;fa=e+2816|0;ga=e+2008|0;ha=e+2080|0;ia=e+2820|0;ka=e+2048|0;p=0;l=0;h=qa;k=qa;j=ra;g=sa;f=ra;i=sa;while(1){o=p+1|0;if(B){c[Ca>>2]=e+11456+(p*28|0);z=(o|0)==(la|0)?0:o}else z=p;o=l*C|0;p=l*D|0;c[E>>2]=c[oa>>2];u=c[Ga>>2]|0;v=u+9|0;do{a[u>>0]=0;u=u+1|0}while((u|0)<(v|0));c[F>>2]=0;c[G>>2]=0-(l<<7);c[H>>2]=33554431-l+(c[A>>2]|0)<<7;s=qa+o|0;x=sa+p|0;w=ra+p|0;u=s+-1|0;c[M>>2]=u;r=x+-1|0;c[N>>2]=r;y=w+-1|0;c[O>>2]=y;t=c[P>>2]|0;c[I>>2]=s+(0-t);s=c[Q>>2]|0;v=0-s|0;c[J>>2]=x+v;c[K>>2]=w+v;c[R>>2]=t;c[S>>2]=s;a[u>>0]=-127;a[u+t>>0]=-127;a[u+(t<<1)>>0]=-127;a[u+(t*3|0)>>0]=-127;a[u+(t<<2)>>0]=-127;a[u+(t*5|0)>>0]=-127;a[u+(t*6|0)>>0]=-127;a[u+(t*7|0)>>0]=-127;a[u+(t<<3)>>0]=-127;a[u+(t*9|0)>>0]=-127;a[u+(t*10|0)>>0]=-127;a[u+(t*11|0)>>0]=-127;a[u+(t*12|0)>>0]=-127;a[u+(t*13|0)>>0]=-127;a[u+(t*14|0)>>0]=-127;a[u+(t*15|0)>>0]=-127;a[r>>0]=-127;a[r+s>>0]=-127;t=s<<1;a[r+t>>0]=-127;u=s*3|0;a[r+u>>0]=-127;v=s<<2;a[r+v>>0]=-127;w=s*5|0;a[r+w>>0]=-127;x=s*6|0;a[r+x>>0]=-127;q=s*7|0;a[r+q>>0]=-127;a[y>>0]=-127;a[y+s>>0]=-127;a[y+t>>0]=-127;a[y+u>>0]=-127;a[y+v>>0]=-127;a[y+w>>0]=-127;a[y+x>>0]=-127;a[y+q>>0]=-127;q=c[pa>>2]|0;if((q|0)>0){y=o;x=0;o=q;q=c[Ha>>2]|0;while(1){c[X>>2]=0-(x<<7);c[Y>>2]=o+33554431-x<<7;c[T>>2]=qa+y;c[U>>2]=sa+p;c[W>>2]=ra+p;o=a[q+2>>0]|0;if(!(o<<24>>24)){c[Z>>2]=0;c[_>>2]=0;c[$>>2]=0;o=Ka}else{o=o&255;c[Z>>2]=(c[Ja+(o*12|0)>>2]|0)+y;c[_>>2]=(c[Ja+(o*12|0)+4>>2]|0)+p;c[$>>2]=(c[Ja+(o*12|0)+8>>2]|0)+p;o=Ka+(o<<2)|0}c[Na>>2]=c[Na>>2]|c[o>>2];do if(!(a[q+9>>0]|0)){if(((c[(c[Ca>>2]|0)+12>>2]|0)+-33|0)>>>0<=1073741790){o=q;break}w=(yb(e,e)|0)==0&1;o=c[Ha>>2]|0;a[o+9>>0]=w}else{xb(e);o=c[Ha>>2]|0}while(0);w=a[o>>0]|0;if(a[Fa>>0]|0){ub(e,e);o=c[Ha>>2]|0}a:do if(!(a[o+2>>0]|0)){pb(e,c[J>>2]|0,c[K>>2]|0,c[N>>2]|0,c[O>>2]|0,c[S>>2]|0,c[U>>2]|0,c[W>>2]|0,c[Q>>2]|0);if(w<<24>>24!=4){ob(e,c[I>>2]|0,c[M>>2]|0,c[R>>2]|0,c[T>>2]|0,c[P>>2]|0);break}u=c[P>>2]|0;q=c[Ha>>2]|0;if(a[q+9>>0]|0){c[ba>>2]=0;c[ba+4>>2]=0;c[ba+8>>2]=0;c[ba+12>>2]=0;c[ba+16>>2]=0;c[ba+20>>2]=0;a[ba+24>>0]=0}t=(c[I>>2]|0)+16|0;r=c[T>>2]|0;v=0-u|0;o=r+v+16|0;c[o+(u<<2)>>2]=c[t>>2];c[o+(u<<3)>>2]=c[t>>2];c[o+(u*12|0)>>2]=c[t>>2];o=0;while(1){t=r+(c[e+2144+(o*28|0)+16>>2]|0)|0;s=t+v|0;sb(s,t+-1|0,u,c[q+12+(o<<2)>>2]|0,t,u,a[s+-1>>0]|0);q=a[e+1984+o>>0]|0;do if(q<<24>>24){r=e+2144+(o*28|0)|0;s=c[r>>2]|0;if(q<<24>>24>1){ac(s,aa,t,u);break}else{oc((b[aa>>1]|0)*(b[s>>1]|0)&65535,t,u,t,u);t=c[r>>2]|0;b[t>>1]=0;b[t+2>>1]=0>>>16;break}}while(0);o=o+1|0;if((o|0)==16)break a;r=c[T>>2]|0;q=c[Ha>>2]|0}}else yc(e);while(0);o=c[Ha>>2]|0;if(!(a[o+9>>0]|0)){b:do switch(w<<24>>24){case 4:break;case 9:{o=aa;Ia=379;break}default:if((a[ga>>0]|0)>1){$b(fa,ha);pc(c[ia>>2]|0,na);u=c[fa>>2]|0;v=u+32|0;do{b[u>>1]=0;u=u+2|0}while((u|0)<(v|0));o=ka;Ia=379;break b}else{o=c[ia>>2]|0;b[o>>1]=(b[ha>>1]|0)*(b[c[fa>>2]>>1]|0);qc(o,na);o=c[fa>>2]|0;b[o>>1]=0;b[o+2>>1]=0>>>16;o=ka;Ia=379;break b}}while(0);if((Ia|0)==379){Ia=0;lc(na,o,c[T>>2]|0,c[P>>2]|0,ba)}mc(ca,da,c[U>>2]|0,c[W>>2]|0,c[Q>>2]|0,ea);o=c[Ha>>2]|0}c[F>>2]=1;c[Na>>2]=c[Na>>2]|((c[(c[Ca>>2]|0)+12>>2]|0)+-33|0)>>>0<1073741791;c[I>>2]=(c[I>>2]|0)+16;c[J>>2]=(c[J>>2]|0)+8;c[K>>2]=(c[K>>2]|0)+8;c[M>>2]=(c[M>>2]|0)+16;c[N>>2]=(c[N>>2]|0)+8;c[O>>2]=(c[O>>2]|0)+8;q=o+76|0;c[Ha>>2]=q;c[E>>2]=(c[E>>2]|0)+9;x=x+1|0;o=c[pa>>2]|0;if((x|0)>=(o|0))break;else{y=y+16|0;p=p+8|0}}}cc(ya,(c[T>>2]|0)+16|0,(c[U>>2]|0)+8|0,(c[W>>2]|0)+8|0);c[Ha>>2]=(c[Ha>>2]|0)+76;c[ma>>2]=1;o=(l|0)!=0;do if(!(c[Da>>2]|0)){if(!o)break;wb(ya,h,g,f);h=h+C|0;g=g+D|0;f=f+D|0}else{if(!o)break;o=l+-1|0;if(!(c[Ea>>2]|0))Za(Sa,m,o,ta,ua,k,i,j);else _a(Sa,m,o,ta,ua,k,i,j);if(l>>>0>1){wb(ya,h,g,f);h=h+C|0;g=g+D|0;f=f+D|0}m=m+((c[pa>>2]|0)*76|0)+76|0;k=k+C|0;j=j+D|0;i=i+D|0}while(0);o=l+1|0;if((o|0)<(c[A>>2]|0)){p=z;l=o}else break}}else{l=-1;h=qa;k=qa;j=ra;g=sa;f=ra;i=sa}if(c[Da>>2]|0){if(!(c[Ea>>2]|0))Za(Sa,m,l,ta,ua,k,i,j);else _a(Sa,m,l,ta,ua,k,i,j);wb(ya,h,g,f);Ka=ua<<3;h=h+(ta<<4)|0;g=g+Ka|0;f=f+Ka|0}wb(ya,h,g,f);m=ya+76|0;j=c[m>>2]|0;h=c[va>>2]|0;f=0-j|0;i=(c[wa>>2]|0)+f|0;if((j|0)>0){g=0;f=i+(h*f|0)|0;while(1){td(f|0,i|0,h|0)|0;g=g+1|0;if((g|0)==(j|0))break;else f=f+h|0}}l=c[xa>>2]|0;k=j>>>1;h=0-k|0;i=(c[za>>2]|0)+h|0;j=l*h|0;if(k|0){f=0;g=i+j|0;while(1){td(g|0,i|0,l|0)|0;f=f+1|0;if((f|0)==(k|0))break;else g=g+l|0}h=(c[Aa>>2]|0)+h|0;g=0;f=h+j|0;while(1){td(f|0,h|0,l|0)|0;g=g+1|0;if((g|0)==(k|0))break;else f=f+l|0}}j=c[m>>2]|0;i=c[va>>2]|0;h=(c[wa>>2]|0)+(0-j)+((c[ya+4>>2]|0)*i|0)+(0-i)|0;if((j|0)>0){f=0;g=h;do{g=g+i|0;td(g|0,h|0,i|0)|0;f=f+1|0}while((f|0)!=(j|0))}m=c[xa>>2]|0;l=j>>>1;k=0-l|0;i=(c[ya+24>>2]|0)*m|0;j=0-m|0;h=(c[za>>2]|0)+k+i+j|0;if(l|0){f=0;g=h;do{g=g+m|0;td(g|0,h|0,m|0)|0;f=f+1|0}while((f|0)!=(l|0));h=(c[Aa>>2]|0)+k+i+j|0;f=0;g=h;do{g=g+m|0;td(g|0,h|0,m|0)|0;f=f+1|0}while((f|0)!=(l|0))}f=c[Na>>2]|((c[n>>2]|0)+-33|0)>>>0<1073741791;c[Ra>>2]=f;do if(!(c[La>>2]|0))if(!(c[Ma>>2]|f)){c[La>>2]=1;break}else{ja(Sa,7,6401,Ua);break}while(0);if(c[Ba>>2]|0){Ua=0;L=Va;return Ua|0}td(e+10314|0,e+9201|0,1113)|0;c[Oa>>2]=Pa;Ua=0;L=Va;return Ua|0}function wb(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;l=c[b+76>>2]|0;g=c[b+16>>2]|0;i=d+(c[b>>2]|0)+-1|0;k=d+(0-l)|0;j=i+1|0;ud(k|0,a[d>>0]|0,l|0)|0;ud(j|0,a[i>>0]|0,l|0)|0;h=d+g|0;i=i+g|0;k=k+g|0;d=j+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;h=h+g|0;i=i+g|0;k=k+g|0;d=d+g|0;ud(k|0,a[h>>0]|0,l|0)|0;ud(d|0,a[i>>0]|0,l|0)|0;ud(k+g|0,a[h+g>>0]|0,l|0)|0;ud(d+g|0,a[i+g>>0]|0,l|0)|0;g=c[b+36>>2]|0;i=c[b+20>>2]|0;d=l>>>1;l=e+i+-1|0;h=0-d|0;k=e+h|0;b=l+1|0;ud(k|0,a[e>>0]|0,d|0)|0;ud(b|0,a[l>>0]|0,d|0)|0;j=e+g|0;e=l+g|0;k=k+g|0;b=b+g|0;ud(k|0,a[j>>0]|0,d|0)|0;ud(b|0,a[e>>0]|0,d|0)|0;j=j+g|0;e=e+g|0;k=k+g|0;b=b+g|0;ud(k|0,a[j>>0]|0,d|0)|0;ud(b|0,a[e>>0]|0,d|0)|0;j=j+g|0;e=e+g|0;k=k+g|0;b=b+g|0;ud(k|0,a[j>>0]|0,d|0)|0;ud(b|0,a[e>>0]|0,d|0)|0;j=j+g|0;e=e+g|0;k=k+g|0;b=b+g|0;ud(k|0,a[j>>0]|0,d|0)|0;ud(b|0,a[e>>0]|0,d|0)|0;j=j+g|0;e=e+g|0;k=k+g|0;b=b+g|0;ud(k|0,a[j>>0]|0,d|0)|0;ud(b|0,a[e>>0]|0,d|0)|0;j=j+g|0;e=e+g|0;k=k+g|0;b=b+g|0;ud(k|0,a[j>>0]|0,d|0)|0;ud(b|0,a[e>>0]|0,d|0)|0;ud(k+g|0,a[j+g>>0]|0,d|0)|0;ud(b+g|0,a[e+g>>0]|0,d|0)|0;i=f+i+-1|0;h=f+h|0;e=i+1|0;ud(h|0,a[f>>0]|0,d|0)|0;ud(e|0,a[i>>0]|0,d|0)|0;b=f+g|0;f=i+g|0;h=h+g|0;e=e+g|0;ud(h|0,a[b>>0]|0,d|0)|0;ud(e|0,a[f>>0]|0,d|0)|0;b=b+g|0;f=f+g|0;h=h+g|0;e=e+g|0;ud(h|0,a[b>>0]|0,d|0)|0;ud(e|0,a[f>>0]|0,d|0)|0;b=b+g|0;f=f+g|0;h=h+g|0;e=e+g|0;ud(h|0,a[b>>0]|0,d|0)|0;ud(e|0,a[f>>0]|0,d|0)|0;b=b+g|0;f=f+g|0;h=h+g|0;e=e+g|0;ud(h|0,a[b>>0]|0,d|0)|0;ud(e|0,a[f>>0]|0,d|0)|0;b=b+g|0;f=f+g|0;h=h+g|0;e=e+g|0;ud(h|0,a[b>>0]|0,d|0)|0;ud(e|0,a[f>>0]|0,d|0)|0;b=b+g|0;f=f+g|0;h=h+g|0;e=e+g|0;ud(h|0,a[b>>0]|0,d|0)|0;ud(e|0,a[f>>0]|0,d|0)|0;ud(h+g|0,a[b+g>>0]|0,d|0)|0;ud(e+g|0,a[f+g>>0]|0,d|0)|0;return}function xb(b){b=b|0;var d=0,e=0,f=0,g=0;d=c[b+3140>>2]|0;e=c[b+3144>>2]|0;f=d;g=f;a[g>>0]=0;a[g+1>>0]=0;a[g+2>>0]=0;a[g+3>>0]=0;f=f+4|0;a[f>>0]=0;a[f+1>>0]=0;a[f+2>>0]=0;a[f+3>>0]=0;f=e;g=f;a[g>>0]=0;a[g+1>>0]=0;a[g+2>>0]=0;a[g+3>>0]=0;f=f+4|0;a[f>>0]=0;a[f+1>>0]=0;a[f+2>>0]=0;a[f+3>>0]=0;if(a[(c[b+3088>>2]|0)+3>>0]|0)return;a[e+8>>0]=0;a[d+8>>0]=0;return}function yb(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;j=c[d+3216>>2]|0;k=c[d+3140>>2]|0;l=c[d+3144>>2]|0;if(!(a[(c[d+3088>>2]|0)+3>>0]|0)){f=k+8|0;i=l+8|0;e=zb(j,b+10597|0,(a[i>>0]|0)+(a[f>>0]|0)|0,0,d+1152|0)|0;h=(e|0)>0&1;a[i>>0]=h;a[f>>0]=h;a[d+2008>>0]=e;f=1;h=b+10333|0;e=e+-16|0}else{f=0;h=b+11125|0;e=0}g=0;i=d+384|0;while(1){n=k+(g&3)|0;p=l+(g>>>2&3)|0;m=zb(j,h,(a[p>>0]|0)+(a[n>>0]|0)|0,f,i)|0;o=(m|0)>0&1;a[p>>0]=o;a[n>>0]=o;m=m+f|0;a[d+1984+g>>0]=m;e=m+e|0;g=g+1|0;if((g|0)==16)break;else i=i+32|0}h=b+10861|0;m=k+4|0;n=l+4|0;p=zb(j,h,(a[n>>0]|0)+(a[m>>0]|0)|0,0,d+896|0)|0;f=(p|0)>0&1;a[n>>0]=f;a[m>>0]=f;a[d+2e3>>0]=p;f=k+5|0;o=zb(j,h,(a[n>>0]|0)+(a[f>>0]|0)|0,0,d+928|0)|0;b=(o|0)>0&1;a[n>>0]=b;a[f>>0]=b;a[d+2001>>0]=o;b=l+5|0;n=zb(j,h,(a[b>>0]|0)+(a[m>>0]|0)|0,0,d+960|0)|0;q=(n|0)>0&1;a[b>>0]=q;a[m>>0]=q;a[d+2002>>0]=n;m=zb(j,h,(a[b>>0]|0)+(a[f>>0]|0)|0,0,d+992|0)|0;q=(m|0)>0&1;a[b>>0]=q;a[f>>0]=q;a[d+2003>>0]=m;f=k+6|0;q=l+6|0;b=zb(j,h,(a[q>>0]|0)+(a[f>>0]|0)|0,0,d+1024|0)|0;i=(b|0)>0&1;a[q>>0]=i;a[f>>0]=i;a[d+2004>>0]=b;i=k+7|0;k=zb(j,h,(a[q>>0]|0)+(a[i>>0]|0)|0,0,d+1056|0)|0;g=(k|0)>0&1;a[q>>0]=g;a[i>>0]=g;a[d+2005>>0]=k;g=l+7|0;l=zb(j,h,(a[g>>0]|0)+(a[f>>0]|0)|0,0,d+1088|0)|0;q=(l|0)>0&1;a[g>>0]=q;a[f>>0]=q;a[d+2006>>0]=l;j=zb(j,h,(a[g>>0]|0)+(a[i>>0]|0)|0,0,d+1120|0)|0;h=(j|0)>0&1;a[g>>0]=h;a[i>>0]=h;a[d+2007>>0]=j;return j+(l+(k+(b+(m+(n+(o+(p+e)))))))|0}function zb(e,f,g,h,i){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;o=f+(h*33|0)+(g*11|0)|0;u=e+16|0;k=((((c[u>>2]|0)+-1|0)*(d[o>>0]|0)|0)>>>8)+1|0;t=e+12|0;g=c[t>>2]|0;if((g|0)<0){Bc(e);g=c[t>>2]|0}s=e+8|0;j=c[s>>2]|0;l=k<<24;if(j>>>0<l>>>0)n=1;else{n=0;k=(c[u>>2]|0)-k|0;j=j-l|0}l=d[2160+k>>0]|0;m=k<<l;k=j<<l;l=g-l|0;c[s>>2]=k;c[t>>2]=l;c[u>>2]=m;if(n){i=0;return i|0}r=h;q=o;j=m;while(1){g=r+1|0;j=(((j+-1|0)*(d[q+1>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(e);k=c[s>>2]|0;l=c[t>>2]|0}m=j<<24;if(k>>>0<m>>>0)n=1;else{n=0;j=(c[u>>2]|0)-j|0;k=k-m|0}p=d[2160+j>>0]|0;j=j<<p;k=k<<p;l=l-p|0;c[s>>2]=k;c[t>>2]=l;c[u>>2]=j;if(n)if((g|0)==16){g=16;j=85;break}else n=f+((d[1056+g>>0]|0)*33|0)|0;else{j=(((j+-1|0)*(d[q+2>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(e);k=c[s>>2]|0;l=c[t>>2]|0}m=j<<24;if(k>>>0<m>>>0)o=1;else{o=0;j=(c[u>>2]|0)-j|0;k=k-m|0}n=d[2160+j>>0]|0;h=j<<n;j=k<<n;n=l-n|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=h;do if(o){m=1;l=1;k=h}else{k=(((h+-1|0)*(d[q+3>>0]|0)|0)>>>8)+1|0;if((n|0)<0){Bc(e);j=c[s>>2]|0;n=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)m=1;else{m=0;k=(c[u>>2]|0)-k|0;j=j-l|0}l=d[2160+k>>0]|0;k=k<<l;j=j<<l;l=n-l|0;c[s>>2]=j;c[t>>2]=l;c[u>>2]=k;if(m){k=(((k+-1|0)*(d[q+4>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(e);j=c[s>>2]|0;m=c[t>>2]|0}else m=l;l=k<<24;if(j>>>0<l>>>0)h=1;else{h=0;k=(c[u>>2]|0)-k|0;j=j-l|0}n=d[2160+k>>0]|0;k=k<<n;j=j<<n;n=m-n|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=k;if(h){m=2;l=2;break}k=(((k+-1|0)*(d[q+5>>0]|0)|0)>>>8)+1|0;if((n|0)<0){Bc(e);j=c[s>>2]|0;n=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)m=3;else{m=4;k=(c[u>>2]|0)-k|0;j=j-l|0}l=d[2160+k>>0]|0;k=k<<l;j=j<<l;n=n-l|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=k;l=2;break}k=(((k+-1|0)*(d[q+6>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(e);j=c[s>>2]|0;m=c[t>>2]|0}else m=l;l=k<<24;if(j>>>0<l>>>0)n=1;else{n=0;k=(c[u>>2]|0)-k|0;j=j-l|0}p=d[2160+k>>0]|0;k=k<<p;j=j<<p;m=m-p|0;c[s>>2]=j;c[t>>2]=m;c[u>>2]=k;if(!n){k=(((k+-1|0)*(d[q+8>>0]|0)|0)>>>8)+1|0;if((m|0)<0){Bc(e);j=c[s>>2]|0;m=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)p=0;else{p=1;k=(c[u>>2]|0)-k|0;j=j-l|0}o=d[2160+k>>0]|0;l=k<<o;j=j<<o;k=m-o|0;c[s>>2]=j;c[t>>2]=k;c[u>>2]=l;l=(((l+-1|0)*(d[q+(p+9)>>0]|0)|0)>>>8)+1|0;if((k|0)<0){Bc(e);j=c[s>>2]|0;k=c[t>>2]|0}m=l<<24;if(j>>>0<m>>>0)h=0;else{h=1;l=(c[u>>2]|0)-l|0;j=j-m|0}n=d[2160+l>>0]|0;o=l<<n;j=j<<n;n=k-n|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=o;p=h|p<<1;k=c[1040+(p<<2)>>2]|0;m=a[k>>0]|0;if(!(m<<24>>24)){l=0;k=o}else{l=0;h=o;do{m=(((h+-1|0)*(m&255)|0)>>>8)+1|0;if((n|0)<0){Bc(e);j=c[s>>2]|0;n=c[t>>2]|0}h=m<<24;if(j>>>0<h>>>0)o=0;else{o=1;m=(c[u>>2]|0)-m|0;j=j-h|0}q=d[2160+m>>0]|0;h=m<<q;j=j<<q;n=n-q|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=h;l=o|l<<1;k=k+1|0;m=a[k>>0]|0}while(m<<24>>24!=0);k=h}m=l+(8<<p|3)|0;l=2;break}k=(((k+-1|0)*(d[q+7>>0]|0)|0)>>>8)+1|0;if((m|0)<0){Bc(e);j=c[s>>2]|0;m=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)h=1;else{h=0;k=(c[u>>2]|0)-k|0;j=j-l|0}n=d[2160+k>>0]|0;k=k<<n;j=j<<n;n=m-n|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=k;k=k+-1|0;if(h){k=((k*159|0)>>>8)+1|0;if((n|0)<0){Bc(e);j=c[s>>2]|0;n=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)m=5;else{m=6;k=(c[u>>2]|0)-k|0;j=j-l|0}l=d[2160+k>>0]|0;k=k<<l;j=j<<l;n=n-l|0;c[s>>2]=j;c[t>>2]=n;c[u>>2]=k;l=2;break}k=((k*165|0)>>>8)+1|0;if((n|0)<0){Bc(e);j=c[s>>2]|0;n=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)h=7;else{h=9;k=(c[u>>2]|0)-k|0;j=j-l|0}q=d[2160+k>>0]|0;l=k<<q;j=j<<q;k=n-q|0;c[s>>2]=j;c[t>>2]=k;c[u>>2]=l;l=(((l*145|0)+-145|0)>>>8)+1|0;if((k|0)<0){Bc(e);j=c[s>>2]|0;k=c[t>>2]|0}m=l<<24;if(j>>>0<m>>>0)n=0;else{n=1;l=(c[u>>2]|0)-l|0;j=j-m|0}q=d[2160+l>>0]|0;p=l<<q;j=j<<q;q=k-q|0;c[s>>2]=j;c[t>>2]=q;c[u>>2]=p;m=n+h|0;l=2;k=p;n=q}while(0);o=f+((d[1056+g>>0]|0)*33|0)+(l*11|0)|0;h=d[1088+r>>0]|0;k=(k+1|0)>>>1;l=k<<24;if((n|0)<0){Bc(e);j=c[s>>2]|0}if(j>>>0<l>>>0)c[u>>2]=k;else{k=(c[u>>2]|0)-k|0;c[u>>2]=k;j=j-l|0;c[s>>2]=j;m=0-m|0}k=k<<1;c[u>>2]=k;j=j<<1;c[s>>2]=j;l=c[t>>2]|0;n=l+-1|0;c[t>>2]=n;b[i+(h<<1)>>1]=m;if((g|0)==16){g=16;j=85;break}k=(((k+-1|0)*(d[o>>0]|0)|0)>>>8)+1|0;if((l|0)<1){Bc(e);j=c[s>>2]|0;n=c[t>>2]|0}l=k<<24;if(j>>>0<l>>>0)h=1;else{h=0;k=(c[u>>2]|0)-k|0;j=j-l|0}r=d[2160+k>>0]|0;m=k<<r;k=j<<r;j=n-r|0;c[s>>2]=k;c[t>>2]=j;c[u>>2]=m;if(h){j=85;break}else{n=o;l=j;j=m}}r=g;q=n}if((j|0)==85)return g|0;return 0}function Ab(a){a=a|0;var b=0;if(!a){b=-1;return b|0}if((c[a+72>>2]|0)>0)Ka(c[a+68>>2]|0);b=a+120|0;do{c[a>>2]=0;a=a+4|0}while((a|0)<(b|0));b=0;return b|0}function Bb(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;if(!a){p=-2;return p|0}o=b+15&-16;p=d+15&-16;i=e<<1;h=o+31+i&-32;i=h*(p+i|0)|0;j=o>>1;k=p>>1;l=h>>1;m=l*(k+e|0)|0;n=(m<<1)+i|0;g=a+68|0;f=c[g>>2]|0;if(!f){f=Ia(32,n)|0;c[g>>2]=f;c[a+72>>2]=n;if(!f){p=-1;return p|0}}else if((c[a+72>>2]|0)<(n|0)){p=-1;return p|0}if(e&31|0){p=-3;return p|0}c[a+8>>2]=b;c[a+12>>2]=d;c[a>>2]=o;c[a+4>>2]=p;c[a+16>>2]=h;c[a+28>>2]=(b+1|0)/2|0;c[a+32>>2]=(d+1|0)/2|0;c[a+20>>2]=j;c[a+24>>2]=k;c[a+36>>2]=l;c[a+40>>2]=0;c[a+44>>2]=0;c[a+48>>2]=0;c[a+76>>2]=e;c[a+80>>2]=n;c[a+52>>2]=f+(h*e|0)+e;n=f+i|0;p=(e|0)/2|0;o=l*p|0;c[a+56>>2]=n+o+p;c[a+60>>2]=n+m+o+p;c[a+64>>2]=0;c[a+112>>2]=0;p=0;return p|0}function Cb(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0;if(!a){g=-2;return g|0}if((c[a+72>>2]|0)>0)Ka(c[a+68>>2]|0);f=a;g=f+120|0;do{c[f>>2]=0;f=f+4|0}while((f|0)<(g|0));g=Bb(a,b,d,e)|0;return g|0}function Db(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0,h=0,i=0;i=d[e>>0]|0;h=d[e+1>>0]|0;f=d[e+2>>0]|0;e=d[e+3>>0]|0;g=h+2|0;ud(a|0,(g+(d[c+-1>>0]|0)+(i<<1)|0)>>>2&255|0,4)|0;c=f+2|0;ud(a+b|0,(c+i+(h<<1)|0)>>>2&255|0,4)|0;ud(a+(b<<1)|0,(g+(f<<1)+e|0)>>>2&255|0,4)|0;ud(a+(b*3|0)|0,(c+e+(e<<1)|0)>>>2&255|0,4)|0;return}function Eb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;k=d[e>>0]|0;j=d[e+1>>0]|0;h=d[e+2>>0]|0;g=d[e+3>>0]|0;f=d[e+4>>0]|0;i=j+2|0;a[b>>0]=(i+(d[e+-1>>0]|0)+(k<<1)|0)>>>2;e=h+2|0;a[b+1>>0]=(e+k+(j<<1)|0)>>>2;a[b+2>>0]=(i+(h<<1)+g|0)>>>2;a[b+3>>0]=(e+(g<<1)+f|0)>>>2;f=b+c|0;e=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;f=b+(c<<1)|0;e=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;f=b+(c*3|0)|0;e=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;return}function Fb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;k=d[f>>0]|0;j=d[f+1>>0]|0;i=d[f+2>>0]|0;e=a[f+3>>0]|0;g=e&255;h=j+1|0;a[b>>0]=(h+k|0)>>>1;h=(h+i|0)>>>1&255;a[b+c>>0]=h;a[b+2>>0]=h;h=(i+1+g|0)>>>1&255;f=c<<1;a[b+f>>0]=h;a[b+(c+2)>>0]=h;h=i+2|0;a[b+1>>0]=(h+k+(j<<1)|0)>>>2;i=(g+2+j+(i<<1)|0)>>>2&255;a[b+(c+1)>>0]=i;a[b+3>>0]=i;g=(h+g+(g<<1)|0)>>>2&255;a[b+(f|1)>>0]=g;a[b+(c+3)>>0]=g;c=c*3|0;a[b+(c+3)>>0]=e;a[b+(c+2)>>0]=e;a[b+(c+1)>>0]=e;a[b+c>>0]=e;a[b+(f+2)>>0]=e;a[b+(f+3)>>0]=e;return}function Gb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;j=d[e>>0]|0;f=d[e+1>>0]|0;m=d[e+2>>0]|0;k=d[e+3>>0]|0;i=d[e+4>>0]|0;h=d[e+5>>0]|0;g=d[e+6>>0]|0;e=d[e+7>>0]|0;n=f+1|0;a[b>>0]=(n+j|0)>>>1;n=(n+m|0)>>>1&255;l=c<<1;a[b+l>>0]=n;a[b+1>>0]=n;n=(m+1+k|0)>>>1&255;a[b+(l|1)>>0]=n;a[b+2>>0]=n;n=(k+1+i|0)>>>1&255;a[b+(l+2)>>0]=n;a[b+3>>0]=n;a[b+(l+3)>>0]=(i+2+(h<<1)+g|0)>>>2;l=m+2|0;a[b+c>>0]=(l+j+(f<<1)|0)>>>2;j=k+2|0;m=(j+f+(m<<1)|0)>>>2&255;f=c*3|0;a[b+f>>0]=m;a[b+(c+1)>>0]=m;k=(l+(k<<1)+i|0)>>>2&255;a[b+(f+1)>>0]=k;a[b+(c+2)>>0]=k;i=(j+(i<<1)+h|0)>>>2&255;a[b+(f+2)>>0]=i;a[b+(c+3)>>0]=i;a[b+(f+3)>>0]=(h+2+(g<<1)+e|0)>>>2;return}function Hb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;n=d[e+1>>0]|0;m=d[e+2>>0]|0;f=d[e+3>>0]|0;j=d[e+4>>0]|0;i=d[e+5>>0]|0;h=d[e+6>>0]|0;g=d[e+7>>0]|0;l=m+2|0;a[b>>0]=(l+(d[e>>0]|0)+(n<<1)|0)>>>2;k=f+2|0;e=(k+n+(m<<1)|0)>>>2&255;a[b+c>>0]=e;a[b+1>>0]=e;f=(l+(f<<1)+j|0)>>>2&255;e=c<<1;a[b+e>>0]=f;a[b+(c+1)>>0]=f;a[b+2>>0]=f;k=(k+(j<<1)+i|0)>>>2&255;f=c*3|0;a[b+f>>0]=k;a[b+(e|1)>>0]=k;a[b+(c+2)>>0]=k;a[b+3>>0]=k;j=(j+2+(i<<1)+h|0)>>>2&255;a[b+(f+1)>>0]=j;a[b+(e+2)>>0]=j;a[b+(c+3)>>0]=j;c=(i+2+(h<<1)+g|0)>>>2&255;a[b+(f+2)>>0]=c;a[b+(e+3)>>0]=c;a[b+(f+3)>>0]=(h+2+g+(g<<1)|0)>>>2;return}function Ib(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;n=d[f>>0]|0;o=d[f+1>>0]|0;p=d[f+2>>0]|0;k=d[e+-1>>0]|0;j=d[e>>0]|0;h=d[e+1>>0]|0;g=d[e+2>>0]|0;f=d[e+3>>0]|0;l=j+1|0;i=(l+k|0)>>>1&255;m=c<<1;a[b+(m|1)>>0]=i;a[b>>0]=i;l=(l+h|0)>>>1&255;a[b+(m+2)>>0]=l;a[b+1>>0]=l;l=(h+1+g|0)>>>1&255;a[b+(m+3)>>0]=l;a[b+2>>0]=l;a[b+3>>0]=(g+1+f|0)>>>1;l=n+2|0;i=c*3|0;a[b+i>>0]=(l+p+(o<<1)|0)>>>2;e=k+2|0;a[b+m>>0]=(e+o+(n<<1)|0)>>>2;k=(l+(k<<1)+j|0)>>>2&255;a[b+(i+1)>>0]=k;a[b+c>>0]=k;e=(e+(j<<1)+h|0)>>>2&255;a[b+(i+2)>>0]=e;a[b+(c+1)>>0]=e;e=(j+2+(h<<1)+g|0)>>>2&255;a[b+(i+3)>>0]=e;a[b+(c+2)>>0]=e;a[b+(c+3)>>0]=(h+2+(g<<1)+f|0)>>>2;return}function Jb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;m=d[f>>0]|0;p=d[f+1>>0]|0;o=d[f+2>>0]|0;j=d[e+-1>>0]|0;i=d[e>>0]|0;h=d[e+1>>0]|0;g=d[e+2>>0]|0;e=d[e+3>>0]|0;n=p+2|0;k=c*3|0;a[b+k>>0]=(n+(o<<1)+(d[f+3>>0]|0)|0)>>>2;l=m+2|0;o=(l+(p<<1)+o|0)>>>2&255;f=c<<1;a[b+f>>0]=o;a[b+(k+1)>>0]=o;m=(n+(m<<1)+j|0)>>>2&255;a[b+c>>0]=m;a[b+(f|1)>>0]=m;a[b+(k+2)>>0]=m;l=(l+i+(j<<1)|0)>>>2&255;a[b>>0]=l;a[b+(c+1)>>0]=l;a[b+(f+2)>>0]=l;a[b+(k+3)>>0]=l;j=(j+2+h+(i<<1)|0)>>>2&255;a[b+1>>0]=j;a[b+(c+2)>>0]=j;a[b+(f+3)>>0]=j;f=(i+2+g+(h<<1)|0)>>>2&255;a[b+2>>0]=f;a[b+(c+3)>>0]=f;a[b+3>>0]=(h+2+e+(g<<1)|0)>>>2;return}function Kb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;m=d[f>>0]|0;j=d[f+1>>0]|0;g=d[f+2>>0]|0;h=d[f+3>>0]|0;l=d[e+-1>>0]|0;n=d[e>>0]|0;k=d[e+1>>0]|0;e=d[e+2>>0]|0;o=m+1|0;i=(o+l|0)>>>1&255;a[b+(c+2)>>0]=i;a[b>>0]=i;o=(o+j|0)>>>1&255;i=c<<1;a[b+(i+2)>>0]=o;a[b+c>>0]=o;o=(j+1+g|0)>>>1&255;f=c*3|0;a[b+(f+2)>>0]=o;a[b+i>>0]=o;a[b+f>>0]=(g+1+h|0)>>>1;a[b+3>>0]=(n+2+(k<<1)+e|0)>>>2;a[b+2>>0]=(l+2+(n<<1)+k|0)>>>2;k=m+2|0;e=(k+(l<<1)+n|0)>>>2&255;a[b+(c+3)>>0]=e;a[b+1>>0]=e;e=j+2|0;l=(e+(m<<1)+l|0)>>>2&255;a[b+(i+3)>>0]=l;a[b+(c+1)>>0]=l;c=(k+g+(j<<1)|0)>>>2&255;a[b+(f+3)>>0]=c;a[b+(i|1)>>0]=c;a[b+(f+1)>>0]=(e+h+(g<<1)|0)>>>2;return}function Lb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0;i=e;g=i;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;f=b;h=f;a[h>>0]=g;a[h+1>>0]=g>>8;a[h+2>>0]=g>>16;a[h+3>>0]=g>>24;f=f+4|0;a[f>>0]=i;a[f+1>>0]=i>>8;a[f+2>>0]=i>>16;a[f+3>>0]=i>>24;f=b+c|0;i=e;h=i;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=f;b=g;a[b>>0]=h;a[b+1>>0]=h>>8;a[b+2>>0]=h>>16;a[b+3>>0]=h>>24;g=g+4|0;a[g>>0]=i;a[g+1>>0]=i>>8;a[g+2>>0]=i>>16;a[g+3>>0]=i>>24;f=f+c|0;g=e;i=g;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;b=f;h=b;a[h>>0]=i;a[h+1>>0]=i>>8;a[h+2>>0]=i>>16;a[h+3>>0]=i>>24;b=b+4|0;a[b>>0]=g;a[b+1>>0]=g>>8;a[b+2>>0]=g>>16;a[b+3>>0]=g>>24;f=f+c|0;b=e;g=b;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;b=b+4|0;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;h=f;i=h;a[i>>0]=g;a[i+1>>0]=g>>8;a[i+2>>0]=g>>16;a[i+3>>0]=g>>24;h=h+4|0;a[h>>0]=b;a[h+1>>0]=b>>8;a[h+2>>0]=b>>16;a[h+3>>0]=b>>24;f=f+c|0;h=e;b=h;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;h=h+4|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;i=f;g=i;a[g>>0]=b;a[g+1>>0]=b>>8;a[g+2>>0]=b>>16;a[g+3>>0]=b>>24;i=i+4|0;a[i>>0]=h;a[i+1>>0]=h>>8;a[i+2>>0]=h>>16;a[i+3>>0]=h>>24;f=f+c|0;i=e;h=i;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;i=i+4|0;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=f;b=g;a[b>>0]=h;a[b+1>>0]=h>>8;a[b+2>>0]=h>>16;a[b+3>>0]=h>>24;g=g+4|0;a[g>>0]=i;a[g+1>>0]=i>>8;a[g+2>>0]=i>>16;a[g+3>>0]=i>>24;f=f+c|0;g=e;i=g;i=d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;b=f;h=b;a[h>>0]=i;a[h+1>>0]=i>>8;a[h+2>>0]=i>>16;a[h+3>>0]=i>>24;b=b+4|0;a[b>>0]=g;a[b+1>>0]=g>>8;a[b+2>>0]=g>>16;a[b+3>>0]=g>>24;b=e;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;e=e+4|0;e=d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24;f=f+c|0;c=f;a[c>>0]=b;a[c+1>>0]=b>>8;a[c+2>>0]=b>>16;a[c+3>>0]=b>>24;f=f+4|0;a[f>>0]=e;a[f+1>>0]=e>>8;a[f+2>>0]=e>>16;a[f+3>>0]=e>>24;return}function Mb(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0;f=b;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=b+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));e=e+c|0;f=e;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));f=e+c|0;g=d;h=f+16|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}function Nb(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;ud(b|0,a[e>>0]|0,8)|0;d=b+c|0;ud(d|0,a[e+1>>0]|0,8)|0;d=d+c|0;ud(d|0,a[e+2>>0]|0,8)|0;d=d+c|0;ud(d|0,a[e+3>>0]|0,8)|0;d=d+c|0;ud(d|0,a[e+4>>0]|0,8)|0;d=d+c|0;ud(d|0,a[e+5>>0]|0,8)|0;d=d+c|0;ud(d|0,a[e+6>>0]|0,8)|0;ud(d+c|0,a[e+7>>0]|0,8)|0;return}function Ob(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;ud(b|0,a[e>>0]|0,16)|0;d=b+c|0;ud(d|0,a[e+1>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+2>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+3>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+4>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+5>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+6>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+7>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+8>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+9>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+10>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+11>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+12>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+13>>0]|0,16)|0;d=d+c|0;ud(d|0,a[e+14>>0]|0,16)|0;ud(d+c|0,a[e+15>>0]|0,16)|0;return}function Pb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0;h=d[e+-1>>0]|0;j=(d[f>>0]|0)-h+(d[e>>0]|0)|0;j=(j|0)>0?j:0;a[b>>0]=(j|0)<255?j:255;j=e+1|0;i=(d[f>>0]|0)-h+(d[j>>0]|0)|0;i=(i|0)>0?i:0;a[b+1>>0]=(i|0)<255?i:255;i=e+2|0;g=(d[f>>0]|0)-h+(d[i>>0]|0)|0;g=(g|0)>0?g:0;a[b+2>>0]=(g|0)<255?g:255;g=e+3|0;k=(d[f>>0]|0)-h+(d[g>>0]|0)|0;k=(k|0)>0?k:0;a[b+3>>0]=(k|0)<255?k:255;b=b+c|0;k=f+1|0;l=(d[k>>0]|0)-h+(d[e>>0]|0)|0;l=(l|0)>0?l:0;a[b>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[j>>0]|0)|0;l=(l|0)>0?l:0;a[b+1>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[b+2>>0]=(l|0)<255?l:255;k=(d[k>>0]|0)-h+(d[g>>0]|0)|0;k=(k|0)>0?k:0;a[b+3>>0]=(k|0)<255?k:255;b=b+c|0;k=f+2|0;l=(d[k>>0]|0)-h+(d[e>>0]|0)|0;l=(l|0)>0?l:0;a[b>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[j>>0]|0)|0;l=(l|0)>0?l:0;a[b+1>>0]=(l|0)<255?l:255;l=(d[k>>0]|0)-h+(d[i>>0]|0)|0;l=(l|0)>0?l:0;a[b+2>>0]=(l|0)<255?l:255;k=(d[k>>0]|0)-h+(d[g>>0]|0)|0;k=(k|0)>0?k:0;a[b+3>>0]=(k|0)<255?k:255;c=b+c|0;f=f+3|0;e=(d[f>>0]|0)-h+(d[e>>0]|0)|0;e=(e|0)>0?e:0;a[c>>0]=(e|0)<255?e:255;e=(d[f>>0]|0)-h+(d[j>>0]|0)|0;e=(e|0)>0?e:0;a[c+1>>0]=(e|0)<255?e:255;e=(d[f>>0]|0)-h+(d[i>>0]|0)|0;e=(e|0)>0?e:0;a[c+2>>0]=(e|0)<255?e:255;f=(d[f>>0]|0)-h+(d[g>>0]|0)|0;f=(f|0)>0?f:0;a[c+3>>0]=(f|0)<255?f:255;return}function Qb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;l=d[e+-1>>0]|0;m=e+1|0;n=e+2|0;o=e+3|0;h=e+4|0;i=e+5|0;j=e+6|0;k=e+7|0;g=0;while(1){p=f+g|0;q=(d[p>>0]|0)-l+(d[e>>0]|0)|0;q=(q|0)>0?q:0;a[b>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[m>>0]|0)|0;q=(q|0)>0?q:0;a[b+1>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[n>>0]|0)|0;q=(q|0)>0?q:0;a[b+2>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[o>>0]|0)|0;q=(q|0)>0?q:0;a[b+3>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[h>>0]|0)|0;q=(q|0)>0?q:0;a[b+4>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[i>>0]|0)|0;q=(q|0)>0?q:0;a[b+5>>0]=(q|0)<255?q:255;q=(d[p>>0]|0)-l+(d[j>>0]|0)|0;q=(q|0)>0?q:0;a[b+6>>0]=(q|0)<255?q:255;p=(d[p>>0]|0)-l+(d[k>>0]|0)|0;p=(p|0)>0?p:0;a[b+7>>0]=(p|0)<255?p:255;g=g+1|0;if((g|0)==8)break;else b=b+c|0}return}function Rb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;t=d[e+-1>>0]|0;u=e+1|0;v=e+2|0;w=e+3|0;h=e+4|0;i=e+5|0;j=e+6|0;k=e+7|0;l=e+8|0;m=e+9|0;n=e+10|0;o=e+11|0;p=e+12|0;q=e+13|0;r=e+14|0;s=e+15|0;g=0;while(1){x=f+g|0;y=(d[x>>0]|0)-t+(d[e>>0]|0)|0;y=(y|0)>0?y:0;a[b>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[u>>0]|0)|0;y=(y|0)>0?y:0;a[b+1>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[v>>0]|0)|0;y=(y|0)>0?y:0;a[b+2>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[w>>0]|0)|0;y=(y|0)>0?y:0;a[b+3>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[h>>0]|0)|0;y=(y|0)>0?y:0;a[b+4>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[i>>0]|0)|0;y=(y|0)>0?y:0;a[b+5>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[j>>0]|0)|0;y=(y|0)>0?y:0;a[b+6>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[k>>0]|0)|0;y=(y|0)>0?y:0;a[b+7>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[l>>0]|0)|0;y=(y|0)>0?y:0;a[b+8>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[m>>0]|0)|0;y=(y|0)>0?y:0;a[b+9>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[n>>0]|0)|0;y=(y|0)>0?y:0;a[b+10>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[o>>0]|0)|0;y=(y|0)>0?y:0;a[b+11>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[p>>0]|0)|0;y=(y|0)>0?y:0;a[b+12>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[q>>0]|0)|0;y=(y|0)>0?y:0;a[b+13>>0]=(y|0)<255?y:255;y=(d[x>>0]|0)-t+(d[r>>0]|0)|0;y=(y|0)>0?y:0;a[b+14>>0]=(y|0)<255?y:255;x=(d[x>>0]|0)-t+(d[s>>0]|0)|0;x=(x|0)>0?x:0;a[b+15>>0]=(x|0)<255?x:255;g=g+1|0;if((g|0)==16)break;else b=b+c|0}return}function Sb(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;e=b;d=e;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+4|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;e=b+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;b=d;a[b>>0]=-2139062144;a[b+1>>0]=-2139062144>>8;a[b+2>>0]=-2139062144>>16;a[b+3>>0]=-2139062144>>24;d=d+4|0;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+c|0;d=e;a[d>>0]=-2139062144;a[d+1>>0]=-2139062144>>8;a[d+2>>0]=-2139062144>>16;a[d+3>>0]=-2139062144>>24;e=e+4|0;a[e>>0]=-2139062144;a[e+1>>0]=-2139062144>>8;a[e+2>>0]=-2139062144>>16;a[e+3>>0]=-2139062144>>24;return}function Tb(b,c,d,e){b=b|0;c=c|0;d=d|0;e=e|0;var f=0;e=b;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=b+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));d=d+c|0;e=d;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));e=d+c|0;f=e+16|0;do{a[e>>0]=128;e=e+1|0}while((e|0)<(f|0));return}function Ub(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[e>>0]|0)+(d[e+1>>0]|0)+(d[e+2>>0]|0)+(d[e+3>>0]|0)+(d[e+4>>0]|0)+(d[e+5>>0]|0)+(d[e+6>>0]|0)+(d[e+7>>0]|0)+4|0)>>>3&255;ud(a|0,e|0,8)|0;c=a+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;ud(c+b|0,e|0,8)|0;return}function Vb(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[e>>0]|0)+(d[e+1>>0]|0)+(d[e+2>>0]|0)+(d[e+3>>0]|0)+(d[e+4>>0]|0)+(d[e+5>>0]|0)+(d[e+6>>0]|0)+(d[e+7>>0]|0)+(d[e+8>>0]|0)+(d[e+9>>0]|0)+(d[e+10>>0]|0)+(d[e+11>>0]|0)+(d[e+12>>0]|0)+(d[e+13>>0]|0)+(d[e+14>>0]|0)+(d[e+15>>0]|0)+8|0)>>>4&255;ud(a|0,e|0,16)|0;c=a+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;ud(c+b|0,e|0,16)|0;return}
function Wb(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[c>>0]|0)+(d[c+1>>0]|0)+(d[c+2>>0]|0)+(d[c+3>>0]|0)+(d[c+4>>0]|0)+(d[c+5>>0]|0)+(d[c+6>>0]|0)+(d[c+7>>0]|0)+4|0)>>>3&255;ud(a|0,e|0,8)|0;c=a+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;ud(c+b|0,e|0,8)|0;return}function Xb(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=((d[c>>0]|0)+(d[c+1>>0]|0)+(d[c+2>>0]|0)+(d[c+3>>0]|0)+(d[c+4>>0]|0)+(d[c+5>>0]|0)+(d[c+6>>0]|0)+(d[c+7>>0]|0)+(d[c+8>>0]|0)+(d[c+9>>0]|0)+(d[c+10>>0]|0)+(d[c+11>>0]|0)+(d[c+12>>0]|0)+(d[c+13>>0]|0)+(d[c+14>>0]|0)+(d[c+15>>0]|0)+8|0)>>>4&255;ud(a|0,e|0,16)|0;c=a+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;ud(c+b|0,e|0,16)|0;return}function Yb(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+4|0)/8|0)&255;ud(a|0,e|0,4)|0;c=a+b|0;ud(c|0,e|0,4)|0;c=c+b|0;ud(c|0,e|0,4)|0;ud(c+b|0,e|0,4)|0;return}function Zb(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+(d[c+4>>0]|0)+(d[e+4>>0]|0)+(d[c+5>>0]|0)+(d[e+5>>0]|0)+(d[c+6>>0]|0)+(d[e+6>>0]|0)+(d[c+7>>0]|0)+(d[e+7>>0]|0)+8|0)/16|0)&255;ud(a|0,e|0,8)|0;c=a+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;c=c+b|0;ud(c|0,e|0,8)|0;ud(c+b|0,e|0,8)|0;return}function _b(a,b,c,e){a=a|0;b=b|0;c=c|0;e=e|0;e=(((d[c>>0]|0)+(d[e>>0]|0)+(d[c+1>>0]|0)+(d[e+1>>0]|0)+(d[c+2>>0]|0)+(d[e+2>>0]|0)+(d[c+3>>0]|0)+(d[e+3>>0]|0)+(d[c+4>>0]|0)+(d[e+4>>0]|0)+(d[c+5>>0]|0)+(d[e+5>>0]|0)+(d[c+6>>0]|0)+(d[e+6>>0]|0)+(d[c+7>>0]|0)+(d[e+7>>0]|0)+(d[c+8>>0]|0)+(d[e+8>>0]|0)+(d[c+9>>0]|0)+(d[e+9>>0]|0)+(d[c+10>>0]|0)+(d[e+10>>0]|0)+(d[c+11>>0]|0)+(d[e+11>>0]|0)+(d[c+12>>0]|0)+(d[e+12>>0]|0)+(d[c+13>>0]|0)+(d[e+13>>0]|0)+(d[c+14>>0]|0)+(d[e+14>>0]|0)+(d[c+15>>0]|0)+(d[e+15>>0]|0)+16|0)/32|0)&255;ud(a|0,e|0,16)|0;c=a+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;c=c+b|0;ud(c|0,e|0,16)|0;ud(c+b|0,e|0,16)|0;return}function $b(a,d){a=a|0;d=d|0;var e=0;e=c[a+4>>2]|0;a=c[a>>2]|0;b[e>>1]=(b[d>>1]|0)*(b[a>>1]|0);b[e+2>>1]=(b[d+2>>1]|0)*(b[a+2>>1]|0);b[e+4>>1]=(b[d+4>>1]|0)*(b[a+4>>1]|0);b[e+6>>1]=(b[d+6>>1]|0)*(b[a+6>>1]|0);b[e+8>>1]=(b[d+8>>1]|0)*(b[a+8>>1]|0);b[e+10>>1]=(b[d+10>>1]|0)*(b[a+10>>1]|0);b[e+12>>1]=(b[d+12>>1]|0)*(b[a+12>>1]|0);b[e+14>>1]=(b[d+14>>1]|0)*(b[a+14>>1]|0);b[e+16>>1]=(b[d+16>>1]|0)*(b[a+16>>1]|0);b[e+18>>1]=(b[d+18>>1]|0)*(b[a+18>>1]|0);b[e+20>>1]=(b[d+20>>1]|0)*(b[a+20>>1]|0);b[e+22>>1]=(b[d+22>>1]|0)*(b[a+22>>1]|0);b[e+24>>1]=(b[d+24>>1]|0)*(b[a+24>>1]|0);b[e+26>>1]=(b[d+26>>1]|0)*(b[a+26>>1]|0);b[e+28>>1]=(b[d+28>>1]|0)*(b[a+28>>1]|0);b[e+30>>1]=(b[d+30>>1]|0)*(b[a+30>>1]|0);return}function ac(a,c,d,e){a=a|0;c=c|0;d=d|0;e=e|0;var f=0;b[a>>1]=(b[a>>1]|0)*(b[c>>1]|0);f=a+2|0;b[f>>1]=(b[f>>1]|0)*(b[c+2>>1]|0);f=a+4|0;b[f>>1]=(b[f>>1]|0)*(b[c+4>>1]|0);f=a+6|0;b[f>>1]=(b[f>>1]|0)*(b[c+6>>1]|0);f=a+8|0;b[f>>1]=(b[f>>1]|0)*(b[c+8>>1]|0);f=a+10|0;b[f>>1]=(b[f>>1]|0)*(b[c+10>>1]|0);f=a+12|0;b[f>>1]=(b[f>>1]|0)*(b[c+12>>1]|0);f=a+14|0;b[f>>1]=(b[f>>1]|0)*(b[c+14>>1]|0);f=a+16|0;b[f>>1]=(b[f>>1]|0)*(b[c+16>>1]|0);f=a+18|0;b[f>>1]=(b[f>>1]|0)*(b[c+18>>1]|0);f=a+20|0;b[f>>1]=(b[f>>1]|0)*(b[c+20>>1]|0);f=a+22|0;b[f>>1]=(b[f>>1]|0)*(b[c+22>>1]|0);f=a+24|0;b[f>>1]=(b[f>>1]|0)*(b[c+24>>1]|0);f=a+26|0;b[f>>1]=(b[f>>1]|0)*(b[c+26>>1]|0);f=a+28|0;b[f>>1]=(b[f>>1]|0)*(b[c+28>>1]|0);f=a+30|0;b[f>>1]=(b[f>>1]|0)*(b[c+30>>1]|0);nc(a,d,e,d,e);c=a+32|0;do{b[a>>1]=0;a=a+2|0}while((a|0)<(c|0));return}function bc(a){a=a|0;td(a+7085|0,2416,1056)|0;return}function cc(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0;g=b+16|0;i=d+((c[g>>2]|0)*14|0)|0;b=b+36|0;h=(c[b>>2]|0)*6|0;d=e+h|0;h=f+h|0;j=i+-1|0;e=d+-1|0;f=h+-1|0;a[i>>0]=a[j>>0]|0;a[d>>0]=a[e>>0]|0;a[h>>0]=a[f>>0]|0;a[i+1>>0]=a[j>>0]|0;a[d+1>>0]=a[e>>0]|0;a[h+1>>0]=a[f>>0]|0;a[i+2>>0]=a[j>>0]|0;a[d+2>>0]=a[e>>0]|0;a[h+2>>0]=a[f>>0]|0;a[i+3>>0]=a[j>>0]|0;a[d+3>>0]=a[e>>0]|0;a[h+3>>0]=a[f>>0]|0;g=i+(c[g>>2]|0)|0;f=c[b>>2]|0;d=d+f|0;f=h+f|0;h=g+-1|0;b=d+-1|0;e=f+-1|0;a[g>>0]=a[h>>0]|0;a[d>>0]=a[b>>0]|0;a[f>>0]=a[e>>0]|0;a[g+1>>0]=a[h>>0]|0;a[d+1>>0]=a[b>>0]|0;a[f+1>>0]=a[e>>0]|0;a[g+2>>0]=a[h>>0]|0;a[d+2>>0]=a[b>>0]|0;a[f+2>>0]=a[e>>0]|0;a[g+3>>0]=a[h>>0]|0;a[d+3>>0]=a[b>>0]|0;a[f+3>>0]=a[e>>0]|0;return}function dc(e,f,g,h,i,j){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;s=L;L=L+144|0;r=s;m=b[3600+(g*12|0)>>1]|0;n=b[3600+(g*12|0)+2>>1]|0;o=b[3600+(g*12|0)+4>>1]|0;p=b[3600+(g*12|0)+6>>1]|0;q=b[3600+(g*12|0)+8>>1]|0;l=b[3600+(g*12|0)+10>>1]|0;k=0;g=e+(0-(f<<1))|0;e=r;while(1){t=d[g+-1>>0]|0;y=d[g>>0]|0;w=d[g+1>>0]|0;x=d[g+2>>0]|0;v=d[g+3>>0]|0;u=(t*n|0)+((d[g+-2>>0]|0)*m|0)+(y*o|0)+(w*p|0)+(x*q|0)+(v*l|0)|0;c[e>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;u=d[g+4>>0]|0;t=(y*n|0)+(t*m|0)+(w*o|0)+(x*p|0)+(v*q|0)+(u*l|0)|0;c[e+4>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;t=d[g+5>>0]|0;y=(w*n|0)+(y*m|0)+(x*o|0)+(v*p|0)+(u*q|0)+(t*l|0)|0;c[e+8>>2]=(y|0)<-64?0:(y|0)>32703?255:y+64>>7;t=(x*n|0)+(w*m|0)+(v*o|0)+(u*p|0)+(t*q|0)+((d[g+6>>0]|0)*l|0)|0;c[e+12>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;k=k+1|0;if((k|0)==9)break;else{g=g+f|0;e=e+16|0}}l=b[3600+(h*12|0)>>1]|0;f=b[3600+(h*12|0)+2>>1]|0;m=b[3600+(h*12|0)+4>>1]|0;n=b[3600+(h*12|0)+6>>1]|0;o=b[3600+(h*12|0)+8>>1]|0;k=b[3600+(h*12|0)+10>>1]|0;e=0;g=r+32|0;while(1){y=g;g=g+16|0;x=((c[y+-16>>2]|0)*f|0)+((c[y+-32>>2]|0)*l|0)+((c[y>>2]|0)*m|0)+((c[g>>2]|0)*n|0)+((c[y+32>>2]|0)*o|0)+((c[y+48>>2]|0)*k|0)|0;a[i>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-12>>2]|0)*f|0)+((c[y+-28>>2]|0)*l|0)+((c[y+4>>2]|0)*m|0)+((c[y+20>>2]|0)*n|0)+((c[y+36>>2]|0)*o|0)+((c[y+52>>2]|0)*k|0)|0;a[i+1>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-8>>2]|0)*f|0)+((c[y+-24>>2]|0)*l|0)+((c[y+8>>2]|0)*m|0)+((c[y+24>>2]|0)*n|0)+((c[y+40>>2]|0)*o|0)+((c[y+56>>2]|0)*k|0)|0;a[i+2>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;y=((c[y+-4>>2]|0)*f|0)+((c[y+-20>>2]|0)*l|0)+((c[y+12>>2]|0)*m|0)+((c[y+28>>2]|0)*n|0)+((c[y+44>>2]|0)*o|0)+((c[y+60>>2]|0)*k|0)|0;a[i+3>>0]=(y|0)<-64?0:(y|0)>32703?-1:(y+64|0)>>>7&255;e=e+1|0;if((e|0)==4)break;else i=i+j|0}L=s;return}function ec(e,f,g,h,i,j){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;s=L;L=L+832|0;r=s;m=b[3600+(g*12|0)>>1]|0;n=b[3600+(g*12|0)+2>>1]|0;o=b[3600+(g*12|0)+4>>1]|0;p=b[3600+(g*12|0)+6>>1]|0;q=b[3600+(g*12|0)+8>>1]|0;l=b[3600+(g*12|0)+10>>1]|0;k=0;g=e+(0-(f<<1))|0;e=r;while(1){t=d[g+-1>>0]|0;u=d[g>>0]|0;y=d[g+1>>0]|0;w=d[g+2>>0]|0;x=d[g+3>>0]|0;v=(t*n|0)+((d[g+-2>>0]|0)*m|0)+(u*o|0)+(y*p|0)+(w*q|0)+(x*l|0)|0;c[e>>2]=(v|0)<-64?0:(v|0)>32703?255:v+64>>7;v=d[g+4>>0]|0;t=(u*n|0)+(t*m|0)+(y*o|0)+(w*p|0)+(x*q|0)+(v*l|0)|0;c[e+4>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;t=d[g+5>>0]|0;u=(y*n|0)+(u*m|0)+(w*o|0)+(x*p|0)+(v*q|0)+(t*l|0)|0;c[e+8>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;u=d[g+6>>0]|0;y=(w*n|0)+(y*m|0)+(x*o|0)+(v*p|0)+(t*q|0)+(u*l|0)|0;c[e+12>>2]=(y|0)<-64?0:(y|0)>32703?255:y+64>>7;u=(x*n|0)+(w*m|0)+(v*o|0)+(t*p|0)+(u*q|0)+((d[g+7>>0]|0)*l|0)|0;c[e+16>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;u=d[g+6>>0]|0;t=((d[g+4>>0]|0)*n|0)+((d[g+3>>0]|0)*m|0)+(t*o|0)+(u*p|0)+((d[g+7>>0]|0)*q|0)+((d[g+8>>0]|0)*l|0)|0;c[e+20>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;t=g+7|0;u=((d[g+5>>0]|0)*n|0)+((d[g+4>>0]|0)*m|0)+(u*o|0)+((d[t>>0]|0)*p|0)+((d[g+8>>0]|0)*q|0)+((d[g+9>>0]|0)*l|0)|0;c[e+24>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;t=((d[g+6>>0]|0)*n|0)+((d[g+5>>0]|0)*m|0)+((d[t>>0]|0)*o|0)+((d[g+8>>0]|0)*p|0)+((d[g+9>>0]|0)*q|0)+((d[g+10>>0]|0)*l|0)|0;c[e+28>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;k=k+1|0;if((k|0)==13)break;else{g=g+f|0;e=e+32|0}}l=b[3600+(h*12|0)>>1]|0;f=b[3600+(h*12|0)+2>>1]|0;m=b[3600+(h*12|0)+4>>1]|0;n=b[3600+(h*12|0)+6>>1]|0;o=b[3600+(h*12|0)+8>>1]|0;k=b[3600+(h*12|0)+10>>1]|0;e=0;g=r+64|0;while(1){y=g;g=g+32|0;x=((c[y+-32>>2]|0)*f|0)+((c[y+-64>>2]|0)*l|0)+((c[y>>2]|0)*m|0)+((c[g>>2]|0)*n|0)+((c[y+64>>2]|0)*o|0)+((c[y+96>>2]|0)*k|0)|0;a[i>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-28>>2]|0)*f|0)+((c[y+-60>>2]|0)*l|0)+((c[y+4>>2]|0)*m|0)+((c[y+36>>2]|0)*n|0)+((c[y+68>>2]|0)*o|0)+((c[y+100>>2]|0)*k|0)|0;a[i+1>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-24>>2]|0)*f|0)+((c[y+-56>>2]|0)*l|0)+((c[y+8>>2]|0)*m|0)+((c[y+40>>2]|0)*n|0)+((c[y+72>>2]|0)*o|0)+((c[y+104>>2]|0)*k|0)|0;a[i+2>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-20>>2]|0)*f|0)+((c[y+-52>>2]|0)*l|0)+((c[y+12>>2]|0)*m|0)+((c[y+44>>2]|0)*n|0)+((c[y+76>>2]|0)*o|0)+((c[y+108>>2]|0)*k|0)|0;a[i+3>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-16>>2]|0)*f|0)+((c[y+-48>>2]|0)*l|0)+((c[y+16>>2]|0)*m|0)+((c[y+48>>2]|0)*n|0)+((c[y+80>>2]|0)*o|0)+((c[y+112>>2]|0)*k|0)|0;a[i+4>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-12>>2]|0)*f|0)+((c[y+-44>>2]|0)*l|0)+((c[y+20>>2]|0)*m|0)+((c[y+52>>2]|0)*n|0)+((c[y+84>>2]|0)*o|0)+((c[y+116>>2]|0)*k|0)|0;a[i+5>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-8>>2]|0)*f|0)+((c[y+-40>>2]|0)*l|0)+((c[y+24>>2]|0)*m|0)+((c[y+56>>2]|0)*n|0)+((c[y+88>>2]|0)*o|0)+((c[y+120>>2]|0)*k|0)|0;a[i+6>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;y=((c[y+-4>>2]|0)*f|0)+((c[y+-36>>2]|0)*l|0)+((c[y+28>>2]|0)*m|0)+((c[y+60>>2]|0)*n|0)+((c[y+92>>2]|0)*o|0)+((c[y+124>>2]|0)*k|0)|0;a[i+7>>0]=(y|0)<-64?0:(y|0)>32703?-1:(y+64|0)>>>7&255;e=e+1|0;if((e|0)==8)break;else i=i+j|0}L=s;return}function fc(e,f,g,h,i,j){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;s=L;L=L+832|0;r=s;m=b[3600+(g*12|0)>>1]|0;n=b[3600+(g*12|0)+2>>1]|0;o=b[3600+(g*12|0)+4>>1]|0;p=b[3600+(g*12|0)+6>>1]|0;q=b[3600+(g*12|0)+8>>1]|0;l=b[3600+(g*12|0)+10>>1]|0;k=0;g=e+(0-(f<<1))|0;e=r;while(1){t=d[g+-1>>0]|0;u=d[g>>0]|0;y=d[g+1>>0]|0;w=d[g+2>>0]|0;x=d[g+3>>0]|0;v=(t*n|0)+((d[g+-2>>0]|0)*m|0)+(u*o|0)+(y*p|0)+(w*q|0)+(x*l|0)|0;c[e>>2]=(v|0)<-64?0:(v|0)>32703?255:v+64>>7;v=d[g+4>>0]|0;t=(u*n|0)+(t*m|0)+(y*o|0)+(w*p|0)+(x*q|0)+(v*l|0)|0;c[e+4>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;t=d[g+5>>0]|0;u=(y*n|0)+(u*m|0)+(w*o|0)+(x*p|0)+(v*q|0)+(t*l|0)|0;c[e+8>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;u=d[g+6>>0]|0;y=(w*n|0)+(y*m|0)+(x*o|0)+(v*p|0)+(t*q|0)+(u*l|0)|0;c[e+12>>2]=(y|0)<-64?0:(y|0)>32703?255:y+64>>7;u=(x*n|0)+(w*m|0)+(v*o|0)+(t*p|0)+(u*q|0)+((d[g+7>>0]|0)*l|0)|0;c[e+16>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;u=d[g+6>>0]|0;t=((d[g+4>>0]|0)*n|0)+((d[g+3>>0]|0)*m|0)+(t*o|0)+(u*p|0)+((d[g+7>>0]|0)*q|0)+((d[g+8>>0]|0)*l|0)|0;c[e+20>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;t=g+7|0;u=((d[g+5>>0]|0)*n|0)+((d[g+4>>0]|0)*m|0)+(u*o|0)+((d[t>>0]|0)*p|0)+((d[g+8>>0]|0)*q|0)+((d[g+9>>0]|0)*l|0)|0;c[e+24>>2]=(u|0)<-64?0:(u|0)>32703?255:u+64>>7;t=((d[g+6>>0]|0)*n|0)+((d[g+5>>0]|0)*m|0)+((d[t>>0]|0)*o|0)+((d[g+8>>0]|0)*p|0)+((d[g+9>>0]|0)*q|0)+((d[g+10>>0]|0)*l|0)|0;c[e+28>>2]=(t|0)<-64?0:(t|0)>32703?255:t+64>>7;k=k+1|0;if((k|0)==9)break;else{g=g+f|0;e=e+32|0}}l=b[3600+(h*12|0)>>1]|0;f=b[3600+(h*12|0)+2>>1]|0;m=b[3600+(h*12|0)+4>>1]|0;n=b[3600+(h*12|0)+6>>1]|0;o=b[3600+(h*12|0)+8>>1]|0;k=b[3600+(h*12|0)+10>>1]|0;e=0;g=r+64|0;while(1){y=g;g=g+32|0;x=((c[y+-32>>2]|0)*f|0)+((c[y+-64>>2]|0)*l|0)+((c[y>>2]|0)*m|0)+((c[g>>2]|0)*n|0)+((c[y+64>>2]|0)*o|0)+((c[y+96>>2]|0)*k|0)|0;a[i>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-28>>2]|0)*f|0)+((c[y+-60>>2]|0)*l|0)+((c[y+4>>2]|0)*m|0)+((c[y+36>>2]|0)*n|0)+((c[y+68>>2]|0)*o|0)+((c[y+100>>2]|0)*k|0)|0;a[i+1>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-24>>2]|0)*f|0)+((c[y+-56>>2]|0)*l|0)+((c[y+8>>2]|0)*m|0)+((c[y+40>>2]|0)*n|0)+((c[y+72>>2]|0)*o|0)+((c[y+104>>2]|0)*k|0)|0;a[i+2>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-20>>2]|0)*f|0)+((c[y+-52>>2]|0)*l|0)+((c[y+12>>2]|0)*m|0)+((c[y+44>>2]|0)*n|0)+((c[y+76>>2]|0)*o|0)+((c[y+108>>2]|0)*k|0)|0;a[i+3>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-16>>2]|0)*f|0)+((c[y+-48>>2]|0)*l|0)+((c[y+16>>2]|0)*m|0)+((c[y+48>>2]|0)*n|0)+((c[y+80>>2]|0)*o|0)+((c[y+112>>2]|0)*k|0)|0;a[i+4>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-12>>2]|0)*f|0)+((c[y+-44>>2]|0)*l|0)+((c[y+20>>2]|0)*m|0)+((c[y+52>>2]|0)*n|0)+((c[y+84>>2]|0)*o|0)+((c[y+116>>2]|0)*k|0)|0;a[i+5>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;x=((c[y+-8>>2]|0)*f|0)+((c[y+-40>>2]|0)*l|0)+((c[y+24>>2]|0)*m|0)+((c[y+56>>2]|0)*n|0)+((c[y+88>>2]|0)*o|0)+((c[y+120>>2]|0)*k|0)|0;a[i+6>>0]=(x|0)<-64?0:(x|0)>32703?-1:(x+64|0)>>>7&255;y=((c[y+-4>>2]|0)*f|0)+((c[y+-36>>2]|0)*l|0)+((c[y+28>>2]|0)*m|0)+((c[y+60>>2]|0)*n|0)+((c[y+92>>2]|0)*o|0)+((c[y+124>>2]|0)*k|0)|0;a[i+7>>0]=(y|0)<-64?0:(y|0)>32703?-1:(y+64|0)>>>7&255;e=e+1|0;if((e|0)==4)break;else i=i+j|0}L=s;return}function gc(e,f,g,h,i,j){e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;u=L;L=L+2016|0;t=u;o=b[3600+(g*12|0)>>1]|0;p=b[3600+(g*12|0)+2>>1]|0;q=b[3600+(g*12|0)+4>>1]|0;r=b[3600+(g*12|0)+6>>1]|0;s=b[3600+(g*12|0)+8>>1]|0;n=b[3600+(g*12|0)+10>>1]|0;m=0;e=e+(0-(f<<1))|0;k=t;while(1){g=0;l=e;do{v=l;l=l+1|0;v=((d[v+-1>>0]|0)*p|0)+((d[v+-2>>0]|0)*o|0)+((d[v>>0]|0)*q|0)+((d[l>>0]|0)*r|0)+((d[v+2>>0]|0)*s|0)+((d[v+3>>0]|0)*n|0)|0;c[k+(g<<2)>>2]=(v|0)<-64?0:(v|0)>32703?255:v+64>>7;g=g+1|0}while((g|0)!=16);m=m+1|0;if((m|0)==21)break;else{e=e+f|0;k=k+64|0}}n=b[3600+(h*12|0)>>1]|0;f=b[3600+(h*12|0)+2>>1]|0;o=b[3600+(h*12|0)+4>>1]|0;p=b[3600+(h*12|0)+6>>1]|0;q=b[3600+(h*12|0)+8>>1]|0;m=b[3600+(h*12|0)+10>>1]|0;l=0;k=t+128|0;while(1){g=0;e=k;while(1){v=((c[e+-64>>2]|0)*f|0)+((c[e+-128>>2]|0)*n|0)+((c[e>>2]|0)*o|0)+((c[e+64>>2]|0)*p|0)+((c[e+128>>2]|0)*q|0)+((c[e+192>>2]|0)*m|0)|0;a[i+g>>0]=(v|0)<-64?0:(v|0)>32703?-1:(v+64|0)>>>7&255;g=g+1|0;if((g|0)==16)break;else e=e+4|0}l=l+1|0;if((l|0)==16)break;else{k=k+64|0;i=i+j|0}}L=u;return}function hc(c,f,g,h,i,j){c=c|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;k=L;L=L+544|0;l=k;m=b[3568+(g<<2)>>1]|0;n=b[3568+(g<<2)+2>>1]|0;s=d[c+1>>0]|0;b[l>>1]=((m*(d[c>>0]|0)|0)+64+(n*s|0)|0)>>>7;o=d[c+2>>0]|0;q=l+2|0;b[q>>1]=((m*s|0)+64+(n*o|0)|0)>>>7;s=d[c+3>>0]|0;b[l+4>>1]=((m*o|0)+64+(n*s|0)|0)>>>7;b[l+6>>1]=((m*s|0)+64+(n*(d[c+4>>0]|0)|0)|0)>>>7;s=c+f|0;o=l+8|0;r=s+1|0;c=d[r>>0]|0;b[o>>1]=((m*(d[s>>0]|0)|0)+64+(n*c|0)|0)>>>7;r=r+1|0;g=d[r>>0]|0;b[l+10>>1]=((m*c|0)+64+(n*g|0)|0)>>>7;r=r+1|0;c=d[r>>0]|0;b[l+12>>1]=((m*g|0)+64+(n*c|0)|0)>>>7;b[l+14>>1]=((m*c|0)+64+(n*(d[r+1>>0]|0)|0)|0)>>>7;s=s+f|0;r=l+16|0;c=s+1|0;g=d[c>>0]|0;b[r>>1]=((m*(d[s>>0]|0)|0)+64+(n*g|0)|0)>>>7;c=c+1|0;p=d[c>>0]|0;b[l+18>>1]=((m*g|0)+64+(n*p|0)|0)>>>7;c=c+1|0;g=d[c>>0]|0;b[l+20>>1]=((m*p|0)+64+(n*g|0)|0)>>>7;b[l+22>>1]=((m*g|0)+64+(n*(d[c+1>>0]|0)|0)|0)>>>7;s=s+f|0;c=l+24|0;g=s+1|0;p=d[g>>0]|0;b[c>>1]=((m*(d[s>>0]|0)|0)+64+(n*p|0)|0)>>>7;g=g+1|0;t=d[g>>0]|0;b[l+26>>1]=((m*p|0)+64+(n*t|0)|0)>>>7;g=g+1|0;p=d[g>>0]|0;b[l+28>>1]=((m*t|0)+64+(n*p|0)|0)>>>7;b[l+30>>1]=((m*p|0)+64+(n*(d[g+1>>0]|0)|0)|0)>>>7;s=s+f|0;f=l+32|0;g=s+1|0;p=d[g>>0]|0;b[f>>1]=((m*(d[s>>0]|0)|0)+64+(n*p|0)|0)>>>7;g=g+1|0;s=d[g>>0]|0;b[l+34>>1]=((m*p|0)+64+(n*s|0)|0)>>>7;g=g+1|0;p=d[g>>0]|0;b[l+36>>1]=((m*s|0)+64+(n*p|0)|0)>>>7;b[l+38>>1]=((m*p|0)+64+(n*(d[g+1>>0]|0)|0)|0)>>>7;g=b[3568+(h<<2)>>1]|0;h=b[3568+(h<<2)+2>>1]|0;o=e[o>>1]|0;a[i>>0]=((g*(e[l>>1]|0)|0)+64+(h*o|0)|0)>>>7;n=e[l+10>>1]|0;a[i+1>>0]=((g*(e[q>>1]|0)|0)+64+(h*n|0)|0)>>>7;q=e[l+12>>1]|0;a[i+2>>0]=((g*(e[l+4>>1]|0)|0)+64+(h*q|0)|0)>>>7;p=e[l+14>>1]|0;a[i+3>>0]=((g*(e[l+6>>1]|0)|0)+64+(h*p|0)|0)>>>7;m=i+j|0;i=e[r>>1]|0;a[m>>0]=((g*o|0)+64+(h*i|0)|0)>>>7;o=e[l+18>>1]|0;a[m+1>>0]=((g*n|0)+64+(h*o|0)|0)>>>7;n=e[l+20>>1]|0;a[m+2>>0]=((g*q|0)+64+(h*n|0)|0)>>>7;a[m+3>>0]=((g*p|0)+64+(h*(e[l+22>>1]|0)|0)|0)>>>7;m=m+j|0;c=e[c>>1]|0;a[m>>0]=((g*i|0)+64+(h*c|0)|0)>>>7;i=e[l+26>>1]|0;a[m+1>>0]=((g*o|0)+64+(h*i|0)|0)>>>7;a[m+2>>0]=((g*n|0)+64+(h*(e[l+28>>1]|0)|0)|0)>>>7;a[m+3>>0]=((g*(e[l+22>>1]|0)|0)+64+(h*(e[l+30>>1]|0)|0)|0)>>>7;j=m+j|0;a[j>>0]=((g*c|0)+64+(h*(e[f>>1]|0)|0)|0)>>>7;a[j+1>>0]=((g*i|0)+64+(h*(e[l+34>>1]|0)|0)|0)>>>7;a[j+2>>0]=((g*(e[l+28>>1]|0)|0)+64+(h*(e[l+36>>1]|0)|0)|0)>>>7;a[j+3>>0]=((g*(e[l+30>>1]|0)|0)+64+(h*(e[l+38>>1]|0)|0)|0)>>>7;L=k;return}function ic(c,f,g,h,i,j){c=c|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0;o=L;L=L+544|0;k=o;n=b[3568+(g<<2)>>1]|0;m=b[3568+(g<<2)+2>>1]|0;l=0;g=c;c=k;while(1){p=d[g+1>>0]|0;b[c>>1]=((n*(d[g>>0]|0)|0)+64+(m*p|0)|0)>>>7;q=d[g+2>>0]|0;b[c+2>>1]=((n*p|0)+64+(m*q|0)|0)>>>7;p=d[g+3>>0]|0;b[c+4>>1]=((n*q|0)+64+(m*p|0)|0)>>>7;q=d[g+4>>0]|0;b[c+6>>1]=((n*p|0)+64+(m*q|0)|0)>>>7;p=d[g+5>>0]|0;b[c+8>>1]=((n*q|0)+64+(m*p|0)|0)>>>7;q=d[g+6>>0]|0;b[c+10>>1]=((n*p|0)+64+(m*q|0)|0)>>>7;p=g+7|0;b[c+12>>1]=((n*q|0)+64+(m*(d[p>>0]|0)|0)|0)>>>7;b[c+14>>1]=((n*(d[p>>0]|0)|0)+64+(m*(d[g+8>>0]|0)|0)|0)>>>7;l=l+1|0;if((l|0)==9)break;else{g=g+f|0;c=c+16|0}}l=b[3568+(h<<2)>>1]|0;c=b[3568+(h<<2)+2>>1]|0;g=0;while(1){q=k;k=k+16|0;a[i>>0]=((l*(e[q>>1]|0)|0)+64+(c*(e[k>>1]|0)|0)|0)>>>7;a[i+1>>0]=((l*(e[q+2>>1]|0)|0)+64+(c*(e[q+18>>1]|0)|0)|0)>>>7;a[i+2>>0]=((l*(e[q+4>>1]|0)|0)+64+(c*(e[q+20>>1]|0)|0)|0)>>>7;a[i+3>>0]=((l*(e[q+6>>1]|0)|0)+64+(c*(e[q+22>>1]|0)|0)|0)>>>7;a[i+4>>0]=((l*(e[q+8>>1]|0)|0)+64+(c*(e[q+24>>1]|0)|0)|0)>>>7;a[i+5>>0]=((l*(e[q+10>>1]|0)|0)+64+(c*(e[q+26>>1]|0)|0)|0)>>>7;a[i+6>>0]=((l*(e[q+12>>1]|0)|0)+64+(c*(e[q+28>>1]|0)|0)|0)>>>7;a[i+7>>0]=((l*(e[q+14>>1]|0)|0)+64+(c*(e[q+30>>1]|0)|0)|0)>>>7;g=g+1|0;if((g|0)==8)break;else i=i+j|0}L=o;return}function jc(c,f,g,h,i,j){c=c|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0;o=L;L=L+544|0;n=o;m=b[3568+(g<<2)>>1]|0;l=b[3568+(g<<2)+2>>1]|0;k=0;g=c;c=n;while(1){p=d[g+1>>0]|0;b[c>>1]=((m*(d[g>>0]|0)|0)+64+(l*p|0)|0)>>>7;q=d[g+2>>0]|0;b[c+2>>1]=((m*p|0)+64+(l*q|0)|0)>>>7;p=d[g+3>>0]|0;b[c+4>>1]=((m*q|0)+64+(l*p|0)|0)>>>7;q=d[g+4>>0]|0;b[c+6>>1]=((m*p|0)+64+(l*q|0)|0)>>>7;p=d[g+5>>0]|0;b[c+8>>1]=((m*q|0)+64+(l*p|0)|0)>>>7;q=d[g+6>>0]|0;b[c+10>>1]=((m*p|0)+64+(l*q|0)|0)>>>7;p=g+7|0;b[c+12>>1]=((m*q|0)+64+(l*(d[p>>0]|0)|0)|0)>>>7;b[c+14>>1]=((m*(d[p>>0]|0)|0)+64+(l*(d[g+8>>0]|0)|0)|0)>>>7;k=k+1|0;if((k|0)==5)break;else{g=g+f|0;c=c+16|0}}m=b[3568+(h<<2)>>1]|0;p=b[3568+(h<<2)+2>>1]|0;f=n+16|0;a[i>>0]=((m*(e[n>>1]|0)|0)+64+(p*(e[f>>1]|0)|0)|0)>>>7;a[i+1>>0]=((m*(e[n+2>>1]|0)|0)+64+(p*(e[n+18>>1]|0)|0)|0)>>>7;a[i+2>>0]=((m*(e[n+4>>1]|0)|0)+64+(p*(e[n+20>>1]|0)|0)|0)>>>7;a[i+3>>0]=((m*(e[n+6>>1]|0)|0)+64+(p*(e[n+22>>1]|0)|0)|0)>>>7;a[i+4>>0]=((m*(e[n+8>>1]|0)|0)+64+(p*(e[n+24>>1]|0)|0)|0)>>>7;a[i+5>>0]=((m*(e[n+10>>1]|0)|0)+64+(p*(e[n+26>>1]|0)|0)|0)>>>7;a[i+6>>0]=((m*(e[n+12>>1]|0)|0)+64+(p*(e[n+28>>1]|0)|0)|0)>>>7;a[i+7>>0]=((m*(e[n+14>>1]|0)|0)+64+(p*(e[n+30>>1]|0)|0)|0)>>>7;q=i+j|0;h=n+32|0;a[q>>0]=((m*(e[f>>1]|0)|0)+64+(p*(e[h>>1]|0)|0)|0)>>>7;a[q+1>>0]=((m*(e[n+18>>1]|0)|0)+64+(p*(e[n+34>>1]|0)|0)|0)>>>7;a[q+2>>0]=((m*(e[n+20>>1]|0)|0)+64+(p*(e[n+36>>1]|0)|0)|0)>>>7;a[q+3>>0]=((m*(e[n+22>>1]|0)|0)+64+(p*(e[n+38>>1]|0)|0)|0)>>>7;a[q+4>>0]=((m*(e[n+24>>1]|0)|0)+64+(p*(e[n+40>>1]|0)|0)|0)>>>7;a[q+5>>0]=((m*(e[n+26>>1]|0)|0)+64+(p*(e[n+42>>1]|0)|0)|0)>>>7;a[q+6>>0]=((m*(e[n+28>>1]|0)|0)+64+(p*(e[n+44>>1]|0)|0)|0)>>>7;a[q+7>>0]=((m*(e[n+30>>1]|0)|0)+64+(p*(e[n+46>>1]|0)|0)|0)>>>7;q=q+j|0;i=n+48|0;a[q>>0]=((m*(e[h>>1]|0)|0)+64+(p*(e[i>>1]|0)|0)|0)>>>7;a[q+1>>0]=((m*(e[n+34>>1]|0)|0)+64+(p*(e[n+50>>1]|0)|0)|0)>>>7;a[q+2>>0]=((m*(e[n+36>>1]|0)|0)+64+(p*(e[n+52>>1]|0)|0)|0)>>>7;a[q+3>>0]=((m*(e[n+38>>1]|0)|0)+64+(p*(e[n+54>>1]|0)|0)|0)>>>7;a[q+4>>0]=((m*(e[n+40>>1]|0)|0)+64+(p*(e[n+56>>1]|0)|0)|0)>>>7;a[q+5>>0]=((m*(e[n+42>>1]|0)|0)+64+(p*(e[n+58>>1]|0)|0)|0)>>>7;a[q+6>>0]=((m*(e[n+44>>1]|0)|0)+64+(p*(e[n+60>>1]|0)|0)|0)>>>7;a[q+7>>0]=((m*(e[n+46>>1]|0)|0)+64+(p*(e[n+62>>1]|0)|0)|0)>>>7;q=q+j|0;a[q>>0]=((m*(e[i>>1]|0)|0)+64+(p*(e[n+64>>1]|0)|0)|0)>>>7;a[q+1>>0]=((m*(e[n+50>>1]|0)|0)+64+(p*(e[n+66>>1]|0)|0)|0)>>>7;a[q+2>>0]=((m*(e[n+52>>1]|0)|0)+64+(p*(e[n+68>>1]|0)|0)|0)>>>7;a[q+3>>0]=((m*(e[n+54>>1]|0)|0)+64+(p*(e[n+70>>1]|0)|0)|0)>>>7;a[q+4>>0]=((m*(e[n+56>>1]|0)|0)+64+(p*(e[n+72>>1]|0)|0)|0)>>>7;a[q+5>>0]=((m*(e[n+58>>1]|0)|0)+64+(p*(e[n+74>>1]|0)|0)|0)>>>7;a[q+6>>0]=((m*(e[n+60>>1]|0)|0)+64+(p*(e[n+76>>1]|0)|0)|0)>>>7;a[q+7>>0]=((m*(e[n+62>>1]|0)|0)+64+(p*(e[n+78>>1]|0)|0)|0)>>>7;L=o;return}function kc(c,f,g,h,i,j){c=c|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0;o=L;L=L+544|0;k=o;n=b[3568+(g<<2)>>1]|0;m=b[3568+(g<<2)+2>>1]|0;l=0;g=c;c=k;while(1){p=d[g+1>>0]|0;b[c>>1]=((n*(d[g>>0]|0)|0)+64+(m*p|0)|0)>>>7;q=d[g+2>>0]|0;b[c+2>>1]=((n*p|0)+64+(m*q|0)|0)>>>7;p=d[g+3>>0]|0;b[c+4>>1]=((n*q|0)+64+(m*p|0)|0)>>>7;q=d[g+4>>0]|0;b[c+6>>1]=((n*p|0)+64+(m*q|0)|0)>>>7;p=d[g+5>>0]|0;b[c+8>>1]=((n*q|0)+64+(m*p|0)|0)>>>7;q=d[g+6>>0]|0;b[c+10>>1]=((n*p|0)+64+(m*q|0)|0)>>>7;p=g+7|0;b[c+12>>1]=((n*q|0)+64+(m*(d[p>>0]|0)|0)|0)>>>7;q=g+8|0;b[c+14>>1]=((n*(d[p>>0]|0)|0)+64+(m*(d[q>>0]|0)|0)|0)>>>7;p=g+9|0;b[c+16>>1]=((n*(d[q>>0]|0)|0)+64+(m*(d[p>>0]|0)|0)|0)>>>7;q=g+10|0;b[c+18>>1]=((n*(d[p>>0]|0)|0)+64+(m*(d[q>>0]|0)|0)|0)>>>7;p=g+11|0;b[c+20>>1]=((n*(d[q>>0]|0)|0)+64+(m*(d[p>>0]|0)|0)|0)>>>7;q=g+12|0;b[c+22>>1]=((n*(d[p>>0]|0)|0)+64+(m*(d[q>>0]|0)|0)|0)>>>7;p=g+13|0;b[c+24>>1]=((n*(d[q>>0]|0)|0)+64+(m*(d[p>>0]|0)|0)|0)>>>7;q=g+14|0;b[c+26>>1]=((n*(d[p>>0]|0)|0)+64+(m*(d[q>>0]|0)|0)|0)>>>7;p=g+15|0;b[c+28>>1]=((n*(d[q>>0]|0)|0)+64+(m*(d[p>>0]|0)|0)|0)>>>7;b[c+30>>1]=((n*(d[p>>0]|0)|0)+64+(m*(d[g+16>>0]|0)|0)|0)>>>7;l=l+1|0;if((l|0)==17)break;else{g=g+f|0;c=c+32|0}}l=b[3568+(h<<2)>>1]|0;c=b[3568+(h<<2)+2>>1]|0;g=0;while(1){q=k;k=k+32|0;a[i>>0]=((l*(e[q>>1]|0)|0)+64+(c*(e[k>>1]|0)|0)|0)>>>7;a[i+1>>0]=((l*(e[q+2>>1]|0)|0)+64+(c*(e[q+34>>1]|0)|0)|0)>>>7;a[i+2>>0]=((l*(e[q+4>>1]|0)|0)+64+(c*(e[q+36>>1]|0)|0)|0)>>>7;a[i+3>>0]=((l*(e[q+6>>1]|0)|0)+64+(c*(e[q+38>>1]|0)|0)|0)>>>7;a[i+4>>0]=((l*(e[q+8>>1]|0)|0)+64+(c*(e[q+40>>1]|0)|0)|0)>>>7;a[i+5>>0]=((l*(e[q+10>>1]|0)|0)+64+(c*(e[q+42>>1]|0)|0)|0)>>>7;a[i+6>>0]=((l*(e[q+12>>1]|0)|0)+64+(c*(e[q+44>>1]|0)|0)|0)>>>7;a[i+7>>0]=((l*(e[q+14>>1]|0)|0)+64+(c*(e[q+46>>1]|0)|0)|0)>>>7;a[i+8>>0]=((l*(e[q+16>>1]|0)|0)+64+(c*(e[q+48>>1]|0)|0)|0)>>>7;a[i+9>>0]=((l*(e[q+18>>1]|0)|0)+64+(c*(e[q+50>>1]|0)|0)|0)>>>7;a[i+10>>0]=((l*(e[q+20>>1]|0)|0)+64+(c*(e[q+52>>1]|0)|0)|0)>>>7;a[i+11>>0]=((l*(e[q+22>>1]|0)|0)+64+(c*(e[q+54>>1]|0)|0)|0)>>>7;a[i+12>>0]=((l*(e[q+24>>1]|0)|0)+64+(c*(e[q+56>>1]|0)|0)|0)>>>7;a[i+13>>0]=((l*(e[q+26>>1]|0)|0)+64+(c*(e[q+58>>1]|0)|0)|0)>>>7;a[i+14>>0]=((l*(e[q+28>>1]|0)|0)+64+(c*(e[q+60>>1]|0)|0)|0)>>>7;a[i+15>>0]=((l*(e[q+30>>1]|0)|0)+64+(c*(e[q+62>>1]|0)|0)|0)>>>7;g=g+1|0;if((g|0)==16)break;else i=i+j|0}L=o;return}function lc(c,d,e,f,g){c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0;k=f<<2;j=0;i=e;while(1){if((a[g>>0]|0)>1)ac(c,d,i,f);else{oc((b[d>>1]|0)*(b[c>>1]|0)&65535,i,f,i,f);b[c>>1]=0;b[c+2>>1]=0>>>16}e=c+32|0;h=i+4|0;if((a[g+1>>0]|0)>1)ac(e,d,h,f);else{oc((b[d>>1]|0)*(b[e>>1]|0)&65535,h,f,h,f);b[e>>1]=0;b[e+2>>1]=0>>>16}e=c+64|0;h=i+8|0;if((a[g+2>>0]|0)>1)ac(e,d,h,f);else{oc((b[d>>1]|0)*(b[e>>1]|0)&65535,h,f,h,f);b[e>>1]=0;b[e+2>>1]=0>>>16}e=c+96|0;h=i+12|0;if((a[g+3>>0]|0)>1)ac(e,d,h,f);else{oc((b[d>>1]|0)*(b[e>>1]|0)&65535,h,f,h,f);b[e>>1]=0;b[e+2>>1]=0>>>16}j=j+1|0;if((j|0)==4)break;else{g=g+4|0;c=c+128|0;i=i+k|0}}return}function mc(c,d,e,f,g,h){c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0;l=g<<2;k=c+128|0;if((a[h>>0]|0)>1)ac(c,d,e,g);else{oc((b[d>>1]|0)*(b[c>>1]|0)&65535,e,g,e,g);b[c>>1]=0;b[c+2>>1]=0>>>16}i=c+32|0;j=e+4|0;if((a[h+1>>0]|0)>1)ac(i,d,j,g);else{oc((b[d>>1]|0)*(b[i>>1]|0)&65535,j,g,j,g);b[i>>1]=0;b[i+2>>1]=0>>>16}j=c+64|0;i=e+l|0;if((a[h+2>>0]|0)>1)ac(j,d,i,g);else{oc((b[d>>1]|0)*(b[j>>1]|0)&65535,i,g,i,g);b[j>>1]=0;b[j+2>>1]=0>>>16}j=c+96|0;i=i+4|0;if((a[h+3>>0]|0)>1)ac(j,d,i,g);else{oc((b[d>>1]|0)*(b[j>>1]|0)&65535,i,g,i,g);b[j>>1]=0;b[j+2>>1]=0>>>16}if((a[h+4>>0]|0)>1)ac(k,d,f,g);else{oc((b[d>>1]|0)*(b[k>>1]|0)&65535,f,g,f,g);b[k>>1]=0;b[k+2>>1]=0>>>16}i=c+160|0;j=f+4|0;if((a[h+5>>0]|0)>1)ac(i,d,j,g);else{oc((b[d>>1]|0)*(b[i>>1]|0)&65535,j,g,j,g);b[i>>1]=0;b[i+2>>1]=0>>>16}j=c+192|0;i=f+l|0;if((a[h+6>>0]|0)>1)ac(j,d,i,g);else{oc((b[d>>1]|0)*(b[j>>1]|0)&65535,i,g,i,g);b[j>>1]=0;b[j+2>>1]=0>>>16}j=c+224|0;i=i+4|0;if((a[h+7>>0]|0)>1){ac(j,d,i,g);return}else{oc((b[d>>1]|0)*(b[j>>1]|0)&65535,i,g,i,g);b[j>>1]=0;b[j+2>>1]=0>>>16;return}}function nc(c,e,f,g,h){c=c|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0;i=L;L=L+32|0;x=i;r=b[c>>1]|0;z=b[c+16>>1]|0;A=z+r|0;z=r-z|0;r=b[c+8>>1]|0;u=b[c+24>>1]|0;w=(r*35468>>16)-u-(u*20091>>16)|0;u=(r*20091>>16)+r+(u*35468>>16)|0;b[x>>1]=u+A;b[x+24>>1]=A-u;u=x+8|0;b[u>>1]=w+z;b[x+16>>1]=z-w;w=x+2|0;z=b[c+2>>1]|0;A=b[c+18>>1]|0;r=A+z|0;A=z-A|0;z=b[c+10>>1]|0;s=b[c+26>>1]|0;j=(z*35468>>16)-s-(s*20091>>16)|0;s=(z*20091>>16)+z+(s*35468>>16)|0;b[w>>1]=s+r;b[x+26>>1]=r-s;b[x+10>>1]=j+A;b[x+18>>1]=A-j;j=b[c+4>>1]|0;A=b[c+20>>1]|0;s=A+j|0;A=j-A|0;j=b[c+12>>1]|0;r=b[c+28>>1]|0;z=(j*35468>>16)-r-(r*20091>>16)|0;r=(j*20091>>16)+j+(r*35468>>16)|0;b[x+28>>1]=s-r;j=z+A|0;b[x+12>>1]=j;b[x+20>>1]=A-z;z=b[c+6>>1]|0;A=b[c+22>>1]|0;o=A+z|0;A=z-A|0;z=b[c+14>>1]|0;n=b[c+30>>1]|0;p=(z*35468>>16)-n-(n*20091>>16)|0;n=(z*20091>>16)+z+(n*35468>>16)|0;b[x+30>>1]=o-n;z=A-p|0;b[x+22>>1]=z;t=b[x>>1]|0;v=x+4|0;s=r+s<<16>>16;r=b[w>>1]|0;q=x+6|0;o=n+o<<16>>16;n=(r*35468>>16)-o-(o*20091>>16)|0;o=(r*20091>>16)+r+(o*35468>>16)|0;r=s+t+4|0;b[x>>1]=(r+o|0)>>>3;b[q>>1]=(r-o|0)>>>3;s=t-s+4|0;b[w>>1]=(s+n|0)>>>3;b[v>>1]=(s-n|0)>>>3;n=b[u>>1]|0;s=x+12|0;j=j<<16>>16;t=x+10|0;o=b[t>>1]|0;r=x+14|0;A=p+A<<16>>16;p=(o*35468>>16)-A-(A*20091>>16)|0;A=(o*20091>>16)+o+(A*35468>>16)|0;o=j+n+4|0;b[u>>1]=(o+A|0)>>>3;b[r>>1]=(o-A|0)>>>3;j=n-j+4|0;b[t>>1]=(j+p|0)>>>3;b[s>>1]=(j-p|0)>>>3;p=x+16|0;j=b[p>>1]|0;n=x+20|0;A=b[n>>1]|0;o=x+18|0;k=b[o>>1]|0;m=x+22|0;z=z<<16>>16;l=(k*35468>>16)-z-(z*20091>>16)|0;z=(k*20091>>16)+k+(z*35468>>16)|0;k=A+j+4|0;b[p>>1]=(k+z|0)>>>3;b[m>>1]=(k-z|0)>>>3;A=j-A+4|0;b[o>>1]=(A+l|0)>>>3;b[n>>1]=(A-l|0)>>>3;l=x+24|0;A=b[l>>1]|0;j=x+28|0;z=b[j>>1]|0;k=x+26|0;C=b[k>>1]|0;c=x+30|0;B=b[c>>1]|0;y=(C*35468>>16)-B-(B*20091>>16)|0;B=(C*20091>>16)+C+(B*35468>>16)|0;C=z+A+4|0;b[l>>1]=(C+B|0)>>>3;b[c>>1]=(C-B|0)>>>3;z=A-z+4|0;b[k>>1]=(z+y|0)>>>3;b[j>>1]=(z-y|0)>>>3;x=(d[e>>0]|0)+(b[x>>1]|0)|0;x=(x|0)>0?x:0;a[g>>0]=(x|0)<255?x:255;w=(d[e+1>>0]|0)+(b[w>>1]|0)|0;w=(w|0)>0?w:0;a[g+1>>0]=(w|0)<255?w:255;v=(d[e+2>>0]|0)+(b[v>>1]|0)|0;v=(v|0)>0?v:0;a[g+2>>0]=(v|0)<255?v:255;q=(d[e+3>>0]|0)+(b[q>>1]|0)|0;q=(q|0)>0?q:0;a[g+3>>0]=(q|0)<255?q:255;q=g+h|0;g=e+f|0;e=(d[g>>0]|0)+(b[u>>1]|0)|0;e=(e|0)>0?e:0;a[q>>0]=(e|0)<255?e:255;e=(d[g+1>>0]|0)+(b[t>>1]|0)|0;e=(e|0)>0?e:0;a[q+1>>0]=(e|0)<255?e:255;e=(d[g+2>>0]|0)+(b[s>>1]|0)|0;e=(e|0)>0?e:0;a[q+2>>0]=(e|0)<255?e:255;e=(d[g+3>>0]|0)+(b[r>>1]|0)|0;e=(e|0)>0?e:0;a[q+3>>0]=(e|0)<255?e:255;e=q+h|0;g=g+f|0;p=(d[g>>0]|0)+(b[p>>1]|0)|0;p=(p|0)>0?p:0;a[e>>0]=(p|0)<255?p:255;o=(d[g+1>>0]|0)+(b[o>>1]|0)|0;o=(o|0)>0?o:0;a[e+1>>0]=(o|0)<255?o:255;n=(d[g+2>>0]|0)+(b[n>>1]|0)|0;n=(n|0)>0?n:0;a[e+2>>0]=(n|0)<255?n:255;m=(d[g+3>>0]|0)+(b[m>>1]|0)|0;m=(m|0)>0?m:0;a[e+3>>0]=(m|0)<255?m:255;h=e+h|0;g=g+f|0;f=(d[g>>0]|0)+(b[l>>1]|0)|0;f=(f|0)>0?f:0;a[h>>0]=(f|0)<255?f:255;f=(d[g+1>>0]|0)+(b[k>>1]|0)|0;f=(f|0)>0?f:0;a[h+1>>0]=(f|0)<255?f:255;f=(d[g+2>>0]|0)+(b[j>>1]|0)|0;f=(f|0)>0?f:0;a[h+2>>0]=(f|0)<255?f:255;g=(d[g+3>>0]|0)+(b[c>>1]|0)|0;g=(g|0)>0?g:0;a[h+3>>0]=(g|0)<255?g:255;L=i;return}function oc(b,c,e,f,g){b=b|0;c=c|0;e=e|0;f=f|0;g=g|0;var h=0;b=(b<<16>>16)+4>>3;h=b+(d[c>>0]|0)|0;h=(h|0)>0?h:0;a[f>>0]=(h|0)<255?h:255;h=b+(d[c+1>>0]|0)|0;h=(h|0)>0?h:0;a[f+1>>0]=(h|0)<255?h:255;h=b+(d[c+2>>0]|0)|0;h=(h|0)>0?h:0;a[f+2>>0]=(h|0)<255?h:255;h=b+(d[c+3>>0]|0)|0;h=(h|0)>0?h:0;a[f+3>>0]=(h|0)<255?h:255;h=f+g|0;f=c+e|0;c=b+(d[f>>0]|0)|0;c=(c|0)>0?c:0;a[h>>0]=(c|0)<255?c:255;c=b+(d[f+1>>0]|0)|0;c=(c|0)>0?c:0;a[h+1>>0]=(c|0)<255?c:255;c=b+(d[f+2>>0]|0)|0;c=(c|0)>0?c:0;a[h+2>>0]=(c|0)<255?c:255;c=b+(d[f+3>>0]|0)|0;c=(c|0)>0?c:0;a[h+3>>0]=(c|0)<255?c:255;c=h+g|0;f=f+e|0;h=b+(d[f>>0]|0)|0;h=(h|0)>0?h:0;a[c>>0]=(h|0)<255?h:255;h=b+(d[f+1>>0]|0)|0;h=(h|0)>0?h:0;a[c+1>>0]=(h|0)<255?h:255;h=b+(d[f+2>>0]|0)|0;h=(h|0)>0?h:0;a[c+2>>0]=(h|0)<255?h:255;h=b+(d[f+3>>0]|0)|0;h=(h|0)>0?h:0;a[c+3>>0]=(h|0)<255?h:255;g=c+g|0;f=f+e|0;e=b+(d[f>>0]|0)|0;e=(e|0)>0?e:0;a[g>>0]=(e|0)<255?e:255;e=b+(d[f+1>>0]|0)|0;e=(e|0)>0?e:0;a[g+1>>0]=(e|0)<255?e:255;e=b+(d[f+2>>0]|0)|0;e=(e|0)>0?e:0;a[g+2>>0]=(e|0)<255?e:255;f=b+(d[f+3>>0]|0)|0;f=(f|0)>0?f:0;a[g+3>>0]=(f|0)<255?f:255;return}function pc(a,c){a=a|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;d=L;L=L+32|0;p=d;t=b[a>>1]|0;f=b[a+24>>1]|0;r=f+t|0;m=b[a+8>>1]|0;h=b[a+16>>1]|0;l=h+m|0;h=m-h|0;f=t-f|0;b[p>>1]=l+r;t=p+8|0;b[t>>1]=h+f;b[p+16>>1]=r-l;b[p+24>>1]=f-h;h=p+2|0;f=b[a+2>>1]|0;l=b[a+26>>1]|0;r=l+f|0;m=b[a+10>>1]|0;u=b[a+18>>1]|0;n=u+m|0;u=m-u|0;l=f-l|0;f=n+r|0;b[h>>1]=f;b[p+10>>1]=u+l;b[p+18>>1]=r-n;b[p+26>>1]=l-u;u=b[a+4>>1]|0;l=b[a+28>>1]|0;n=l+u|0;r=b[a+12>>1]|0;m=b[a+20>>1]|0;o=m+r|0;m=r-m|0;l=u-l|0;b[p+20>>1]=n-o;b[p+28>>1]=l-m;u=b[a+6>>1]|0;r=b[a+30>>1]|0;j=r+u|0;q=b[a+14>>1]|0;s=b[a+22>>1]|0;k=s+q|0;s=q-s|0;r=u-r|0;u=j-k|0;b[p+22>>1]=u;q=r-s|0;b[p+30>>1]=q;i=b[p>>1]|0;j=k+j<<16>>16;f=f<<16>>16;n=o+n<<16>>16;o=n+f|0;n=f-n|0;f=j+i+3|0;b[p>>1]=(f+o|0)>>>3;j=i-j+3|0;b[h>>1]=(j+n|0)>>>3;b[p+4>>1]=(f-o|0)>>>3;b[p+6>>1]=(j-n|0)>>>3;n=b[t>>1]|0;r=s+r<<16>>16;s=p+10|0;j=b[s>>1]|0;l=m+l<<16>>16;m=l+j|0;l=j-l|0;j=r+n+3|0;o=(j+m|0)>>>3&65535;b[t>>1]=o;r=n-r+3|0;n=(r+l|0)>>>3&65535;b[s>>1]=n;m=(j-m|0)>>>3&65535;b[p+12>>1]=m;l=(r-l|0)>>>3&65535;b[p+14>>1]=l;r=p+16|0;j=b[r>>1]|0;u=u<<16>>16;s=p+18|0;t=b[s>>1]|0;f=p+20|0;h=b[f>>1]|0;i=h+t|0;h=t-h|0;t=u+j+3|0;k=(t+i|0)>>>3&65535;b[r>>1]=k;u=j-u+3|0;j=(u+h|0)>>>3&65535;b[s>>1]=j;i=(t-i|0)>>>3&65535;b[f>>1]=i;h=(u-h|0)>>>3&65535;b[p+22>>1]=h;u=p+24|0;f=b[u>>1]|0;q=q<<16>>16;t=p+26|0;s=b[t>>1]|0;r=p+28|0;a=b[r>>1]|0;e=a+s|0;a=s-a|0;s=q+f+3|0;g=(s+e|0)>>>3&65535;b[u>>1]=g;q=f-q+3|0;f=(q+a|0)>>>3&65535;b[t>>1]=f;e=(s-e|0)>>>3&65535;b[r>>1]=e;a=(q-a|0)>>>3&65535;b[p+30>>1]=a;b[c>>1]=b[p>>1]|0;b[c+32>>1]=b[p+2>>1]|0;b[c+64>>1]=b[p+4>>1]|0;b[c+96>>1]=b[p+6>>1]|0;b[c+128>>1]=o;b[c+160>>1]=n;b[c+192>>1]=m;b[c+224>>1]=l;b[c+256>>1]=k;b[c+288>>1]=j;b[c+320>>1]=i;b[c+352>>1]=h;b[c+384>>1]=g;b[c+416>>1]=f;b[c+448>>1]=e;b[c+480>>1]=a;L=d;return}function qc(a,c){a=a|0;c=c|0;a=(b[a>>1]|0)+3>>3&65535;b[c>>1]=a;b[c+32>>1]=a;b[c+64>>1]=a;b[c+96>>1]=a;b[c+128>>1]=a;b[c+160>>1]=a;b[c+192>>1]=a;b[c+224>>1]=a;b[c+256>>1]=a;b[c+288>>1]=a;b[c+320>>1]=a;b[c+352>>1]=a;b[c+384>>1]=a;b[c+416>>1]=a;b[c+448>>1]=a;b[c+480>>1]=a;return}function rc(a,b){a=a|0;b=b|0;b=b+a|0;b=(b|0)>0?b:0;return c[3696+(((b|0)<127?b:127)<<2)>>2]|0}function sc(a,b){a=a|0;b=b|0;b=b+a|0;b=(b|0)>0?b:0;return c[3696+(((b|0)<127?b:127)<<2)>>2]<<1|0}function tc(a,b){a=a|0;b=b|0;b=b+a|0;b=(b|0)>0?b:0;b=c[3696+(((b|0)<127?b:127)<<2)>>2]|0;return ((b|0)<132?b:132)|0}function uc(a){a=a|0;a=(a|0)>0?a:0;return c[4208+(((a|0)<127?a:127)<<2)>>2]|0}function vc(a,b){a=a|0;b=b|0;b=b+a|0;b=(b|0)>0?b:0;b=(c[4208+(((b|0)<127?b:127)<<2)>>2]|0)*101581>>16;return ((b|0)>8?b:8)|0}function wc(a,b){a=a|0;b=b|0;b=b+a|0;b=(b|0)>0?b:0;return c[4208+(((b|0)<127?b:127)<<2)>>2]|0}function xc(b,e,f,g,h,i){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;o=c[b+2900>>2]|0;r=c[b+2864>>2]|0;q=c[b+3088>>2]|0;m=c[q+4>>2]|0;n=m&65535;j=m>>>16&65535;do if(a[q+10>>0]|0){l=m>>16;k=c[b+3184>>2]|0;if((k+-152|0)<=(l|0)){k=c[b+3188>>2]|0;if((k+144|0)<(l|0))l=k+128&65535;else l=j}else l=k+65408&65535;k=m<<16>>16;j=c[b+3192>>2]|0;if((j+-152|0)>(k|0)){k=j+65408&65535;j=l;break}j=c[b+3196>>2]|0;if((j+144|0)<(k|0)){k=j+128&65535;j=l}else{k=n;j=l}}else k=n;while(0);p=k<<16>>16;q=j<<16>>16;l=o+((p>>3)*r|0)+(q>>3)|0;if(!(((j&65535)<<16|k&65535)&458759)){m=e;n=l;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=l+r|0;k=e+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));j=j+r|0;k=k+h|0;m=k;n=j;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));l=j+r|0;j=k+h|0;m=j;n=l;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0));m=j+h|0;n=l+r|0;o=m+16|0;do{a[m>>0]=a[n>>0]|0;m=m+1|0;n=n+1|0}while((m|0)<(o|0))}else W[c[b+3212>>2]&15](l,r,q&7,p&7,e,h);m=c[b+2844>>2]|0;n=m&(((((p>>31|1)+p&65535)<<16>>16)/2|0)&65535);m=(((q>>31|1)+q<<16>>16|0)/2|0)&65535&m;j=r>>1;r=m<<16;l=((n<<16>>19)*j|0)+(r>>19)|0;k=(c[b+2904>>2]|0)+l|0;l=(c[b+2908>>2]|0)+l|0;if(!((r|n)&458759)){p=k;r=p;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;p=p+4|0;p=d[p>>0]|d[p+1>>0]<<8|d[p+2>>0]<<16|d[p+3>>0]<<24;h=f;b=h;a[b>>0]=r;a[b+1>>0]=r>>8;a[b+2>>0]=r>>16;a[b+3>>0]=r>>24;h=h+4|0;a[h>>0]=p;a[h+1>>0]=p>>8;a[h+2>>0]=p>>16;a[h+3>>0]=p>>24;h=k+j|0;f=f+i|0;p=h;b=p;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;p=p+4|0;p=d[p>>0]|d[p+1>>0]<<8|d[p+2>>0]<<16|d[p+3>>0]<<24;r=f;q=r;a[q>>0]=b;a[q+1>>0]=b>>8;a[q+2>>0]=b>>16;a[q+3>>0]=b>>24;r=r+4|0;a[r>>0]=p;a[r+1>>0]=p>>8;a[r+2>>0]=p>>16;a[r+3>>0]=p>>24;h=h+j|0;f=f+i|0;r=h;p=r;p=d[p>>0]|d[p+1>>0]<<8|d[p+2>>0]<<16|d[p+3>>0]<<24;r=r+4|0;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;q=f;b=q;a[b>>0]=p;a[b+1>>0]=p>>8;a[b+2>>0]=p>>16;a[b+3>>0]=p>>24;q=q+4|0;a[q>>0]=r;a[q+1>>0]=r>>8;a[q+2>>0]=r>>16;a[q+3>>0]=r>>24;h=h+j|0;f=f+i|0;q=h;r=q;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;q=q+4|0;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;b=f;p=b;a[p>>0]=r;a[p+1>>0]=r>>8;a[p+2>>0]=r>>16;a[p+3>>0]=r>>24;b=b+4|0;a[b>>0]=q;a[b+1>>0]=q>>8;a[b+2>>0]=q>>16;a[b+3>>0]=q>>24;h=h+j|0;f=f+i|0;b=h;q=b;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;b=b+4|0;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;p=f;r=p;a[r>>0]=q;a[r+1>>0]=q>>8;a[r+2>>0]=q>>16;a[r+3>>0]=q>>24;p=p+4|0;a[p>>0]=b;a[p+1>>0]=b>>8;a[p+2>>0]=b>>16;a[p+3>>0]=b>>24;h=h+j|0;f=f+i|0;p=h;b=p;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;p=p+4|0;p=d[p>>0]|d[p+1>>0]<<8|d[p+2>>0]<<16|d[p+3>>0]<<24;r=f;q=r;a[q>>0]=b;a[q+1>>0]=b>>8;a[q+2>>0]=b>>16;a[q+3>>0]=b>>24;r=r+4|0;a[r>>0]=p;a[r+1>>0]=p>>8;a[r+2>>0]=p>>16;a[r+3>>0]=p>>24;h=h+j|0;f=f+i|0;r=h;p=r;p=d[p>>0]|d[p+1>>0]<<8|d[p+2>>0]<<16|d[p+3>>0]<<24;r=r+4|0;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;q=f;b=q;a[b>>0]=p;a[b+1>>0]=p>>8;a[b+2>>0]=p>>16;a[b+3>>0]=p>>24;q=q+4|0;a[q>>0]=r;a[q+1>>0]=r>>8;a[q+2>>0]=r>>16;a[q+3>>0]=r>>24;h=h+j|0;q=h;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;h=h+4|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;f=f+i|0;r=f;a[r>>0]=q;a[r+1>>0]=q>>8;a[r+2>>0]=q>>16;a[r+3>>0]=q>>24;f=f+4|0;a[f>>0]=h;a[f+1>>0]=h>>8;a[f+2>>0]=h>>16;a[f+3>>0]=h>>24;f=l;h=f;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;f=f+4|0;f=d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24;r=g;q=r;a[q>>0]=h;a[q+1>>0]=h>>8;a[q+2>>0]=h>>16;a[q+3>>0]=h>>24;r=r+4|0;a[r>>0]=f;a[r+1>>0]=f>>8;a[r+2>>0]=f>>16;a[r+3>>0]=f>>24;r=l+j|0;f=g+i|0;q=r;h=q;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;q=q+4|0;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;g=f;b=g;a[b>>0]=h;a[b+1>>0]=h>>8;a[b+2>>0]=h>>16;a[b+3>>0]=h>>24;g=g+4|0;a[g>>0]=q;a[g+1>>0]=q>>8;a[g+2>>0]=q>>16;a[g+3>>0]=q>>24;g=r+j|0;f=f+i|0;r=g;q=r;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;r=r+4|0;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;b=f;h=b;a[h>>0]=q;a[h+1>>0]=q>>8;a[h+2>>0]=q>>16;a[h+3>>0]=q>>24;b=b+4|0;a[b>>0]=r;a[b+1>>0]=r>>8;a[b+2>>0]=r>>16;a[b+3>>0]=r>>24;g=g+j|0;f=f+i|0;b=g;r=b;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;b=b+4|0;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;h=f;q=h;a[q>>0]=r;a[q+1>>0]=r>>8;a[q+2>>0]=r>>16;a[q+3>>0]=r>>24;h=h+4|0;a[h>>0]=b;a[h+1>>0]=b>>8;a[h+2>>0]=b>>16;a[h+3>>0]=b>>24;g=g+j|0;f=f+i|0;h=g;b=h;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;h=h+4|0;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;q=f;r=q;a[r>>0]=b;a[r+1>>0]=b>>8;a[r+2>>0]=b>>16;a[r+3>>0]=b>>24;q=q+4|0;a[q>>0]=h;a[q+1>>0]=h>>8;a[q+2>>0]=h>>16;a[q+3>>0]=h>>24;g=g+j|0;f=f+i|0;q=g;h=q;h=d[h>>0]|d[h+1>>0]<<8|d[h+2>>0]<<16|d[h+3>>0]<<24;q=q+4|0;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;r=f;b=r;a[b>>0]=h;a[b+1>>0]=h>>8;a[b+2>>0]=h>>16;a[b+3>>0]=h>>24;r=r+4|0;a[r>>0]=q;a[r+1>>0]=q>>8;a[r+2>>0]=q>>16;a[r+3>>0]=q>>24;g=g+j|0;f=f+i|0;r=g;q=r;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;r=r+4|0;r=d[r>>0]|d[r+1>>0]<<8|d[r+2>>0]<<16|d[r+3>>0]<<24;b=f;h=b;a[h>>0]=q;a[h+1>>0]=q>>8;a[h+2>>0]=q>>16;a[h+3>>0]=q>>24;b=b+4|0;a[b>>0]=r;a[b+1>>0]=r>>8;a[b+2>>0]=r>>16;a[b+3>>0]=r>>24;g=g+j|0;b=g;b=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;g=g+4|0;g=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;i=f+i|0;f=i;a[f>>0]=b;a[f+1>>0]=b>>8;a[f+2>>0]=b>>16;a[f+3>>0]=b>>24;i=i+4|0;a[i>>0]=g;a[i+1>>0]=g>>8;a[i+2>>0]=g>>16;a[i+3>>0]=g>>24;return}else{h=b+3208|0;r=m&7;b=n&7;W[c[h>>2]&15](k,j,r,b,f,i);W[c[h>>2]&15](l,j,r,b,g,i);return}}function yc(e){e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0;w=e+3088|0;f=c[w>>2]|0;if((a[f>>0]|0)!=9){xc(e,c[e+3020>>2]|0,c[e+3024>>2]|0,c[e+3028>>2]|0,c[e+2984>>2]|0,c[e+3004>>2]|0);return}o=e+2844|0;x=e+3184|0;z=e+3188|0;y=e+3192|0;C=e+3196|0;n=0;do{l=n<<3;j=n<<1;A=l|1;D=l|4;h=l|5;f=(b[f+12+(A<<2)>>1]|0)+(b[f+12+(l<<2)>>1]|0)+(b[f+12+(D<<2)>>1]|0)+(b[f+12+(h<<2)>>1]|0)|0;m=c[o>>2]|0;f=(((f>>31<<3|4)+f|0)/8|0)&m;g=e+2144+((j+16|0)*28|0)+24|0;b[g>>1]=f;i=c[w>>2]|0;h=(b[i+12+(A<<2)+2>>1]|0)+(b[i+12+(l<<2)+2>>1]|0)+(b[i+12+(D<<2)+2>>1]|0)+(b[i+12+(h<<2)+2>>1]|0)|0;h=(((h>>31<<3|4)+h|0)/8|0)&m;i=g+2|0;b[i>>1]=h;if(a[(c[w>>2]|0)+10>>0]|0){D=h<<16;A=c[x>>2]|0;D=(D>>15|0)<(A+-152|0)?A+-128>>1:D>>16;A=c[z>>2]|0;b[i>>1]=(D<<16>>15|0)>(A+144|0)?(A+128|0)>>>1:D;D=f<<16;A=c[y>>2]|0;D=(D>>15|0)<(A+-152|0)?A+-128>>1:D>>16;A=c[C>>2]|0;b[g>>1]=(D<<16>>15|0)>(A+144|0)?(A+128|0)>>>1:D}c[e+2144+((j+20|0)*28|0)+24>>2]=c[g>>2];k=j|1;f=c[w>>2]|0;D=f+12+((l|2)<<2)|0;A=f+12+((l|3)<<2)|0;j=f+12+((l|6)<<2)|0;i=f+12+((l|7)<<2)|0;g=(b[A>>1]|0)+(b[D>>1]|0)+(b[j>>1]|0)+(b[i>>1]|0)|0;g=(((g>>31<<3|4)+g|0)/8|0)&m;h=e+2144+((k+16|0)*28|0)+24|0;b[h>>1]=g;i=(b[A+2>>1]|0)+(b[D+2>>1]|0)+(b[j+2>>1]|0)+(b[i+2>>1]|0)|0;i=(((i>>31<<3|4)+i|0)/8|0)&c[o>>2];j=h+2|0;b[j>>1]=i;if(a[f+10>>0]|0){D=i<<16;A=c[x>>2]|0;D=(D>>15|0)<(A+-152|0)?A+-128>>1:D>>16;A=c[z>>2]|0;b[j>>1]=(D<<16>>15|0)>(A+144|0)?(A+128|0)>>>1:D;D=g<<16;A=c[y>>2]|0;D=(D>>15|0)<(A+-152|0)?A+-128>>1:D>>16;A=c[C>>2]|0;b[h>>1]=(D<<16>>15|0)>(A+144|0)?(A+128|0)>>>1:D}c[e+2144+((k+20|0)*28|0)+24>>2]=c[h>>2];n=n+1|0}while((n|0)!=2);A=c[e+3020>>2]|0;D=c[e+2900>>2]|0;t=e+2984|0;a:do if((d[f+8>>0]|0)<3){v=c[t>>2]|0;l=e+2168|0;k=c[f+12>>2]|0;c[l>>2]=k;s=e+2224|0;m=c[f+20>>2]|0;c[s>>2]=m;t=e+2392|0;o=c[f+44>>2]|0;c[t>>2]=o;u=e+2448|0;r=c[f+52>>2]|0;c[u>>2]=r;g=k>>>16&65535;i=k&65535;do if(a[f+10>>0]|0){j=l+2|0;h=k>>16;p=c[x>>2]|0;q=p+-152|0;if((q|0)<=(h|0)){f=c[z>>2]|0;if((f+144|0)<(h|0)){g=f+128&65535;B=15}}else{g=p+65408&65535;B=15}if((B|0)==15)b[j>>1]=g;h=k<<16>>16;k=c[y>>2]|0;n=k+-152|0;if((n|0)<=(h|0)){f=c[C>>2]|0;if((f+144|0)<(h|0)){f=f+128|0;B=20}}else{f=k+65408|0;B=20}if((B|0)==20){i=f&65535;b[l>>1]=i}j=s+2|0;f=m>>16;if((q|0)<=(f|0)){h=c[z>>2]|0;if((h+144|0)<(f|0)){f=h+128|0;B=25}}else{f=p+65408|0;B=25}if((B|0)==25)b[j>>1]=f;f=m<<16>>16;if((n|0)<=(f|0)){h=c[C>>2]|0;if((h+144|0)<(f|0)){f=h+128|0;B=30}}else{f=k+65408|0;B=30}if((B|0)==30)b[s>>1]=f;j=t+2|0;f=o>>16;if((q|0)<=(f|0)){h=c[z>>2]|0;if((h+144|0)<(f|0)){f=h+128|0;B=35}}else{f=p+65408|0;B=35}if((B|0)==35)b[j>>1]=f;f=o<<16>>16;if((n|0)<=(f|0)){h=c[C>>2]|0;if((h+144|0)<(f|0)){f=h+128|0;B=40}}else{f=k+65408|0;B=40}if((B|0)==40)b[t>>1]=f;j=u+2|0;h=r>>16;if((q|0)<=(h|0)){f=c[z>>2]|0;if((f+144|0)<(h|0)){f=f+128|0;B=45}}else{f=p+65408|0;B=45}if((B|0)==45)b[j>>1]=f;h=r<<16>>16;if((n|0)>(h|0)){b[u>>1]=k+65408;break}f=c[C>>2]|0;if((f+144|0)<(h|0))b[u>>1]=f+128}while(0);h=c[e+2160>>2]|0;f=A+h|0;h=D+h+((i<<16>>16>>3)*v|0)+(g<<16>>16>>3)|0;if(!((i|g)&7)){C=h;x=C;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;C=C+4|0;C=d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24;B=f;w=B;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;B=B+4|0;a[B>>0]=C;a[B+1>>0]=C>>8;a[B+2>>0]=C>>16;a[B+3>>0]=C>>24;B=h+v|0;C=f+v|0;w=B;x=w;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=w+4|0;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=C;y=z;a[y>>0]=x;a[y+1>>0]=x>>8;a[y+2>>0]=x>>16;a[y+3>>0]=x>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;B=B+v|0;C=C+v|0;z=B;w=z;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=C;x=y;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;B=B+v|0;C=C+v|0;y=B;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;x=C;w=x;a[w>>0]=z;a[w+1>>0]=z>>8;a[w+2>>0]=z>>16;a[w+3>>0]=z>>24;x=x+4|0;a[x>>0]=y;a[x+1>>0]=y>>8;a[x+2>>0]=y>>16;a[x+3>>0]=y>>24;B=B+v|0;C=C+v|0;x=B;y=x;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;x=x+4|0;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=C;z=w;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;w=w+4|0;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;B=B+v|0;C=C+v|0;w=B;x=w;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=w+4|0;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=C;y=z;a[y>>0]=x;a[y+1>>0]=x>>8;a[y+2>>0]=x>>16;a[y+3>>0]=x>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;B=B+v|0;C=C+v|0;z=B;w=z;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=C;x=y;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;B=B+v|0;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;C=C+v|0;z=C;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;C=C+4|0;a[C>>0]=B;a[C+1>>0]=B>>8;a[C+2>>0]=B>>16;a[C+3>>0]=B>>24}else W[c[e+3208>>2]&15](h,v,g&7,i&7,f,v);g=c[e+2216>>2]|0;h=A+g|0;i=b[s>>1]|0;f=b[s+2>>1]|0;g=D+g+((i<<16>>16>>3)*v|0)+(f<<16>>16>>3)|0;if(!((f|i)&7)){C=g;x=C;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;C=C+4|0;C=d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24;B=h;w=B;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;B=B+4|0;a[B>>0]=C;a[B+1>>0]=C>>8;a[B+2>>0]=C>>16;a[B+3>>0]=C>>24;B=g+v|0;C=h+v|0;w=B;x=w;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=w+4|0;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=C;y=z;a[y>>0]=x;a[y+1>>0]=x>>8;a[y+2>>0]=x>>16;a[y+3>>0]=x>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;B=B+v|0;C=C+v|0;z=B;w=z;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=C;x=y;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;B=B+v|0;C=C+v|0;y=B;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;x=C;w=x;a[w>>0]=z;a[w+1>>0]=z>>8;a[w+2>>0]=z>>16;a[w+3>>0]=z>>24;x=x+4|0;a[x>>0]=y;a[x+1>>0]=y>>8;a[x+2>>0]=y>>16;a[x+3>>0]=y>>24;B=B+v|0;C=C+v|0;x=B;y=x;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;x=x+4|0;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=C;z=w;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;w=w+4|0;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;B=B+v|0;C=C+v|0;w=B;x=w;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=w+4|0;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=C;y=z;a[y>>0]=x;a[y+1>>0]=x>>8;a[y+2>>0]=x>>16;a[y+3>>0]=x>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;B=B+v|0;C=C+v|0;z=B;w=z;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=C;x=y;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;B=B+v|0;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;C=C+v|0;z=C;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;C=C+4|0;a[C>>0]=B;a[C+1>>0]=B>>8;a[C+2>>0]=B>>16;a[C+3>>0]=B>>24}else W[c[e+3208>>2]&15](g,v,f&7,i&7,h,v);g=c[e+2384>>2]|0;h=A+g|0;i=b[t>>1]|0;f=b[t+2>>1]|0;g=D+g+((i<<16>>16>>3)*v|0)+(f<<16>>16>>3)|0;if(!((f|i)&7)){C=g;x=C;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;C=C+4|0;C=d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24;B=h;w=B;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;B=B+4|0;a[B>>0]=C;a[B+1>>0]=C>>8;a[B+2>>0]=C>>16;a[B+3>>0]=C>>24;B=g+v|0;C=h+v|0;w=B;x=w;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=w+4|0;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=C;y=z;a[y>>0]=x;a[y+1>>0]=x>>8;a[y+2>>0]=x>>16;a[y+3>>0]=x>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;B=B+v|0;C=C+v|0;z=B;w=z;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=C;x=y;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;B=B+v|0;C=C+v|0;y=B;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;x=C;w=x;a[w>>0]=z;a[w+1>>0]=z>>8;a[w+2>>0]=z>>16;a[w+3>>0]=z>>24;x=x+4|0;a[x>>0]=y;a[x+1>>0]=y>>8;a[x+2>>0]=y>>16;a[x+3>>0]=y>>24;B=B+v|0;C=C+v|0;x=B;y=x;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;x=x+4|0;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=C;z=w;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;w=w+4|0;a[w>>0]=x;a[w+1>>0]=x>>8;a[w+2>>0]=x>>16;a[w+3>>0]=x>>24;B=B+v|0;C=C+v|0;w=B;x=w;x=d[x>>0]|d[x+1>>0]<<8|d[x+2>>0]<<16|d[x+3>>0]<<24;w=w+4|0;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=C;y=z;a[y>>0]=x;a[y+1>>0]=x>>8;a[y+2>>0]=x>>16;a[y+3>>0]=x>>24;z=z+4|0;a[z>>0]=w;a[z+1>>0]=w>>8;a[z+2>>0]=w>>16;a[z+3>>0]=w>>24;B=B+v|0;C=C+v|0;z=B;w=z;w=d[w>>0]|d[w+1>>0]<<8|d[w+2>>0]<<16|d[w+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=C;x=y;a[x>>0]=w;a[x+1>>0]=w>>8;a[x+2>>0]=w>>16;a[x+3>>0]=w>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;B=B+v|0;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;C=C+v|0;z=C;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;C=C+4|0;a[C>>0]=B;a[C+1>>0]=B>>8;a[C+2>>0]=B>>16;a[C+3>>0]=B>>24}else W[c[e+3208>>2]&15](g,v,f&7,i&7,h,v);f=c[e+2440>>2]|0;h=A+f|0;i=b[u>>1]|0;g=b[u+2>>1]|0;f=D+f+((i<<16>>16>>3)*v|0)+(g<<16>>16>>3)|0;if(!((g|i)&7)){D=f;z=D;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;D=D+4|0;D=d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24;C=h;y=C;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;C=C+4|0;a[C>>0]=D;a[C+1>>0]=D>>8;a[C+2>>0]=D>>16;a[C+3>>0]=D>>24;C=f+v|0;D=h+v|0;y=C;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=D;A=B;a[A>>0]=z;a[A+1>>0]=z>>8;a[A+2>>0]=z>>16;a[A+3>>0]=z>>24;B=B+4|0;a[B>>0]=y;a[B+1>>0]=y>>8;a[B+2>>0]=y>>16;a[B+3>>0]=y>>24;C=C+v|0;D=D+v|0;B=C;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;A=D;z=A;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;A=A+4|0;a[A>>0]=B;a[A+1>>0]=B>>8;a[A+2>>0]=B>>16;a[A+3>>0]=B>>24;C=C+v|0;D=D+v|0;A=C;B=A;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;A=A+4|0;A=d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24;z=D;y=z;a[y>>0]=B;a[y+1>>0]=B>>8;a[y+2>>0]=B>>16;a[y+3>>0]=B>>24;z=z+4|0;a[z>>0]=A;a[z+1>>0]=A>>8;a[z+2>>0]=A>>16;a[z+3>>0]=A>>24;C=C+v|0;D=D+v|0;z=C;A=z;A=d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24;z=z+4|0;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=D;B=y;a[B>>0]=A;a[B+1>>0]=A>>8;a[B+2>>0]=A>>16;a[B+3>>0]=A>>24;y=y+4|0;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;C=C+v|0;D=D+v|0;y=C;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=D;A=B;a[A>>0]=z;a[A+1>>0]=z>>8;a[A+2>>0]=z>>16;a[A+3>>0]=z>>24;B=B+4|0;a[B>>0]=y;a[B+1>>0]=y>>8;a[B+2>>0]=y>>16;a[B+3>>0]=y>>24;C=C+v|0;D=D+v|0;B=C;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;A=D;z=A;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;A=A+4|0;a[A>>0]=B;a[A+1>>0]=B>>8;a[A+2>>0]=B>>16;a[A+3>>0]=B>>24;C=C+v|0;A=C;A=d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24;C=C+4|0;C=d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24;D=D+v|0;B=D;a[B>>0]=A;a[B+1>>0]=A>>8;a[B+2>>0]=A>>16;a[B+3>>0]=A>>24;D=D+4|0;a[D>>0]=C;a[D+1>>0]=C>>8;a[D+2>>0]=C>>16;a[D+3>>0]=C>>24;break}else{W[c[e+3208>>2]&15](f,v,g&7,i&7,h,v);break}}else{u=e+3200|0;v=e+3204|0;s=0;while(1){p=s|1;r=c[t>>2]|0;o=e+2144+(s*28|0)+24|0;i=c[f+12+(s<<2)>>2]|0;c[o>>2]=i;q=e+2144+(p*28|0)+24|0;n=c[(c[w>>2]|0)+12+(p<<2)>>2]|0;c[q>>2]=n;j=i>>>16&65535;do if(a[(c[w>>2]|0)+10>>0]|0){h=o+2|0;f=i>>16;l=c[x>>2]|0;m=l+-152|0;if((m|0)<=(f|0)){g=c[z>>2]|0;if((g+144|0)<(f|0)){j=g+128&65535;B=68}}else{j=l+65408&65535;B=68}if((B|0)==68){B=0;b[h>>1]=j}f=i<<16>>16;i=c[y>>2]|0;k=i+-152|0;if((k|0)<=(f|0)){g=c[C>>2]|0;if((g+144|0)<(f|0)){f=g+128|0;B=73}}else{f=i+65408|0;B=73}if((B|0)==73){B=0;b[o>>1]=f}h=q+2|0;g=n>>16;if((m|0)<=(g|0)){f=c[z>>2]|0;if((f+144|0)<(g|0)){f=f+128|0;B=78}}else{f=l+65408|0;B=78}if((B|0)==78){B=0;b[h>>1]=f}g=n<<16>>16;if((k|0)>(g|0)){b[q>>1]=i+65408;break}f=c[C>>2]|0;if((f+144|0)<(g|0))b[q>>1]=f+128}while(0);f=c[o>>2]|0;h=c[e+2144+(s*28|0)+16>>2]|0;g=A+h|0;h=D+h+((f<<16>>19)*r|0)+(j<<16>>16>>3)|0;i=((j|f&65535)&7)==0;do if((f|0)==(c[q>>2]|0))if(i){p=h;m=p;m=d[m>>0]|d[m+1>>0]<<8|d[m+2>>0]<<16|d[m+3>>0]<<24;p=p+4|0;p=d[p>>0]|d[p+1>>0]<<8|d[p+2>>0]<<16|d[p+3>>0]<<24;q=g;l=q;a[l>>0]=m;a[l+1>>0]=m>>8;a[l+2>>0]=m>>16;a[l+3>>0]=m>>24;q=q+4|0;a[q>>0]=p;a[q+1>>0]=p>>8;a[q+2>>0]=p>>16;a[q+3>>0]=p>>24;q=h+r|0;p=g+r|0;l=q;m=l;m=d[m>>0]|d[m+1>>0]<<8|d[m+2>>0]<<16|d[m+3>>0]<<24;l=l+4|0;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;n=p;o=n;a[o>>0]=m;a[o+1>>0]=m>>8;a[o+2>>0]=m>>16;a[o+3>>0]=m>>24;n=n+4|0;a[n>>0]=l;a[n+1>>0]=l>>8;a[n+2>>0]=l>>16;a[n+3>>0]=l>>24;q=q+r|0;p=p+r|0;n=q;l=n;l=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;n=n+4|0;n=d[n>>0]|d[n+1>>0]<<8|d[n+2>>0]<<16|d[n+3>>0]<<24;o=p;m=o;a[m>>0]=l;a[m+1>>0]=l>>8;a[m+2>>0]=l>>16;a[m+3>>0]=l>>24;o=o+4|0;a[o>>0]=n;a[o+1>>0]=n>>8;a[o+2>>0]=n>>16;a[o+3>>0]=n>>24;q=q+r|0;o=q;o=d[o>>0]|d[o+1>>0]<<8|d[o+2>>0]<<16|d[o+3>>0]<<24;q=q+4|0;q=d[q>>0]|d[q+1>>0]<<8|d[q+2>>0]<<16|d[q+3>>0]<<24;r=p+r|0;p=r;a[p>>0]=o;a[p+1>>0]=o>>8;a[p+2>>0]=o>>16;a[p+3>>0]=o>>24;r=r+4|0;a[r>>0]=q;a[r+1>>0]=q>>8;a[r+2>>0]=q>>16;a[r+3>>0]=q>>24;break}else{W[c[v>>2]&15](h,r,j&7,f&7,g,r);break}else{if(i){a[g>>0]=a[h>>0]|0;a[g+1>>0]=a[h+1>>0]|0;a[g+2>>0]=a[h+2>>0]|0;a[g+3>>0]=a[h+3>>0]|0;o=g+r|0;n=h+r|0;a[o>>0]=a[n>>0]|0;a[o+1>>0]=a[n+1>>0]|0;a[o+2>>0]=a[n+2>>0]|0;a[o+3>>0]=a[n+3>>0]|0;o=o+r|0;n=n+r|0;a[o>>0]=a[n>>0]|0;a[o+1>>0]=a[n+1>>0]|0;a[o+2>>0]=a[n+2>>0]|0;a[o+3>>0]=a[n+3>>0]|0;o=o+r|0;n=n+r|0;a[o>>0]=a[n>>0]|0;a[o+1>>0]=a[n+1>>0]|0;a[o+2>>0]=a[n+2>>0]|0;a[o+3>>0]=a[n+3>>0]|0}else W[c[u>>2]&15](h,r,j&7,f&7,g,r);g=c[e+2144+(p*28|0)+16>>2]|0;h=A+g|0;i=b[q>>1]|0;f=b[q+2>>1]|0;g=D+g+((i<<16>>16>>3)*r|0)+(f<<16>>16>>3)|0;if(!((f|i)&7)){a[h>>0]=a[g>>0]|0;a[h+1>>0]=a[g+1>>0]|0;a[h+2>>0]=a[g+2>>0]|0;a[h+3>>0]=a[g+3>>0]|0;q=h+r|0;p=g+r|0;a[q>>0]=a[p>>0]|0;a[q+1>>0]=a[p+1>>0]|0;a[q+2>>0]=a[p+2>>0]|0;a[q+3>>0]=a[p+3>>0]|0;q=q+r|0;p=p+r|0;a[q>>0]=a[p>>0]|0;a[q+1>>0]=a[p+1>>0]|0;a[q+2>>0]=a[p+2>>0]|0;a[q+3>>0]=a[p+3>>0]|0;q=q+r|0;r=p+r|0;a[q>>0]=a[r>>0]|0;a[q+1>>0]=a[r+1>>0]|0;a[q+2>>0]=a[r+2>>0]|0;a[q+3>>0]=a[r+3>>0]|0;break}else{W[c[u>>2]&15](g,r,f&7,i&7,h,r);break}}while(0);f=s+2|0;if(f>>>0>=16)break a;s=f;f=c[w>>2]|0}}while(0);m=c[e+3024>>2]|0;n=c[e+2904>>2]|0;p=e+3004|0;q=e+3200|0;r=e+3204|0;l=16;do{k=l|1;o=c[p>>2]|0;f=c[e+2144+(l*28|0)+24>>2]|0;i=c[e+2144+(l*28|0)+16>>2]|0;g=m+i|0;h=b[e+2144+(l*28|0)+24+2>>1]|0;i=n+i+((f<<16>>19)*o|0)+(h<<16>>16>>3)|0;j=((h|f&65535)&7)==0;do if((f|0)==(c[e+2144+(k*28|0)+24>>2]|0))if(j){D=i;z=D;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;D=D+4|0;D=d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24;C=g;y=C;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;C=C+4|0;a[C>>0]=D;a[C+1>>0]=D>>8;a[C+2>>0]=D>>16;a[C+3>>0]=D>>24;C=i+o|0;D=g+o|0;y=C;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=D;A=B;a[A>>0]=z;a[A+1>>0]=z>>8;a[A+2>>0]=z>>16;a[A+3>>0]=z>>24;B=B+4|0;a[B>>0]=y;a[B+1>>0]=y>>8;a[B+2>>0]=y>>16;a[B+3>>0]=y>>24;C=C+o|0;D=D+o|0;B=C;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;A=D;z=A;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;A=A+4|0;a[A>>0]=B;a[A+1>>0]=B>>8;a[A+2>>0]=B>>16;a[A+3>>0]=B>>24;C=C+o|0;A=C;A=d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24;C=C+4|0;C=d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24;D=D+o|0;B=D;a[B>>0]=A;a[B+1>>0]=A>>8;a[B+2>>0]=A>>16;a[B+3>>0]=A>>24;D=D+4|0;a[D>>0]=C;a[D+1>>0]=C>>8;a[D+2>>0]=C>>16;a[D+3>>0]=C>>24;break}else{W[c[r>>2]&15](i,o,h&7,f&7,g,o);break}else{if(j){a[g>>0]=a[i>>0]|0;a[g+1>>0]=a[i+1>>0]|0;a[g+2>>0]=a[i+2>>0]|0;a[g+3>>0]=a[i+3>>0]|0;D=g+o|0;C=i+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0}else W[c[q>>2]&15](i,o,h&7,f&7,g,o);h=c[e+2144+(k*28|0)+16>>2]|0;i=m+h|0;g=e+2144+(k*28|0)+24|0;f=b[g>>1]|0;g=b[g+2>>1]|0;h=n+h+((f<<16>>16>>3)*o|0)+(g<<16>>16>>3)|0;if(!((g|f)&7)){a[i>>0]=a[h>>0]|0;a[i+1>>0]=a[h+1>>0]|0;a[i+2>>0]=a[h+2>>0]|0;a[i+3>>0]=a[h+3>>0]|0;D=i+o|0;C=h+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;break}else{W[c[q>>2]&15](h,o,g&7,f&7,i,o);break}}while(0);l=l+2|0}while(l>>>0<20);m=c[e+3028>>2]|0;n=c[e+2908>>2]|0;l=20;do{k=l|1;o=c[p>>2]|0;f=c[e+2144+(l*28|0)+24>>2]|0;i=c[e+2144+(l*28|0)+16>>2]|0;g=m+i|0;h=b[e+2144+(l*28|0)+24+2>>1]|0;i=n+i+((f<<16>>19)*o|0)+(h<<16>>16>>3)|0;j=((h|f&65535)&7)==0;do if((f|0)==(c[e+2144+(k*28|0)+24>>2]|0))if(j){D=i;z=D;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;D=D+4|0;D=d[D>>0]|d[D+1>>0]<<8|d[D+2>>0]<<16|d[D+3>>0]<<24;C=g;y=C;a[y>>0]=z;a[y+1>>0]=z>>8;a[y+2>>0]=z>>16;a[y+3>>0]=z>>24;C=C+4|0;a[C>>0]=D;a[C+1>>0]=D>>8;a[C+2>>0]=D>>16;a[C+3>>0]=D>>24;C=i+o|0;D=g+o|0;y=C;z=y;z=d[z>>0]|d[z+1>>0]<<8|d[z+2>>0]<<16|d[z+3>>0]<<24;y=y+4|0;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=D;A=B;a[A>>0]=z;a[A+1>>0]=z>>8;a[A+2>>0]=z>>16;a[A+3>>0]=z>>24;B=B+4|0;a[B>>0]=y;a[B+1>>0]=y>>8;a[B+2>>0]=y>>16;a[B+3>>0]=y>>24;C=C+o|0;D=D+o|0;B=C;y=B;y=d[y>>0]|d[y+1>>0]<<8|d[y+2>>0]<<16|d[y+3>>0]<<24;B=B+4|0;B=d[B>>0]|d[B+1>>0]<<8|d[B+2>>0]<<16|d[B+3>>0]<<24;A=D;z=A;a[z>>0]=y;a[z+1>>0]=y>>8;a[z+2>>0]=y>>16;a[z+3>>0]=y>>24;A=A+4|0;a[A>>0]=B;a[A+1>>0]=B>>8;a[A+2>>0]=B>>16;a[A+3>>0]=B>>24;C=C+o|0;A=C;A=d[A>>0]|d[A+1>>0]<<8|d[A+2>>0]<<16|d[A+3>>0]<<24;C=C+4|0;C=d[C>>0]|d[C+1>>0]<<8|d[C+2>>0]<<16|d[C+3>>0]<<24;D=D+o|0;B=D;a[B>>0]=A;a[B+1>>0]=A>>8;a[B+2>>0]=A>>16;a[B+3>>0]=A>>24;D=D+4|0;a[D>>0]=C;a[D+1>>0]=C>>8;a[D+2>>0]=C>>16;a[D+3>>0]=C>>24;break}else{W[c[r>>2]&15](i,o,h&7,f&7,g,o);break}else{if(j){a[g>>0]=a[i>>0]|0;a[g+1>>0]=a[i+1>>0]|0;a[g+2>>0]=a[i+2>>0]|0;a[g+3>>0]=a[i+3>>0]|0;D=g+o|0;C=i+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0}else W[c[q>>2]&15](i,o,h&7,f&7,g,o);h=c[e+2144+(k*28|0)+16>>2]|0;i=m+h|0;g=e+2144+(k*28|0)+24|0;f=b[g>>1]|0;g=b[g+2>>1]|0;h=n+h+((f<<16>>16>>3)*o|0)+(g<<16>>16>>3)|0;if(!((g|f)&7)){a[i>>0]=a[h>>0]|0;a[i+1>>0]=a[h+1>>0]|0;a[i+2>>0]=a[h+2>>0]|0;a[i+3>>0]=a[h+3>>0]|0;D=i+o|0;C=h+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;D=D+o|0;C=C+o|0;a[D>>0]=a[C>>0]|0;a[D+1>>0]=a[C+1>>0]|0;a[D+2>>0]=a[C+2>>0]|0;a[D+3>>0]=a[C+3>>0]|0;break}else{W[c[q>>2]&15](h,o,g&7,f&7,i,o);break}}while(0);l=l+2|0}while(l>>>0<24);return}function zc(a){a=a|0;var b=0,d=0;ud((c[a+52>>2]|0)+-1+(0-(c[a+16>>2]|0))|0,127,(c[a>>2]|0)+5|0)|0;d=a+36|0;b=a+20|0;ud((c[a+56>>2]|0)+-1+(0-(c[d>>2]|0))|0,127,(c[b>>2]|0)+5|0)|0;ud((c[a+60>>2]|0)+-1+(0-(c[d>>2]|0))|0,127,(c[b>>2]|0)+5|0)|0;return}function Ac(a,b,e,f,g){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0;m=L;L=L+16|0;h=m;c[a>>2]=b+e;j=a+4|0;c[j>>2]=b;k=a+8|0;c[k>>2]=0;l=a+12|0;c[l>>2]=-8;c[a+16>>2]=255;c[a+20>>2]=f;c[a+24>>2]=g;if(!((b|0)!=0|(e|0)==0)){l=1;L=m;return l|0}i=e<<3;a=32-i|0;if(f){V[f&31](g,b,h,e>>>0<5?e:5);b=h}g=(a|0)>-1;e=a>>31&-1073741824;h=g?a:0;if((i|0)==0&g|(h|0)>24){a=0;b=e|1073741816}else{g=e+(24-h)|0;a=0;f=24;e=c[j>>2]|0;while(1){a=(d[b>>0]|0)<<f|a;e=e+1|0;c[j>>2]=e;f=f+-8|0;if((f|0)<(h|0))break;else b=b+1|0}b=g+1073741824|0}c[k>>2]=a;c[l>>2]=b;l=0;L=m;return l|0}function Bc(a){a=a|0;var b=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;p=L;L=L+16|0;e=p;m=a+4|0;i=c[m>>2]|0;n=a+8|0;b=c[n>>2]|0;o=a+12|0;j=c[o>>2]|0;g=16-j|0;f=(c[a>>2]|0)-i|0;l=f<<3;k=g+8-l|0;h=c[a+20>>2]|0;if(!h)e=i;else V[h&31](c[a+24>>2]|0,i,e,f>>>0<5?f:5);i=(k|0)>-1;a=i?j+1073741824|0:j;h=i?k:0;if(i&(l|0)==0|(g|0)<(h|0)){l=b;m=a;c[n>>2]=l;c[o>>2]=m;L=p;return}f=c[m>>2]|0;while(1){a=a+8|0;b=(d[e>>0]|0)<<g|b;f=f+1|0;c[m>>2]=f;g=g+-8|0;if((g|0)<(h|0))break;else e=e+1|0}c[n>>2]=b;c[o>>2]=a;L=p;return}function Cc(f){f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0;sa=L;L=L+32|0;ma=sa+16|0;oa=sa;v=c[f+5772>>2]|0;pa=f+11680|0;qa=f+11696|0;i=(((c[qa>>2]<<7)+-128|0)>>>8)+1|0;na=f+11692|0;g=c[na>>2]|0;if((g|0)<0){Bc(pa);g=c[na>>2]|0}la=f+11688|0;h=c[la>>2]|0;j=i<<24;if(h>>>0<j>>>0)l=0;else{l=1;i=(c[qa>>2]|0)-i|0;h=h-j|0}j=d[2160+i>>0]|0;i=i<<j;k=h<<j;j=g-j|0;c[la>>2]=k;c[na>>2]=j;c[qa>>2]=i;ja=f+5728|0;c[ja>>2]=l;ka=f+11831|0;a[ka>>0]=0;if(!l){h=i;i=j;g=k}else{h=(((i<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(pa);g=c[la>>2]|0;j=c[na>>2]|0}else g=k;i=h<<24;if(g>>>0<i>>>0)r=0;else{r=128;h=(c[qa>>2]|0)-h|0;g=g-i|0}ia=d[2160+h>>0]|0;i=h<<ia;g=g<<ia;h=j-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=64;i=(c[qa>>2]|0)-i|0;g=g-j|0}ia=d[2160+i>>0]|0;i=i<<ia;g=g<<ia;h=h-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)p=0;else{p=32;i=(c[qa>>2]|0)-i|0;g=g-j|0}ia=d[2160+i>>0]|0;i=i<<ia;g=g<<ia;h=h-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=16;i=(c[qa>>2]|0)-i|0;g=g-j|0}ia=d[2160+i>>0]|0;i=i<<ia;g=g<<ia;h=h-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)n=0;else{n=8;i=(c[qa>>2]|0)-i|0;g=g-j|0}ia=d[2160+i>>0]|0;i=i<<ia;g=g<<ia;h=h-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=4;i=(c[qa>>2]|0)-i|0;g=g-j|0}ia=d[2160+i>>0]|0;i=i<<ia;g=g<<ia;h=h-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[qa>>2]|0)-i|0;g=g-j|0}ia=d[2160+i>>0]|0;i=i<<ia;g=g<<ia;h=h-ia|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;ia=i<<ha;g=g<<ha;i=h-ha|0;c[la>>2]=g;c[na>>2]=i;c[qa>>2]=ia;a[ka>>0]=q|r|p|o|n|m|l|k;h=ia}ia=f+5700|0;if(c[ia>>2]|0){h=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);g=c[la>>2]|0;i=c[na>>2]|0}j=h<<24;if(g>>>0<j>>>0)r=0;else{r=128;h=(c[qa>>2]|0)-h|0;g=g-j|0}ga=d[2160+h>>0]|0;ha=h<<ga;g=g<<ga;h=i-ga|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=ha;i=(((ha<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=64;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)p=0;else{p=32;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=16;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)n=0;else{n=8;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=4;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[qa>>2]|0)-i|0;g=g-j|0}j=d[2160+i>>0]|0;ha=i<<j;g=g<<j;j=h-j|0;c[la>>2]=g;c[na>>2]=j;c[qa>>2]=ha;a[f+11828>>0]=q|r|p|o|n|m|l|k;h=(((ha<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(pa);g=c[la>>2]|0;j=c[na>>2]|0}i=h<<24;if(g>>>0<i>>>0)r=0;else{r=128;h=(c[qa>>2]|0)-h|0;g=g-i|0}ha=d[2160+h>>0]|0;i=h<<ha;g=g<<ha;h=j-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=64;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)p=0;else{p=32;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=16;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)n=0;else{n=8;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=4;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[qa>>2]|0)-i|0;g=g-j|0}j=d[2160+i>>0]|0;ha=i<<j;g=g<<j;j=h-j|0;c[la>>2]=g;c[na>>2]=j;c[qa>>2]=ha;a[f+11829>>0]=q|r|p|o|n|m|l|k;h=(((ha<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(pa);g=c[la>>2]|0;j=c[na>>2]|0}i=h<<24;if(g>>>0<i>>>0)s=0;else{s=128;h=(c[qa>>2]|0)-h|0;g=g-i|0}ha=d[2160+h>>0]|0;i=h<<ha;g=g<<ha;h=j-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)r=0;else{r=64;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)q=0;else{q=32;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)p=0;else{p=16;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=8;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)n=0;else{n=4;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=2;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=1;i=(c[qa>>2]|0)-i|0;g=g-j|0}k=d[2160+i>>0]|0;ha=i<<k;j=g<<k;k=h-k|0;c[la>>2]=j;c[na>>2]=k;c[qa>>2]=ha;a[f+11830>>0]=r|s|q|p|o|n|m|l;g=(((ha<<7)+-128|0)>>>8)+1|0;if((k|0)<0){Bc(pa);j=c[la>>2]|0;k=c[na>>2]|0}h=g<<24;if(j>>>0<h>>>0)l=1;else{l=0;g=(c[qa>>2]|0)-g|0;j=j-h|0}ha=d[2160+g>>0]|0;i=g<<ha;h=k-ha|0;c[la>>2]=j<<ha;c[na>>2]=h;c[qa>>2]=i;if(l)g=i;else{g=0;while(1){j=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);h=c[na>>2]|0}i=c[la>>2]|0;k=j<<24;if(i>>>0<k>>>0)s=0;else{s=128;j=(c[qa>>2]|0)-j|0;i=i-k|0}ha=d[2160+j>>0]|0;l=j<<ha;i=i<<ha;j=h-ha|0;c[la>>2]=i;c[na>>2]=j;c[qa>>2]=l;l=(((l<<7)+-128|0)>>>8)+1|0;if((j|0)<0){Bc(pa);h=c[la>>2]|0;j=c[na>>2]|0}else h=i;k=l<<24;if(h>>>0<k>>>0){r=0;i=l}else{r=64;i=(c[qa>>2]|0)-l|0;h=h-k|0}ga=d[2160+i>>0]|0;ha=i<<ga;h=h<<ga;i=j-ga|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=ha;j=(((ha<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)q=0;else{q=32;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)p=0;else{p=16;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)o=0;else{o=8;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)n=0;else{n=4;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)m=0;else{m=2;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)l=0;else{l=1;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;c[la>>2]=h<<ha;c[na>>2]=i-ha;c[qa>>2]=j<<ha;a[f+10323+g>>0]=r|s|q|p|o|n|m|l;g=g+1|0;if((g|0)==4)break;i=c[qa>>2]|0;h=c[na>>2]|0}g=c[qa>>2]|0;h=c[na>>2]|0}g=(((g<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);h=c[na>>2]|0}j=c[la>>2]|0;i=g<<24;if(j>>>0<i>>>0)l=1;else{l=0;g=(c[qa>>2]|0)-g|0;j=j-i|0}ha=d[2160+g>>0]|0;k=g<<ha;i=h-ha|0;c[la>>2]=j<<ha;c[na>>2]=i;c[qa>>2]=k;a:do if(!l){g=0;h=k;while(1){j=(((h<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);i=c[na>>2]|0}h=c[la>>2]|0;k=j<<24;if(h>>>0<k>>>0)s=0;else{s=128;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)r=0;else{r=64;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)q=0;else{q=32;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)p=0;else{p=16;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)o=0;else{o=8;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)n=0;else{n=4;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)m=0;else{m=2;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;j=j<<ha;h=h<<ha;i=i-ha|0;c[la>>2]=h;c[na>>2]=i;c[qa>>2]=j;j=(((j<<7)+-128|0)>>>8)+1|0;if((i|0)<0){Bc(pa);h=c[la>>2]|0;i=c[na>>2]|0}k=j<<24;if(h>>>0<k>>>0)l=0;else{l=1;j=(c[qa>>2]|0)-j|0;h=h-k|0}ha=d[2160+j>>0]|0;c[la>>2]=h<<ha;c[na>>2]=i-ha;c[qa>>2]=j<<ha;a[f+10327+g>>0]=r|s|q|p|o|n|m|l;g=g+1|0;if((g|0)==3)break a;h=c[qa>>2]|0;i=c[na>>2]|0}}while(0);r=0;do{u=f+11389+(r*19|0)+19|0;s=3472+(r*19|0)|0;t=f+11389+(r*19|0)|0;do{i=((((c[qa>>2]|0)+-1|0)*(d[s>>0]|0)|0)>>>8)+1|0;s=s+1|0;g=c[na>>2]|0;if((g|0)<0){Bc(pa);g=c[na>>2]|0}h=c[la>>2]|0;j=i<<24;if(h>>>0<j>>>0)l=1;else{l=0;i=(c[qa>>2]|0)-i|0;h=h-j|0}k=d[2160+i>>0]|0;i=i<<k;j=h<<k;k=g-k|0;c[la>>2]=j;c[na>>2]=k;c[qa>>2]=i;if(!l){h=(((i<<7)+-128|0)>>>8)+1|0;if((k|0)<0){Bc(pa);g=c[la>>2]|0;k=c[na>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)q=0;else{q=64;h=(c[qa>>2]|0)-h|0;g=g-i|0}ha=d[2160+h>>0]|0;i=h<<ha;g=g<<ha;h=k-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)p=0;else{p=32;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)o=0;else{o=16;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)n=0;else{n=8;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)m=0;else{m=4;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)l=0;else{l=2;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;i=i<<ha;g=g<<ha;h=h-ha|0;c[la>>2]=g;c[na>>2]=h;c[qa>>2]=i;i=(((i<<7)+-128|0)>>>8)+1|0;if((h|0)<0){Bc(pa);g=c[la>>2]|0;h=c[na>>2]|0}j=i<<24;if(g>>>0<j>>>0)k=0;else{k=1;i=(c[qa>>2]|0)-i|0;g=g-j|0}ha=d[2160+i>>0]|0;c[la>>2]=g<<ha;c[na>>2]=h-ha;c[qa>>2]=i<<ha;ha=p|q|o|n|m|l|k;a[t>>0]=(ha|0)==0?1:ha<<1&255}t=t+1|0}while(t>>>0<u>>>0);r=r+1|0}while((r|0)!=2)}J=f+3192|0;c[J>>2]=0;K=f+5716|0;h=c[K>>2]|0;i=(h<<7)+-128|0;M=f+3196|0;c[M>>2]=i;N=f+5720|0;g=c[N>>2]|0;O=(g<<7)+-128|0;if((h|0)<=0){L=sa;return}P=f+3184|0;Q=f+3188|0;R=f+3149|0;S=f+3148|0;T=f+3152|0;U=f+5724|0;V=f+11828|0;W=f+3153|0;X=f+3154|0;Y=f+3092|0;Z=f+11829|0;_=oa+8|0;$=oa+4|0;aa=ma+12|0;ba=ma+8|0;ca=ma+4|0;da=f+11830|0;ea=$+2|0;fa=oa+4|0;ga=f+11389|0;ha=f+11408|0;k=v;I=0;j=0;while(1){c[P>>2]=0;c[Q>>2]=O;if((g|0)>0){G=k;H=0;do{do if(!(a[R>>0]|0)){if(!(c[ia>>2]|0))a[G+11>>0]=0}else if(a[S>>0]|0){i=((((c[qa>>2]|0)+-1|0)*(d[T>>0]|0)|0)>>>8)+1|0;g=c[na>>2]|0;if((g|0)<0){Bc(pa);g=c[na>>2]|0}h=c[la>>2]|0;j=i<<24;if(h>>>0<j>>>0)l=1;else{l=0;i=(c[qa>>2]|0)-i|0;h=h-j|0}k=d[2160+i>>0]|0;i=i<<k;j=h<<k;k=g-k|0;c[la>>2]=j;c[na>>2]=k;c[qa>>2]=i;if(l){h=(((i+-1|0)*(d[W>>0]|0)|0)>>>8)+1|0;if((k|0)<0){Bc(pa);g=c[la>>2]|0;k=c[na>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)j=0;else{j=1;h=(c[qa>>2]|0)-h|0;g=g-i|0}F=d[2160+h>>0]|0;c[la>>2]=g<<F;c[na>>2]=k-F;c[qa>>2]=h<<F;a[G+11>>0]=j;break}else{h=(((i+-1|0)*(d[X>>0]|0)|0)>>>8)+1|0;if((k|0)<0){Bc(pa);g=c[la>>2]|0;k=c[na>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)j=2;else{j=3;h=(c[qa>>2]|0)-h|0;g=g-i|0}F=d[2160+h>>0]|0;c[la>>2]=g<<F;c[na>>2]=k-F;c[qa>>2]=h<<F;a[G+11>>0]=j;break}}while(0);if(!(c[ja>>2]|0))g=0;else{i=((((c[qa>>2]|0)+-1|0)*(d[ka>>0]|0)|0)>>>8)+1|0;g=c[na>>2]|0;if((g|0)<0){Bc(pa);k=c[na>>2]|0}else k=g;h=c[la>>2]|0;j=i<<24;if(h>>>0<j>>>0)g=0;else{g=1;i=(c[qa>>2]|0)-i|0;h=h-j|0}F=d[2160+i>>0]|0;c[la>>2]=h<<F;c[na>>2]=k-F;c[qa>>2]=i<<F}a[G+9>>0]=g;F=G+3|0;a[F>>0]=0;do if(!(c[ia>>2]|0)){m=c[U>>2]|0;a[G+2>>0]=0;h=0;g=c[qa>>2]|0;i=c[na>>2]|0;do{l=h<<24>>24;g=(((g+-1|0)*(d[6081+(l>>1)>>0]|0)|0)>>>8)+1|0;if((i|0)<0){Bc(pa);i=c[na>>2]|0}h=c[la>>2]|0;j=g<<24;if(h>>>0<j>>>0)k=0;else{k=1;g=(c[qa>>2]|0)-g|0;h=h-j|0}E=d[2160+g>>0]|0;g=g<<E;i=i-E|0;c[la>>2]=h<<E;c[na>>2]=i;c[qa>>2]=g;h=a[6108+(k+l)>>0]|0}while(h<<24>>24>0);E=0-(h<<24>>24)|0;a[G>>0]=E;if((E&255|0)==4){a[F>>0]=1;p=0-m|0;q=G+(p*76|0)|0;r=G+-76|0;n=0;do{b:do if(n>>>0>3)m=c[G+12+(n<<2)+-16>>2]|0;else switch(a[q>>0]|0){case 4:{m=c[G+(p*76|0)+12+(n<<2)+48>>2]|0;break b}case 3:{m=1;break b}case 1:{m=2;break b}case 2:{m=3;break b}default:{m=0;break b}}while(0);c:do if(!(n&3))switch(a[r>>0]|0){case 4:{o=c[G+-64+(n<<2)+12>>2]|0;break c}case 3:{o=1;break c}case 1:{o=2;break c}case 2:{o=3;break c}default:{o=0;break c}}else o=c[G+12+(n<<2)+-4>>2]|0;while(0);g=0;i=c[qa>>2]|0;j=c[na>>2]|0;do{l=g<<24>>24;g=(((i+-1|0)*(d[(l>>1)+(96+(m*90|0)+(o*9|0))>>0]|0)|0)>>>8)+1|0;if((j|0)<0){Bc(pa);j=c[na>>2]|0}h=c[la>>2]|0;i=g<<24;if(h>>>0<i>>>0)k=0;else{k=1;g=(c[qa>>2]|0)-g|0;h=h-i|0}F=d[2160+g>>0]|0;i=g<<F;j=j-F|0;c[la>>2]=h<<F;c[na>>2]=j;c[qa>>2]=i;g=a[1008+(k+l)>>0]|0}while(g<<24>>24>0);c[G+12+(n<<2)>>2]=0-(g<<24>>24);n=n+1|0}while((n|0)!=16);i=c[na>>2]|0;g=c[qa>>2]|0}h=0;do{l=h<<24>>24;g=(((g+-1|0)*(d[6088+(l>>1)>>0]|0)|0)>>>8)+1|0;if((i|0)<0){Bc(pa);i=c[na>>2]|0}h=c[la>>2]|0;j=g<<24;if(h>>>0<j>>>0)k=0;else{k=1;g=(c[qa>>2]|0)-g|0;h=h-j|0}F=d[2160+g>>0]|0;g=g<<F;i=i-F|0;c[la>>2]=h<<F;c[na>>2]=i;c[qa>>2]=g;h=a[6116+(k+l)>>0]|0}while(h<<24>>24>0);a[G+1>>0]=0-(h&255)}else{i=((((c[qa>>2]|0)+-1|0)*(d[V>>0]|0)|0)>>>8)+1|0;g=c[na>>2]|0;if((g|0)<0){Bc(pa);g=c[na>>2]|0}h=c[la>>2]|0;j=i<<24;if(h>>>0<j>>>0)k=0;else{k=1;i=(c[qa>>2]|0)-i|0;h=h-j|0}l=d[2160+i>>0]|0;i=i<<l;j=h<<l;l=g-l|0;c[la>>2]=j;c[na>>2]=l;c[qa>>2]=i;m=G+2|0;a[m>>0]=k;if(!(k<<24>>24)){c[G+4>>2]=0;h=0;g=c[qa>>2]|0;i=c[na>>2]|0;do{l=h<<24>>24;g=(((g+-1|0)*(d[(l>>1)+(f+10323)>>0]|0)|0)>>>8)+1|0;if((i|0)<0){Bc(pa);j=c[na>>2]|0}else j=i;h=c[la>>2]|0;i=g<<24;if(h>>>0<i>>>0)k=0;else{k=1;g=(c[qa>>2]|0)-g|0;h=h-i|0}E=d[2160+g>>0]|0;g=g<<E;i=j-E|0;c[la>>2]=h<<E;c[na>>2]=i;c[qa>>2]=g;h=a[6100+(k+l)>>0]|0}while(h<<24>>24>0);E=0-(h<<24>>24)|0;a[G>>0]=E;if((E&255|0)==4){a[F>>0]=1;m=0;h=i;do{i=0;do{l=i<<24>>24;g=(((g+-1|0)*(d[(l>>1)+(f+10314)>>0]|0)|0)>>>8)+1|0;if((h|0)<0){Bc(pa);h=c[na>>2]|0}i=c[la>>2]|0;j=g<<24;if(i>>>0<j>>>0)k=0;else{k=1;g=(c[qa>>2]|0)-g|0;i=i-j|0}F=d[2160+g>>0]|0;g=g<<F;h=h-F|0;c[la>>2]=i<<F;c[na>>2]=h;c[qa>>2]=g;i=a[1008+(k+l)>>0]|0}while(i<<24>>24>0);c[G+12+(m<<2)>>2]=0-(i<<24>>24);m=m+1|0;g=c[qa>>2]|0;h=c[na>>2]|0}while((m|0)!=16)}else h=i;i=0;do{l=i<<24>>24;g=(((g+-1|0)*(d[(l>>1)+(f+10327)>>0]|0)|0)>>>8)+1|0;if((h|0)<0){Bc(pa);h=c[na>>2]|0}i=c[la>>2]|0;j=g<<24;if(i>>>0<j>>>0)k=0;else{k=1;g=(c[qa>>2]|0)-g|0;i=i-j|0}F=d[2160+g>>0]|0;g=g<<F;h=h-F|0;c[la>>2]=i<<F;c[na>>2]=h;c[qa>>2]=g;i=a[6116+(k+l)>>0]|0}while(i<<24>>24>0);a[G+1>>0]=0-(i&255);break}C=0-(c[Y>>2]|0)|0;D=G+(C*76|0)|0;q=D+-76|0;E=G+10|0;a[E>>0]=0;h=(((i+-1|0)*(d[Z>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(pa);g=c[la>>2]|0;l=c[na>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)j=1;else{j=0;h=(c[qa>>2]|0)-h|0;g=g-i|0}i=d[2160+h>>0]|0;h=h<<i;g=g<<i;i=l-i|0;c[la>>2]=g;c[na>>2]=i;c[qa>>2]=h;if(!j){h=(((h+-1|0)*(d[da>>0]|0)|0)>>>8)+1|0;if((i|0)<0){Bc(pa);g=c[la>>2]|0;i=c[na>>2]|0}j=h<<24;if(g>>>0<j>>>0)k=2;else{k=3;h=(c[qa>>2]|0)-h|0;g=g-j|0}B=d[2160+h>>0]|0;c[la>>2]=g<<B;c[na>>2]=i-B;c[qa>>2]=h<<B;a[m>>0]=k}c[_>>2]=0;c[$>>2]=0;c[oa>>2]=0;c[ma>>2]=0;c[ma+4>>2]=0;c[ma+8>>2]=0;c[ma+12>>2]=0;g=a[G+(C*76|0)+2>>0]|0;if(!(g<<24>>24)){h=ma;g=oa;i=0}else{h=c[G+(C*76|0)+4>>2]|0;do if(!h){h=ma;g=oa}else{c[$>>2]=h;if((c[f+9172+(d[m>>0]<<2)>>2]|0)==(c[f+9172+((g&255)<<2)>>2]|0)){h=ca;g=$;break}b[$>>1]=0-h;b[ea>>1]=0-(h>>>16);h=ca;g=$}while(0);i=(c[h>>2]|0)+2|0;c[h>>2]=i}j=a[G+-74>>0]|0;do if(j<<24>>24){k=c[G+-72>>2]|0;if(!k){c[ma>>2]=(c[ma>>2]|0)+2;break}B=k>>>16;j=(c[f+9172+(d[m>>0]<<2)>>2]|0)==(c[f+9172+((j&255)<<2)>>2]|0);j=(j?B:0-B|0)<<16|(j?k:0-k|0)&65535;if((j|0)!=(c[g>>2]|0)){g=g+4|0;c[g>>2]=j;i=h+4|0;h=i;i=c[i>>2]|0}c[h>>2]=i+2}while(0);i=a[q+2>>0]|0;do if(i<<24>>24){j=c[q+4>>2]|0;if(!j){c[ma>>2]=(c[ma>>2]|0)+1;break}B=j>>>16;i=(c[f+9172+(d[m>>0]<<2)>>2]|0)==(c[f+9172+((i&255)<<2)>>2]|0);i=(i?B:0-B|0)<<16|(i?j:0-j|0)&65535;if((i|0)!=(c[g>>2]|0)){g=g+4|0;c[g>>2]=i;h=h+4|0}c[h>>2]=(c[h>>2]|0)+1}while(0);p=c[ma>>2]|0;j=((((c[qa>>2]|0)+-1|0)*(c[4880+(p<<4)>>2]|0)|0)>>>8)+1|0;h=c[na>>2]|0;if((h|0)<0){Bc(pa);h=c[na>>2]|0}i=c[la>>2]|0;k=j<<24;if(i>>>0<k>>>0)l=1;else{l=0;j=(c[qa>>2]|0)-j|0;i=i-k|0}k=d[2160+j>>0]|0;j=j<<k;m=i<<k;k=h-k|0;c[la>>2]=m;c[na>>2]=k;c[qa>>2]=j;do if(l){a[G>>0]=7;c[G+4>>2]=0}else{i=c[fa>>2]|0;g=(c[ca>>2]|0)+(((c[aa>>2]|0)>0?(c[g>>2]|0)==(i|0):0)&1)|0;c[ca>>2]=g;h=c[ba>>2]|0;if((h|0)>(g|0)){c[ca>>2]=h;c[ba>>2]=g;n=c[_>>2]|0;c[fa>>2]=n;c[_>>2]=i;o=h;h=g}else{o=g;n=i}i=(((c[4880+(o<<4)+4>>2]|0)*(j+-1|0)|0)>>>8)+1|0;if((k|0)<0){Bc(pa);g=c[la>>2]|0;k=c[na>>2]|0}else g=m;j=i<<24;if(g>>>0<j>>>0)l=1;else{l=0;i=(c[qa>>2]|0)-i|0;g=g-j|0}j=d[2160+i>>0]|0;i=i<<j;g=g<<j;j=k-j|0;c[la>>2]=g;c[na>>2]=j;c[qa>>2]=i;if(l){a[G>>0]=5;j=G+4|0;c[j>>2]=n;i=j+2|0;h=n>>16;g=(c[P>>2]|0)+-128|0;if((g|0)<=(h|0)){g=(c[Q>>2]|0)+128|0;if((g|0)<(h|0))ra=438}else ra=438;if((ra|0)==438){ra=0;b[i>>1]=g}h=n<<16>>16;g=(c[J>>2]|0)+-128|0;if((g|0)>(h|0)){b[j>>1]=g;break}g=(c[M>>2]|0)+128|0;if((g|0)>=(h|0))break;b[j>>1]=g;break}h=(((c[4880+(h<<4)+8>>2]|0)*(i+-1|0)|0)>>>8)+1|0;if((j|0)<0){Bc(pa);g=c[la>>2]|0;j=c[na>>2]|0}i=h<<24;if(g>>>0<i>>>0)k=1;else{k=0;h=(c[qa>>2]|0)-h|0;g=g-i|0}B=d[2160+h>>0]|0;m=h<<B;n=g<<B;j=j-B|0;c[la>>2]=n;c[na>>2]=j;c[qa>>2]=m;if(k){a[G>>0]=6;i=c[_>>2]|0;k=G+4|0;c[k>>2]=i;j=k+2|0;h=i>>16;g=(c[P>>2]|0)+-128|0;if((g|0)<=(h|0)){g=(c[Q>>2]|0)+128|0;if((g|0)<(h|0))ra=431}else ra=431;if((ra|0)==431){ra=0;b[j>>1]=g}h=i<<16>>16;g=(c[J>>2]|0)+-128|0;if((g|0)>(h|0)){b[k>>1]=g;break}g=(c[M>>2]|0)+128|0;if((g|0)>=(h|0))break;b[k>>1]=g;break}y=(c[J>>2]|0)+-128|0;z=(c[M>>2]|0)+128|0;A=(c[Q>>2]|0)+128|0;B=(c[P>>2]|0)+-128|0;p=oa+(((o|0)>=(p|0)&1)<<2)|0;i=p+2|0;g=b[i>>1]|0;h=g<<16>>16;do if((B|0)>(h|0)){g=B&65535;ra=375}else{if((A|0)>=(h|0)){o=g;break}g=A&65535;ra=375}while(0);if((ra|0)==375){ra=0;b[i>>1]=g;o=g}g=b[p>>1]|0;h=g<<16>>16;if((y|0)<=(h|0))if((z|0)<(h|0)){g=z;ra=378}else l=g;else{g=y;ra=378}if((ra|0)==378){ra=0;l=g&65535;b[p>>1]=l}x=G+-76|0;h=((a[x>>0]|0)==9&1)+((a[D>>0]|0)==9&1)<<1|(a[q>>0]|0)==9;c[aa>>2]=h;h=(((c[4880+(h<<4)+12>>2]|0)*(m+-1|0)|0)>>>8)+1|0;if((j|0)<0){Bc(pa);g=c[la>>2]|0;j=c[na>>2]|0}else g=n;i=h<<24;if(g>>>0<i>>>0)k=1;else{k=0;h=(c[qa>>2]|0)-h|0;g=g-i|0}i=d[2160+h>>0]|0;h=h<<i;g=g<<i;i=j-i|0;c[la>>2]=g;c[na>>2]=i;c[qa>>2]=h;if(k){C=G+4|0;b[C>>1]=(Dc(pa,ga)|0)<<1;F=(Dc(pa,ha)|0)<<1;D=(e[C>>1]|0)+(l&65535)|0;b[C>>1]=D;F=F+(o&65535)|0;b[C+2>>1]=F;F=F<<16>>16;D=D<<16>>16;a[E>>0]=((D|0)>(z|0)|((D|0)<(y|0)|((F|0)<(B|0)|(F|0)>(A|0))))&1;a[G>>0]=8;break}v=c[p>>2]|0;w=v>>>16;h=(((h*110|0)+-110|0)>>>8)+1|0;if((i|0)<0){Bc(pa);g=c[la>>2]|0;j=c[na>>2]|0}else j=i;i=h<<24;if(g>>>0<i>>>0)k=1;else{k=0;h=(c[qa>>2]|0)-h|0;g=g-i|0}i=d[2160+h>>0]|0;h=h<<i;g=g<<i;i=j-i|0;c[la>>2]=g;c[na>>2]=i;c[qa>>2]=h;do if(k){k=3;q=16}else{h=(((h*111|0)+-111|0)>>>8)+1|0;if((i|0)<0){Bc(pa);g=c[la>>2]|0;i=c[na>>2]|0}j=h<<24;if(g>>>0<j>>>0)k=1;else{k=0;h=(c[qa>>2]|0)-h|0;g=g-j|0}u=d[2160+h>>0]|0;h=h<<u;g=g<<u;i=i-u|0;c[la>>2]=g;c[na>>2]=i;c[qa>>2]=h;if(k){k=2;q=4;break}h=(((h*150|0)+-150|0)>>>8)+1|0;if((i|0)<0){Bc(pa);g=c[la>>2]|0;i=c[na>>2]|0}j=h<<24;if(g>>>0<j>>>0)k=0;else{k=1;h=(c[qa>>2]|0)-h|0;g=g-j|0}q=d[2160+h>>0]|0;c[la>>2]=g<<q;c[na>>2]=i-q;c[qa>>2]=h<<q;q=2}while(0);s=G+-72|0;t=G+(C*76|0)+4|0;u=6474+k|0;r=0;do{h=d[4816+(k<<4)+r>>0]|0;if(!(h&3))g=(a[x>>0]|0)==9?G+-64+(h<<2)+12|0:s;else g=G+12+(h<<2)+-4|0;n=c[g>>2]|0;if(!(h&252))g=(a[D>>0]|0)==9?G+(C*76|0)+12+(h<<2)+48|0:t;else g=G+12+(h<<2)+-16|0;p=c[g>>2]|0;o=((n|0)==0&1)<<1|(n|0)==(p|0)|((p|0)==0&1)<<2;i=((((c[qa>>2]|0)+-1|0)*(d[4720+(o*3|0)>>0]|0)|0)>>>8)+1|0;g=c[na>>2]|0;if((g|0)<0){Bc(pa);g=c[na>>2]|0}h=c[la>>2]|0;j=i<<24;if(h>>>0<j>>>0)m=1;else{m=0;i=(c[qa>>2]|0)-i|0;h=h-j|0}l=d[2160+i>>0]|0;i=i<<l;j=h<<l;l=g-l|0;c[la>>2]=j;c[na>>2]=l;c[qa>>2]=i;do if(m){h=n&65535;g=n>>>16&65535}else{h=(((i+-1|0)*(d[4720+(o*3|0)+1>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(pa);g=c[la>>2]|0;l=c[na>>2]|0}else g=j;i=h<<24;if(g>>>0<i>>>0)j=1;else{j=0;h=(c[qa>>2]|0)-h|0;g=g-i|0}n=d[2160+h>>0]|0;h=h<<n;g=g<<n;l=l-n|0;c[la>>2]=g;c[na>>2]=l;c[qa>>2]=h;if(j){h=p&65535;g=p>>>16&65535;break}h=(((h+-1|0)*(d[4720+(o*3|0)+2>>0]|0)|0)>>>8)+1|0;if((l|0)<0){Bc(pa);g=c[la>>2]|0;l=c[na>>2]|0}i=h<<24;if(g>>>0<i>>>0)j=1;else{j=0;h=(c[qa>>2]|0)-h|0;g=g-i|0}p=d[2160+h>>0]|0;c[la>>2]=g<<p;c[na>>2]=l-p;c[qa>>2]=h<<p;if(j){h=0;g=0;break}h=((Dc(pa,ga)|0)<<1)+v&65535;g=((Dc(pa,ha)|0)<<1)+w&65535}while(0);i=g<<16>>16;p=h<<16>>16;a[E>>0]=((z|0)<(p|0)|((B|0)>(i|0)|(A|0)<(i|0)|(y|0)>(p|0)))&1|d[E>>0];p=d[u>>0]|0;i=(g&65535)<<16|h&65535;g=p;h=(r*p|0)+(4752+(k<<4))|0;while(1){c[G+12+(d[h>>0]<<2)>>2]=i;g=g+-1|0;if(!g)break;else h=h+1|0}r=r+1|0}while(r>>>0<q>>>0);a[G+8>>0]=k;c[G+4>>2]=c[G+72>>2];a[G>>0]=9;a[F>>0]=1}while(0)}while(0);c[P>>2]=(c[P>>2]|0)+-128;c[Q>>2]=(c[Q>>2]|0)+-128;G=G+76|0;H=H+1|0;g=c[N>>2]|0}while((H|0)<(g|0));k=G;j=c[J>>2]|0;i=c[M>>2]|0;h=c[K>>2]|0}j=j+-128|0;c[J>>2]=j;i=i+-128|0;c[M>>2]=i;I=I+1|0;if((I|0)>=(h|0))break;else k=k+76|0}L=sa;return}function Dc(b,e){b=b|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;u=b+16|0;h=((((c[u>>2]|0)+-1|0)*(d[e>>0]|0)|0)>>>8)+1|0;t=b+12|0;f=c[t>>2]|0;if((f|0)<0){Bc(b);f=c[t>>2]|0}s=b+8|0;g=c[s>>2]|0;i=h<<24;if(g>>>0<i>>>0)k=1;else{k=0;h=(c[u>>2]|0)-h|0;g=g-i|0}r=d[2160+h>>0]|0;j=h<<r;h=g<<r;g=f-r|0;c[s>>2]=h;c[t>>2]=g;c[u>>2]=j;do if(k){l=e+2|0;i=0;f=j;do{k=i<<24>>24;f=(((f+-1|0)*(d[l+(k>>1)>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);h=c[s>>2]|0;g=c[t>>2]|0}i=f<<24;if(h>>>0<i>>>0)j=0;else{j=1;f=(c[u>>2]|0)-f|0;h=h-i|0}i=d[2160+f>>0]|0;f=f<<i;h=h<<i;g=g-i|0;c[s>>2]=h;c[t>>2]=g;c[u>>2]=f;i=a[6122+(j+k)>>0]|0}while(i<<24>>24>0);j=0-(i<<24>>24)|0}else{j=(((j+-1|0)*(d[e+9>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}else f=h;i=j<<24;if(f>>>0<i>>>0){r=0;h=j}else{r=1;h=(c[u>>2]|0)-j|0;f=f-i|0}q=d[2160+h>>0]|0;h=h<<q;f=f<<q;g=g-q|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+10>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)q=0;else{q=2;h=(c[u>>2]|0)-h|0;f=f-i|0}p=d[2160+h>>0]|0;h=h<<p;f=f<<p;g=g-p|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+11>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)p=0;else{p=4;h=(c[u>>2]|0)-h|0;f=f-i|0}o=d[2160+h>>0]|0;h=h<<o;f=f<<o;g=g-o|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+18>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)o=0;else{o=512;h=(c[u>>2]|0)-h|0;f=f-i|0}n=d[2160+h>>0]|0;h=h<<n;f=f<<n;g=g-n|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+17>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)n=0;else{n=256;h=(c[u>>2]|0)-h|0;f=f-i|0}m=d[2160+h>>0]|0;h=h<<m;f=f<<m;g=g-m|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+16>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)m=0;else{m=128;h=(c[u>>2]|0)-h|0;f=f-i|0}l=d[2160+h>>0]|0;h=h<<l;f=f<<l;g=g-l|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+15>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)l=0;else{l=64;h=(c[u>>2]|0)-h|0;f=f-i|0}k=d[2160+h>>0]|0;h=h<<k;f=f<<k;g=g-k|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=h;h=(((h+-1|0)*(d[e+14>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}i=h<<24;if(f>>>0<i>>>0)k=0;else{k=32;h=(c[u>>2]|0)-h|0;f=f-i|0}j=d[2160+h>>0]|0;i=h<<j;f=f<<j;g=g-j|0;c[s>>2]=f;c[t>>2]=g;c[u>>2]=i;i=(((i+-1|0)*(d[e+13>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);f=c[s>>2]|0;g=c[t>>2]|0}h=i<<24;if(f>>>0<h>>>0){j=0;h=f}else{j=16;i=(c[u>>2]|0)-i|0;h=f-h|0}v=d[2160+i>>0]|0;f=i<<v;h=h<<v;g=g-v|0;c[s>>2]=h;c[t>>2]=g;c[u>>2]=f;k=j+(k+(l+(m|(n|(o|(p|(q|r)))))))|0;if(k&65520){f=(((f+-1|0)*(d[e+12>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);g=c[t>>2]|0;h=c[s>>2]|0}i=f<<24;if(h>>>0<i>>>0)j=1;else{j=0;f=(c[u>>2]|0)-f|0;h=h-i|0}v=d[2160+f>>0]|0;f=f<<v;h=h<<v;g=g-v|0;c[s>>2]=h;c[t>>2]=g;c[u>>2]=f;if(j){j=k;break}}j=k+8|0}while(0);if(!j)return 0;i=(((f+-1|0)*(d[e+1>>0]|0)|0)>>>8)+1|0;if((g|0)<0){Bc(b);h=c[s>>2]|0;g=c[t>>2]|0}f=i<<24;if(h>>>0<f>>>0){b=1;v=i;r=h;e=2160+v|0;e=a[e>>0]|0;e=e&255;v=v<<e;r=r<<e;e=g-e|0;c[s>>2]=r;c[t>>2]=e;c[u>>2]=v;v=0-j|0;v=b?j:v;return v|0}b=0;v=(c[u>>2]|0)-i|0;r=h-f|0;e=2160+v|0;e=a[e>>0]|0;e=e&255;v=v<<e;r=r<<e;e=g-e|0;c[s>>2]=r;c[t>>2]=e;c[u>>2]=v;v=0-j|0;v=b?j:v;return v|0}
function Ec(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;w=L;L=L+16|0;n=w;do if(a>>>0<245){k=a>>>0<11?16:a+11&-8;a=k>>>3;m=c[1686]|0;d=m>>>a;if(d&3|0){b=(d&1^1)+a|0;a=6784+(b<<1<<2)|0;d=a+8|0;e=c[d>>2]|0;f=e+8|0;g=c[f>>2]|0;if((g|0)==(a|0))c[1686]=m&~(1<<b);else{c[g+12>>2]=a;c[d>>2]=g}v=b<<3;c[e+4>>2]=v|3;v=e+v+4|0;c[v>>2]=c[v>>2]|1;v=f;L=w;return v|0}l=c[1688]|0;if(k>>>0>l>>>0){if(d|0){b=2<<a;b=d<<a&(b|0-b);b=(b&0-b)+-1|0;i=b>>>12&16;b=b>>>i;d=b>>>5&8;b=b>>>d;g=b>>>2&4;b=b>>>g;a=b>>>1&2;b=b>>>a;e=b>>>1&1;e=(d|i|g|a|e)+(b>>>e)|0;b=6784+(e<<1<<2)|0;a=b+8|0;g=c[a>>2]|0;i=g+8|0;d=c[i>>2]|0;if((d|0)==(b|0)){a=m&~(1<<e);c[1686]=a}else{c[d+12>>2]=b;c[a>>2]=d;a=m}v=e<<3;h=v-k|0;c[g+4>>2]=k|3;f=g+k|0;c[f+4>>2]=h|1;c[g+v>>2]=h;if(l|0){e=c[1691]|0;b=l>>>3;d=6784+(b<<1<<2)|0;b=1<<b;if(!(a&b)){c[1686]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=e;c[b+12>>2]=e;c[e+8>>2]=b;c[e+12>>2]=d}c[1688]=h;c[1691]=f;v=i;L=w;return v|0}g=c[1687]|0;if(g){d=(g&0-g)+-1|0;f=d>>>12&16;d=d>>>f;e=d>>>5&8;d=d>>>e;h=d>>>2&4;d=d>>>h;i=d>>>1&2;d=d>>>i;j=d>>>1&1;j=c[7048+((e|f|h|i|j)+(d>>>j)<<2)>>2]|0;d=j;i=j;j=(c[j+4>>2]&-8)-k|0;while(1){a=c[d+16>>2]|0;if(!a){a=c[d+20>>2]|0;if(!a)break}h=(c[a+4>>2]&-8)-k|0;f=h>>>0<j>>>0;d=a;i=f?a:i;j=f?h:j}h=i+k|0;if(h>>>0>i>>>0){f=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=c[a>>2]|0;if(!b){a=i+16|0;b=c[a>>2]|0;if(!b){d=0;break}}while(1){e=b+20|0;d=c[e>>2]|0;if(!d){e=b+16|0;d=c[e>>2]|0;if(!d)break;else{b=d;a=e}}else{b=d;a=e}}c[a>>2]=0;d=b}else{d=c[i+8>>2]|0;c[d+12>>2]=b;c[b+8>>2]=d;d=b}while(0);do if(f|0){b=c[i+28>>2]|0;a=7048+(b<<2)|0;if((i|0)==(c[a>>2]|0)){c[a>>2]=d;if(!d){c[1687]=g&~(1<<b);break}}else{v=f+16|0;c[((c[v>>2]|0)==(i|0)?v:f+20|0)>>2]=d;if(!d)break}c[d+24>>2]=f;b=c[i+16>>2]|0;if(b|0){c[d+16>>2]=b;c[b+24>>2]=d}b=c[i+20>>2]|0;if(b|0){c[d+20>>2]=b;c[b+24>>2]=d}}while(0);if(j>>>0<16){v=j+k|0;c[i+4>>2]=v|3;v=i+v+4|0;c[v>>2]=c[v>>2]|1}else{c[i+4>>2]=k|3;c[h+4>>2]=j|1;c[h+j>>2]=j;if(l|0){e=c[1691]|0;b=l>>>3;d=6784+(b<<1<<2)|0;b=1<<b;if(!(b&m)){c[1686]=b|m;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=e;c[b+12>>2]=e;c[e+8>>2]=b;c[e+12>>2]=d}c[1688]=j;c[1691]=h}v=i+8|0;L=w;return v|0}else m=k}else m=k}else m=k}else if(a>>>0<=4294967231){a=a+11|0;k=a&-8;e=c[1687]|0;if(e){f=0-k|0;a=a>>>8;if(a)if(k>>>0>16777215)j=31;else{m=(a+1048320|0)>>>16&8;q=a<<m;i=(q+520192|0)>>>16&4;q=q<<i;j=(q+245760|0)>>>16&2;j=14-(i|m|j)+(q<<j>>>15)|0;j=k>>>(j+7|0)&1|j<<1}else j=0;d=c[7048+(j<<2)>>2]|0;a:do if(!d){d=0;a=0;q=61}else{a=0;i=k<<((j|0)==31?0:25-(j>>>1)|0);g=0;while(1){h=(c[d+4>>2]&-8)-k|0;if(h>>>0<f>>>0)if(!h){a=d;f=0;q=65;break a}else{a=d;f=h}q=c[d+20>>2]|0;d=c[d+16+(i>>>31<<2)>>2]|0;g=(q|0)==0|(q|0)==(d|0)?g:q;if(!d){d=g;q=61;break}else i=i<<1}}while(0);if((q|0)==61){if((d|0)==0&(a|0)==0){a=2<<j;a=(a|0-a)&e;if(!a){m=k;break}m=(a&0-a)+-1|0;h=m>>>12&16;m=m>>>h;g=m>>>5&8;m=m>>>g;i=m>>>2&4;m=m>>>i;j=m>>>1&2;m=m>>>j;d=m>>>1&1;a=0;d=c[7048+((g|h|i|j|d)+(m>>>d)<<2)>>2]|0}if(!d){i=a;h=f}else q=65}if((q|0)==65){g=d;while(1){m=(c[g+4>>2]&-8)-k|0;d=m>>>0<f>>>0;f=d?m:f;a=d?g:a;d=c[g+16>>2]|0;if(!d)d=c[g+20>>2]|0;if(!d){i=a;h=f;break}else g=d}}if(((i|0)!=0?h>>>0<((c[1688]|0)-k|0)>>>0:0)?(l=i+k|0,l>>>0>i>>>0):0){g=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=c[a>>2]|0;if(!b){a=i+16|0;b=c[a>>2]|0;if(!b){b=0;break}}while(1){f=b+20|0;d=c[f>>2]|0;if(!d){f=b+16|0;d=c[f>>2]|0;if(!d)break;else{b=d;a=f}}else{b=d;a=f}}c[a>>2]=0}else{v=c[i+8>>2]|0;c[v+12>>2]=b;c[b+8>>2]=v}while(0);do if(g){a=c[i+28>>2]|0;d=7048+(a<<2)|0;if((i|0)==(c[d>>2]|0)){c[d>>2]=b;if(!b){e=e&~(1<<a);c[1687]=e;break}}else{v=g+16|0;c[((c[v>>2]|0)==(i|0)?v:g+20|0)>>2]=b;if(!b)break}c[b+24>>2]=g;a=c[i+16>>2]|0;if(a|0){c[b+16>>2]=a;c[a+24>>2]=b}a=c[i+20>>2]|0;if(a){c[b+20>>2]=a;c[a+24>>2]=b}}while(0);b:do if(h>>>0<16){v=h+k|0;c[i+4>>2]=v|3;v=i+v+4|0;c[v>>2]=c[v>>2]|1}else{c[i+4>>2]=k|3;c[l+4>>2]=h|1;c[l+h>>2]=h;b=h>>>3;if(h>>>0<256){d=6784+(b<<1<<2)|0;a=c[1686]|0;b=1<<b;if(!(a&b)){c[1686]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=l;c[b+12>>2]=l;c[l+8>>2]=b;c[l+12>>2]=d;break}b=h>>>8;if(b)if(h>>>0>16777215)d=31;else{u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;d=(v+245760|0)>>>16&2;d=14-(t|u|d)+(v<<d>>>15)|0;d=h>>>(d+7|0)&1|d<<1}else d=0;b=7048+(d<<2)|0;c[l+28>>2]=d;a=l+16|0;c[a+4>>2]=0;c[a>>2]=0;a=1<<d;if(!(e&a)){c[1687]=e|a;c[b>>2]=l;c[l+24>>2]=b;c[l+12>>2]=l;c[l+8>>2]=l;break}b=c[b>>2]|0;c:do if((c[b+4>>2]&-8|0)!=(h|0)){e=h<<((d|0)==31?0:25-(d>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(h|0)){b=a;break c}else{e=e<<1;b=a}}c[d>>2]=l;c[l+24>>2]=b;c[l+12>>2]=l;c[l+8>>2]=l;break b}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=l;c[u>>2]=l;c[l+8>>2]=v;c[l+12>>2]=b;c[l+24>>2]=0}while(0);v=i+8|0;L=w;return v|0}else m=k}else m=k}else m=-1;while(0);d=c[1688]|0;if(d>>>0>=m>>>0){b=d-m|0;a=c[1691]|0;if(b>>>0>15){v=a+m|0;c[1691]=v;c[1688]=b;c[v+4>>2]=b|1;c[a+d>>2]=b;c[a+4>>2]=m|3}else{c[1688]=0;c[1691]=0;c[a+4>>2]=d|3;v=a+d+4|0;c[v>>2]=c[v>>2]|1}v=a+8|0;L=w;return v|0}h=c[1689]|0;if(h>>>0>m>>>0){t=h-m|0;c[1689]=t;v=c[1692]|0;u=v+m|0;c[1692]=u;c[u+4>>2]=t|1;c[v+4>>2]=m|3;v=v+8|0;L=w;return v|0}if(!(c[1804]|0)){c[1806]=4096;c[1805]=4096;c[1807]=-1;c[1808]=-1;c[1809]=0;c[1797]=0;c[1804]=n&-16^1431655768;a=4096}else a=c[1806]|0;i=m+48|0;j=m+47|0;g=a+j|0;f=0-a|0;k=g&f;if(k>>>0<=m>>>0){v=0;L=w;return v|0}a=c[1796]|0;if(a|0?(l=c[1794]|0,n=l+k|0,n>>>0<=l>>>0|n>>>0>a>>>0):0){v=0;L=w;return v|0}d:do if(!(c[1797]&4)){d=c[1692]|0;e:do if(d){e=7192;while(1){n=c[e>>2]|0;if(n>>>0<=d>>>0?(n+(c[e+4>>2]|0)|0)>>>0>d>>>0:0)break;a=c[e+8>>2]|0;if(!a){q=128;break e}else e=a}b=g-h&f;if(b>>>0<2147483647){a=vd(b|0)|0;if((a|0)==((c[e>>2]|0)+(c[e+4>>2]|0)|0)){if((a|0)!=(-1|0)){h=b;g=a;q=145;break d}}else{e=a;q=136}}else b=0}else q=128;while(0);do if((q|0)==128){d=vd(0)|0;if((d|0)!=(-1|0)?(b=d,o=c[1805]|0,p=o+-1|0,b=((p&b|0)==0?0:(p+b&0-o)-b|0)+k|0,o=c[1794]|0,p=b+o|0,b>>>0>m>>>0&b>>>0<2147483647):0){n=c[1796]|0;if(n|0?p>>>0<=o>>>0|p>>>0>n>>>0:0){b=0;break}a=vd(b|0)|0;if((a|0)==(d|0)){h=b;g=d;q=145;break d}else{e=a;q=136}}else b=0}while(0);do if((q|0)==136){d=0-b|0;if(!(i>>>0>b>>>0&(b>>>0<2147483647&(e|0)!=(-1|0))))if((e|0)==(-1|0)){b=0;break}else{h=b;g=e;q=145;break d}a=c[1806]|0;a=j-b+a&0-a;if(a>>>0>=2147483647){h=b;g=e;q=145;break d}if((vd(a|0)|0)==(-1|0)){vd(d|0)|0;b=0;break}else{h=a+b|0;g=e;q=145;break d}}while(0);c[1797]=c[1797]|4;q=143}else{b=0;q=143}while(0);if(((q|0)==143?k>>>0<2147483647:0)?(t=vd(k|0)|0,p=vd(0)|0,r=p-t|0,s=r>>>0>(m+40|0)>>>0,!((t|0)==(-1|0)|s^1|t>>>0<p>>>0&((t|0)!=(-1|0)&(p|0)!=(-1|0))^1)):0){h=s?r:b;g=t;q=145}if((q|0)==145){b=(c[1794]|0)+h|0;c[1794]=b;if(b>>>0>(c[1795]|0)>>>0)c[1795]=b;j=c[1692]|0;f:do if(j){b=7192;while(1){a=c[b>>2]|0;d=c[b+4>>2]|0;if((g|0)==(a+d|0)){q=154;break}e=c[b+8>>2]|0;if(!e)break;else b=e}if(((q|0)==154?(u=b+4|0,(c[b+12>>2]&8|0)==0):0)?g>>>0>j>>>0&a>>>0<=j>>>0:0){c[u>>2]=d+h;v=(c[1689]|0)+h|0;t=j+8|0;t=(t&7|0)==0?0:0-t&7;u=j+t|0;t=v-t|0;c[1692]=u;c[1689]=t;c[u+4>>2]=t|1;c[j+v+4>>2]=40;c[1693]=c[1808];break}if(g>>>0<(c[1690]|0)>>>0)c[1690]=g;d=g+h|0;b=7192;while(1){if((c[b>>2]|0)==(d|0)){q=162;break}a=c[b+8>>2]|0;if(!a)break;else b=a}if((q|0)==162?(c[b+12>>2]&8|0)==0:0){c[b>>2]=g;l=b+4|0;c[l>>2]=(c[l>>2]|0)+h;l=g+8|0;l=g+((l&7|0)==0?0:0-l&7)|0;b=d+8|0;b=d+((b&7|0)==0?0:0-b&7)|0;k=l+m|0;i=b-l-m|0;c[l+4>>2]=m|3;g:do if((j|0)==(b|0)){v=(c[1689]|0)+i|0;c[1689]=v;c[1692]=k;c[k+4>>2]=v|1}else{if((c[1691]|0)==(b|0)){v=(c[1688]|0)+i|0;c[1688]=v;c[1691]=k;c[k+4>>2]=v|1;c[k+v>>2]=v;break}a=c[b+4>>2]|0;if((a&3|0)==1){h=a&-8;e=a>>>3;h:do if(a>>>0<256){a=c[b+8>>2]|0;d=c[b+12>>2]|0;if((d|0)==(a|0)){c[1686]=c[1686]&~(1<<e);break}else{c[a+12>>2]=d;c[d+8>>2]=a;break}}else{g=c[b+24>>2]|0;a=c[b+12>>2]|0;do if((a|0)==(b|0)){d=b+16|0;e=d+4|0;a=c[e>>2]|0;if(!a){a=c[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=c[f>>2]|0;if(!e){f=a+16|0;e=c[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}c[d>>2]=0}else{v=c[b+8>>2]|0;c[v+12>>2]=a;c[a+8>>2]=v}while(0);if(!g)break;d=c[b+28>>2]|0;e=7048+(d<<2)|0;do if((c[e>>2]|0)!=(b|0)){v=g+16|0;c[((c[v>>2]|0)==(b|0)?v:g+20|0)>>2]=a;if(!a)break h}else{c[e>>2]=a;if(a|0)break;c[1687]=c[1687]&~(1<<d);break h}while(0);c[a+24>>2]=g;d=b+16|0;e=c[d>>2]|0;if(e|0){c[a+16>>2]=e;c[e+24>>2]=a}d=c[d+4>>2]|0;if(!d)break;c[a+20>>2]=d;c[d+24>>2]=a}while(0);b=b+h|0;f=h+i|0}else f=i;b=b+4|0;c[b>>2]=c[b>>2]&-2;c[k+4>>2]=f|1;c[k+f>>2]=f;b=f>>>3;if(f>>>0<256){d=6784+(b<<1<<2)|0;a=c[1686]|0;b=1<<b;if(!(a&b)){c[1686]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=k;c[b+12>>2]=k;c[k+8>>2]=b;c[k+12>>2]=d;break}b=f>>>8;do if(!b)e=0;else{if(f>>>0>16777215){e=31;break}u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}while(0);b=7048+(e<<2)|0;c[k+28>>2]=e;a=k+16|0;c[a+4>>2]=0;c[a>>2]=0;a=c[1687]|0;d=1<<e;if(!(a&d)){c[1687]=a|d;c[b>>2]=k;c[k+24>>2]=b;c[k+12>>2]=k;c[k+8>>2]=k;break}b=c[b>>2]|0;i:do if((c[b+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(f|0)){b=a;break i}else{e=e<<1;b=a}}c[d>>2]=k;c[k+24>>2]=b;c[k+12>>2]=k;c[k+8>>2]=k;break g}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=k;c[u>>2]=k;c[k+8>>2]=v;c[k+12>>2]=b;c[k+24>>2]=0}while(0);v=l+8|0;L=w;return v|0}b=7192;while(1){a=c[b>>2]|0;if(a>>>0<=j>>>0?(v=a+(c[b+4>>2]|0)|0,v>>>0>j>>>0):0)break;b=c[b+8>>2]|0}f=v+-47|0;a=f+8|0;a=f+((a&7|0)==0?0:0-a&7)|0;f=j+16|0;a=a>>>0<f>>>0?j:a;b=a+8|0;d=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=d-t|0;c[1692]=u;c[1689]=t;c[u+4>>2]=t|1;c[g+d+4>>2]=40;c[1693]=c[1808];d=a+4|0;c[d>>2]=27;c[b>>2]=c[1798];c[b+4>>2]=c[1799];c[b+8>>2]=c[1800];c[b+12>>2]=c[1801];c[1798]=g;c[1799]=h;c[1801]=0;c[1800]=b;b=a+24|0;do{u=b;b=b+4|0;c[b>>2]=7}while((u+8|0)>>>0<v>>>0);if((a|0)!=(j|0)){g=a-j|0;c[d>>2]=c[d>>2]&-2;c[j+4>>2]=g|1;c[a>>2]=g;b=g>>>3;if(g>>>0<256){d=6784+(b<<1<<2)|0;a=c[1686]|0;b=1<<b;if(!(a&b)){c[1686]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=j;c[b+12>>2]=j;c[j+8>>2]=b;c[j+12>>2]=d;break}b=g>>>8;if(b)if(g>>>0>16777215)e=31;else{u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=g>>>(e+7|0)&1|e<<1}else e=0;d=7048+(e<<2)|0;c[j+28>>2]=e;c[j+20>>2]=0;c[f>>2]=0;b=c[1687]|0;a=1<<e;if(!(b&a)){c[1687]=b|a;c[d>>2]=j;c[j+24>>2]=d;c[j+12>>2]=j;c[j+8>>2]=j;break}b=c[d>>2]|0;j:do if((c[b+4>>2]&-8|0)!=(g|0)){e=g<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(g|0)){b=a;break j}else{e=e<<1;b=a}}c[d>>2]=j;c[j+24>>2]=b;c[j+12>>2]=j;c[j+8>>2]=j;break f}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=j;c[u>>2]=j;c[j+8>>2]=v;c[j+12>>2]=b;c[j+24>>2]=0}}else{v=c[1690]|0;if((v|0)==0|g>>>0<v>>>0)c[1690]=g;c[1798]=g;c[1799]=h;c[1801]=0;c[1695]=c[1804];c[1694]=-1;c[1699]=6784;c[1698]=6784;c[1701]=6792;c[1700]=6792;c[1703]=6800;c[1702]=6800;c[1705]=6808;c[1704]=6808;c[1707]=6816;c[1706]=6816;c[1709]=6824;c[1708]=6824;c[1711]=6832;c[1710]=6832;c[1713]=6840;c[1712]=6840;c[1715]=6848;c[1714]=6848;c[1717]=6856;c[1716]=6856;c[1719]=6864;c[1718]=6864;c[1721]=6872;c[1720]=6872;c[1723]=6880;c[1722]=6880;c[1725]=6888;c[1724]=6888;c[1727]=6896;c[1726]=6896;c[1729]=6904;c[1728]=6904;c[1731]=6912;c[1730]=6912;c[1733]=6920;c[1732]=6920;c[1735]=6928;c[1734]=6928;c[1737]=6936;c[1736]=6936;c[1739]=6944;c[1738]=6944;c[1741]=6952;c[1740]=6952;c[1743]=6960;c[1742]=6960;c[1745]=6968;c[1744]=6968;c[1747]=6976;c[1746]=6976;c[1749]=6984;c[1748]=6984;c[1751]=6992;c[1750]=6992;c[1753]=7e3;c[1752]=7e3;c[1755]=7008;c[1754]=7008;c[1757]=7016;c[1756]=7016;c[1759]=7024;c[1758]=7024;c[1761]=7032;c[1760]=7032;v=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=v-t|0;c[1692]=u;c[1689]=t;c[u+4>>2]=t|1;c[g+v+4>>2]=40;c[1693]=c[1808]}while(0);b=c[1689]|0;if(b>>>0>m>>>0){t=b-m|0;c[1689]=t;v=c[1692]|0;u=v+m|0;c[1692]=u;c[u+4>>2]=t|1;c[v+4>>2]=m|3;v=v+8|0;L=w;return v|0}}c[(Jc()|0)>>2]=12;v=0;L=w;return v|0}function Fc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!a)return;d=a+-8|0;f=c[1690]|0;a=c[a+-4>>2]|0;b=a&-8;j=d+b|0;do if(!(a&1)){e=c[d>>2]|0;if(!(a&3))return;h=d+(0-e)|0;g=e+b|0;if(h>>>0<f>>>0)return;if((c[1691]|0)==(h|0)){a=j+4|0;b=c[a>>2]|0;if((b&3|0)!=3){i=h;b=g;break}c[1688]=g;c[a>>2]=b&-2;c[h+4>>2]=g|1;c[h+g>>2]=g;return}d=e>>>3;if(e>>>0<256){a=c[h+8>>2]|0;b=c[h+12>>2]|0;if((b|0)==(a|0)){c[1686]=c[1686]&~(1<<d);i=h;b=g;break}else{c[a+12>>2]=b;c[b+8>>2]=a;i=h;b=g;break}}f=c[h+24>>2]|0;a=c[h+12>>2]|0;do if((a|0)==(h|0)){b=h+16|0;d=b+4|0;a=c[d>>2]|0;if(!a){a=c[b>>2]|0;if(!a){a=0;break}}else b=d;while(1){e=a+20|0;d=c[e>>2]|0;if(!d){e=a+16|0;d=c[e>>2]|0;if(!d)break;else{a=d;b=e}}else{a=d;b=e}}c[b>>2]=0}else{i=c[h+8>>2]|0;c[i+12>>2]=a;c[a+8>>2]=i}while(0);if(f){b=c[h+28>>2]|0;d=7048+(b<<2)|0;if((c[d>>2]|0)==(h|0)){c[d>>2]=a;if(!a){c[1687]=c[1687]&~(1<<b);i=h;b=g;break}}else{i=f+16|0;c[((c[i>>2]|0)==(h|0)?i:f+20|0)>>2]=a;if(!a){i=h;b=g;break}}c[a+24>>2]=f;b=h+16|0;d=c[b>>2]|0;if(d|0){c[a+16>>2]=d;c[d+24>>2]=a}b=c[b+4>>2]|0;if(b){c[a+20>>2]=b;c[b+24>>2]=a;i=h;b=g}else{i=h;b=g}}else{i=h;b=g}}else{i=d;h=d}while(0);if(h>>>0>=j>>>0)return;a=j+4|0;e=c[a>>2]|0;if(!(e&1))return;if(!(e&2)){if((c[1692]|0)==(j|0)){j=(c[1689]|0)+b|0;c[1689]=j;c[1692]=i;c[i+4>>2]=j|1;if((i|0)!=(c[1691]|0))return;c[1691]=0;c[1688]=0;return}if((c[1691]|0)==(j|0)){j=(c[1688]|0)+b|0;c[1688]=j;c[1691]=h;c[i+4>>2]=j|1;c[h+j>>2]=j;return}f=(e&-8)+b|0;d=e>>>3;do if(e>>>0<256){b=c[j+8>>2]|0;a=c[j+12>>2]|0;if((a|0)==(b|0)){c[1686]=c[1686]&~(1<<d);break}else{c[b+12>>2]=a;c[a+8>>2]=b;break}}else{g=c[j+24>>2]|0;a=c[j+12>>2]|0;do if((a|0)==(j|0)){b=j+16|0;d=b+4|0;a=c[d>>2]|0;if(!a){a=c[b>>2]|0;if(!a){d=0;break}}else b=d;while(1){e=a+20|0;d=c[e>>2]|0;if(!d){e=a+16|0;d=c[e>>2]|0;if(!d)break;else{a=d;b=e}}else{a=d;b=e}}c[b>>2]=0;d=a}else{d=c[j+8>>2]|0;c[d+12>>2]=a;c[a+8>>2]=d;d=a}while(0);if(g|0){a=c[j+28>>2]|0;b=7048+(a<<2)|0;if((c[b>>2]|0)==(j|0)){c[b>>2]=d;if(!d){c[1687]=c[1687]&~(1<<a);break}}else{e=g+16|0;c[((c[e>>2]|0)==(j|0)?e:g+20|0)>>2]=d;if(!d)break}c[d+24>>2]=g;a=j+16|0;b=c[a>>2]|0;if(b|0){c[d+16>>2]=b;c[b+24>>2]=d}a=c[a+4>>2]|0;if(a|0){c[d+20>>2]=a;c[a+24>>2]=d}}}while(0);c[i+4>>2]=f|1;c[h+f>>2]=f;if((i|0)==(c[1691]|0)){c[1688]=f;return}}else{c[a>>2]=e&-2;c[i+4>>2]=b|1;c[h+b>>2]=b;f=b}a=f>>>3;if(f>>>0<256){d=6784+(a<<1<<2)|0;b=c[1686]|0;a=1<<a;if(!(b&a)){c[1686]=b|a;a=d;b=d+8|0}else{b=d+8|0;a=c[b>>2]|0}c[b>>2]=i;c[a+12>>2]=i;c[i+8>>2]=a;c[i+12>>2]=d;return}a=f>>>8;if(a)if(f>>>0>16777215)e=31;else{h=(a+1048320|0)>>>16&8;j=a<<h;g=(j+520192|0)>>>16&4;j=j<<g;e=(j+245760|0)>>>16&2;e=14-(g|h|e)+(j<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;a=7048+(e<<2)|0;c[i+28>>2]=e;c[i+20>>2]=0;c[i+16>>2]=0;b=c[1687]|0;d=1<<e;a:do if(!(b&d)){c[1687]=b|d;c[a>>2]=i;c[i+24>>2]=a;c[i+12>>2]=i;c[i+8>>2]=i}else{a=c[a>>2]|0;b:do if((c[a+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b)break;if((c[b+4>>2]&-8|0)==(f|0)){a=b;break b}else{e=e<<1;a=b}}c[d>>2]=i;c[i+24>>2]=a;c[i+12>>2]=i;c[i+8>>2]=i;break a}while(0);h=a+8|0;j=c[h>>2]|0;c[j+12>>2]=i;c[h>>2]=i;c[i+8>>2]=j;c[i+12>>2]=a;c[i+24>>2]=0}while(0);j=(c[1694]|0)+-1|0;c[1694]=j;if(j|0)return;a=7200;while(1){a=c[a>>2]|0;if(!a)break;else a=a+8|0}c[1694]=-1;return}function Gc(a,b){a=a|0;b=b|0;var d=0,e=0;if(!a){b=Ec(b)|0;return b|0}if(b>>>0>4294967231){c[(Jc()|0)>>2]=12;b=0;return b|0}d=Hc(a+-8|0,b>>>0<11?16:b+11&-8)|0;if(d|0){b=d+8|0;return b|0}d=Ec(b)|0;if(!d){b=0;return b|0}e=c[a+-4>>2]|0;e=(e&-8)-((e&3|0)==0?8:4)|0;td(d|0,a|0,(e>>>0<b>>>0?e:b)|0)|0;Fc(a);b=d;return b|0}function Hc(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=a+4|0;m=c[l>>2]|0;d=m&-8;i=a+d|0;if(!(m&3)){if(b>>>0<256){a=0;return a|0}if(d>>>0>=(b+4|0)>>>0?(d-b|0)>>>0<=c[1806]<<1>>>0:0)return a|0;a=0;return a|0}if(d>>>0>=b>>>0){d=d-b|0;if(d>>>0<=15)return a|0;k=a+b|0;c[l>>2]=m&1|b|2;c[k+4>>2]=d|3;m=i+4|0;c[m>>2]=c[m>>2]|1;Ic(k,d);return a|0}if((c[1692]|0)==(i|0)){k=(c[1689]|0)+d|0;d=k-b|0;e=a+b|0;if(k>>>0<=b>>>0){a=0;return a|0}c[l>>2]=m&1|b|2;c[e+4>>2]=d|1;c[1692]=e;c[1689]=d;return a|0}if((c[1691]|0)==(i|0)){e=(c[1688]|0)+d|0;if(e>>>0<b>>>0){a=0;return a|0}d=e-b|0;if(d>>>0>15){k=a+b|0;e=a+e|0;c[l>>2]=m&1|b|2;c[k+4>>2]=d|1;c[e>>2]=d;e=e+4|0;c[e>>2]=c[e>>2]&-2;e=k}else{c[l>>2]=m&1|e|2;e=a+e+4|0;c[e>>2]=c[e>>2]|1;e=0;d=0}c[1688]=d;c[1691]=e;return a|0}e=c[i+4>>2]|0;if(e&2|0){a=0;return a|0}j=(e&-8)+d|0;if(j>>>0<b>>>0){a=0;return a|0}k=j-b|0;f=e>>>3;do if(e>>>0<256){e=c[i+8>>2]|0;d=c[i+12>>2]|0;if((d|0)==(e|0)){c[1686]=c[1686]&~(1<<f);break}else{c[e+12>>2]=d;c[d+8>>2]=e;break}}else{h=c[i+24>>2]|0;d=c[i+12>>2]|0;do if((d|0)==(i|0)){e=i+16|0;f=e+4|0;d=c[f>>2]|0;if(!d){d=c[e>>2]|0;if(!d){f=0;break}}else e=f;while(1){g=d+20|0;f=c[g>>2]|0;if(!f){g=d+16|0;f=c[g>>2]|0;if(!f)break;else{d=f;e=g}}else{d=f;e=g}}c[e>>2]=0;f=d}else{f=c[i+8>>2]|0;c[f+12>>2]=d;c[d+8>>2]=f;f=d}while(0);if(h|0){d=c[i+28>>2]|0;e=7048+(d<<2)|0;if((c[e>>2]|0)==(i|0)){c[e>>2]=f;if(!f){c[1687]=c[1687]&~(1<<d);break}}else{g=h+16|0;c[((c[g>>2]|0)==(i|0)?g:h+20|0)>>2]=f;if(!f)break}c[f+24>>2]=h;d=i+16|0;e=c[d>>2]|0;if(e|0){c[f+16>>2]=e;c[e+24>>2]=f}d=c[d+4>>2]|0;if(d|0){c[f+20>>2]=d;c[d+24>>2]=f}}}while(0);if(k>>>0<16){c[l>>2]=m&1|j|2;m=a+j+4|0;c[m>>2]=c[m>>2]|1;return a|0}else{i=a+b|0;c[l>>2]=m&1|b|2;c[i+4>>2]=k|3;m=a+j+4|0;c[m>>2]=c[m>>2]|1;Ic(i,k);return a|0}return 0}function Ic(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;i=a+b|0;d=c[a+4>>2]|0;do if(!(d&1)){f=c[a>>2]|0;if(!(d&3))return;h=a+(0-f)|0;b=f+b|0;if((c[1691]|0)==(h|0)){a=i+4|0;d=c[a>>2]|0;if((d&3|0)!=3)break;c[1688]=b;c[a>>2]=d&-2;c[h+4>>2]=b|1;c[i>>2]=b;return}e=f>>>3;if(f>>>0<256){a=c[h+8>>2]|0;d=c[h+12>>2]|0;if((d|0)==(a|0)){c[1686]=c[1686]&~(1<<e);break}else{c[a+12>>2]=d;c[d+8>>2]=a;break}}g=c[h+24>>2]|0;a=c[h+12>>2]|0;do if((a|0)==(h|0)){d=h+16|0;e=d+4|0;a=c[e>>2]|0;if(!a){a=c[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=c[f>>2]|0;if(!e){f=a+16|0;e=c[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}c[d>>2]=0}else{f=c[h+8>>2]|0;c[f+12>>2]=a;c[a+8>>2]=f}while(0);if(g){d=c[h+28>>2]|0;e=7048+(d<<2)|0;if((c[e>>2]|0)==(h|0)){c[e>>2]=a;if(!a){c[1687]=c[1687]&~(1<<d);break}}else{f=g+16|0;c[((c[f>>2]|0)==(h|0)?f:g+20|0)>>2]=a;if(!a)break}c[a+24>>2]=g;d=h+16|0;e=c[d>>2]|0;if(e|0){c[a+16>>2]=e;c[e+24>>2]=a}d=c[d+4>>2]|0;if(d){c[a+20>>2]=d;c[d+24>>2]=a}}}else h=a;while(0);a=i+4|0;e=c[a>>2]|0;if(!(e&2)){if((c[1692]|0)==(i|0)){i=(c[1689]|0)+b|0;c[1689]=i;c[1692]=h;c[h+4>>2]=i|1;if((h|0)!=(c[1691]|0))return;c[1691]=0;c[1688]=0;return}if((c[1691]|0)==(i|0)){i=(c[1688]|0)+b|0;c[1688]=i;c[1691]=h;c[h+4>>2]=i|1;c[h+i>>2]=i;return}f=(e&-8)+b|0;d=e>>>3;do if(e>>>0<256){a=c[i+8>>2]|0;b=c[i+12>>2]|0;if((b|0)==(a|0)){c[1686]=c[1686]&~(1<<d);break}else{c[a+12>>2]=b;c[b+8>>2]=a;break}}else{g=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+16|0;d=a+4|0;b=c[d>>2]|0;if(!b){b=c[a>>2]|0;if(!b){d=0;break}}else a=d;while(1){e=b+20|0;d=c[e>>2]|0;if(!d){e=b+16|0;d=c[e>>2]|0;if(!d)break;else{b=d;a=e}}else{b=d;a=e}}c[a>>2]=0;d=b}else{d=c[i+8>>2]|0;c[d+12>>2]=b;c[b+8>>2]=d;d=b}while(0);if(g|0){b=c[i+28>>2]|0;a=7048+(b<<2)|0;if((c[a>>2]|0)==(i|0)){c[a>>2]=d;if(!d){c[1687]=c[1687]&~(1<<b);break}}else{e=g+16|0;c[((c[e>>2]|0)==(i|0)?e:g+20|0)>>2]=d;if(!d)break}c[d+24>>2]=g;b=i+16|0;a=c[b>>2]|0;if(a|0){c[d+16>>2]=a;c[a+24>>2]=d}b=c[b+4>>2]|0;if(b|0){c[d+20>>2]=b;c[b+24>>2]=d}}}while(0);c[h+4>>2]=f|1;c[h+f>>2]=f;if((h|0)==(c[1691]|0)){c[1688]=f;return}}else{c[a>>2]=e&-2;c[h+4>>2]=b|1;c[h+b>>2]=b;f=b}b=f>>>3;if(f>>>0<256){d=6784+(b<<1<<2)|0;a=c[1686]|0;b=1<<b;if(!(a&b)){c[1686]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=h;c[b+12>>2]=h;c[h+8>>2]=b;c[h+12>>2]=d;return}b=f>>>8;if(b)if(f>>>0>16777215)e=31;else{g=(b+1048320|0)>>>16&8;i=b<<g;d=(i+520192|0)>>>16&4;i=i<<d;e=(i+245760|0)>>>16&2;e=14-(d|g|e)+(i<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;b=7048+(e<<2)|0;c[h+28>>2]=e;c[h+20>>2]=0;c[h+16>>2]=0;a=c[1687]|0;d=1<<e;if(!(a&d)){c[1687]=a|d;c[b>>2]=h;c[h+24>>2]=b;c[h+12>>2]=h;c[h+8>>2]=h;return}b=c[b>>2]|0;a:do if((c[b+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(f|0)){b=a;break a}else{e=e<<1;b=a}}c[d>>2]=h;c[h+24>>2]=b;c[h+12>>2]=h;c[h+8>>2]=h;return}while(0);g=b+8|0;i=c[g>>2]|0;c[i+12>>2]=h;c[g>>2]=h;c[h+8>>2]=i;c[h+12>>2]=b;c[h+24>>2]=0;return}function Jc(){return 7304}function Kc(a){a=a|0;return (a+-48|0)>>>0<10|0}function Lc(){return 5540}function Mc(a){a=a|0;return}function Nc(a){a=a|0;return 1}function Oc(b){b=b|0;var d=0,e=0;d=b+74|0;e=a[d>>0]|0;a[d>>0]=e+255|e;d=c[b>>2]|0;if(!(d&8)){c[b+8>>2]=0;c[b+4>>2]=0;e=c[b+44>>2]|0;c[b+28>>2]=e;c[b+20>>2]=e;c[b+16>>2]=e+(c[b+48>>2]|0);b=0}else{c[b>>2]=d|32;b=-1}return b|0}function Pc(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0;f=e+16|0;g=c[f>>2]|0;if(!g)if(!(Oc(e)|0)){g=c[f>>2]|0;h=5}else f=0;else h=5;a:do if((h|0)==5){j=e+20|0;i=c[j>>2]|0;f=i;if((g-i|0)>>>0<d>>>0){f=Q[c[e+36>>2]&3](e,b,d)|0;break}b:do if((a[e+75>>0]|0)<0|(d|0)==0){h=0;g=b}else{i=d;while(1){g=i+-1|0;if((a[b+g>>0]|0)==10)break;if(!g){h=0;g=b;break b}else i=g}f=Q[c[e+36>>2]&3](e,b,i)|0;if(f>>>0<i>>>0)break a;h=i;g=b+i|0;d=d-i|0;f=c[j>>2]|0}while(0);td(f|0,g|0,d|0)|0;c[j>>2]=(c[j>>2]|0)+d;f=h+d|0}while(0);return f|0}function Qc(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=d&255;f=(e|0)!=0;a:do if(f&(b&3|0)!=0){g=d&255;while(1){if((a[b>>0]|0)==g<<24>>24){i=6;break a}b=b+1|0;e=e+-1|0;f=(e|0)!=0;if(!(f&(b&3|0)!=0)){i=5;break}}}else i=5;while(0);if((i|0)==5)if(f)i=6;else i=16;b:do if((i|0)==6){g=d&255;if((a[b>>0]|0)==g<<24>>24)if(!e){i=16;break}else break;f=h*16843009|0;c:do if(e>>>0>3)while(1){h=c[b>>2]^f;if((h&-2139062144^-2139062144)&h+-16843009|0)break c;b=b+4|0;e=e+-4|0;if(e>>>0<=3){i=11;break}}else i=11;while(0);if((i|0)==11)if(!e){i=16;break}while(1){if((a[b>>0]|0)==g<<24>>24)break b;e=e+-1|0;if(!e){i=16;break}else b=b+1|0}}while(0);if((i|0)==16)b=0;return b|0}function Rc(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;r=L;L=L+224|0;m=r+208|0;n=r+160|0;p=r+80|0;q=r;f=n;g=f+40|0;do{c[f>>2]=0;f=f+4|0}while((f|0)<(g|0));c[m>>2]=c[e>>2];if((Sc(0,d,m,p,n)|0)<0)e=-1;else{if((c[b+76>>2]|0)>-1)o=Nc(b)|0;else o=0;e=c[b>>2]|0;l=e&32;if((a[b+74>>0]|0)<1)c[b>>2]=e&-33;f=b+48|0;if(!(c[f>>2]|0)){g=b+44|0;h=c[g>>2]|0;c[g>>2]=q;i=b+28|0;c[i>>2]=q;j=b+20|0;c[j>>2]=q;c[f>>2]=80;k=b+16|0;c[k>>2]=q+80;e=Sc(b,d,m,p,n)|0;if(h){Q[c[b+36>>2]&3](b,0,0)|0;e=(c[j>>2]|0)==0?-1:e;c[g>>2]=h;c[f>>2]=0;c[k>>2]=0;c[i>>2]=0;c[j>>2]=0}}else e=Sc(b,d,m,p,n)|0;f=c[b>>2]|0;c[b>>2]=f|l;if(o|0)Mc(b);e=(f&32|0)==0?e:-1}L=r;return e|0}function Sc(d,e,f,h,i){d=d|0;e=e|0;f=f|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;H=L;L=L+64|0;C=H+56|0;D=H+40|0;z=H;F=H+48|0;G=H+60|0;c[C>>2]=e;w=(d|0)!=0;x=z+40|0;y=x;z=z+39|0;A=F+4|0;j=0;e=0;l=0;a:while(1){do{do if((e|0)>-1)if((j|0)>(2147483647-e|0)){c[(Jc()|0)>>2]=75;e=-1;break}else{e=j+e|0;break}while(0);p=c[C>>2]|0;j=a[p>>0]|0;if(!(j<<24>>24)){u=92;break a}k=p;b:while(1){switch(j<<24>>24){case 37:{u=10;break b}case 0:{j=k;break b}default:{}}t=k+1|0;c[C>>2]=t;j=a[t>>0]|0;k=t}c:do if((u|0)==10){u=0;j=k;do{if((a[k+1>>0]|0)!=37)break c;j=j+1|0;k=k+2|0;c[C>>2]=k}while((a[k>>0]|0)==37)}while(0);j=j-p|0;if(w)Tc(d,p,j)}while((j|0)!=0);t=(Kc(a[(c[C>>2]|0)+1>>0]|0)|0)==0;k=c[C>>2]|0;if(!t?(a[k+2>>0]|0)==36:0){r=(a[k+1>>0]|0)+-48|0;n=1;j=3}else{r=-1;n=l;j=1}j=k+j|0;c[C>>2]=j;k=a[j>>0]|0;l=(k<<24>>24)+-32|0;if(l>>>0>31|(1<<l&75913|0)==0)m=0;else{m=0;do{m=1<<l|m;j=j+1|0;c[C>>2]=j;k=a[j>>0]|0;l=(k<<24>>24)+-32|0}while(!(l>>>0>31|(1<<l&75913|0)==0))}if(k<<24>>24==42){if((Kc(a[j+1>>0]|0)|0)!=0?(E=c[C>>2]|0,(a[E+2>>0]|0)==36):0){j=E+1|0;c[i+((a[j>>0]|0)+-48<<2)>>2]=10;j=c[h+((a[j>>0]|0)+-48<<3)>>2]|0;l=1;k=E+3|0}else{if(n|0){e=-1;break}if(w){t=(c[f>>2]|0)+(4-1)&~(4-1);j=c[t>>2]|0;c[f>>2]=t+4}else j=0;l=0;k=(c[C>>2]|0)+1|0}c[C>>2]=k;t=(j|0)<0;s=t?0-j|0:j;m=t?m|8192:m;t=l}else{j=Uc(C)|0;if((j|0)<0){e=-1;break}s=j;t=n;k=c[C>>2]|0}do if((a[k>>0]|0)==46){j=k+1|0;if((a[j>>0]|0)!=42){c[C>>2]=j;j=Uc(C)|0;k=c[C>>2]|0;break}if(Kc(a[k+2>>0]|0)|0?(B=c[C>>2]|0,(a[B+3>>0]|0)==36):0){j=B+2|0;c[i+((a[j>>0]|0)+-48<<2)>>2]=10;j=c[h+((a[j>>0]|0)+-48<<3)>>2]|0;k=B+4|0;c[C>>2]=k;break}if(t|0){e=-1;break a}if(w){q=(c[f>>2]|0)+(4-1)&~(4-1);j=c[q>>2]|0;c[f>>2]=q+4}else j=0;k=(c[C>>2]|0)+2|0;c[C>>2]=k}else j=-1;while(0);q=0;while(1){if(((a[k>>0]|0)+-65|0)>>>0>57){e=-1;break a}l=k;k=k+1|0;c[C>>2]=k;l=a[(a[l>>0]|0)+-65+(4976+(q*58|0))>>0]|0;n=l&255;if((n+-1|0)>>>0>=8)break;else q=n}if(!(l<<24>>24)){e=-1;break}o=(r|0)>-1;do if(l<<24>>24==19)if(o){e=-1;break a}else u=54;else{if(o){c[i+(r<<2)>>2]=n;o=h+(r<<3)|0;r=c[o+4>>2]|0;u=D;c[u>>2]=c[o>>2];c[u+4>>2]=r;u=54;break}if(!w){e=0;break a}Vc(D,n,f);k=c[C>>2]|0;u=55}while(0);if((u|0)==54){u=0;if(w)u=55;else j=0}d:do if((u|0)==55){u=0;k=a[k+-1>>0]|0;k=(q|0)!=0&(k&15|0)==3?k&-33:k;o=m&-65537;r=(m&8192|0)==0?m:o;e:do switch(k|0){case 110:switch((q&255)<<24>>24){case 0:{c[c[D>>2]>>2]=e;j=0;break d}case 1:{c[c[D>>2]>>2]=e;j=0;break d}case 2:{j=c[D>>2]|0;c[j>>2]=e;c[j+4>>2]=((e|0)<0)<<31>>31;j=0;break d}case 3:{b[c[D>>2]>>1]=e;j=0;break d}case 4:{a[c[D>>2]>>0]=e;j=0;break d}case 6:{c[c[D>>2]>>2]=e;j=0;break d}case 7:{j=c[D>>2]|0;c[j>>2]=e;c[j+4>>2]=((e|0)<0)<<31>>31;j=0;break d}default:{j=0;break d}}case 112:{k=120;j=j>>>0>8?j:8;l=r|8;u=67;break}case 88:case 120:{l=r;u=67;break}case 111:{l=D;k=c[l>>2]|0;l=c[l+4>>2]|0;p=Xc(k,l,x)|0;o=y-p|0;m=0;n=6478;j=(r&8|0)==0|(j|0)>(o|0)?j:o+1|0;o=r;u=73;break}case 105:case 100:{l=D;k=c[l>>2]|0;l=c[l+4>>2]|0;if((l|0)<0){k=kd(0,0,k|0,l|0)|0;l=v()|0;m=D;c[m>>2]=k;c[m+4>>2]=l;m=1;n=6478;u=72;break e}else{m=(r&2049|0)!=0&1;n=(r&2048|0)==0?((r&1|0)==0?6478:6480):6479;u=72;break e}}case 117:{l=D;m=0;n=6478;k=c[l>>2]|0;l=c[l+4>>2]|0;u=72;break}case 99:{a[z>>0]=c[D>>2];p=z;m=0;n=6478;l=1;k=o;j=y;break}case 115:{q=c[D>>2]|0;q=(q|0)==0?6488:q;r=Qc(q,0,j)|0;I=(r|0)==0;p=q;m=0;n=6478;l=I?j:r-q|0;k=o;j=I?q+j|0:r;break}case 67:{c[F>>2]=c[D>>2];c[A>>2]=0;c[D>>2]=F;n=-1;u=79;break}case 83:{if(!j){Zc(d,32,s,0,r);j=0;u=89}else{n=j;u=79}break}case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:{j=$c(d,+g[D>>3],s,j,r,k)|0;break d}default:{m=0;n=6478;l=j;k=r;j=y}}while(0);f:do if((u|0)==67){I=D;r=c[I>>2]|0;I=c[I+4>>2]|0;p=Wc(r,I,x,k&32)|0;n=(l&8|0)==0|(r|0)==0&(I|0)==0;m=n?0:2;n=n?6478:6478+(k>>>4)|0;o=l;k=r;l=I;u=73}else if((u|0)==72){p=Yc(k,l,x)|0;o=r;u=73}else if((u|0)==79){u=0;m=c[D>>2]|0;j=0;while(1){k=c[m>>2]|0;if(!k)break;k=_c(G,k)|0;l=(k|0)<0;if(l|k>>>0>(n-j|0)>>>0){u=83;break}j=k+j|0;if(n>>>0>j>>>0)m=m+4|0;else break}if((u|0)==83){u=0;if(l){e=-1;break a}}Zc(d,32,s,j,r);if(!j){j=0;u=89}else{l=c[D>>2]|0;m=0;while(1){k=c[l>>2]|0;if(!k){u=89;break f}k=_c(G,k)|0;m=k+m|0;if((m|0)>(j|0)){u=89;break f}Tc(d,G,k);if(m>>>0>=j>>>0){u=89;break}else l=l+4|0}}}while(0);if((u|0)==73){u=0;l=(k|0)!=0|(l|0)!=0;k=(j|0)!=0|l;l=y-p+((l^1)&1)|0;p=k?p:x;l=k?((j|0)>(l|0)?j:l):0;k=(j|0)>-1?o&-65537:o;j=y}else if((u|0)==89){u=0;Zc(d,32,s,j,r^8192);j=(s|0)>(j|0)?s:j;break}r=j-p|0;q=(l|0)<(r|0)?r:l;I=q+m|0;j=(s|0)<(I|0)?I:s;Zc(d,32,j,I,k);Tc(d,n,m);Zc(d,48,j,I,k^65536);Zc(d,48,q,r,0);Tc(d,p,r);Zc(d,32,j,I,k^8192)}while(0);l=t}g:do if((u|0)==92)if(!d)if(!l)e=0;else{e=1;while(1){j=c[i+(e<<2)>>2]|0;if(!j)break;Vc(h+(e<<3)|0,j,f);e=e+1|0;if(e>>>0>=10){e=1;break g}}while(1){if(c[i+(e<<2)>>2]|0){e=-1;break g}e=e+1|0;if(e>>>0>=10){e=1;break}}}while(0);L=H;return e|0}function Tc(a,b,d){a=a|0;b=b|0;d=d|0;if(!(c[a>>2]&32))Pc(b,d,a)|0;return}function Uc(b){b=b|0;var d=0,e=0;if(!(Kc(a[c[b>>2]>>0]|0)|0))d=0;else{d=0;do{e=c[b>>2]|0;d=(d*10|0)+-48+(a[e>>0]|0)|0;e=e+1|0;c[b>>2]=e}while((Kc(a[e>>0]|0)|0)!=0)}return d|0}function Vc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,h=0.0;a:do if(b>>>0<=20)do switch(b|0){case 9:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;c[a>>2]=b;break a}case 10:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=b;c[e+4>>2]=((b|0)<0)<<31>>31;break a}case 11:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=b;c[e+4>>2]=0;break a}case 12:{e=(c[d>>2]|0)+(8-1)&~(8-1);b=e;f=c[b>>2]|0;b=c[b+4>>2]|0;c[d>>2]=e+8;e=a;c[e>>2]=f;c[e+4>>2]=b;break a}case 13:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;e=(e&65535)<<16>>16;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a}case 14:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e&65535;c[f+4>>2]=0;break a}case 15:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;e=(e&255)<<24>>24;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a}case 16:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e&255;c[f+4>>2]=0;break a}case 17:{f=(c[d>>2]|0)+(8-1)&~(8-1);h=+g[f>>3];c[d>>2]=f+8;g[a>>3]=h;break a}case 18:{f=(c[d>>2]|0)+(8-1)&~(8-1);h=+g[f>>3];c[d>>2]=f+8;g[a>>3]=h;break a}default:break a}while(0);while(0);return}function Wc(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;if(!((b|0)==0&(c|0)==0))do{e=e+-1|0;a[e>>0]=d[5440+(b&15)>>0]|0|f;b=od(b|0,c|0,4)|0;c=v()|0}while(!((b|0)==0&(c|0)==0));return e|0}function Xc(b,c,d){b=b|0;c=c|0;d=d|0;if(!((b|0)==0&(c|0)==0))do{d=d+-1|0;a[d>>0]=b&7|48;b=od(b|0,c|0,3)|0;c=v()|0}while(!((b|0)==0&(c|0)==0));return d|0}function Yc(b,c,d){b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;if(c>>>0>0|(c|0)==0&b>>>0>4294967295){do{e=b;b=nd(b|0,c|0,10,0)|0;f=c;c=v()|0;g=id(b|0,c|0,10,0)|0;g=kd(e|0,f|0,g|0,v()|0)|0;v()|0;d=d+-1|0;a[d>>0]=g&255|48}while(f>>>0>9|(f|0)==9&e>>>0>4294967295);c=b}else c=b;if(c)do{g=c;c=(c>>>0)/10|0;d=d+-1|0;a[d>>0]=g-(c*10|0)|48}while(g>>>0>=10);return d|0}function Zc(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0;g=L;L=L+256|0;f=g;if((c|0)>(d|0)&(e&73728|0)==0){e=c-d|0;ud(f|0,b<<24>>24|0,(e>>>0<256?e:256)|0)|0;if(e>>>0>255){b=c-d|0;do{Tc(a,f,256);e=e+-256|0}while(e>>>0>255);e=b&255}Tc(a,f,e)}L=g;return}function _c(a,b){a=a|0;b=b|0;if(!a)a=0;else a=dd(a,b,0)|0;return a|0}function $c(b,e,f,g,h,i){b=b|0;e=+e;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0;G=L;L=L+560|0;l=G+32|0;u=G+536|0;F=G;E=F;m=G+540|0;c[u>>2]=0;D=m+12|0;ad(e)|0;j=v()|0;if((j|0)<0){e=-e;ad(e)|0;C=1;B=6495;j=v()|0}else{C=(h&2049|0)!=0&1;B=(h&2048|0)==0?((h&1|0)==0?6496:6501):6498}do if(0==0&(j&2146435072|0)==2146435072){F=(i&32|0)!=0;j=C+3|0;Zc(b,32,f,j,h&-65537);Tc(b,B,C);Tc(b,e!=e|0.0!=0.0?(F?6522:6526):F?6514:6518,3);Zc(b,32,f,j,h^8192)}else{q=+bd(e,u)*2.0;j=q!=0.0;if(j)c[u>>2]=(c[u>>2]|0)+-1;t=i|32;if((t|0)==97){o=i&32;r=(o|0)==0?B:B+9|0;p=C|2;j=12-g|0;do if(!(g>>>0>11|(j|0)==0)){e=8.0;do{j=j+-1|0;e=e*16.0}while((j|0)!=0);if((a[r>>0]|0)==45){e=-(e+(-q-e));break}else{e=q+e-e;break}}else e=q;while(0);k=c[u>>2]|0;j=(k|0)<0?0-k|0:k;j=Yc(j,((j|0)<0)<<31>>31,D)|0;if((j|0)==(D|0)){j=m+11|0;a[j>>0]=48}a[j+-1>>0]=(k>>31&2)+43;n=j+-2|0;a[n>>0]=i+15;k=(g|0)<1;l=(h&8|0)==0;m=F;do{C=~~e;j=m+1|0;a[m>>0]=o|d[5440+C>>0];e=(e-+(C|0))*16.0;if((j-E|0)==1?!(l&(k&e==0.0)):0){a[j>>0]=46;m=m+2|0}else m=j}while(e!=0.0);if((g|0)!=0?(-2-E+m|0)<(g|0):0){k=D;l=n;j=g+2+k-l|0}else{k=D;l=n;j=k-E-l+m|0}D=j+p|0;Zc(b,32,f,D,h);Tc(b,r,p);Zc(b,48,f,D,h^65536);E=m-E|0;Tc(b,F,E);F=k-l|0;Zc(b,48,j-(E+F)|0,0,0);Tc(b,n,F);Zc(b,32,f,D,h^8192);j=D;break}k=(g|0)<0?6:g;if(j){j=(c[u>>2]|0)+-28|0;c[u>>2]=j;e=q*268435456.0}else{e=q;j=c[u>>2]|0}A=(j|0)<0?l:l+288|0;l=A;do{y=~~e>>>0;c[l>>2]=y;l=l+4|0;e=(e-+(y>>>0))*1.0e9}while(e!=0.0);y=A;if((j|0)>0){o=A;while(1){n=(j|0)<29?j:29;j=l+-4|0;if(j>>>0>=o>>>0){m=0;do{s=pd(c[j>>2]|0,0,n|0)|0;s=jd(s|0,v()|0,m|0,0)|0;w=v()|0;m=nd(s|0,w|0,1e9,0)|0;x=id(m|0,v()|0,1e9,0)|0;x=kd(s|0,w|0,x|0,v()|0)|0;v()|0;c[j>>2]=x;j=j+-4|0}while(j>>>0>=o>>>0);if(m){x=o+-4|0;c[x>>2]=m;m=x}else m=o}else m=o;a:do if(l>>>0>m>>>0){j=l;while(1){l=j+-4|0;if(c[l>>2]|0){l=j;break a}if(l>>>0>m>>>0)j=l;else break}}while(0);j=(c[u>>2]|0)-n|0;c[u>>2]=j;if((j|0)>0)o=m;else break}}else m=A;if((j|0)<0){g=((k+25|0)/9|0)+1|0;s=(t|0)==102;do{r=0-j|0;r=(r|0)<9?r:9;if(m>>>0<l>>>0){n=(1<<r)+-1|0;o=1e9>>>r;p=0;j=m;do{x=c[j>>2]|0;c[j>>2]=(x>>>r)+p;p=(x&n)*o|0;j=j+4|0}while(j>>>0<l>>>0);m=(c[m>>2]|0)==0?m+4|0:m;if(p){c[l>>2]=p;l=l+4|0}}else m=(c[m>>2]|0)==0?m+4|0:m;j=s?A:m;l=(l-j>>2|0)>(g|0)?j+(g<<2)|0:l;j=(c[u>>2]|0)+r|0;c[u>>2]=j}while((j|0)<0);s=m}else s=m;if(s>>>0<l>>>0){j=(y-s>>2)*9|0;n=c[s>>2]|0;if(n>>>0>=10){m=10;do{m=m*10|0;j=j+1|0}while(n>>>0>=m>>>0)}}else j=0;w=(t|0)==103;x=(k|0)!=0;m=k-((t|0)==102?0:j)+((x&w)<<31>>31)|0;if((m|0)<(((l-y>>2)*9|0)+-9|0)){u=m+9216|0;m=(u|0)/9|0;g=A+4+(m+-1024<<2)|0;m=u-(m*9|0)|0;if((m|0)<8){n=10;while(1){n=n*10|0;if((m|0)<7)m=m+1|0;else break}}else n=10;p=c[g>>2]|0;m=(p>>>0)/(n>>>0)|0;r=p-(m*n|0)|0;o=(g+4|0)==(l|0);if(!(o&(r|0)==0)){q=(m&1|0)==0?9007199254740992.0:9007199254740994.0;u=n>>>1;e=r>>>0<u>>>0?.5:o&(r|0)==(u|0)?1.0:1.5;if(C){u=(a[B>>0]|0)==45;e=u?-e:e;q=u?-q:q}m=p-r|0;c[g>>2]=m;if(q+e!=q){u=m+n|0;c[g>>2]=u;if(u>>>0>999999999){n=g;j=s;while(1){m=n+-4|0;c[n>>2]=0;if(m>>>0<j>>>0){j=j+-4|0;c[j>>2]=0}u=(c[m>>2]|0)+1|0;c[m>>2]=u;if(u>>>0>999999999)n=m;else{n=j;break}}}else{m=g;n=s}j=(y-n>>2)*9|0;p=c[n>>2]|0;if(p>>>0>=10){o=10;do{o=o*10|0;j=j+1|0}while(p>>>0>=o>>>0)}}else{m=g;n=s}}else{m=g;n=s}u=m+4|0;l=l>>>0>u>>>0?u:l}else n=s;g=0-j|0;b:do if(l>>>0>n>>>0)while(1){m=l+-4|0;if(c[m>>2]|0){u=l;t=1;break b}if(m>>>0>n>>>0)l=m;else{u=m;t=0;break}}else{u=l;t=0}while(0);do if(w){k=k+((x^1)&1)|0;if((k|0)>(j|0)&(j|0)>-5){o=i+-1|0;k=k+-1-j|0}else{o=i+-2|0;k=k+-1|0}if(!(h&8)){if(t?(z=c[u+-4>>2]|0,(z|0)!=0):0)if(!((z>>>0)%10|0)){m=0;l=10;do{l=l*10|0;m=m+1|0}while(!((z>>>0)%(l>>>0)|0|0))}else m=0;else m=9;l=((u-y>>2)*9|0)+-9|0;if((o|32|0)==102){i=l-m|0;i=(i|0)>0?i:0;k=(k|0)<(i|0)?k:i;break}else{i=l+j-m|0;i=(i|0)>0?i:0;k=(k|0)<(i|0)?k:i;break}}}else o=i;while(0);s=(k|0)!=0;p=s?1:h>>>3&1;r=(o|32|0)==102;if(r){w=0;j=(j|0)>0?j:0}else{l=(j|0)<0?g:j;l=Yc(l,((l|0)<0)<<31>>31,D)|0;m=D;if((m-l|0)<2)do{l=l+-1|0;a[l>>0]=48}while((m-l|0)<2);a[l+-1>>0]=(j>>31&2)+43;j=l+-2|0;a[j>>0]=o;w=j;j=m-j|0}j=C+1+k+p+j|0;Zc(b,32,f,j,h);Tc(b,B,C);Zc(b,48,f,j,h^65536);if(r){p=n>>>0>A>>>0?A:n;r=F+9|0;n=r;o=F+8|0;m=p;do{l=Yc(c[m>>2]|0,0,r)|0;if((m|0)==(p|0)){if((l|0)==(r|0)){a[o>>0]=48;l=o}}else if(l>>>0>F>>>0){ud(F|0,48,l-E|0)|0;do l=l+-1|0;while(l>>>0>F>>>0)}Tc(b,l,n-l|0);m=m+4|0}while(m>>>0<=A>>>0);if(!((h&8|0)==0&(s^1)))Tc(b,6530,1);if(m>>>0<u>>>0&(k|0)>0)while(1){l=Yc(c[m>>2]|0,0,r)|0;if(l>>>0>F>>>0){ud(F|0,48,l-E|0)|0;do l=l+-1|0;while(l>>>0>F>>>0)}Tc(b,l,(k|0)<9?k:9);m=m+4|0;l=k+-9|0;if(!(m>>>0<u>>>0&(k|0)>9)){k=l;break}else k=l}Zc(b,48,k+9|0,9,0)}else{u=t?u:n+4|0;if(n>>>0<u>>>0&(k|0)>-1){g=F+9|0;s=(h&8|0)==0;t=g;p=0-E|0;r=F+8|0;o=n;do{l=Yc(c[o>>2]|0,0,g)|0;if((l|0)==(g|0)){a[r>>0]=48;l=r}do if((o|0)==(n|0)){m=l+1|0;Tc(b,l,1);if(s&(k|0)<1){l=m;break}Tc(b,6530,1);l=m}else{if(l>>>0<=F>>>0)break;ud(F|0,48,l+p|0)|0;do l=l+-1|0;while(l>>>0>F>>>0)}while(0);E=t-l|0;Tc(b,l,(k|0)>(E|0)?E:k);k=k-E|0;o=o+4|0}while(o>>>0<u>>>0&(k|0)>-1)}Zc(b,48,k+18|0,18,0);Tc(b,w,D-w|0)}Zc(b,32,f,j,h^8192)}while(0);L=G;return ((j|0)<(f|0)?f:j)|0}function ad(a){a=+a;var b=0;g[h>>3]=a;b=c[h>>2]|0;u(c[h+4>>2]|0);return b|0}function bd(a,b){a=+a;b=b|0;return +(+cd(a,b))}function cd(a,b){a=+a;b=b|0;var d=0,e=0,f=0;g[h>>3]=a;d=c[h>>2]|0;e=c[h+4>>2]|0;f=od(d|0,e|0,52)|0;v()|0;switch(f&2047){case 0:{if(a!=0.0){a=+cd(a*18446744073709551616.0,b);d=(c[b>>2]|0)+-64|0}else d=0;c[b>>2]=d;break}case 2047:break;default:{c[b>>2]=(f&2047)+-1022;c[h>>2]=d;c[h+4>>2]=e&-2146435073|1071644672;a=+g[h>>3]}}return +a}function dd(b,d,e){b=b|0;d=d|0;e=e|0;do if(b){if(d>>>0<128){a[b>>0]=d;b=1;break}if(!(c[c[(ed()|0)+188>>2]>>2]|0))if((d&-128|0)==57216){a[b>>0]=d;b=1;break}else{c[(Jc()|0)>>2]=84;b=-1;break}if(d>>>0<2048){a[b>>0]=d>>>6|192;a[b+1>>0]=d&63|128;b=2;break}if(d>>>0<55296|(d&-8192|0)==57344){a[b>>0]=d>>>12|224;a[b+1>>0]=d>>>6&63|128;a[b+2>>0]=d&63|128;b=3;break}if((d+-65536|0)>>>0<1048576){a[b>>0]=d>>>18|240;a[b+1>>0]=d>>>12&63|128;a[b+2>>0]=d>>>6&63|128;a[b+3>>0]=d&63|128;b=4;break}else{c[(Jc()|0)>>2]=84;b=-1;break}}else b=1;while(0);return b|0}function ed(){return Lc()|0}function fd(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;m=L;L=L+128|0;g=m+124|0;l=m;h=l;i=5784;j=h+124|0;do{c[h>>2]=c[i>>2];h=h+4|0;i=i+4|0}while((h|0)<(j|0));if((d+-1|0)>>>0>2147483646)if(!d){b=g;d=1;k=4}else{c[(Jc()|0)>>2]=75;d=-1}else k=4;if((k|0)==4){k=-2-b|0;k=d>>>0>k>>>0?k:d;c[l+48>>2]=k;g=l+20|0;c[g>>2]=b;c[l+44>>2]=b;d=b+k|0;b=l+16|0;c[b>>2]=d;c[l+28>>2]=d;d=Rc(l,e,f)|0;if(k){l=c[g>>2]|0;a[l+(((l|0)==(c[b>>2]|0))<<31>>31)>>0]=0}}L=m;return d|0}function gd(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0;e=a+20|0;f=c[e>>2]|0;a=(c[a+16>>2]|0)-f|0;a=a>>>0>d>>>0?d:a;td(f|0,b|0,a|0)|0;c[e>>2]=(c[e>>2]|0)+a;return d|0}function hd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=a&65535;e=b&65535;c=e*f|0;d=a>>>16;a=(c>>>16)+(e*d|0)|0;e=b>>>16;b=e*f|0;return (u((a>>>16)+(e*d|0)+(((a&65535)+b|0)>>>16)|0),a+b<<16|c&65535|0)|0}function id(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=a;f=c;c=hd(e,f)|0;a=v()|0;return (u((b*f|0)+(d*e|0)+a|a&0|0),c|0|0)|0}function jd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;c=a+c>>>0;return (u(b+d+(c>>>0<a>>>0|0)>>>0|0),c|0)|0}function kd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;d=b-d-(c>>>0>a>>>0|0)>>>0;return (u(d|0),a-c>>>0|0)|0}function ld(a){a=a|0;return (a?31-(s(a^a-1)|0)|0:32)|0}function md(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;l=a;j=b;k=j;h=d;n=e;i=n;if(!k){g=(f|0)!=0;if(!i){if(g){c[f>>2]=(l>>>0)%(h>>>0);c[f+4>>2]=0}n=0;f=(l>>>0)/(h>>>0)>>>0;return (u(n|0),f)|0}else{if(!g){n=0;f=0;return (u(n|0),f)|0}c[f>>2]=a|0;c[f+4>>2]=b&0;n=0;f=0;return (u(n|0),f)|0}}g=(i|0)==0;do if(h){if(!g){g=(s(i|0)|0)-(s(k|0)|0)|0;if(g>>>0<=31){m=g+1|0;i=31-g|0;b=g-31>>31;h=m;a=l>>>(m>>>0)&b|k<<i;b=k>>>(m>>>0)&b;g=0;i=l<<i;break}if(!f){n=0;f=0;return (u(n|0),f)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;n=0;f=0;return (u(n|0),f)|0}g=h-1|0;if(g&h|0){i=(s(h|0)|0)+33-(s(k|0)|0)|0;p=64-i|0;m=32-i|0;j=m>>31;o=i-32|0;b=o>>31;h=i;a=m-1>>31&k>>>(o>>>0)|(k<<m|l>>>(i>>>0))&b;b=b&k>>>(i>>>0);g=l<<p&j;i=(k<<p|l>>>(o>>>0))&j|l<<m&i-33>>31;break}if(f|0){c[f>>2]=g&l;c[f+4>>2]=0}if((h|0)==1){o=j|b&0;p=a|0|0;return (u(o|0),p)|0}else{p=ld(h|0)|0;o=k>>>(p>>>0)|0;p=k<<32-p|l>>>(p>>>0)|0;return (u(o|0),p)|0}}else{if(g){if(f|0){c[f>>2]=(k>>>0)%(h>>>0);c[f+4>>2]=0}o=0;p=(k>>>0)/(h>>>0)>>>0;return (u(o|0),p)|0}if(!l){if(f|0){c[f>>2]=0;c[f+4>>2]=(k>>>0)%(i>>>0)}o=0;p=(k>>>0)/(i>>>0)>>>0;return (u(o|0),p)|0}g=i-1|0;if(!(g&i)){if(f|0){c[f>>2]=a|0;c[f+4>>2]=g&k|b&0}o=0;p=k>>>((ld(i|0)|0)>>>0);return (u(o|0),p)|0}g=(s(i|0)|0)-(s(k|0)|0)|0;if(g>>>0<=30){b=g+1|0;i=31-g|0;h=b;a=k<<i|l>>>(b>>>0);b=k>>>(b>>>0);g=0;i=l<<i;break}if(!f){o=0;p=0;return (u(o|0),p)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;o=0;p=0;return (u(o|0),p)|0}while(0);if(!h){k=i;j=0;i=0}else{m=d|0|0;l=n|e&0;k=jd(m|0,l|0,-1,-1)|0;d=v()|0;j=i;i=0;do{e=j;j=g>>>31|j<<1;g=i|g<<1;e=a<<1|e>>>31|0;n=a>>>31|b<<1|0;kd(k|0,d|0,e|0,n|0)|0;p=v()|0;o=p>>31|((p|0)<0?-1:0)<<1;i=o&1;a=kd(e|0,n|0,o&m|0,(((p|0)<0?-1:0)>>31|((p|0)<0?-1:0)<<1)&l|0)|0;b=v()|0;h=h-1|0}while((h|0)!=0);k=j;j=0}h=0;if(f|0){c[f>>2]=a;c[f+4>>2]=b}o=(g|0)>>>31|(k|h)<<1|(h<<1|g>>>31)&0|j;p=(g<<1|0>>>31)&-2|i;return (u(o|0),p)|0}function nd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return md(a,b,c,d,0)|0}function od(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){u(b>>>c|0);return a>>>c|(b&(1<<c)-1)<<32-c}u(0);return b>>>c-32|0}function pd(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){u(b<<c|(a&(1<<c)-1<<32-c)>>>32-c|0);return a<<c}u(a<<c-32|0);return 0}function qd(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0;l=l+1|0;c[a>>2]=l;while((f|0)<(e|0)){if(!(c[d+(f<<3)>>2]|0)){c[d+(f<<3)>>2]=l;c[d+((f<<3)+4)>>2]=b;c[d+((f<<3)+8)>>2]=0;u(e|0);return d|0}f=f+1|0}e=e*2|0;d=Gc(d|0,8*(e+1|0)|0)|0;d=qd(a|0,b|0,d|0,e|0)|0;u(e|0);return d|0}function rd(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0;while((f|0)<(d|0)){e=c[b+(f<<3)>>2]|0;if(!e)break;if((e|0)==(a|0))return c[b+((f<<3)+4)>>2]|0;f=f+1|0}return 0}function sd(a,b){a=a|0;b=b|0;if(!j){j=a;k=b}}function td(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0;if((e|0)>=8192){G(b|0,d|0,e|0)|0;return b|0}h=b|0;g=b+e|0;if((b&3)==(d&3)){while(b&3){if(!e)return h|0;a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}e=g&-4|0;f=e-64|0;while((b|0)<=(f|0)){c[b>>2]=c[d>>2];c[b+4>>2]=c[d+4>>2];c[b+8>>2]=c[d+8>>2];c[b+12>>2]=c[d+12>>2];c[b+16>>2]=c[d+16>>2];c[b+20>>2]=c[d+20>>2];c[b+24>>2]=c[d+24>>2];c[b+28>>2]=c[d+28>>2];c[b+32>>2]=c[d+32>>2];c[b+36>>2]=c[d+36>>2];c[b+40>>2]=c[d+40>>2];c[b+44>>2]=c[d+44>>2];c[b+48>>2]=c[d+48>>2];c[b+52>>2]=c[d+52>>2];c[b+56>>2]=c[d+56>>2];c[b+60>>2]=c[d+60>>2];b=b+64|0;d=d+64|0}while((b|0)<(e|0)){c[b>>2]=c[d>>2];b=b+4|0;d=d+4|0}}else{e=g-4|0;while((b|0)<(e|0)){a[b>>0]=a[d>>0]|0;a[b+1>>0]=a[d+1>>0]|0;a[b+2>>0]=a[d+2>>0]|0;a[b+3>>0]=a[d+3>>0]|0;b=b+4|0;d=d+4|0}}while((b|0)<(g|0)){a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0}return h|0}function ud(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=b+e|0;d=d&255;if((e|0)>=67){while(b&3){a[b>>0]=d;b=b+1|0}f=h&-4|0;i=d|d<<8|d<<16|d<<24;g=f-64|0;while((b|0)<=(g|0)){c[b>>2]=i;c[b+4>>2]=i;c[b+8>>2]=i;c[b+12>>2]=i;c[b+16>>2]=i;c[b+20>>2]=i;c[b+24>>2]=i;c[b+28>>2]=i;c[b+32>>2]=i;c[b+36>>2]=i;c[b+40>>2]=i;c[b+44>>2]=i;c[b+48>>2]=i;c[b+52>>2]=i;c[b+56>>2]=i;c[b+60>>2]=i;b=b+64|0}while((b|0)<(f|0)){c[b>>2]=i;b=b+4|0}}while((b|0)<(h|0)){a[b>>0]=d;b=b+1|0}return h-e|0}function vd(a){a=a|0;var b=0,d=0;d=c[i>>2]|0;b=d+a|0;if((a|0)>0&(b|0)<(d|0)|(b|0)<0){K(b|0)|0;E(12);return -1}if((b|0)>(F()|0)){if(!(H(b|0)|0)){E(12);return -1}}else c[i>>2]=b;return d|0}function wd(a,b){a=a|0;b=b|0;return O[a&3](b|0)|0}function xd(a,b,c){a=a|0;b=b|0;c=c|0;return P[a&15](b|0,c|0)|0}function yd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Q[a&3](b|0,c|0,d|0)|0}function zd(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return R[a&3](b|0,c|0,d|0,e|0,f|0)|0}function Ad(a){a=a|0;S[a&3]()}function Bd(a,b){a=a|0;b=b|0;T[a&7](b|0)}function Cd(a,b,c){a=a|0;b=b|0;c=c|0;U[a&1](b|0,c|0)}function Dd(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;V[a&31](b|0,c|0,d|0,e|0)}function Ed(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;W[a&15](b|0,c|0,d|0,e|0,f|0,g|0)}function Fd(a){a=a|0;t(0);return 0}function Gd(a,b){a=a|0;b=b|0;t(1);return 0}function Hd(a,b,c){a=a|0;b=b|0;c=c|0;t(2);return 0}function Id(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;t(3);return 0}function Jd(){t(4)}function Kd(a){a=a|0;t(5)}function Ld(a,b){a=a|0;b=b|0;t(6)}function Md(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;t(7)}function Nd(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;t(8)}

// EMSCRIPTEN_END_FUNCS
var O=[Fd,ta,vb,Fd];var P=[Gd,ka,la,ma,na,oa,pa,qa,ra,sa,va,xa,Ea,Ia,Gd,Gd];var Q=[Hd,ua,gd,Pa];var R=[Id,wa,Ba,Id];var S=[Jd,Oa,qb,Jd];var T=[Kd,nb,Sa,Ka,Ra,tb,Xa,mb];var U=[Ld,Ma];var V=[Md,ja,Mb,Ob,Rb,Tb,Xb,Vb,_b,Lb,Nb,Qb,Sb,Wb,Ub,Zb,Yb,Pb,Eb,Db,Hb,Jb,Ib,Gb,Kb,Fb,Md,Md,Md,Md,Md,Md];var W=[Nd,dc,hc,fc,jc,ec,ic,gc,kc,Nd,Nd,Nd,Nd,Nd,Nd,Nd];return{___muldi3:id,___udivdi3:nd,_bitshift64Lshr:od,_bitshift64Shl:pd,_free:Fc,_i64Add:jd,_i64Subtract:kd,_malloc:Ec,_memcpy:td,_memset:ud,_ogv_video_decoder_async:aa,_ogv_video_decoder_destroy:ba,_ogv_video_decoder_init:$,_ogv_video_decoder_process_frame:da,_ogv_video_decoder_process_header:ca,_realloc:Gc,_saveSetjmp:qd,_sbrk:vd,_setThrew:sd,_testSetjmp:rd,dynCall_ii:wd,dynCall_iii:xd,dynCall_iiii:yd,dynCall_iiiiii:zd,dynCall_v:Ad,dynCall_vi:Bd,dynCall_vii:Cd,dynCall_viiii:Dd,dynCall_viiiiii:Ed,establishStackSpace:_,stackAlloc:X,stackRestore:Z,stackSave:Y}})


// EMSCRIPTEN_END_ASM
(va,Ea,buffer);b.___muldi3=S.___muldi3;b.___udivdi3=S.___udivdi3;b._bitshift64Lshr=S._bitshift64Lshr;b._bitshift64Shl=S._bitshift64Shl;b._free=S._free;b._i64Add=S._i64Add;b._i64Subtract=S._i64Subtract;b._malloc=S._malloc;b._memcpy=S._memcpy;b._memset=S._memset;b._ogv_video_decoder_async=S._ogv_video_decoder_async;
b._ogv_video_decoder_destroy=S._ogv_video_decoder_destroy;b._ogv_video_decoder_init=S._ogv_video_decoder_init;b._ogv_video_decoder_process_frame=S._ogv_video_decoder_process_frame;b._ogv_video_decoder_process_header=S._ogv_video_decoder_process_header;b._realloc=S._realloc;b._saveSetjmp=S._saveSetjmp;b._sbrk=S._sbrk;var R=b._setThrew=S._setThrew;b._testSetjmp=S._testSetjmp;b.establishStackSpace=S.establishStackSpace;b.stackAlloc=S.stackAlloc;
var Q=b.stackRestore=S.stackRestore,P=b.stackSave=S.stackSave,wa=b.dynCall_ii=S.dynCall_ii,xa=b.dynCall_iii=S.dynCall_iii,ya=b.dynCall_iiii=S.dynCall_iiii,za=b.dynCall_iiiiii=S.dynCall_iiiiii,Aa=b.dynCall_v=S.dynCall_v,Ba=b.dynCall_vi=S.dynCall_vi,Ca=b.dynCall_vii=S.dynCall_vii,Da=b.dynCall_viiii=S.dynCall_viiii;b.dynCall_viiiiii=S.dynCall_viiiiii;b.asm=S;
if(K){if(String.prototype.startsWith?!K.startsWith(O):0!==K.indexOf(O)){var Fa=K;K=b.locateFile?b.locateFile(Fa,v):v+Fa}if(t||u){var Ga=b.readBinary(K);C.set(Ga,8)}else{H++;b.monitorRunDependencies&&b.monitorRunDependencies(H);var T=function(a){a.byteLength&&(a=new Uint8Array(a));C.set(a,8);b.memoryInitializerRequest&&delete b.memoryInitializerRequest.response;H--;b.monitorRunDependencies&&b.monitorRunDependencies(H);0==H&&(null!==I&&(clearInterval(I),I=null),J&&(a=J,J=null,a()))},Ha=function(){b.readAsync(K,
T,function(){throw"could not load memory initializer "+K;})},Ia=A(K);if(Ia)T(Ia.buffer);else if(b.memoryInitializerRequest){var Ja=function(){var a=b.memoryInitializerRequest,c=a.response;if(200!==a.status&&0!==a.status)if(c=A(b.memoryInitializerRequestURL))c=c.buffer;else{console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+a.status+", retrying "+K);Ha();return}T(c)};b.memoryInitializerRequest.response?setTimeout(Ja,0):b.memoryInitializerRequest.addEventListener("load",
Ja)}else Ha()}}b.then=function(a){if(b.calledRun)a(b);else{var c=b.onRuntimeInitialized;b.onRuntimeInitialized=function(){c&&c();a(b)}}return b};function Ka(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}Ka.prototype=Error();Ka.prototype.constructor=Ka;J=function La(){b.calledRun||Na();b.calledRun||(J=La)};
function Na(){function a(){if(!b.calledRun&&(b.calledRun=!0,!ea)){ka||(ka=!0,F(ha));F(ia);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var a=b.postRun.shift();ja.unshift(a)}F(ja)}}if(!(0<H)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)la();F(fa);0<H||b.calledRun||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},
1);a()},1)):a())}}b.run=Na;function B(a){if(b.onAbort)b.onAbort(a);void 0!==a?(ba(a),ca(a),a=JSON.stringify(a)):a="";ea=!0;throw"abort("+a+"). Build with -s ASSERTIONS=1 for more info.";}b.abort=B;if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();b.noExitRuntime=!0;Na();var U,Oa,Pa;Pa="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);
function Z(a){var c=Pa();a=a();b.cpuTime+=Pa()-c;return a}b.loadedMetadata=!!g.videoFormat;b.videoFormat=g.videoFormat||null;b.frameBuffer=null;b.cpuTime=0;Object.defineProperty(b,"processing",{get:function(){return!1}});b.init=function(a){Z(function(){b._ogv_video_decoder_init()});a()};b.processHeader=function(a,c){var d=Z(function(){var c=a.byteLength;U&&Oa>=c||(U&&b._free(U),Oa=c,U=b._malloc(Oa));var d=U;b.HEAPU8.set(new Uint8Array(a),d);return b._ogv_video_decoder_process_header(d,c)});c(d)};
b.A=[];b.processFrame=function(a,c){function d(a){b._free(h);c(a)}var e=b._ogv_video_decoder_async(),f=a.byteLength,h=b._malloc(f);e&&b.A.push(d);var l=Z(function(){b.HEAPU8.set(new Uint8Array(a),h);return b._ogv_video_decoder_process_frame(h,f)});e||d(l)};b.close=function(){};b.sync=function(){b._ogv_video_decoder_async()&&(b.A.push(function(){}),Z(function(){b._ogv_video_decoder_process_frame(0,0)}))};



  return OGVDecoderVideoVP8
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
      module.exports = OGVDecoderVideoVP8;
    else if (typeof define === 'function' && define['amd'])
      define([], function() { return OGVDecoderVideoVP8; });
    else if (typeof exports === 'object')
      exports["OGVDecoderVideoVP8"] = OGVDecoderVideoVP8;
    