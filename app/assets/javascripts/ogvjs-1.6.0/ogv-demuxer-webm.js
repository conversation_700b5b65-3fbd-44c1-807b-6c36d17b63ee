
var OGVDemuxerWebM = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  return (
function(OGVDemuxerWebM) {
  OGVDemuxerWebM = OGVDemuxerWebM || {};

var a;a||(a=typeof OGVDemuxerWebM !== 'undefined' ? OGVDemuxerWebM : {});var aa=a;a.memoryLimit&&(a.TOTAL_MEMORY=aa.memoryLimit);var g={},l;for(l in a)a.hasOwnProperty(l)&&(g[l]=a[l]);a.arguments=[];a.thisProgram="./this.program";a.quit=function(b,c){throw c;};a.preRun=[];a.postRun=[];var m=!1,n=!1,p=!1,q=!1;m="object"===typeof window;n="function"===typeof importScripts;p="object"===typeof process&&"function"===typeof require&&!m&&!n;q=!m&&!p&&!n;var r="";
if(p){r=__dirname+"/";var t,u;a.read=function(b,c){var d=v(b);d||(t||(t=require("fs")),u||(u=require("path")),b=u.normalize(b),d=t.readFileSync(b));return c?d:d.toString()};a.readBinary=function(b){b=a.read(b,!0);b.buffer||(b=new Uint8Array(b));assert(b.buffer);return b};1<process.argv.length&&(a.thisProgram=process.argv[1].replace(/\\/g,"/"));a.arguments=process.argv.slice(2);process.on("unhandledRejection",x);a.quit=function(b){process.exit(b)};a.inspect=function(){return"[Emscripten Module object]"}}else if(q)"undefined"!=
typeof read&&(a.read=function(b){var c=v(b);return c?y(c):read(b)}),a.readBinary=function(b){var c;if(c=v(b))return c;if("function"===typeof readbuffer)return new Uint8Array(readbuffer(b));c=read(b,"binary");assert("object"===typeof c);return c},"undefined"!=typeof scriptArgs?a.arguments=scriptArgs:"undefined"!=typeof arguments&&(a.arguments=arguments),"function"===typeof quit&&(a.quit=function(b){quit(b)});else if(m||n)n?r=self.location.href:document.currentScript&&(r=document.currentScript.src),
_scriptDir&&(r=_scriptDir),0!==r.indexOf("blob:")?r=r.substr(0,r.lastIndexOf("/")+1):r="",a.read=function(b){try{var c=new XMLHttpRequest;c.open("GET",b,!1);c.send(null);return c.responseText}catch(d){if(b=v(b))return y(b);throw d;}},n&&(a.readBinary=function(b){try{var c=new XMLHttpRequest;c.open("GET",b,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}catch(d){if(b=v(b))return b;throw d;}}),a.readAsync=function(b,c,d){var e=new XMLHttpRequest;e.open("GET",b,!0);e.responseType=
"arraybuffer";e.onload=function(){if(200==e.status||0==e.status&&e.response)c(e.response);else{var f=v(b);f?c(f.buffer):d()}};e.onerror=d;e.send(null)},a.setWindowTitle=function(b){document.title=b};var z=a.print||("undefined"!==typeof console?console.log.bind(console):"undefined"!==typeof print?print:null),A=a.printErr||("undefined"!==typeof printErr?printErr:"undefined"!==typeof console&&console.warn.bind(console)||z);for(l in g)g.hasOwnProperty(l)&&(a[l]=g[l]);g=void 0;var B=0,C=!1;
function assert(b,c){b||x("Assertion failed: "+c)}var ba="undefined"!==typeof TextDecoder?new TextDecoder("utf8"):void 0;
function ca(b,c,d){var e=c+d;for(d=c;b[d]&&!(d>=e);)++d;if(16<d-c&&b.subarray&&ba)return ba.decode(b.subarray(c,d));for(e="";c<d;){var f=b[c++];if(f&128){var h=b[c++]&63;if(192==(f&224))e+=String.fromCharCode((f&31)<<6|h);else{var k=b[c++]&63;f=224==(f&240)?(f&15)<<12|h<<6|k:(f&7)<<18|h<<12|k<<6|b[c++]&63;65536>f?e+=String.fromCharCode(f):(f-=65536,e+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else e+=String.fromCharCode(f)}return e}function D(b){return b?ca(E,b,void 0):""}
"undefined"!==typeof TextDecoder&&new TextDecoder("utf-16le");var buffer,E,F,G=a.TOTAL_MEMORY||16777216;5242880>G&&A("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+G+"! (TOTAL_STACK=5242880)");a.buffer?buffer=a.buffer:(buffer=new ArrayBuffer(G),a.buffer=buffer);a.HEAP8=new Int8Array(buffer);a.HEAP16=new Int16Array(buffer);a.HEAP32=F=new Int32Array(buffer);a.HEAPU8=E=new Uint8Array(buffer);a.HEAPU16=new Uint16Array(buffer);a.HEAPU32=new Uint32Array(buffer);a.HEAPF32=new Float32Array(buffer);
a.HEAPF64=new Float64Array(buffer);F[2208]=5251968;function H(b){for(;0<b.length;){var c=b.shift();if("function"==typeof c)c();else{var d=c.D;"number"===typeof d?void 0===c.w?a.dynCall_v(d):a.dynCall_vi(d,c.w):d(void 0===c.w?null:c.w)}}}var da=[],ea=[],fa=[],ha=[],ia=!1;function ja(){var b=a.preRun.shift();da.unshift(b)}Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(b,c){var d=b&65535,e=c&65535;return d*e+((b>>>16)*e+d*(c>>>16)<<16)|0});
Math.clz32||(Math.clz32=function(b){var c=32,d=b>>16;d&&(c-=16,b=d);if(d=b>>8)c-=8,b=d;if(d=b>>4)c-=4,b=d;if(d=b>>2)c-=2,b=d;return b>>1?c-2:c-b});Math.trunc||(Math.trunc=function(b){return 0>b?Math.ceil(b):Math.floor(b)});var J=0,K=null,L=null;a.preloadedImages={};a.preloadedAudios={};var M=null,N="data:application/octet-stream;base64,";M="data:application/octet-stream;base64,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";
var O=[null,[],[]];function P(b,c){var d=O[b];0===c||10===c?((1===b?z:A)(ca(d,0)),d.length=0):d.push(c)}var Q=0;function R(){Q+=4;return F[Q-4>>2]}var ka={};function la(){x("OOM")}var ma=!1;function y(b){for(var c=[],d=0;d<b.length;d++){var e=b[d];255<e&&(ma&&assert(!1,"Character code "+e+" ("+String.fromCharCode(e)+")  at offset "+d+" not in 0x00-0xFF."),e&=255);c.push(String.fromCharCode(e))}return c.join("")}
var na="function"===typeof atob?atob:function(b){var c="",d=0;b=b.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));var h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));var k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(d++));e=e<<2|f>>4;
f=(f&15)<<4|h>>2;var w=(h&3)<<6|k;c+=String.fromCharCode(e);64!==h&&(c+=String.fromCharCode(f));64!==k&&(c+=String.fromCharCode(w))}while(d<b.length);return c};
function v(b){if(String.prototype.startsWith?b.startsWith(N):0===b.indexOf(N)){b=b.slice(N.length);if("boolean"===typeof p&&p){try{var c=Buffer.from(b,"base64")}catch(h){c=new Buffer(b,"base64")}var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength)}else try{var e=na(b),f=new Uint8Array(e.length);for(c=0;c<e.length;++c)f[c]=e.charCodeAt(c);d=f}catch(h){throw Error("Converting base64 string to bytes failed.");}return d}}
var oa={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Float32Array:Float32Array,Float64Array:Float64Array},pa={a:x,b:function(b){B=b},c:function(){return B},d:function(b,c,d,e){x("Assertion failed: "+D(b)+", at: "+[c?D(c):"unknown filename",d,e?D(e):"unknown function"])},e:function(b){a.___errno_location&&(F[a.___errno_location()>>2]=b);return b},f:function(b,c){Q=c;try{var d=ka.B();R();var e=R(),f=R(),h=R();(void 0).F(d,e,
h);F[f>>2]=d.position;d.C&&0===e&&0===h&&(d.C=null);return 0}catch(k){return x(k),-k.A}},g:function(b,c){Q=c;try{var d=R(),e=R(),f=R();for(c=b=0;c<f;c++){for(var h=F[e+8*c>>2],k=F[e+(8*c+4)>>2],w=0;w<k;w++)P(d,E[h+w]);b+=k}return b}catch(I){return x(I),-I.A}},h:function(b,c){Q=c;return 0},i:function(b,c){Q=c;try{var d=ka.B();(void 0).close(d);return 0}catch(e){return x(e),-e.A}},j:function(){a.abort()},k:function(){return G},l:function(b,c,d){E.set(E.subarray(c,c+d),b)},m:function(b){la(b)},n:function(b,
c,d,e){a.audioPackets.push({data:a.HEAPU8.buffer.slice?a.HEAPU8.buffer.slice(b,b+c):(new Uint8Array(new Uint8Array(a.HEAPU8.buffer,b,c))).buffer,timestamp:d,discardPadding:e})},o:function(b,c,d,e,f,h,k,w,I,ta,ua){a.videoFormat={width:b,height:c,chromaWidth:d,chromaHeight:e,cropLeft:w,cropTop:I,cropWidth:h,cropHeight:k,displayWidth:ta,displayHeight:ua,fps:f}},p:function(b,c){function d(b){for(var c="",d=a.HEAPU8;0!=d[b];b++)c+=String.fromCharCode(d[b]);return c}b&&(a.videoCodec=d(b));c&&(a.audioCodec=
d(c));b=a._ogv_demuxer_media_duration();a.duration=0<=b?b:NaN;a.loadedMetadata=!0},q:function(b,c){if(a.onseek)a.onseek(b+4294967296*c)},r:function(b,c,d,e,f){a.videoPackets.push({data:a.HEAPU8.buffer.slice?a.HEAPU8.buffer.slice(b,b+c):(new Uint8Array(new Uint8Array(a.HEAPU8.buffer,b,c))).buffer,timestamp:d,keyframeTimestamp:e,isKeyframe:!!f})},s:la,t:function(){var b=a._fflush;b&&b(0);O[1].length&&P(1,10);O[2].length&&P(2,10)},u:9072,v:8832};// EMSCRIPTEN_START_ASM

var S=(/** @suppress {uselessCode} */ function(global,env,buffer) {
"use asm";var a=new global.Int8Array(buffer),b=new global.Int16Array(buffer),c=new global.Int32Array(buffer),d=new global.Uint8Array(buffer),e=new global.Uint16Array(buffer),f=new global.Float32Array(buffer),g=new global.Float64Array(buffer),h=env.u|0,i=env.v|0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=global.Math.floor,s=global.Math.abs,t=global.Math.ceil,u=global.Math.imul,v=global.Math.min,w=global.Math.clz32,x=env.a,y=env.b,z=env.c,A=env.d,B=env.e,C=env.f,D=env.g,E=env.h,F=env.i,G=env.j,H=env.k,I=env.l,J=env.m,K=env.n,L=env.o,M=env.p,N=env.q,O=env.r,P=env.s,Q=env.t,R=9088,S=5251968,T=0.0;
// EMSCRIPTEN_START_FUNCS
function Y(a){a=a|0;var b=0;b=R;R=R+a|0;R=R+15&-16;return b|0}function Z(){return R|0}function _(a){a=a|0;R=a}function $(a,b){a=a|0;b=b|0;R=a;S=b}function aa(){c[2052]=0;c[2053]=pa()|0;return}function ba(a,b){a=a|0;b=b|0;if((b|0)<=0)return;ua(c[2053]|0,a,b);return}function ca(){var b=0,e=0,f=0.0,h=0.0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;r=R;R=R+96|0;o=r+72|0;e=r+56|0;p=r+16|0;k=r+8|0;l=r+64|0;m=r+60|0;i=r;switch(c[2052]|0){case 0:{b=c[2053]|0;c[949]=b;b=ra(b)|0;n=z()|0;c[o>>2]=c[946];c[o+4>>2]=c[947];c[o+8>>2]=c[948];c[o+12>>2]=c[949];n=(xa(8216,o,1,b,n)|0)<0;b=c[2053]|0;if(n){sa(b,0,0)|0;q=0;R=r;return q|0}i=Lb(qa(b)|0,z()|0,-12,-1)|0;j=z()|0;n=8200;c[n>>2]=i;c[n+4>>2]=j;if((Da(c[2054]|0,o)|0)>=0)if(!(c[o>>2]|0))q=8;else{j=0;do{e=Ia(c[2054]|0,j)|0;i=Ja(c[2054]|0,j)|0;b=a[8804]|0;a:do if(!((e|0)==0&b<<24>>24==0)){if((e|0)==1&(a[8805]|0)==0)switch(i|0){case 1:{a[8805]=1;c[2057]=j;c[2059]=4184;break a}case 3:{a[8805]=1;c[2057]=j;c[2059]=4191;break a}default:break a}}else switch(i|0){case 0:{a[8804]=1;c[2055]=j;c[2058]=4172;b=1;break a}case 2:{a[8804]=1;c[2055]=j;c[2058]=4176;b=1;break a}case 4:{a[8804]=1;c[2055]=j;c[2058]=4180;b=1;break a}default:{b=0;break a}}while(0);j=j+1|0}while(j>>>0<(c[o>>2]|0)>>>0)}else{c[o>>2]=0;q=8}if((q|0)==8)b=a[8804]|0;if(b<<24>>24)if((Ma(c[2054]|0,c[2055]|0,p)|0)<0)a[8804]=0;else{j=c[p+4>>2]|0;n=c[p+8>>2]|0;o=c[p+28>>2]|0;q=c[p+24>>2]|0;L(j|0,n|0,j>>>1|0,n>>>1|0,0.0,j-o-(c[p+32>>2]|0)|0,n-q-(c[p+20>>2]|0)|0,o|0,q|0,c[p+12>>2]|0,c[p+16>>2]|0)}if(a[8805]|0)if((Na(c[2054]|0,c[2057]|0,p)|0)<0)a[8805]=0;else{Ka(c[2054]|0,c[2057]|0,k)|0;b:do if(c[k>>2]|0){b=0;while(1){if((La(c[2054]|0,c[2057]|0,b,l,m)|0)<0)break;K(c[l>>2]|0,c[m>>2]|0,-1.0,0.0);b=b+1|0;if(b>>>0>=(c[k>>2]|0)>>>0)break b}G()}while(0)}c[2052]=1;M(c[2058]|0,c[2059]|0);q=1;R=r;return q|0}case 1:{c[o>>2]=0;b=Pa(c[2054]|0,o)|0;do if(!b){Oa(c[2054]|0)|0;b=0}else{if((b|0)<0){c[e>>2]=b;Hb(4196,e)|0;b=0;break}Ta(c[o>>2]|0,p)|0;Ua(c[o>>2]|0,k)|0;n=k;f=(+((c[n>>2]|0)>>>0)+4294967296.0*+((c[n+4>>2]|0)>>>0))/1.0e9;c[l>>2]=0;c[m>>2]=0;Wa(c[o>>2]|0,0,l,m)|0;if((a[8804]|0)!=0?(c[p>>2]|0)==(c[2055]|0):0){b=(Sa(c[o>>2]|0)|0)==1;if(b){h=f;g[472]=h}else h=+g[472];O(c[l>>2]|0,c[m>>2]|0,+f,+h,b&1|0)}else q=45;if(((q|0)==45?a[8805]|0:0)?(c[p>>2]|0)==(c[2057]|0):0){q=i;c[q>>2]=0;c[q+4>>2]=0;Va(c[o>>2]|0,i)|0;q=i;K(c[l>>2]|0,c[m>>2]|0,+f,+(+((c[q>>2]|0)>>>0)+4294967296.0*+(c[q+4>>2]|0)))}Ra(c[o>>2]|0);b=1}while(0);q=b;R=r;return q|0}case 2:{m=qa(c[2053]|0)|0;n=z()|0;k=c[2053]|0;l=(va(k,o,1)|0)!=0;b=a[o>>0]|0;c:do if(!(l|b<<24>>24==0)){e=b&255;d:do if(!(e&128)){j=1;while(1){b=e<<1;l=e;e=b&254;if(l&64|0)break;else j=j+1|0}a[o>>0]=b;b=e>>>j;i=1;e=b;b=((b|0)<0)<<31>>31;while(1){if(va(k,p,1)|0)break;e=Rb(e|0,b|0,8)|0;b=z()|0;e=e|d[p>>0];if(i>>>0<j>>>0)i=i+1|0;else break d}break c}else{e=b&255;b=0}while(0);l=(e|0)!=475249515|(b|0)!=0;k=c[2053]|0;j=(va(k,o,1)|0)!=0;b=a[o>>0]|0;e:do if(!(j|b<<24>>24==0)){e=b&255;f:do if(!(e&128)){j=1;while(1){b=e<<1;if(!(e&64)){e=b&254;j=j+1|0}else break}e=b&126;a[o>>0]=e;e=(e&255)>>>j;i=1;b=e;e=((e|0)<0)<<31>>31;while(1){if(va(k,p,1)|0)break;b=Rb(b|0,e|0,8)|0;e=z()|0;b=b|d[p>>0];if(i>>>0<j>>>0)i=i+1|0;else break f}q=69;break e}else{b=b&127;a[o>>0]=b;e=0;b=b&255}while(0);p=ra(c[2053]|0)|0;o=z()|0;sa(c[2053]|0,m,n)|0;if(!(l|((o|0)>(e|0)|(o|0)==(e|0)&p>>>0>=b>>>0))){q=0;R=r;return q|0}}else q=69;while(0);if((q|0)==69?(sa(c[2053]|0,m,n)|0,!l):0){q=0;R=r;return q|0}q=oa()|0;R=r;return q|0}while(0);sa(c[2053]|0,m,n)|0;q=0;R=r;return q|0}default:{q=0;R=r;return q|0}}return 0}function da(){wa(c[2053]|0);c[2053]=0;return}function ea(){ta(c[2053]|0);g[472]=-1.0;return}function fa(){return -1}function ga(){var a=0,b=0,d=0.0;b=R;R=R+16|0;a=b;if((Ca(c[2054]|0,a)|0)<0){d=-1.0;R=b;return +d}d=(+((c[a>>2]|0)>>>0)+4294967296.0*+((c[a+4>>2]|0)>>>0))/1.0e9;R=b;return +d}function ha(){return 1}function ia(a){a=a|0;return -1}function ja(b){b=b|0;var d=0,e=0;c[2052]=2;e=Kb(b|0,((b|0)<0)<<31>>31|0,1e6,0)|0;d=z()|0;b=8192;c[b>>2]=e;c[b+4>>2]=d;if(!(a[8804]|0))if(!(a[8805]|0)){e=0;return e|0}else b=8228;else b=8220;c[2056]=c[b>>2];oa()|0;e=1;return e|0}function ka(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0;f=R;R=R+16|0;a=f;if(b>>>0>9){c[a>>2]=e;Ib(d,a)|0}R=f;return}function la(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=ra(c)|0;e=z()|0;if((e|0)<0|(e|0)==0&d>>>0<b>>>0){e=0;return e|0}e=(va(c,a,b)|0)==0;e=e?1:-1;return e|0}function ma(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;switch(d|0){case 0:break;case 1:{d=e+16|0;a=Lb(c[d>>2]|0,c[d+4>>2]|0,a|0,b|0)|0;b=z()|0;break}default:{e=-1;return e|0}}e=((sa(e,a,b)|0)!=0)<<31>>31;return e|0}function na(a){a=a|0;a=qa(a)|0;y(z()|0);return a|0}function oa(){var a=0,b=0,d=0;d=(c[2053]|0)+24|0;c[d>>2]=-1;c[d+4>>2]=-1;d=(Xa(c[2054]|0)|0)==0;a=c[2054]|0;if(d){d=8200;a=Ga(a,c[d>>2]|0,c[d+4>>2]|0)|0}else{d=8192;a=Ha(a,c[2056]|0,c[d>>2]|0,c[d+4>>2]|0)|0}if(!a){c[2052]=1;d=1;return d|0}a=c[2053]|0;d=a+24|0;b=c[d>>2]|0;d=c[d+4>>2]|0;if((b|0)==-1&(d|0)==-1){d=0;return d|0}ta(a);a=(c[2053]|0)+16|0;c[a>>2]=b;c[a+4>>2]=d;N(b|0,d|0);d=0;return d|0}function pa(){var a=0,b=0;a=Ya(32)|0;b=a+16|0;c[b>>2]=0;c[b+4>>2]=0;c[a+4>>2]=0;c[a+8>>2]=8;c[a>>2]=Ya(192)|0;return a|0}function qa(a){a=a|0;var b=0;b=a+16|0;a=c[b>>2]|0;y(c[b+4>>2]|0);return a|0}function ra(a){a=a|0;var b=0,d=0,e=0;b=c[a+4>>2]|0;if(!b){b=a+16|0;a=c[b>>2]|0;b=c[b+4>>2]|0;e=a;d=b;b=Mb(e|0,d|0,a|0,b|0)|0;a=z()|0;y(a|0);return b|0}else{e=c[a>>2]|0;d=b+-1|0;b=e+(d*24|0)+8|0;d=Lb(c[b>>2]|0,c[b+4>>2]|0,c[e+(d*24|0)+16>>2]|0,0)|0;b=z()|0;e=a+16|0;a=d;d=c[e>>2]|0;e=c[e+4>>2]|0;e=Mb(a|0,b|0,d|0,e|0)|0;d=z()|0;y(d|0);return e|0}return 0}function sa(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0;f=c[a+4>>2]|0;g=(f|0)==0;if(g)e=a+16|0;else e=(c[a>>2]|0)+8|0;h=c[e+4>>2]|0;do if(!((h|0)>(d|0)|((h|0)==(d|0)?(c[e>>2]|0)>>>0>b>>>0:0))){if(g){f=a+16|0;e=c[f+4>>2]|0;f=c[f>>2]|0}else{e=c[a>>2]|0;f=f+-1|0;h=e+(f*24|0)+8|0;f=Lb(c[h>>2]|0,c[h+4>>2]|0,c[e+(f*24|0)+16>>2]|0,0)|0;e=z()|0}if((e|0)<(d|0)|(e|0)==(d|0)&f>>>0<b>>>0){f=-1;e=a+24|0;break}else{f=0;e=a+16|0;break}}else{f=-1;e=a+24|0}while(0);h=e;c[h>>2]=b;c[h+4>>2]=d;return f|0}function ta(a){a=a|0;var b=0,d=0,e=0;d=a+4|0;if(c[d>>2]|0){b=0;e=c[a>>2]|0;do{Za(c[e+(b*24|0)>>2]|0);e=c[a>>2]|0;c[e+(b*24|0)>>2]=0;b=b+1|0}while(b>>>0<(c[d>>2]|0)>>>0)}c[d>>2]=0;e=a+16|0;c[e>>2]=0;c[e+4>>2]=0;return}function ua(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;k=a+4|0;e=c[k>>2]|0;j=a+8|0;do if((e|0)==(c[j>>2]|0))if(!e)e=0;else{h=a+16|0;g=0;f=c[a>>2]|0;do{m=f+(g*24|0)+8|0;m=Lb(c[m>>2]|0,c[m+4>>2]|0,c[f+(g*24|0)+16>>2]|0,0)|0;o=z()|0;l=h;n=c[l+4>>2]|0;if(!((o|0)<(n|0)|((o|0)==(n|0)?m>>>0<(c[l>>2]|0)>>>0:0))){i=6;break}Za(c[f+(g*24|0)>>2]|0);f=c[a>>2]|0;c[f+(g*24|0)>>2]=0;g=g+1|0;e=c[k>>2]|0}while(g>>>0<e>>>0);if((i|0)==6)if(!g)break;e=e-g|0;c[k>>2]=e;Tb(f|0,f+(g*24|0)|0,e*24|0)|0;e=c[k>>2]|0}while(0);o=c[j>>2]|0;f=o+8|0;if((e|0)==(o|0)){c[j>>2]=f;c[a>>2]=$a(c[a>>2]|0,f*24|0)|0;e=c[k>>2]|0}if(!e){m=a+16|0;n=c[a>>2]|0;a=c[m>>2]|0;m=c[m+4>>2]|0;o=n+(e*24|0)+8|0;l=o;c[l>>2]=a;o=o+4|0;c[o>>2]=m;o=n+(e*24|0)+16|0;c[o>>2]=d;o=Ya(d)|0;n=n+(e*24|0)|0;c[n>>2]=o;Sb(o|0,b|0,d|0)|0;o=e+1|0;c[k>>2]=o;return}else{n=c[a>>2]|0;a=e+-1|0;m=n+(a*24|0)+8|0;a=Lb(c[m>>2]|0,c[m+4>>2]|0,c[n+(a*24|0)+16>>2]|0,0)|0;m=z()|0;o=n+(e*24|0)+8|0;l=o;c[l>>2]=a;o=o+4|0;c[o>>2]=m;o=n+(e*24|0)+16|0;c[o>>2]=d;o=Ya(d)|0;n=n+(e*24|0)|0;c[n>>2]=o;Sb(o|0,b|0,d|0)|0;o=e+1|0;c[k>>2]=o;return}}function va(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;o=a+4|0;j=c[o>>2]|0;g=(j|0)==0;if(g){h=a+16|0;i=c[h>>2]|0;h=c[h+4>>2]|0;e=i;f=h}else{f=c[a>>2]|0;e=j+-1|0;h=f+(e*24|0)+8|0;e=Lb(c[h>>2]|0,c[h+4>>2]|0,c[f+(e*24|0)+16>>2]|0,0)|0;f=z()|0;h=a+16|0;i=c[h>>2]|0;h=c[h+4>>2]|0}n=Mb(e|0,f|0,i|0,h|0)|0;m=z()|0;if((m|0)<0|(m|0)==0&n>>>0<d>>>0|g){o=-1;return o|0}n=a+16|0;m=0;e=d;l=0;f=h;k=j;while(1){j=c[a>>2]|0;d=j+(m*24|0)+8|0;g=c[d>>2]|0;d=c[d+4>>2]|0;h=c[j+(m*24|0)+16>>2]|0;p=Lb(g|0,d|0,h|0,0)|0;q=z()|0;if((q|0)<(f|0)|(q|0)==(f|0)&p>>>0<i>>>0){g=l;h=k;d=i}else{d=Mb(i|0,f|0,g|0,d|0)|0;z()|0;g=h-d|0;g=g>>>0>e>>>0?e:g;Sb(b+l|0,(c[j+(m*24|0)>>2]|0)+d|0,g|0)|0;d=n;d=Lb(c[d>>2]|0,c[d+4>>2]|0,g|0,0)|0;f=z()|0;q=n;c[q>>2]=d;c[q+4>>2]=f;e=e-g|0;if(!e){e=0;f=10;break}g=g+l|0;h=c[o>>2]|0}m=m+1|0;if(m>>>0>=h>>>0){e=-1;f=10;break}else{l=g;i=d;k=h}}if((f|0)==10)return e|0;return 0}function wa(a){a=a|0;var b=0,d=0,e=0;e=a+4|0;if(!(c[e>>2]|0))b=c[a>>2]|0;else{d=0;b=c[a>>2]|0;do{Za(c[b+(d*24|0)>>2]|0);b=c[a>>2]|0;c[b+(d*24|0)>>2]=0;d=d+1|0}while(d>>>0<(c[e>>2]|0)>>>0)}c[e>>2]=0;e=a+16|0;c[e>>2]=0;c[e+4>>2]=0;Za(b);Za(a);return}function xa(a,b,e,f,g){a=a|0;b=b|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;p=R;R=R+16|0;n=p+8|0;m=p;j=c[b>>2]|0;k=c[b+4>>2]|0;l=c[b+8>>2]|0;b=c[b+12>>2]|0;if((j|0)==0|(k|0)==0|(l|0)==0){a=-1;R=p;return a|0}o=_a(1,272)|0;if(!o){a=-1;R=p;return a|0}i=_a(1,16)|0;c[o>>2]=i;if(!i){ya(o);a=-1;R=p;return a|0}c[i>>2]=j;c[i+4>>2]=k;c[i+8>>2]=l;c[i+12>>2]=b;b=o+4|0;c[b>>2]=e;l=_a(1,4)|0;c[o+8>>2]=l;if(!l){ya(o);a=-1;R=p;return a|0}if(!e)c[b>>2]=2;if((Aa(o,m,0)|0)!=1){ya(o);a=-1;R=p;return a|0}if(!((c[m>>2]|0)==440786851&(c[m+4>>2]|0)==0)){ya(o);a=-1;R=p;return a|0}b=c[b>>2]|0;c[n>>2]=o;X[b&3](o,1,4228,n);b=_a(1,12)|0;j=o+36|0;if(b|0){c[b>>2]=c[j>>2];c[b+4>>2]=16;c[b+8>>2]=o;c[j>>2]=b}i=Ba(o,0,f,g)|0;b=c[j>>2]|0;if(b|0)do{c[j>>2]=c[b>>2];Za(b);b=c[j>>2]|0}while((b|0)!=0);if((i|0)!=1){ya(o);a=-1;R=p;return a|0}i=o+56|0;b=c[i>>2]|0;i=c[i+4>>2]|0;if(c[o+68>>2]|0){if((c[o+64>>2]|0)!=2)A(4235,4258,828,4292);if(!((b|0)==1&(i|0)==0)){ya(o);a=-1;R=p;return a|0}}g=o+116|0;if(d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24){g=o+112|0;b=o+104|0;if((d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24|0)==4)h=d[b>>0]|d[b+1>>0]<<8|d[b+2>>0]<<16|d[b+3>>0]<<24;else A(4304,4258,854,4329)}else h=4343;if(lb(h,4352)|0?lb(h,4343)|0:0){ya(o);a=-1;R=p;return a|0}b=o+136|0;h=c[b>>2]|0;b=c[b+4>>2]|0;if(c[o+148>>2]|0){if((c[o+144>>2]|0)!=2)A(4235,4258,828,4292);g=Lb(h|0,b|0,-1,-1)|0;f=z()|0;if(f>>>0>0|(f|0)==0&g>>>0>1){ya(o);a=-1;R=p;return a|0}}h=c[o+192>>2]|0;if(!h){ya(o);a=-1;R=p;return a|0}i=o+216|0;c[i>>2]=0;b=0;do{b=b+1|0;h=c[h>>2]|0}while((h|0)!=0);c[i>>2]=b;n=c[o>>2]|0;n=U[c[n+8>>2]&3](c[n+12>>2]|0)|0;g=z()|0;f=o+240|0;c[f>>2]=n;c[f+4>>2]=g;if((g|0)<0){ya(o);a=-1;R=p;return a|0}else{g=o+16|0;f=c[g+4>>2]|0;n=o+248|0;c[n>>2]=c[g>>2];c[n+4>>2]=f;n=o+24|0;f=c[n+4>>2]|0;g=o+256|0;c[g>>2]=c[n>>2];c[g+4>>2]=f;c[o+264>>2]=c[o+32>>2];c[a>>2]=o;a=0;R=p;return a|0}return 0}function ya(a){a=a|0;var b=0,d=0,e=0;if(c[a+36>>2]|0)A(5775,4258,2178,5797);d=c[a+8>>2]|0;b=c[d>>2]|0;if(!b){Za(d);d=c[a>>2]|0;Za(d);Za(a);return}do{e=b;b=c[b>>2]|0;Za(c[e+4>>2]|0);Za(e)}while((b|0)!=0);Za(d);e=c[a>>2]|0;Za(e);Za(a);return}function za(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return}function Aa(b,e,f){b=b|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;p=R;R=R+16|0;m=p;o=b+32|0;if(c[o>>2]|0){if(e|0){m=b+16|0;n=c[m+4>>2]|0;o=e;c[o>>2]=c[m>>2];c[o+4>>2]=n}if(!f){o=1;R=p;return o|0}m=b+24|0;n=c[m+4>>2]|0;o=f;c[o>>2]=c[m>>2];c[o+4>>2]=n;o=1;R=p;return o|0}j=c[b>>2]|0;n=b+16|0;i=j+12|0;g=V[c[j>>2]&7](m,1,c[i>>2]|0)|0;a:do if((g|0)==1){h=a[m>>0]|0;g=h&255;do if(!(g&128)){if(!(g&64))if(!(g&32))if(!(g&16))if(!(g&8))if(!(g&4)){k=n;c[k>>2]=h&255;c[k+4>>2]=0;g=8-(g>>>1&1)+-1|0;if(!g)break}else{g=5;l=10}else{g=4;l=10}else{g=3;l=10}else{g=2;l=10}else{g=1;l=10}if((l|0)==10){k=n;c[k>>2]=h&255;c[k+4>>2]=0}h=g;do{g=V[c[j>>2]&7](m,1,c[i>>2]|0)|0;if((g|0)!=1)break a;q=n;q=Rb(c[q>>2]|0,c[q+4>>2]|0,8)|0;g=z()|0;k=n;c[k>>2]=q|(d[m>>0]|0);c[k+4>>2]=g;h=h+-1|0}while((h|0)!=0)}else{q=n;c[q>>2]=h&255;c[q+4>>2]=0}while(0);k=c[b>>2]|0;b=b+24|0;j=k+12|0;g=V[c[k>>2]&7](m,1,c[j>>2]|0)|0;b:do if((g|0)==1){i=d[m>>0]|0;do if(!(i&128)){if(!(i&64))if(!(i&32))if(!(i&16))if(!(i&8)){g=i>>>1&1;if(!(i&4)){q=b;c[q>>2]=254-g&i;c[q+4>>2]=0;g=8-g+-1|0;if(!g)break}else{h=-5;g=5;l=24}}else{h=-9;g=4;l=24}else{h=-17;g=3;l=24}else{h=-33;g=2;l=24}else{h=-65;g=1;l=24}if((l|0)==24){q=b;c[q>>2]=h&i;c[q+4>>2]=0}h=g;do{g=V[c[k>>2]&7](m,1,c[j>>2]|0)|0;if((g|0)!=1)break b;i=b;i=Rb(c[i>>2]|0,c[i+4>>2]|0,8)|0;l=z()|0;q=b;c[q>>2]=i|(d[m>>0]|0);c[q+4>>2]=l;h=h+-1|0}while((h|0)!=0)}else{q=b;c[q>>2]=i&127;c[q+4>>2]=0}while(0);if(e|0){m=n;n=c[m+4>>2]|0;q=e;c[q>>2]=c[m>>2];c[q+4>>2]=n}if(f|0){m=b;n=c[m+4>>2]|0;q=f;c[q>>2]=c[m>>2];c[q+4>>2]=n}c[o>>2]=1;q=1;R=p;return q|0}while(0);q=g;R=p;return q|0}while(0);q=g;R=p;return q|0}function Ba(b,e,i,j){b=b|0;e=e|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,S=0,T=0,W=0,Y=0.0;W=R;R=R+8336|0;Q=W+8320|0;P=W+8312|0;O=W+8304|0;N=W+8280|0;M=W+8264|0;L=W+8256|0;K=W+8240|0;S=W+8232|0;J=W+8216|0;I=W+8208|0;F=W;G=W+8200|0;H=W+8192|0;T=b+36|0;if(!(c[T>>2]|0))A(4357,4258,1099,4371);x=(j|0)>0|(j|0)==0&i>>>0>0;y=b+32|0;B=b+4|0;C=(e|0)==0;D=b+8|0;a:while(1){if(x?(v=c[b>>2]|0,v=U[c[v+8>>2]&3](c[v+12>>2]|0)|0,u=z()|0,!((u|0)<(j|0)|(u|0)==(j|0)&v>>>0<i>>>0)):0){k=1;w=103;break}m=Aa(b,G,H)|0;if((m|0)!=1){k=m;break}s=G;q=c[s>>2]|0;s=c[s+4>>2]|0;p=c[T>>2]|0;m=c[p+4>>2]|0;o=m+8|0;n=c[o>>2]|0;o=c[o+4>>2]|0;b:do if(!((n|0)==0&(o|0)==0)){t=m;m=o;while(1){if((n|0)==(q|0)&(m|0)==(s|0))break;m=t+48|0;n=c[m>>2]|0;m=c[m+4>>2]|0;if((n|0)==0&(m|0)==0){w=78;break b}else t=t+40|0}if(t){r=t+24|0;if(c[r>>2]&2|0){w=13;break a}m=Aa(b,G,H)|0;if((m|0)!=1){k=m;break a}c[y>>2]=0;v=G;if(!((c[v>>2]|0)==(q|0)?(c[v+4>>2]|0)==(s|0):0)){w=19;break a}if(c[r>>2]&4|0?(u=(c[(c[T>>2]|0)+8>>2]|0)+(c[t+36>>2]|0)|0,s=c[b>>2]|0,s=U[c[s+8>>2]&3](c[s+12>>2]|0)|0,v=z()|0,u,c[u>>2]=s,c[u+4>>2]=v,(v|0)<0):0){k=-1;break a}o=t+16|0;p=c[o>>2]|0;if((p|0)==1){m=c[B>>2]|0;q=t+8|0;o=q;n=c[o>>2]|0;o=c[o+4>>2]|0;p=c[t>>2]|0;if(!(c[r>>2]&1)){q=K;c[q>>2]=n;c[q+4>>2]=o;c[K+8>>2]=p;X[m&3](b,1,4525,K);m=c[B>>2]|0;p=t+20|0;n=c[p>>2]|0;c[L>>2]=(c[(c[T>>2]|0)+8>>2]|0)+n;c[L+4>>2]=n;X[m&3](b,1,4557,L);m=c[t+28>>2]|0;n=c[T>>2]|0;o=c[n+8>>2]|0;p=c[p>>2]|0;q=_a(1,12)|0;if(!q){k=1;w=103;break a}c[q>>2]=n;c[q+4>>2]=m;c[q+8>>2]=o+p;c[T>>2]=q;break}r=J;c[r>>2]=n;c[r+4>>2]=o;c[J+8>>2]=p;X[m&3](b,1,4476,J);r=(c[(c[T>>2]|0)+8>>2]|0)+(c[t+20>>2]|0)|0;o=c[D>>2]|0;m=_a(1,8)|0;if(!m){k=1;w=103;break a}s=_a(1,24)|0;c[m+4>>2]=s;if(!s){w=26;break a}c[m>>2]=c[o>>2];c[o>>2]=m;v=q;n=c[v+4>>2]|0;m=s+8|0;c[m>>2]=c[v>>2];c[m+4>>2]=n;m=c[t+32>>2]|0;n=_a(1,8)|0;if(!n){w=30;break a}q=_a(1,m)|0;c[n+4>>2]=q;if(!q){w=29;break a}c[n>>2]=c[o>>2];c[o>>2]=n;p=s+16|0;c[p>>2]=q;m=r+4|0;n=c[m>>2]|0;if(n|0)c[n>>2]=s;c[m>>2]=s;if(!(c[r>>2]|0))c[r>>2]=s;o=c[B>>2]|0;c[S>>2]=q;X[o&3](b,1,4507,S);o=c[t+28>>2]|0;m=c[p>>2]|0;n=_a(1,12)|0;if(!n){k=1;w=103;break a}c[n>>2]=c[T>>2];c[n+4>>2]=o;c[n+8>>2]=m;c[T>>2]=n;break}n=c[H>>2]|0;m=c[t+20>>2]|0;u=(c[(c[T>>2]|0)+8>>2]|0)+m|0;v=u+12|0;c:do if(!(c[v>>2]|0)){c[u+8>>2]=p;s=c[B>>2]|0;p=t+8|0;q=c[p+4>>2]|0;t=c[t>>2]|0;r=N;c[r>>2]=c[p>>2];c[r+4>>2]=q;c[N+8>>2]=t;c[N+12>>2]=u;c[N+16>>2]=m;X[s&3](b,1,4624,N);d:do switch(c[o>>2]|0){case 2:{p=c[b>>2]|0;m=Lb(n|0,0,-1,-1)|0;n=z()|0;e:do if(!(n>>>0>0|(n|0)==0&m>>>0>7)){o=p+12|0;l=V[c[p>>2]&7](F,1,c[o>>2]|0)|0;if((l|0)==1){w=u;c[w>>2]=d[F>>0];c[w+4>>2]=0;if((m|0)==0&(n|0)==0)l=1;else while(1){l=V[c[p>>2]&7](F,1,c[o>>2]|0)|0;if((l|0)!=1)break e;s=u;s=Rb(c[s>>2]|0,c[s+4>>2]|0,8)|0;t=z()|0;w=u;c[w>>2]=s|(d[F>>0]|0);c[w+4>>2]=t;m=Lb(m|0,n|0,-1,-1)|0;n=z()|0;if((m|0)==0&(n|0)==0){l=1;break}}}}else l=-1;while(0);w=74;break}case 3:{t=c[b>>2]|0;switch(n|0){case 4:case 8:break;default:{k=-1;break a}}s=t+12|0;m=V[c[t>>2]&7](F,1,c[s>>2]|0)|0;f:do if((m|0)==1){q=Lb(n|0,0,-1,-1)|0;r=z()|0;o=0;p=d[F>>0]|0;do{m=V[c[t>>2]&7](F,1,c[s>>2]|0)|0;if((m|0)!=1)break f;m=p;p=p<<8|(d[F>>0]|0);o=Qb(m|0,o|0,24)|0;z()|0;q=Lb(q|0,r|0,-1,-1)|0;r=z()|0}while(!((q|0)==0&(r|0)==0));Y=(c[h>>2]=p,+f[h>>2]);c[h>>2]=p;c[h+4>>2]=o;g[u>>3]=(n|0)==4?Y:+g[h>>3];break d}while(0);break c}case 4:{if(n>>>0>1048576){k=-1;break a}m=c[D>>2]|0;o=_a(1,8)|0;if(!o){k=-1;break a}p=_a(1,n+1|0)|0;c[o+4>>2]=p;if(!p){w=63;break a}c[o>>2]=c[m>>2];c[m>>2]=o;if(n|0?(E=c[b>>2]|0,E=V[c[E>>2]&7](p,n,c[E+12>>2]|0)|0,(E|0)!=1):0){m=E;break c}a[p+n>>0]=0;c[u>>2]=p;break}case 5:{t=Lb(n|0,0,-1,-1)|0;s=z()|0;if(s>>>0>0|(s|0)==0&t>>>0>16777215){k=-1;break a}l=c[D>>2]|0;o=_a(1,8)|0;if(!o){w=71;break a}m=_a(1,n)|0;c[o+4>>2]=m;if(!m){w=70;break a}c[o>>2]=c[l>>2];c[l>>2]=o;c[u>>2]=m;c[u+4>>2]=n;l=c[b>>2]|0;l=V[c[l>>2]&7](m,n,c[l+12>>2]|0)|0;w=74;break}default:{w=73;break a}}while(0);if((w|0)==74?(w=0,(l|0)!=1):0){m=l;break}c[v>>2]=1;break b}else{p=c[B>>2]|0;r=t+8|0;s=c[r+4>>2]|0;v=c[t>>2]|0;u=M;c[u>>2]=c[r>>2];c[u+4>>2]=s;c[M+8>>2]=v;c[M+12>>2]=n;X[p&3](b,1,4580,M);p=c[b>>2]|0;g:do if(!n)m=1;else{o=p+12|0;while(1){v=n>>>0<8192?n:8192;m=V[c[p>>2]&7](F,v,c[o>>2]|0)|0;n=n-v|0;if((m|0)!=1)break g;if(!n){m=1;break}}}while(0)}while(0);if((m|0)<0){k=m;break a}}else w=78}else w=78;while(0);h:do if((w|0)==78){w=0;m=c[p>>2]|0;i:do if(m|0){j:while(1){n=c[m+4>>2]|0;o=n+8|0;p=c[o>>2]|0;o=c[o+4>>2]|0;if(!((p|0)==0&(o|0)==0))while(1){if((p|0)==(q|0)&(o|0)==(s|0))break j;o=n+48|0;p=c[o>>2]|0;o=c[o+4>>2]|0;if((p|0)==0&(o|0)==0)break;else n=n+40|0}m=c[m>>2]|0;if(!m)break i}m=c[B>>2]|0;v=O;c[v>>2]=q;c[v+4>>2]=s;X[m&3](b,1,4670,O);m=c[T>>2]|0;if(!C?(c[m+4>>2]|0)==(e|0):0){w=87;break a}c[T>>2]=c[m>>2];Za(m);break h}while(0);m=Aa(b,G,H)|0;if((m|0)!=1){k=m;break a}c[y>>2]=0;n=G;m=c[n>>2]|0;n=c[n+4>>2]|0;switch(m|0){case 191:{if(n|0)w=93;break}case 236:{if(n|0)w=93;break}default:w=93}if((w|0)==93){w=0;v=c[B>>2]|0;u=Q;c[u>>2]=m;c[u+4>>2]=n;X[v&3](b,1,4732,Q)}p=c[b>>2]|0;m=c[H>>2]|0;if(m|0){o=p+12|0;do{v=m>>>0<8192?m:8192;n=V[c[p>>2]&7](F,v,c[o>>2]|0)|0;m=m-v|0;if((n|0)!=1){w=99;break a}}while((m|0)!=0)}}while(0)}switch(w|0){case 13:{T=t+8|0;if(!((c[T>>2]|0)==524531317&(c[T+4>>2]|0)==0))A(4380,4258,1115,4371);if((c[t+16>>2]|0)!=1)A(4380,4258,1115,4371);T=c[B>>2]|0;S=I;c[S>>2]=q;c[S+4>>2]=s;X[T&3](b,1,4438,I);T=1;R=W;return T|0}case 19:{A(4460,4258,1124,4371);break}case 26:{Za(m);T=1;R=W;return T|0}case 29:{Za(n);w=30;break}case 63:{Za(o);k=-1;break}case 70:{Za(o);w=71;break}case 73:{A(4653,4258,1081,4655);break}case 87:{X[c[B>>2]&3](b,1,4690,P);T=1;R=W;return T|0}case 99:{k=n;break}case 103:{R=W;return k|0}}if((w|0)==30){c[s+16>>2]=0;T=1;R=W;return T|0}else if((w|0)==71){c[u>>2]=0;k=-1}l=c[T>>2]|0;if(!l){T=k;R=W;return T|0}do{c[T>>2]=c[l>>2];Za(l);l=c[T>>2]|0}while((l|0)!=0);R=W;return k|0}function Ca(b,e){b=b|0;e=e|0;var f=0,i=0,j=0.0,k=0,l=0;i=b+176|0;a[h>>0]=a[i>>0];a[h+1>>0]=a[i+1>>0];a[h+2>>0]=a[i+2>>0];a[h+3>>0]=a[i+3>>0];a[h+4>>0]=a[i+4>>0];a[h+5>>0]=a[i+5>>0];a[h+6>>0]=a[i+6>>0];a[h+7>>0]=a[i+7>>0];j=+g[h>>3];i=b+188|0;if(!(d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24)){e=-1;return e|0}i=b+184|0;if((d[i>>0]|d[i+1>>0]<<8|d[i+2>>0]<<16|d[i+3>>0]<<24|0)!=3)A(5813,4258,841,5837);if(c[b+172>>2]|0){f=b+160|0;i=c[f>>2]|0;f=c[f+4>>2]|0;if((c[b+168>>2]|0)!=2)A(4235,4258,828,4292);if((i|0)==0&(f|0)==0){e=-1;return e|0}else b=i}else{b=1e6;f=0}if(!(j>=0.0)|j>=18446744073709551616.0){e=-1;return e|0}k=+s(j)>=1.0?(j>0.0?~~+v(+r(j/4294967296.0),4294967295.0)>>>0:~~+t((j-+(~~j>>>0))/4294967296.0)>>>0):0;i=Pb(-1,-1,b|0,f|0)|0;l=z()|0;if(l>>>0<k>>>0|(l|0)==(k|0)&i>>>0<~~j>>>0>>>0){l=-1;return l|0}j=j*(+(b>>>0)+4294967296.0*+(f>>>0));k=+s(j)>=1.0?(j>0.0?~~+v(+r(j/4294967296.0),4294967295.0)>>>0:~~+t((j-+(~~j>>>0))/4294967296.0)>>>0):0;l=e;c[l>>2]=~~j>>>0;c[l+4>>2]=k;l=0;return l|0}function Da(a,b){a=a|0;b=b|0;c[b>>2]=c[a+216>>2];return 0}function Ea(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;v=R;R=R+16|0;j=v+8|0;h=v;i=a+152|0;u=a+200|0;if(!(c[u>>2]|0)){e=Fa(c[i>>2]|0)|0;if(!e){u=-1;R=v;return u|0}g=e+16|0;f=c[g>>2]|0;g=c[g+4>>2]|0;if(!(c[e+28>>2]|0)){u=-1;R=v;return u|0}if((c[e+24>>2]|0)!=2)A(4235,4258,828,4292);k=c[a>>2]|0;k=U[c[k+8>>2]&3](c[k+12>>2]|0)|0;l=z()|0;if((l|0)<0){u=-1;R=v;return u|0}m=a+16|0;o=m;n=c[o>>2]|0;o=c[o+4>>2]|0;p=a+24|0;r=p;q=c[r>>2]|0;r=c[r+4>>2]|0;s=a+32|0;t=c[s>>2]|0;e=c[a>>2]|0;w=a+208|0;f=Lb(c[w>>2]|0,c[w+4>>2]|0,f|0,g|0)|0;g=z()|0;if(W[c[e+4>>2]&1](f,g,0,c[e+12>>2]|0)|0){w=-1;R=v;return w|0}c[s>>2]=0;if((Aa(a,h,0)|0)!=1){w=-1;R=v;return w|0}c[s>>2]=0;w=h;if(!((c[w>>2]|0)==475249515&(c[w+4>>2]|0)==0)){w=-1;R=v;return w|0}h=a+36|0;e=c[h>>2]|0;if(e|0)A(5775,4258,1923,5906);f=_a(1,12)|0;if(!f){w=-1;R=v;return w|0}c[f>>2]=e;c[f+4>>2]=16;c[f+8>>2]=a;c[h>>2]=f;g=_a(1,12)|0;if(!g){w=-1;R=v;return w|0}c[g>>2]=f;c[g+4>>2]=464;c[g+8>>2]=i;c[h>>2]=g;e=_a(1,12)|0;if(!e){w=-1;R=v;return w|0}c[e>>2]=g;c[e+4>>2]=992;c[e+8>>2]=u;c[h>>2]=e;X[c[a+4>>2]&3](a,1,5925,j);f=Ba(a,992,b,d)|0;e=c[h>>2]|0;if(e|0)do{c[h>>2]=c[e>>2];Za(e);e=c[h>>2]|0}while((e|0)!=0);w=c[a>>2]|0;if(W[c[w+4>>2]&1](k,l,0,c[w+12>>2]|0)|0){w=-1;R=v;return w|0}w=m;c[w>>2]=n;c[w+4>>2]=o;w=p;c[w>>2]=q;c[w+4>>2]=r;c[s>>2]=t;if((f|0)<0){w=-1;R=v;return w|0}if(!(c[u>>2]|0)){w=-1;R=v;return w|0}}w=0;R=v;return w|0}function Fa(a){a=a|0;var b=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!a){i=0;return i|0}a:while(1){i=a+8|0;if(!((c[i>>2]|0)==290298740&(c[i+4>>2]|0)==0)){b=4;break}b=c[c[a+16>>2]>>2]|0;if(b|0)do{i=b+8|0;if(!((c[i>>2]|0)==19899&(c[i+4>>2]|0)==0)){b=8;break a}i=c[b+16>>2]|0;f=c[i>>2]|0;e=c[i+4>>2]|0;if(c[i+12>>2]|0){if((c[i+8>>2]|0)!=5){b=11;break a}if(e|0){g=0;h=0;while(1){e=e+-1|0;j=Rb(g|0,h|0,8)|0;h=z()|0;g=j|(d[f>>0]|0);if(!e)break;else f=f+1|0}if((g|0)==475249515&(h|0)==0){a=i;b=18;break a}}}b=c[b>>2]|0}while((b|0)!=0);a=c[a>>2]|0;if(!a){a=0;b=18;break}}if((b|0)==4)A(5952,4258,1808,5982);else if((b|0)==8)A(6002,4258,1813,5982);else if((b|0)==11)A(6022,4258,867,6047);else if((b|0)==18)return a|0;return 0}function Ga(a,b,d){a=a|0;b=b|0;d=d|0;var e=0;if((d|0)<0){d=-1;return d|0}e=c[a>>2]|0;if(W[c[e+4>>2]&1](b,d,0,c[e+12>>2]|0)|0){e=-1;return e|0}c[a+32>>2]=0;if(!(c[a+36>>2]|0)){e=0;return e|0}else A(5775,4258,2309,6061);return 0}function Ha(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=a+200|0;if((c[h>>2]|0)==0?Ea(a,-1,-1)|0:0){a=-1;return a|0}if(c[a+172>>2]|0){g=a+160|0;f=c[g>>2]|0;g=c[g+4>>2]|0;if((c[a+168>>2]|0)!=2)A(4235,4258,828,4292);if((f|0)==0&(g|0)==0){a=-1;return a|0}else q=f}else{q=1e6;g=0}f=c[h>>2]|0;if(!f){a=-1;return a|0}r=a+192|0;p=f;f=0;a:while(1){o=p+8|0;if(!((c[o>>2]|0)==187&(c[o+4>>2]|0)==0)){g=10;break}o=c[p+16>>2]|0;f=(f|0)==0?o:f;i=o;h=c[i>>2]|0;i=c[i+4>>2]|0;if(c[o+12>>2]|0){if((c[o+8>>2]|0)!=2){g=13;break}n=Kb(h|0,i|0,q|0,g|0)|0;m=z()|0;if(m>>>0>e>>>0|(m|0)==(e|0)&n>>>0>d>>>0){g=35;break}}h=c[o+16>>2]|0;b:do if(!h)h=0;else{m=h;while(1){n=m+8|0;if(!((c[n>>2]|0)==183&(c[n+4>>2]|0)==0)){g=18;break a}h=c[m+16>>2]|0;n=c[h>>2]|0;if(!(c[h+12>>2]|0)){h=0;break b}if((c[h+8>>2]|0)!=2){g=21;break a}if(!n){h=0;break b}i=c[r>>2]|0;if(!i){h=0;break b}j=0;while(1){l=i+8|0;if(!((c[l>>2]|0)==174&(c[l+4>>2]|0)==0)){g=26;break a}k=c[i+16>>2]|0;l=k;if(!(c[k+12>>2]|0)){h=0;break b}if((c[k+8>>2]|0)!=2){g=29;break a}if((c[l+4>>2]|0)==0?(c[l>>2]|0)==(n|0):0)break;i=c[i>>2]|0;if(!i){h=0;break b}else j=j+1|0}if((j|0)==(b|0))break b;m=c[m>>2]|0;if(!m){h=0;break}}}while(0);f=(h|0)==0?f:o;p=c[p>>2]|0;if(!p){g=35;break}}if((g|0)==10)A(6081,4258,1861,6111);else if((g|0)==13)A(4235,4258,828,4292);else if((g|0)==18)A(6140,4258,1837,6175);else if((g|0)==21)A(4235,4258,828,4292);else if((g|0)==26)A(5850,4258,1338,5877);else if((g|0)==29)A(4235,4258,828,4292);else if((g|0)==35){if(!f){a=-1;return a|0}f=c[f+16>>2]|0;if(!f){a=-1;return a|0}c:while(1){e=f+8|0;if(!((c[e>>2]|0)==183&(c[e+4>>2]|0)==0)){g=39;break}l=c[f+16>>2]|0;k=c[l>>2]|0;if(!(c[l+12>>2]|0)){f=-1;g=63;break}if((c[l+8>>2]|0)!=2){g=42;break}if(!k){f=-1;g=63;break}g=c[r>>2]|0;if(!g){f=-1;g=63;break}h=0;while(1){e=g+8|0;if(!((c[e>>2]|0)==174&(c[e+4>>2]|0)==0)){g=47;break c}i=c[g+16>>2]|0;j=i;if(!(c[i+12>>2]|0)){f=-1;g=63;break c}if((c[i+8>>2]|0)!=2){g=50;break c}if((c[j+4>>2]|0)==0?(c[j>>2]|0)==(k|0):0)break;g=c[g>>2]|0;if(!g){f=-1;g=63;break c}else h=h+1|0}if((h|0)==(b|0)){g=55;break}f=c[f>>2]|0;if(!f){f=-1;g=63;break}}if((g|0)==39)A(6140,4258,1837,6175);else if((g|0)==42)A(4235,4258,828,4292);else if((g|0)==47)A(5850,4258,1338,5877);else if((g|0)==50)A(4235,4258,828,4292);else if((g|0)==55){if(!l){a=-1;return a|0}g=l+16|0;f=c[g>>2]|0;g=c[g+4>>2]|0;if(!(c[l+28>>2]|0)){a=-1;return a|0}if((c[l+24>>2]|0)!=2)A(4235,4258,828,4292);b=a+208|0;f=Lb(c[b>>2]|0,c[b+4>>2]|0,f|0,g|0)|0;g=z()|0;if((g|0)<0){a=-1;return a|0}b=c[a>>2]|0;if(W[c[b+4>>2]&1](f,g,0,c[b+12>>2]|0)|0){a=-1;return a|0}c[a+32>>2]=0;if(!(c[a+36>>2]|0)){a=0;return a|0}else A(5775,4258,2309,6061)}else if((g|0)==63)return f|0}return 0}function Ia(a,b){a=a|0;b=b|0;var d=0,e=0;a=c[a+192>>2]|0;if(!a)return -1;d=0;while(1){e=a+8|0;if(!((c[e>>2]|0)==174&(c[e+4>>2]|0)==0)){d=4;break}if((d|0)==(b|0)){d=7;break}a=c[a>>2]|0;if(!a){d=12;break}else d=d+1|0}if((d|0)==4)A(5850,4258,1359,6206);else if((d|0)==7){a=c[a+16>>2]|0;if(!a)return -1;d=c[a+32>>2]|0;if(!(c[a+44>>2]|0))return -1;if((c[a+40>>2]|0)==2)return ((d&1|0)==0&0==0?((d&2|0)==0&0==0?2147483647:1):0)|0;else A(4235,4258,828,4292)}else if((d|0)==12)return -1;return 0}function Ja(a,b){a=a|0;b=b|0;var e=0,f=0;a=c[a+192>>2]|0;if(!a){b=-1;return b|0}e=0;while(1){f=a+8|0;if(!((c[f>>2]|0)==174&(c[f+4>>2]|0)==0)){e=4;break}if((e|0)==(b|0)){e=7;break}a=c[a>>2]|0;if(!a){a=-1;e=16;break}else e=e+1|0}if((e|0)==4)A(5850,4258,1359,6206);else if((e|0)==7){e=c[a+16>>2]|0;if(!e){f=-1;return f|0}a=e+128|0;a=d[a>>0]|d[a+1>>0]<<8|d[a+2>>0]<<16|d[a+3>>0]<<24;f=e+140|0;if(!(d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24)){f=-1;return f|0}f=e+136|0;if((d[f>>0]|d[f+1>>0]<<8|d[f+2>>0]<<16|d[f+3>>0]<<24|0)!=4)A(4304,4258,854,4329);if(!(lb(a,6226)|0)){f=0;return f|0}if(!(lb(a,6232)|0)){f=2;return f|0}if(!(lb(a,6238)|0)){f=4;return f|0}if(!(lb(a,6244)|0)){f=1;return f|0}f=(lb(a,6253)|0)==0;f=f?3:2147483647;return f|0}else if((e|0)==16)return a|0;return 0}function Ka(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0;c[e>>2]=0;f=c[b+192>>2]|0;if(!f){e=-1;return e|0}g=0;while(1){h=f+8|0;if(!((c[h>>2]|0)==174&(c[h+4>>2]|0)==0)){g=4;break}if((g|0)==(d|0)){g=7;break}f=c[f>>2]|0;if(!f){f=-1;g=15;break}else g=g+1|0}if((g|0)==4)A(5850,4258,1359,6206);else if((g|0)==7){h=c[f+16>>2]|0;if(!h){e=-1;return e|0}switch(Ja(b,d)|0){case 3:{c[e>>2]=1;e=0;return e|0}case 1:{f=c[h+144>>2]|0;g=c[h+148>>2]|0;if(!(c[h+156>>2]|0)){e=-1;return e|0}if((c[h+152>>2]|0)!=5)A(6022,4258,867,6047);if(!g){e=-1;return e|0}d=a[f>>0]|0;c[e>>2]=(d&255)+1;e=((d&255)>2)<<31>>31;return e|0}default:{e=-1;return e|0}}}else if((g|0)==15)return f|0;return 0}function La(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;w=R;R=R+32|0;v=w;c[f>>2]=0;c[g>>2]=0;h=c[b+192>>2]|0;if(!h){g=-1;R=w;return g|0}i=0;while(1){u=h+8|0;if(!((c[u>>2]|0)==174&(c[u+4>>2]|0)==0)){i=4;break}if((i|0)==(d|0)){i=7;break}h=c[h>>2]|0;if(!h){h=-1;i=36;break}else i=i+1|0}if((i|0)==4)A(5850,4258,1359,6206);else if((i|0)==7){h=c[h+16>>2]|0;if(!h){g=-1;R=w;return g|0}if((Ja(b,d)|0)!=1?(Ja(b,d)|0)!=3:0){g=-1;R=w;return g|0}t=c[h+144>>2]|0;u=c[h+148>>2]|0;if(!(c[h+156>>2]|0)){g=-1;R=w;return g|0}if((c[h+152>>2]|0)!=5)A(6022,4258,867,6047);if((Ja(b,d)|0)!=1){if(e|0){g=-1;R=w;return g|0}c[f>>2]=t;c[g>>2]=u;g=0;R=w;return g|0}a:do if(u){r=t;h=a[r>>0]|0;s=h&255;if((h&255)<=2){if(!(h<<24>>24)){d=0;b=1;i=0;h=0}else{d=0;b=1;k=1;j=0;i=0;h=0;p=s;q=0;while(1){o=v+(d<<3)|0;if((u|0)==(k|0)){h=0;break a}j=Lb(k|0,j|0,1,0)|0;l=z()|0;n=a[r+k>>0]|0;k=n&255;b=b+1|0;m=o;c[m>>2]=k;c[m+4>>2]=0;if(n<<24>>24==-1){m=j;k=255;j=0;while(1){if((u|0)==(m|0)){h=0;break a}n=Lb(m|0,l|0,1,0)|0;l=z()|0;m=a[r+m>>0]|0;b=b+1|0;k=Lb(k|0,j|0,m&255|0,0)|0;j=z()|0;x=o;c[x>>2]=k;c[x+4>>2]=j;if(m<<24>>24==-1)m=n;else break}}else{n=j;j=0}i=Lb(k|0,j|0,i|0,h|0)|0;h=z()|0;d=d+1|0;p=Lb(p|0,q|0,-1,-1)|0;q=z()|0;if((p|0)==0&(q|0)==0)break;else{k=n;j=l}}}x=Lb(i|0,h|0,b|0,0)|0;q=z()|0;if(!(q>>>0>0|(q|0)==0&x>>>0>u>>>0)?(p=Mb(u-b|0,0,i|0,h|0)|0,q=z()|0,x=v+(d<<3)|0,c[x>>2]=p,c[x+4>>2]=q,!(0<0|0==0&s>>>0<e>>>0)):0){h=r+b|0;if(!e)b=h;else{i=0;do{h=h+(c[v+(i<<3)>>2]|0)|0;i=i+1|0}while((i|0)!=(e|0));b=h}i=b-t|0;if(u>>>0<i>>>0)A(6260,4258,2509,6384);x=v+(e<<3)|0;h=c[x>>2]|0;x=c[x+4>>2]|0;if(x>>>0>0|(x|0)==0&h>>>0>(u-i|0)>>>0)A(6260,4258,2509,6384);c[f>>2]=b;c[g>>2]=h;x=0;R=w;return x|0}else h=-1}else h=-1}else h=0;while(0);x=h;R=w;return x|0}else if((i|0)==36){R=w;return h|0}return 0}function Ma(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;e=d;f=e+40|0;do{c[e>>2]=0;e=e+4|0}while((e|0)<(f|0));g=c[a+192>>2]|0;if(!g){d=-1;return d|0}f=g;e=0;while(1){j=f+8|0;if(!((c[j>>2]|0)==174&(c[j+4>>2]|0)==0)){h=4;break}if((e|0)==(b|0)){h=7;break}a=c[f>>2]|0;if(!a){i=-1;h=56;break}else{f=a;e=e+1|0}}if((h|0)==4)A(5850,4258,1359,6206);else if((h|0)==7){j=c[f+16>>2]|0;if(!j){d=-1;return d|0}f=g;e=0;while(1){h=f+8|0;if(!((c[h>>2]|0)==174&(c[h+4>>2]|0)==0)){h=10;break}if((e|0)==(b|0)){h=13;break}a=c[f>>2]|0;if(!a){i=-1;h=56;break}else{f=a;e=e+1|0}}if((h|0)==10)A(5850,4258,1359,6206);else if((h|0)==13){a=c[f+16>>2]|0;if(!a){d=-1;return d|0}e=c[a+32>>2]|0;if(!(c[a+44>>2]|0)){d=-1;return d|0}if((c[a+40>>2]|0)!=2)A(4235,4258,828,4292);if((e&1|0)==0&0==0){d=-1;return d|0}e=j+208|0;a=c[e>>2]|0;e=c[e+4>>2]|0;a:do if(!(c[j+220>>2]|0)){k=0;h=27}else{if((c[j+216>>2]|0)!=2)A(4235,4258,828,4292);switch(a|0){case 0:if(!e){k=a;h=27;break a}else break a;case 1:if(!e){k=a;h=27;break a}else break a;case 2:if(!e){k=a;h=27;break a}else break a;case 3:if(!e){k=a;h=27;break a}else break a;case 11:if(!e){k=a;h=27;break a}else break a;default:break a}}while(0);if((h|0)==27)c[d>>2]=k;if(c[j+236>>2]|0)if((c[j+232>>2]|0)==2)l=c[j+224>>2]|0;else A(4235,4258,828,4292);else l=0;c[d+36>>2]=l;a=c[j+240>>2]|0;if(!(c[j+252>>2]|0)){d=-1;return d|0}if((c[j+248>>2]|0)!=2)A(4235,4258,828,4292);c[d+4>>2]=a;e=c[j+256>>2]|0;if(!(c[j+268>>2]|0)){d=-1;return d|0}if((c[j+264>>2]|0)!=2)A(4235,4258,828,4292);c[d+8>>2]=e;if(c[j+284>>2]|0)if((c[j+280>>2]|0)==2)m=c[j+272>>2]|0;else A(4235,4258,828,4292);else m=0;c[d+20>>2]=m;if(c[j+300>>2]|0)if((c[j+296>>2]|0)==2)n=c[j+288>>2]|0;else A(4235,4258,828,4292);else n=0;c[d+24>>2]=n;if(c[j+316>>2]|0)if((c[j+312>>2]|0)==2)o=c[j+304>>2]|0;else A(4235,4258,828,4292);else o=0;c[d+28>>2]=o;if(c[j+332>>2]|0)if((c[j+328>>2]|0)==2)p=c[j+320>>2]|0;else A(4235,4258,828,4292);else p=0;c[d+32>>2]=p;do if(!(c[j+348>>2]|0))q=a;else{if((c[j+344>>2]|0)==2){q=c[j+336>>2]|0;break}A(4235,4258,828,4292)}while(0);c[d+12>>2]=q;do if(!(c[j+364>>2]|0))r=e;else{if((c[j+360>>2]|0)==2){r=c[j+352>>2]|0;break}A(4235,4258,828,4292)}while(0);c[d+16>>2]=r;d=0;return d|0}else if((h|0)==56)return i|0}else if((h|0)==56)return i|0;return 0}function Na(b,e,f){b=b|0;e=e|0;f=f|0;var i=0.0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;c[f>>2]=0;c[f+4>>2]=0;c[f+8>>2]=0;c[f+12>>2]=0;c[f+16>>2]=0;c[f+20>>2]=0;c[f+24>>2]=0;c[f+28>>2]=0;l=c[b+192>>2]|0;if(!l){t=-1;return t|0}k=l;j=0;while(1){n=k+8|0;if(!((c[n>>2]|0)==174&(c[n+4>>2]|0)==0)){b=4;break}if((j|0)==(e|0)){b=7;break}b=c[k>>2]|0;if(!b){m=-1;b=35;break}else{k=b;j=j+1|0}}if((b|0)==4)A(5850,4258,1359,6206);else if((b|0)==7){n=c[k+16>>2]|0;if(!n){t=-1;return t|0}k=l;j=0;while(1){l=k+8|0;if(!((c[l>>2]|0)==174&(c[l+4>>2]|0)==0)){b=10;break}if((j|0)==(e|0)){b=13;break}b=c[k>>2]|0;if(!b){m=-1;b=35;break}else{k=b;j=j+1|0}}if((b|0)==10)A(5850,4258,1359,6206);else if((b|0)==13){b=c[k+16>>2]|0;if(!b){t=-1;return t|0}j=c[b+32>>2]|0;if(!(c[b+44>>2]|0)){t=-1;return t|0}if((c[b+40>>2]|0)!=2)A(4235,4258,828,4292);if(!((j&3|0)==2&0==0)){t=-1;return t|0}g[f>>3]=8.0e3;e=n+368|0;a[h>>0]=a[e>>0];a[h+1>>0]=a[e+1>>0];a[h+2>>0]=a[e+2>>0];a[h+3>>0]=a[e+3>>0];a[h+4>>0]=a[e+4>>0];a[h+5>>0]=a[e+5>>0];a[h+6>>0]=a[e+6>>0];a[h+7>>0]=a[e+7>>0];i=+g[h>>3];e=n+380|0;do if(d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24|0){e=n+376|0;if((d[e>>0]|d[e+1>>0]<<8|d[e+2>>0]<<16|d[e+3>>0]<<24|0)==3){g[f>>3]=i;break}else A(5813,4258,841,5837)}while(0);if(c[n+396>>2]|0)if((c[n+392>>2]|0)==2)o=c[n+384>>2]|0;else A(4235,4258,828,4292);else o=1;c[f+8>>2]=o;if(c[n+412>>2]|0)if((c[n+408>>2]|0)==2)p=c[n+400>>2]|0;else A(4235,4258,828,4292);else p=16;c[f+12>>2]=p;if(c[n+172>>2]|0){b=n+160|0;if((c[n+168>>2]|0)==2){q=c[b>>2]|0;r=c[b+4>>2]|0}else A(4235,4258,828,4292)}else{q=0;r=0}p=f+16|0;c[p>>2]=q;c[p+4>>2]=r;if(c[n+188>>2]|0){b=n+176|0;if((c[n+184>>2]|0)==2){s=c[b>>2]|0;t=c[b+4>>2]|0}else A(4235,4258,828,4292)}else{s=0;t=0}f=f+24|0;c[f>>2]=s;c[f+4>>2]=t;t=0;return t|0}else if((b|0)==35)return m|0}else if((b|0)==35)return m|0;return 0}function Oa(a){a=a|0;var b=0,d=0,e=0;if(c[a+36>>2]|0)A(5775,4258,2745,6409);d=a+240|0;b=c[d+4>>2]|0;if((b|0)<0){d=-1;return d|0}e=c[a>>2]|0;if(W[c[e+4>>2]&1](c[d>>2]|0,b,0,c[e+12>>2]|0)|0){e=-1;return e|0}e=a+248|0;d=c[e+4>>2]|0;b=a+16|0;c[b>>2]=c[e>>2];c[b+4>>2]=d;b=a+256|0;d=c[b+4>>2]|0;e=a+24|0;c[e>>2]=c[b>>2];c[e+4>>2]=d;c[a+32>>2]=c[a+264>>2];e=0;return e|0}function Pa(b,e){b=b|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,S=0,T=0,W=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0;ga=R;R=R+8288|0;aa=ga+8280|0;$=ga+8272|0;_=ga+8264|0;Z=ga+8256|0;ca=ga+8248|0;ba=ga+8240|0;Y=ga+8232|0;W=ga+8224|0;K=ga;L=ga+8216|0;M=ga+8208|0;S=ga+8200|0;T=ga+8192|0;c[e>>2]=0;if(c[b+36>>2]|0)A(5775,4258,2757,6428);O=c[b>>2]|0;O=U[c[O+8>>2]&3](c[O+12>>2]|0)|0;Q=z()|0;P=b+240|0;c[P>>2]=O;c[P+4>>2]=Q;if((Q|0)<0){e=-1;R=ga;return e|0}N=b+16|0;O=c[N+4>>2]|0;J=b+248|0;c[J>>2]=c[N>>2];c[J+4>>2]=O;J=b+24|0;O=c[J+4>>2]|0;N=b+256|0;c[N>>2]=c[J>>2];c[N+4>>2]=O;N=b+32|0;c[b+264>>2]=c[N>>2];O=b+4|0;J=b+172|0;I=b+168|0;H=b+160|0;P=b+224|0;Q=b+232|0;a:while(1){f=Aa(b,S,T)|0;if((f|0)!=1){fa=182;break}c[N>>2]=0;g=S;f=c[g>>2]|0;g=c[g+4>>2]|0;b:do if((f|0)<163){switch(f|0){case 160:break;default:{fa=175;break b}}if(!g){G=c[b>>2]|0;G=U[c[G+8>>2]&3](c[G+12>>2]|0)|0;F=z()|0;E=T;F=Lb(c[E>>2]|0,c[E+4>>2]|0,G|0,F|0)|0;G=z()|0;E=c[b>>2]|0;E=U[c[E+8>>2]&3](c[E+12>>2]|0)|0;D=z()|0;if((D|0)<(G|0)|(D|0)==(G|0)&E>>>0<F>>>0){r=0;p=0;D=0;q=0;f=0;j=0;i=0;E=0;k=0;n=0;m=0;while(1){g=Aa(b,S,T)|0;if((g|0)!=1){fa=24;break a}c[N>>2]=0;h=S;g=c[h>>2]|0;h=c[h+4>>2]|0;c:do if((g|0)>=236){if((g|0)<30113){switch(g|0){case 236:if(!h){fa=156;break c}else{fa=155;break c}case 251:break;default:{fa=155;break c}}if(h|0){fa=155;break}r=c[b>>2]|0;t=T;s=c[t>>2]|0;t=c[t+4>>2]|0;l=Lb(s|0,t|0,-1,-1)|0;h=z()|0;if(h>>>0>0|(h|0)==0&l>>>0>7){g=-1;fa=150;break a}o=r+12|0;g=V[c[r>>2]&7](K,1,c[o>>2]|0)|0;if((g|0)!=1){fa=150;break a}g=d[K>>0]|0;if(!((l|0)==0&(h|0)==0)){j=g;i=0;do{g=V[c[r>>2]&7](K,1,c[o>>2]|0)|0;if((g|0)!=1){fa=150;break a}C=Rb(j|0,i|0,8)|0;i=z()|0;j=C|(d[K>>0]|0);l=Lb(l|0,h|0,-1,-1)|0;h=z()|0}while(!((l|0)==0&(h|0)==0));if(t>>>0<0|(t|0)==0&s>>>0<8)g=j;else{r=1;o=D;l=E;break}}else i=0;o=Rb(s|0,t|0,3)|0;r=Lb(o|0,z()|0,-1,-1)|0;z()|0;r=Rb(1,0,r|0)|0;j=z()|0;r=i>>>0<j>>>0|(i|0)==(j|0)&g>>>0<r>>>0;o=Rb(1,0,o|0)|0;j=z()|0;j=Mb(g|0,i|0,(r?0:o)|0,(r?0:j)|0)|0;r=1;o=D;l=E;i=z()|0;break}switch(g|0){case 30113:break;case 30114:{if(h|0){fa=155;break c}s=c[b>>2]|0;u=T;t=c[u>>2]|0;u=c[u+4>>2]|0;o=Lb(t|0,u|0,-1,-1)|0;h=z()|0;if(h>>>0>0|(h|0)==0&o>>>0>7){g=-1;fa=75;break a}p=s+12|0;g=V[c[s>>2]&7](K,1,c[p>>2]|0)|0;if((g|0)!=1){fa=75;break a}g=d[K>>0]|0;if(!((o|0)==0&(h|0)==0)){l=g;k=0;do{g=V[c[s>>2]&7](K,1,c[p>>2]|0)|0;if((g|0)!=1){fa=75;break a}E=Rb(l|0,k|0,8)|0;k=z()|0;l=E|(d[K>>0]|0);o=Lb(o|0,h|0,-1,-1)|0;h=z()|0}while(!((o|0)==0&(h|0)==0));if(u>>>0<0|(u|0)==0&t>>>0<8)g=l;else{p=1;o=D;break c}}else k=0;o=Rb(t|0,u|0,3)|0;p=Lb(o|0,z()|0,-1,-1)|0;z()|0;p=Rb(1,0,p|0)|0;l=z()|0;p=k>>>0<l>>>0|(k|0)==(l|0)&g>>>0<p>>>0;o=Rb(1,0,o|0)|0;l=z()|0;l=Mb(g|0,k|0,(p?0:o)|0,(p?0:l)|0)|0;p=1;o=D;k=z()|0;break c}default:{fa=155;break c}}if(!h){if(f|0){fa=81;break a}B=T;C=c[B>>2]|0;B=c[B+4>>2]|0;y=c[b>>2]|0;y=U[c[y+8>>2]&3](c[y+12>>2]|0)|0;B=Lb(y|0,z()|0,C|0,B|0)|0;C=z()|0;y=c[b>>2]|0;y=U[c[y+8>>2]&3](c[y+12>>2]|0)|0;x=z()|0;d:do if((x|0)<(C|0)|(x|0)==(C|0)&y>>>0<B>>>0){da=0;while(1){f=Aa(b,L,M)|0;if((f|0)!=1){ea=f;break a}c[N>>2]=0;g=L;f=c[g>>2]|0;g=c[g+4>>2]|0;e:do switch(f|0){case 166:{if(g|0){fa=92;break e}y=c[b>>2]|0;y=U[c[y+8>>2]&3](c[y+12>>2]|0)|0;x=z()|0;w=M;x=Lb(c[w>>2]|0,c[w+4>>2]|0,y|0,x|0)|0;y=z()|0;w=c[b>>2]|0;w=U[c[w+8>>2]&3](c[w+12>>2]|0)|0;v=z()|0;if(!((v|0)<(y|0)|(v|0)==(y|0)&w>>>0<x>>>0)){fa=133;break a}f=1;v=0;h=0;w=0;while(1){g=Aa(b,L,M)|0;if((g|0)!=1){fa=103;break a}c[N>>2]=0;l=L;g=c[l>>2]|0;l=c[l+4>>2]|0;f:do switch(g|0){case 165:{if(l|0){fa=124;break f}if(v|0){fa=119;break a}w=M;l=c[w>>2]|0;w=Lb(l|0,c[w+4>>2]|0,-1,-1)|0;v=z()|0;if(!(v>>>0<0|(v|0)==0&w>>>0<268435455)){g=1;break f}h=_a(1,l)|0;if(!h){ea=-1;break a}g=c[b>>2]|0;g=V[c[g>>2]&7](h,l,c[g+12>>2]|0)|0;if((g|0)==1)g=1;else{fa=123;break a}break}case 191:{if(!l)fa=125;else fa=124;break}case 236:{if(!l)fa=125;else fa=124;break}case 238:{if(l|0){fa=124;break f}u=c[b>>2]|0;l=M;l=Lb(c[l>>2]|0,c[l+4>>2]|0,-1,-1)|0;o=z()|0;if(o>>>0>0|(o|0)==0&l>>>0>7){f=-1;fa=115;break a}t=u+12|0;f=V[c[u>>2]&7](K,1,c[t>>2]|0)|0;if((f|0)!=1){fa=115;break a}f=d[K>>0]|0;if((l|0)==0&(o|0)==0)g=0;else{s=f;g=0;while(1){f=V[c[u>>2]&7](K,1,c[t>>2]|0)|0;if((f|0)!=1){fa=115;break a}f=Rb(s|0,g|0,8)|0;g=z()|0;f=f|(d[K>>0]|0);l=Lb(l|0,o|0,-1,-1)|0;o=z()|0;if((l|0)==0&(o|0)==0)break;else s=f}}if((f|0)==0&(g|0)==0){fa=117;break a}else{g=v;l=w}break}default:fa=124}while(0);if((fa|0)==124){u=c[O>>2]|0;t=Z;c[t>>2]=g;c[t+4>>2]=l;X[u&3](b,1,6640,Z);fa=125}if((fa|0)==125){fa=0;s=c[b>>2]|0;g=c[M>>2]|0;if(g|0){o=s+12|0;do{u=g>>>0<8192?g:8192;l=V[c[s>>2]&7](K,u,c[o>>2]|0)|0;g=g-u|0;if((l|0)!=1){fa=130;break a}}while((g|0)!=0)}g=v;l=w}w=c[b>>2]|0;w=U[c[w+8>>2]&3](c[w+12>>2]|0)|0;v=z()|0;if((v|0)<(y|0)|(v|0)==(y|0)&w>>>0<x>>>0){v=g;w=l}else break}if(!g){fa=133;break a}y=_a(1,16)|0;c[y+12>>2]=da;c[y>>2]=f;c[y+4>>2]=h;c[y+8>>2]=l;f=y;break}case 191:{if(!g)fa=93;else fa=92;break}case 236:{if(!g)fa=93;else fa=92;break}default:fa=92}while(0);if((fa|0)==92){y=c[O>>2]|0;x=Y;c[x>>2]=f;c[x+4>>2]=g;X[y&3](b,1,6523,Y);fa=93}if((fa|0)==93){fa=0;l=c[b>>2]|0;f=c[M>>2]|0;if(f|0){h=l+12|0;do{y=f>>>0<8192?f:8192;g=V[c[l>>2]&7](K,y,c[h>>2]|0)|0;f=f-y|0;if((g|0)!=1){fa=97;break a}}while((f|0)!=0)}f=da}y=c[b>>2]|0;y=U[c[y+8>>2]&3](c[y+12>>2]|0)|0;x=z()|0;if((x|0)<(C|0)|(x|0)==(C|0)&y>>>0<B>>>0)da=f;else break d}}else f=0;while(0);o=D;l=E}else fa=155}else switch(g|0){case 155:{if(h|0){fa=155;break c}s=c[b>>2]|0;h=T;h=Lb(c[h>>2]|0,c[h+4>>2]|0,-1,-1)|0;m=z()|0;if(m>>>0>0|(m|0)==0&h>>>0>7){g=-1;fa=51;break a}o=s+12|0;g=V[c[s>>2]&7](K,1,c[o>>2]|0)|0;if((g|0)!=1){fa=51;break a}g=d[K>>0]|0;if((h|0)==0&(m|0)==0){m=g;l=0}else{n=g;l=0;while(1){g=V[c[s>>2]&7](K,1,c[o>>2]|0)|0;if((g|0)!=1){fa=51;break a}g=Rb(n|0,l|0,8)|0;l=z()|0;g=g|(d[K>>0]|0);h=Lb(h|0,m|0,-1,-1)|0;m=z()|0;if((h|0)==0&(m|0)==0){m=g;break}else n=g}}if(c[J>>2]|0){h=H;g=c[h>>2]|0;h=c[h+4>>2]|0;if((c[I>>2]|0)!=2){fa=58;break a}if((g|0)==0&(h|0)==0){fa=60;break a}}else{g=1e6;h=0}n=Kb(g|0,h|0,m|0,l|0)|0;o=1;m=z()|0;l=E;break c}case 161:{if(h|0){fa=155;break c}if(!(c[e>>2]|0)){g=161;h=0}else{X[c[O>>2]&3](b,1,6448,W);Ra(c[e>>2]|0);h=S;g=c[h>>2]|0;h=c[h+4>>2]|0}C=T;g=Qa(b,g,h,c[C>>2]|0,c[C+4>>2]|0,e)|0;if((g|0)==1){o=D;q=1;l=E;break c}else{fa=40;break a}}case 191:if(!h){fa=156;break c}else{fa=155;break c}default:{fa=155;break c}}while(0);if((fa|0)==155){C=c[O>>2]|0;B=$;c[B>>2]=g;c[B+4>>2]=h;X[C&3](b,1,6716,$);fa=156}if((fa|0)==156){fa=0;l=c[b>>2]|0;g=c[T>>2]|0;if(g|0){h=l+12|0;do{C=g>>>0<8192?g:8192;o=V[c[l>>2]&7](K,C,c[h>>2]|0)|0;g=g-C|0;if((o|0)!=1){fa=161;break a}}while((g|0)!=0)}o=D;l=E}E=c[b>>2]|0;E=U[c[E+8>>2]&3](c[E+12>>2]|0)|0;D=z()|0;if((D|0)<(G|0)|(D|0)==(G|0)&E>>>0<F>>>0){D=o;E=l}else break}}else{r=0;p=0;o=0;q=0;f=0;n=0;m=0;l=0;k=0;j=0;i=0}g=c[e>>2]|0;h=(g|0)!=0;if((q|0)!=(h&1|0)){fa=168;break a}if(h){G=g+16|0;c[G>>2]=n;c[G+4>>2]=m;c[g+24>>2]=o;G=g+40|0;c[G>>2]=l;c[G+4>>2]=k;c[g+48>>2]=p;G=g+56|0;c[G>>2]=j;c[G+4>>2]=i;c[g+64>>2]=r;c[g+32>>2]=f;f=c[e>>2]|0;if(!(c[f+64>>2]|0)){f=q;break}a[f+68>>0]=0;f=q;break}if(!f)f=q;else{do{G=f;f=c[f+12>>2]|0;Za(c[G+4>>2]|0);Za(G)}while((f|0)!=0);f=q}}else fa=175}else{if((f|0)<524531317){switch(f|0){case 163:break;default:{fa=175;break b}}if(g|0){fa=175;break}f=T;f=Qa(b,163,0,c[f>>2]|0,c[f+4>>2]|0,e)|0;if((f|0)==1){f=1;break}else{fa=182;break a}}switch(f|0){case 524531317:break;default:{fa=175;break b}}if(!g){f=Aa(b,S,T)|0;if((f|0)!=1){fa=182;break a}c[N>>2]=0;G=S;if(!((c[G>>2]|0)==231&(c[G+4>>2]|0)==0)){f=-1;fa=182;break a}j=c[b>>2]|0;h=T;h=Lb(c[h>>2]|0,c[h+4>>2]|0,-1,-1)|0;g=z()|0;if(g>>>0>0|(g|0)==0&h>>>0>7){f=-1;fa=18;break a}i=j+12|0;f=V[c[j>>2]&7](K,1,c[i>>2]|0)|0;if((f|0)!=1){fa=18;break a}G=P;c[G>>2]=d[K>>0];c[G+4>>2]=0;if(!((h|0)==0&(g|0)==0))do{f=V[c[j>>2]&7](K,1,c[i>>2]|0)|0;if((f|0)!=1){fa=18;break a}E=P;E=Rb(c[E>>2]|0,c[E+4>>2]|0,8)|0;F=z()|0;G=P;c[G>>2]=E|(d[K>>0]|0);c[G+4>>2]=F;h=Lb(h|0,g|0,-1,-1)|0;g=z()|0}while(!((h|0)==0&(g|0)==0));c[Q>>2]=1;f=0}else fa=175}while(0);if((fa|0)==175){fa=0;i=c[O>>2]|0;G=aa;c[G>>2]=f;c[G+4>>2]=g;X[i&3](b,1,6793,aa);i=c[b>>2]|0;f=c[T>>2]|0;if(f|0){h=i+12|0;do{G=f>>>0<8192?f:8192;g=V[c[i>>2]&7](K,G,c[h>>2]|0)|0;f=f-G|0;if((g|0)!=1){fa=180;break a}}while((f|0)!=0)}f=0}if(f|0){f=1;fa=182;break}}switch(fa|0){case 18:{e=f;R=ga;return e|0}case 24:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=g;R=ga;return e|0}Ra(f);c[e>>2]=0;e=g;R=ga;return e|0}case 40:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=g;R=ga;return e|0}Ra(f);c[e>>2]=0;e=g;R=ga;return e|0}case 51:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=g;R=ga;return e|0}Ra(f);c[e>>2]=0;e=g;R=ga;return e|0}case 58:{A(4235,4258,828,4292);break}case 60:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=-1;R=ga;return e|0}Ra(f);c[e>>2]=0;e=-1;R=ga;return e|0}case 75:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=g;R=ga;return e|0}Ra(f);c[e>>2]=0;e=g;R=ga;return e|0}case 81:{do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=-1;R=ga;return e|0}Ra(f);c[e>>2]=0;e=-1;R=ga;return e|0}case 97:{ea=g;break}case 103:{Za(h);ea=g;break}case 115:{Za(h);ea=f;break}case 117:{X[c[O>>2]&3](b,1e3,6562,ba);Za(h);ea=-1;break}case 119:{X[c[O>>2]&3](b,1e3,6591,ca);Za(h);ea=-1;break}case 123:{Za(h);ea=g;break}case 130:{Za(h);ea=l;break}case 133:{X[c[O>>2]&3](b,1e3,6674,_);ea=-1;break}case 150:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=g;R=ga;return e|0}Ra(f);c[e>>2]=0;e=g;R=ga;return e|0}case 161:{if(f|0)do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0);f=c[e>>2]|0;if(!f){e=o;R=ga;return e|0}Ra(f);c[e>>2]=0;e=o;R=ga;return e|0}case 168:{A(6764,4258,2924,6428);break}case 180:{e=g;R=ga;return e|0}case 182:{R=ga;return f|0}}if(da|0){f=da;do{fa=f;f=c[f+12>>2]|0;Za(c[fa+4>>2]|0);Za(fa)}while((f|0)!=0)}f=c[e>>2]|0;if(!f){e=ea;R=ga;return e|0}Ra(f);c[e>>2]=0;e=ea;R=ga;return e|0}function Qa(e,f,h,i,j,k){e=e|0;f=f|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,u=0,w=0,x=0,y=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,S=0,T=0,U=0,W=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0.0;Fa=R;R=R+2144|0;ya=Fa+2096|0;ma=Fa+2088|0;ja=Fa+2080|0;ka=Fa+2072|0;ga=Fa+2064|0;fa=Fa+2056|0;ea=Fa+2048|0;Y=Fa+2137|0;wa=Fa;xa=Fa+2136|0;c[k>>2]=0;if(j>>>0>0|(j|0)==0&i>>>0>1073741824){Ea=-1;R=Fa;return Ea|0}D=c[e>>2]|0;C=D+12|0;l=V[c[D>>2]&7](Y,1,c[C>>2]|0)|0;a:do if((l|0)==1){y=d[Y>>0]|0;do if(!(y&128)){if(!(y&64))if(!(y&32))if(!(y&16))if(!(y&8)){l=y>>>1&1;u=8-l|0;if(!(y&4)){o=254-l&y;l=u+-1|0;if(!l){B=1;W=o;l=0;break}else{q=l;p=0;B=u}}else{w=-5;x=6;la=6}}else{w=-9;x=5;la=6}else{w=-17;x=4;la=6}else{w=-33;x=3;la=6}else{w=-65;x=2;la=6}if((la|0)==6){q=x+-1|0;o=w&y;p=0;B=x}while(1){l=V[c[D>>2]&7](Y,1,c[C>>2]|0)|0;if((l|0)!=1)break a;o=Rb(o|0,p|0,8)|0;l=z()|0;o=o|d[Y>>0];q=q+-1|0;if(!q){W=o;break}else p=l}}else{B=1;W=y&127;l=0}while(0);if((W|0)==0&(l|0)==0){Ea=-1;R=Fa;return Ea|0}p=c[e>>2]|0;o=p+12|0;l=V[c[p>>2]&7](Y,1,c[o>>2]|0)|0;if((l|0)==1){q=a[Y>>0]|0;l=V[c[p>>2]&7](Y,1,c[o>>2]|0)|0;if((l|0)==1){l=Rb(q&255|0,0,8)|0;_=z()|0;l=l|d[Y>>0];Z=_>>>0<0|(_|0)==0&l>>>0<32768;Z=Mb(l|0,_|0,(Z?0:65536)|0,(Z?0:0)|0)|0;_=z()|0;l=c[e>>2]|0;l=V[c[l>>2]&7](Y,1,c[l+12>>2]|0)|0;if((l|0)!=1){Ea=l;R=Fa;return Ea|0}o=a[Y>>0]|0;ca=o&255;ba=(f|0)==163&(h|0)==0?(o&255)>>>7:2;o=(o&255)>>>1;b:do if(!(o&3)){m=B+3|0;L=Mb(i|0,j|0,m|0,0)|0;aa=z()|0;$=wa;c[$>>2]=L;c[$+4>>2]=aa;$=0;aa=1;L=0}else{l=c[e>>2]|0;l=V[c[l>>2]&7](Y,1,c[l+12>>2]|0)|0;if((l|0)!=1){Ea=l;R=Fa;return Ea|0}l=a[Y>>0]|0;q=l&255;p=B+4|0;M=Lb(q|0,0,1,0)|0;N=z()|0;switch(o&3){case 3:{if(!(l<<24>>24)){Ea=-1;R=Fa;return Ea|0}I=c[e>>2]|0;D=I+12|0;l=V[c[I>>2]&7](Y,1,c[D>>2]|0)|0;c:do if((l|0)==1){o=d[Y>>0]|0;d:do if(!(o&128)){do if(!(o&64)){if(o&32|0){F=-33;G=3;la=47;break}if(o&16|0){F=-17;G=4;la=47;break}if(o&8|0){F=-9;G=5;la=47;break}l=o>>>1&1;m=8-l|0;if(o&4|0){F=-5;G=6;la=47;break}n=254-l&o;l=m+-1|0;if(!l){m=1;l=0;break d}else{H=l;E=0;J=m}}else{F=-65;G=2;la=47}while(0);if((la|0)==47){H=G+-1|0;n=F&o;E=0;J=G}m=E;o=H;while(1){l=V[c[I>>2]&7](Y,1,c[D>>2]|0)|0;if((l|0)!=1)break c;n=Rb(n|0,m|0,8)|0;l=z()|0;n=n|d[Y>>0];o=o+-1|0;if(!o){m=J;break}else m=l}}else{m=1;n=o&127;l=0}while(0);m=m+p|0;p=wa;c[p>>2]=n;c[p+4>>2]=l;p=Lb(q|0,0,-1,-1)|0;q=z()|0;e:do if((p|0)==0&(q|0)==0)o=1;else{o=1;B=n;C=l;y=l;f:while(1){l=V[c[I>>2]&7](Y,1,c[D>>2]|0)|0;if((l|0)!=1)break;x=d[Y>>0]|0;g:do if(!(x&128)){do if(!(x&64)){if(x&32|0){O=-33;P=3;la=63;break}if(x&16|0){O=-17;P=4;la=63;break}if(x&8|0){O=-9;P=5;la=63;break}l=x>>>1&1;w=8-l|0;if(x&4|0){O=-5;P=6;la=63;break}u=254-l&x;l=w+-1|0;if(!l){w=1;l=0;break g}else{Q=w;S=l;T=u;U=0}}else{O=-65;P=2;la=63}while(0);if((la|0)==63){la=0;Q=P;S=P+-1|0;T=O&x;U=0}w=T;u=U;x=S;while(1){l=V[c[I>>2]&7](Y,1,c[D>>2]|0)|0;if((l|0)!=1)break f;u=Rb(w|0,u|0,8)|0;l=z()|0;u=u|d[Y>>0];x=x+-1|0;if(!x){w=Q;break}else{w=u;u=l}}}else{w=1;u=x&127;l=0}while(0);aa=3232+(w+-1<<3)|0;l=Mb(u|0,l|0,c[aa>>2]|0,c[aa+4>>2]|0)|0;m=w+m|0;B=Lb(B|0,C|0,l|0,z()|0)|0;C=z()|0;l=wa+(o<<3)|0;c[l>>2]=B;c[l+4>>2]=C;n=Lb(B|0,C|0,n|0,y|0)|0;l=z()|0;o=o+1|0;p=Lb(p|0,q|0,-1,-1)|0;q=z()|0;if((p|0)==0&(q|0)==0)break e;else y=l}Ea=l;R=Fa;return Ea|0}while(0);la=Lb(n|0,l|0,m|0,0)|0;aa=z()|0;if(aa>>>0>0|(aa|0)==0&la>>>0>i>>>0){Ea=-1;R=Fa;return Ea|0}else{L=Mb(i-m|0,0,n|0,l|0)|0;aa=z()|0;$=wa+(o<<3)|0;c[$>>2]=L;c[$+4>>2]=aa;$=N;aa=M;L=0;break b}}while(0);Ea=l;R=Fa;return Ea|0}case 1:{if(!(l<<24>>24)){Ea=-1;R=Fa;return Ea|0}y=c[e>>2]|0;x=y+12|0;D=0;l=p;B=0;C=0;u=0;h:while(1){p=wa+(D<<3)|0;w=V[c[y>>2]&7](Y,1,c[x>>2]|0)|0;if((w|0)!=1){la=30;break}la=a[Y>>0]|0;n=la&255;l=l+1|0;U=p;c[U>>2]=n;c[U+4>>2]=0;if(la<<24>>24==-1){o=l;n=255;l=0;while(1){K=V[c[y>>2]&7](Y,1,c[x>>2]|0)|0;if((K|0)!=1){la=34;break h}la=a[Y>>0]|0;o=o+1|0;n=Lb(n|0,l|0,la&255|0,0)|0;l=z()|0;U=p;c[U>>2]=n;c[U+4>>2]=l;if(la<<24>>24!=-1){I=o;break}}}else{I=l;l=0}B=Lb(n|0,l|0,B|0,C|0)|0;C=z()|0;D=D+1|0;q=Lb(q|0,u|0,-1,-1)|0;u=z()|0;if((q|0)==0&(u|0)==0){la=37;break}else l=I}if((la|0)==30){Ea=w;R=Fa;return Ea|0}else if((la|0)==34){Ea=K;R=Fa;return Ea|0}else if((la|0)==37){la=Lb(B|0,C|0,I|0,0)|0;aa=z()|0;if(aa>>>0>0|(aa|0)==0&la>>>0>i>>>0){Ea=-1;R=Fa;return Ea|0}else{m=Mb(i-I|0,0,B|0,C|0)|0;aa=z()|0;$=wa+(D<<3)|0;c[$>>2]=m;c[$+4>>2]=aa;$=N;aa=M;m=I;L=0;break b}}break}case 2:{$=Mb(i|0,j|0,p|0,0)|0;aa=z()|0;m=Pb($|0,aa|0,M|0,N|0)|0;n=z()|0;la=Kb(m|0,n|0,M|0,N|0)|0;la=Mb($|0,aa|0,la|0,z()|0)|0;if(!((la|0)==0&(z()|0)==0)){Ea=-1;R=Fa;return Ea|0}l=0;do{la=wa+(l<<3)|0;c[la>>2]=m;c[la+4>>2]=n;l=l+1|0}while(!(0<0|0==0&q>>>0<l>>>0));$=N;aa=M;m=p;L=0;break}default:{}}}while(0);n=0;l=L;do{la=wa+(n<<3)|0;m=Lb(c[la>>2]|0,c[la+4>>2]|0,m|0,l|0)|0;l=z()|0;n=n+1|0}while($>>>0>0|($|0)==0&aa>>>0>n>>>0);if((W|0)==0|(l>>>0>j>>>0|(l|0)==(j|0)&m>>>0>i>>>0)){Ea=-1;R=Fa;return Ea|0}n=c[e+192>>2]|0;if(!n){Ea=-1;R=Fa;return Ea|0}l=n;p=0;while(1){la=l+8|0;if(!((c[la>>2]|0)==174&(c[la+4>>2]|0)==0)){la=83;break}m=c[l+16>>2]|0;o=m;if(!(c[m+12>>2]|0)){da=-1;la=173;break}if((c[m+8>>2]|0)!=2){la=86;break}if((c[o+4>>2]|0)==0?(c[o>>2]|0)==(W|0):0){la=88;break}l=c[l>>2]|0;if(!l){da=-1;la=173;break}else p=p+1|0}if((la|0)==83)A(5850,4258,1338,5877);else if((la|0)==86)A(4235,4258,828,4292);else if((la|0)==88){m=0;while(1){la=n+8|0;if(!((c[la>>2]|0)==174&(c[la+4>>2]|0)==0)){la=91;break}if((m|0)==(p|0)){la=94;break}l=c[n>>2]|0;if(!l){da=-1;la=173;break}else{n=l;m=m+1|0}}if((la|0)==91)A(5850,4258,1359,6206);else if((la|0)==94){l=c[n+16>>2]|0;if(!l){Ea=-1;R=Fa;return Ea|0}l=c[l+416>>2]|0;do if(l){l=c[l+16>>2]|0;n=l;m=c[n>>2]|0;n=c[n+4>>2]|0;if(!(c[l+12>>2]|0)){Ea=-1;R=Fa;return Ea|0}if((c[l+8>>2]|0)!=2)A(4235,4258,828,4292);if((m|0)==1&(n|0)==0){l=c[l+16>>2]|0;if(!l){Ea=-1;R=Fa;return Ea|0}l=c[l+16>>2]|0;n=l;m=c[n>>2]|0;n=c[n+4>>2]|0;if(!(c[l+12>>2]|0)){X[c[e+4>>2]&3](e,1e3,6827,ea);Ea=-1;R=Fa;return Ea|0}if((c[l+8>>2]|0)!=2)A(4235,4258,828,4292);if(!((m|0)==5&(n|0)==0)){X[c[e+4>>2]&3](e,1e3,6859,fa);Ea=-1;R=Fa;return Ea|0}l=c[l+32>>2]|0;if(!l){X[c[e+4>>2]&3](e,1e3,6890,ga);Ea=-1;R=Fa;return Ea|0}l=c[l+16>>2]|0;n=l;m=c[n>>2]|0;n=c[n+4>>2]|0;do if(c[l+12>>2]|0){if((c[l+8>>2]|0)!=2)A(4235,4258,828,4292);if((m|0)==1&(n|0)==0)break;X[c[e+4>>2]&3](e,1e3,6929,ka);Ea=-1;R=Fa;return Ea|0}while(0);if((ca&6|0)==0&0==0){na=1;break}X[c[e+4>>2]&3](e,1e3,6967,ja);Ea=-1;R=Fa;return Ea|0}else{ha=m;ia=n;la=114}}else{ha=0;ia=0;la=114}while(0);if((la|0)==114)na=(ha|0)==1&(ia|0)==0;if(c[e+172>>2]|0){m=e+160|0;l=c[m>>2]|0;m=c[m+4>>2]|0;if((c[e+168>>2]|0)!=2)A(4235,4258,828,4292);if((l|0)==0&(m|0)==0){Ea=-1;R=Fa;return Ea|0}else o=m}else{l=1e6;o=0}if(!(c[e+232>>2]|0)){Ea=-1;R=Fa;return Ea|0}m=e+224|0;m=Lb(c[m>>2]|0,c[m+4>>2]|0,Z|0,_|0)|0;n=z()|0;if((n|0)<0){ka=c[e+4>>2]|0;ja=ma;c[ja>>2]=m;c[ja+4>>2]=n;X[ka&3](e,100,7006,ma);m=0;n=0}F=_a(1,72)|0;if(!F){Ea=-1;R=Fa;return Ea|0}C=F;c[C>>2]=p;c[C+4>>2]=0;C=Kb(m|0,n|0,l|0,o|0)|0;Ga=+(C>>>0)+4294967296.0*+((z()|0)>>>0);C=~~Ga>>>0;E=+s(Ga)>=1.0?(Ga>0.0?~~+v(+r(Ga/4294967296.0),4294967295.0)>>>0:~~+t((Ga-+(~~Ga>>>0))/4294967296.0)>>>0):0;D=F+8|0;c[D>>2]=C;c[D+4>>2]=E;a[F+68>>0]=ba;D=c[e+4>>2]|0;c[ya>>2]=(f|0)==161&(h|0)==0?8806:7039;h=ya+8|0;c[h>>2]=p;c[h+4>>2]=0;g[ya+16>>3]=(+(C>>>0)+4294967296.0*+(E>>>0))/1.0e9;E=ya+24|0;c[E>>2]=ca;c[E+4>>2]=0;E=ya+32|0;c[E>>2]=aa;c[E+4>>2]=$;X[D&3](e,1,7046,ya);D=F+28|0;E=0;C=0;i:while(1){y=wa+(C<<3)|0;x=c[y>>2]|0;y=c[y+4>>2]|0;if(y>>>0>0|(y|0)==0&x>>>0>268435456){la=127;break}B=E;E=_a(1,16)|0;if(!E){la=130;break};c[E>>2]=0;c[E+4>>2]=0;c[E+8>>2]=0;c[E+12>>2]=0;do if(na){Da=c[e>>2]|0;Da=V[c[Da>>2]&7](xa,1,c[Da+12>>2]|0)|0;if((Da|0)!=1){la=132;break i}m=_a(1,16)|0;if(!m){la=136;break i};c[m>>2]=0;c[m+4>>2]=0;b[m+8>>1]=0;ra=E+8|0;c[ra>>2]=m;h=a[xa>>0]|0;a[m+8>>0]=h;if(!(h&1)){l=1;Ca=m;break}l=_a(1,8)|0;c[m>>2]=l;if(!l){la=139;break i}Aa=c[e>>2]|0;Aa=V[c[Aa>>2]&7](l,8,c[Aa+12>>2]|0)|0;sa=c[ra>>2]|0;if((Aa|0)!=1){la=141;break i}c[sa+4>>2]=8;if(!(a[xa>>0]&2)){l=9;Ca=sa;break}Ba=c[e>>2]|0;Ba=V[c[Ba>>2]&7](sa+9|0,1,c[Ba+12>>2]|0)|0;ta=c[ra>>2]|0;if((Ba|0)!=1){la=146;break i}h=ta+9|0;f=d[h>>0]<<2;l=(f|1)+9|0;c[ta+12>>2]=_a(1,f)|0;if(!(a[h>>0]|0)){Ca=ta;break}w=0;while(1){p=c[e>>2]|0;o=p+12|0;m=V[c[p>>2]&7](ya,1,c[o>>2]|0)|0;if((m|0)!=1){pa=m;la=154;break}u=a[ya>>0]|0;m=V[c[p>>2]&7](ya,1,c[o>>2]|0)|0;if((m|0)!=1){pa=m;la=154;break}q=a[ya>>0]|0;m=V[c[p>>2]&7](ya,1,c[o>>2]|0)|0;if((m|0)!=1){pa=m;la=154;break}n=a[ya>>0]|0;m=V[c[p>>2]&7](ya,1,c[o>>2]|0)|0;if((m|0)!=1){pa=m;la=154;break}o=c[ra>>2]|0;c[(c[o+12>>2]|0)+(w<<2)>>2]=(((u&255)<<8|q&255)<<8|n&255)<<8|d[ya>>0];n=w+1|0;m=a[o+9>>0]|0;if(n>>>0<(m&255)>>>0)w=n;else{qa=n;za=1;ua=m;va=o;break}}if((la|0)==154){la=0;va=c[ra>>2]|0;qa=w;za=pa;ua=a[va+9>>0]|0}if(ua<<24>>24==(qa&255)<<24>>24)Ca=va;else{la=156;break i}}else{l=0;Ca=0}while(0);if(y>>>0<0|(y|0)==0&x>>>0<l>>>0){la=160;break}l=x-l|0;m=_a(1,l)|0;c[E>>2]=m;if(!m){la=164;break}c[E+4>>2]=l;Ea=c[e>>2]|0;Ea=V[c[Ea>>2]&7](m,l,c[Ea+12>>2]|0)|0;if((Ea|0)!=1){la=168;break}c[((B|0)==0?D:B+12|0)>>2]=E;C=C+1|0;if(!($>>>0>0|($|0)==0&aa>>>0>C>>>0)){la=172;break}}switch(la|0){case 127:{Ra(F);Ea=-1;R=Fa;return Ea|0}case 130:{Ra(F);Ea=-1;R=Fa;return Ea|0}case 132:{l=E+8|0;m=c[l>>2]|0;if(!m)l=0;else{Za(c[m>>2]|0);Za(c[(c[l>>2]|0)+12>>2]|0);l=c[l>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);Ea=Da;R=Fa;return Ea|0}case 136:{c[E+8>>2]=0;Za(c[E>>2]|0);Za(E);Ra(F);Ea=-1;R=Fa;return Ea|0}case 139:{Za(c[(c[ra>>2]|0)+12>>2]|0);Za(c[ra>>2]|0);Za(c[E>>2]|0);Za(E);Ra(F);Ea=-1;R=Fa;return Ea|0}case 141:{if(!sa)l=0;else{Za(c[sa>>2]|0);Za(c[(c[ra>>2]|0)+12>>2]|0);l=c[ra>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);Ea=Aa;R=Fa;return Ea|0}case 146:{if(!ta)l=0;else{Za(c[ta>>2]|0);Za(c[(c[ra>>2]|0)+12>>2]|0);l=c[ra>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);Ea=Ba;R=Fa;return Ea|0}case 156:{if(!va)l=0;else{Za(c[va>>2]|0);Za(c[(c[ra>>2]|0)+12>>2]|0);l=c[ra>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);Ea=za;R=Fa;return Ea|0}case 160:{l=E+8|0;if(!Ca)l=0;else{Za(c[Ca>>2]|0);Za(c[(c[l>>2]|0)+12>>2]|0);l=c[l>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);Ea=-1;R=Fa;return Ea|0}case 164:{l=E+8|0;if(!Ca)l=0;else{Za(c[Ca>>2]|0);Za(c[(c[l>>2]|0)+12>>2]|0);l=c[l>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);Ea=-1;R=Fa;return Ea|0}case 168:{l=E+8|0;m=c[l>>2]|0;if(!m)l=0;else{Za(c[m>>2]|0);Za(c[(c[l>>2]|0)+12>>2]|0);l=c[l>>2]|0}Za(l);Za(c[E>>2]|0);Za(E);Ra(F);R=Fa;return Ea|0}case 172:{c[k>>2]=F;Ea=1;R=Fa;return Ea|0}}}else if((la|0)==173){R=Fa;return da|0}}else if((la|0)==173){R=Fa;return da|0}}else oa=l}else oa=l;Ea=oa;R=Fa;return Ea|0}while(0);Ea=l;R=Fa;return Ea|0}function Ra(a){a=a|0;var b=0,d=0,e=0,f=0;f=a+28|0;b=c[f>>2]|0;if(b|0)do{c[f>>2]=c[b+12>>2];d=b+8|0;e=c[d>>2]|0;if(!e)d=0;else{Za(c[e>>2]|0);Za(c[(c[d>>2]|0)+12>>2]|0);d=c[d>>2]|0}Za(d);Za(c[b>>2]|0);Za(b);b=c[f>>2]|0}while((b|0)!=0);b=c[a+32>>2]|0;if(!b){Za(a);return}do{f=b;b=c[b+12>>2]|0;Za(c[f+4>>2]|0);Za(f)}while((b|0)!=0);Za(a);return}function Sa(a){a=a|0;return d[a+68>>0]|0|0}function Ta(a,b){a=a|0;b=b|0;c[b>>2]=c[a>>2];return 0}function Ua(a,b){a=a|0;b=b|0;var d=0;d=a+8|0;a=c[d+4>>2]|0;c[b>>2]=c[d>>2];c[b+4>>2]=a;return 0}function Va(a,b){a=a|0;b=b|0;var d=0;if(!(c[a+48>>2]|0)){b=-1;return b|0}d=a+40|0;a=c[d+4>>2]|0;c[b>>2]=c[d>>2];c[b+4>>2]=a;b=0;return b|0}function Wa(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0;a=c[a+28>>2]|0;c[d>>2]=0;c[e>>2]=0;if(!a){g=-1;return g|0}f=0;while(1){if((f|0)==(b|0))break;a=c[a+12>>2]|0;if(!a){a=-1;g=6;break}else f=f+1|0}if((g|0)==6)return a|0;c[d>>2]=c[a>>2];c[e>>2]=c[a+4>>2];g=0;return g|0}function Xa(a){a=a|0;if(c[a+200>>2]|0){a=1;return a|0}a=(Fa(c[a+152>>2]|0)|0)!=0&1;return a|0}function Ya(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;w=R;R=R+16|0;n=w;do if(a>>>0<245){k=a>>>0<11?16:a+11&-8;a=k>>>3;m=c[2060]|0;d=m>>>a;if(d&3|0){b=(d&1^1)+a|0;a=8280+(b<<1<<2)|0;d=a+8|0;e=c[d>>2]|0;f=e+8|0;g=c[f>>2]|0;if((g|0)==(a|0))c[2060]=m&~(1<<b);else{c[g+12>>2]=a;c[d>>2]=g}v=b<<3;c[e+4>>2]=v|3;v=e+v+4|0;c[v>>2]=c[v>>2]|1;v=f;R=w;return v|0}l=c[2062]|0;if(k>>>0>l>>>0){if(d|0){b=2<<a;b=d<<a&(b|0-b);b=(b&0-b)+-1|0;i=b>>>12&16;b=b>>>i;d=b>>>5&8;b=b>>>d;g=b>>>2&4;b=b>>>g;a=b>>>1&2;b=b>>>a;e=b>>>1&1;e=(d|i|g|a|e)+(b>>>e)|0;b=8280+(e<<1<<2)|0;a=b+8|0;g=c[a>>2]|0;i=g+8|0;d=c[i>>2]|0;if((d|0)==(b|0)){a=m&~(1<<e);c[2060]=a}else{c[d+12>>2]=b;c[a>>2]=d;a=m}v=e<<3;h=v-k|0;c[g+4>>2]=k|3;f=g+k|0;c[f+4>>2]=h|1;c[g+v>>2]=h;if(l|0){e=c[2065]|0;b=l>>>3;d=8280+(b<<1<<2)|0;b=1<<b;if(!(a&b)){c[2060]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=e;c[b+12>>2]=e;c[e+8>>2]=b;c[e+12>>2]=d}c[2062]=h;c[2065]=f;v=i;R=w;return v|0}g=c[2061]|0;if(g){d=(g&0-g)+-1|0;f=d>>>12&16;d=d>>>f;e=d>>>5&8;d=d>>>e;h=d>>>2&4;d=d>>>h;i=d>>>1&2;d=d>>>i;j=d>>>1&1;j=c[8544+((e|f|h|i|j)+(d>>>j)<<2)>>2]|0;d=j;i=j;j=(c[j+4>>2]&-8)-k|0;while(1){a=c[d+16>>2]|0;if(!a){a=c[d+20>>2]|0;if(!a)break}h=(c[a+4>>2]&-8)-k|0;f=h>>>0<j>>>0;d=a;i=f?a:i;j=f?h:j}h=i+k|0;if(h>>>0>i>>>0){f=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=c[a>>2]|0;if(!b){a=i+16|0;b=c[a>>2]|0;if(!b){d=0;break}}while(1){e=b+20|0;d=c[e>>2]|0;if(!d){e=b+16|0;d=c[e>>2]|0;if(!d)break;else{b=d;a=e}}else{b=d;a=e}}c[a>>2]=0;d=b}else{d=c[i+8>>2]|0;c[d+12>>2]=b;c[b+8>>2]=d;d=b}while(0);do if(f|0){b=c[i+28>>2]|0;a=8544+(b<<2)|0;if((i|0)==(c[a>>2]|0)){c[a>>2]=d;if(!d){c[2061]=g&~(1<<b);break}}else{v=f+16|0;c[((c[v>>2]|0)==(i|0)?v:f+20|0)>>2]=d;if(!d)break}c[d+24>>2]=f;b=c[i+16>>2]|0;if(b|0){c[d+16>>2]=b;c[b+24>>2]=d}b=c[i+20>>2]|0;if(b|0){c[d+20>>2]=b;c[b+24>>2]=d}}while(0);if(j>>>0<16){v=j+k|0;c[i+4>>2]=v|3;v=i+v+4|0;c[v>>2]=c[v>>2]|1}else{c[i+4>>2]=k|3;c[h+4>>2]=j|1;c[h+j>>2]=j;if(l|0){e=c[2065]|0;b=l>>>3;d=8280+(b<<1<<2)|0;b=1<<b;if(!(b&m)){c[2060]=b|m;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=e;c[b+12>>2]=e;c[e+8>>2]=b;c[e+12>>2]=d}c[2062]=j;c[2065]=h}v=i+8|0;R=w;return v|0}else m=k}else m=k}else m=k}else if(a>>>0<=4294967231){a=a+11|0;k=a&-8;e=c[2061]|0;if(e){f=0-k|0;a=a>>>8;if(a)if(k>>>0>16777215)j=31;else{m=(a+1048320|0)>>>16&8;q=a<<m;i=(q+520192|0)>>>16&4;q=q<<i;j=(q+245760|0)>>>16&2;j=14-(i|m|j)+(q<<j>>>15)|0;j=k>>>(j+7|0)&1|j<<1}else j=0;d=c[8544+(j<<2)>>2]|0;a:do if(!d){d=0;a=0;q=61}else{a=0;i=k<<((j|0)==31?0:25-(j>>>1)|0);g=0;while(1){h=(c[d+4>>2]&-8)-k|0;if(h>>>0<f>>>0)if(!h){a=d;f=0;q=65;break a}else{a=d;f=h}q=c[d+20>>2]|0;d=c[d+16+(i>>>31<<2)>>2]|0;g=(q|0)==0|(q|0)==(d|0)?g:q;if(!d){d=g;q=61;break}else i=i<<1}}while(0);if((q|0)==61){if((d|0)==0&(a|0)==0){a=2<<j;a=(a|0-a)&e;if(!a){m=k;break}m=(a&0-a)+-1|0;h=m>>>12&16;m=m>>>h;g=m>>>5&8;m=m>>>g;i=m>>>2&4;m=m>>>i;j=m>>>1&2;m=m>>>j;d=m>>>1&1;a=0;d=c[8544+((g|h|i|j|d)+(m>>>d)<<2)>>2]|0}if(!d){i=a;h=f}else q=65}if((q|0)==65){g=d;while(1){m=(c[g+4>>2]&-8)-k|0;d=m>>>0<f>>>0;f=d?m:f;a=d?g:a;d=c[g+16>>2]|0;if(!d)d=c[g+20>>2]|0;if(!d){i=a;h=f;break}else g=d}}if(((i|0)!=0?h>>>0<((c[2062]|0)-k|0)>>>0:0)?(l=i+k|0,l>>>0>i>>>0):0){g=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+20|0;b=c[a>>2]|0;if(!b){a=i+16|0;b=c[a>>2]|0;if(!b){b=0;break}}while(1){f=b+20|0;d=c[f>>2]|0;if(!d){f=b+16|0;d=c[f>>2]|0;if(!d)break;else{b=d;a=f}}else{b=d;a=f}}c[a>>2]=0}else{v=c[i+8>>2]|0;c[v+12>>2]=b;c[b+8>>2]=v}while(0);do if(g){a=c[i+28>>2]|0;d=8544+(a<<2)|0;if((i|0)==(c[d>>2]|0)){c[d>>2]=b;if(!b){e=e&~(1<<a);c[2061]=e;break}}else{v=g+16|0;c[((c[v>>2]|0)==(i|0)?v:g+20|0)>>2]=b;if(!b)break}c[b+24>>2]=g;a=c[i+16>>2]|0;if(a|0){c[b+16>>2]=a;c[a+24>>2]=b}a=c[i+20>>2]|0;if(a){c[b+20>>2]=a;c[a+24>>2]=b}}while(0);b:do if(h>>>0<16){v=h+k|0;c[i+4>>2]=v|3;v=i+v+4|0;c[v>>2]=c[v>>2]|1}else{c[i+4>>2]=k|3;c[l+4>>2]=h|1;c[l+h>>2]=h;b=h>>>3;if(h>>>0<256){d=8280+(b<<1<<2)|0;a=c[2060]|0;b=1<<b;if(!(a&b)){c[2060]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=l;c[b+12>>2]=l;c[l+8>>2]=b;c[l+12>>2]=d;break}b=h>>>8;if(b)if(h>>>0>16777215)d=31;else{u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;d=(v+245760|0)>>>16&2;d=14-(t|u|d)+(v<<d>>>15)|0;d=h>>>(d+7|0)&1|d<<1}else d=0;b=8544+(d<<2)|0;c[l+28>>2]=d;a=l+16|0;c[a+4>>2]=0;c[a>>2]=0;a=1<<d;if(!(e&a)){c[2061]=e|a;c[b>>2]=l;c[l+24>>2]=b;c[l+12>>2]=l;c[l+8>>2]=l;break}b=c[b>>2]|0;c:do if((c[b+4>>2]&-8|0)!=(h|0)){e=h<<((d|0)==31?0:25-(d>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(h|0)){b=a;break c}else{e=e<<1;b=a}}c[d>>2]=l;c[l+24>>2]=b;c[l+12>>2]=l;c[l+8>>2]=l;break b}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=l;c[u>>2]=l;c[l+8>>2]=v;c[l+12>>2]=b;c[l+24>>2]=0}while(0);v=i+8|0;R=w;return v|0}else m=k}else m=k}else m=-1;while(0);d=c[2062]|0;if(d>>>0>=m>>>0){b=d-m|0;a=c[2065]|0;if(b>>>0>15){v=a+m|0;c[2065]=v;c[2062]=b;c[v+4>>2]=b|1;c[a+d>>2]=b;c[a+4>>2]=m|3}else{c[2062]=0;c[2065]=0;c[a+4>>2]=d|3;v=a+d+4|0;c[v>>2]=c[v>>2]|1}v=a+8|0;R=w;return v|0}h=c[2063]|0;if(h>>>0>m>>>0){t=h-m|0;c[2063]=t;v=c[2066]|0;u=v+m|0;c[2066]=u;c[u+4>>2]=t|1;c[v+4>>2]=m|3;v=v+8|0;R=w;return v|0}if(!(c[2178]|0)){c[2180]=4096;c[2179]=4096;c[2181]=-1;c[2182]=-1;c[2183]=0;c[2171]=0;c[2178]=n&-16^1431655768;a=4096}else a=c[2180]|0;i=m+48|0;j=m+47|0;g=a+j|0;f=0-a|0;k=g&f;if(k>>>0<=m>>>0){v=0;R=w;return v|0}a=c[2170]|0;if(a|0?(l=c[2168]|0,n=l+k|0,n>>>0<=l>>>0|n>>>0>a>>>0):0){v=0;R=w;return v|0}d:do if(!(c[2171]&4)){d=c[2066]|0;e:do if(d){e=8688;while(1){n=c[e>>2]|0;if(n>>>0<=d>>>0?(n+(c[e+4>>2]|0)|0)>>>0>d>>>0:0)break;a=c[e+8>>2]|0;if(!a){q=128;break e}else e=a}b=g-h&f;if(b>>>0<2147483647){a=Vb(b|0)|0;if((a|0)==((c[e>>2]|0)+(c[e+4>>2]|0)|0)){if((a|0)!=(-1|0)){h=b;g=a;q=145;break d}}else{e=a;q=136}}else b=0}else q=128;while(0);do if((q|0)==128){d=Vb(0)|0;if((d|0)!=(-1|0)?(b=d,o=c[2179]|0,p=o+-1|0,b=((p&b|0)==0?0:(p+b&0-o)-b|0)+k|0,o=c[2168]|0,p=b+o|0,b>>>0>m>>>0&b>>>0<2147483647):0){n=c[2170]|0;if(n|0?p>>>0<=o>>>0|p>>>0>n>>>0:0){b=0;break}a=Vb(b|0)|0;if((a|0)==(d|0)){h=b;g=d;q=145;break d}else{e=a;q=136}}else b=0}while(0);do if((q|0)==136){d=0-b|0;if(!(i>>>0>b>>>0&(b>>>0<2147483647&(e|0)!=(-1|0))))if((e|0)==(-1|0)){b=0;break}else{h=b;g=e;q=145;break d}a=c[2180]|0;a=j-b+a&0-a;if(a>>>0>=2147483647){h=b;g=e;q=145;break d}if((Vb(a|0)|0)==(-1|0)){Vb(d|0)|0;b=0;break}else{h=a+b|0;g=e;q=145;break d}}while(0);c[2171]=c[2171]|4;q=143}else{b=0;q=143}while(0);if(((q|0)==143?k>>>0<2147483647:0)?(t=Vb(k|0)|0,p=Vb(0)|0,r=p-t|0,s=r>>>0>(m+40|0)>>>0,!((t|0)==(-1|0)|s^1|t>>>0<p>>>0&((t|0)!=(-1|0)&(p|0)!=(-1|0))^1)):0){h=s?r:b;g=t;q=145}if((q|0)==145){b=(c[2168]|0)+h|0;c[2168]=b;if(b>>>0>(c[2169]|0)>>>0)c[2169]=b;j=c[2066]|0;f:do if(j){b=8688;while(1){a=c[b>>2]|0;d=c[b+4>>2]|0;if((g|0)==(a+d|0)){q=154;break}e=c[b+8>>2]|0;if(!e)break;else b=e}if(((q|0)==154?(u=b+4|0,(c[b+12>>2]&8|0)==0):0)?g>>>0>j>>>0&a>>>0<=j>>>0:0){c[u>>2]=d+h;v=(c[2063]|0)+h|0;t=j+8|0;t=(t&7|0)==0?0:0-t&7;u=j+t|0;t=v-t|0;c[2066]=u;c[2063]=t;c[u+4>>2]=t|1;c[j+v+4>>2]=40;c[2067]=c[2182];break}if(g>>>0<(c[2064]|0)>>>0)c[2064]=g;d=g+h|0;b=8688;while(1){if((c[b>>2]|0)==(d|0)){q=162;break}a=c[b+8>>2]|0;if(!a)break;else b=a}if((q|0)==162?(c[b+12>>2]&8|0)==0:0){c[b>>2]=g;l=b+4|0;c[l>>2]=(c[l>>2]|0)+h;l=g+8|0;l=g+((l&7|0)==0?0:0-l&7)|0;b=d+8|0;b=d+((b&7|0)==0?0:0-b&7)|0;k=l+m|0;i=b-l-m|0;c[l+4>>2]=m|3;g:do if((j|0)==(b|0)){v=(c[2063]|0)+i|0;c[2063]=v;c[2066]=k;c[k+4>>2]=v|1}else{if((c[2065]|0)==(b|0)){v=(c[2062]|0)+i|0;c[2062]=v;c[2065]=k;c[k+4>>2]=v|1;c[k+v>>2]=v;break}a=c[b+4>>2]|0;if((a&3|0)==1){h=a&-8;e=a>>>3;h:do if(a>>>0<256){a=c[b+8>>2]|0;d=c[b+12>>2]|0;if((d|0)==(a|0)){c[2060]=c[2060]&~(1<<e);break}else{c[a+12>>2]=d;c[d+8>>2]=a;break}}else{g=c[b+24>>2]|0;a=c[b+12>>2]|0;do if((a|0)==(b|0)){d=b+16|0;e=d+4|0;a=c[e>>2]|0;if(!a){a=c[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=c[f>>2]|0;if(!e){f=a+16|0;e=c[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}c[d>>2]=0}else{v=c[b+8>>2]|0;c[v+12>>2]=a;c[a+8>>2]=v}while(0);if(!g)break;d=c[b+28>>2]|0;e=8544+(d<<2)|0;do if((c[e>>2]|0)!=(b|0)){v=g+16|0;c[((c[v>>2]|0)==(b|0)?v:g+20|0)>>2]=a;if(!a)break h}else{c[e>>2]=a;if(a|0)break;c[2061]=c[2061]&~(1<<d);break h}while(0);c[a+24>>2]=g;d=b+16|0;e=c[d>>2]|0;if(e|0){c[a+16>>2]=e;c[e+24>>2]=a}d=c[d+4>>2]|0;if(!d)break;c[a+20>>2]=d;c[d+24>>2]=a}while(0);b=b+h|0;f=h+i|0}else f=i;b=b+4|0;c[b>>2]=c[b>>2]&-2;c[k+4>>2]=f|1;c[k+f>>2]=f;b=f>>>3;if(f>>>0<256){d=8280+(b<<1<<2)|0;a=c[2060]|0;b=1<<b;if(!(a&b)){c[2060]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=k;c[b+12>>2]=k;c[k+8>>2]=b;c[k+12>>2]=d;break}b=f>>>8;do if(!b)e=0;else{if(f>>>0>16777215){e=31;break}u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}while(0);b=8544+(e<<2)|0;c[k+28>>2]=e;a=k+16|0;c[a+4>>2]=0;c[a>>2]=0;a=c[2061]|0;d=1<<e;if(!(a&d)){c[2061]=a|d;c[b>>2]=k;c[k+24>>2]=b;c[k+12>>2]=k;c[k+8>>2]=k;break}b=c[b>>2]|0;i:do if((c[b+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(f|0)){b=a;break i}else{e=e<<1;b=a}}c[d>>2]=k;c[k+24>>2]=b;c[k+12>>2]=k;c[k+8>>2]=k;break g}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=k;c[u>>2]=k;c[k+8>>2]=v;c[k+12>>2]=b;c[k+24>>2]=0}while(0);v=l+8|0;R=w;return v|0}b=8688;while(1){a=c[b>>2]|0;if(a>>>0<=j>>>0?(v=a+(c[b+4>>2]|0)|0,v>>>0>j>>>0):0)break;b=c[b+8>>2]|0}f=v+-47|0;a=f+8|0;a=f+((a&7|0)==0?0:0-a&7)|0;f=j+16|0;a=a>>>0<f>>>0?j:a;b=a+8|0;d=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=d-t|0;c[2066]=u;c[2063]=t;c[u+4>>2]=t|1;c[g+d+4>>2]=40;c[2067]=c[2182];d=a+4|0;c[d>>2]=27;c[b>>2]=c[2172];c[b+4>>2]=c[2173];c[b+8>>2]=c[2174];c[b+12>>2]=c[2175];c[2172]=g;c[2173]=h;c[2175]=0;c[2174]=b;b=a+24|0;do{u=b;b=b+4|0;c[b>>2]=7}while((u+8|0)>>>0<v>>>0);if((a|0)!=(j|0)){g=a-j|0;c[d>>2]=c[d>>2]&-2;c[j+4>>2]=g|1;c[a>>2]=g;b=g>>>3;if(g>>>0<256){d=8280+(b<<1<<2)|0;a=c[2060]|0;b=1<<b;if(!(a&b)){c[2060]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=j;c[b+12>>2]=j;c[j+8>>2]=b;c[j+12>>2]=d;break}b=g>>>8;if(b)if(g>>>0>16777215)e=31;else{u=(b+1048320|0)>>>16&8;v=b<<u;t=(v+520192|0)>>>16&4;v=v<<t;e=(v+245760|0)>>>16&2;e=14-(t|u|e)+(v<<e>>>15)|0;e=g>>>(e+7|0)&1|e<<1}else e=0;d=8544+(e<<2)|0;c[j+28>>2]=e;c[j+20>>2]=0;c[f>>2]=0;b=c[2061]|0;a=1<<e;if(!(b&a)){c[2061]=b|a;c[d>>2]=j;c[j+24>>2]=d;c[j+12>>2]=j;c[j+8>>2]=j;break}b=c[d>>2]|0;j:do if((c[b+4>>2]&-8|0)!=(g|0)){e=g<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(g|0)){b=a;break j}else{e=e<<1;b=a}}c[d>>2]=j;c[j+24>>2]=b;c[j+12>>2]=j;c[j+8>>2]=j;break f}while(0);u=b+8|0;v=c[u>>2]|0;c[v+12>>2]=j;c[u>>2]=j;c[j+8>>2]=v;c[j+12>>2]=b;c[j+24>>2]=0}}else{v=c[2064]|0;if((v|0)==0|g>>>0<v>>>0)c[2064]=g;c[2172]=g;c[2173]=h;c[2175]=0;c[2069]=c[2178];c[2068]=-1;c[2073]=8280;c[2072]=8280;c[2075]=8288;c[2074]=8288;c[2077]=8296;c[2076]=8296;c[2079]=8304;c[2078]=8304;c[2081]=8312;c[2080]=8312;c[2083]=8320;c[2082]=8320;c[2085]=8328;c[2084]=8328;c[2087]=8336;c[2086]=8336;c[2089]=8344;c[2088]=8344;c[2091]=8352;c[2090]=8352;c[2093]=8360;c[2092]=8360;c[2095]=8368;c[2094]=8368;c[2097]=8376;c[2096]=8376;c[2099]=8384;c[2098]=8384;c[2101]=8392;c[2100]=8392;c[2103]=8400;c[2102]=8400;c[2105]=8408;c[2104]=8408;c[2107]=8416;c[2106]=8416;c[2109]=8424;c[2108]=8424;c[2111]=8432;c[2110]=8432;c[2113]=8440;c[2112]=8440;c[2115]=8448;c[2114]=8448;c[2117]=8456;c[2116]=8456;c[2119]=8464;c[2118]=8464;c[2121]=8472;c[2120]=8472;c[2123]=8480;c[2122]=8480;c[2125]=8488;c[2124]=8488;c[2127]=8496;c[2126]=8496;c[2129]=8504;c[2128]=8504;c[2131]=8512;c[2130]=8512;c[2133]=8520;c[2132]=8520;c[2135]=8528;c[2134]=8528;v=h+-40|0;t=g+8|0;t=(t&7|0)==0?0:0-t&7;u=g+t|0;t=v-t|0;c[2066]=u;c[2063]=t;c[u+4>>2]=t|1;c[g+v+4>>2]=40;c[2067]=c[2182]}while(0);b=c[2063]|0;if(b>>>0>m>>>0){t=b-m|0;c[2063]=t;v=c[2066]|0;u=v+m|0;c[2066]=u;c[u+4>>2]=t|1;c[v+4>>2]=m|3;v=v+8|0;R=w;return v|0}}c[(gb()|0)>>2]=12;v=0;R=w;return v|0}function Za(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!a)return;d=a+-8|0;f=c[2064]|0;a=c[a+-4>>2]|0;b=a&-8;j=d+b|0;do if(!(a&1)){e=c[d>>2]|0;if(!(a&3))return;h=d+(0-e)|0;g=e+b|0;if(h>>>0<f>>>0)return;if((c[2065]|0)==(h|0)){a=j+4|0;b=c[a>>2]|0;if((b&3|0)!=3){i=h;b=g;break}c[2062]=g;c[a>>2]=b&-2;c[h+4>>2]=g|1;c[h+g>>2]=g;return}d=e>>>3;if(e>>>0<256){a=c[h+8>>2]|0;b=c[h+12>>2]|0;if((b|0)==(a|0)){c[2060]=c[2060]&~(1<<d);i=h;b=g;break}else{c[a+12>>2]=b;c[b+8>>2]=a;i=h;b=g;break}}f=c[h+24>>2]|0;a=c[h+12>>2]|0;do if((a|0)==(h|0)){b=h+16|0;d=b+4|0;a=c[d>>2]|0;if(!a){a=c[b>>2]|0;if(!a){a=0;break}}else b=d;while(1){e=a+20|0;d=c[e>>2]|0;if(!d){e=a+16|0;d=c[e>>2]|0;if(!d)break;else{a=d;b=e}}else{a=d;b=e}}c[b>>2]=0}else{i=c[h+8>>2]|0;c[i+12>>2]=a;c[a+8>>2]=i}while(0);if(f){b=c[h+28>>2]|0;d=8544+(b<<2)|0;if((c[d>>2]|0)==(h|0)){c[d>>2]=a;if(!a){c[2061]=c[2061]&~(1<<b);i=h;b=g;break}}else{i=f+16|0;c[((c[i>>2]|0)==(h|0)?i:f+20|0)>>2]=a;if(!a){i=h;b=g;break}}c[a+24>>2]=f;b=h+16|0;d=c[b>>2]|0;if(d|0){c[a+16>>2]=d;c[d+24>>2]=a}b=c[b+4>>2]|0;if(b){c[a+20>>2]=b;c[b+24>>2]=a;i=h;b=g}else{i=h;b=g}}else{i=h;b=g}}else{i=d;h=d}while(0);if(h>>>0>=j>>>0)return;a=j+4|0;e=c[a>>2]|0;if(!(e&1))return;if(!(e&2)){if((c[2066]|0)==(j|0)){j=(c[2063]|0)+b|0;c[2063]=j;c[2066]=i;c[i+4>>2]=j|1;if((i|0)!=(c[2065]|0))return;c[2065]=0;c[2062]=0;return}if((c[2065]|0)==(j|0)){j=(c[2062]|0)+b|0;c[2062]=j;c[2065]=h;c[i+4>>2]=j|1;c[h+j>>2]=j;return}f=(e&-8)+b|0;d=e>>>3;do if(e>>>0<256){b=c[j+8>>2]|0;a=c[j+12>>2]|0;if((a|0)==(b|0)){c[2060]=c[2060]&~(1<<d);break}else{c[b+12>>2]=a;c[a+8>>2]=b;break}}else{g=c[j+24>>2]|0;a=c[j+12>>2]|0;do if((a|0)==(j|0)){b=j+16|0;d=b+4|0;a=c[d>>2]|0;if(!a){a=c[b>>2]|0;if(!a){d=0;break}}else b=d;while(1){e=a+20|0;d=c[e>>2]|0;if(!d){e=a+16|0;d=c[e>>2]|0;if(!d)break;else{a=d;b=e}}else{a=d;b=e}}c[b>>2]=0;d=a}else{d=c[j+8>>2]|0;c[d+12>>2]=a;c[a+8>>2]=d;d=a}while(0);if(g|0){a=c[j+28>>2]|0;b=8544+(a<<2)|0;if((c[b>>2]|0)==(j|0)){c[b>>2]=d;if(!d){c[2061]=c[2061]&~(1<<a);break}}else{e=g+16|0;c[((c[e>>2]|0)==(j|0)?e:g+20|0)>>2]=d;if(!d)break}c[d+24>>2]=g;a=j+16|0;b=c[a>>2]|0;if(b|0){c[d+16>>2]=b;c[b+24>>2]=d}a=c[a+4>>2]|0;if(a|0){c[d+20>>2]=a;c[a+24>>2]=d}}}while(0);c[i+4>>2]=f|1;c[h+f>>2]=f;if((i|0)==(c[2065]|0)){c[2062]=f;return}}else{c[a>>2]=e&-2;c[i+4>>2]=b|1;c[h+b>>2]=b;f=b}a=f>>>3;if(f>>>0<256){d=8280+(a<<1<<2)|0;b=c[2060]|0;a=1<<a;if(!(b&a)){c[2060]=b|a;a=d;b=d+8|0}else{b=d+8|0;a=c[b>>2]|0}c[b>>2]=i;c[a+12>>2]=i;c[i+8>>2]=a;c[i+12>>2]=d;return}a=f>>>8;if(a)if(f>>>0>16777215)e=31;else{h=(a+1048320|0)>>>16&8;j=a<<h;g=(j+520192|0)>>>16&4;j=j<<g;e=(j+245760|0)>>>16&2;e=14-(g|h|e)+(j<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;a=8544+(e<<2)|0;c[i+28>>2]=e;c[i+20>>2]=0;c[i+16>>2]=0;b=c[2061]|0;d=1<<e;a:do if(!(b&d)){c[2061]=b|d;c[a>>2]=i;c[i+24>>2]=a;c[i+12>>2]=i;c[i+8>>2]=i}else{a=c[a>>2]|0;b:do if((c[a+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=a+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b)break;if((c[b+4>>2]&-8|0)==(f|0)){a=b;break b}else{e=e<<1;a=b}}c[d>>2]=i;c[i+24>>2]=a;c[i+12>>2]=i;c[i+8>>2]=i;break a}while(0);h=a+8|0;j=c[h>>2]|0;c[j+12>>2]=i;c[h>>2]=i;c[i+8>>2]=j;c[i+12>>2]=a;c[i+24>>2]=0}while(0);j=(c[2068]|0)+-1|0;c[2068]=j;if(j|0)return;a=8696;while(1){a=c[a>>2]|0;if(!a)break;else a=a+8|0}c[2068]=-1;return}function _a(a,b){a=a|0;b=b|0;var d=0;if(a){d=u(b,a)|0;if((b|a)>>>0>65535)d=((d>>>0)/(a>>>0)|0|0)==(b|0)?d:-1}else d=0;a=Ya(d)|0;if(!a)return a|0;if(!(c[a+-4>>2]&3))return a|0;Ub(a|0,0,d|0)|0;return a|0}function $a(a,b){a=a|0;b=b|0;var d=0,e=0;if(!a){b=Ya(b)|0;return b|0}if(b>>>0>4294967231){c[(gb()|0)>>2]=12;b=0;return b|0}d=ab(a+-8|0,b>>>0<11?16:b+11&-8)|0;if(d|0){b=d+8|0;return b|0}d=Ya(b)|0;if(!d){b=0;return b|0}e=c[a+-4>>2]|0;e=(e&-8)-((e&3|0)==0?8:4)|0;Sb(d|0,a|0,(e>>>0<b>>>0?e:b)|0)|0;Za(a);b=d;return b|0}function ab(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;l=a+4|0;m=c[l>>2]|0;d=m&-8;i=a+d|0;if(!(m&3)){if(b>>>0<256){a=0;return a|0}if(d>>>0>=(b+4|0)>>>0?(d-b|0)>>>0<=c[2180]<<1>>>0:0)return a|0;a=0;return a|0}if(d>>>0>=b>>>0){d=d-b|0;if(d>>>0<=15)return a|0;k=a+b|0;c[l>>2]=m&1|b|2;c[k+4>>2]=d|3;m=i+4|0;c[m>>2]=c[m>>2]|1;bb(k,d);return a|0}if((c[2066]|0)==(i|0)){k=(c[2063]|0)+d|0;d=k-b|0;e=a+b|0;if(k>>>0<=b>>>0){a=0;return a|0}c[l>>2]=m&1|b|2;c[e+4>>2]=d|1;c[2066]=e;c[2063]=d;return a|0}if((c[2065]|0)==(i|0)){e=(c[2062]|0)+d|0;if(e>>>0<b>>>0){a=0;return a|0}d=e-b|0;if(d>>>0>15){k=a+b|0;e=a+e|0;c[l>>2]=m&1|b|2;c[k+4>>2]=d|1;c[e>>2]=d;e=e+4|0;c[e>>2]=c[e>>2]&-2;e=k}else{c[l>>2]=m&1|e|2;e=a+e+4|0;c[e>>2]=c[e>>2]|1;e=0;d=0}c[2062]=d;c[2065]=e;return a|0}e=c[i+4>>2]|0;if(e&2|0){a=0;return a|0}j=(e&-8)+d|0;if(j>>>0<b>>>0){a=0;return a|0}k=j-b|0;f=e>>>3;do if(e>>>0<256){e=c[i+8>>2]|0;d=c[i+12>>2]|0;if((d|0)==(e|0)){c[2060]=c[2060]&~(1<<f);break}else{c[e+12>>2]=d;c[d+8>>2]=e;break}}else{h=c[i+24>>2]|0;d=c[i+12>>2]|0;do if((d|0)==(i|0)){e=i+16|0;f=e+4|0;d=c[f>>2]|0;if(!d){d=c[e>>2]|0;if(!d){f=0;break}}else e=f;while(1){g=d+20|0;f=c[g>>2]|0;if(!f){g=d+16|0;f=c[g>>2]|0;if(!f)break;else{d=f;e=g}}else{d=f;e=g}}c[e>>2]=0;f=d}else{f=c[i+8>>2]|0;c[f+12>>2]=d;c[d+8>>2]=f;f=d}while(0);if(h|0){d=c[i+28>>2]|0;e=8544+(d<<2)|0;if((c[e>>2]|0)==(i|0)){c[e>>2]=f;if(!f){c[2061]=c[2061]&~(1<<d);break}}else{g=h+16|0;c[((c[g>>2]|0)==(i|0)?g:h+20|0)>>2]=f;if(!f)break}c[f+24>>2]=h;d=i+16|0;e=c[d>>2]|0;if(e|0){c[f+16>>2]=e;c[e+24>>2]=f}d=c[d+4>>2]|0;if(d|0){c[f+20>>2]=d;c[d+24>>2]=f}}}while(0);if(k>>>0<16){c[l>>2]=m&1|j|2;m=a+j+4|0;c[m>>2]=c[m>>2]|1;return a|0}else{i=a+b|0;c[l>>2]=m&1|b|2;c[i+4>>2]=k|3;m=a+j+4|0;c[m>>2]=c[m>>2]|1;bb(i,k);return a|0}return 0}function bb(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0;i=a+b|0;d=c[a+4>>2]|0;do if(!(d&1)){f=c[a>>2]|0;if(!(d&3))return;h=a+(0-f)|0;b=f+b|0;if((c[2065]|0)==(h|0)){a=i+4|0;d=c[a>>2]|0;if((d&3|0)!=3)break;c[2062]=b;c[a>>2]=d&-2;c[h+4>>2]=b|1;c[i>>2]=b;return}e=f>>>3;if(f>>>0<256){a=c[h+8>>2]|0;d=c[h+12>>2]|0;if((d|0)==(a|0)){c[2060]=c[2060]&~(1<<e);break}else{c[a+12>>2]=d;c[d+8>>2]=a;break}}g=c[h+24>>2]|0;a=c[h+12>>2]|0;do if((a|0)==(h|0)){d=h+16|0;e=d+4|0;a=c[e>>2]|0;if(!a){a=c[d>>2]|0;if(!a){a=0;break}}else d=e;while(1){f=a+20|0;e=c[f>>2]|0;if(!e){f=a+16|0;e=c[f>>2]|0;if(!e)break;else{a=e;d=f}}else{a=e;d=f}}c[d>>2]=0}else{f=c[h+8>>2]|0;c[f+12>>2]=a;c[a+8>>2]=f}while(0);if(g){d=c[h+28>>2]|0;e=8544+(d<<2)|0;if((c[e>>2]|0)==(h|0)){c[e>>2]=a;if(!a){c[2061]=c[2061]&~(1<<d);break}}else{f=g+16|0;c[((c[f>>2]|0)==(h|0)?f:g+20|0)>>2]=a;if(!a)break}c[a+24>>2]=g;d=h+16|0;e=c[d>>2]|0;if(e|0){c[a+16>>2]=e;c[e+24>>2]=a}d=c[d+4>>2]|0;if(d){c[a+20>>2]=d;c[d+24>>2]=a}}}else h=a;while(0);a=i+4|0;e=c[a>>2]|0;if(!(e&2)){if((c[2066]|0)==(i|0)){i=(c[2063]|0)+b|0;c[2063]=i;c[2066]=h;c[h+4>>2]=i|1;if((h|0)!=(c[2065]|0))return;c[2065]=0;c[2062]=0;return}if((c[2065]|0)==(i|0)){i=(c[2062]|0)+b|0;c[2062]=i;c[2065]=h;c[h+4>>2]=i|1;c[h+i>>2]=i;return}f=(e&-8)+b|0;d=e>>>3;do if(e>>>0<256){a=c[i+8>>2]|0;b=c[i+12>>2]|0;if((b|0)==(a|0)){c[2060]=c[2060]&~(1<<d);break}else{c[a+12>>2]=b;c[b+8>>2]=a;break}}else{g=c[i+24>>2]|0;b=c[i+12>>2]|0;do if((b|0)==(i|0)){a=i+16|0;d=a+4|0;b=c[d>>2]|0;if(!b){b=c[a>>2]|0;if(!b){d=0;break}}else a=d;while(1){e=b+20|0;d=c[e>>2]|0;if(!d){e=b+16|0;d=c[e>>2]|0;if(!d)break;else{b=d;a=e}}else{b=d;a=e}}c[a>>2]=0;d=b}else{d=c[i+8>>2]|0;c[d+12>>2]=b;c[b+8>>2]=d;d=b}while(0);if(g|0){b=c[i+28>>2]|0;a=8544+(b<<2)|0;if((c[a>>2]|0)==(i|0)){c[a>>2]=d;if(!d){c[2061]=c[2061]&~(1<<b);break}}else{e=g+16|0;c[((c[e>>2]|0)==(i|0)?e:g+20|0)>>2]=d;if(!d)break}c[d+24>>2]=g;b=i+16|0;a=c[b>>2]|0;if(a|0){c[d+16>>2]=a;c[a+24>>2]=d}b=c[b+4>>2]|0;if(b|0){c[d+20>>2]=b;c[b+24>>2]=d}}}while(0);c[h+4>>2]=f|1;c[h+f>>2]=f;if((h|0)==(c[2065]|0)){c[2062]=f;return}}else{c[a>>2]=e&-2;c[h+4>>2]=b|1;c[h+b>>2]=b;f=b}b=f>>>3;if(f>>>0<256){d=8280+(b<<1<<2)|0;a=c[2060]|0;b=1<<b;if(!(a&b)){c[2060]=a|b;b=d;a=d+8|0}else{a=d+8|0;b=c[a>>2]|0}c[a>>2]=h;c[b+12>>2]=h;c[h+8>>2]=b;c[h+12>>2]=d;return}b=f>>>8;if(b)if(f>>>0>16777215)e=31;else{g=(b+1048320|0)>>>16&8;i=b<<g;d=(i+520192|0)>>>16&4;i=i<<d;e=(i+245760|0)>>>16&2;e=14-(d|g|e)+(i<<e>>>15)|0;e=f>>>(e+7|0)&1|e<<1}else e=0;b=8544+(e<<2)|0;c[h+28>>2]=e;c[h+20>>2]=0;c[h+16>>2]=0;a=c[2061]|0;d=1<<e;if(!(a&d)){c[2061]=a|d;c[b>>2]=h;c[h+24>>2]=b;c[h+12>>2]=h;c[h+8>>2]=h;return}b=c[b>>2]|0;a:do if((c[b+4>>2]&-8|0)!=(f|0)){e=f<<((e|0)==31?0:25-(e>>>1)|0);while(1){d=b+16+(e>>>31<<2)|0;a=c[d>>2]|0;if(!a)break;if((c[a+4>>2]&-8|0)==(f|0)){b=a;break a}else{e=e<<1;b=a}}c[d>>2]=h;c[h+24>>2]=b;c[h+12>>2]=h;c[h+8>>2]=h;return}while(0);g=b+8|0;i=c[g>>2]|0;c[i+12>>2]=h;c[g>>2]=h;c[h+8>>2]=i;c[h+12>>2]=b;c[h+24>>2]=0;return}function cb(a){a=a|0;var b=0,d=0;b=R;R=R+16|0;d=b;c[d>>2]=hb(c[a+60>>2]|0)|0;a=fb(F(6,d|0)|0)|0;R=b;return a|0}function db(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;m=R;R=R+48|0;k=m+32|0;g=m+16|0;f=m;i=a+28|0;e=c[i>>2]|0;c[f>>2]=e;j=a+20|0;e=(c[j>>2]|0)-e|0;c[f+4>>2]=e;c[f+8>>2]=b;c[f+12>>2]=d;e=e+d|0;h=a+60|0;c[g>>2]=c[h>>2];c[g+4>>2]=f;c[g+8>>2]=2;g=fb(D(146,g|0)|0)|0;a:do if((e|0)!=(g|0)){b=2;while(1){if((g|0)<0)break;e=e-g|0;o=c[f+4>>2]|0;n=g>>>0>o>>>0;f=n?f+8|0:f;b=b+(n<<31>>31)|0;o=g-(n?o:0)|0;c[f>>2]=(c[f>>2]|0)+o;n=f+4|0;c[n>>2]=(c[n>>2]|0)-o;c[k>>2]=c[h>>2];c[k+4>>2]=f;c[k+8>>2]=b;g=fb(D(146,k|0)|0)|0;if((e|0)==(g|0)){l=3;break a}}c[a+16>>2]=0;c[i>>2]=0;c[j>>2]=0;c[a>>2]=c[a>>2]|32;if((b|0)==2)d=0;else d=d-(c[f+4>>2]|0)|0}else l=3;while(0);if((l|0)==3){o=c[a+44>>2]|0;c[a+16>>2]=o+(c[a+48>>2]|0);c[i>>2]=o;c[j>>2]=o}R=m;return d|0}function eb(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0;f=R;R=R+32|0;g=f;e=f+20|0;c[g>>2]=c[a+60>>2];c[g+4>>2]=0;c[g+8>>2]=b;c[g+12>>2]=e;c[g+16>>2]=d;if((fb(C(140,g|0)|0)|0)<0){c[e>>2]=-1;a=-1}else a=c[e>>2]|0;R=f;return a|0}function fb(a){a=a|0;if(a>>>0>4294963200){c[(gb()|0)>>2]=0-a;a=-1}return a|0}function gb(){return 8800}function hb(a){a=a|0;return a|0}function ib(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0;g=R;R=R+32|0;f=g;c[b+36>>2]=4;if((c[b>>2]&64|0)==0?(c[f>>2]=c[b+60>>2],c[f+4>>2]=21523,c[f+8>>2]=g+16,E(54,f|0)|0):0)a[b+75>>0]=-1;f=db(b,d,e)|0;R=g;return f|0}function jb(a){a=a|0;return (a+-48|0)>>>0<10|0}function kb(){return 3928}function lb(b,c){b=b|0;c=c|0;var d=0,e=0;d=a[b>>0]|0;e=a[c>>0]|0;if(d<<24>>24==0?1:d<<24>>24!=e<<24>>24)b=e;else{do{b=b+1|0;c=c+1|0;d=a[b>>0]|0;e=a[c>>0]|0}while(!(d<<24>>24==0?1:d<<24>>24!=e<<24>>24));b=e}return (d&255)-(b&255)|0}function mb(a){a=a|0;return}function nb(a){a=a|0;return 1}function ob(b){b=b|0;var d=0,e=0;d=b+74|0;e=a[d>>0]|0;a[d>>0]=e+255|e;d=c[b>>2]|0;if(!(d&8)){c[b+8>>2]=0;c[b+4>>2]=0;e=c[b+44>>2]|0;c[b+28>>2]=e;c[b+20>>2]=e;c[b+16>>2]=e+(c[b+48>>2]|0);b=0}else{c[b>>2]=d|32;b=-1}return b|0}function pb(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0;f=e+16|0;g=c[f>>2]|0;if(!g)if(!(ob(e)|0)){g=c[f>>2]|0;h=5}else f=0;else h=5;a:do if((h|0)==5){j=e+20|0;i=c[j>>2]|0;f=i;if((g-i|0)>>>0<d>>>0){f=V[c[e+36>>2]&7](e,b,d)|0;break}b:do if((a[e+75>>0]|0)<0|(d|0)==0){h=0;g=b}else{i=d;while(1){g=i+-1|0;if((a[b+g>>0]|0)==10)break;if(!g){h=0;g=b;break b}else i=g}f=V[c[e+36>>2]&7](e,b,i)|0;if(f>>>0<i>>>0)break a;h=i;g=b+i|0;d=d-i|0;f=c[j>>2]|0}while(0);Sb(f|0,g|0,d|0)|0;c[j>>2]=(c[j>>2]|0)+d;f=h+d|0}while(0);return f|0}function qb(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=d&255;f=(e|0)!=0;a:do if(f&(b&3|0)!=0){g=d&255;while(1){if((a[b>>0]|0)==g<<24>>24){i=6;break a}b=b+1|0;e=e+-1|0;f=(e|0)!=0;if(!(f&(b&3|0)!=0)){i=5;break}}}else i=5;while(0);if((i|0)==5)if(f)i=6;else i=16;b:do if((i|0)==6){g=d&255;if((a[b>>0]|0)==g<<24>>24)if(!e){i=16;break}else break;f=u(h,16843009)|0;c:do if(e>>>0>3)while(1){h=c[b>>2]^f;if((h&-2139062144^-2139062144)&h+-16843009|0)break c;b=b+4|0;e=e+-4|0;if(e>>>0<=3){i=11;break}}else i=11;while(0);if((i|0)==11)if(!e){i=16;break}while(1){if((a[b>>0]|0)==g<<24>>24)break b;e=e+-1|0;if(!e){i=16;break}else b=b+1|0}}while(0);if((i|0)==16)b=0;return b|0}function rb(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;r=R;R=R+224|0;m=r+208|0;n=r+160|0;p=r+80|0;q=r;f=n;g=f+40|0;do{c[f>>2]=0;f=f+4|0}while((f|0)<(g|0));c[m>>2]=c[e>>2];if((sb(0,d,m,p,n)|0)<0)e=-1;else{if((c[b+76>>2]|0)>-1)o=nb(b)|0;else o=0;e=c[b>>2]|0;l=e&32;if((a[b+74>>0]|0)<1)c[b>>2]=e&-33;f=b+48|0;if(!(c[f>>2]|0)){g=b+44|0;h=c[g>>2]|0;c[g>>2]=q;i=b+28|0;c[i>>2]=q;j=b+20|0;c[j>>2]=q;c[f>>2]=80;k=b+16|0;c[k>>2]=q+80;e=sb(b,d,m,p,n)|0;if(h){V[c[b+36>>2]&7](b,0,0)|0;e=(c[j>>2]|0)==0?-1:e;c[g>>2]=h;c[f>>2]=0;c[k>>2]=0;c[i>>2]=0;c[j>>2]=0}}else e=sb(b,d,m,p,n)|0;f=c[b>>2]|0;c[b>>2]=f|l;if(o|0)mb(b);e=(f&32|0)==0?e:-1}R=r;return e|0}function sb(d,e,f,h,i){d=d|0;e=e|0;f=f|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;H=R;R=R+64|0;C=H+56|0;D=H+40|0;y=H;F=H+48|0;G=H+60|0;c[C>>2]=e;v=(d|0)!=0;w=y+40|0;x=w;y=y+39|0;A=F+4|0;j=0;e=0;l=0;a:while(1){do{do if((e|0)>-1)if((j|0)>(2147483647-e|0)){c[(gb()|0)>>2]=75;e=-1;break}else{e=j+e|0;break}while(0);p=c[C>>2]|0;j=a[p>>0]|0;if(!(j<<24>>24)){u=92;break a}k=p;b:while(1){switch(j<<24>>24){case 37:{u=10;break b}case 0:{j=k;break b}default:{}}t=k+1|0;c[C>>2]=t;j=a[t>>0]|0;k=t}c:do if((u|0)==10){u=0;j=k;do{if((a[k+1>>0]|0)!=37)break c;j=j+1|0;k=k+2|0;c[C>>2]=k}while((a[k>>0]|0)==37)}while(0);j=j-p|0;if(v)tb(d,p,j)}while((j|0)!=0);t=(jb(a[(c[C>>2]|0)+1>>0]|0)|0)==0;k=c[C>>2]|0;if(!t?(a[k+2>>0]|0)==36:0){r=(a[k+1>>0]|0)+-48|0;n=1;j=3}else{r=-1;n=l;j=1}j=k+j|0;c[C>>2]=j;k=a[j>>0]|0;l=(k<<24>>24)+-32|0;if(l>>>0>31|(1<<l&75913|0)==0)m=0;else{m=0;do{m=1<<l|m;j=j+1|0;c[C>>2]=j;k=a[j>>0]|0;l=(k<<24>>24)+-32|0}while(!(l>>>0>31|(1<<l&75913|0)==0))}if(k<<24>>24==42){if((jb(a[j+1>>0]|0)|0)!=0?(E=c[C>>2]|0,(a[E+2>>0]|0)==36):0){j=E+1|0;c[i+((a[j>>0]|0)+-48<<2)>>2]=10;j=c[h+((a[j>>0]|0)+-48<<3)>>2]|0;l=1;k=E+3|0}else{if(n|0){e=-1;break}if(v){t=(c[f>>2]|0)+(4-1)&~(4-1);j=c[t>>2]|0;c[f>>2]=t+4}else j=0;l=0;k=(c[C>>2]|0)+1|0}c[C>>2]=k;t=(j|0)<0;s=t?0-j|0:j;m=t?m|8192:m;t=l}else{j=ub(C)|0;if((j|0)<0){e=-1;break}s=j;t=n;k=c[C>>2]|0}do if((a[k>>0]|0)==46){j=k+1|0;if((a[j>>0]|0)!=42){c[C>>2]=j;j=ub(C)|0;k=c[C>>2]|0;break}if(jb(a[k+2>>0]|0)|0?(B=c[C>>2]|0,(a[B+3>>0]|0)==36):0){j=B+2|0;c[i+((a[j>>0]|0)+-48<<2)>>2]=10;j=c[h+((a[j>>0]|0)+-48<<3)>>2]|0;k=B+4|0;c[C>>2]=k;break}if(t|0){e=-1;break a}if(v){q=(c[f>>2]|0)+(4-1)&~(4-1);j=c[q>>2]|0;c[f>>2]=q+4}else j=0;k=(c[C>>2]|0)+2|0;c[C>>2]=k}else j=-1;while(0);q=0;while(1){if(((a[k>>0]|0)+-65|0)>>>0>57){e=-1;break a}l=k;k=k+1|0;c[C>>2]=k;l=a[(a[l>>0]|0)+-65+(3296+(q*58|0))>>0]|0;n=l&255;if((n+-1|0)>>>0>=8)break;else q=n}if(!(l<<24>>24)){e=-1;break}o=(r|0)>-1;do if(l<<24>>24==19)if(o){e=-1;break a}else u=54;else{if(o){c[i+(r<<2)>>2]=n;o=h+(r<<3)|0;r=c[o+4>>2]|0;u=D;c[u>>2]=c[o>>2];c[u+4>>2]=r;u=54;break}if(!v){e=0;break a}vb(D,n,f);k=c[C>>2]|0;u=55}while(0);if((u|0)==54){u=0;if(v)u=55;else j=0}d:do if((u|0)==55){u=0;k=a[k+-1>>0]|0;k=(q|0)!=0&(k&15|0)==3?k&-33:k;o=m&-65537;r=(m&8192|0)==0?m:o;e:do switch(k|0){case 110:switch((q&255)<<24>>24){case 0:{c[c[D>>2]>>2]=e;j=0;break d}case 1:{c[c[D>>2]>>2]=e;j=0;break d}case 2:{j=c[D>>2]|0;c[j>>2]=e;c[j+4>>2]=((e|0)<0)<<31>>31;j=0;break d}case 3:{b[c[D>>2]>>1]=e;j=0;break d}case 4:{a[c[D>>2]>>0]=e;j=0;break d}case 6:{c[c[D>>2]>>2]=e;j=0;break d}case 7:{j=c[D>>2]|0;c[j>>2]=e;c[j+4>>2]=((e|0)<0)<<31>>31;j=0;break d}default:{j=0;break d}}case 112:{k=120;j=j>>>0>8?j:8;l=r|8;u=67;break}case 88:case 120:{l=r;u=67;break}case 111:{l=D;k=c[l>>2]|0;l=c[l+4>>2]|0;p=xb(k,l,w)|0;o=x-p|0;m=0;n=7088;j=(r&8|0)==0|(j|0)>(o|0)?j:o+1|0;o=r;u=73;break}case 105:case 100:{l=D;k=c[l>>2]|0;l=c[l+4>>2]|0;if((l|0)<0){k=Mb(0,0,k|0,l|0)|0;l=z()|0;m=D;c[m>>2]=k;c[m+4>>2]=l;m=1;n=7088;u=72;break e}else{m=(r&2049|0)!=0&1;n=(r&2048|0)==0?((r&1|0)==0?7088:7090):7089;u=72;break e}}case 117:{l=D;m=0;n=7088;k=c[l>>2]|0;l=c[l+4>>2]|0;u=72;break}case 99:{a[y>>0]=c[D>>2];p=y;m=0;n=7088;l=1;k=o;j=x;break}case 115:{q=c[D>>2]|0;q=(q|0)==0?7098:q;r=qb(q,0,j)|0;I=(r|0)==0;p=q;m=0;n=7088;l=I?j:r-q|0;k=o;j=I?q+j|0:r;break}case 67:{c[F>>2]=c[D>>2];c[A>>2]=0;c[D>>2]=F;n=-1;u=79;break}case 83:{if(!j){zb(d,32,s,0,r);j=0;u=89}else{n=j;u=79}break}case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:{j=Bb(d,+g[D>>3],s,j,r,k)|0;break d}default:{m=0;n=7088;l=j;k=r;j=x}}while(0);f:do if((u|0)==67){I=D;r=c[I>>2]|0;I=c[I+4>>2]|0;p=wb(r,I,w,k&32)|0;n=(l&8|0)==0|(r|0)==0&(I|0)==0;m=n?0:2;n=n?7088:7088+(k>>>4)|0;o=l;k=r;l=I;u=73}else if((u|0)==72){p=yb(k,l,w)|0;o=r;u=73}else if((u|0)==79){u=0;m=c[D>>2]|0;j=0;while(1){k=c[m>>2]|0;if(!k)break;k=Ab(G,k)|0;l=(k|0)<0;if(l|k>>>0>(n-j|0)>>>0){u=83;break}j=k+j|0;if(n>>>0>j>>>0)m=m+4|0;else break}if((u|0)==83){u=0;if(l){e=-1;break a}}zb(d,32,s,j,r);if(!j){j=0;u=89}else{l=c[D>>2]|0;m=0;while(1){k=c[l>>2]|0;if(!k){u=89;break f}k=Ab(G,k)|0;m=k+m|0;if((m|0)>(j|0)){u=89;break f}tb(d,G,k);if(m>>>0>=j>>>0){u=89;break}else l=l+4|0}}}while(0);if((u|0)==73){u=0;l=(k|0)!=0|(l|0)!=0;k=(j|0)!=0|l;l=x-p+((l^1)&1)|0;p=k?p:w;l=k?((j|0)>(l|0)?j:l):0;k=(j|0)>-1?o&-65537:o;j=x}else if((u|0)==89){u=0;zb(d,32,s,j,r^8192);j=(s|0)>(j|0)?s:j;break}r=j-p|0;q=(l|0)<(r|0)?r:l;I=q+m|0;j=(s|0)<(I|0)?I:s;zb(d,32,j,I,k);tb(d,n,m);zb(d,48,j,I,k^65536);zb(d,48,q,r,0);tb(d,p,r);zb(d,32,j,I,k^8192)}while(0);l=t}g:do if((u|0)==92)if(!d)if(!l)e=0;else{e=1;while(1){j=c[i+(e<<2)>>2]|0;if(!j)break;vb(h+(e<<3)|0,j,f);e=e+1|0;if(e>>>0>=10){e=1;break g}}while(1){if(c[i+(e<<2)>>2]|0){e=-1;break g}e=e+1|0;if(e>>>0>=10){e=1;break}}}while(0);R=H;return e|0}function tb(a,b,d){a=a|0;b=b|0;d=d|0;if(!(c[a>>2]&32))pb(b,d,a)|0;return}function ub(b){b=b|0;var d=0,e=0;if(!(jb(a[c[b>>2]>>0]|0)|0))d=0;else{d=0;do{e=c[b>>2]|0;d=(d*10|0)+-48+(a[e>>0]|0)|0;e=e+1|0;c[b>>2]=e}while((jb(a[e>>0]|0)|0)!=0)}return d|0}function vb(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,h=0.0;a:do if(b>>>0<=20)do switch(b|0){case 9:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;c[a>>2]=b;break a}case 10:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=b;c[e+4>>2]=((b|0)<0)<<31>>31;break a}case 11:{e=(c[d>>2]|0)+(4-1)&~(4-1);b=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=b;c[e+4>>2]=0;break a}case 12:{e=(c[d>>2]|0)+(8-1)&~(8-1);b=e;f=c[b>>2]|0;b=c[b+4>>2]|0;c[d>>2]=e+8;e=a;c[e>>2]=f;c[e+4>>2]=b;break a}case 13:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;e=(e&65535)<<16>>16;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a}case 14:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e&65535;c[f+4>>2]=0;break a}case 15:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;e=(e&255)<<24>>24;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a}case 16:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e&255;c[f+4>>2]=0;break a}case 17:{f=(c[d>>2]|0)+(8-1)&~(8-1);h=+g[f>>3];c[d>>2]=f+8;g[a>>3]=h;break a}case 18:{f=(c[d>>2]|0)+(8-1)&~(8-1);h=+g[f>>3];c[d>>2]=f+8;g[a>>3]=h;break a}default:break a}while(0);while(0);return}function wb(b,c,e,f){b=b|0;c=c|0;e=e|0;f=f|0;if(!((b|0)==0&(c|0)==0))do{e=e+-1|0;a[e>>0]=d[3760+(b&15)>>0]|0|f;b=Qb(b|0,c|0,4)|0;c=z()|0}while(!((b|0)==0&(c|0)==0));return e|0}function xb(b,c,d){b=b|0;c=c|0;d=d|0;if(!((b|0)==0&(c|0)==0))do{d=d+-1|0;a[d>>0]=b&7|48;b=Qb(b|0,c|0,3)|0;c=z()|0}while(!((b|0)==0&(c|0)==0));return d|0}function yb(b,c,d){b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;if(c>>>0>0|(c|0)==0&b>>>0>4294967295){do{e=b;b=Pb(b|0,c|0,10,0)|0;f=c;c=z()|0;g=Kb(b|0,c|0,10,0)|0;g=Mb(e|0,f|0,g|0,z()|0)|0;z()|0;d=d+-1|0;a[d>>0]=g&255|48}while(f>>>0>9|(f|0)==9&e>>>0>4294967295);c=b}else c=b;if(c)do{g=c;c=(c>>>0)/10|0;d=d+-1|0;a[d>>0]=g-(c*10|0)|48}while(g>>>0>=10);return d|0}function zb(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0;g=R;R=R+256|0;f=g;if((c|0)>(d|0)&(e&73728|0)==0){e=c-d|0;Ub(f|0,b<<24>>24|0,(e>>>0<256?e:256)|0)|0;if(e>>>0>255){b=c-d|0;do{tb(a,f,256);e=e+-256|0}while(e>>>0>255);e=b&255}tb(a,f,e)}R=g;return}function Ab(a,b){a=a|0;b=b|0;if(!a)a=0;else a=Fb(a,b,0)|0;return a|0}function Bb(b,e,f,g,h,i){b=b|0;e=+e;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0.0,r=0,s=0,t=0,v=0,w=0,x=0,y=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0;H=R;R=R+560|0;l=H+32|0;v=H+536|0;G=H;F=G;m=H+540|0;c[v>>2]=0;E=m+12|0;Cb(e)|0;j=z()|0;if((j|0)<0){e=-e;Cb(e)|0;D=1;C=7105;j=z()|0}else{D=(h&2049|0)!=0&1;C=(h&2048|0)==0?((h&1|0)==0?7106:7111):7108}do if(0==0&(j&2146435072|0)==2146435072){G=(i&32|0)!=0;j=D+3|0;zb(b,32,f,j,h&-65537);tb(b,C,D);tb(b,e!=e|0.0!=0.0?(G?7132:7136):G?7124:7128,3);zb(b,32,f,j,h^8192)}else{q=+Db(e,v)*2.0;j=q!=0.0;if(j)c[v>>2]=(c[v>>2]|0)+-1;t=i|32;if((t|0)==97){o=i&32;r=(o|0)==0?C:C+9|0;p=D|2;j=12-g|0;do if(!(g>>>0>11|(j|0)==0)){e=8.0;do{j=j+-1|0;e=e*16.0}while((j|0)!=0);if((a[r>>0]|0)==45){e=-(e+(-q-e));break}else{e=q+e-e;break}}else e=q;while(0);k=c[v>>2]|0;j=(k|0)<0?0-k|0:k;j=yb(j,((j|0)<0)<<31>>31,E)|0;if((j|0)==(E|0)){j=m+11|0;a[j>>0]=48}a[j+-1>>0]=(k>>31&2)+43;n=j+-2|0;a[n>>0]=i+15;k=(g|0)<1;l=(h&8|0)==0;m=G;do{D=~~e;j=m+1|0;a[m>>0]=o|d[3760+D>>0];e=(e-+(D|0))*16.0;if((j-F|0)==1?!(l&(k&e==0.0)):0){a[j>>0]=46;m=m+2|0}else m=j}while(e!=0.0);if((g|0)!=0?(-2-F+m|0)<(g|0):0){k=E;l=n;j=g+2+k-l|0}else{k=E;l=n;j=k-F-l+m|0}E=j+p|0;zb(b,32,f,E,h);tb(b,r,p);zb(b,48,f,E,h^65536);F=m-F|0;tb(b,G,F);G=k-l|0;zb(b,48,j-(F+G)|0,0,0);tb(b,n,G);zb(b,32,f,E,h^8192);j=E;break}k=(g|0)<0?6:g;if(j){j=(c[v>>2]|0)+-28|0;c[v>>2]=j;e=q*268435456.0}else{e=q;j=c[v>>2]|0}B=(j|0)<0?l:l+288|0;l=B;do{y=~~e>>>0;c[l>>2]=y;l=l+4|0;e=(e-+(y>>>0))*1.0e9}while(e!=0.0);y=B;if((j|0)>0){o=B;while(1){n=(j|0)<29?j:29;j=l+-4|0;if(j>>>0>=o>>>0){m=0;do{s=Rb(c[j>>2]|0,0,n|0)|0;s=Lb(s|0,z()|0,m|0,0)|0;w=z()|0;m=Pb(s|0,w|0,1e9,0)|0;x=Kb(m|0,z()|0,1e9,0)|0;x=Mb(s|0,w|0,x|0,z()|0)|0;z()|0;c[j>>2]=x;j=j+-4|0}while(j>>>0>=o>>>0);if(m){x=o+-4|0;c[x>>2]=m;m=x}else m=o}else m=o;a:do if(l>>>0>m>>>0){j=l;while(1){l=j+-4|0;if(c[l>>2]|0){l=j;break a}if(l>>>0>m>>>0)j=l;else break}}while(0);j=(c[v>>2]|0)-n|0;c[v>>2]=j;if((j|0)>0)o=m;else break}}else m=B;if((j|0)<0){g=((k+25|0)/9|0)+1|0;s=(t|0)==102;do{r=0-j|0;r=(r|0)<9?r:9;if(m>>>0<l>>>0){n=(1<<r)+-1|0;o=1e9>>>r;p=0;j=m;do{x=c[j>>2]|0;c[j>>2]=(x>>>r)+p;p=u(x&n,o)|0;j=j+4|0}while(j>>>0<l>>>0);m=(c[m>>2]|0)==0?m+4|0:m;if(p){c[l>>2]=p;l=l+4|0}}else m=(c[m>>2]|0)==0?m+4|0:m;j=s?B:m;l=(l-j>>2|0)>(g|0)?j+(g<<2)|0:l;j=(c[v>>2]|0)+r|0;c[v>>2]=j}while((j|0)<0);s=m}else s=m;if(s>>>0<l>>>0){j=(y-s>>2)*9|0;n=c[s>>2]|0;if(n>>>0>=10){m=10;do{m=m*10|0;j=j+1|0}while(n>>>0>=m>>>0)}}else j=0;w=(t|0)==103;x=(k|0)!=0;m=k-((t|0)==102?0:j)+((x&w)<<31>>31)|0;if((m|0)<(((l-y>>2)*9|0)+-9|0)){v=m+9216|0;m=(v|0)/9|0;g=B+4+(m+-1024<<2)|0;m=v-(m*9|0)|0;if((m|0)<8){n=10;while(1){n=n*10|0;if((m|0)<7)m=m+1|0;else break}}else n=10;p=c[g>>2]|0;m=(p>>>0)/(n>>>0)|0;r=p-(u(m,n)|0)|0;o=(g+4|0)==(l|0);if(!(o&(r|0)==0)){q=(m&1|0)==0?9007199254740992.0:9007199254740994.0;v=n>>>1;e=r>>>0<v>>>0?.5:o&(r|0)==(v|0)?1.0:1.5;if(D){v=(a[C>>0]|0)==45;e=v?-e:e;q=v?-q:q}m=p-r|0;c[g>>2]=m;if(q+e!=q){v=m+n|0;c[g>>2]=v;if(v>>>0>999999999){n=g;j=s;while(1){m=n+-4|0;c[n>>2]=0;if(m>>>0<j>>>0){j=j+-4|0;c[j>>2]=0}v=(c[m>>2]|0)+1|0;c[m>>2]=v;if(v>>>0>999999999)n=m;else{n=j;break}}}else{m=g;n=s}j=(y-n>>2)*9|0;p=c[n>>2]|0;if(p>>>0>=10){o=10;do{o=o*10|0;j=j+1|0}while(p>>>0>=o>>>0)}}else{m=g;n=s}}else{m=g;n=s}v=m+4|0;l=l>>>0>v>>>0?v:l}else n=s;g=0-j|0;b:do if(l>>>0>n>>>0)while(1){m=l+-4|0;if(c[m>>2]|0){v=l;t=1;break b}if(m>>>0>n>>>0)l=m;else{v=m;t=0;break}}else{v=l;t=0}while(0);do if(w){k=k+((x^1)&1)|0;if((k|0)>(j|0)&(j|0)>-5){o=i+-1|0;k=k+-1-j|0}else{o=i+-2|0;k=k+-1|0}if(!(h&8)){if(t?(A=c[v+-4>>2]|0,(A|0)!=0):0)if(!((A>>>0)%10|0)){m=0;l=10;do{l=l*10|0;m=m+1|0}while(!((A>>>0)%(l>>>0)|0|0))}else m=0;else m=9;l=((v-y>>2)*9|0)+-9|0;if((o|32|0)==102){i=l-m|0;i=(i|0)>0?i:0;k=(k|0)<(i|0)?k:i;break}else{i=l+j-m|0;i=(i|0)>0?i:0;k=(k|0)<(i|0)?k:i;break}}}else o=i;while(0);s=(k|0)!=0;p=s?1:h>>>3&1;r=(o|32|0)==102;if(r){w=0;j=(j|0)>0?j:0}else{l=(j|0)<0?g:j;l=yb(l,((l|0)<0)<<31>>31,E)|0;m=E;if((m-l|0)<2)do{l=l+-1|0;a[l>>0]=48}while((m-l|0)<2);a[l+-1>>0]=(j>>31&2)+43;j=l+-2|0;a[j>>0]=o;w=j;j=m-j|0}j=D+1+k+p+j|0;zb(b,32,f,j,h);tb(b,C,D);zb(b,48,f,j,h^65536);if(r){p=n>>>0>B>>>0?B:n;r=G+9|0;n=r;o=G+8|0;m=p;do{l=yb(c[m>>2]|0,0,r)|0;if((m|0)==(p|0)){if((l|0)==(r|0)){a[o>>0]=48;l=o}}else if(l>>>0>G>>>0){Ub(G|0,48,l-F|0)|0;do l=l+-1|0;while(l>>>0>G>>>0)}tb(b,l,n-l|0);m=m+4|0}while(m>>>0<=B>>>0);if(!((h&8|0)==0&(s^1)))tb(b,7140,1);if(m>>>0<v>>>0&(k|0)>0)while(1){l=yb(c[m>>2]|0,0,r)|0;if(l>>>0>G>>>0){Ub(G|0,48,l-F|0)|0;do l=l+-1|0;while(l>>>0>G>>>0)}tb(b,l,(k|0)<9?k:9);m=m+4|0;l=k+-9|0;if(!(m>>>0<v>>>0&(k|0)>9)){k=l;break}else k=l}zb(b,48,k+9|0,9,0)}else{v=t?v:n+4|0;if(n>>>0<v>>>0&(k|0)>-1){g=G+9|0;s=(h&8|0)==0;t=g;p=0-F|0;r=G+8|0;o=n;do{l=yb(c[o>>2]|0,0,g)|0;if((l|0)==(g|0)){a[r>>0]=48;l=r}do if((o|0)==(n|0)){m=l+1|0;tb(b,l,1);if(s&(k|0)<1){l=m;break}tb(b,7140,1);l=m}else{if(l>>>0<=G>>>0)break;Ub(G|0,48,l+p|0)|0;do l=l+-1|0;while(l>>>0>G>>>0)}while(0);F=t-l|0;tb(b,l,(k|0)>(F|0)?F:k);k=k-F|0;o=o+4|0}while(o>>>0<v>>>0&(k|0)>-1)}zb(b,48,k+18|0,18,0);tb(b,w,E-w|0)}zb(b,32,f,j,h^8192)}while(0);R=H;return ((j|0)<(f|0)?f:j)|0}function Cb(a){a=+a;var b=0;g[h>>3]=a;b=c[h>>2]|0;y(c[h+4>>2]|0);return b|0}function Db(a,b){a=+a;b=b|0;return +(+Eb(a,b))}function Eb(a,b){a=+a;b=b|0;var d=0,e=0,f=0;g[h>>3]=a;d=c[h>>2]|0;e=c[h+4>>2]|0;f=Qb(d|0,e|0,52)|0;z()|0;switch(f&2047){case 0:{if(a!=0.0){a=+Eb(a*18446744073709551616.0,b);d=(c[b>>2]|0)+-64|0}else d=0;c[b>>2]=d;break}case 2047:break;default:{c[b>>2]=(f&2047)+-1022;c[h>>2]=d;c[h+4>>2]=e&-2146435073|1071644672;a=+g[h>>3]}}return +a}function Fb(b,d,e){b=b|0;d=d|0;e=e|0;do if(b){if(d>>>0<128){a[b>>0]=d;b=1;break}if(!(c[c[(Gb()|0)+188>>2]>>2]|0))if((d&-128|0)==57216){a[b>>0]=d;b=1;break}else{c[(gb()|0)>>2]=84;b=-1;break}if(d>>>0<2048){a[b>>0]=d>>>6|192;a[b+1>>0]=d&63|128;b=2;break}if(d>>>0<55296|(d&-8192|0)==57344){a[b>>0]=d>>>12|224;a[b+1>>0]=d>>>6&63|128;a[b+2>>0]=d&63|128;b=3;break}if((d+-65536|0)>>>0<1048576){a[b>>0]=d>>>18|240;a[b+1>>0]=d>>>12&63|128;a[b+2>>0]=d>>>6&63|128;a[b+3>>0]=d&63|128;b=4;break}else{c[(gb()|0)>>2]=84;b=-1;break}}else b=1;while(0);return b|0}function Gb(){return kb()|0}function Hb(a,b){a=a|0;b=b|0;var d=0,e=0;d=R;R=R+16|0;e=d;c[e>>2]=b;b=rb(c[950]|0,a,e)|0;R=d;return b|0}function Ib(a,b){a=a|0;b=b|0;return rb(c[950]|0,a,b)|0}function Jb(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=a&65535;e=b&65535;c=u(e,f)|0;d=a>>>16;a=(c>>>16)+(u(e,d)|0)|0;e=b>>>16;b=u(e,f)|0;return (y((a>>>16)+(u(e,d)|0)+(((a&65535)+b|0)>>>16)|0),a+b<<16|c&65535|0)|0}function Kb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=a;f=c;c=Jb(e,f)|0;a=z()|0;return (y((u(b,f)|0)+(u(d,e)|0)+a|a&0|0),c|0|0)|0}function Lb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;c=a+c>>>0;return (y(b+d+(c>>>0<a>>>0|0)>>>0|0),c|0)|0}function Mb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;d=b-d-(c>>>0>a>>>0|0)>>>0;return (y(d|0),a-c>>>0|0)|0}function Nb(a){a=a|0;return (a?31-(w(a^a-1)|0)|0:32)|0}function Ob(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;l=a;j=b;k=j;h=d;n=e;i=n;if(!k){g=(f|0)!=0;if(!i){if(g){c[f>>2]=(l>>>0)%(h>>>0);c[f+4>>2]=0}n=0;f=(l>>>0)/(h>>>0)>>>0;return (y(n|0),f)|0}else{if(!g){n=0;f=0;return (y(n|0),f)|0}c[f>>2]=a|0;c[f+4>>2]=b&0;n=0;f=0;return (y(n|0),f)|0}}g=(i|0)==0;do if(h){if(!g){g=(w(i|0)|0)-(w(k|0)|0)|0;if(g>>>0<=31){m=g+1|0;i=31-g|0;b=g-31>>31;h=m;a=l>>>(m>>>0)&b|k<<i;b=k>>>(m>>>0)&b;g=0;i=l<<i;break}if(!f){n=0;f=0;return (y(n|0),f)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;n=0;f=0;return (y(n|0),f)|0}g=h-1|0;if(g&h|0){i=(w(h|0)|0)+33-(w(k|0)|0)|0;p=64-i|0;m=32-i|0;j=m>>31;o=i-32|0;b=o>>31;h=i;a=m-1>>31&k>>>(o>>>0)|(k<<m|l>>>(i>>>0))&b;b=b&k>>>(i>>>0);g=l<<p&j;i=(k<<p|l>>>(o>>>0))&j|l<<m&i-33>>31;break}if(f|0){c[f>>2]=g&l;c[f+4>>2]=0}if((h|0)==1){o=j|b&0;p=a|0|0;return (y(o|0),p)|0}else{p=Nb(h|0)|0;o=k>>>(p>>>0)|0;p=k<<32-p|l>>>(p>>>0)|0;return (y(o|0),p)|0}}else{if(g){if(f|0){c[f>>2]=(k>>>0)%(h>>>0);c[f+4>>2]=0}o=0;p=(k>>>0)/(h>>>0)>>>0;return (y(o|0),p)|0}if(!l){if(f|0){c[f>>2]=0;c[f+4>>2]=(k>>>0)%(i>>>0)}o=0;p=(k>>>0)/(i>>>0)>>>0;return (y(o|0),p)|0}g=i-1|0;if(!(g&i)){if(f|0){c[f>>2]=a|0;c[f+4>>2]=g&k|b&0}o=0;p=k>>>((Nb(i|0)|0)>>>0);return (y(o|0),p)|0}g=(w(i|0)|0)-(w(k|0)|0)|0;if(g>>>0<=30){b=g+1|0;i=31-g|0;h=b;a=k<<i|l>>>(b>>>0);b=k>>>(b>>>0);g=0;i=l<<i;break}if(!f){o=0;p=0;return (y(o|0),p)|0}c[f>>2]=a|0;c[f+4>>2]=j|b&0;o=0;p=0;return (y(o|0),p)|0}while(0);if(!h){k=i;j=0;i=0}else{m=d|0|0;l=n|e&0;k=Lb(m|0,l|0,-1,-1)|0;d=z()|0;j=i;i=0;do{e=j;j=g>>>31|j<<1;g=i|g<<1;e=a<<1|e>>>31|0;n=a>>>31|b<<1|0;Mb(k|0,d|0,e|0,n|0)|0;p=z()|0;o=p>>31|((p|0)<0?-1:0)<<1;i=o&1;a=Mb(e|0,n|0,o&m|0,(((p|0)<0?-1:0)>>31|((p|0)<0?-1:0)<<1)&l|0)|0;b=z()|0;h=h-1|0}while((h|0)!=0);k=j;j=0}h=0;if(f|0){c[f>>2]=a;c[f+4>>2]=b}o=(g|0)>>>31|(k|h)<<1|(h<<1|g>>>31)&0|j;p=(g<<1|0>>>31)&-2|i;return (y(o|0),p)|0}function Pb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Ob(a,b,c,d,0)|0}function Qb(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){y(b>>>c|0);return a>>>c|(b&(1<<c)-1)<<32-c}y(0);return b>>>c-32|0}function Rb(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){y(b<<c|(a&(1<<c)-1<<32-c)>>>32-c|0);return a<<c}y(a<<c-32|0);return 0}function Sb(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0;if((e|0)>=8192){I(b|0,d|0,e|0)|0;return b|0}h=b|0;g=b+e|0;if((b&3)==(d&3)){while(b&3){if(!e)return h|0;a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}e=g&-4|0;f=e-64|0;while((b|0)<=(f|0)){c[b>>2]=c[d>>2];c[b+4>>2]=c[d+4>>2];c[b+8>>2]=c[d+8>>2];c[b+12>>2]=c[d+12>>2];c[b+16>>2]=c[d+16>>2];c[b+20>>2]=c[d+20>>2];c[b+24>>2]=c[d+24>>2];c[b+28>>2]=c[d+28>>2];c[b+32>>2]=c[d+32>>2];c[b+36>>2]=c[d+36>>2];c[b+40>>2]=c[d+40>>2];c[b+44>>2]=c[d+44>>2];c[b+48>>2]=c[d+48>>2];c[b+52>>2]=c[d+52>>2];c[b+56>>2]=c[d+56>>2];c[b+60>>2]=c[d+60>>2];b=b+64|0;d=d+64|0}while((b|0)<(e|0)){c[b>>2]=c[d>>2];b=b+4|0;d=d+4|0}}else{e=g-4|0;while((b|0)<(e|0)){a[b>>0]=a[d>>0]|0;a[b+1>>0]=a[d+1>>0]|0;a[b+2>>0]=a[d+2>>0]|0;a[b+3>>0]=a[d+3>>0]|0;b=b+4|0;d=d+4|0}}while((b|0)<(g|0)){a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0}return h|0}function Tb(b,c,d){b=b|0;c=c|0;d=d|0;var e=0;if((c|0)<(b|0)&(b|0)<(c+d|0)){e=b;c=c+d|0;b=b+d|0;while((d|0)>0){b=b-1|0;c=c-1|0;d=d-1|0;a[b>>0]=a[c>>0]|0}b=e}else Sb(b,c,d)|0;return b|0}function Ub(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;h=b+e|0;d=d&255;if((e|0)>=67){while(b&3){a[b>>0]=d;b=b+1|0}f=h&-4|0;i=d|d<<8|d<<16|d<<24;g=f-64|0;while((b|0)<=(g|0)){c[b>>2]=i;c[b+4>>2]=i;c[b+8>>2]=i;c[b+12>>2]=i;c[b+16>>2]=i;c[b+20>>2]=i;c[b+24>>2]=i;c[b+28>>2]=i;c[b+32>>2]=i;c[b+36>>2]=i;c[b+40>>2]=i;c[b+44>>2]=i;c[b+48>>2]=i;c[b+52>>2]=i;c[b+56>>2]=i;c[b+60>>2]=i;b=b+64|0}while((b|0)<(f|0)){c[b>>2]=i;b=b+4|0}}while((b|0)<(h|0)){a[b>>0]=d;b=b+1|0}return h-e|0}function Vb(a){a=a|0;var b=0,d=0;d=c[i>>2]|0;b=d+a|0;if((a|0)>0&(b|0)<(d|0)|(b|0)<0){P(b|0)|0;B(12);return -1}if((b|0)>(H()|0)){if(!(J(b|0)|0)){B(12);return -1}}else c[i>>2]=b;return d|0}function Wb(a,b){a=a|0;b=b|0;return U[a&3](b|0)|0}function Xb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return V[a&7](b|0,c|0,d|0)|0}function Yb(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;return W[a&1](b|0,c|0,d|0,e|0)|0}function Zb(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;X[a&3](b|0,c|0,d|0,e|0)}function _b(a){a=a|0;x(0);return 0}function $b(a,b,c){a=a|0;b=b|0;c=c|0;x(1);return 0}function ac(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;x(2);return 0}function bc(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;x(3)}

// EMSCRIPTEN_END_FUNCS
var U=[_b,na,cb,_b];var V=[$b,la,ib,eb,db,$b,$b,$b];var W=[ac,ma];var X=[bc,ka,za,bc];return{___muldi3:Kb,___udivdi3:Pb,_bitshift64Lshr:Qb,_bitshift64Shl:Rb,_free:Za,_i64Add:Lb,_i64Subtract:Mb,_malloc:Ya,_memcpy:Sb,_memmove:Tb,_memset:Ub,_ogv_demuxer_destroy:da,_ogv_demuxer_flush:ea,_ogv_demuxer_init:aa,_ogv_demuxer_keypoint_offset:ia,_ogv_demuxer_media_duration:ga,_ogv_demuxer_media_length:fa,_ogv_demuxer_process:ca,_ogv_demuxer_receive_input:ba,_ogv_demuxer_seek_to_keypoint:ja,_ogv_demuxer_seekable:ha,_sbrk:Vb,dynCall_ii:Wb,dynCall_iiii:Xb,dynCall_iiiii:Yb,dynCall_viiii:Zb,establishStackSpace:$,stackAlloc:Y,stackRestore:_,stackSave:Z}})


// EMSCRIPTEN_END_ASM
(oa,pa,buffer);a.___muldi3=S.___muldi3;a.___udivdi3=S.___udivdi3;a._bitshift64Lshr=S._bitshift64Lshr;a._bitshift64Shl=S._bitshift64Shl;a._free=S._free;a._i64Add=S._i64Add;a._i64Subtract=S._i64Subtract;a._malloc=S._malloc;a._memcpy=S._memcpy;a._memmove=S._memmove;a._memset=S._memset;a._ogv_demuxer_destroy=S._ogv_demuxer_destroy;a._ogv_demuxer_flush=S._ogv_demuxer_flush;a._ogv_demuxer_init=S._ogv_demuxer_init;a._ogv_demuxer_keypoint_offset=S._ogv_demuxer_keypoint_offset;
a._ogv_demuxer_media_duration=S._ogv_demuxer_media_duration;a._ogv_demuxer_media_length=S._ogv_demuxer_media_length;a._ogv_demuxer_process=S._ogv_demuxer_process;a._ogv_demuxer_receive_input=S._ogv_demuxer_receive_input;a._ogv_demuxer_seek_to_keypoint=S._ogv_demuxer_seek_to_keypoint;a._ogv_demuxer_seekable=S._ogv_demuxer_seekable;a._sbrk=S._sbrk;a.establishStackSpace=S.establishStackSpace;a.stackAlloc=S.stackAlloc;a.stackRestore=S.stackRestore;a.stackSave=S.stackSave;a.dynCall_ii=S.dynCall_ii;
a.dynCall_iiii=S.dynCall_iiii;a.dynCall_iiiii=S.dynCall_iiiii;a.dynCall_viiii=S.dynCall_viiii;a.asm=S;
if(M){if(String.prototype.startsWith?!M.startsWith(N):0!==M.indexOf(N)){var qa=M;M=a.locateFile?a.locateFile(qa,r):r+qa}if(p||q){var ra=a.readBinary(M);E.set(ra,8)}else{J++;a.monitorRunDependencies&&a.monitorRunDependencies(J);var T=function(b){b.byteLength&&(b=new Uint8Array(b));E.set(b,8);a.memoryInitializerRequest&&delete a.memoryInitializerRequest.response;J--;a.monitorRunDependencies&&a.monitorRunDependencies(J);0==J&&(null!==K&&(clearInterval(K),K=null),L&&(b=L,L=null,b()))},sa=function(){a.readAsync(M,
T,function(){throw"could not load memory initializer "+M;})},va=v(M);if(va)T(va.buffer);else if(a.memoryInitializerRequest){var wa=function(){var b=a.memoryInitializerRequest,c=b.response;if(200!==b.status&&0!==b.status)if(c=v(a.memoryInitializerRequestURL))c=c.buffer;else{console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+b.status+", retrying "+M);sa();return}T(c)};a.memoryInitializerRequest.response?setTimeout(wa,0):a.memoryInitializerRequest.addEventListener("load",
wa)}else sa()}}a.then=function(b){if(a.calledRun)b(a);else{var c=a.onRuntimeInitialized;a.onRuntimeInitialized=function(){c&&c();b(a)}}return a};function U(b){this.name="ExitStatus";this.message="Program terminated with exit("+b+")";this.status=b}U.prototype=Error();U.prototype.constructor=U;L=function xa(){a.calledRun||V();a.calledRun||(L=xa)};
function V(){function b(){if(!a.calledRun&&(a.calledRun=!0,!C)){ia||(ia=!0,H(ea));H(fa);if(a.onRuntimeInitialized)a.onRuntimeInitialized();if(a.postRun)for("function"==typeof a.postRun&&(a.postRun=[a.postRun]);a.postRun.length;){var b=a.postRun.shift();ha.unshift(b)}H(ha)}}if(!(0<J)){if(a.preRun)for("function"==typeof a.preRun&&(a.preRun=[a.preRun]);a.preRun.length;)ja();H(da);0<J||a.calledRun||(a.setStatus?(a.setStatus("Running..."),setTimeout(function(){setTimeout(function(){a.setStatus("")},1);
b()},1)):b())}}a.run=V;function x(b){if(a.onAbort)a.onAbort(b);void 0!==b?(z(b),A(b),b=JSON.stringify(b)):b="";C=!0;throw"abort("+b+"). Build with -s ASSERTIONS=1 for more info.";}a.abort=x;if(a.preInit)for("function"==typeof a.preInit&&(a.preInit=[a.preInit]);0<a.preInit.length;)a.preInit.pop()();a.noExitRuntime=!0;V();var W,X,Y;Y="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);
function Z(b){var c=Y();b=b();c=Y()-c;a.cpuTime+=c;return b}a.loadedMetadata=!1;a.videoCodec=null;a.audioCodec=null;a.duration=NaN;a.onseek=null;a.cpuTime=0;a.audioPackets=[];Object.defineProperty(a,"hasAudio",{get:function(){return a.loadedMetadata&&a.audioCodec}});Object.defineProperty(a,"audioReady",{get:function(){return 0<a.audioPackets.length}});Object.defineProperty(a,"audioTimestamp",{get:function(){return 0<a.audioPackets.length?a.audioPackets[0].timestamp:-1}});a.videoPackets=[];
Object.defineProperty(a,"hasVideo",{get:function(){return a.loadedMetadata&&a.videoCodec}});Object.defineProperty(a,"frameReady",{get:function(){return 0<a.videoPackets.length}});Object.defineProperty(a,"frameTimestamp",{get:function(){return 0<a.videoPackets.length?a.videoPackets[0].timestamp:-1}});Object.defineProperty(a,"keyframeTimestamp",{get:function(){return 0<a.videoPackets.length?a.videoPackets[0].keyframeTimestamp:-1}});
Object.defineProperty(a,"nextKeyframeTimestamp",{get:function(){for(var b=0;b<a.videoPackets.length;b++){var c=a.videoPackets[b];if(c.isKeyframe)return c.timestamp}return-1}});Object.defineProperty(a,"processing",{get:function(){return!1}});Object.defineProperty(a,"seekable",{get:function(){return!!a._ogv_demuxer_seekable()}});a.init=function(b){Z(function(){a._ogv_demuxer_init()});b()};
a.receiveInput=function(b,c){Z(function(){var c=b.byteLength;W&&X>=c||(W&&a._free(W),X=c,W=a._malloc(X));var e=W;a.HEAPU8.set(new Uint8Array(b),e);a._ogv_demuxer_receive_input(e,c)});c()};a.process=function(b){var c=Z(function(){return a._ogv_demuxer_process()});b(!!c)};a.dequeueVideoPacket=function(b){if(a.videoPackets.length){var c=a.videoPackets.shift().data;b(c)}else b(null)};a.dequeueAudioPacket=function(b){if(a.audioPackets.length){var c=a.audioPackets.shift();b(c.data,c.discardPadding)}else b(null)};
a.getKeypointOffset=function(b,c){var d=Z(function(){return a._ogv_demuxer_keypoint_offset(1E3*b)});c(d)};a.seekToKeypoint=function(b,c){var d=Z(function(){return a._ogv_demuxer_seek_to_keypoint(1E3*b)});d&&(a.audioPackets.splice(0,a.audioPackets.length),a.videoPackets.splice(0,a.videoPackets.length));c(!!d)};a.flush=function(b){Z(function(){a.audioPackets.splice(0,a.audioPackets.length);a.videoPackets.splice(0,a.videoPackets.length);a._ogv_demuxer_flush()});b()};a.close=function(){};



  return OGVDemuxerWebM
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
      module.exports = OGVDemuxerWebM;
    else if (typeof define === 'function' && define['amd'])
      define([], function() { return OGVDemuxerWebM; });
    else if (typeof exports === 'object')
      exports["OGVDemuxerWebM"] = OGVDemuxerWebM;
    