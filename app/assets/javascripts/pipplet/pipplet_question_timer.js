var Pipplet = Pipplet || {};

Pipplet.QuestionTimer = function (elt) {
  /*****************************************
   * Private attributes
   *****************************************/
  let timerElement = elt;
  let timerClock = null;
  let timerObject = null;
  let timerTime = null;
  let timeDuration = 180000;
  let stepValue = 0;
  let stepValueLeft = 0;

  /*****************************************
   * Private methods
   *****************************************/
  const onTimerEnd = function () {
    Pipplet.Test.currentQuestion.timeout();
  };

  /*****************************************
   * Public methods
   *****************************************/
  this.initialise = function () {
    timerElement = typeof timerElement !== 'undefined' ? timerElement : '#question_timer';
    timerClock = $(timerElement).find('#question_timer_clock');
    timerTime = $(timerElement).find('#question_timer_time');

    // Extract duration from meta tag
    if ($('meta[name=question_duration]').length > 0) {
      timeDuration = parseInt($('meta[name=question_duration]').attr('content'));
    } else {
      timeDuration = 180000;
    }
    stepValueLeft = timeDuration;

    timerObject = $(timerClock).circleProgress({
      value: 1,
      size: 40,
      startAngle: -Math.PI / 2,
      fill: {
        color: '#fff'
      },
      animation: false

    }).on('circle-animation-progress', function (_event, _progress, _stepValue) {
      stepValueLeft = timeDuration - timeDuration * _stepValue;
      stepValue = _stepValue;
      if (stepValueLeft <= 30000) {
        $(timerTime).addClass('critical');
      }
      $(timerTime).text(pippletHelper.millisecToMMSS(stepValueLeft));
    }).on('circle-animation-end', function (event) {
      if (stepValueLeft <= 0) {
        $(timerObject.circleProgress('widget')).stop();
        onTimerEnd();
      }
    });
  }; // initialise

  this.show = function () {
    $(timerElement).show();
  };

  this.hide = function () {
    $(timerElement).hide();
  };

  this.start = function () {
    timeDuration = Pipplet.Test.currentQuestion.getQuestionDuration();
    stepValueLeft = timeDuration;

    timerTime.removeClass('critical');
    $(timerObject.circleProgress('widget')).stop();
    timerObject.circleProgress({
      value: 1,
      animationStartValue: 0,
      animation: {
        duration: timeDuration,
        easing: 'linear'
      }
    });
  };

  this.stop = function () {
    $(timerObject.circleProgress('widget')).stop();
  };

  this.restart = function () {
    timerTime.removeClass('critical');
    $(timerObject.circleProgress('widget')).stop();
    timerObject.circleProgress({
      value: 1,
      animationStartValue: stepValue,
      animation: {
        duration: stepValueLeft,
        easing: 'linear'
      }
    });
  };

  this.getPercentageElapsed = function () {
    return timerObject.data('circle-progress').lastFrameValue;
  };
};
