// Place all the styles related to the questions controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
// Force change
// $pipplet-gray-dim: #e6e6e6;
$pipplet-gray-dim: #d6d6d6;
$pipplet-green: #98cf7c;
$pipplet-gray: #333;
$pipplet-medium-gray: #888;
$pipplet-gray-hard: #000000;
$pipplet-orange: #f15a24;
$pipplet-light-orange: #ff934b;
$pipplet-brand: #0084ff;
$pipplet-light-grey: #F7F7FC;

@font-face {
  font-family: 'Glyphicons Halflings';
  src: asset-url('glyphicons-halflings-regular.eot');
  src: asset-url('glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), asset-url('glyphicons-halflings-regular.woff') format('woff'), asset-url('glyphicons-halflings-regular.ttf') format('truetype'), asset-url('glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}

//Mic test arrow SVG
.arrow {
  fill: $pipplet-green;
}

// Mic animation
/* Chrome, Safari, Opera */
@-webkit-keyframes rec_flash {
  0% {
    border-color: white;
  }

  100% {
    border-color: rgba(255, 0, 0, 1);
  }
}

/* Standard syntax */
@keyframes rec_flash {
  0% {
    border-color: white;
  }

  100% {
    border-color: rgba(255, 0, 0, 1);
  }
}

// Timer animation
@keyframes timerAnimation {
  0% {
    opacity: 0.2;
  }

  40% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@-o-keyframes timerAnimation {
  0% {
    opacity: 0.2;
  }

  40% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@-moz-keyframes timerAnimation {
  0% {
    opacity: 0.2;
  }

  40% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@-webkit-keyframes timerAnimation {
  0% {
    opacity: 0.2;
  }

  40% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0.2;
  }
}

@media only screen and (min-width : 992px) {
  .right-background {
    position: fixed;
    top: 0;
    left: 50%;
    width: 50%;
    height: 100%;
    background-color: #F0F1F2;
    z-index: -1000;
  }
}

// Standard Nav Bar
.standard-pipplet-nav.test {
  #question_timer {
    margin-top: 15px;
    color: black;

    #question_timer_time {
      width: 80px;
      text-align: right;
      font-size: 30px;
    }

    #question_timer_clock {
      width: 40px;
      margin-right: 10px;
    }

    #question_timer_time.critical {
      -webkit-animation: timerAnimation 1s 30;
      -moz-animation: timerAnimation 1s 30;
      -o-animation: timerAnimation 1s 30;
      animation: timerAnimation 1s 30;
    }
  }
}


.question_controls {
  margin-top: 20px;

  .control-progress {
    margin-top: 20px;

    .progress {
      margin-bottom: 5px;
      height: 18px;
    }

    .progress-bar {
      background-color: #c4e7b2;
    }

    .progress-info {
      font-size: 14px;
    }
  }

  .control-buttons {
    margin-top: 20px;

    input {
      min-width: 100px;
    }
  }
}


.timepicker .input-group-addon {
  border: none;
  background-color: transparent;
}

h4 {
  margin-bottom: 15px;

  .title {
    color: $pipplet-gray;
  }
}

// Generic question element
.wait-audio {
  visibility: hidden;
}

// Reading question layout
.question-layout-basic.question-layout-reading {
  .questionable {
    h2 {
      font-family: "Helvetica Neue";
      font-size: 30px;
      text-transform: uppercase;
      font-weight: 600;
      color: #333;
    }

    h3 {
      font-family: "helvetica neue";
      font-size: 16px;
      color: #333;
      font-weight: 500;
      margin: 10px 0px;
      text-transform: uppercase;
    }

    p {
      font-family: "Droid Serif";
      font-size: 19px;
      color: #333;
      margin: 10px 0px;
    }

    a {
      color: #333;
    }
  }
}

ul.tutorial_list {
  list-style-type: none;
  padding: 0px;
}

// Standard question layout
.question-layout-basic {
  .questionable {
    //border-left: 4px solid $pipplet-gray-dim;
    margin-top: 20px;

    .instruction {
      h2 {
        font-size: 20px;
        color: $pipplet-gray;

        &.focus_title {

          margin: 5px 0;
        }
      }

      ul.focus_points {
        list-style-type: none;
        padding: 0px;

        li {
          h2 {
            color: $pipplet-gray;
            margin: 5px 0;
            font-size: 16px;
          }
        }
      }
    }

    &.validation_error {
      border-left: 3px solid $pipplet-orange;

      textarea {
        border-color: $pipplet-orange;
      }

    }

    &.scorable {
      padding-left: 15px;
      margin-bottom: 20px;
    }

    h1 {
      font-size: 18px;
      color: $pipplet-brand;

      table {
        color: $pipplet-gray;
        font-size: 15px;
      }
    }

    h2 {
      font-size: 20px;
      color: $pipplet-gray;
    }

    p {
      font-size: 15px;
      color: $pipplet-gray;
      font-weight: normal;
    }


    // General question elements properties
    textarea {
      width: 100%;
    }

    img {
      max-width: 100%;
      border-radius: 3px;
      border: 1px solid #ccc;
    }

    .text-muted.credit {
      font-size: 12px;
      font-weight: normal;

      a {
        color: #999;
      }
    }

    .checkbox {
      margin-right: 10px;
      padding-top: 0;
    }

    .radio {
      margin-right: 10px;
      padding-top: 0;
      max-width: 100%;
    }

    .radio_word_selected {
      border-bottom: 2px solid $pipplet-orange;
    }

    // Properties depending on the static or dynamic status
    .static-elements {
      img.selected {
        border: 6px solid $pipplet-orange;
      }

      input[type="radio"]:checked+span {
        color: #5BC1DE;
      }

      input[type="radio"]:checked+img {
        border: 1px solid $pipplet-orange;
      }

      input[type="radio"]:checked+img {
        border: 1px solid $pipplet-orange;
      }
    }

    .dynamic-elements {
      img {
        //border: 1px solid #666666;
      }

      img:hover {
        border: 1px solid $pipplet-orange;
      }

      img.selected {
        border: 2px solid $pipplet-orange;
      }
    }

    .play {
      background-image: asset-data-url('speaker_off.svg');
      background-size: 100px 100px;
      background-repeat: no-repeat;
      width: 100px;
      height: 100px;
      border-radius: 100px;
      border: none;

      &:hover,
      .playing {
        background-image: asset-data-url('speaker_on.svg');
      }
    }


    .recorder {
      margin-top: 20px;
      height: 100px;

      .button {
        .record {
          background-image: asset-data-url('microphone_off.svg');
          background-size: 100px 100px;
          background-repeat: no-repeat;
          width: 100px;
          height: 100px;
          border-radius: 100px;
          border: none;

          &:hover {
            background-image: asset-data-url('microphone_on.svg');
          }

          &.recording {
            background-image: asset-data-url('microphone_active.svg');
          }

          &.recorded {
            background-image: asset-data-url('microphone_full.svg');
          }
        }
      }

      #recorder_info {
        width: 100%;
        padding-top: 28px;
        padding-left: 115px;
        color: $pipplet-medium-gray;

        .recorder_duration {
          font-size: 15px;

          &.time_is_good {
            color: $pipplet-green;
          }
        }
      }

      .check {
        color: #88c355;
        margin-right: 5px;
      }
    }

    .text-count-wrapper {
      font-size: 14px;
      margin-top: 5px;

      &.under_limit::before {
        content: "✗ ";
      }

      &.over-limit {
        color: green;
      }

      &.over-limit::before {
        content: "✔ ";
      }
    }

    .question-audio-player {
      width: 100%;
    }

  }


  .main {
    margin-top: 0 !important;
    padding: 0 15px !important;
  }

  .main .text-frame {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 3px;
  }

  /*  .question_choices {
    font-size: 150%;
    li.selected span {
      font-size: 180%;
      color: #FF7963;
    }
    li img {
      border: 1px solid #666666;
    }
    li img:hover {
      border: 1px solid $pipplet-orange;
    }
    li.selected img {
      font-size: 100%;
      border: 6px solid $pipplet-orange;
    }
    li input[type="radio"]:checked + span {
      color: #5BC1DE;
    }
    li input[type="radio"]:checked + img {
      border: 1px solid $pipplet-orange;
    }
    li input[type="radio"]:checked + img {
      border: 1px solid $pipplet-orange;
    }
  }*/

}

// End standard question layout


// Specific answer button
.answer-button {
  margin-top: 20px;
  float: none;


  .button {
    margin-top: 10px;

    input {
      width: 100%;
    }
  }
}

// Audio elements
// Player and recorder elements
.recording_player {
  background-image: asset-data-url('speaker_off.svg');
  background-repeat: no-repeat;
  width: 100px;
  height: 100px;
  border-radius: 50px;
  border-style: solid;
  border-color: transparent;

  &:hover,
  .playing {
    background-image: asset-data-url('speaker_on.svg');
  }

  &:focus,
  &:active {
    border-color: transparent;
    background-color: #0084ff;
    outline: none;
  }
}


.recording_recorder {
  background-image: asset-data-url('microphone_off.svg');
  background-repeat: no-repeat;
  width: 100px;
  height: 100px;
  border-radius: 50px;
  border-style: solid;
  border-color: transparent;
  padding-top: 0px;
  padding-bottom: 0px;

  &:hover {
    background-image: asset-data-url('microphone_on.svg');
  }

  &:focus,
  &:active {
    border-color: transparent;
    background-color: #0084ff;
    outline: none;
  }

  &.recording {
    box-shadow: inset 0px 0px 0px 2px;
    background-image: asset-data-url('microphone_active.svg');
    border-width: 2px;
    -webkit-animation-name: rec_flash;
    /* Chrome, Safari, Opera */
    -webkit-animation-duration: 1s;
    /* Chrome, Safari, Opera */
    -webkit-animation-iteration-count: infinite;
    /* Chrome, Safari, Opera */
    -webkit-animation-direction: alternate;
    -webkit-animation-timing-function: linear;
    /* Chrome, Safari, Opera */
    animation-name: rec_flash;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-timing-function: linear;
  }

  &.recorded {
    background-image: asset-data-url('microphone_full.svg');
  }
}



.audio {
  margin: 20px auto;
  float: none;

  input {
    width: 100%;
  }
}


// Audio Test
#bad_audio_alert {
  margin-left: 20px;
}

.audio-test-content {
  #launch_test_button {
    .btn {
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }

  .jumbotron-mic {
    margin-top: 40px;
    padding: 20px 50px;
    font-size: 20px;

  }

  .check-voice {
    margin-left: 20px;
  }

  .row-check-audio {
    padding: 15px 0;
  }

  #welcome_content {
    margin-top: 100px;
  }

  #audio_player {
    audio {
      display: none;
    }
  }
}

.tutorial {
  .question_content {
    height: auto;
    min-height: 200px;

    #question_data_sample {
      border: 1px solid $pipplet-gray;
    }
  }
}

// Inter question
#inter_question {
  height: 100%;
  position: relative;

  #countdown {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 40px;
    color: $pipplet-gray;
  }
}


// Admin footer
#admin_footer {
  //visibility: hidden;
  //position: absolute;
  //bottom: 0;
  //width: 100%;
  text-align: left;
  color: black;
  height: 100px;
  background-color: #f5f5f5;
  padding: 10px 10px;
  font-family: "Courier New", Courier, monospace;
  border: none;
  line-height: 14px;
}


.question_error {
  border-left: 3px solid #F15A24;
  margin-left: -23px;
  padding-left: 20px;
}

// Showcase footer
#showcase_footer {
  text-align: left;
  color: black;
  height: 105px;
  background-color: #f5f5f5;
  padding: 20px 10px;
  border-top: 1px solid black;
  line-height: 14px;

  .form-control {
    background-color: #f5f5f5;
  }

  .response {
    font-size: 0.75em;
  }
}


/*
 * State machine statuses
 */
/* --------------------------------

Test-onboarding-breadcrumbs

-------------------------------- */

.test-onboarding-breadcrumbs {
  margin-top: 1em;
  padding: 0.5em 1em;
  border-radius: .25em;
  box-shadow: 0px 4px 8px rgba(0, 26, 51, 0.05);
}

.test-onboarding-breadcrumbs:after {
  content: "";
  display: table;
  clear: both;
}

.test-onboarding-breadcrumbs li {
  display: inline-block;
  float: left;
  margin: 0.5em 0;
}

.test-onboarding-breadcrumbs li::after {
  /* this is the separator between items */
  display: inline-block;
  // content: '\00bb';
  content: '\279F';
  margin: 0 .6em;
  color: $pipplet-gray-dim;
}

.test-onboarding-breadcrumbs li:last-of-type::after {
  /* hide separator after the last item */
  display: none;
}

.test-onboarding-breadcrumbs li>* {
  /* single step */
  display: inline-block;
  color: #888;
  text-decoration: none;
}

.test-onboarding-breadcrumbs li.current>* {
  /* selected step */
  color: $pipplet-green;
}

@media only screen and (min-width: 768px) {
  .test-onboarding-breadcrumbs {
    background-color: white;
    margin: 0 auto 1em auto;
    padding: 0;
    text-align: center;
  }

  .test-onboarding-breadcrumbs li {
    position: relative;
    float: none;
    margin: 0.4em 30px 0.4em 0;
  }

  .test-onboarding-breadcrumbs li::after {
    margin: 0 1em;
  }

  .test-onboarding-breadcrumbs li>* {}

  .test-onboarding-breadcrumbs li:last-of-type,
  .test-onboarding-breadcrumbs li:last-of-type>* {
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  .test-onboarding-breadcrumbs li:first-of-type,
  .test-onboarding-breadcrumbs li:first-of-type>* {
    margin-left: 0 !important;
    padding-left: 0 !important;
  }

  .test-onboarding-breadcrumbs li::after {
    /* this is the line connecting 2 adjacent items */
    position: absolute;
    content: '';
    height: 2px;
    background: $pipplet-gray-dim;
    /* reset style */
    margin: 0;
    max-width: 35px;
  }

  .test-onboarding-breadcrumbs li.current::after {
    background: $pipplet-gray-dim;

  }

  .test-onboarding-breadcrumbs li>*,
  .test-onboarding-breadcrumbs li.current>* {
    position: relative;
    color: $pipplet-gray;
  }

  .test-onboarding-breadcrumbs.text-center li::after {
    width: 100%;
    top: 50%;
    left: 100%;
    -webkit-transform: translateY(-50%) translateX(-1px);
    -moz-transform: translateY(-50%) translateX(-1px);
    -ms-transform: translateY(-50%) translateX(-1px);
    -o-transform: translateY(-50%) translateX(-1px);
    transform: translateY(-50%) translateX(-1px);
  }

  .test-onboarding-breadcrumbs.text-center li>* {
    z-index: 1;
    padding: .6em 1em;
    border-radius: .25em;
    color: $pipplet-medium-gray;
  }

  .test-onboarding-breadcrumbs.text-center li.current>*,
  .test-onboarding-breadcrumbs.text-center li.visited>* {

    color: $pipplet-brand;

  }

}

.support-tips {
  background-color: #D9E9FF;
  padding: 10px 15px;
}

.unselectable {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.speech-bubble {
  padding: 10px 15px;
  width: 50%;
  position: relative;
  border-radius: .4em;
  box-shadow: 0 2px 1px rgba(0, 0, 0, .13);

  &.big {
    width: 85%;
  }

  &:after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 15px solid transparent;
  }

  &.left {
    background: #ebebeb;

    &:after {
      left: 0;
      top: 0;
      border-right-color: #ebebeb;
      border-left: 0;
      border-top: 0;
      margin-top: 0px;
      margin-left: -10px;
    }
  }

  &.right {
    background: #dcf8c6;
    margin-left: 40%;

    &:after {
      right: 0;
      top: 0;
      border-left-color: #dcf8c6;
      border-right: 0;
      border-top: 0;
      margin-top: 0px;
      margin-right: -10px;
    }
  }


}

.skip-question-row {
  width: 100%;
  position: fixed;
  height: 30px;
  bottom: 0;
  left: 0;

  #skip-question-link {
    position: absolute;
    right: 30px;
    bottom: 30px;
  }

  #skip-question-link-cam {
    color: $pipplet-brand;
    position: absolute;
    right: 30px;
    bottom: 30px;
    margin-right: 85px;
  }
}


// Modals

#skip_question_modal {
  z-index: 2000;

  .modal-content {
    padding: 0;
  }

  .modal-header {
    .close {
      color: #333;

      &:hover {
        color: black;
      }
    }
  }

  .modal-body {}

  .modal-footer {}
}

#leaving_test {
  z-index: 2001;
  .modal-buttons {
    button:first-child {
      float: left;
    }
    button:last-child {
      float: right;
    }
  }
}
