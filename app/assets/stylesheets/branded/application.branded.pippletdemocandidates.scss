@media (min-width: 768px) {
  body.branded.pippletdemocandidates {
    .navbar.standard-pipplet-nav .navbar-header:after{
      content: "Test Center Preview";
      clear: none;
      border-left: 1px solid white;
      padding-left: 20px;
      margin-top: 15px;
      line-height: 40px;
      font-size: 18px;
      font-weight: 300;
    }

    .navbar.standard-pipplet-nav .navbar-header{
       width: 400px;
    }  

    .navbar.standard-pipplet-nav .navbar-header img{
       width: 110px;
      margin-left: 10px;
    }
    
    /* The ribbon */

    .navbar:after{
        content: "Demo";
        width: 200px;
        background: #e43;
        position: absolute;
        text-align: center;
        line-height: 50px;
        letter-spacing: 1px;
        color: white;
        position: fixed;
        /* shadow */
        box-shadow: 0 0 3px rgba(0,0,0,.3);
        /* color */
        background: #23d85a;
        /* position */
        top: 25px;
        left: -50px;
        transform: rotate(-45deg);
        -webkit-transform: rotate(-45deg);
            
    }

  }
}