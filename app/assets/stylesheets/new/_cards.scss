.card {
  @include outline-default;

  height: fit-content;
  max-width: 750px;
  min-width: 225px;
  padding: 0 $grid-space-4;
  margin-bottom: $grid-space-4;

  &.primary {
    @include outline-primary;
  }

  .option {
    border-top: 1px solid $very-light-grey;
    padding: $grid-space-3 0;

    span.entitled-text {
      font-size: 17px;
      font-weight: 600;
      margin-bottom: $grid-space-1;
    }
  }

  &.small {
    max-width: 270px !important;
  }

  .step-icon {
    height: 20px;
    max-width: 25px;
    margin-right: $grid-space-1;
  }

  &.simple {
    width: 100%;
    text-align: left;
    border: none;
    padding: $grid-space-4;
    box-shadow: 0px 4px 8px rgba(0, 26, 51, 0.05);

    &.fit {
      width: 100%;
    }
  }
}

.instruction-card {
  @include outline-default;

  display: flex;
  flex-direction: column;
  padding: $grid-space-3;
  width: 200px;
  min-height: 220px;
  text-align: center;
  box-shadow: 0px 4px 8px rgba(0, 26, 51, 0.05);
  border: none;

  img {
    align-self: center;
    height: 80px;
    width: 80px;
  }

  h3, h4 {
    text-align: center;
    margin: $grid-space-3 0 $grid-space-1 0;
  }
}
