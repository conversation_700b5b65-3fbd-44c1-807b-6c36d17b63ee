#pdf_certificate.pdf-certificate-ai {
  header {
    #title > div {
      font-size: 22px;
    }

    #logo > img {
      width: 180px;
    }

    #header-logo-ets {
      position: absolute;
      right: 40px;
      top: 62px;
    }

    #user_detail {
      width: 70%;
      font-size: 16px;
    }

    #certificate-laurel-background {
      background-image: asset-data-url('report-decoration.png');
      background-position: top;
      background-repeat: no-repeat;
      background-size: cover;
      width: 200px;
      height: 164px;
      font-weight: normal;
      line-height: 134px;
      padding-left: 50px;
      padding-right: 50px;
      font-size: 30px;

      > span {
        display: block;
        line-height: 24px;
        padding-top: 52px;
        font-size: 22px;
      }
    }

    #user_score > div {
      &:first-child {
        font-weight: bold;
        font-size: 18px;

        .align-items-user {
          padding-right: 20px;
        }
      }

      .align-items-user-right {
        min-width: 200px;
        text-align: right;
      }

      .align-items-user-center {
        min-width: 200px;
        text-align: center;
        font-size: 18px;
      }

      .flex-row-dir > p + .with-width {
        width: 65px;
      }
    }
  }

  main {
    .flex-row-dir > p + .with-width {
      width: 65px;
    }

    .double_progress {
      position: relative;
      background-color: #eee;
      width: 70%;
      height: 20px;
    }

    .double_progress div:first-child {
      position: absolute;
      z-index: 9999;
      top: 0;
      left: 0;
      background-color: #3284ff;
      height: 20px;
    }

    .double_progress div:last-child {
      position: absolute;
      top: 0;
      left: 0;
      background-color: #84c1f9;
      height: 20px;
    }

    .progress-meter {
      width: 70%;
    }
  }
}
