class ExaminerMailer < ApplicationMailer
  default from: "team@#{ENV.fetch('SMTP_DOMAIN', nil)}"
  default bcc: Rails.env.production? ? '<EMAIL>' : '<EMAIL>'

  # ----------------------------------------
  # :section: This mailer is for emails sent to Examiners
  # ----------------------------------------

  # Send invitations & reminders to users
  def new_test_to_assess(evaluation_id)
    return unless (@evaluation = MailerDeliveryHelper.find_resource_and_verify_associations(resource_id: evaluation_id, resource_class: Evaluation, associated_resources: %i[test_instance examiner]))

    @resource = @evaluation.test_instance

    if /linguistics\+adminexaminer.*@pipplet\.com/.match(@evaluation.examiner.email)
      @examiners = Examiner.real.where(language: @resource.test_language).order(:next_unavailable_end_date, rank: :asc)
      mail(to: '<EMAIL>',
           subject: subject_prefix + "[Pipplet][ti:#{@resource.id}] No examiner available for #{@resource.test_language}" + subject_suffix) do |format|
        format.html { render 'no_examiner_available' }
      end

    else
      mail(to: examiner_email(@evaluation.examiner),
           subject: subject_prefix + "[Pipplet][ti:#{@resource.id}] New test to assess" + subject_suffix) do |format|
        format.html { render 'new_test_to_assess' }
      end

    end
  end

  def changed_max_assessment_time(examiner, old_max_assessment_time)
    @examiner = examiner
    destination = Rails.env.production? ? '<EMAIL>' : "linguistics+maxevalchange+#{Rails.env}@pipplet.com"
    mail(to: destination, subject: subject_prefix + "[#{examiner.language}] examiner [#{examiner.user.full_name}] changed their max assessment time from #{old_max_assessment_time} min to #{examiner.max_assessment_time} min") do |format|
      format.html { render 'changed_max_assessment_time' }
    end
  end

  def changed_unavailable_dates(examiner)
    @examiner = examiner
    destination = Rails.env.production? ? '<EMAIL>' : "linguistics+unavailable+#{Rails.env}@pipplet.com"
    mail(to: destination, subject: subject_prefix + "[#{examiner.language}] examiner [#{examiner.user.full_name}] changed their unavailablity") do |format|
      format.html { render 'changed_unavailable_dates' }
    end
    same_lang_examiners = Examiner.usable_status.where(language: examiner.language)
    unavailable_examiners_count = same_lang_examiners.where('(next_unavailable_start_date <= ?) AND (next_unavailable_end_date >= ?)', examiner.next_unavailable_end_date, examiner.next_unavailable_start_date).count

    return unless same_lang_examiners.count == unavailable_examiners_count

    mail(to: destination, subject: subject_prefix + "[#{examiner.language}] No examiners available after [#{examiner.user.full_name}] changed their unavailablity") do |format|
      format.html { render 'examiners_unavailable' }
    end
  end

  # Send soon overdue eval
  def evaluation_soon_overdue(evaluation)
    @resource = evaluation.test_instance
    @evaluation = evaluation

    mail(to: examiner_email(@evaluation.examiner),
         subject: "Test ##{@resource.id} is soon overdue") do |format|
      format.html { render 'evaluation_soon_overdue' }
    end
  end

  # Send overdue eval
  def evaluation_overdue(evaluation)
    @resource = evaluation.test_instance
    @evaluation = evaluation

    mail(to: examiner_email(@evaluation.examiner),
         subject: "Important: Test ##{@resource.id} is overdue") do |format|
      format.html { render 'evaluation_overdue' }
    end
  end

  def evaluation_canceled(evaluation)
    @resource = evaluation.test_instance
    @evaluation = evaluation

    mail(to: examiner_email(@evaluation.examiner),
         subject: subject_prefix + "[Pipplet][ti:#{@resource.id}] Test Cancelled" + subject_suffix) do |format|
      format.html { render 'evaluation_canceled' }
    end
  end

  def evaluation_reassess(evaluation)
    @resource = evaluation.test_instance
    @evaluation = evaluation

    mail(to: examiner_email(@evaluation.examiner),
         subject: subject_prefix + "[Pipplet][ti:#{@resource.id}] Test to review" + subject_suffix) do |format|
      format.html { render 'evaluation_reassess' }
    end
  end

  def new_evaluations_presented_for_review(examiner, evaluations)
    @examiner = examiner
    @evaluations = evaluations

    mail(
      to: examiner_email(@examiner),
      subject: '[Pipplet] New evaluations in your review list',
      bcc: []
    ) do |format|
      format.html { render 'new_evaluations_presented_for_review' }
    end
  end

  def evaluation_received(evaluation)
    @resource = evaluation.test_instance
    @evaluation = evaluation

    mail(to: examiner_email(@evaluation.examiner),
         subject: "[Pipplet][ti:#{@resource.id}] Your assessment has been submitted.") do |format|
      format.html { render 'evaluation_received' }
    end
  end

  def set_password(examiner_id)
    @resource = Examiner.find_by(id: examiner_id)
    return unless @resource

    mail(to: examiner_email(@resource),
         subject: 'Set your Pipplet examiner password') do |format|
      format.html { render 'set_password' }
    end
  end

  def examiner_role(examiner)
    @resource = examiner
    mail(to: examiner_email(@resource),
         subject: 'Examiner role') do |format|
      format.html { render 'examiner_role' }
    end
  end

  # ----------------------------------------
  # :section: Email helpers
  # ----------------------------------------
  private

  def subject_suffix
    " ##{object_id}"
  end

  def subject_prefix
    if Rails.env.production?
      "[PT-#{ENV['RAILS_ENV'].upcase}]"
    else
      "[PT-#{ENV.fetch('HEROKU_APP_NAME', '').sub('pipplet-', '').upcase}]"
    end
  end
end
