module HasIntegration
  extend  ActiveSupport::Concern
  include IntegrationsHelper
  include LocaleManager

  INTEGRATION_NAMES = %i[lever teamtailor jobylon smartrecruiters workable factorial jobvite].freeze

  included do
    class << self
      attr_accessor :integration_name, :integration_namespace
    end

    # Api::<integration_name>::<controller_name>
    integration_name = self.module_parent.to_s.demodulize.underscore.to_sym

    self.integration_name = integration_name
    self.integration_namespace = Integrations.const_get(integration_name.to_s.camelize)

    skip_before_action :verify_authenticity_token
    before_action :authenticate_api_user
    before_action :define_test_instance, only: :show
  end

  def index
    render json: available_tests, each_serializer: self.class.integration_namespace::TestSerializer, adapter: self.class.integration_namespace::TestSerializer.adapter, status: 200
  end

  def show
    render json: @test_instance, serializer: self.class.integration_namespace::TestSerializer, status: 200
  end

  def create
    params = self.class.integration_namespace::TestInstanceRequest.new(test_instance_request_params, api_user: @api_user)
    unless params.valid?
      Alert.integrations('Invalid params while creating order', params.errors.full_messages.to_sentence, integration_name: self.class.integration_name, handled: true)
      invalid_params_callback(message: params.errors.full_messages.to_sentence, params: params)
      render(api_order_rejected_response(json: { error: params.errors.full_messages.to_sentence })).then { return }
    end
    begin
      api_order, user, test_instance = fulfill_order(@api_user, params)
    rescue ArgumentError, ActiveRecord::RecordInvalid => e
      Alert.integrations('Unhandled error while creating order', e.message, integration_name: self.integration_name, handled: false)
      invalid_params_callback(message: e.message, params: params)
      render(api_order_rejected_response).then { return }
    end
    if user&.valid? && test_instance&.valid?
      accept_order api_order
      render(api_order_created_response(params: { test_instance: test_instance })).then { return }
    else
      reject_order api_order, test_instance
      render(api_order_rejected_response).then { return }
    end
  end

  private

  def invalid_params_callback(message: nil, params: nil); end

  # method to override in the controller
  def define_test_instance
    render status: :not_found, json: { error: 'Integration not implemented' }
  end

  # method to override in the controller
  def authenticate_api_user
    render status: :not_found, json: { error: 'Integration not implemented' }
  end

  def test_instance_request_params
    []
  end

  def api_order_created_response(json: {}, params: {})
    { status: :created, json: }
  end

  def api_order_rejected_response(json: {}, params: {})
    { status: :bad_request, json: }
  end

  def api_order_response(status:, json: {})
    { status:, json: }
  end
end
