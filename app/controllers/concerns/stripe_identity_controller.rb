module StripeIdentityController
  extend ActiveSupport::Concern

  # ----------------------------------------
  # :section: Managing Identity Verification
  # ----------------------------------------
  public

  def create_stripe_verification_session
    Stripe.api_key = ENV.fetch('STRIPE_API_KEY', nil)
    Stripe.api_version = ENV.fetch('STRIPE_API_VERSION', nil)

    return Stripe::Identity::VerificationSession.create({
                                                          type: 'document',
                                                          metadata: {
                                                            user_id: current_user.id
                                                          },
                                                          options: {
                                                            document: {
                                                              require_matching_selfie: true
                                                            }
                                                          },
                                                          return_url: identified_url
                                                        })
  end

  def retrieve_verification_session
    Stripe.api_key = ENV.fetch('STRIPE_API_KEY', nil)
    Stripe.api_version = ENV.fetch('STRIPE_API_VERSION', nil)

    Stripe::Identity::VerificationSession.retrieve(current_user.stripe_verification_session_id)
  end

  def get_stripe_verification_session
    if current_user.stripe_verification_session_id.nil? || current_user.identity_status == "verification_failed"
      result = create_stripe_verification_session
      current_user.update(stripe_verification_session_id: result.id)
      current_user.identity_set_to_unverified!
    else
      result = retrieve_verification_session
      launch_identity_validation unless current_user.identity_process_taken?
    end
    result
  end

  def launch_identity_validation
    current_user.identity_set_to_pending!
    IdentityJobs::StripeValidationReportJob.perform_later(current_user.id)
  end
end
