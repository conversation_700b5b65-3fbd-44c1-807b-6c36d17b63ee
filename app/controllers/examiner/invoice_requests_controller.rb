class Examiner::InvoiceRequestsController < Examiner::ExaminerController
  before_action :select_fields
  before_action :check_ownership

  def show
    unless current_user.is_admin? || current_examiner.billing_info_complet?
      redirect_to my_account_path(redirection_url: examiner_invoice_request_url)
      flash[:warning] = 'Please enter your billing information before attempting to access the invoice request page'
      return false
    end

    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def upload_invoice
    if invoice_request_params[:invoice_pdf].blank?
      message = { type: 'alert', text: 'PDF invoice missing' }
    elsif invoice_request_params[:requested_amount].blank?
      message = { type: 'alert', text: 'Requested amount missing' }
    elsif @invoice_request.update(invoice_request_params)
      @invoice_request.receive_invoice!('invoice_received_at')
      @invoice_request.try_auto_validation
      message = { type: 'notice', text: 'Your invoice has been uploaded!' }
    else
      message = { type: 'alert', text: @invoice_request.errors.full_messages.to_sentence }
    end

    redirect_to examiner_invoice_request_url(@invoice_request.id), message[:type].to_sym => message[:text]
  end

  private

  def select_fields
    @invoice_request = if current_user.is_admin?
                         InvoiceRequest.find_by(id: params[:id])
                       else
                         current_examiner.invoice_requests.find_by(id: params[:id])
                       end
  end

  def check_ownership
    redirect_to '/' if !current_user.is_admin? && (@invoice_request.examiner.id != current_examiner.id)
  end

  def invoice_request_params
    params.permit(:requested_amount, :invoice_currency, :vat_amount, :examiner_invoice_reference,
                  :requested_amount_evaluations, :requested_amount_other_services, :invoice_pdf)
  end
end
