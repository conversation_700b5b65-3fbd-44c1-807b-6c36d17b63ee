class Examiner::EvaluationsController < Examiner::ExaminerController
  def index
    @assessment_type_simple = AssessmentType.find_by(name: 'simple')
    @assessment_type_detailed = AssessmentType.find_by(name: 'detailed')
    @evaluations = current_examiner.evaluations.ongoing
    @evaluations_to_assess = @evaluations.where(urgent: true).sort_by(&:getRatio).reverse!
    @evaluations_to_assess += @evaluations.where(urgent: false).sort_by(&:getRatio).reverse!
    @evaluations_assessed = current_examiner.evaluations.completed.where('assessed_at > ?', 2.days.ago)
    @beta = current_examiner.beta_features

    @eval_assessed = params[:eval_assessed] == 'true'
    flash.now[:success] = 'We successfully received your assessment' if @eval_assessed
    flash.now[:warning] = 'You successfully reported this evaluation' if params[:reported] == 'true'
  end

  def mark_evaluation_as_reviewed
    evaluation = get_evaluation(params[:id])
    if evaluation.update(presented_for_review: false)
      flash[:success] = 'Your evaluation was marked as reviewed'
    else
      flash[:error] = 'An error occured, marked as reviewed failed'
    end
    redirect_to evaluations_presented_for_review_examiner_evaluations_url
    nil
  end

  def evaluations_presented_for_review
    examiner_id = params[:examiner_id]

    if current_user.is_admin?
      @selected_examiner = Examiner.find_by(id: examiner_id) || Examiner.first_for(:en)
      @examiners = []

      AVAILABLE_TEST_LANGUAGES.each do |lang|
        @examiners += Examiner.where(language: lang).real.order(:rank)
      end
      @evaluations = @selected_examiner.evaluations.not_canceled

    else
      @evaluations = current_examiner.evaluations.not_canceled
    end

    @evaluations = @evaluations.includes(:examiner, test_instance: :average_grades).where(presented_for_review_at: 2.weeks.ago..).order(presented_for_review_at: :desc)
  end

  def show
    @evaluation = get_evaluation(params[:id])
    @only_show = params[:only_show] == 'true'

    if @evaluation.nil?
      flash.now[:warning] = 'You do not have access to this test anymore'
      redirect_to examiner_evaluations_path
      return
    end

    @test_language = @evaluation.test_instance.test_language
    @comments = @evaluation.test_instance&.client_config&.comments(@test_language)
    @beta = params[:beta] == 'true'
    @test_profile_description = @evaluation.test_instance.test_profile.examiner_description

    id_ti_graded_for_user = @evaluation.test_instance.user.test_instances.graded.where(test_language: @test_language).select(:id)
    @previous_evaluation = Evaluation.delivered.where(test_instance_id: id_ti_graded_for_user).where(examiner_id: current_examiner.id).last if current_examiner
    # TODO: refactor
    if @previous_evaluation && (@previous_evaluation.certificate.nil? || @previous_evaluation.certificate.grades.empty?)
      message = "Evaluation : #{@previous_evaluation.id}"
      Alert.system('Certificate or grades are nil ', message)
    end
  end

  def get_assessment_json
    @evaluation = get_evaluation(params[:id])
    @evaluation.update(start_assessment_at: Time.now) if @evaluation.assigned?

    ## productions = ti.ordered_completed_productions
    productions = @evaluation.test_instance.ordered_completed_productions
    @test_language = @evaluation.test_instance.test_language

    assessment_type = AssessmentType.find_by(id: @evaluation.assessment_type.id)
    assessment_type_json = assessment_type.json_data

    json_survey = { pages: [questions: []] }

    assessment_questions_json_data = AssessmentQuestion.where(id: assessment_type.assessment_question_ids).pluck(:id, :json_data).to_h
    assessment_type_json['questions'].each_with_index do |q, _i|
      q['questionablesToDisplay'].each do |question_index|
        json_survey[:pages][0][:questions].push(get_production_html(question_index, @evaluation, productions))
      end
      q['assessmentQuestions'].each_with_index do |aq, _aqi|
        assessment_question = get_assessment_question(aq, @evaluation, q, assessment_questions_json_data)
        json_survey[:pages][0][:questions].push(assessment_question)
      end
    end

    render(json: json_survey, status: :ok)
  end

  def get_survey_response
    evaluation = get_evaluation(params[:id])
    if evaluation
      render(json: evaluation.json_assessment, status: :ok)
    else
      render(json: {}, status: :not_found)
    end
  end

  def submit_assessment
    evaluation = get_evaluation(params[:id])

    if evaluation&.may_assess? && !evaluation&.assessed?
      evaluation.assess!('assessed_at')
      EventLog.add(evaluation, :process, "Evaluation #{evaluation.id} has been submitted by examiner and assessed", :info)
      GenerateEvaluationCertificateJob.perform_later(evaluation.id) if evaluation.can_be_delivered?
    end

    if current_user.is_admin?
      redirect_to edit_admin_evaluation_path(evaluation)
    else
      redirect_to examiner_evaluations_path(eval_assessed: true)
    end
  end

  def report_test_instance
    evaluation = get_evaluation(params[:id])
    test_instance = evaluation.test_instance
    question_id = params[:q_id]

    CreateTechnicalIssueJob.perform_later(test_instance_id: test_instance.id, status: 'Report TI from examiner', from: 'examiner', evaluation_id: evaluation.id, question_id: question_id)

    redirect_to action: 'index', reported: true
  end

  def get_assessment_question(assessment_question, evaluation, question, assessment_questions_json_data)
    assessment_question_id = assessment_question['assessment_question_id']
    assessment_question_key = assessment_question['key']
    no_question_to_display = question['questionablesToDisplay'].empty?
    indent = no_question_to_display ? 1 : 3
    assessment_question_json = assessment_questions_json_data[assessment_question_id].dup
    assessment_question_json['name'] = assessment_question_key
    assessment_question_json['id'] = assessment_question_key
    assessment_question_json['indent'] = indent
    assessment_question_json['title'] = question['questionablesToDisplay'].map { |q_i| "Q#{evaluation.find_index(q_i)}" }.join(' - ') + ' | ' + assessment_question_json['title']
    assessment_question_json
  end

  private

  def get_production_html(q_index, evaluation, productions)
    {
      name: 'question',
      type: 'html',
      html: render_to_string(partial: 'question_answer', locals: { production: productions[q_index], i: q_index, evaluation: evaluation })
    }
  end
end
