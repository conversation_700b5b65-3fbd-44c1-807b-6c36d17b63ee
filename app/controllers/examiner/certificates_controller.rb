class Examiner::CertificatesController < Examiner::ExaminerController
  def generate_certificate
    evaluation = get_evaluation(certificates_params[:evaluation_id])

    survey_resp = certificates_params[:survey_resp]

    survey_resp.each do |k, _v|
      survey_resp[k] = Integer(survey_resp[k])
    rescue StandardError => _e
      # Ignored
    end

    if evaluation.present?
      if evaluation.may_assess?
        evaluation.update(json_assessment: survey_resp)
        evaluation.generate_certificate
      end
      render(json: evaluation.certificate.as_json(only: [:id]), status: :ok)
    else
      render(json: { error: 'You cannot assessed this evaluation anymore' }, status: 403)
    end
  end

  def show
    @certificate = if current_user.is_admin?
                     Certificate.find_by(id: params[:id])
                   else
                     Certificate.joins(:evaluation).find_by(evaluations: { examiner_id: current_examiner.id }, id: params[:id])
                   end
    return redirect_back(fallback_location: admin_root_path, flash: { error: "Certificate #{params[:id]} not found" }) unless @certificate

    @evaluation = @certificate.evaluation

    @main_grades_order = [{ 'Spoken' => 'spoken' }, { 'Written' => 'written' }, { 'Academic' => 'academic' }]
    @sub_grades_order = [{ 'Pronunciation' => 'pronunciation' }, { 'Spoken Fluency' => 'spokenFluency' }, { 'Grammar' => 'grammar' }, { 'Vocabulary' => 'vocabulary' }, { 'Coherence' => 'coherence' }]
  end

  private

  def certificates_params
    params.permit(:evaluation_id, survey_resp: {})
  end
end
