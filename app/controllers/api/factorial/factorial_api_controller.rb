module Api
  module Factorial
    class FactorialApiController < ApplicationController
      include HasIntegration

      before_action :authenticate_api_user, only: :create # rubocop:disable Rails/LexicallyScopedActionFilter

      private

      def authenticate_api_user
        challenge = request.headers['x-factorial-wh-challenge']
        id = params[:api_user_id]

        head(:unauthorized).then { return } unless id.present? && challenge.present?

        @api_user = ApiUser.active.find_by(id:)

        head(:unauthorized).then { return } unless @api_user

        api_user_challenge = @api_user.metadata.dig('factorial', 'challenge')

        head(:unauthorized).then { return } unless api_user_challenge.present? && api_user_challenge == challenge
      end

      def test_instance_request_params
        params.permit([
                        :id,
                        :ats_candidate_id,
                        :ats_job_posting_id
                      ])
      end

      def format_created_request(test_instance: nil)
        { json: { assessment_id: test_instance.uuid.to_s }, status: :created }
      end
    end
  end
end
