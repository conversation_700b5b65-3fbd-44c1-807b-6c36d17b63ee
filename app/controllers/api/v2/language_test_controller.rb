class Api::V2::LanguageTestController < Api::V1::LanguageTestController
  def create
    test_instance_service = Api::TestInstancesService.new(test_profile_id: @test_profile_id,
                                                          params: secure_params,
                                                          current_user:,
                                                          request:)
    test_instance_service.call
    if test_instance_service.valid?
      render json: test_instance_service.test_instance,
             serializer: language_test_serializer,
             root: 'language_test',
             status: 201
    else
      render_validation_errors(
        [
          test_instance_service.user,
          test_instance_service.test_instance_errors,
          test_instance_service.requestor_errors
        ]
      )
    end
  end

  private

  def define_test_profile_id
    test_profile_id_param = API_AVAILABLE_PROFILES_ID_V2[secure_params.dig(:test_instance, :profile)&.to_s_escaped&.downcase]
    @test_profile_id = TestProfile.active.find_by_id(test_profile_id_param)&.id ||
                       current_user.default_test_profile_id
  end
end
