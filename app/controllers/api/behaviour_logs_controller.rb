class Api::BehaviourLogsController < ApplicationController
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :null_session
  skip_before_action :verify_authenticity_token
  before_action :user_has_test_instance

  def create
    BehaviourLog.create(secure_params) ? head(:ok) : head(:bad_request)
  end

  private

  def secure_params
    params.permit(:category, :test_instance_id, :event_date, metadata: [:content, { content_indexes: [:start, :end] }, :value, { confirmations: [] }])
  end

  def user_has_test_instance
    head :unauthorized unless secure_params[:test_instance_id].present? && current_user&.test_instance_ids&.include?(secure_params[:test_instance_id].to_i)
  end
end
