class Api::Talentsoft::TalentsoftApiController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :authenticate_from_token!, except: [:token]

  attr_accessor :api_user

  private

  def secure_params
    params.permit(
      :grant_type,
      :name,
      :client_id,
      :passphrase,
      :client_secret,
      :keyword,
      :language,
      :campaign_id,
      :applicant_id,
      :email,
      :applicants,
      :talentsoft_api => {},
      :applicants => [],
      :campaigns => [],
      :invitations => [:applicant_id, :lastname, :firstname, :phone, :email, :language],
      :order => [:reference]
    ).delete_if { |k, v| v.nil? }
  end

# Authenticate with the Authorization parameter token
=begin
def authenticate_from_token!
      if authenticate_with_http_token.nil?
        missing_token!
      end
    authenticate_with_http_token do |token, options|

      api_user = ApiUser.find_by_authentication_token(token)
      if api_user && api_user.authentication_token_expires_at
        if api_user.authentication_token_expires_at < Time.now
          expired_token!
        else
          @current_user = api_user
        end
      else
        invalid_token!
      end
    end
end
=end

  # Je remet ma version car celle ci dessus ne fonctionne pas, il faut ajouter la prise en compte de "Bearer : " dans le champ authorisatipn

  def bearer_token
    pattern = /^Bearer /
    header  = request.headers["Authorization"] # <= env
    header.gsub(pattern, '') if header && header.match(pattern)
  end

  def authenticate_from_token!
    if bearer_token
      user_token = bearer_token
    else
      user_token = request.headers["Authorization"]
    end

    if !user_token
      missing_token!

    else
      api_user = ApiUser.find_by_authentication_token(user_token.to_s)

      if api_user && api_user.authentication_token_expires_at
        if api_user.authentication_token_expires_at < Time.now
          expired_token!
        else
          @current_user = api_user
        end
      else
        invalid_token!
      end
    end
  end

  def missing_token!
    render(json: { "code" => 401, "status" => "error", "message" => "AE01 : No access token." }, status: 401)
  end

  def expired_token!
    render(json: { "code" => 401, "status" => "error", "message" => "AE02 : Access token expired." }, status: 401)
  end

  def invalid_token!
    render(json: { "code" => 401, "status" => "error", "message" => "AE03 : Invalid access token." }, status: 401)
  end

  def missing_campaign_id!
    render(json: { "code" => 400, "status" => "error", "message" => "IW03 : The campaign does not exist." }, status: 400)
  end

  def missing_applicant!
    render(json: { "code" => 400, "status" => "error", "message" => "MA04 : Candidate not found." }, status: 400)
  end

  def unknown_language!
    render(json: { "code" => 400, "status" => "error", "message" => "IW04 : Language not supported" }, status: 400)
  end

  def unknown_keyword!
    render(json: { "code" => 400, "status" => "fail", "message" => "Keyword fail : nothing corresponding to keyword" }, status: 400)
  end

  def missing_grant_type!
    render(json: { "error" => "invalid_request", "error_description" => "IT01 : The grant_type is required." }, status: 400)
  end

  def unsupported_grant_type!
    render(json: { "error" => "unsupported_grant_type", "error_description" => "IT02 : Unsupported grant_type." }, status: 400)
  end

  def invalid_client!
    render(json: { "error" => "invalid_client", "error_description" => "IT06 : Client authentication failed" }, status: 401)
  end

  def client_id_missing!
    render(json: { "error" => "invalid_client", "error_description" => "IT04 : The client_id is required with 'client_credentials' grant_type." }, status: 400)
  end

  def passphrase_missing!
    render(json: { "error" => "invalid_client", "error_description" => "IT05 : The client_secret is required with 'client_credentials' grant_type." }, status: 400)
  end

  def check_applicants(invitations) # invitations is an Array
    # Goal : check if applicants array is complete with all required information
    # If complete => true
    # Else if not okay => false
    return false if invitations.nil?

    invitations.each do |candidate|
      if candidate[:applicant_id].nil? || candidate[:lastname].nil? || candidate[:firstname].nil? || candidate[:email].nil?
        return false
      end
    end

    return true
  end

  # Find a test instance from talentsoft uuid (store in ApiOrder[order_reference]) and a campaign
  #   Reminder : campaigns are fake, it serve only for language of test                and default local language
  def find_ti_with_talentsoft_uuid(tuuid, campaign)
    ao = ApiOrder.where(source: ApiOrder.sources[:talentsoft]).where(order_reference: tuuid)
    if ao.empty?
      return nil
    end

    if ao.respond_to?(:each) # ao can be a collection
      ao.each do |api_o|
        if api_o&.test_instance && (api_o.test_instance.test_language == campaign["language"].to_s)
          return api_o.test_instance
        end
      end
    else
      if ao.test_instance && (ao.test_instance.test_language == campaign["language"])
        return ao.test_instance
      end
    end
    return nil
  end

  # Find a user from talentsoft uuid and a campaign id
  def find_user_with_talentsoft_uuid(tuuid)
    ao = ApiOrder.where(source: ApiOrder.sources[:talentsoft]).where(order_reference: tuuid)
    if ao.empty?
      return nil
    end

    if ao.respond_to?(:each) # ao can be a collection
      ao.each do |api_o|
        if api_o && api_o.user
          return api_o.user
        end
      end
    else

      if ao.user
        return ao.user
      end
    end
    return nil
  end

  public

  # Function to get a 2 letters code for language (for us) receiving a 2 language code - country
  def language_without_country(language)
    language.scan(/\w+/)[0] unless language.nil?
  end

  # __________________ TOKEN __________________
  # Call this endpoint to acquire a 'bearer' access token that allows you to perform calls to the APIs. The parameters should be provided in the URL encoded format in the body of the request.
  #
  # POST /api/talentsoft_api/token
  #
  # HTTP request = https://api.partner.com/api/talentsoft_api/token
  #
  # Request Body : application/x-www-form-urlencoded
  # The list of candidates to invite to the campaign.
  #
  #   - grant_type (required) [string] : Must be client_credentials, as we do not provide User authentication.
  #   - client_id (required) [string] : Required with client_credentials grant type. The client id provided by Talentsoft.
  #   - client_secret (required) [string] : Required with client_credentials grant type. The client secret key provided by Talentsoft.
  #
  #
  # Response
  # - [200] Access token response
  # - [400] Bad request
  # - [401] Unauthorized

  # Authenticate user using HTTP basic
  # Check for an ApiUser with the given key as client_secret
  # Set api_user if found, otherwise render 401
  def token
    grant_type = secure_params[:grant_type]
    name = secure_params[:client_id]
    passphrase = secure_params[:client_secret]

    if grant_type == "client_credentials" # Only grant type authorised

      if name.nil?
        return client_id_missing!
      end

      if passphrase.nil?
        return passphrase_missing!
      end

      api_oauth_manager = ApiOauthManager.find_by(login_name: name, passphrase: passphrase)

      api_user = if api_oauth_manager.nil?
                   ApiUser.find_by(name: name, passphrase: passphrase, status: ApiUser.statuses.fetch(:active))
                 else
                   api_oauth_manager.api_user
                 end

      if !api_user.nil?

        if api_user.authentication_token.nil?
          # If token doesn't exist
          api_user.generate_authentication_token!
          @current_user = api_user
          expires_in = api_user.authentication_token_expires_at - Time.now

          render(json: { "access_token" => api_user.authentication_token, "token_type" => "bearer", "expires_in" => expires_in }, status: 200)

        elsif api_user.authentication_token_expires_at < Time.now # If token is expired

          # If token is expired it generate a new one
          api_user.generate_authentication_token!
          @current_user = api_user
          expires_in = api_user.authentication_token_expires_at - Time.now

          render(json: { "access_token" => api_user.authentication_token, "token_type" => "bearer", "expires_in" => expires_in }, status: 200)
        else
          # Token exist and still valid
          #   Response have 3 attributs
          # {
          # "access_token": "2YotnFZFEjr1zCsicMWpAA",
          # "token_type": "bearer",
          # "expires_in": "3600"
          # }

          api_user.touch_token!
          @current_user = api_user
          expires_in = api_user.authentication_token_expires_at - Time.now

          render(json: { "access_token" => api_user.authentication_token, "token_type" => "bearer", "expires_in" => expires_in }, status: 200)
        end

      else # if user doesn't exist
        invalid_client!
      end

    elsif grant_type.nil?

      missing_grant_type!

    else # if grant_type == "urn:ietf:params:oauth:grant-type:jwt-bearer" or else
      unsupported_grant_type!

    end
  end
end
