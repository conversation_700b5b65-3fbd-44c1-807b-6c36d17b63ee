class Api::Lever::LeverApiController < ApplicationController
  include HasIntegration

  # rubocop:disable Rails/LexicallyScopedActionFilter

  before_action :authenticate_api_user, only: :create
  before_action :valid_event?, only: :create
  before_action :define_and_check_opportunity, only: :create

  # rubocop:enable Rails/LexicallyScopedActionFilter

  private

  def valid_event?
    to_stage_id = params[:data]&.[]('toStageId')
    api_user_trigger_stage = @api_user&.metadata_1&.[]('lever_trigger_stage')

    unless to_stage_id.in?(Rails.application.config.lever[:webhook][:trigger_stages]) || api_user_trigger_stage == to_stage_id
      head :no_content
    end
  end

  def authenticate_api_user
    @api_user = ApiUser.find_by_name(params[:api_user_name])
    if @api_user.blank? || @api_user&.api_oauth_manager&.passphrase.blank?
      EventLog.create(loggable_type: 'Alert', category: 'order_rejected', event_date: DateTime.now, content: "[LeverAPI] #{I18n.t('lever.errors.api_user_not_found')}", criticity: :info)
      render status: 404, json: { error: I18n.t('lever.errors.api_user_not_found') }
      return
    end

    token = params[:token]
    triggered_at = params['triggeredAt']&.to_s
    signature = params[:signature]&.to_s
    body = token + triggered_at
    hash = OpenSSL::HMAC.new(@api_user.api_oauth_manager.passphrase, OpenSSL::Digest.new('sha256'))
    api_user_signature = hash.update(body).to_s
    if signature.blank? || api_user_signature != signature
      EventLog.create(loggable_type: 'Alert', category: 'order_rejected', event_date: DateTime.now, content: "[LeverAPI] #{I18n.t('lever.errors.invalid_webhook_signature')}", criticity: :info)
      render status: 401, json: { error: I18n.t('lever.errors.invalid_webhook_signature') }
    end
  end

  def define_and_check_opportunity
    opportunity_id = params['data']&.[]('opportunityId')
    @opportunity = Lever::Api::Core::Opportunities.get(@api_user, opportunity_id)

    trigger_only_on_tag = ActiveModel::Type::Boolean.new.cast(@api_user&.metadata_1&.[]('lever_trigger_only_on_tag'))
    head :no_content if @opportunity.nil? || (trigger_only_on_tag && @opportunity.tag.blank?)
  end

  public

  def callback
    params = callback_params
    api_user = ApiUser.find_by_name(params[:state])
    api_user&.api_oauth_manager&.login_name = params[:code]
    flash = { success: I18n.t('devise.confirmations.confirmed') }
    if api_user&.save
      EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{I18n.t('lever.code_added', code: params[:code])}", criticity: :info)
      begin
        Lever::Api.get_and_save_tokens api_user
      rescue LeverError, LeverSetupError => e
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{e.message}", criticity: :danger)
        flash = { error: e.message }
      end
      begin
        Lever::Api.set_activation_config api_user
      rescue LeverError, LeverSetupError => e
        EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{e.message}", criticity: :danger)
        flash = { error: e.message }
      end
    else
      error_message = "Error saving code for Api_user : #{params[:state]}, code :  #{params[:code]}"
      EventLog.create(loggable_type: 'Alert', category: 'process', event_date: DateTime.now, content: "[LeverAPI] #{error_message}", criticity: :info)
      flash = { error: error_message }
    end
    redirect_to root_url, flash: flash
  end

  private

  def callback_params
    params.permit(:code, :state)
  end

  def test_instance_request_params
    permitted_params = params.permit(:event, :token, :signature, :triggeredAt, data: [:toStageId, :opportunityId])
    permitted_params[:opportunity] = @opportunity
    permitted_params
  end

  def invalid_params_callback(message: nil, params: nil)
    Integrations::Lever::ErrorJob.perform_later(@api_user.id, params.order_information[:opportunity_id], message)
  end

  def api_order_created_response(json: {}, params: {})
    { status: :created, json: { partner_interview_id: params[:test_instance].uuid.to_s } }
  end
end
