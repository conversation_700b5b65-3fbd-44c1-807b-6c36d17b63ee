class Api::V1::EvaluationsController < Api::V1::BaseController
  include ApplicationHelper

  before_action :authenticate_user!, :authorize_user!

  def assessed
    evaluation = Evaluation.find_by(id: params[:evaluation_id])
    status = :not_found
    if evaluation&.may_assess? && evaluation.assess!('assessed_at')
      evaluation.deliver!('delivered_at') if evaluation.test_instance.graded?
      status = 200
    end
    render(json: {}, status: status)
  end

  def get_infos
    evaluation = Evaluation.find_by(id: params[:id])
    return not_found! unless evaluation

    render json: evaluation, serializer: Api::V1::EvaluationSerializer, status: 201
  end
end
