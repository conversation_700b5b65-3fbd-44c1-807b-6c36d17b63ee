class Api::V1::BaseController < ActionController::API
  before_action :define_version
  after_action :set_cors_headers
  after_action :log_request

  rescue_from ActiveRecord::RecordNotFound, with: :not_found!

  attr_accessor :current_user

  protected

  # ----------------------------------------
  # :section: CORS headers
  # ----------------------------------------
  def set_cors_headers
    if current_user && current_user.is_a?(ApiUser) && current_user.has_cors_headers?
      response.headers['Access-Control-Allow-Origin'] = current_user.access_control_allow_origin
      response.headers['Access-Control-Allow-Methods'] = current_user.access_control_allow_methods
    end
  end

  # ----------------------------------------
  # :section: Basic API responses
  # ----------------------------------------
  def invalid_request!
    render_api_error(status: 400, error: 'Bad request')
  end

  def unauthenticated!
    response.headers['WWW-Authenticate'] = "Token realm=Application"
    render_api_error(status: 401, error: 'Bad credentials')
  end

  def unauthorized_access!
    render_api_error(status: 403, error: 'Not authorized')
  end

  def token_expired!
    render_api_error(status: 403, error: 'Not authorized. Your authentication token has expired')
  end

  def not_found!
    render_api_error(status: 404, error: 'Not found')
  end

  def invalid_resource!(errors = [])
    render_api_error(status: 422, error: errors)
  end

  # ----------------------------------------
  # :section: Pagination
  # ----------------------------------------
  def per_page
    if params[:per_page].blank? || params[:per_page].to_i < 0
      API_DEFAULT_OBJECTS_PER_PAGE.to_i
    else
      params[:per_page].to_i
    end
  end

  def current_page
    params[:page] || 1
  end

  def meta_attributes(object)
    {
      current_page: object.current_page,
      next_page: object.next_page,
      previous_page: object.prev_page,
      per_page: per_page,
      total_pages: object.total_pages,
      total_count: object.total_count
    }
  end

  # ----------------------------------------
  # :section: Authentication (OAuth 2.0 compatible)
  # ----------------------------------------
  private

  # Extract token from Authorization header
  def authorization_header_token
    parsed_token || bearer_token
  end

  # Parses Authorization headers like this:
  # Authorization: Bearer Z2wX2eL5oHyJqmbTE8ac1vVfpk/YD8fgK+98l8Da1ZnQ/m553tSTHX+jexbbTIev29zwWyO3bVfYzFVhslVwGw==
  def bearer_token
    if request.headers['Authorization'].present?
      request.headers['Authorization'].split(' ').last
    else
      nil
    end
  end

  # Parses Authorization headers like this:
  # Authorization: Token token=Z2wX2eL5oHyJqmbTE8ac1vVfpk/YD8fgK+98l8Da1ZnQ/m553tSTHX+jexbbTIev29zwWyO3bVfYzFVhslVwGw==, name=main-dashboard
  def parsed_token
    token, _options = ActionController::HttpAuthentication::Token.token_and_options(request)
    token
  end

  protected

  def authenticate_user!
    token = authorization_header_token
    return unauthenticated! if token.nil?

    api_user = ApiUser.active.where(authentication_token: token).first
    if !api_user.nil? && api_user.authentication_token_expires_at
      if api_user.authentication_token_expires_at < Time.now
        log_request
        token_expired!
      else
        api_user.touch_token!
        @current_user = api_user
      end
    else
      log_request
      unauthenticated!
    end
  end

  # ----------------------------------------
  # :section: Authorization
  # ----------------------------------------
  def authorize_user!
    unauthorized_access! unless current_user
  end

  # ----------------------------------------
  # :section: Rendering helpers
  # ----------------------------------------
  def render_api_success(status: 200, message: 'OK', data: nil)
    render json: { status: status.to_s, message: message.to_s, data: data }, status: status
  end

  def render_api_error(status: 500, error: 'Unknown', data: nil)
    render json: { status: status.to_s, error: error.to_s, data: data }, status: status
  end

  def render_validation_errors(object)
    if object.is_a?(Array)
      json = {}
      object.each do |o|
        if o.kind_of?(ApplicationRecord)
          json[o.class.name.underscore] = { errors: validation_errors_to_json(o.errors) }
        elsif o.is_a?(Array)
          json[o[0]] = o[1]
        end
      end

      render(status: 422, json: json)
    else
      render(status: 422, json: { object.class.name.underscore => { errors: validation_errors_to_json(object.errors) } })
    end
  end

  def language_test_serializer
    Api.const_get(@version)::LanguageTestSerializer
  end

  private

  def define_version
    @version = self.class.module_parent.to_s.demodulize || 'V1'
  end

  # ----------------------------------------
  # :section: Internal helpers
  # ----------------------------------------

  # TODO: remove
  def validation_errors_to_json(errors)
    return errors if errors.is_a? String

    errors.messages
  end

  def log_request
    a = if !current_user.nil?
          "id:#{current_user.id} name:#{current_user.name}"
        else
          "not defined"
        end

    l = {
      ApiUser: a,
      RequestUrl: request.url,
      UserAgent: request.user_agent,
      Params: params.to_s,
      QueryParameters: request.query_parameters.to_s,
      RequestParameter: request.request_parameters.to_s,
      RawPOST: request.raw_post,
      Authorization: request.authorization.to_s,
      Headers: request.headers.env.select { |k, _| k.in?(ActionDispatch::Http::Headers::CGI_VARIABLES) || k =~ /^HTTP_/ }.to_yaml
    }
    logger.debug("API DEBUG: " + l.inspect)
  end

  def api_log(code, response)
    api_logger.debug(
      "\nRequest: " + request.url +
      "\nParams: " + params.to_s +
      "\n\n" +
      "\nResponse code: " + code.to_s +
      "\nResponse: \n" +
      response +
      "\n------"
    )
  end

  def api_logger
    @api_logger ||= Logger.new(Rails.root.join('log', "api-#{Rails.env.to_s}.log"))
  end
end
