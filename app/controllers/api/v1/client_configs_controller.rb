class Api::V1::ClientConfigsController < TestInstancesController
  # Creates a new client configuration.
  #
  # This method is intended to handle the creation of a new client configuration.
  # This method is used by Freshworks when a lead is converted into an deal in trial mode.
  #
  # @example
  #   POST /api/v1/client_configs
  #
  # @return [void]
  def create
    client_name = params[:client_name]
    return head :bad_request if client_name.blank?

    if ClientConfig.find_by(sanitized_client_name: client_name.downcase)
      head :no_content # We return 204 otherwise PClients will send an alert email
    else
      ClientConfig.create(client_name:, active: true)
      head :created
    end
  end
end
