class Admin::TestProfilesController < Admin::AdminController
  include Rest<PERSON><PERSON>ontroller

  before_action :update_is_secure?, only: [:update]

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :test_profile
  end

  def identifier
    :name
  end

  def controller_namespace
    :admin
  end

  # Because we have a special case with :translated_languages_list, we have
  # to override the whole method secure_params instead of just permitted_params
  def secure_params
    params.require(object).permit(:name,
                                  :next_questionable_profile,
                                  :max_questions_to_answer,
                                  :linguist_ids,
                                  :additional_linguist_ids,
                                  :fallback_set,
                                  :force_multilingual,
                                  :test_taker_type,
                                  :certification_type,
                                  :client_type,
                                  :level,
                                  :show_skip_button,
                                  :internal_description,
                                  :examiner_description,
                                  :test_instance_validation_id,
                                  :api_exposed,
                                  :talent_ai,
                                  translated_languages_list: [],
                                  additional_translated_languages_list: [],
                                  test_languages: [])
  end

  def index
    @resources = TestProfile.order('created_at DESC')

    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # PUT /admin/resources/1/activate
  # PUT /admin/resources/1/activate.json
  def activate
    respond_to do |format|
      if @resource.activate!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/expire
  # PUT /admin/resources/1/expire.json
  def expire
    respond_to do |format|
      if @resource.expire!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  private

  def update_is_secure?
    # return true if secure_params[:linguist_ids] == @resource.linguist_ids

    # tis_in_progress = @resource.test_instances.in_progress.count
    # if  tis_in_progress > 0
    #   redirect_back(fallback_location: root_path, flash: {warning: "Update forbidden, #{tis_in_progress} test instances are in_progress."})
    #   return false
    # end
  end
end
