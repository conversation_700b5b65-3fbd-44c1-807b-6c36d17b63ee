class Admin::EvaluationDelaysController < Admin::AdminController
  include RestfulController

  def object
    :evaluation_delay
  end

  def identifier
    :id
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :delay_label,
      :payement_rate,
      :graded_time_limit,
      :soon_overdue_time_limit
    ]
  end

  def edit
    @resource = EvaluationDelay.find(params[:id])
    respond_to do |format|
      format.html { render template: edit_template_path }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  def index
    @resources = EvaluationDelay.all
    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end
end
