## Statistics controller
# Only accessible as an admin

class Admin::StatisticsController < Admin::AdminController
  # All methods here must export a CSV
  # Template behaviour is
  # colums lists all standard columns (direct attributes of the model)
  # extra_columns are non direct attributes, requiring a computation, an argument, ...
  # html view of the page return an error

  ## Export challenges, id and
  def challenges
    columns = ['linguist_id']
    extra_columns = %w[average_score_production production_count average_score_reception reception_count]
    @data = []
    @data_columns = columns + extra_columns
    Challenge.find_each do |statistics_instance|
      row = columns.collect { |c| statistics_instance.send(c) }

      ## Extra columns, with more complex calculation
      row += [statistics_instance.average_score(:production), statistics_instance.productions.count,
              statistics_instance.average_score(:reception), statistics_instance.receptions.count]

      @data << row
    end

    respond_to do |format|
      format.html { render template: 'admin/statistics/statistics_table' }
      format.csv do
        csv_report = CSV.generate do |csv|
          @data.each do |line|
            csv << line
          end
        end
        send_data csv_report
      end
    end
  end

  # Standard information on user
  # Allow us to detect user waiting for receptions
  def users
    columns = %w[id first_name last_name email created_at score_rating count_of_productions_answered
                 count_of_productions_reported count_of_receptions_answered]
    extra_columns = %w[productions_waiting days_waiting_for_production recording_reported]
    @data = []
    @data_columns = columns + extra_columns
    User.find_each do |statistics_instance|
      row = columns.collect { |c| statistics_instance.send(c) }

      days_waiting = if statistics_instance.productions.empty?
                       0
                     else
                       (Time.now - statistics_instance.productions.last.created_at).to_i / 86_400
                     end

      ## Extra columns, with more complex calculation
      row += [statistics_instance.count_of_productions_left_to_be_challenged, days_waiting,
              statistics_instance.count_of_productions_reported]
      @data << row
    end

    respond_to do |format|
      format.html { render template: 'admin/statistics/statistics_table' }
      format.csv do
        csv_report = CSV.generate do |csv|
          @data.each do |line|
            csv << line
          end
        end
        send_data csv_report
      end
    end
  end

  # Standard information on user
  # Allow us to detect user waiting for receptions
  def challenge_logs
    columns = %w[id challenge_id question_id success question_element_id question_element_production_rating
                 question_element_reception_rating question_element_type challenge_validated_at production_user_id
                 production_user_main_rating reception_user_id reception_user_main_rating]
    extra_columns = ['challenge linguist id', 'question label']
    @data_columns = columns + extra_columns
    @data = []
    ChallengeLog.find_each do |statistics_instance|
      row = columns.collect { |c| statistics_instance.send(c) }

      ## Extra columns, with more complex calculation
      row += [statistics_instance.challenge.linguist_id, statistics_instance.question.label]

      @data << row
    end

    respond_to do |format|
      format.html { render template: 'admin/statistics/statistics_table' }
      format.csv do
        csv_report = CSV.generate do |csv|
          @data.each do |line|
            csv << line
          end
        end
        send_data csv_report
      end
    end
  end

  # Standard information on user
  # Allow us to detect user waiting for receptions
  def past_challenges
    # PastChallenges.rebuild_all
    columns = %w[user_id challenge_id challenger_user_id success question_element_id
                 question_element_type challenge_validated_at production_user_id reception_user_id]
    extra_columns = ['challenge linguist id', 'question label']
    @data_columns = columns + extra_columns
    @data = []
    PastChallenges.find_each do |statistics_instance|
      row = columns.collect { |c| statistics_instance.send(c) }

      ## Extra columns, with more complex calculation
      row += [statistics_instance.challenge.linguist_id, statistics_instance.question.label]

      @data << row
    end

    respond_to do |format|
      format.html { render template: 'admin/statistics/statistics_table' }
      format.csv do
        csv_report = CSV.generate do |csv|
          @data.each do |line|
            csv << line
          end
        end
        send_data csv_report
      end
    end
  end

  def statistics_generic_method(columns, extra_columns, statistics_class = User)
    @data_columns = columns + extra_columns
    @data = []
    statistics_class.find_each do |statistics_instance|
      row = columns.collect { |c| statistics_instance.send(c) }

      ## Extra columns, with more complex calculation
      row += [
        statistics_instance.count_of_productions_left_to_be_challenged,
        (Time.now - statistics_instance.productions.last.created_at).to_i / 86_400,
        statistics_instance.count_of_productions_reported
      ]
      @data << row
    end

    respond_to do |format|
      format.html { render template: 'admin/statistics/statistics_table' }
      format.csv do
        csv_report = CSV.generate do |csv|
          @data.each do |line|
            csv << line
          end
        end
        send_data csv_report
      end
    end
  end

  def index
    @statistics_service = AdminStatisticsService.new(params[:week], params[:month])
    @statistics_service.call
  end
end
