class Admin::ClientConfigsController < Admin::AdminController
  include Restful<PERSON>ontroller

  def index
    @resources = klass.order(created_at: index_sort_order)
    # Used by Freshworks
    @resources = @resources.where("sanitized_client_name ILIKE ?", "%#{params[:client_name].downcase}%") if params[:client_name].present?
    if @resources.count == 1
      redirect_to edit_admin_client_config_path(@resources.first)
      return
    end
    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :client_config
  end

  def identifier
    :client_name
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :client_name,
      :active,
      :expire_test_instances,
      :expire_test_instances_limit,
      :send_reminders,
      :count_of_reminders_to_send,
      :days_between_reminders,
      :urgent_mode,
      :force_secure_browser,
      :surcharged_certificate_email,
      :cced_email,
      :strong_identity_check,
      :block_direct_candidate_contact,
      :deliver_certificate_to_client,
      :skip_check_caracters,
      :systematic_peer_review,
      :systematic_peer_review_counter,
      :custom_message,
      :practice_pack_mention,
      :expose_dashboard_url_in_api,
      :expose_identity_photos_in_api,
      :disable_ai_assessments,
      :disable_ai_model_training,
      :enable_wheebox_proctoring,
      client_comments_attributes: [:comment, :languages, :id]
    ]
  end

  def add_client_comment
    ClientComment.create(client_config: @resource)
    redirect_to edit_admin_client_config_path(@resource)
  end

  def delete_client_comment
    comment = ClientComment.find(params[:format])
    comment.delete
    redirect_to edit_admin_client_config_path(@resource)
  end
end
