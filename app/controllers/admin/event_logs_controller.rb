class Admin::EventLogsController < Admin::AdminController
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def object
    :event_log
  end

  def identifier
    :id
  end

  def controller_namespace
    :admin
  end

  def index
    filters = {}

    @limits = [250, 500, 1000, 2000, 3000]
    @limit = params[:limit]&.to_i || @limits.first

    @all_loggables = EventLog.loggable_types.keys
    @loggable_type = params[:loggable] || @all_loggables.first

    @all_categories = EventLog.categories.symbolize_keys.keys
    @categories = params[:categories]&.map(&:to_sym) || @all_categories
    filters[:category] = @categories.map { |cat| EventLog.categories[cat] } if params[:categories]&.any? && @categories.length < @all_categories.length

    @all_criticities = EventLog.criticities.symbolize_keys.keys
    @criticities = params[:criticity] || @all_criticities
    filters[:criticity] = @criticities.map { |cr| EventLog.criticities[cr] } if params[:criticity] && @criticities.length < @all_criticities.length

    filters[:loggable_type] = @loggable_type

    if params[:loggable_id].present?
      filters[:loggable_id] = params[:loggable_id]
      loggable_class = [Alert, Evaluation, InvoiceRequest, Production, TestInstance, TestInstanceValidation, User].find { |klass| klass.name == @loggable_type.classify }
      @event_log_categories_stats = EventLog.categories_stats_for(loggable_class.find_by(id: params[:loggable_id])) if loggable_class
    end

    @resources = EventLog.where(filters).order(event_date: :desc).limit(@limit)

    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end
end
