class Admin::QuestionElementsController < Admin::AdminController
  def index
  end

  def show
    @qe = QuestionElement.find(params[:id])
  end

  def edit
    @qe = QuestionElement.find(params[:id])
  end

  def search
    if QuestionElement.where(:id => params[:search_id]).empty?
      flash[:error] = "No corresponding id"
      render 'index'
    else
      @qe = QuestionElement.find(params[:search_id])
      render 'edit'
    end
  end

  def update
    @qe = QuestionElement.find(params[:id])

    content = eval(params[:content])
    if @qe.update_attribute(:content, content)
      flash[:success] = "QuestionElement updated"
    else
      flash[:error] = "Errors"
    end
    render 'edit'
  end
end
