class Admin::AssessmentQuestionsController < Admin::AdminController
  include Restful<PERSON>ontroller

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :assessment_question
  end

  def identifier
    :id
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :json_data,
    ]
  end

  def create
    @resource = AssessmentQuestion.new
    params_to_update = secure_params.dup
    params_to_update[:json_data] = JSON.parse(params_to_update[:json_data])
    @resource.update!(params_to_update)
    redirect_to edit_admin_assessment_question_url(@resource)
  end

  def update
    @resource = AssessmentQuestion.find(params[:id])

    params_to_update = secure_params.dup
    params_to_update[:json_data] = JSON.parse(params_to_update[:json_data])
    @resource.update!(params_to_update)
    redirect_back(fallback_location: root_path)
  end
end
