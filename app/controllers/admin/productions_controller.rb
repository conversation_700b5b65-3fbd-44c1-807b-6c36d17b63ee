class Admin::ProductionsController < Admin::AdminController
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  skip_before_action :define_resource, only: [:select_production]

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :production
  end

  def identifier
    :id
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :id,
      :new_audio_production,
      :new_text_production,
      :i_know_what_im_doing
    ]
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # POST /admin/productions/select_production
  # POST /admin/productions/select_production.json
  #
  # This method just shows the form to select a production
  def index
    if params[:test_instance_id]
      redirect_to edit_admin_test_instance_path(params[:test_instance_id])
    else
      redirect_to action: :select_production
    end
  end

  def destroy
    ti = TestInstance.find_by(id: @resource.test_instance_id)

    if ti
      EventLog.add(@resource, :admin, "ADMIN#delete_production! production=#{@resource.id} challenge=#{@resource.challenge_id}", :info)
    end

    # Proceed to deletion
    super
  end

  def select_production
    render action: :edit_production_content
  end

  # POST /admin/productions/select_production_redirect
  # POST /admin/productions/select_production_redirect.json
  #
  # This method is targeted by the form of select_production
  def select_production_redirect
    if @resource
      redirect_to edit_production_content_admin_production_path(@resource)
    else
      redirect_to select_production_admin_productions_path, alert: "Error: no production found for id: '#{params[:id]}''"
    end
  end

  # GET /admin/productions/1/edit_production_content
  # GET /admin/productions/1/edit_production_content.json
  #
  # View the edit for of a Production
  def edit_production_content
    if !@resource
      redirect_to select_production_admin_productions_path, alert: "Error: no production found for id: '#{params[:id]}''"
    end
  end

  # POST /admin/productions/1/update_production_content
  # POST /admin/productions/1/update_production_content.json
  #
  # Update the content of a production
  def update_production_content
    # Ensure that we know what we are doing
    if !secure_params[:i_know_what_im_doing] || secure_params[:i_know_what_im_doing] != '1' ||
       (!secure_params[:new_audio_production] && !secure_params[:new_text_production])

      message = 'Please confirm that you know what you are doing.'
      respond_to do |format|
        format.html { redirect_to edit_production_content_admin_production_path(@resource), alert: message }
        format.json { render json: message, status: :unprocessable_entity }
      end
    else

      if secure_params[:new_audio_production]
        message = if @resource.create_or_replace_audio_production(secure_params[:new_audio_production])
                    'Audio production was updated'
                  else
                    'Audio production could not be updated'
                  end

      elsif secure_params[:new_text_production]
        message = if @resource.create_or_replace_text_production(secure_params[:new_text_production])
                    'Text production was updated'
                  else
                    'Text production could not be updated'
                  end
      end

      respond_to do |format|
        format.html { redirect_to edit_production_content_admin_production_path(@resource), notice: message }
        format.json { render json: message, status: 200 }
      end

    end
  end

  # # GET /admin/productions/1/make_super
  # # GET /admin/productions/1/make_super.json
  # def make_super
  #   production = Production.find(params[:id])
  #   production.update_attribute(:super, true)
  #   production.create_clone(User.super_producer.id)
  #   @p = production
  #   respond_to do |format|
  #     format.html
  #     format.js
  #   end
  # end

  # # GET /admin/productions/1/unmake_super
  # # GET /admin/productions/1/unmake_super.json
  # def unmake_super
  #   production = Production.find(params[:id])
  #   production.update_attribute(:super, false)
  #   #Delete ready receptions for super production of this user
  #   Production.where(super: true).where(challenge_id: production.challenge_id).where(user_id: production.user_id).each do |sp|
  #     if sp.reception && sp.reception.ready?
  #       sp.disabled!
  #     end
  #   end
  #   @p = production
  #   respond_to do |format|
  #     format.html
  #     format.js
  #   end
  # end
end
