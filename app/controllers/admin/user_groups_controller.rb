class Admin::UserGroupsController < Admin::AdminController
  include Restful<PERSON>ontroller

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :user_group
  end

  def identifier
    :name
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    %i[
      name
      examiner_en_id
      examiner_de_id
      examiner_es_id
      examiner_fr_id
      examiner_ru_id
      examiner_ar_id
      examiner_it_id
      examiner_nl_id
      examiner_pt_id
      examiner_ptbr_id
      examiner_el_id
      examiner_sv_id
      examiner_zhcn_id
      examiner_ja_id
      examiner_ko_id
      examiner_th_id
      examiner_vi_id
      examiner_tr_id
      examiner_he_id
      examiner_pl_id
      examiner_ms_id
      examiner_hi_id
      examiner_fi_id
      examiner_hr_id
      examiner_ro_id
      examiner_sl_id
      examiner_sr_id
      examiner_sq_id
      examiner_lb_id
      examiner_cs_id
      examiner_da_id
      examiner_no_id
      examiner_uk_id
      examiner_hu_id
      examiner_eu_id
      examiner_bg_id
      examiner_sk_id
      examiner_et_id
      examiner_lt_id
      examiner_lv_id
      examiner_zhyue_id
    ]
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
end
