## Challenges controller
# Only accessible as an admin

class Admin::ChallengesController < Admin::AdminController
  before_action :challenge, only: %i[update production reception infos_production infos_reception edit_production
                                     edit_reception add_question_element remove_question_element publication show
                                     duplicate]

  def index
    @challenges = []
    @all_challenges = Challenge.all
    @all_challenges.each do |ac|
      @challenges << ac
    end

    @chall_usages = Production.where('answered_at > ?', 1.month.ago).group(:challenge_id).count

    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def index_normal_page
    @challenges = []
    @question_number_production = {}
    @question_number_reception = {}

    Challenge.find_each do |c|
      challenge = {
        id: c.id,
        linguist_id: c.linguist_id,
        status: c.status,
        average_rating_production: c.average_rating(:production),
        average_rating_reception: c.average_rating(:reception)
      }

      @question_number_reception[c[:id]] = 0

      challenge[:cecrl_prod] = 'NA'
      c.questions.each do |q|
        if !q.cecrl_prod.nil? && q.cecrl_prod != challenge[:cecrl_prod]
          if challenge[:cecrl_prod] == 'NA'
            challenge[:cecrl_prod] = q.cecrl_prod
          elsif challenge[:cecrl_prod_multiple].nil?
            challenge[:cecrl_prod_multiple] = q.cecrl_prod
            challenge[:cecrl_prod] = "#{challenge[:cecrl_prod]}*"
          else
            challenge[:cecrl_prod_multiple] = "#{challenge[:cecrl_prod_multiple]},#{q.cecrl_prod}"
          end
        end
        @question_number_reception[c[:id]] = @question_number_reception[c[:id]] + 1 if q.is_a?(Questions::QTextFieldRecorder) || q.is_a?(Questions::QDateTime) || q.is_a?(Questions::QRadioLinkedWordsRecorder) || q.is_a?(Questions::QRadioWordsRecorder) || q.is_a?(Questions::QCheckbox) || q.is_a?(Questions::QDropDown) || q.is_a?(Questions::QRadioImagesRecorder) || q.is_a?(Questions::QRadioLinkedImagesRecorder) || q.is_a?(Questions::QRadioTextsRecorder)
      end

      cecrl_prod = challenge[:cecrl_prod]

      if @cecrl_statistics
        if @cecrl_statistics[cecrl_prod].nil?
          @cecrl_statistics[cecrl_prod] = {
            production: {
              count: 1,
              total_score: c.average_rating(:production),
              max_score: c.average_rating(:production),
              min_score: c.average_rating(:production)
            }
          }
        elsif !@cecrl_statistics[cecrl_prod][:production].nil?
          @cecrl_statistics[cecrl_prod][:production][:count] = @cecrl_statistics[cecrl_prod][:production][:count] + 1
          @cecrl_statistics[cecrl_prod][:production][:total_score] = @cecrl_statistics[cecrl_prod][:production][:total_score] + c.average_rating(:production)
          @cecrl_statistics[cecrl_prod][:production][:max_score] = c.average_rating(:production) if c.average_rating(:production) > @cecrl_statistics[cecrl_prod][:production][:max_score]
          @cecrl_statistics[cecrl_prod][:production][:min_score] = c.average_rating(:production) if c.average_rating(:production) < @cecrl_statistics[cecrl_prod][:production][:min_score]
        else
          @cecrl_statistics[cecrl_prod][:production] = {
            count: 1,
            total_score: c.average_rating(:production),
            max_score: c.average_rating(:production),
            min_score: c.average_rating(:production)
          }
        end
      end

      challenge[:cecrl_recep] = 'NA'
      c.questions.each do |q|
        if !q.cecrl_recep.nil? && q.cecrl_recep != challenge[:cecrl_recep]
          if challenge[:cecrl_recep] == 'NA'
            challenge[:cecrl_recep] = q.cecrl_recep
          elsif challenge[:cecrl_recep_multiple].nil?
            challenge[:cecrl_recep_multiple] = q.cecrl_recep
            challenge[:cecrl_recep] = "#{challenge[:cecrl_recep]}*"
          else
            challenge[:cecrl_recep_multiple] = "#{challenge[:cecrl_recep_multiple]},#{q.cecrl_recep}"
          end
        end
      end

      cecrl_recep = challenge[:cecrl_recep]

      if @cecrl_statistics
        if @cecrl_statistics[cecrl_recep].nil?
          @cecrl_statistics[cecrl_recep] = {
            reception: {
              count: 1,
              total_score: c.average_rating(:reception),
              max_score: c.average_rating(:reception),
              min_score: c.average_rating(:reception)
            }
          }
        elsif !@cecrl_statistics[cecrl_recep][:reception].nil?
          @cecrl_statistics[cecrl_recep][:reception][:count] = @cecrl_statistics[cecrl_recep][:reception][:count] + 1
          @cecrl_statistics[cecrl_recep][:reception][:total_score] = @cecrl_statistics[cecrl_recep][:reception][:total_score] + c.average_rating(:reception)
          @cecrl_statistics[cecrl_recep][:reception][:max_score] = c.average_rating(:reception) if c.average_rating(:reception) > @cecrl_statistics[cecrl_recep][:reception][:max_score]
          @cecrl_statistics[cecrl_recep][:reception][:min_score] = c.average_rating(:reception) if c.average_rating(:reception) < @cecrl_statistics[cecrl_recep][:reception][:min_score]
        else
          @cecrl_statistics[cecrl_recep][:reception] = {
            count: 1,
            total_score: c.average_rating(:reception),
            max_score: c.average_rating(:reception),
            min_score: c.average_rating(:reception)
          }
        end
      end

      @challenges << challenge
    end
  end

  def index_cecrl_statistics
    @challenges = []
    @cecrl_statistics = {}
    @question_number_production = {}
    @question_number_reception = {}

    Challenge.find_each do |c|
      challenge = {
        id: c.id,
        linguist_id: c.linguist_id,
        status: c.status,
        average_rating_production: c.average_rating(:production),
        average_rating_reception: c.average_rating(:reception)
      }

      @question_number_reception[c[:id]] = 0

      challenge[:cecrl_prod] = 'NA'
      c.questions.each do |q|
        if !q.cecrl_prod.nil? && q.cecrl_prod != challenge[:cecrl_prod]
          if challenge[:cecrl_prod] == 'NA'
            challenge[:cecrl_prod] = q.cecrl_prod
          elsif challenge[:cecrl_prod_multiple].nil?
            challenge[:cecrl_prod_multiple] = q.cecrl_prod
            challenge[:cecrl_prod] = "#{challenge[:cecrl_prod]}*"
          else
            challenge[:cecrl_prod_multiple] = "#{challenge[:cecrl_prod_multiple]},#{q.cecrl_prod}"
          end
        end
        @question_number_reception[c[:id]] = @question_number_reception[c[:id]] + 1 if q.is_a?(Questions::QTextFieldRecorder) || q.is_a?(Questions::QDateTime) || q.is_a?(Questions::QRadioLinkedWordsRecorder) || q.is_a?(Questions::QRadioWordsRecorder) || q.is_a?(Questions::QCheckbox) || q.is_a?(Questions::QDropDown) || q.is_a?(Questions::QRadioImagesRecorder) || q.is_a?(Questions::QRadioLinkedImagesRecorder) || q.is_a?(Questions::QRadioTextsRecorder)
      end

      cecrl_prod = challenge[:cecrl_prod]

      if @cecrl_statistics
        if @cecrl_statistics[cecrl_prod].nil?
          @cecrl_statistics[cecrl_prod] = {
            production: {
              count: 1,
              total_score: c.average_rating(:production),
              max_score: c.average_rating(:production),
              min_score: c.average_rating(:production)
            }
          }
        elsif !@cecrl_statistics[cecrl_prod][:production].nil?
          @cecrl_statistics[cecrl_prod][:production][:count] = @cecrl_statistics[cecrl_prod][:production][:count] + 1
          @cecrl_statistics[cecrl_prod][:production][:total_score] = @cecrl_statistics[cecrl_prod][:production][:total_score] + c.average_rating(:production)
          @cecrl_statistics[cecrl_prod][:production][:max_score] = c.average_rating(:production) if c.average_rating(:production) > @cecrl_statistics[cecrl_prod][:production][:max_score]
          @cecrl_statistics[cecrl_prod][:production][:min_score] = c.average_rating(:production) if c.average_rating(:production) < @cecrl_statistics[cecrl_prod][:production][:min_score]
        else
          @cecrl_statistics[cecrl_prod][:production] = {
            count: 1,
            total_score: c.average_rating(:production),
            max_score: c.average_rating(:production),
            min_score: c.average_rating(:production)
          }
        end
      end

      challenge[:cecrl_recep] = 'NA'
      c.questions.each do |q|
        if !q.cecrl_recep.nil? && q.cecrl_recep != challenge[:cecrl_recep]
          if challenge[:cecrl_recep] == 'NA'
            challenge[:cecrl_recep] = q.cecrl_recep
          elsif challenge[:cecrl_recep_multiple].nil?
            challenge[:cecrl_recep_multiple] = q.cecrl_recep
            challenge[:cecrl_recep] = "#{challenge[:cecrl_recep]}*"
          else
            challenge[:cecrl_recep_multiple] = "#{challenge[:cecrl_recep_multiple]},#{q.cecrl_recep}"
          end
        end
      end

      cecrl_recep = challenge[:cecrl_recep]

      if @cecrl_statistics
        if @cecrl_statistics[cecrl_recep].nil?
          @cecrl_statistics[cecrl_recep] = {
            reception: {
              count: 1,
              total_score: c.average_rating(:reception),
              max_score: c.average_rating(:reception),
              min_score: c.average_rating(:reception)
            }
          }
        elsif !@cecrl_statistics[cecrl_recep][:reception].nil?
          @cecrl_statistics[cecrl_recep][:reception][:count] = @cecrl_statistics[cecrl_recep][:reception][:count] + 1
          @cecrl_statistics[cecrl_recep][:reception][:total_score] = @cecrl_statistics[cecrl_recep][:reception][:total_score] + c.average_rating(:reception)
          @cecrl_statistics[cecrl_recep][:reception][:max_score] = c.average_rating(:reception) if c.average_rating(:reception) > @cecrl_statistics[cecrl_recep][:reception][:max_score]
          @cecrl_statistics[cecrl_recep][:reception][:min_score] = c.average_rating(:reception) if c.average_rating(:reception) < @cecrl_statistics[cecrl_recep][:reception][:min_score]
        else
          @cecrl_statistics[cecrl_recep][:reception] = {
            count: 1,
            total_score: c.average_rating(:reception),
            max_score: c.average_rating(:reception),
            min_score: c.average_rating(:reception)
          }
        end
      end

      @challenges << challenge
    end
  end

  def index_time_statistics
    @challenges = []
    @record_length = []
    @average_recording_length = {}
    @time_allowed_recording = {}
    @main_count = {}
    @over_count = {}
    @under_count = {}
    @reported_productions = {}

    @all_challenges = Challenge.all
    @all_challenges.each do |ac|
      @challenges << ac if ac.published?
    end
    @time_statistics = {}

    @challenges.each do |c|
      @questions = c.questions.where(question_for: 0)

      # Stats on recordings

      if @questions.where(type: Questions::QMicRecorder).any?
        @time_allowed_recording[c[:id]] = @questions.where(type: 'Questions::QMicRecorder').first.max_duration.to_s
        @main_count[c[:id]] = 0 #
        @over_count[c[:id]] = 0 # Compteurs pour le nombre d'enregistrements < et > 1.5s
        @under_count[c[:id]] = 0
        @average_recording_length[c[:id]] = []
        @reported_productions[c[:id]] = c.productions.reported.count
        c.productions.completed.each do |p|
          next if p.recording.nil?

          @record_length[p.id] = p.recording.data_file_size / MAGIC_RATIO_TO_GUESS_RECORDING_DURATION
          @average_recording_length[c[:id]] << @record_length[p.id]
          @main_count[c[:id]] += 1
          if @record_length[p.id] < 1.5
            @under_count[c[:id]] += 1
          elsif @record_length[p.id] > @questions.where(type: 'QMicRecorder').first.max_duration - 1.5
            @over_count[c[:id]] += 1
          end
        end
        @average_recording_length[c[:id]] = if @main_count[c[:id]].zero?
                                              'NO RECORDING'
                                            else
                                              @average_recording_length[c[:id]].sum / @average_recording_length[c[:id]].size
                                            end
      else
        @time_allowed_recording[c[:id]] = 'Q SANS MICRO'
      end

      # End of stats on recordings

      # Stats on challenge Production/Reception answering time
      @time_statistics[c[:id]] = {
        times_spent_reception: [],
        average_time_spent_reception: 0,
        time_allowed_answer_reception: c.reception_duration / 1000,
        time_out_reception: c.receptions.timeout.count, # avec answered_at les receptions qui ont été réalisé avant sont quand même comptabilisées
        times_spent_production: [],
        average_time_spent_production: 0,
        time_allowed_answer_production: c.production_duration / 1000,
        time_out_production: c.productions.timeout.count # avec answered_at les productions qui ont été réalisé avant sont quand même comptabilisées
      }
      # RECEPTIONS (a afficher avec un answered_at comme les productions : @current_questionable.update_attribute(:answered_at, Time.now)) dans le question_controller)
      c.receptions.each do |r|
        next unless !r.displayed_at.nil? && Result.where(reception_id: r.id).any?

        @time_statistics[c[:id]][:times_spent_reception] << (Result.where(reception_id: r.id).first.created_at - r.displayed_at).to_i if (Result.where(reception_id: r.id).first.created_at - r.displayed_at).to_i.positive? && (Result.where(reception_id: r.id).first.created_at - r.displayed_at).to_i < @time_statistics[c[:id]][:time_allowed_answer_reception] * 2
      end
      @time_statistics[c[:id]][:average_time_spent_reception] = if @time_statistics[c[:id]][:times_spent_reception].size.zero?
                                                                  'NO ANSWER'
                                                                else
                                                                  @time_statistics[c[:id]][:times_spent_reception].sum / @time_statistics[c[:id]][:times_spent_reception].size
                                                                end

      # PRODUCTION
      c.productions.each do |p|
        next unless !p.displayed_at.nil? && !p.answered_at.nil?

        @time_statistics[c[:id]][:times_spent_production] << (p.created_at - p.displayed_at).to_i if (p.answered_at - p.displayed_at).to_i.positive? && (p.answered_at - p.displayed_at).to_i < @time_statistics[c[:id]][:time_allowed_answer_production] * 2
      end
      @time_statistics[c[:id]][:average_time_spent_production] = if @time_statistics[c[:id]][:times_spent_production].size.zero?
                                                                   'NO ANSWER'
                                                                 else
                                                                   @time_statistics[c[:id]][:times_spent_production].sum / @time_statistics[c[:id]][:times_spent_production].size
                                                                 end
      # End of stats on Production/Reception answering time
    end
  end

  def update
    if params[:domain]
      @challenge.domain = params[:domain].split(',').first
      @element = 'domain'
    end

    if params[:type]
      @challenge.type = params[:type].split(',').first
      @element = 'type'
    end

    if params[:skills]
      @challenge.skills = params[:skills]
      @element = 'skills'
    end

    respond_to do |format|
      format.html
      format.js
    end
  end

  def production
    # Get language parameter from URL to translate instructions and question content
    @test_language = params[:lang] || 'en'

    # Load challenge and generate productiotn
    @current_questionable = if @challenge.productions.ready.empty?
                              @challenge.generate_ready_production
                            else
                              @challenge.productions.ready.first
                            end
    render template: @challenge.layout_for(:production).template_name
  end

  def reception
    @current_questionable = if @challenge.receptions.empty?
                              Reception.create({
                                                 production_id: @challenge.productions.first.id,
                                                 challenge_id: @challenge.id,
                                                 status: Production.statuses['unready']
                                               })
                            else
                              @challenge.receptions.first
                            end
    render template: @challenge.layout_for(:reception).template_name
  end

  def infos_production
    @questions = @challenge.questions.where(question_for: 0)
    @record_length = []

    # Regarde si la production n'est pas nul pour donner les stats : @average_recording_length, @time_allowed_recording et le nombre d'enregistrements < et > 1.5s
    if @questions.where(type: Questions::QMicRecorder).any?
      @time_allowed_recording = "Temps alloué pour s'enregistrer : #{@questions.where(type: 'Questions::QMicRecorder').first.max_duration} s."
      @main_count = 0 #
      @over_count = 0 # Compteurs pour le nombre d'enregistrements < et > 1.5s
      @under_count = 0
      @challenge.productions.order(displayed_at: :desc).each do |c|
        next if c.recording.nil?

        @record_length[c.id] = (c.recording.data_file_size / MAGIC_RATIO_TO_GUESS_RECORDING_DURATION).to_f
        @average_recording_length = @average_recording_length.to_f + @record_length[c.id].to_f
        @main_count += 1
        if @record_length[c.id] < 1.5
          @under_count += 1
        elsif @record_length[c.id] > @questions.where(type: 'Questions::QMicRecorder').first.max_duration - 1.5
          @over_count += 1
        end
      end
      if @main_count.zero?
        @average_recording_length = "Le temps moyen de réponse est d'environ : PAS DE RECORDINGS DISPONIBLE"
      else
        @average_recording_length = "Le temps moyen de réponse est d'environ : #{(@average_recording_length.to_f / @main_count).round} s sur #{@main_count} enregistrement(s)."
        @time_over_under = "Il y a #{@under_count} enregistrement(s) en dessous de 'min_time + 1,5s' et #{@over_count} enregistrement(s) au dessus de 'max_time - 1,5s'"
      end
    else
      @time_allowed_recording = "Temps alloué pour s'enregistrer : QUESTION SANS MICRO"
    end
  end

  def infos_reception
    @questions = @challenge.questions.where(question_for: 1)
    @total_count = []
    @valid_answer = []
    @rate_success = []
    @average_score = []

    # On parcour toutes les questions pour afficher les stats : score moyen, pourcentagede réponses correctes
    # et question_elements pour afficher les stats : pourcentage de réponses correctes, nombre de users qui ont répondu ce qe et si la réponse est vraie ou fausse
    @questions.each do |q|
      @main_count = 0
      q.question_elements.unscoped.where(question_id: q.id).find_each do |qe|
        @answer_count = ChallengeLog.where(rated: true).where(question_element_id: qe.id).count
        @success_count = ChallengeLog.where(rated: true).where(question_element_id: qe.id).where(success: true).count
        if %w[1 true].include?(qe.static_valid_answer)
          @valid_answer[qe.id] = ' | True'
          @user_count = @success_count
        #            elsif qe.content == {"word"=>"The answer is not shown"}
        #                @user_count = @answer_count.to_i - @success_count.to_i
        #                if @answer_count != 0
        #                    @total_count[qe.id] = " | Nombre de personnes ayant coché la case : " + @user_count.to_s + "/" + @answer_count.to_s
        #                 else
        #                    @total_count[qe.id] = " | pourcentage de réponses correctes : NO RECEPTIONS"
        #                end
        else
          @valid_answer[qe.id] = ' | False'
          @user_count = @answer_count.to_i - @success_count.to_i
        end
        if @answer_count.zero?
          @total_count[qe.id] = ' | pourcentage de réponses correctes : NO RECEPTIONS'
        else
          @total_count[qe.id] = " | pourcentage de réponses correctes : #{@success_count.to_i * 100 / @answer_count.to_i} % | nombre de users qui ont répondu ça : #{@user_count}/#{@answer_count}"
          @rate_success[q.id] = (@rate_success[q.id].to_i + (@success_count.to_i * 100 / @answer_count.to_i)).to_s
          @average_score[q.id] = (@average_score[q.id].to_i + qe.score.rating).to_s
        end
        @main_count += 1
      end
      if @main_count.zero?
        @rate_success[q.id] = 'Pourcentage moyen de réponses correctes : PAS DE REPONSES DISPONIBLES'
        @average_score[q.id] = 'Score moyen pour cette question : PAS DE SCORES DISPONIBLES'
      else
        @rate_success[q.id] = "Pourcentage moyen de réponses correctes : #{(@rate_success[q.id].to_i / @main_count).round} %"
        @average_score[q.id] = "Score moyen pour cette question : #{@average_score[q.id].to_i / @main_count}"
      end
    end
  end

  def edit_production
    @questions = @challenge.questions.where(question_for: 0)
    # récupère tout les question_element de la reception d'un challenge

    # Si on update un question_element
    if !params['question_elements'].nil?
      @type_question = 0 # La variable type_question est util pour renvoyer a un javascript précis
      params['question_elements'].each_pair do |qe_key, qe_value| # util pour les éléments qui ont deux attributs dans leurs Hash
        @question_element = QuestionElement.find(qe_key.to_i)

        qe_value.each_pair do |content_key, content_value|
          content = @question_element.content.dup
          content_value = content_value.split(',').map(&:to_s) if content_key == 'forbidden_words'
          content[content_key] = content_value
          @success = if @question_element.content[content_key] == content_value
                       1
                     elsif @question_element.update_attribute(:content, content)
                       0
                     else
                       2 # La variable success est util pour renvoyer a un javascript précis
                     end
        end
      end
    # Si on update un label
    elsif !params['question_labels'].nil?
      @type_question = 1 # La variable type_question est util pour renvoyer a un javascript précis
      params['question_labels'].each_pair do |key, value| # util pour les éléments qui ont deux attributs dans leurs Hash
        @question_label = Question.find(key.to_i)
        @success = if @question_label.label == value
                     1
                   elsif @question_label.update_attribute(:label, value)
                     0
                   else
                     2 # La variable success est util pour renvoyer a un javascript précis
                   end
      end
    end

    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def edit_reception
    @questions = @challenge.questions.where(question_for: 1)
    # récupère tout les question_element de la reception d'un challenge

    # Si on update un question_element
    if !params['question_elements'].nil?
      @type_question = 0 # La variable type_question est util pour renvoyer a un javascript précis
      params['question_elements'].each_pair do |qe_key, qe_value| # util pour les éléments qui ont deux attributs dans leurs Hash
        @question_element = QuestionElement.find(qe_key.to_i)
        @question = Question.find(@question_element.question_id)
        qe_value.each_pair do |content_key, content_value|
          if content_key == 'static_valid_answer'
            # static_valid_answer = @question_element.static_valid_answer.dup
            static_valid_answer = content_value
            if @question_element.static_valid_answer == content_value
              @success = 1 unless [0, 3, 4].include?(@success)
            elsif @question.has_static_valid_answer?(@question_element)
              @success = 3
            elsif @question_element.update_attribute(:static_valid_answer, static_valid_answer)
              @question_element.behind_islinked_update if @question_element.islinked?
              if @question.is_a?(Questions::QRadioWordsRecorder) || @question.is_a?(Questions::QRadioImagesRecorder) || @question.is_a?(Questions::QDropDown) || @question.is_a?(Questions::QRadioTextsRecorder)
                @question.question_elements.where.not(id: @question_element.id).find_each do |qe|
                  qe.update_attribute(:static_valid_answer, 'false')
                end
                @success = 4
              else
                @success = 0
              end
            else
              @success = 2 # La variable success est util pour renvoyer a un javascript précis
            end
          else
            content = @question_element.content.dup
            content[content_key] = content_value
            if @question_element.content[content_key] == content_value
              @success = 1 if @success != 0 && @success != 3 && @success != 4
            elsif @question_element.update_attribute(:content, content)
              @question_element.behind_islinked_update if @question_element.islinked?
              @success = 0
            else
              @success = 2 # La variable success est util pour renvoyer a un javascript précis
            end
          end
        end
      end
    # Si on update un label
    elsif !params['question_labels'].nil?
      @type_question = 1 # La vraiable type_question est util pour renvoyer a un javascript précis
      params['question_labels'].each_pair do |key, value| # util pour les éléments qui ont deux attributs dans leurs Hash
        @question_label = Question.find(key.to_i)
        @success = if @question_label.label == value
                     1
                   elsif @question_label.update_attribute(:label, value)
                     0
                   else
                     2 # La variable success est util pour renvoyer a un javascript précis
                   end
      end
    end
    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def add_question_element
    unless params['question_id'].nil?
      @question = Question.find(params['question_id'].to_i)

      @question_element = @question.build_question_element_default

      #      @question_element = QECheckbox.create({
      #        question_id: @question.id,
      #        order: QuestionElement.where(:question_id => params["question_id"]).count,
      #        scorable: true,
      #        content: {value: ""},
      #        static_valid_answer: "0",
      #        type: "QECheckbox"
      #      })

      @current_page = params['current_page']
    end

    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def remove_question_element
    unless params['question_id'].nil?
      @question = Question.find(params['question_id'])
      @question_element = QuestionElement.find(params['question_element_id'])

      list_of_exceptions = ['Questions::QHtml', 'Questions::QDateTime', 'Questions::QImage', 'Questions::QMicRecorder', 'Questions::QInstruction', 'Questions::QTextFieldRecorder', 'Questions::QTextArea', 'Questions::QText']
      # List of Question who can have only one QE : QHtml QDateTime QImage QMicRecorder QInstruction QTextFieldRecorder QTextArea QText

      if @question.has_static_valid_answer?(@question_element)
        @success = 1
      elsif list_of_exceptions.include?(@question.type.to_s) && @question.question_elements.count <= 1
        @success = 3
      elsif !list_of_exceptions.include?(@question.type.to_s) && @question.question_elements.count <= 2
        @success = 2
      else
        @question_element.behind_islinked_remove if @question_element.islinked?
        @question_element.unpublished!
        @success = 0 # La variable type_question est util pour renvoyer a un javascript précis
      end
    end

    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def create_image
    @type_question = 0 # La variable type_question est util pour renvoyer a un javascript précis
    if params['question_elements'].nil?
      @success = 1
    else
      params['question_elements'].each_pair do |qe_key, qe_value| # util pour les éléments qui ont deux attributs dans leurs Hash
        @question_element = QuestionElement.find(qe_key.to_i)
        qe_value.each_pair do |_content_key, content_value|
          # Create image object
          image = Image.create({
                                 data: content_value,
                                 name: 'image_name'
                               })
          @question_element.content['image_id'] = image.id
          @question_element.save
          if @question_element.save
            @question_element.behind_islinked_update if @question_element.islinked?
            @success = 0
          else
            @success = 2 # La variable success est util pour renvoyer a un javascript précis
          end
        end
      end
    end
    session[:return_to] ||= request.referer
    redirect_to session.delete(:return_to)
  end

  def import
    linguist_id = params[:linguist_id].to_i
    path_to_file = Rails.root.join('db', 'questions', 'challenges', "#{linguist_id}.rb")
    if linguist_id.present? && File.exist?(path_to_file)
      Challenge.find_by_linguist_id(linguist_id)&.destroy
      @new_challenge = (Challenge.find_by_linguist_id(linguist_id) if load path_to_file)
    end
    import_folder = 'db/questions/challenges/'
    @challenges_files = Dir.glob("#{import_folder}*")
    @available_challenges = []
    @challenges_files.each do |ac|
      linguist_id = ac.gsub('.rb', '').gsub(import_folder, '')
      @available_challenges << {
        filename: ac,
        linguist_id: linguist_id,
        challenge_status: !Challenge.where(linguist_id: linguist_id).empty?,
        published_status: Challenge.where(linguist_id: linguist_id).empty? ? '' : Challenge.find_by(linguist_id: linguist_id).status
      }
    end
  end

  def publication
    @challenge.published? ? @challenge.unpublished! : @challenge.published!

    respond_to do |format| # pour faire marcher :remote => true
      format.html
      format.js
    end
  end

  def show
    @filter = params[:filter] || {}
    @show_stats = params[:show_stats].present?
    @productions = @challenge.productions_answered_by_language(@filter[:language]) if params[:show_productions].present?
  end

  def duplicate
    new_challenge = Challenge.duplicator(source: @challenge.linguist_id, destination: params[:destination_linguist_id])
    if new_challenge.invalid?
      flash[:error] = new_challenge.errors.full_messages.to_sentence
      redirect_to admin_challenge_path(@challenge)
    else
      redirect_to admin_challenge_path(new_challenge)
    end
  end

  private

  def challenge
    @challenge = Challenge.find(params[:id])
  end
end
