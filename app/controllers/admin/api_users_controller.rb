class Admin::ApiUsersController < Admin::AdminController
  include Restful<PERSON><PERSON>roller

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :api_user
  end

  def identifier
    :name
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :name,
      :default_locale,
      :default_test_language,
      :default_test_profile_id,
      :force_test_mode,
      :access_control_allow_origin,
      :access_control_allow_methods,
      :force_api_user_brand,
      :force_identity_check,
      :days_before_test_instance_cancellation,
      :api_ti_creation_requires_rc,
      :identity_provider_id,
      api_oauth_manager_attributes: [:id, :login_name, :passphrase, :access_token_url, :access_token]
    ]
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # PUT /admin/resources/1/activate
  # PUT /admin/resources/1/activate.json
  def activate
    respond_to do |format|
      if @resource.activate!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/expire
  # PUT /admin/resources/1/expire.json
  def expire
    respond_to do |format|
      if @resource.expire!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/generate_authentication_token
  # PUT /admin/resources/1/generate_authentication_token.json
  def generate_authentication_token
    respond_to do |format|
      if @resource.generate_authentication_token!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/generate_passphrase
  # PUT /admin/resources/1/generate_passphrase.json
  def generate_passphrase
    respond_to do |format|
      if @resource.refresh_passphrase!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # GET /admin/resources
  # GET /admin/resources.json
  def index
    @resources = if klass.new.attributes.key?('created_at')
                   klass.where(type: nil).includes(resource_includes).order(created_at: index_sort_order)
                 else
                   klass.where(type: nil).includes(resource_includes)
                 end

    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end
end
