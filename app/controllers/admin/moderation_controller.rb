class Admin::ModerationController < Admin::AdminController
  def index
    ## List all production in two separated groups
    ## Group 1 - Production with recording
    ## Groupe 2 - Production without a recording (ie : written)
    @recording_productions = []
    @written_productions = []
    Production.waiting_moderation.each do |production|
      # In case the user has been deleted since the production, do not show it
      next if User.where(:id => production.user_id).empty?

      if production.has_recording?
        @recording_productions << production
      else
        @written_productions << production
      end
    end
  end

  def update
    @status_count = {
      :valid => 0,
      :bad_audio => 0,
      :no_sound => 0,
      :innapropriate => 0,
      :text_empty => 0,
      :bad_text => 0
    }
    params[:moderation_status].each_pair do |id, status|
      p = Production.find(id)
      ## Todo, add specific reason of reporting to the recording as an attribute
      case status
      when 'valid'
        p.do_completed!
        @status_count[:valid] += 1
      when 'bad_audio'
        p.reported!
        @status_count[:bad_audio] += 1
      when 'no_sound'
        p.reported!
        @status_count[:no_sound] += 1
      when 'innapropriate'
        p.reported!
        @status_count[:innapropriate] += 1
      when 'bad_text'
        p.reported!
        @status_count[:bad_text] += 1
      when 'text_empty'
        p.reported!
        @status_count[:text_empty] += 1
      else
        raise Error
      end
    end
  end
end
