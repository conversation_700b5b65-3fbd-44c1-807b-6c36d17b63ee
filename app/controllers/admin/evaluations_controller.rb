class Admin::EvaluationsController < Admin::AdminController
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  before_action :select_fields, except: %i[index create new stuck_evaluations_index overdue_evaluations_index create_invoice_requests evaluation_payment_index send_email_evaluations_review_required]
  before_action :define_form_options, except: %i[index stuck_evaluations_index evaluation_payment_index send_email_evaluations_review_required]

  def object
    :evaluation
  end

  def identifier
    :id
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :payment_status,
      :payment_type,
      :evaluation_goal,
      :evaluation_delay_id,
      :test_instance_id,
      :examiner_id,
      :urgent,
      :assessment_type_id,
      :invoice_request_id,
      { tag_ids: [] }
    ]
  end

  def resource_includes
    @inclusions || []
  end

  def show
    # template dont manage select need QUERY Evaluation::SELECT_QUERY_EDIT
    edit
  end

  def create
    # TODO : use the updated way to add Evaluations to TIs
    #       test_instance = TestInstance.find(params[:evaluation][:test_instance_id])
    #       if test_instance.add_evaluation(post_params)
    #         ...
    @resource = Evaluation.new_for_test_instance(TestInstance.find(params[:evaluation][:test_instance_id]))
    if @resource&.update(post_params)
      EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} created the evaluation #{@resource.id}", :warning)
      redirect_to admin_evaluation_path @resource
    else
      respond_to do |format|
        format.html { render template: new_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  def new
    @resource = Evaluation.new(test_instance_id: params[:test_instance])
    respond_to do |format|
      format.html { render template: new_template_path }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  def edit
    @examiners = Examiner.includes(:user).available.where({ status: [Examiner.statuses[:initialized], Examiner.statuses[:trusted], Examiner.statuses[:active], Examiner.statuses[:on_trial_period]], language: @resource.test_language })
    respond_to do |format|
      format.html { render template: edit_template_path }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  def index
    @limits = [100, 500, 1000, 2000, 3000]
    @limit = params[:limit] || @limits.first
    @search_language = params[:search_language]

    @eval_states = Evaluation.aasm.states.map(&:name)
    @selected_eval_states = params[:selected_evaluation_states] || @eval_states

    @limit = @limit.to_i

    @test_instance_status = TestInstance.statuses
    @ti_selected_status = params[:selected_test_instance_statuses] || @test_instance_status

    @eval_goals = Evaluation::EVALUATION_GOAL.values
    @selected_eval_goals = params[:selected_evaluation_goals] || @eval_goals

    @eval_tags = Tag.get_all_as_hash.merge({ 'any' => 'any' })
    @selected_eval_tags = params[:selected_tags] || 'any'
    @assessment_types = AssessmentType.all.to_h { |at| [at.id, at] }

    @resources = Evaluation.includes(
      [{ examiner: :user }, { test_instance: :test_profile }, :tags]
    ).select(
      'evaluations.*, test_instances.test_language AS test_language'
    ).joins(
      :test_instance
    ).where(
      evaluations: { status: @selected_eval_states }
    ).where(
      evaluation_goal: @selected_eval_goals
    ).where(
      test_instances: { status: @ti_selected_status }
    )

    @resources = @resources.joins(:tags).where(tags: { id: @selected_eval_tags }) unless @selected_eval_tags.include?('any')

    @resources = @resources.order('evaluations.updated_at DESC').limit(@limit)

    @resources = @resources.where(test_instances: { test_language: params[:search_language] }) if params[:search_language] && !params[:search_language].empty?

    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end

  def evaluation_payment_index
    go_back = params[:go_back].blank? ? 1 : params[:go_back].to_i
    date = (Time.zone.now - go_back.month).end_of_month

    @resources = Evaluation.includes(examiner: :user).available_for_payment_before(date)

    render template: '/admin/evaluations/index_payment'
  end

  def create_invoice_requests
    CreateInvoiceRequestsJob.perform_later(params[:evaluation_ids])

    respond_to do |format|
      format.html { redirect_to admin_invoice_requests_path, notice: 'Invoice Requests created succefully !' }
      format.json { render json: { status: 200 } }
    end
  end

  def stuck_evaluations_index
    @resources = Evaluation.includes(:certificate, { examiner: :user }).tag_and_return_stuck_evaluations

    respond_to do |format|
      format.html {}
      format.json { render json: @resources }
    end
  end

  def overdue_evaluations_index
    @resources = Evaluation.joins(:test_instance)
                           .where(status: %i[overdue soon_overdue])
                           .where.not(evaluation_goal: [Evaluation::EVALUATION_GOAL[:peer_review], Evaluation::EVALUATION_GOAL[:holding_peer_review]])
                           .where.not(test_instances: { status: :graded })
                           .where.not(test_instances: { status: :cancelled })
                           .order('test_instance_id ASC')

    respond_to do |format|
      format.html {}
      format.json { render json: @resources }
    end
  end

  def check_evaluation_for_certificate_issue
    CheckEvaluationForCertificateIssueJob.perform_later(@resource.id, true)
    respond_to do |format|
      format.html { render template: edit_template_path }
      format.json { render json: { status: 200 } }
    end
  end

  def assign_to_examiner
    begin
      @resource.assign! 'assigned_at'
    rescue AASM::InvalidTransition
      EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} failed to assign the evaluation #{@resource.id} to examiner", :danger)
      respond_to do |format|
        format.html { redirect_to admin_evaluation_path(@resource), flash: { error: 'Examiner cannot be assigned' } }
        format.json { render json: { status: :unprocessable_entity } }
      end
      return
    end
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} assigned the evaluation #{@resource.id} to examiner", :warning)
    respond_to do |format|
      format.html { redirect_back fallback_location: admin_root_path, notice: 'Examiner has been assigned' }
      format.json { render json: @resource }
    end
  end

  def deliver_to_client
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} delivered the evaluation #{@resource.id} to client", :warning)
    GenerateEvaluationCertificateJob.perform_now(@resource.id, false, false)
    if params[:from] && params[:from] == 'stuck_evaluations'
      respond_to do |format|
        format.html { redirect_to stuck_evaluations_index_admin_evaluations_path, notice: 'Evaluation has been delivered' }
        format.json { render json: @resource }
        format.js
      end
    else
      respond_to do |format|
        format.html { redirect_back fallback_location: admin_root_path, notice: 'Evaluation has been delivered' }
        format.json { render json: @resource }
      end
    end
  end

  def confirm_initial_grade
    if @resource.regrading?
      @resource.cancel!
      @resource.regrade_confirmation
      EventLog.add(@resource, :admin, "#{current_user&.full_name} confirmed the initial grade.", :warning)
      flash[:notice] = 'Initial grade confirmed'
    else
      flash[:error] = 'Initial grade unconfirmed'
    end
    redirect_back fallback_location: stuck_evaluations_index_admin_evaluations_path
  end

  def cancel_evaluation
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} cancelled the evaluation #{@resource.id}", :warning)
    @resource.cancel!('canceled_at')
    respond_to do |format|
      format.html { redirect_back fallback_location: admin_root_path, notice: 'Evaluation has been cancelled' }
      format.json { render json: @resource }
    end
  end

  def to_pay
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} changed the to paid status of evaluation #{@resource.id} to #{params[:to_pay]}", :warning)
    if params[:to_pay] == 'true'
      @resource.to_pay!
    else
      params[:to_pay] == 'false'
      @resource.to_not_pay!
    end

    respond_to do |format|
      format.html { redirect_back fallback_location: admin_root_path, notice: 'Evaluation has been marked as paid' }
      format.json { render json: @resource }
    end
  end

  def force_assign
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} forced status assign of evaluation #{@resource.id}", :warning)

    begin
      @resource.force_assign! 'assigned_at'
    rescue AASM::InvalidTransition
      EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} failed to force assign the evaluation #{@resource.id} to examiner", :danger)
      respond_to do |format|
        format.html { redirect_to admin_evaluation_path(@resource), flash: { error: 'Evaluation cannot transition to assigned' } }
        format.json { render json: { status: :unprocessable_entity } }
      end
      return
    end

    respond_to do |format|
      format.html { redirect_back fallback_location: admin_root_path, notice: 'Evaluation has been forced assigned' }
      format.json { render json: @resource }
    end
  end

  def force_assign_to_examiner
    if @resource.assigned?
      message = "#{current_user&.first_name} #{current_user&.last_name} forced examiner assignment was unsuccessful : evaluation #{@resource.id} is already assigned"
      EventLog.add(@resource.test_instance, :admin, message, :danger)
      flash[:error] = message
      respond_to do |format|
        format.html { redirect_to admin_evaluation_path(@resource), flash: { error: message } }
        format.json { render json: message, status: :unprocessable_entity }
      end
    else
      EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} forced assignment examiner of evaluation #{@resource.id}", :warning)
      @resource.force_assign!('assigned_at')
      respond_to do |format|
        format.html { redirect_back fallback_location: admin_root_path, notice: 'Examiner has been forced assigned' }
        format.json { render json: @resource }
      end
    end
  end

  def generate_certificate
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} generated certificate of evaluation #{@resource.id}", :warning)
    test_instance = @resource.test_instance

    ## Check if all necessary data is here, otherwise send an alert
    if @resource.certificate.nil?
      message = "Current Evaluation delivered is : #{@resource.id}"
      Alert.system('Evaluation is delivered but do not have a certificate', message)
      return
    end

    certificate_parameters = @resource.get_certificate_parameters

    locale = if params[:locale].present?
               params[:locale]
             else
               certificate_parameters['locale']
             end

    grades = @resource.certificate.grades

    I18n.with_locale(locale) do
      respond_to do |format|
        format.pdf do
          render pdf: 'certificate',
                 margin: {
                   top: 0, # default 10 (mm)
                   bottom: 0,
                   left: 0,
                   right: 0
                 },
                 show_as_html: params.key?('debug'),
                 orientation: certificate_parameters['orientation'],
                 template: certificate_parameters['template'],
                 dpi: '300',
                 locals: { evaluation: @resource, test_instance: test_instance, user: test_instance.user, cecrl_score: (grades.empty? ? nil : grades.overall.last.cecrl_score), full_score: (grades.empty? ? nil : grades.overall.last.score), brand: Brand.find_by_id(certificate_parameters['brand_id']) }
        end
      end
    end
  end

  def reload_certificate
    @resource.generate_and_save_certificate

    respond_to do |format|
      format.html { redirect_back fallback_location: admin_root_path }
      format.json { render json: @resource }
    end
  end

  def deliver_no_answers_evaluation
    pass_checks = true if params[:pass_checks].nil?
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} created a no answer eval of evaluation #{@resource.id}", :warning)
    @resource.deliver_no_answers_evaluation(pass_checks:)
    respond_to do |format|
      format.html { redirect_to edit_admin_test_instance_path(@resource.test_instance_id) }
      format.js
    end
  end

  def clone_and_assign_note_manually
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} cloned and assigned note manually of evaluation #{@resource.id}", :warning)
    # TODO : use the updated way to add Evaluations to TIs
    #       @resource.test_instance.add_and_force_assign_evaluation(
    #         ...
    #       )

    new_eval = @resource.dup
    new_eval.get_json_assessment_in_keys
    new_eval.update({ status: 'created', invoice_request_id: nil })
    admin = Examiner.pipplet_admin_examiner(@resource.test_instance.test_language)
    EventLog.add(new_eval, :admin, 'clone_and_assign_note_manually# Assign admin examiner', :info)
    new_eval.examiner_id = admin.id
    new_eval.assign_to_admin!('assigned_at')
    new_eval.save!
    @resource.cancel!('canceled_at') if @resource.created? || @resource.assigned?
    @resource = new_eval
    respond_to do |format|
      format.html { redirect_to examiner_evaluation_path(@resource) }
      format.json { render json: new_eval }
    end
  end

  def clone_and_assign_change_score
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} cloned and changed score of evaluation #{@resource.id}", :warning)
    change_level = params[:change_level].to_i
    grade_labels = params[:grade_labels]

    return redirect_to(admin_evaluation_path(@resource), flash: { alert: 'Select at least one grade label' }).then { return } if grade_labels.blank?

    new_eval = @resource.duplicate_with_adjusted_score(change_level, grade_labels, @resource.evaluation_goal)

    @resource.cancel!('canceled_at') if @resource.created? || @resource.assigned?
    @resource = new_eval
    respond_to do |format|
      format.html { redirect_to admin_evaluation_path(@resource) }
      format.json { render json: @resource }
    end
  end

  def create_teki
    EventLog.add(@resource.test_instance, :admin, "#{current_user&.first_name} #{current_user&.last_name} created teki on evaluation #{@resource.id}", :warning)
    CreateTechnicalIssueJob.perform_later(test_instance_id: @resource.test_instance.id, status: 'Admin report', from: 'admin', admin_email: current_user.email)

    redirect_back(fallback_location: admin_root_path, flash: { alert: 'Teki sent' })
  end

  def send_email_evaluations_review_required
    evaluations = Evaluation.where(id: params[:evaluations_ids])
    grouped_eval = evaluations.group_by(&:examiner)

    evaluations.update_all(presented_for_review: true, presented_for_review_at: Time.now)
    grouped_eval.each do |examiner, evals|
      ExaminerMailer.new_evaluations_presented_for_review(examiner, evals).deliver_later
    end

    respond_to do |format|
      format.html { redirect_to compare_evaluations_admin_examiners }
      format.json { render json: { evaluation_ids: evaluations.pluck(:id) } }
    end
  end

  private

  def post_params
    params.require(:evaluation).permit(:evaluation_delay_id, :payment_type, :evaluation_goal)
  end

  def select_fields
    @resource = Evaluation
                .joins(:test_instance, :examiner, :evaluation_delay)
                .select(Evaluation::SELECT_QUERY_EDIT)
                .find(params[:id])
    @examiners = Examiner.usable.where(language: @resource.test_language)
  end

  def define_form_options
    @evaluation_delays = EvaluationDelay.select('evaluation_delays.id, evaluation_delays.delay_label')
    @assessment_types = AssessmentType.all
    @tags = Tag.all.sort_by { |t| t.id.in?(@resource&.tag_ids || []) ? 0 : 1 }
  end
end
