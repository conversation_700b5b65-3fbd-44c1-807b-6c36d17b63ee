class Admin::IdentityProvidersController < Admin::AdminController
  include Restful<PERSON>ontroller

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :identity_provider
  end

  def identifier
    :name
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :metadata_url,
      :metadata_xml,
      :name,
      :status,
      :vendor,
      :sp_entity_id,
      :sso_user_id,
      :first_name_saml_attribute,
      :last_name_saml_attribute,
      :order_reference_saml_attribute,
      :create_ti_on_first_sign_in
    ]
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # PUT /admin/resources/1/activate
  # PUT /admin/resources/1/activate.json
  def activate
    respond_to do |format|
      if @resource.activate!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/expire
  # PUT /admin/resources/1/expire.json
  def expire
    respond_to do |format|
      if @resource.expire!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/generate_authentication_token
  # PUT /admin/resources/1/generate_authentication_token.json
  def generate_authentication_token
    respond_to do |format|
      if @resource.generate_authentication_token!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/generate_passphrase
  # PUT /admin/resources/1/generate_passphrase.json
  def generate_passphrase
    respond_to do |format|
      if @resource.refresh_passphrase!
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /admin/resources/1
  # PATCH/PUT /admin/resources/1.json
  def update
    respond_to do |format|
      if @resource.update(secure_params)
        format.html { redirect_to send(resource_url, @resource), notice: 'Update successful!' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end
end
