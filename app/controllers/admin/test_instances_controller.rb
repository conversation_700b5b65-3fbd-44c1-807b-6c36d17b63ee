class Admin::TestInstancesController < Admin::AdminController
  include <PERSON><PERSON><PERSON><PERSON>roller

  before_action :test_stats, only: %i[edit show]
  before_action :define_form_options, except: %i[new index create]
  prepend_before_action :define_resource_inclusions_for_edit, only: %i[edit]
  prepend_before_action :define_resource_inclusions_for_show, only: %i[show]

  # ----------------------------------------
  # :section: Configuration variables
  # ----------------------------------------
  def object
    :test_instance
  end

  def identifier
    :id
  end

  def controller_namespace
    :admin
  end

  def permitted_params
    [
      :id,
      :user_id,
      :test_profile_id,
      :direct_user_id,
      :test_language,
      :result_cecrl,
      :result_rating,
      :detailed_evaluation,
      :report,
      :questionable_type,
      :questionable_id,
      :skip_audio_test,
      :skip_tutorial,
      :skip_identity_check,
      :skip_browser_check,
      :send_reminders,
      :examiner_id,
      :client_name,
      :test_mode,
      :question_set_id,
      :limit,
      :group,
      :pipplet_clients_campaign_id,
      :email,
      :test_instance,
      :user,
      :audit_trail,
      :skip_email,
      :trusted,
      :monitored,
      :time_multiplier,
      :test_language,
      { tag_ids: [] }
    ]
  end

  def permitted_search_params
    params.permit(
      :test_profile_id,
      :email,
      :api_user_id,
      :confirmation_token,
      :monitored,
      :test_mode,
      :ordering,
      :limit,
      :group,
      :commit,
      :date_filter,
      :beginning_date,
      :ending_date,
      status: [],
      test_language: []
    )
  end

  def define_resource_inclusions_for_edit
    @inclusions = [{ evaluations: [{ examiner: :user }, :certificate] }, { productions: [:challenge, :reception, :production_metadatum] }, { next_questionables: [{ production: :challenge }, :challenge] }]
  end

  def define_resource_inclusions_for_show
    @inclusions = :productions
  end

  def resource_includes
    @inclusions || []
  end

  # ----------------------------------------
  # :section: Override default Actions
  # ----------------------------------------
  # GET /admin/resources?test_profile_id=:test_profile_id
  # GET /admin/resources.json
  def index
    search_params = permitted_search_params
    @limits = [250, 500, 1000, 2000, 3000]
    @limit = search_params[:limit]&.to_i || @limits.first
    filters = {}

    @test_profiles = TestProfile.active.select(:id, :name).order(id: :desc)
    filters[:test_profile_id] = @test_profile_id = search_params[:test_profile_id] if search_params[:test_profile_id].present?

    @test_languages = AVAILABLE_TEST_LANGUAGES
    filters[:test_language] = @test_language = search_params[:test_language] || @test_languages

    @statuses = TestInstance.aasm.states.map(&:name)
    filters[:status] = @status = search_params[:status] || @statuses

    filters[:api_user_id] = search_params[:api_user_id] if search_params[:api_user_id].present?

    # TODO: include users conditionally
    (filters[:users] ||= {})[:group] = search_params[:group] if search_params[:group].present?
    (filters[:users] ||= {})[:confirmation_token] = search_params[:confirmation_token] if search_params[:confirmation_token].present?

    filters[:monitored] = @monitored = search_params[:monitored] == 'true' if search_params&.[](:monitored).present?
    filters[:test_mode] = @test_mode = search_params[:test_mode] == 'true' if search_params&.[](:test_mode).present?

    @ordering = search_params[:ordering] || 'created_at'

    if (beginning_date = search_params[:beginning_date]&.to_date).present? && (ending_date = search_params[:ending_date]&.to_date).present?
      filters[search_params[:date_filter]] = beginning_date..ending_date
    end

    @resources = ShardsHelper.reading_replica_on_production do
      resources = TestInstance.includes(:user, :test_profile)

      if search_params[:email].present?
        resources = resources.joins(:user).where("users.email ILIKE ?", "%#{search_params[:email].strip}%")
      end

      resources.where(filters).where.not("test_instances.#{@ordering}" => nil).order("test_instances.#{@ordering} DESC").limit(@limit)
    end

    respond_to do |format|
      format.html { render template: index_template_path }
      format.json { render json: @resources }
    end
  end

  # ----------------------------------------
  # :section: Actions
  # ----------------------------------------
  # PUT /admin/resources/1/redo_questionable
  # PUT /admin/resources/1/redo_questionable.json
  def redo_questionable
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} redone questionable #{secure_params[:questionable_id]}", :warning)

    if @resource.redo_questionable(secure_params[:questionable_type], secure_params[:questionable_id])
      respond_to do |format|
        format.html { redirect_to send(resource_url, @resource), notice: 'Questionable back in the list!' }
        format.json { head :no_content }
      end

    else
      respond_to do |format|
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  def redo_written
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} redone written questions", :warning)

    if @resource.redo_all(:written)
      respond_to do |format|
        format.html { redirect_to send(resource_url, @resource), notice: 'Questionables back in the list!' }
        format.json { head :no_content }
      end
    else
      respond_to do |format|
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  def assign_evaluation_to_all_examiners
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} assigned evaluation to all examiner", :warning)
    @resource.assign_to_all_examiners
    redirect_to send(resource_url, @resource), notice: 'Peer Review evaluations was assigned to all examiners'
  end

  def redo_audio
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} redone audio questions", :warning)

    if @resource.redo_all(:audio)
      respond_to do |format|
        format.html { redirect_to send(resource_url, @resource), notice: 'Questionables back in the list!' }
        format.json { head :no_content }
      end
    else
      respond_to do |format|
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/recompute_next_questionables
  # PUT /admin/resources/1/recompute_next_questionables.json
  def recompute_next_questionables
    EventLog.add(@resource, :redo, "#{current_user&.first_name} #{current_user&.last_name} recomputed next questionables", :warning)

    @resource.update_next_questionables
    @resource.update(status: :in_progress)

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), notice: 'Recompute successful!' }
      format.json { head :no_content }
    end
  rescue StandardError
    respond_to do |format|
      format.html { render template: edit_template_path }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  def set_test_mode
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} set ti to test_mode #{params[:test_mode]}", :danger)

    @resource.set_test_mode(params[:test_mode])

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), notice: 'Test Instance has been updated' }
      format.json { head :no_content }
    end
  end

  # POST /admin/resources/1/send_sms_reminder
  def send_sms_reminder
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} sent an sms reminder", :info)

    message = I18n.t('reminders.sms', client_name: @resource.client_contact_full_name, client_company: @resource.client_name, ti_language: I18n.t("languages.#{@resource.test_language}"))
    if SendSmsUsersJob.perform_now(@resource.id, message)
      flash[:notice] = "SMS reminder sent to candidate at #{@resource.user.email}"
    else
      flash[:error] = 'SMS reminder could NOT be SENT, check event logs.'
    end
    redirect_back(fallback_location: root_path)
  end

  # POST /admin/resources/1/send_email_reminder
  # POST /admin/resources/1/send_email_reminder.json
  def send_email_reminder
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} sent an email reminder", :info)

    flash = if @resource.force_send_reminder!
              { notice: "Email reminder sent to candidate at #{ti.user.email}" }
            else
              { alert: 'Email reminder could NOT be SENT.' }
            end

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), flash }
      format.json { head :no_content }
    end
  rescue StandardError
    m = 'Email reminder could NOT be SENT'
    respond_to do |format|
      format.html { render template: edit_template_path, alert: m }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  # POST /admin/resources/1/send_instructions_email
  # POST /admin/resources/1/send_instructions_email.json
  def send_instructions_email
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} sent an instructions email", :info)

    flash = if @resource.send_email_notification_to_candidate!
              { notice: "Email instructions sent to candidate at #{ti.user.email}" }
            else
              { alert: "Email instructions could NOT be sent to candidate" }
            end

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), flash }
      format.json { head :no_content }
    end
  rescue StandardError
    respond_to do |format|
      format.html { render template: edit_template_path }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  # PUT /admin/resources/1/grade
  # PUT /admin/resources/1/grade.json
  def grade
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} graded the ti", :danger)

    @resource.complete! unless @resource.finished?

    respond_to do |format|
      if @resource.may_grade? && @resource.grade!
        format.html { redirect_to send(resource_url, @resource), notice: 'Test is now graded! Callbacks have been triggered.' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/complete
  # PUT /admin/resources/1/complete.json
  def complete
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} changed the ti to complete", :danger)

    respond_to do |format|
      if @resource.complete!
        format.html { redirect_to send(resource_url, @resource), notice: 'Test is now completed! Callbacks have been triggered.' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  # PUT /admin/resources/1/send_for_evaluation
  # PUT /admin/resources/1/send_for_evaluation.json
  def send_for_evaluation
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} send the ti for evaluation", :danger)

    respond_to do |format|
      if @resource.send_for_evaluation!
        format.html { redirect_to send(resource_url, @resource), notice: 'Test has been sent for evaluation! Callbacks have been triggered.' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  def set_review_required
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} set the ti to review_required", :danger)

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), notice: 'Test Instance has been updated' }
      format.json { head :no_content }
    end
  end

  # PUT /admin/resources/1/pass_validations
  # PUT /admin/resources/1/pass_validations.json
  def pass_validations
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} passed the ti validations", :danger)

    @resource.complete! unless @resource.finished?

    begin
      @resource.pass_validations!
    rescue AASM::InvalidTransition
      respond_to do |format|
        flash[:error] = "Test Instance cannot be validated, 'status'='#{@resource.status}'"
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
      return
    end

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), notice: 'Validations have been forced to PASSED! Callbacks have been triggered.' }
      format.json { head :no_content }
    end
  end

  # GET equivalent of the one above so it is accessible from email
  def force_pass_validations
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} passed the ti validations", :danger)

    @resource.complete! unless @resource.finished?

    respond_to do |format|
      if @resource.pass_validations!
        format.html { redirect_to send(resource_url, @resource), notice: 'Validations have been forced to PASSED! Callbacks have been triggered.' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  def set_pending
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} set the it pending", :danger)

    respond_to do |format|
      if @resource.pending!
        format.html { redirect_to send(resource_url, @resource), notice: 'Pending status have been set ! Callbacks have been triggered.' }
        format.json { head :no_content }
      else
        format.html { render template: edit_template_path }
        format.json { render json: @resource.errors, status: :unprocessable_entity }
      end
    end
  end

  def admin_take_next_question
    nq = @resource.next_questionable
    nq.assign_user_and_test_instance(@resource.user, @resource)
    nq.do_being_answered!
    nq.admin_answered!

    respond_to do |format|
      format.html { redirect_back fallback_location: admin_root_path }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  def create_technical_issue
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} created at tekis", :warning)

    CreateTechnicalIssueJob.perform_later(test_instance_id: @resource.id, status: 'Admin report', from: 'admin', admin_email: current_user.email)
    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource) }
      format.json { render json: @resource.errors, status: :unprocessable_entity }
    end
  end

  # PUT /admin/resources/1/sync_to_gdocs
  # PUT /admin/resources/1/sync_to_gdocs.json
  # def sync_to_gdocs
  #   @resource.manual_gdrive_export!

  #   respond_to do |format|
  #     format.html { redirect_to send(resource_url, @resource), notice: 'Tried to synchronised to Gdocs. Check last sync date for confirmation.' }
  #     format.json { head :no_content }
  #   end

  # rescue
  #   respond_to do |format|
  #     format.html { render template: edit_template_path }
  #     format.json { render json: @resource.errors, status: :unprocessable_entity }
  #   end
  # end

  # POST /admin/resources/1/upload_report
  # POST /admin/resources/1/upload_report.json
  def upload_report
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} uploaded a report", :warning)

    @resource.update(report: params[:report])
    redirect_to action: :show
  end

  # PUT /admin/resources/1/full_reset
  # PUT /admin/resources/1/full_reset.json
  def full_reset
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} fully reset the ti", :danger)

    @resource.full_reset

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), notice: 'Full reset performed.' }
      format.json { head :no_content }
    end
  end

  # PUT /admin/resources/1/cancel
  # PUT /admin/resources/1/cancel.json
  def cancel
    EventLog.add(@resource, :admin, "#{current_user&.first_name} #{current_user&.last_name} cancelled the ti", :danger)
    begin
      @resource.cancel!
    rescue AASM::InvalidTransition
      flash[:error] = "Test Instance cannot be cancelled, 'status'='#{@resource.status}'"
      respond_to do |format|
        format.html { render template: edit_template_path }
        format.json { head :unprocessable_entity }
      end
      return
    end

    respond_to do |format|
      format.html { redirect_to send(resource_url, @resource), notice: 'Test Instance cancelled.' }
      format.json { head :no_content }
    end
  end

  # GET /admin/resources/1/audit_trail
  # GET /admin/resources/1/audit_trail.json
  def audit_trail; end

  def show
    super
  end

  def duplicate_comparator
    tis_to_remove = params[:remove_tis]
    @ti = TestInstance.find(params[:id])
    @duplicates = @ti.check_duplicate.where.not(id: tis_to_remove).includes(:test_profile, user: :roles, evaluations: [:examiner, { certificate: :grades }], productions: [challenge: :questions]).reorder(graded_at: :desc)
  end

  def get_check_face_matching_similarity
    @ti = TestInstance.find(params[:id])
    @face_similarity = @ti.check_face_matching_similarity
    ## Return as json the integer value
    render json: @face_similarity
  end

  private

  def test_stats
    if params[:stats].blank?
      @stats = ''
      return
    else
      @stats = params[:stats].to_sym
    end

    @diff = {}

    # Get evaluations that we are doing statistics on. Either all examiners or restrict to trusted examiners
    evaluations = if @stats == :trusted
                    Evaluation.joins(:examiner).select(:id).where(test_instance_id: @resource.id).where('examiners.status = ?', Examiner.statuses[:trusted])
                  else
                    Evaluation.select(:id).where(test_instance_id: @resource.id)
                  end
    current_evaluation_average = Grade.overall.joins(:certificate).where(certificates: { evaluation_id: evaluations }).average(:score)
    @resource.evaluations.each do |evaluation|
      evaluation_average = Grade.overall.joins(:certificate).where(certificates: { evaluation_id: evaluation.id }).average(:score)
      @diff[evaluation.id] = if evaluation_average
                               (evaluation_average - current_evaluation_average).round(2)
                             else
                               'No Scores'
                             end
    end
  end

  def define_form_options
    return unless @resource

    @tags = Tag.left_outer_joins(:tags_test_instances).order(Arel.sql("CASE WHEN tags_test_instances.test_instance_id = #{@resource.id} THEN 1 ELSE 0 END DESC")).uniq
  end
end
