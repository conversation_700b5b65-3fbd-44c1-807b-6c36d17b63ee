module ShardsHelper
  module_function

  # To use for slow & frequent read queries
  def reading_replica_on_production(&)
    if Rails.env.test?
      yield
    else
      ActiveRecord::Base.connected_to(shard: :read_replica_on_production, role: :reading, &)
    end
  end

  # To use inside a reading_replica_on_production block if a write is needed
  def writing_primary_on_production(&)
    if Rails.env.test?
      yield
    else
      ActiveRecord::Base.connected_to(shard: :read_replica_on_production, role: :writing, &)
    end
  end
end
