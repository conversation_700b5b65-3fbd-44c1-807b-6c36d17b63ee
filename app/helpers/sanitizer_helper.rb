module SanitizerHelper
  module SQL
    module_function

    def sanitize(string)
      ActiveRecord::Base.connection.execute(
        <<~SQL.squish
          SELECT #{sanitize_sql_var("'#{string.gsub("'", "''")}'")} AS sanitized_string
        SQL
      )&.first&.[]('sanitized_string') || nil
    end

    def sanitize_sql_var(sql_var)
      <<~SQL.squish
        translate(lower(unaccent(#{sql_var})), E' -.''', '')
      SQL
    end
  end
end
