module IntegrationsHelper
  private

  def integration_name
    self.class.integration_name
  end

  def available_test_languages
    @available_test_languages ||= build_test_languages
  end

  def build_test_languages
    config_languages = Rails.application.config.public_send(integration_name)&.[](:catalog)
    return config_languages.keys unless config_languages.nil? || config_languages.empty?

    AVAILABLE_TEST_LANGUAGES
  end

  def build_tests
    test_profiles = TestProfile.where(id: Rails.application.config.public_send(integration_name)&.[](:test_profile_ids))
    tests = []
    available_test_languages.each do |language|
      tests += test_profiles.map do |test_profile|
        Integrations::Test.new(test_profile_id: test_profile.id, test_profile_name: test_profile.name, language: language.to_s)
      end
    end
    tests
  end

  def available_tests
    @available_tests ||= build_tests
  end

  def accept_order(api_order)
    api_order.accept!
    EventLog.create(loggable_type: 'Alert', category: 'order_accepted', event_date: DateTime.now, content: "[#{integration_name}] Order accepted", criticity: :info)
  end

  def reject_order(api_order, test_instance)
    api_order.reject!(rejected_test_instance: test_instance)
    EventLog.create(loggable_type: 'Alert', category: 'order_rejected', event_date: DateTime.now, content: "[#{integration_name}] Order rejected", criticity: :info)
    Alert.integrations("Order rejected", api_order.errors.full_messages.to_sentence, integration_name: integration_name, handled: true)
  end

  def fulfill_order(api_user, params)
    api_order = ApiOrder.create({ api_user: api_user,
                                  order_information: params.order_information,
                                  status: 0,
                                  source: ApiOrder.sources[integration_name] })
    user, test_instance = TestInstance.create_with_user(TestInstanceRequest.new({
                                                                                  test_profile_id: params.test_profile_id,
                                                                                  test_language: params.language,
                                                                                  test_mode: api_user.force_test_mode
                                                                                }, {
                                                                                  email: params.candidate_data&.[](:email),
                                                                                  first_name: params.candidate_data&.[](:first_name),
                                                                                  last_name: params.candidate_data&.[](:last_name),
                                                                                  locale: api_user.default_locale,
                                                                                  group: api_user.name
                                                                                }, {
                                                                                  contact_email: params.client_data&.[](:email),
                                                                                  contact_first_name: params.client_data&.[](:first_name),
                                                                                  contact_last_name: params.client_data&.[](:last_name),
                                                                                  source_type: :external_api,
                                                                                  source_id: api_order.id,
                                                                                  client_type: SOURCE_CLIENT_TYPES[integration_name],
                                                                                  client_name: api_user.name.gsub("#{integration_name}_", '').capitalize
                                                                                }))

    [api_order, user, test_instance]
  end
end
