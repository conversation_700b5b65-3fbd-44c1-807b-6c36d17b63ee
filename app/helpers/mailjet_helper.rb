module MailjetHelper # rubocop:disable Metrics/ModuleLength
  module_function

  def from_env_email
    Rails.env.production? ? '<EMAIL>' : '<EMAIL>'
  end

  def from_no_reply_env_email
    Rails.env.production? ? '<EMAIL>' : '<EMAIL>'
  end

  def get_sandbox_mode_from_email(email, test_instance = nil)
    # Sandbox mode is disabled for specifics apps
    return false if %w[pipplet-audition pipplet-api].include?(ENV['HEROKU_APP_NAME'])
    # Sandbox mode is enabled if TI is in test mode AND TEST_MODE_EMAILS differs from 'true'
    return true if test_instance.present? && test_instance.test_mode && ENV['TEST_MODE_EMAILS'] != 'true'

    emails = clean_emails(email)
    # Sandbox mode is disabled if emails contains only "@pipplet.com" emails
    emails.count { |email_entry| email_entry.match(/@pipplet\.com/) } != emails.flatten.size
  end

  def clean_emails(data)
    data = case data
           when String
             [data]
           when Array
             if data.all?(Hash)
               data.map(&:values)
             else
               data
             end
           when Hash
             data.values
           when Struct, OpenStruct
             data.to_h.values
           else
             []
           end

    data&.flatten&.reject(&:blank?)&.uniq
  end

  def bcc_env_email
    Rails.env.production? ? '<EMAIL>' : '<EMAIL>'
  end

  def send_demo_email(test_instance)
    to_user = test_instance.user
    user_email = to_user.email
    direct_user = test_instance.direct_user

    user_email = direct_user.decode_fake_email(user_email) if direct_user&.fake_emails

    mailjet_config = Rails.application.config.mailjet
    user_language = to_user.language_name == 'fr' ? 'fr' : 'en'
    template_id = if test_instance.talent_ai?
                    mailjet_config.dig(:demo_template_ids, :talent_ai, user_language)
                  else
                    mailjet_config.dig(:demo_template_ids, :default, user_language)
                  end

    demo_certificate_file_name = mailjet_config.dig(:demo_certificate_file_names_by_service, test_instance.api_order&.service_name) || mailjet_config.dig(:demo_certificate_file_names_by_assessment_type, test_instance.assessment_type)
    file_name = I18n.t("demo_certificate.#{demo_certificate_file_name}")

    email = { messages: [{
      'From' => {
        'Email' => from_env_email,
        'Name' => 'Pipplet'
      },
      'To' => [
        {
          'Email' => user_email,
          'Name' => to_user.full_name
        }
      ],
      Bcc: [{
        Email: bcc_env_email
      }],
      'TemplateID' => template_id,
      'TemplateLanguage' => true,
      Variables: {
        firstname: to_user.first_name
      },
      'Attachments' => [{
        'ContentType' => 'application/pdf',
        'Filename' => 'certificat.pdf',
        'Base64Content' => Base64.encode64(File.binread("public/documents/#{file_name}.pdf"))
      }],
      TemplateErrorReporting: {
        Email: '<EMAIL>',
        Name: 'MailJet Error Management'
      }
    }], sandbox_mode: get_sandbox_mode_from_email(user_email) }

    MailjetSenderJob.perform_later(email)
  end

  def send_clients_tekis_reminders(test_instance)
    I18n.with_locale(test_instance.user.locale) do
      template_id  = I18n.t('mailjet.codes.send_clients_tekis_reminders')
      client_email = test_instance.client_contact_email
      user         = test_instance.user

      email = { messages: [{
        From: {
          Email: '<EMAIL>',
          Name: 'Pipplet'
        },
        To: [
          {
            Email: client_email,
            Name: "#{test_instance.client_contact_first_name} #{test_instance.client_contact_last_name}"
          }
        ],
        Cc: [{ Email: user.email }],
        Bcc: [{ Email: '<EMAIL>' }],
        TemplateID: template_id.to_i,
        TemplateLanguage: true,
        Variables: {
          firstname: user.first_name,
          lastname: user.last_name,
          test_url: test_instance.test_url
        },
        URLTags: 'utm_source=dashboard&utm_medium=email&utm_campaign=certificateuser',
        TemplateErrorDeliver: true,
        TemplateErrorReporting: {
          Email: '<EMAIL>',
          Name: 'MailJet Error Management'
        }
      }], sandbox_mode: get_sandbox_mode_from_email(client_email, test_instance) }

      MailjetSenderJob.perform_later(email)
    end
  end

  def send_candidate_tekis_reminders(test_instance)
    I18n.with_locale(test_instance.user.locale) do
      template_id  = I18n.t('mailjet.codes.send_candidate_tekis_reminders')
      user         = test_instance.user

      email = { messages: [{
        From: {
          Email: '<EMAIL>',
          Name: 'Pipplet'
        },
        To: [
          {
            Email: user.email,
            Name: "#{user.first_name} #{user.last_name}"
          }
        ],
        Bcc: [{ Email: '<EMAIL>' }],
        TemplateID: template_id.to_i,
        TemplateLanguage: true,
        Variables: {
          firstname: user.first_name,
          lastname: user.last_name,
          test_url: test_instance.test_url
        },
        URLTags: 'utm_source=dashboard&utm_medium=email&utm_campaign=certificateuser',
        TemplateErrorDeliver: true,
        TemplateErrorReporting: {
          Email: '<EMAIL>',
          Name: 'MailJet Error Management'
        }
      }], sandbox_mode: get_sandbox_mode_from_email(user.email, test_instance) }

      MailjetSenderJob.perform_later(email)
    end
  end

  def send_reminder(test_instance, is_reminder = false)
    return if test_instance.user.has_dummy_email?

    user                 = test_instance.user
    test_url             = test_instance.test_url || ''
    recruiter_name       = test_instance.client_contact_full_name
    @template_id         = nil
    @subject             = nil
    user_email           = user.email

    I18n.with_locale(user.locale) do
      test_language = I18n.t("languages.#{test_instance.test_language}")

      begin
        @subject = if is_reminder
                     I18n.t('confirmation_mailer.send_invitation.audition.subject_reminder')
                   elsif test_instance.api_order.order_information.dig('params', 'test_instance', 'custom_invitation_object')
                     test_instance.api_order.order_information.dig('params', 'test_instance', 'custom_invitation_object')
                   else
                     I18n.t('confirmation_mailer.send_invitation.audition.subject')
                   end
      rescue StandardError
        @subject = I18n.t('confirmation_mailer.send_invitation.audition.subject')
      end

      begin
        @custom_message = test_instance.api_order.order_information.dig('params', 'custom_params', 'custom_message')
        if @custom_message.blank? && (cc = test_instance.client_config).present?
          @custom_message = cc.custom_message
        end
      rescue StandardError
        @custom_message = nil
      end

      @template_id = MailjetHelper.invitation_custom_template(test_instance)

      @cc = []
      @bcc = [{ Email: bcc_env_email }]

      begin
        if Rails.env.production? && test_instance.api_order.order_information.dig('params', 'pipplet_clients_client', 'is_cc') == 'true'
          @cc = [{ Email: test_instance.client_contact_email }]
        elsif !Rails.env.production?
          @cc = [{ Email: '<EMAIL>' }]
        end
      rescue StandardError
        @cc = []
      end

      email = { messages: [{
        'From' => {
          'Email' => from_no_reply_env_email,
          'Name' => 'Pipplet'
        },
        'To' => [
          {
            'Email' => user_email,
            'Name' => user.full_name
          }
        ],
        'Subject' => @subject,
        Cc: @cc,
        Bcc: @bcc,
        'TemplateID' => @template_id.to_i,
        'TemplateLanguage' => true,
        Variables: {
          firstname: user.first_name,
          client_name: recruiter_name,
          test_language: test_language,
          identity_check: !test_instance.skip_identity_check,
          client_company: test_instance.client_name,
          registration_url: test_url,
          stop_reminder_url: test_instance.unsusbscribe_url,
          send_preview: 'false',
          is_reminder: is_reminder.to_s,
          show_client_name: test_instance.source_type == 'recruitment',
          has_strong_identity: test_instance.need_strong_id_check?,
          custom_message: @custom_message.to_s,
          non_latin_characters_warning: %w[zhyue].include?(test_instance.test_language) ? I18n.t("mailjet.languages.#{test_instance.test_language}.non_latin_characters_warning") : '',
          writting_only: test_instance.test_profile.test_type == 'writting',
          practice_pack_mention: test_instance.practice_pack_mention?
        },
        TemplateErrorReporting: {
          Email: '<EMAIL>',
          Name: 'MailJet Error Management'
        }
      }], sandbox_mode: get_sandbox_mode_from_email(user_email, test_instance) }

      email[:messages].first.except!('Subject') if is_reminder == false
      MailjetSenderJob.perform_later(email, test_instance)
    end
  end

  def send_certificate(evaluation, recipients, ccs = nil)
    # If fake emails (practice test) in production, don't deliver email
    return if Rails.env.production? && evaluation.test_instance.user.has_dummy_email?

    test_instance = evaluation.test_instance
    user = test_instance.user
    locale = evaluation.certificate.certif_language

    I18n.with_locale(locale) do
      brand_name = brand_name_for_emails(test_instance)

      template_id = if test_instance.regrade_requested_at.present?
                      I18n.t('mailjet.client.regrade_unconfirmed')
                    elsif !brand_name.blank? && ApplicationController.helpers.i18n_set?("mailjet.certificate_email.brand.#{brand_name}")
                      I18n.t("mailjet.certificate_email.brand.#{brand_name}")
                    else
                      I18n.t('certificate_email.default')
                    end

      test_language = I18n.translate("languages.#{test_instance.test_language}")
      pdf = URI.parse(evaluation.pdf_certificate.url).read

      bcc = []

      bcc << if Rails.env.production?
               { Email: '<EMAIL>' }
             else
               { Email: '<EMAIL>' }
             end

      has_attached_certificate = test_instance.client_config&.has_attached_certificate || true

      variables = {
        firstname: user.first_name,
        test_language: test_language,
        candidate_fullname: user.full_name,
        client_firstname: test_instance.client_contact_first_name.to_s,
        client_lastname: test_instance.client_contact_last_name.to_s,
        client_account: test_instance.client_name.to_s,
        has_attached_certificate: has_attached_certificate.to_s,
        writting_only: test_instance.test_profile.test_type == 'writting',
        talent_ai: test_instance.talent_ai?,
        has_security_violation: evaluation.would_deliver_with_security_violation?,
        has_unusual_behaviour: evaluation.has_unusual_behaviour_tags? && !evaluation.would_deliver_with_security_violation?,
        previous_grade: evaluation.previous_delivered&.overall_cecrl_score.to_s,
        new_grade: evaluation.overall_cecrl_score.to_s
      }

      evaluation.security_violation_tags.each do |tag|
        variables[tag.name.underscore.to_sym] = true
      end

      evaluation.unusual_behaviour_tags.each do |tag|
        variables[tag.name.underscore.to_sym] = true
      end

      email = { messages: [{
        From: {
          Email: '<EMAIL>',
          Name: 'Pipplet'
        },
        To: recipients,
        TemplateID: template_id.to_i,
        TemplateLanguage: true,
        Variables: variables,
        Bcc: bcc,
        Cc: Rails.env.production? ? ccs : [],
        TemplateErrorReporting: {
          Email: '<EMAIL>',
          Name: 'MailJet Error Management'
        }
      }], sandbox_mode: get_sandbox_mode_from_email(recipients, test_instance) }

      if has_attached_certificate
        filename = I18n.t('certificate.filename',
                          full_name: "#{user.first_name} #{user.last_name}",
                          lang: I18n.translate("languages.#{test_instance.test_language}"),
                          date: Time.now.strftime('%Y.%m.%d'))
        email[:messages].first['Attachments'] = [{
          'ContentType' => 'application/pdf',
          'Filename' => "#{filename}.pdf",
          'Base64Content' => Base64.encode64(pdf)
        }]
      end

      MailjetSenderJob.perform_later(email)
    end
  end

  def send_certificate_user(test_instance)
    user = test_instance.user
    return false if user.nil?

    ti_eval_delivered = test_instance.evaluations.delivered.last

    return false unless ti_eval_delivered

    I18n.with_locale(user.locale) do
      template_id = I18n.t('mailjet.codes.send_certificate_user_V2')
      to_email = user.email
      certificate_content = URI.parse(ti_eval_delivered.pdf_certificate.url).read
      cefr_level = ti_eval_delivered.certificate.grades.overall.last.cecrl_score

      begin
        ccs = [{ Email: test_instance.client_contact_email }] if Rails.env.production? && test_instance.api_order.order_information.dig('params', 'pipplet_clients_client', 'is_cc') == 'true'
      rescue StandardError
        ccs = []
      end
      issue_date = test_instance.end_date || Date.current
      linkedin_link_params = {
        'startTask' => 'PIPPLET',
        'name' => "Pipplet Certification \"#{I18n.t("languages.#{test_instance.test_language}")} #{cefr_level}\"",
        'organizationName' => 'Pipplet',
        'issueYear' => issue_date.year,
        'issueMonth' => issue_date.month,
        'expirationYear' => (issue_date + 2.years).year,
        'expirationMonth' => issue_date.month,
        'certUrl' => test_instance.certificate_url,
        'certId' => test_instance.uuid
      }
      linkedin_link = "https://www.linkedin.com/profile/add?#{linkedin_link_params.to_query}"

      email = { messages: [{
        From: {
          Email: '<EMAIL>',
          Name: 'Pipplet'
        },
        To: [
          {
            Email: to_email,
            Name: user.full_name
          }
        ],
        Cc: ccs,
        Bcc: [{
          Email: bcc_env_email
        }],
        TemplateID: template_id.to_i,
        TemplateLanguage: true,
        Variables: {
          firstname: user.first_name,
          test_language: I18n.t("languages.#{test_instance.test_language}"),
          company: test_instance.client_name,
          issuing_date: test_instance.end_date.present? ? I18n.l(test_instance.end_date.to_date, format: :long) : nil,
          cefr_level: cefr_level,
          expiration_date: test_instance.end_date.present? ? I18n.l((test_instance.end_date + 2.years).to_date, format: :long) : nil,
          linkedin_link: linkedin_link,
          writting_only: test_instance.test_profile.test_type == 'writting'
        },
        Attachments: [
          {
            ContentType: 'application/pdf',
            Filename: 'Pipplet certificate.pdf',
            Base64Content: Base64.encode64(certificate_content)
          }
        ],
        URLTags: 'utm_source=dashboard&utm_medium=email&utm_campaign=certificateuser',
        TemplateErrorDeliver: true,
        TemplateErrorReporting: {
          Email: '<EMAIL>',
          Name: 'MailJet Error Management'
        }
      }], sandbox_mode: get_sandbox_mode_from_email(to_email, test_instance) }

      MailjetSenderJob.perform_later(email)
    end
  end

  def send_test_completed(test_instance)
    return if test_instance.user.has_dummy_email?

    user = test_instance.user

    brand_name = brand_name_for_emails(test_instance)

    template_id = if !brand_name.blank? && ApplicationController.helpers.i18n_set?("mailjet.test_completed.brand.#{brand_name}_V2")
                    I18n.t("mailjet.test_completed.brand.#{brand_name}_V2", locale: user.locale)
                  else
                    I18n.t('mailjet.codes.test_completed_V2', locale: user.locale)
                  end

    user_email = user.email
    language = I18n.t("languages.#{test_instance.test_language}", locale: user.locale)

    email = { messages: [{
      From: {
        Email: from_no_reply_env_email,
        Name: 'Pipplet'
      },
      'To' => [
        {
          Email: user_email,
          Name: user.full_name
        }
      ],
      TemplateID: template_id.to_i,
      'TemplateLanguage' => true,
      Variables: {
        firstname: user.first_name,
        test_language: language,
        company: test_instance.client_name,
        certificate_sent_to_candidate: test_instance.certificate_sent_to_candidate.present? ? test_instance.certificate_sent_to_candidate.to_s : false.to_s,
        redirection_url: test_instance.redirection_url.present? ? test_instance.redirection_url : false.to_s
      },
      TemplateErrorReporting: {
        Email: '<EMAIL>',
        Name: 'MailJet Error Management'
      }
    }], sandbox_mode: get_sandbox_mode_from_email(user_email, test_instance) }

    MailjetSenderJob.perform_later(email)
  end

  def send_welcome_b2c_email(to_test_instance)
    to_user = to_test_instance.user
    template_id = to_user.language_name == 'fr' ? 477_371 : 477_373
    user_email = to_user.email

    email = { messages: [{
      'From' => {
        'Email' => from_env_email,
        'Name' => 'Pipplet'
      },
      'To' => [
        {
          'Email' => user_email,
          'Name' => to_user.full_name
        }
      ],
      'TemplateID' => template_id,
      'TemplateLanguage' => true,
      Variables: {
        firstname: to_user.first_name,
        language: I18n.t("languages.#{to_test_instance.test_language}", locale: to_user.locale),
        link_test: to_test_instance.test_url
      },
      TemplateErrorReporting: {
        Email: '<EMAIL>',
        Name: 'MailJet Error Management'
      }
    }], sandbox_mode: get_sandbox_mode_from_email(user_email, to_test_instance) }
    MailjetSenderJob.perform_later(email)
  end

  def self.invitation_custom_template(test_instance)
    if test_instance.api_user&.force_api_user_brand && I18n.exists?("mailjet.invitation.brand.#{test_instance.api_user.name.parameterize}_V2")
      I18n.t("mailjet.invitation.brand.#{test_instance.api_user.name&.parameterize}_V2")
    elsif I18n.exists?("mailjet.invitation.brand.#{test_instance.client_name&.parameterize}_V2")
      I18n.t("mailjet.invitation.brand.#{test_instance.client_name&.parameterize}_V2")
    elsif I18n.exists?("mailjet.invitation.brand.#{test_instance.user.group_id}_V2")
      I18n.t("mailjet.invitation.brand.#{test_instance.user.group_id}_V2")
    else
      I18n.t('mailjet.codes.send_invitation_V2')
    end
  end

  def brand_name_for_emails(test_instance)
    ## Test if we are in the context of an API User that forces the white labelling
    if test_instance.api_user&.force_api_user_brand && Brand.exists?(name: test_instance.api_user.name)
      test_instance.api_user.name.parameterize
    else
      test_instance.client_name&.parameterize
    end
  end

  def send_test_canceled(test_instance)
    return if test_instance.user.has_dummy_email?

    user = test_instance.user
    template_id = I18n.t('mailjet.codes.test_canceled', locale: user.locale)

    user_email = user.email
    language = I18n.t("languages.#{test_instance.test_language}", locale: user.locale)

    email = { messages: [{
      From: {
        Email: from_no_reply_env_email,
        Name: 'Pipplet'
      },
      'To' => [
        {
          Email: user_email,
          Name: user.full_name
        }
      ],
      TemplateID: template_id.to_i,
      'TemplateLanguage' => true,
      Variables: {
        firstname: user.first_name,
        language: language,
        company_name: test_instance.client_name.to_s
      },
      TemplateErrorReporting: {
        Email: '<EMAIL>',
        Name: 'MailJet Error Management'
      }
    }], sandbox_mode: get_sandbox_mode_from_email(user_email, test_instance) }

    MailjetSenderJob.perform_later(email)
  end

  def send_redos_email(test_instance)
    to_user = test_instance.user
    template_id = to_user.language_name == 'fr' ? Rails.application.config.mailjet.dig(:redo_template_ids, 'fr') : Rails.application.config.mailjet.dig(:redo_template_ids, 'en')
    user_email = to_user.email

    email = { messages: [{
      From: {
        Email: from_env_email,
        Name: 'Pipplet'
      },
      To: [
        {
          Email: user_email,
          Name: to_user.full_name
        }
      ],
      Bcc: [{
        Email: bcc_env_email
      }],
      TemplateID: template_id,
      TemplateLanguage: true,
      Variables: {
        firstname: to_user.first_name,
        test_url: test_instance.test_url
      },
      TemplateErrorReporting: {
        Email: '<EMAIL>',
        Name: 'MailJet Error Management'
      }
    }], sandbox_mode: get_sandbox_mode_from_email(user_email) }

    MailjetSenderJob.perform_later(email)
  end

  def send_test_reminder_cancel(test_instance:, day_reminder:)
    user = test_instance.user
    return if user.has_dummy_email?

    template_id = I18n.t('mailjet.codes.test_reminder_cancel', locale: user.locale)
    bcc = []
    bcc << { Email: '<EMAIL>' } if day_reminder == 30
    email = { messages: [{
      From: {
        Email: from_no_reply_env_email,
        Name: 'Pipplet'
      },
      To: [
        {
          Email: user.email,
          Name: user.full_name
        }
      ],
      Bcc: bcc,
      TemplateID: template_id.to_i,
      'TemplateLanguage' => true,
      Variables: {
        firstname: user.first_name,
        language: I18n.t("languages.#{test_instance.test_language}", locale: user.locale),
        show_client_name: test_instance.source_type == 'recruitment',
        client_name: test_instance.client_contact_full_name,
        company_name: test_instance.client_name,
        day_reminder: day_reminder
      },
      TemplateErrorReporting: {
        Email: '<EMAIL>',
        Name: 'MailJet Error Management'
      }
    }], sandbox_mode: get_sandbox_mode_from_email(user.email, test_instance) }

    MailjetSenderJob.perform_later(email)
  end

  def send_regrade_confirmation(evaluation, recipients, ccs = nil)
    # If fake emails (practice test) in production, don't deliver email
    return if Rails.env.production? && evaluation.test_instance.user.has_dummy_email?

    test_instance = evaluation.test_instance
    user = test_instance.user
    locale = evaluation.certificate.certif_language

    I18n.with_locale(locale) do
      template_id = I18n.t('mailjet.client.regrade_confirmed')
      test_language = I18n.translate("languages.#{test_instance.test_language}")
      bcc = []
      bcc << if Rails.env.production?
               { Email: '<EMAIL>' }
             else
               { Email: '<EMAIL>' }
             end

      email = { messages: [{
        From: {
          Email: '<EMAIL>',
          Name: 'Pipplet'
        },
        To: recipients,
        TemplateID: template_id.to_i,
        TemplateLanguage: true,
        Variables: {
          firstname: user.first_name,
          test_language: test_language,
          candidate_fullname: user.full_name,
          client_firstname: test_instance.client_contact_first_name.to_s,
          client_lastname: test_instance.client_contact_last_name.to_s,
          client_account: test_instance.client_name.to_s,
          writting_only: test_instance.test_profile.test_type == 'writting',
          talent_ai: test_instance.talent_ai?,
          has_security_violation: evaluation.would_deliver_with_security_violation?,
          has_unusual_behaviour: evaluation.has_unusual_behaviour_tags? && !evaluation.would_deliver_with_security_violation?
        },
        Bcc: bcc,
        Cc: Rails.env.production? ? ccs : [],
        TemplateErrorReporting: {
          Email: '<EMAIL>',
          Name: 'MailJet Error Management'
        }
      }], sandbox_mode: get_sandbox_mode_from_email(recipients, test_instance) }

      MailjetSenderJob.perform_now(email)
    end
  end
end
