module AdminHelper
  def production_data(p)
    raise ArgumentError unless p.is_a?(Production)

    if p.has_text_production?
      simple_format p.text_production
    elsif !p.recording.nil?
      "recording: <audio controls data-placement='right' data-toggle='tooltip' title=\"#{p.production_metadatum&.production_transcription || 'No transcription'}\"><source src=\"#{p.recording.url}\" type='audio/ogg'>Your browser does not support the audio element.</audio>".html_safe
    elsif !p.audio_production.nil? && !p.audio_production.empty? && p.audio_production.to_s[0..3] == 'http'
      "raw audio: <audio controls><source src='#{p.audio_production}' type='audio/ogg'>Your browser does not support the audio element.</audio>".html_safe
    else
      'Nothing was recorded'
    end
  end

  def examiner_production_data(p)
    raise ArgumentError unless p.is_a?(Production)

    if p.has_text_production?
      simple_format p.text_production
    elsif !p.recording.nil?
      "<audio style='width: 100%' controls><source src='#{p.recording.url}' type='audio/ogg'>Your browser does not support the audio element.</audio>".html_safe
    elsif !p.audio_production.nil? && !p.audio_production.empty? && p.audio_production.to_s[0..3] == 'http'
      "raw audio: <audio controls><source src='#{p.audio_production}' type='audio/ogg'>Your browser does not support the audio element.</audio>".html_safe
    else
      'Nothing was recorded'
    end
  end

  def form_select_search_field(f, key, collection, key_search, key_display, id: '', label: key, style: '', selected: nil)
    options = {}
    options[:selected] = selected if selected.present?
    %(<div class="form-group">
        #{f.label key, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">
          #{f.collection_select(key, collection, key_search, key_display, options,
                                { class: 'select_search form-control', id: id, style: style })}
        </div>
    </div>).html_safe
  end

  def form_section(s)
    "<h4 class='col-sm-12'>#{s}</h4>".html_safe
  end

  def text_only(key, value)
    %(<div class="form-group">
        <label class="col-sm-4 control-label">#{key}</label>
        <div class="col-sm-7" style="padding-top: 9px">#{value}</div>
    </div>).html_safe
  end

  def link_text(key, value, link, given_class = '')
    %(<div class="form-group">
        <label class="col-sm-4 control-label">#{key}</label>
        <div class="col-sm-7" style="padding-top: 9px"><a href=#{link} class=\"#{given_class}\" >#{value}</a></div>
    </div>).html_safe
  end

  def form_color_field(f, e, label = e, required: false)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-4">#{f.color_field e, class: 'form-control-color', required:}</div>
    </div>).html_safe
  end

  def form_text_field(f, e, label = e, required: false, placeholder: nil, disable: false)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.text_field e, class: 'form-control', required:, placeholder:, disabled: disable}</div>
    </div>).html_safe
  end

  def form_number_field(f, e, label = e, required: false, step: 1)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.number_field e, class: 'form-control', step:, required:}</div>
    </div>).html_safe
  end

  def form_email_field(f, e, label = e, required: false, placeholder: nil)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.email_field e, class: 'form-control', required:, placeholder:}</div>
    </div>).html_safe
  end

  def form_text_field_with_unit(f, e, unit, label: e, col_size: 4, added_class: '')
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">
          <div class="row">
            <div class="col-xs-#{col_size}">
              #{f.text_field e, class: "form-control #{added_class}"}
            </div>
            <div class="col-xs-3">
              #{unit}
            </div>
          </div>
        </div>
    </div>).html_safe
  end

  def form_date_field(f, e, label = e, class_label = 'col-sm-4 control-label', class_field = 'form-control', required: false)
    %(<div class="form-group">
        #{f.label e, label, class: class_label}
        <div class="col-sm-7">#{f.datetime_local_field e, class: class_field, required:}</div>
    </div>).html_safe
  end

  def form_json(f, e, style: '')
    %(<div class="form-group">
        #{f.label e, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.text_area e, value: JSON.pretty_generate(f.object[e]), class: 'form-control',
                                               style: style, as: :json}</div>
    </div>).html_safe
  end

  def form_textarea(f, e, style: '', value: nil, readonly: false)
    %(<div class="form-group">
        #{f.label e, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">
          #{f.text_area e, class: 'form-control', value: value, readonly: readonly, style: style}
        </div>
    </div>).html_safe
  end

  def form_check_box(f, e, label = e, disable: false)
    %{<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">
          <div class="col-md-1" style=" -ms-transform: scale(2); /* IE */ -moz-transform: scale(2); /* FF */ -webkit-transform: scale(2); /* Safari and Chrome */ -o-transform: scale(2); /* Opera */ padding: 10px;">
            #{f.check_box e, { disabled: disable }}
          </div>
        </div>
    </div>}.html_safe
  end

  def form_collection_check_boxes(f, e, collection, value, text, label = nil, display: 'block')
    check_boxes = f.collection_check_boxes(e, collection, value, text) do |b|
      %(<div  style="display: #{display}">
        #{block_given? ? yield(b) : b.check_box(style: 'margin-right: 1px') + b.label(style: 'font-size:.8em;line-height:0')}
      </div>).html_safe
    end
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label' if label}
        <div class="col-sm-7">
            #{check_boxes}
        </div>
    </div>).html_safe
  end

  def form_select_field(f, e, collection, value_method, text_method, label = e, include_blank: false)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.collection_select e, collection, value_method, text_method,
                                                    { class: 'form-control', include_blank: }}</div>
    </div>).html_safe
  end

  def form_select_with_prompt_field(f, e, collection, value_method, text_method, label = e, prompt: true, include_blank: false)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.collection_select e, collection, value_method, text_method,
                                                    { class: 'form-control', prompt:, include_blank: }}</div>
    </div>).html_safe
  end

  def form_select_multiple_field(f, e, collection, value_method, text_method, label = e, disabled: false)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.collection_select e, collection, value_method, text_method,
                                                    { class: 'form-control' }, multiple: true, disabled:}</div>
    </div>).html_safe
  end

  def form_text_readonly(f, e, label = e)
    %(<div class="form-group">
        #{f.label e, label, class: 'col-sm-4 control-label'}
        <div class="col-sm-7">#{f.text_field e, class: 'form-control', readonly: :true}</div>
    </div>).html_safe
  end

  def parse_date(date, round = nil)
    date = date.round(round) if round
    days = date / (60 * 60 * 24)
    hours = (date / (60 * 60)) % 24
    mins = (date / 60) % 60
    "#{days}d #{hours}hours #{mins}mins"
  end

  def ti_status_verification(test_instance, next_status)
    class_names = []
    class_names << 'pt-status-disabled' unless test_instance.status_can_change?(next_status)
    class_names << 'pt-status-test_mode-disabled' if test_instance.status_disabled_for_test_mode?(next_status)
    return unless class_names.any?

    " class=\"#{class_names.join(' ')}\"".html_safe
  end

  def tags_labels(tag)
    labels = []
    labels << %(<div style="margin: 0 3px;font-size:.8rem" class="label label-default">Blocks Delivery</div>) if tag.blocks_evaluation_delivery?
    labels << %(<div style="margin: 0 3px;font-size:.8rem" class="label label-warning">Unusual Behaviour</div>) if tag.unusual_behaviour?
    labels << %(<div style="margin: 0 3px;font-size:.8rem" class="label label-danger">Security Violation</div>) if tag.security_violation?
    labels << %(<div style="margin: 0 3px;font-size:.8rem" class="label label-warning">Warning</div>) if tag.warning?
    labels.join.html_safe
  end
end
