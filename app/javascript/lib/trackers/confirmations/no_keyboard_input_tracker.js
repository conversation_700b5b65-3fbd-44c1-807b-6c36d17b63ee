import { Tracker } from 'lib/tracker'

export class NoKeyboardInputTracker extends Tracker {
  static mode = Tracker.Modes.Confirmations

  constructor (config, target) {
    super()

    const interval = config.transformations.refresh_interval
    this.tracked = true

    setInterval(() => { this.tracked = true }, interval)
    target.addEventListener('keydown', () => { this.tracked = false })
  }

  report () {
    const tracked = this.tracked
    this.tracked = true
    return tracked
  }
}
