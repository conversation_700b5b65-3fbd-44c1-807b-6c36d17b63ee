import { Tracker } from 'lib/tracker'

export class CpmTracker extends Tracker {
  static mode = Tracker.Modes.Statistics

  constructor (config, target) {
    super()

    this.firstCharacterTimeStamp = null
    this.lastCharacterTimeStamp = null
    this.target = target

    target.addEventListener('input', (event) => {
      if (!this.firstCharacterTimeStamp) {
        this.firstCharacterTimeStamp = event.timeStamp
      }
      this.lastCharacterTimeStamp = event.timeStamp
    })
  }

  report () {
    return { cpm: (this.target.value.length / (this.lastCharacterTimeStamp - this.firstCharacterTimeStamp) * 6000.0).toFixed(3) }
  }
}
