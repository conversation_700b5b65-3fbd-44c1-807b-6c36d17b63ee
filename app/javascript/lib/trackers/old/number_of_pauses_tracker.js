import { Tracker } from 'lib/tracker'

export class NumberOfPausesTracker extends Tracker {
  static mode = Tracker.Modes.Statistics

  constructor (config, target) {
    super()

    let currentDelay = null
    let previousDelay = null
    const breakDelay = config?.break_delay

    if (!breakDelay) return

    this.breaks_count = 0

    target.addEventListener('input', (event) => {
      currentDelay = event.timeStamp
      if (previousDelay) {
        const delay = (currentDelay - previousDelay) / 1000.0
        if (delay > breakDelay) this.breaks_count += 1
      }
      previousDelay = currentDelay
    })
  }

  report () {
    return { number_of_pauses: (this.breaks_count || 'unknown') }
  }

  // private

  average (arr) {
    return arr.reduce((a, b) => a + b, 0) / arr.length
  }
}
