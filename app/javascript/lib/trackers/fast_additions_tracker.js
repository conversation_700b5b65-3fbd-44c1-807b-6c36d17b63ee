import { Tracker } from 'lib/tracker'
import { PasteTracker } from 'lib/trackers/confirmations/paste_tracker'
import { PasteKeystrokeTracker } from 'lib/trackers/confirmations/paste_keystroke_tracker'
import { DragTracker } from 'lib/trackers/confirmations/drag_tracker'

export class FastAdditionsTracker extends Tracker {
  static mode = Tracker.Modes.Behaviour

  constructor (config, target) {
    super()

    this.confirmationTrackers = {
      paste: new PasteTracker(config, target),
      keystroke: new PasteKeystrokeTracker(config, target),
      drag: new DragTracker(config, target)
    }

    let previousContent = ''
    const threshold = config.fast_additions.characters_threshold
    const interval = config.fast_additions.refresh_interval
    if (!threshold || !interval) return

    const _self = this
    setInterval(function () {
      const content = target.value

      if (content.length - previousContent.length >= threshold) {
        const difference = _self.differentCharacters(previousContent, content)
        if (difference.result !== '') {
          const displayedContent = content.substring(difference.startIndex - 30, difference.endIndex + 30)
          _self.tracked.push({
            event_date: new Date(),
            metadata: {
              content: displayedContent,
              content_indexes: {
                start: difference.startIndex,
                end: difference.endIndex
              },
              value: difference.result,
              confirmations: _self.confirmationsReport()
            },
            category: 'fast_addition'
          })
        }
      }
      previousContent = content
    }, interval)
  }

  // private

  differentCharacters (string1, string2) {
    let i1 = 0
    let i2 = 0
    let startIndex = null
    let result = ''

    while (i2 < string2.length) {
      if (string1[i1] !== string2[i2] || i1 === string1.length) {
        if (startIndex === null) startIndex = i2
        result += string2[i2]
      } else { i1++ }
      i2++
    }

    return {
      result,
      startIndex,
      endIndex: startIndex + result.length - 1
    }
  }
}
