import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['modal']
  static disabledKey = 'focus-changed-disabled'

  connect () {
    window.addEventListener('register-detections-controllers', () => {
      this.showModalDebounced = debounce(() => { this.show() }, 2000) // eslint-disable-line
      this.detectFocusChanged()
    })
  }

  detectFocusChanged () {
    document.addEventListener('visibilitychange', () => { this.show() })
    document.addEventListener('mouseleave', () => { this.showModalDebounced() })
    document.addEventListener('mouseover', () => { this.showModalDebounced.cancel() })
  }

  hide () {
    window.jQuery(this.modalTarget).modal('hide')
    window.pippletHelper.trackAnalytics('mouse_leave')
  }

  disable () {
    window.localStorage.setItem(this.constructor.disabledKey, 'true')
    this.hide()
  }

  // Private

  show () {
    if (window.localStorage.getItem(this.constructor.disabledKey) === 'true') return
    window.jQuery(this.modalTarget).modal('show')
    window.pippletHelper.trackAnalytics('mouse_enter')
  }
}
