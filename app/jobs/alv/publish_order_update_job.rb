class Alv::PublishOrderUpdateJob < ApplicationJob
  class RetriableError < StandardError; end

  queue_as :default
  retry_on(RetriableError, wait: 2.minutes, attempts: 5) do
    # Don't raise if all retries failed
  end

  def perform(api_order_id, source:)
    api_order = ApiOrder.find(api_order_id)
    Alv::Api.publish_update(api_order)
  rescue SocketError, Timeout::Error, Errno::ECONNREFUSED
    raise RetriableError
  rescue StandardError => e
    Alert.integrations('Error while publishing results', e.message, integration_name: source, meta: { api_order_id: })
  end
end
