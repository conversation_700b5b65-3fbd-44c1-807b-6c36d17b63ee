class MonthlyFnpJob < ActiveJob::Base
  queue_as :default

  def perform
    month_date = (1.month.ago).beginning_of_month
    simple = Evaluation.completed.where("assessed_at > ?", month_date).where(:certification_type => 'simple').count
    detailed = Evaluation.completed.where("assessed_at > ?", month_date).where(:certification_type => ['flex', 'detailed']).count
    month = month_date.strftime('%Y-%m')

    params = {
      "entry.1957878859" => month,
      "entry.957701432" => simple,
      "entry.951757805" => detailed
    }

    url = URI("https://docs.google.com/forms/d/e/1FAIpQLSewDkziGNlxRMMweRTJchCsx2zdelW6_CG8agIko7VrpF2bbw/formResponse")
    res = Net::HTTP.post_form(url, params)

    unless res.is_a?(Net::HTTPOK)
      Alert.system("[MonthlyFnpJob#perform] Couldn't write PCA", res.to_yaml).deliver_later
    end
  end
end
