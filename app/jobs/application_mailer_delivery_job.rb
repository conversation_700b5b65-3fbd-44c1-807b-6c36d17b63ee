class ApplicationMailerDeliveryJob < ActionMailer::MailDeliveryJob
  # modified wait formula from https://edgeapi.rubyonrails.org/classes/ActiveJob/Exceptions/ClassMethods.html#method-i-retry_on
  # example times for 3 tries: 1m, 2.5-3m, 4.5-6.7m
  retry_on(StandardError, attempts: 3, wait: ->(executions) { (Kernel.rand * (executions**3) * 6) + (25 * (executions**2)) + 40 }) do
    # Don't raise if all retries failed
  end
end
