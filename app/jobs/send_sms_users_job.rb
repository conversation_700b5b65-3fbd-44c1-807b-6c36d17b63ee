class SendSmsUsersJob < ApplicationJob
  queue_as :default

  def perform(test_instance_id, message)
    test_instance = TestInstance.find_by(id: test_instance_id)
    return unless test_instance

    sms_service = SmsService::SendSmsMailjetService.new(user_id: test_instance.user_id, message: message)
    sms_service.send_sms do
      test_instance.update(last_sms_reminder_sent_at: DateTime.current)
    end

    return sms_service if sms_service.errors.blank?

    Alert.job(job: self, meta: { test_instance_id: test_instance.id, sms_message: message }, message: nil)
  end
end
