class ArchivedJobs::GetHumanScoreForUsersJob < ApplicationJob
  queue_as :default

  ## This job gets all Human Score objects which have not been rated yet.
  ## It goes through the google speadsheet to check if there is a score for them
  ## If so it sets the score.
  def perform(user = nil)
    scoring_batch = ScoringBatch.create()

    ## Browse all scores which have not been rated yet to check if we have a score
    to_be_scored = {}
    Score.where(:label => 'human').where(:scoring_batch_id => nil).find_each do |s|
      to_be_scored[s.scorable_id] = s
    end

    ## Load the spreadsheet
    session = GoogleDrive::Session.from_config("config/gdrive_config.json")
    worksheet = session.spreadsheet_by_key(SPREADSHEET_IMPORT).worksheet_by_title(SPREADSHEET_IMPORT_WORKSHEET_NAME)

    ## Read the spreadsheet, if we get a user_id that has not a score already, get it
    line_index = 2
    user_id = worksheet[line_index, 1]

    while !user_id.nil? && !user_id.blank? && !to_be_scored.empty?
      user_id_int = user_id.to_i
      worksheet_score = worksheet[line_index, 2]
      worksheet_report_url = worksheet[line_index, 3]
      user = User.find_by_id(user_id_int)

      if !worksheet_score.nil? && !worksheet_score.blank? && to_be_scored.has_key?(user_id_int) && user
        # We have a user and a score in the spreadsheet

        ## Update Score
        to_update_score = to_be_scored[user_id_int]
        to_update_score.rating = worksheet_score.to_f
        to_update_score.scoring_batch_id = scoring_batch.id

        ## Update Report Url
        ### V1 : Using the URL
        ### V2 : Using the Google Drive gem ?
        user.report = open(worksheet_report_url)

        to_update_score.save!

        ## Make Score available
        user.do_score_available!

        ## Say that we do not need to update this score anymore
        to_be_scored.delete(user_id_int)
      end

      line_index += 1
      user_id = worksheet[line_index, 1]
    end
  end
end
