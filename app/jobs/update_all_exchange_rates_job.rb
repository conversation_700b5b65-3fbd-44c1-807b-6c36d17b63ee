class UpdateAllExchangeRatesJob < ApplicationJob
  queue_as :default

  def perform
    download = URI.open('https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml')
    hash = Hash.from_xml(download)

    hash.first.last["Cube"]["Cube"]["Cube"].each do |currency|
      c = Currency.find_by_code(currency["currency"])
      if c.present?
        c.eur_exchange_rate = currency["rate"]
        c.save!
      end
    end

    c = Currency.find_by_code("EUR")
    c.eur_exchange_rate_updated_at = Time.now
    c.save!
  rescue Exception => exception
    Alert.job(job: self, message: exception.message)
  end
end
