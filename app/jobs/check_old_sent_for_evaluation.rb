class CheckOldSentForEvaluation < ApplicationJob
  queue_as :batch

  def perform
    tis = TestInstance.sent_for_evaluation.where(sent_for_evaluation_date: ...23.business_hours.ago)
    if !tis.empty?
      AlertMailer.old_sent_for_evaluation(tis)
    end
  rescue Exception => exception
    Alert.job(job: self, meta: { test_instance_ids: tis&.pluck(:id)&.sort }, message: exception.message)
  end
end
