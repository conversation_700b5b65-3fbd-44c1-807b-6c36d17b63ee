class FlagCompletedTestsJob < ApplicationJob
  queue_as :batch

  def perform
    TestInstance.no_next_questionables.not_test_mode.in_progress.find_each do |test_instance|
      next unless test_instance.last_production_before?(from: 1.hour.ago)

      EventLog.add(test_instance, :process, "FlagCompletedTestsJob - TestInstance #{test_instance.id}. Test instance complete!", :info)
      test_instance.complete!
    end

    # return count
  rescue StandardError => e
    Alert.job(job: self, message: e.message)
  end
end
