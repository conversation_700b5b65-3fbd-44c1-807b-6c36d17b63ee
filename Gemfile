# frozen_string_literal: true

source 'https://rubygems.org'

# ----------------------------------------
# :section: Rails!
# ----------------------------------------
gem 'rails', '~> 8.0.1'

# ----------------------------------------
# :section: Assets
# ----------------------------------------
# Use SCSS for stylesheets. Also used for compressor for CSS assets
gem 'sass-rails'
# Use jquery as the JavaScript library
gem 'bootstrap-sass', '~> 3'

gem 'jquery-rails'
# Use Uglifier as compressor for JavaScript assets
gem 'kt-paperclip' # This requires imagemagick!
gem 'terser'

# ----------------------------------------
# :section: AWS
# ----------------------------------------
# Uploaded assets to amazon S3
gem 'aws-sdk-s3'
# Identity check same person
gem 'aws-sdk-rekognition'
# transcript audio
gem 'aws-sdk-transcribeservice'
# Detect dominant language
gem 'aws-sdk-comprehend'

# ----------------------------------------
# :section: Core
# ----------------------------------------
# Use postgresql as the database for Active Record
gem 'pg'

# Authentication and authorisation
gem 'devise'
gem 'devise-i18n'
# Soft delete objects
gem 'paranoia'
# State machine for active record
gem 'aasm'
# Add roles for user
# Very simple Roles library without any authorization enforcement supporting scope on resource object.
gem 'rolify'
# Static pages made easy: https://github.com/thoughtbot/high_voltage
gem 'high_voltage'
# Timezone
gem 'tzinfo'
gem 'tzinfo-data'
# manage evaluation time
gem 'business_time'
# Official ISO Language list
gem 'language_list'
# Send email when exceptions happen
gem 'exception_notification'
# Fast version of HAML
gem 'hamlit'
# Background jobs managed by sidekiq + redis
gem 'csv'
gem 'sidekiq'

# Choosing the right ruby server
gem 'puma'

# Browser detection, to limit the app to certain browsers only
gem 'browser'
# Internationalisation management in Google Docs, we need version > 0.1.1 (unreleased) for Ruby 3 compatibilty
gem 'i18n-docs', git: 'https://github.com/renuo/i18n-docs', ref: 'd49c465'
# send emails with mailjet
gem 'mailjet'
# B2C
gem 'stripe'
# B2C
gem 'google_drive'

gem "rbtrace", require: String(ENV.fetch("RBTRACE_ENABLED", false)) == "true"

gem 'rubyzip'

# Avoid DOS and IP endpoint spam
gem 'rack-attack'

# Allow asset delivery from cloudfront
gem 'rack-cors'

gem 'stimulus-rails'

# Graphical charts statistics
gem 'chartkick'
gem 'groupdate'

gem 'bulk_insert'
# ----------------------------------------
# :section: API stuff
# ----------------------------------------
# Serialize models in JSON
gem 'active_model_serializers'

# Paginations
gem 'kaminari'
# Rest client to connect to external APIs
gem 'rest-client'

# Country select
gem 'country_select', '~> 8.0'

# JWT decoding
gem 'jwt'

# Phone number conversion to international
gem 'phonelib'

gem 'validates_email_format_of'

gem 'httparty'
gem "pghero"

# ----------------------------------------
# :section: Development: mainly debugging
# ----------------------------------------
group :development do
  # Webconsole when raising error, that can access values
  gem 'better_errors'
  gem 'web-console'

  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  # Add schema information on top of model files
  gem 'annotaterb'
  # i18n tasks with some featuress
  gem 'i18n-tasks'
  # Used to watch file changes (config.file_watcher)
  gem 'listen'
  gem 'wkhtmltopdf-binary'
end

# ----------------------------------------
# :section: Testing
# ----------------------------------------
group :development, :test do
  # Call 'binding.break' or 'debugger' anywhere in the code to stop execution and get a debugger console
  gem 'byebug'
  gem 'capybara-lockstep'
  gem 'debug'
  # Use minitest as testing framework
  gem 'overcommit'
  gem 'minitest-bisect'
  gem 'minitest-hooks'
  gem 'minitest-rails'
  gem 'minitest-reporters'
  gem 'webmock'

  # Display code coverage stats in /coverage folder.
  gem 'simplecov', require: false

  gem 'brakeman'
  gem 'bullet'
  gem 'bundler-audit'
  gem 'capybara'
  gem 'capybara-screenshot'
  gem 'dotenv-rails'
  gem 'fasterer'
  gem 'mocha'
  gem 'pg_query'
  gem 'prosopite'
  gem 'rails-controller-testing'
  gem 'rubocop'
  gem 'rubocop-minitest'
  gem 'rubocop-performance'
  gem 'rubocop-rails'
  gem "rubocop-capybara"
  gem "rubocop-factory_bot"
  gem 'rubycritic', require: false
  gem 'selenium-devtools'
  gem 'selenium-webdriver'
end

# ----------------------------------------
# :section: Production specific gems
# ----------------------------------------
group :production, :preprod do
  gem "barnes"
  gem 'wkhtmltopdf-heroku', '3.0.0'
end

gem 'appsignal'
gem 'w3c_validators'
################ PDF
gem 'safer_rails_console'
gem 'wicked_pdf'

# ----------------------------------------
# :section: Local gems to also be used in Preprod (for Live Test Plans)
# ----------------------------------------
group :development, :test, :preprod do
  gem 'factory_bot_rails'
end

# ----------------------------------------
# :section: Ruby!
# ----------------------------------------
ruby file: '.ruby-version'

gem 'after_commit_everywhere', '~> 1.0'
gem 'importmap-rails'

gem 'faker'
gem "strong_migrations"

# ----------------------------------------
# :section: Gems to be removed from stdlib
# ----------------------------------------
gem 'mutex_m'

gem "ruby-saml", "~> 1.18"
