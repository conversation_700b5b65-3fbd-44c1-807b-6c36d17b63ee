name: "Ruby on Rails CI"
on:
  pull_request
jobs:
  test:
    runs-on: ubuntu-runner-2
    services:
      postgres:
        image: postgres:15-alpine
        ports:
          - "5432:5432"
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
    env:
      RAILS_ENV: test
      AWS_REGION: eu-west-1
      AWS_IDENTITY_BUCKET_NAME: 'bucket-name'
      AWS_IDENTITY_ACCESS_KEY_ID: 'access-key'
      AWS_IDENTITY_SECRET_ACCESS_KEY: 'secret-access'
      SMTP_DOMAIN: ''
      S3_BUCKET_NAME: 'bucket-name'
      AWS_ACCESS_KEY_ID: 'access-key'
      AWS_SECRET_ACCESS_KEY: 'secret-access'
      RAILS_MASTER_KEY: ${{ secrets.RAILS_MASTER_KEY }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      # Add or replace dependency steps here
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          ruby-version: '.ruby-version'
        # Add or replace database setup steps here
      - name: Set up primary database
        env:
          POSTGRES_DB: pipplet_test
          DATABASE_URL: "postgres://postgres:password@localhost:5432/pipplet_test"
        run: bin/rails db:create:primary
      - name: Set up logs database
        env:
          POSTGRES_DB: pipplet_logs_test
          DATABASE_LOGS_URL: "postgres://postgres:password@localhost:5432/pipplet_logs_test"
        run: bin/rails db:create:logs
      - name: Set up database schema
        run: bin/rails db:schema:load
      # Add or replace test runners here
      - name: Run tests
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 30
          max_attempts: 3
          command: RUBYOPT='-W:deprecated --debug-frozen-string-literal' bin/rake

  security:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          ruby-version: '.ruby-version'
      # Add or replace any other lints here
      - name: Security audit dependencies
        run: bin/bundler-audit --update
      - name: Security audit application code
        run: bin/brakeman -q -w3
#      - name: Lint Ruby files
#        run: bin/rubocop --parallel
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          ruby-version: '.ruby-version'
      # Add or replace any other lints here
      - name: Fasterer
        run: bundle exec fasterer
      - name: Rubocop
        run: bundle exec rubocop
