name: <PERSON>
on:
  pull_request:
    types: [opened]
jobs:
  claude-response:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      issues: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Claude <PERSON> Review
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          use_sticky_comment: true
          direct_prompt: |
            Review this pull request for a solo developer.
            Analyze each change individually. Focus only on issues, risks, or areas needing improvement.
            For each problem, include:
            - Severity: (critical, major, minor, or nit)
            - Description: What’s wrong and why
            Do not include praise, summaries, or general observations.
            Be concise but cover every problem — do not skip any to save space.
